(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[7905],{19540:function(Nt,se,N){"use strict";N.r(se),N.d(se,{default:function(){return Ct}});var kt=N(57663),Y=N(71577),E=N(39428),I=N(3182),Dt=N(34792),H=N(48086),Kt=N(71194),J=N(50146),Zt=N(77576),Ee=N(12028),M=N(94657),$=N(3980),Te=N(87588),Pe=N(51042),Oe=N(36450),we=N(93279),Q=N(16894),ie=N(80582),b=N(67294),B=N(93224),D=N(11849),w=N(32059),X=N(69610),q=N(54941),T=N(3372),_=N(43028),ee=N(15818),je=N(94184),j=N.n(je),G=N(45598),de=b.createContext(null),Le=N(45916),y=N(85893),Ie=["className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","expanded","selected","checked","halfChecked"],le="open",ce="close",$e="---",Re=function(f){(0,_.Z)(h,f);var l=(0,ee.Z)(h);function h(){var e;(0,X.Z)(this,h);for(var o=arguments.length,v=new Array(o),i=0;i<o;i++)v[i]=arguments[i];return e=l.call.apply(l,[this].concat(v)),e.state={dragNodeHighlight:!1},e.selectHandle=void 0,e.onSelectorClick=function(t){var n=e.props.context.onNodeClick;n(t,(0,T.Z)(e)),e.isSelectable()?e.onSelect(t):e.onCheck(t)},e.onSelectorDoubleClick=function(t){var n=e.props.context.onNodeDoubleClick;n(t,(0,T.Z)(e))},e.onSelect=function(t){if(!e.isDisabled()){var n=e.props.context.onNodeSelect;t.preventDefault(),n(t,(0,T.Z)(e))}},e.onCheck=function(t){if(!e.isDisabled()){var n=e.props,a=n.disableCheckbox,c=n.checked,r=e.props.context.onNodeCheck;if(!(!e.isCheckable()||a)){t.preventDefault();var d=!c;r(t,(0,T.Z)(e),d)}}},e.onMouseEnter=function(t){var n=e.props.context.onNodeMouseEnter;n(t,(0,T.Z)(e))},e.onMouseLeave=function(t){var n=e.props.context.onNodeMouseLeave;n(t,(0,T.Z)(e))},e.onContextMenu=function(t){var n=e.props.context.onNodeContextMenu;n(t,(0,T.Z)(e))},e.onDragStart=function(t){var n=e.props.context.onNodeDragStart;t.stopPropagation(),e.setState({dragNodeHighlight:!0}),n(t,(0,T.Z)(e));try{t.dataTransfer.setData("text/plain","")}catch(a){}},e.onDragEnter=function(t){var n=e.props.context.onNodeDragEnter;t.preventDefault(),t.stopPropagation(),n(t,(0,T.Z)(e))},e.onDragOver=function(t){var n=e.props.context.onNodeDragOver;t.preventDefault(),t.stopPropagation(),n(t,(0,T.Z)(e))},e.onDragLeave=function(t){var n=e.props.context.onNodeDragLeave;t.stopPropagation(),n(t,(0,T.Z)(e))},e.onDragEnd=function(t){var n=e.props.context.onNodeDragEnd;t.stopPropagation(),e.setState({dragNodeHighlight:!1}),n(t,(0,T.Z)(e))},e.onDrop=function(t){var n=e.props.context.onNodeDrop;t.preventDefault(),t.stopPropagation(),e.setState({dragNodeHighlight:!1}),n(t,(0,T.Z)(e))},e.onExpand=function(t){var n=e.props.context.onNodeExpand;n(t,(0,T.Z)(e))},e.setSelectHandle=function(t){e.selectHandle=t},e.getNodeChildren=function(){var t=e.props.children,n=(0,G.default)(t).filter(function(c){return c}),a=ve(n);return n.length!==a.length&&fe(),a},e.getNodeState=function(){var t=e.props.expanded;return e.isLeaf()?null:t?le:ce},e.isLeaf=function(){var t=e.props,n=t.isLeaf,a=t.loaded,c=e.props.context.loadData,r=e.getNodeChildren().length!==0;return n===!1?!1:n||!c&&!r||c&&a&&!r},e.isDisabled=function(){var t=e.props.disabled,n=e.props.context.disabled;return t===!1?!1:!!(n||t)},e.isCheckable=function(){var t=e.props.checkable,n=e.props.context.checkable;return!n||t===!1?!1:n},e.syncLoadData=function(t){var n=t.expanded,a=t.loading,c=t.loaded,r=e.props.context,d=r.loadData,u=r.onNodeLoad;if(!a&&d&&n&&!e.isLeaf()){var s=e.getNodeChildren().length!==0;!s&&!c&&u((0,T.Z)(e))}},e.renderSwitcher=function(){var t=e.props,n=t.expanded,a=t.switcherIcon,c=e.props.context,r=c.prefixCls,d=c.switcherIcon,u=a||d;if(e.isLeaf())return(0,y.jsx)("span",{className:j()("".concat(r,"-switcher"),"".concat(r,"-switcher-noop")),children:typeof u=="function"?u((0,D.Z)((0,D.Z)({},e.props),{},{isLeaf:!0})):u});var s=j()("".concat(r,"-switcher"),"".concat(r,"-switcher_").concat(n?le:ce));return(0,y.jsx)("span",{onClick:e.onExpand,className:s,children:typeof u=="function"?u((0,D.Z)((0,D.Z)({},e.props),{},{isLeaf:!1})):u})},e.renderCheckbox=function(){var t=e.props,n=t.checked,a=t.halfChecked,c=t.disableCheckbox,r=e.props.context.prefixCls,d=e.isDisabled(),u=e.isCheckable();if(!u)return null;var s=typeof u!="boolean"?u:null;return(0,y.jsx)("span",{className:j()("".concat(r,"-checkbox"),n&&"".concat(r,"-checkbox-checked"),!n&&a&&"".concat(r,"-checkbox-indeterminate"),(d||c)&&"".concat(r,"-checkbox-disabled")),onClick:e.onCheck,children:s})},e.renderIcon=function(){var t=e.props.loading,n=e.props.context.prefixCls;return(0,y.jsx)("span",{className:j()("".concat(n,"-iconEle"),"".concat(n,"-icon__").concat(e.getNodeState()||"docu"),t&&"".concat(n,"-icon_loading"))})},e.renderSelector=function(){var t=e.state.dragNodeHighlight,n=e.props,a=n.title,c=n.selected,r=n.icon,d=n.loading,u=e.props.context,s=u.prefixCls,x=u.showIcon,p=u.icon,C=u.draggable,m=u.loadData,g=e.isDisabled(),k="".concat(s,"-node-content-wrapper"),K;if(x){var Z=r||p;K=Z?(0,y.jsx)("span",{className:j()("".concat(s,"-iconEle"),"".concat(s,"-icon__customize")),children:typeof Z=="function"?Z(e.props):Z}):e.renderIcon()}else m&&d&&(K=e.renderIcon());var P=(0,y.jsx)("span",{className:"".concat(s,"-title"),children:a});return(0,y.jsxs)("span",{ref:e.setSelectHandle,title:typeof a=="string"?a:"",className:j()("".concat(k),"".concat(k,"-").concat(e.getNodeState()||"normal"),!g&&(c||t)&&"".concat(s,"-node-selected"),!g&&C&&"draggable"),draggable:!g&&C||void 0,"aria-grabbed":!g&&C||void 0,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onContextMenu:e.onContextMenu,onClick:e.onSelectorClick,onDoubleClick:e.onSelectorDoubleClick,onDragStart:C?e.onDragStart:void 0,children:[K,P]})},e.renderChildren=function(){var t=e.props,n=t.expanded,a=t.pos,c=e.props.context,r=c.prefixCls,d=c.motion,u=c.renderTreeNode,s=e.getNodeChildren();if(s.length===0)return null;var x=!0;return s.forEach(function(p){p.props.children.length>0&&(x=!1)}),(0,y.jsx)(Le.ZP,(0,D.Z)((0,D.Z)({visible:n},d),{},{children:function(C){var m=C.style,g=C.className;return(0,y.jsx)("ul",{className:j()(g,"".concat(r,"-child-tree"),n&&"".concat(r,"-child-tree-open")),style:(0,D.Z)((0,D.Z)({},m),{},{display:x?"flex":""}),"data-expanded":n,role:"group",children:ge(s,function(k,K){return u(k,K,a)})})}}))},e}return(0,q.Z)(h,[{key:"componentDidMount",value:function(){var o=this.props,v=o.eventKey,i=o.context.registerTreeNode;this.syncLoadData(this.props),i(v,this)}},{key:"componentDidUpdate",value:function(){this.syncLoadData(this.props)}},{key:"componentWillUnmount",value:function(){var o=this.props,v=o.eventKey,i=o.context.registerTreeNode;i(v,null)}},{key:"isSelectable",value:function(){var o=this.props.selectable,v=this.props.context.selectable;return typeof o=="boolean"?o:v}},{key:"render",value:function(){var o,v=this.props.loading,i=this.props,t=i.className,n=i.style,a=i.dragOver,c=i.dragOverGapTop,r=i.dragOverGapBottom,d=i.isLeaf,u=i.expanded,s=i.selected,x=i.checked,p=i.halfChecked,C=(0,B.Z)(i,Ie),m=this.props.context,g=m.prefixCls,k=m.filterTreeNode,K=m.draggable,Z=this.isDisabled(),P=De(C);return(0,y.jsxs)("li",(0,D.Z)((0,D.Z)({className:j()(t,(o={},(0,w.Z)(o,"".concat(g,"-treenode-disabled"),Z),(0,w.Z)(o,"".concat(g,"-treenode-switcher-").concat(u?"open":"close"),!d),(0,w.Z)(o,"".concat(g,"-treenode-checkbox-checked"),x),(0,w.Z)(o,"".concat(g,"-treenode-checkbox-indeterminate"),p),(0,w.Z)(o,"".concat(g,"-treenode-selected"),s),(0,w.Z)(o,"".concat(g,"-treenode-loading"),v),(0,w.Z)(o,"drag-over",!Z&&a),(0,w.Z)(o,"drag-over-gap-top",!Z&&c),(0,w.Z)(o,"drag-over-gap-bottom",!Z&&r),(0,w.Z)(o,"filter-node",k&&k(this)),o)),style:n,role:"treeitem",onDragEnter:K?this.onDragEnter:void 0,onDragOver:K?this.onDragOver:void 0,onDragLeave:K?this.onDragLeave:void 0,onDrop:K?this.onDrop:void 0,onDragEnd:K?this.onDragEnd:void 0},P),{},{children:[this.renderSwitcher(),this.renderCheckbox(),this.renderSelector(),this.renderChildren()]}))}}]),h}(b.Component),te=function(l){return(0,y.jsx)(de.Consumer,{children:function(e){return(0,y.jsx)(Re,(0,D.Z)((0,D.Z)({},l),{},{context:e}))}})};te.defaultProps={title:$e},te.isTreeNode=1;var ne=te,Fe=["children"],Ae=.25,Me=2,ue=!1;function fe(){ue||(ue=!0)}function R(f,l){var h=f.slice(),e=h.indexOf(l);return e>=0&&h.splice(e,1),h}function F(f,l){var h=f.slice();return h.indexOf(l)===-1&&h.push(l),h}function Ue(f){return f.split("-")}function he(f,l){return"".concat(f,"-").concat(l)}function He(f){return f&&f.type&&f.type.isTreeNode}function ve(f){return(0,G.default)(f).filter(He)}function A(f){var l=f.props||{},h=l.disabled,e=l.disableCheckbox,o=l.checkable;return!!(h||e)||o===!1}function pe(f,l){function h(e,o,v){var i=e?e.props.children:f,t=e?he(v.pos,o):0,n=ve(i);if(e){var a={node:e,index:o,pos:t,key:e.key||t,parentPos:v.node?v.pos:null};l(a)}b.Children.forEach(n,function(c,r){h(c,r,{node:e,pos:t})})}h(null)}function ge(f,l){var h=(0,G.default)(f).map(l);return h.length===1?h[0]:h}function Be(f,l){var h=l.props,e=h.eventKey,o=h.pos,v=[];return pe(f,function(i){var t=i.key;v.push(t)}),v.push(e||o),v}function ye(f,l){var h=f.clientY,e=l.selectHandle.getBoundingClientRect(),o=e.top,v=e.bottom,i=e.height,t=Math.max(i*Ae,Me);return h<=o+t?-1:h>=v-t?1:0}function xe(f,l){if(!!f){var h=l.multiple;return h?f.slice():f.length?[f[0]]:f}}function Ce(f){return f&&f.map(function(l){return String(l)})}var Ge=function(l){return l};function me(f,l){if(!f)return[];var h=l||{},e=h.processProps,o=e===void 0?Ge:e,v=Array.isArray(f)?f:[f];return v.map(function(i){var t=i.children,n=(0,B.Z)(i,Fe),a=me(t,l);return(0,y.jsx)(ne,(0,D.Z)((0,D.Z)({},o(n)),{},{children:a}))})}function We(f){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},h=l.initWrapper,e=l.processEntity,o=l.onProcessFinished,v={},i={},t={posEntities:v,keyEntities:i};return h&&(t=h(t)||t),pe(f,function(n){var a=n.node,c=n.index,r=n.pos,d=n.key,u=n.parentPos,s={node:a,index:c,key:d,pos:r};v[r]=s,i[d]=s,s.parent=v[u],s.parent&&(s.parent.children=s.parent.children||[],s.parent.children.push(s)),e&&e(s,t)}),o&&o(t),t}function re(f){if(!f)return null;var l;if(Array.isArray(f))l={checkedKeys:f,halfCheckedKeys:void 0};else if(typeof f=="object")l={checkedKeys:f.checked||void 0,halfCheckedKeys:f.halfChecked||void 0};else return null;return l.checkedKeys=Ce(l.checkedKeys),l.halfCheckedKeys=Ce(l.halfCheckedKeys),l}function Ne(f,l,h){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o={},v={};(e.checkedKeys||[]).forEach(function(r){o[r]=!0}),(e.halfCheckedKeys||[]).forEach(function(r){v[r]=!0});function i(r){if(o[r]!==l){var d=h[r];if(!!d){var u=d.children,s=d.parent,x=d.node;if(!A(x)){var p=!0,C=!1;(u||[]).filter(function(m){return!A(m.node)}).forEach(function(m){var g=m.key,k=o[g],K=v[g];(k||K)&&(C=!0),k||(p=!1)}),l?o[r]=p:o[r]=!1,v[r]=C,s&&i(s.key)}}}}function t(r){if(o[r]!==l){var d=h[r];if(!!d){var u=d.children,s=d.node;A(s)||(o[r]=l,(u||[]).forEach(function(x){t(x.key)}))}}}function n(r){var d=h[r];if(!!d){var u=d.children,s=d.parent,x=d.node;o[r]=l,!A(x)&&((u||[]).filter(function(p){return!A(p.node)}).forEach(function(p){t(p.key)}),s&&i(s.key))}}(f||[]).forEach(function(r){n(r)});var a=[],c=[];return Object.keys(o).forEach(function(r){o[r]&&a.push(r)}),Object.keys(v).forEach(function(r){!o[r]&&v[r]&&c.push(r)}),{checkedKeys:a,halfCheckedKeys:c}}function ke(f,l){var h={};function e(o){if(!h[o]){var v=l[o];if(!!v){h[o]=!0;var i=v.parent,t=v.node;A(t)||i&&e(i.key)}}}return(f||[]).forEach(function(o){e(o)}),Object.keys(h)}function De(f){return Object.keys(f).reduce(function(l,h){return(h.substr(0,5)==="data-"||h.substr(0,5)==="aria-")&&(l[h]=f[h]),l},{})}var Ke=function(f){(0,_.Z)(h,f);var l=(0,ee.Z)(h);function h(){var e;(0,X.Z)(this,h);for(var o=arguments.length,v=new Array(o),i=0;i<o;i++)v[i]=arguments[i];return e=l.call.apply(l,[this].concat(v)),e.domTreeNodes={},e.delayedDragEnterLogic=void 0,e.state={keyEntities:{},selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],dragNodesKeys:[],dragOverNodeKey:null,dropPosition:null,treeNode:[],prevProps:null},e.dragNode=void 0,e.onNodeDragStart=function(t,n){var a=e.state.expandedKeys,c=e.props.onDragStart,r=n.props,d=r.eventKey,u=r.children;e.dragNode=n,e.setState({dragNodesKeys:Be(u,n),expandedKeys:R(a,d)}),c&&c({event:t,node:n})},e.onNodeDragEnter=function(t,n){var a=e.state.expandedKeys,c=e.props.onDragEnter,r=n.props,d=r.pos,u=r.eventKey;if(!!e.dragNode){var s=ye(t,n);if(e.dragNode.props.eventKey===u&&s===0){e.setState({dragOverNodeKey:"",dropPosition:null});return}setTimeout(function(){e.setState({dragOverNodeKey:u,dropPosition:s}),e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach(function(x){clearTimeout(e.delayedDragEnterLogic[x])}),e.delayedDragEnterLogic[d]=window.setTimeout(function(){var x=F(a,u);"expandedKeys"in e.props||e.setState({expandedKeys:x}),c&&c({event:t,node:n,expandedKeys:x})},400)},0)}},e.onNodeDragOver=function(t,n){var a=e.props.onDragOver,c=n.props.eventKey;if(e.dragNode&&c===e.state.dragOverNodeKey){var r=ye(t,n);if(r===e.state.dropPosition)return;e.setState({dropPosition:r})}a&&a({event:t,node:n})},e.onNodeDragLeave=function(t,n){var a=e.props.onDragLeave;e.setState({dragOverNodeKey:""}),a&&a({event:t,node:n})},e.onNodeDragEnd=function(t,n){var a=e.props.onDragEnd;e.setState({dragOverNodeKey:""}),a&&a({event:t,node:n}),e.dragNode=null},e.onNodeDrop=function(t,n){var a=e.state,c=a.dragNodesKeys,r=c===void 0?[]:c,d=a.dropPosition,u=e.props.onDrop,s=n.props,x=s.eventKey,p=s.pos;if(e.setState({dragOverNodeKey:""}),r.indexOf(x)===-1){var C=Ue(p),m={event:t,node:n,dragNode:e.dragNode,dragNodesKeys:r.slice(),dropPosition:d+Number(C[C.length-1]),dropToGap:!1};d!==0&&(m.dropToGap=!0),u&&u(m),e.dragNode=null}},e.onNodeClick=function(t,n){var a=e.props.onClick;a&&a(t,n)},e.onNodeDoubleClick=function(t,n){var a=e.props.onDoubleClick;a&&a(t,n)},e.onNodeSelect=function(t,n){var a=e.state.selectedKeys,c=e.state.keyEntities,r=e.props,d=r.onSelect,u=r.multiple,s=n.props,x=s.selected,p=s.eventKey,C=!x;C?u?a=F(a,p):a=[p]:a=R(a,p);var m=a.map(function(g){var k=c[g];return k?k.node:null}).filter(function(g){return g});e.setUncontrolledState({selectedKeys:a}),d&&d(a,{event:"select",selected:C,node:n,selectedNodes:m,nativeEvent:t.nativeEvent})},e.onNodeCheck=function(t,n,a){var c=e.state,r=c.keyEntities,d=c.checkedKeys,u=c.halfCheckedKeys,s=e.props,x=s.checkStrictly,p=s.onCheck,C=n.props.eventKey,m,g={event:"check",node:n,checked:a,nativeEvent:t.nativeEvent};if(x){var k=a?F(d,C):R(d,C),K=R(u,C);m={checked:k,halfChecked:K},g.checkedNodes=k.map(function(S){return r[S]}).filter(function(S){return S}).map(function(S){return S.node}),e.setUncontrolledState({checkedKeys:k})}else{var Z=Ne([C],a,r,{checkedKeys:d,halfCheckedKeys:u}),P=Z.checkedKeys,O=Z.halfCheckedKeys;m=P,g.checkedNodes=[],g.checkedNodesPositions=[],g.halfCheckedKeys=O,P.forEach(function(S){var U=r[S];if(!!U){var V=U.node,mt=U.pos;g.checkedNodes.push(V),g.checkedNodesPositions.push({node:V,pos:mt})}}),e.setUncontrolledState({checkedKeys:P,halfCheckedKeys:O})}p&&p(m,g)},e.onNodeLoad=function(t){return new Promise(function(n){e.setState(function(a){var c=a.loadedKeys,r=c===void 0?[]:c,d=a.loadingKeys,u=d===void 0?[]:d,s=e.props,x=s.loadData,p=s.onLoad,C=t.props.eventKey;if(!x||r.indexOf(C)!==-1||u.indexOf(C)!==-1)return{};var m=x(t);return m.then(function(){var g=e.state,k=g.loadedKeys,K=g.loadingKeys,Z=F(k,C),P=R(K,C);p&&p(Z,{event:"load",node:t}),e.setUncontrolledState({loadedKeys:Z}),e.setState({loadingKeys:P}),n()}),{loadingKeys:F(u,C)}})})},e.onNodeExpand=function(t,n){var a=e.state.expandedKeys,c=e.props,r=c.onExpand,d=c.loadData,u=n.props,s=u.eventKey,x=u.expanded,p=a.indexOf(s),C=!x;if(C?a=F(a,s):a=R(a,s),e.setUncontrolledState({expandedKeys:a}),r&&r(a,{node:n,expanded:C,nativeEvent:t.nativeEvent}),C&&d){var m=e.onNodeLoad(n);return m?m.then(function(){e.setUncontrolledState({expandedKeys:a})}):null}return null},e.onNodeMouseEnter=function(t,n){var a=e.props.onMouseEnter;a&&a({event:t,node:n})},e.onNodeMouseLeave=function(t,n){var a=e.props.onMouseLeave;a&&a({event:t,node:n})},e.onNodeContextMenu=function(t,n){var a=e.props.onRightClick;a&&(t.preventDefault(),a({event:t,node:n}))},e.setUncontrolledState=function(t){var n=!1,a={};Object.keys(t).forEach(function(c){c in e.props||(n=!0,a[c]=t[c])}),n&&e.setState(a)},e.registerTreeNode=function(t,n){n?e.domTreeNodes[t]=n:delete e.domTreeNodes[t]},e.isKeyChecked=function(t){var n=e.state.checkedKeys,a=n===void 0?[]:n;return a.indexOf(t)!==-1},e.renderTreeNode=function(t,n){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,c=e.state,r=c.keyEntities,d=c.expandedKeys,u=d===void 0?[]:d,s=c.selectedKeys,x=s===void 0?[]:s,p=c.halfCheckedKeys,C=p===void 0?[]:p,m=c.loadedKeys,g=m===void 0?[]:m,k=c.loadingKeys,K=k===void 0?[]:k,Z=c.dragOverNodeKey,P=c.dropPosition,O=he(a,n),S=t.key||O;return r[S]?b.cloneElement(t,{key:S,eventKey:S,expanded:u.indexOf(S)!==-1,selected:x.indexOf(S)!==-1,loaded:g.indexOf(S)!==-1,loading:K.indexOf(S)!==-1,checked:e.isKeyChecked(S),halfChecked:C.indexOf(S)!==-1,pos:O,dragOver:Z===S&&P===0,dragOverGapTop:Z===S&&P===-1,dragOverGapBottom:Z===S&&P===1}):(fe(),null)},e}return(0,q.Z)(h,[{key:"render",value:function(){var o=this,v=this.state.treeNode,i=this.props,t=i.prefixCls,n=i.className,a=i.focusable,c=i.style,r=i.showLine,d=i.tabIndex,u=d===void 0?0:d,s=i.selectable,x=i.showIcon,p=i.icon,C=i.switcherIcon,m=i.draggable,g=i.checkable,k=i.checkStrictly,K=i.disabled,Z=i.motion,P=i.loadData,O=i.filterTreeNode,S=De(this.props);return a&&(S.tabIndex=u),(0,y.jsx)(de.Provider,{value:{prefixCls:t,selectable:s,showIcon:x,icon:p,switcherIcon:C,draggable:m,checkable:g,checkStrictly:k,disabled:K,motion:Z,loadData:P,filterTreeNode:O,renderTreeNode:this.renderTreeNode,isKeyChecked:this.isKeyChecked,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop,registerTreeNode:this.registerTreeNode},children:(0,y.jsx)("ul",(0,D.Z)((0,D.Z)({},S),{},{className:j()(t,n,(0,w.Z)({},"".concat(t,"-show-line"),r)),style:c,role:"tree",unselectable:"on",children:ge(v,function(U,V){return o.renderTreeNode(U,V)})}))})}}],[{key:"getDerivedStateFromProps",value:function(o,v){var i=v.prevProps,t={prevProps:o};function n(g){return!i&&g in o||i&&i[g]!==o[g]}var a=null;if(n("treeData")?a=me(o.treeData):n("children")&&(a=(0,G.default)(o.children)),a){t.treeNode=a;var c=We(a);t.keyEntities=c.keyEntities}var r=t.keyEntities||v.keyEntities;if(n("expandedKeys")||i&&n("autoExpandParent")?t.expandedKeys=o.autoExpandParent||!i&&o.defaultExpandParent?ke(o.expandedKeys,r):o.expandedKeys:!i&&o.defaultExpandAll?t.expandedKeys=Object.keys(r):!i&&o.defaultExpandedKeys&&(t.expandedKeys=o.autoExpandParent||o.defaultExpandParent?ke(o.defaultExpandedKeys,r):o.defaultExpandedKeys),o.selectable&&(n("selectedKeys")?t.selectedKeys=xe(o.selectedKeys,o):!i&&o.defaultSelectedKeys&&(t.selectedKeys=xe(o.defaultSelectedKeys,o))),o.checkable){var d;if(n("checkedKeys")?d=re(o.checkedKeys)||{}:!i&&o.defaultCheckedKeys?d=re(o.defaultCheckedKeys)||{}:a&&(d=re(o.checkedKeys)||{checkedKeys:v.checkedKeys,halfCheckedKeys:v.halfCheckedKeys}),d){var u=d,s=u.checkedKeys,x=s===void 0?[]:s,p=u.halfCheckedKeys,C=p===void 0?[]:p;if(!o.checkStrictly){var m=Ne(x,!0,r);x=m.checkedKeys,C=m.halfCheckedKeys}t.checkedKeys=x,t.halfCheckedKeys=C}}return n("loadedKeys")&&(t.loadedKeys=o.loadedKeys),t}}]),h}(b.Component);Ke.defaultProps={prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[]};var ze=Ke,Ze=ze;Ze.TreeNode=ne;var Ve=Ze,ae=function(){return{height:0,opacity:0}},Se=function(l){return{height:l.scrollHeight,opacity:1}},Ye=function(l){return{height:l.offsetHeight}},Je={motionName:"ant-motion-collapse",onAppearStart:ae,onEnterStart:ae,onAppearActive:Se,onEnterActive:Se,onLeaveStart:Ye,onLeaveActive:ae},Qe=Je,be=N(16165),Xe=N(46133),qe=N.n(Xe),_e=N(3361),et=N.n(_e),tt=N(31929),W=N(83279),nt=["value","flatTreeData","onChange","readonly"],rt=function(l){var h=l.value,e=h===void 0?[]:h,o=l.flatTreeData,v=l.onChange,i=l.readonly,t=(0,B.Z)(l,nt);return(0,y.jsx)(oe,(0,D.Z)({checkable:!0,showIcon:!0,defaultCheckedKeys:e,checkedKeys:e,checkStrictly:!0,defaultExpandAll:!0,onCheck:function(a,c){var r=a.checked,d=c.node.props.eventKey;if(!(!d||!v||i)){var u=(o==null?void 0:o.filter(function(s){return s.key.startsWith(d)}).map(function(s){return s.key}))||[];if(c.checked){v((0,W.Z)(new Set([].concat((0,W.Z)(r),(0,W.Z)(u)))));return}if(!c.checked){v(r.filter(function(s){return!u.includes(s)}));return}}}},t))},oe=function(f){(0,_.Z)(h,f);var l=(0,ee.Z)(h);function h(){var e;(0,X.Z)(this,h);for(var o=arguments.length,v=new Array(o),i=0;i<o;i++)v[i]=arguments[i];return e=l.call.apply(l,[this].concat(v)),e.tree=void 0,e.renderSwitcherIcon=function(t,n,a){var c=a.isLeaf,r=a.expanded,d=a.loading,u=e.props.showLine;if(d)return(0,y.jsx)(be.Z,{type:"loading",className:"".concat(t,"-switcher-loading-icon")});if(c)return u?(0,y.jsx)(be.Z,{type:"file",className:"".concat(t,"-switcher-line-icon")}):null;var s="".concat(t,"-switcher-icon");return n?b.cloneElement(n,{className:j()(n.props.className||"",s)}):r?(0,y.jsx)(qe(),{className:s}):(0,y.jsx)(et(),{className:s})},e.setTreeRef=function(t){e.tree=t},e.renderTree=function(t){var n,a=t.getPrefixCls,c=(0,T.Z)(e),r=c.props,d=r.prefixCls,u=r.className,s=r.showIcon,x=r.switcherIcon,p=r.blockNode,C=r.children,m=r.checkable,g="ant3-tree";return(0,y.jsx)(Ve,(0,D.Z)((0,D.Z)({ref:e.setTreeRef},r),{},{prefixCls:g,className:j()(u,(n={},(0,w.Z)(n,"".concat(g,"-icon-hide"),!s),(0,w.Z)(n,"".concat(g,"-block-node"),p),n)),checkable:m&&(0,y.jsx)("span",{className:"".concat(g,"-checkbox-inner")}),switcherIcon:function(K){return e.renderSwitcherIcon(g,x,K)},children:C}))},e}return(0,q.Z)(h,[{key:"render",value:function(){return(0,y.jsx)(tt.ConfigConsumer,{children:this.renderTree})}}]),h}(b.Component);oe.TreeNode=ne,oe.defaultProps={checkable:!1,showIcon:!1,motion:(0,D.Z)((0,D.Z)({},Qe),{},{motionAppear:!1}),blockNode:!1};var at=N(52125),ot=N(14361),L=N(21307),st=N(64284),it=["treeData","flatTreeData","readonly"];function dt(f){for(var l=f.split(":"),h=[],e=0;e<l.length;e++)e>0?h.push("".concat(h[e-1],":").concat(l[e])):h.push(l[0]);return h}var z={list:"1",detail:"2",create:"3",update:"4",delete:"5",import:"6",export:"7"},lt=function f(l,h){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"parentKey",o=l.filter(function(i){return i[e]===h}),v=o.filter(function(i){return Object.keys(z).includes(i.key.toLowerCase().split(":").slice(-1)[0])}).length>0;return v&&o.sort(function(i,t){var n=z[i.key.toLowerCase().split(":").slice(-1)[0]]||i,a=z[t.key.toLowerCase().split(":").slice(-1)[0]]||t;return n>a?1:n<a?-1:0}),o.map(function(i){return(0,D.Z)((0,D.Z)({},i),{},{children:f(l,i.key)})})},ct=function(l){var h=l.treeData,e=l.flatTreeData,o=l.readonly,v=(0,B.Z)(l,it);return(0,y.jsx)(L.ZP.Item,(0,D.Z)((0,D.Z)({},v),{},{children:(0,y.jsx)(rt,(0,D.Z)({treeData:h,flatTreeData:e},l.fieldProps))}))},ut=(0,ie.Pi)(function(f){var l=f.onOk,h=f.onClose,e=f.role,o=(0,b.useRef)(),v=!!(e!=null&&e.id),i=(0,st.Z)(),t=(0,$.m2)(),n=[];t.permissions.filter(function(r){return!r.code.startsWith("ROLE_")}).forEach(function(r){dt(r.code).forEach(function(d,u,s){var x=r.name||i.formatMessage({id:"authority.".concat(d.replaceAll(":",".")),defaultMessage:d});if(!n.find(function(C){return C.key===d})){var p=Object.keys(z).includes(d.toLowerCase().split(":").slice(-1)[0]);n.push({key:d,title:x,icon:p?(0,y.jsx)(at.Z,{}):(0,y.jsx)(ot.Z,{}),parentKey:s[u-1]})}})});var a=lt(n,void 0),c=["home","project","exercise","repo","answer","template","user","system"];return a=a.sort(function(r,d){return c.indexOf(r.key)-c.indexOf(d.key)}),(0,y.jsxs)(L.aN,{title:v?e!=null&&e.id?"\u4FEE\u6539\u89D2\u8272":"\u65B0\u589E\u89D2\u8272":"\u67E5\u770B\u89D2\u8272",formRef:o,open:!0,initialValues:e,drawerProps:{forceRender:!0,destroyOnClose:!0,onClose:function(){h()}},onFinish:function(){var r=(0,I.Z)((0,E.Z)().mark(function d(u){var s;return(0,E.Z)().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:if(!((e==null?void 0:e.code)==="admin"&&u.code!=="admin")){p.next=3;break}return H.default.error("\u9ED8\u8BA4\u7BA1\u7406\u5458\u89D2\u8272\u7F16\u7801\u4E0D\u80FD\u4FEE\u6539"),p.abrupt("return");case 3:return p.next=5,t.saveOrUpdateRole((0,D.Z)((0,D.Z)({},e),u));case 5:s=p.sent,s.success&&l();case 7:case"end":return p.stop()}},d)}));return function(d){return r.apply(this,arguments)}}(),children:[(0,y.jsxs)(L.ZP.Group,{children:[(0,y.jsx)(L.V,{name:"name",width:"md",label:"\u89D2\u8272\u540D\u79F0",placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0",required:!0,rules:[{required:!0,message:"\u89D2\u8272\u540D\u4E0D\u80FD\u4E3A\u7A7A"}]}),(0,y.jsx)(L.V,{width:"md",name:"code",label:"\u89D2\u8272\u7F16\u7801",placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u7F16\u7801",rules:[{required:!0,message:"\u89D2\u8272\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A"}],required:!0}),(0,y.jsx)(L._I,{width:"md",name:"status",label:"\u72B6\u6001",rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u89D2\u8272\u72B6\u6001"}],fieldProps:{options:[{label:"\u6B63\u5E38",value:1},{label:"\u505C\u7528",value:0}]},required:!0})]}),(0,y.jsx)(L.ZP.Group,{children:(0,y.jsx)(L.$J,{name:"remark",width:"xl",label:"\u63CF\u8FF0",placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u63CF\u8FF0\u4FE1\u606F"})}),(0,y.jsx)(L.ZP.Group,{children:(0,y.jsx)(ct,{name:"authorities",label:"\u89D2\u8272\u6388\u6743",treeData:a,flatTreeData:n})})]})}),St=N(57338),ft=N(273),bt=N(62350),ht=N(24565),vt=N(37809),pt=N(96642),gt=function(l){var h=l.onClose,e=l.role,o=(0,b.useRef)(),v=(0,b.useState)([]),i=(0,M.Z)(v,2),t=i[0],n=i[1],a=[{title:"\u7EC4\u7EC7\u673A\u6784",dataIndex:"deptName",hideInSearch:!0,width:150},{title:"\u59D3\u540D",dataIndex:"name",ellipsis:!0,width:120},{title:"\u624B\u673A",dataIndex:"phone",width:150,search:!1},{title:"\u72B6\u6001",dataIndex:"status",search:!1,width:100,valueEnum:{0:{text:"\u5931\u6D3B",status:"Default"},1:{text:"\u6FC0\u6D3B",status:"Processing"}}},{title:"\u521B\u5EFA\u65F6\u95F4",key:"showTime",dataIndex:"createAt",width:150,valueType:"dateTime",hideInSearch:!0}];return(0,y.jsx)(J.Z,{title:"\u9009\u62E9\u7528\u6237",open:!0,width:950,onCancel:function(){return h()},okText:"\u6279\u91CF\u6DFB\u52A0",okButtonProps:{disabled:t.length===0},onOk:(0,I.Z)((0,E.Z)().mark(function c(){var r,d;return(0,E.Z)().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=2,$.hi.updateRole({id:e.id,userIds:t});case 2:if(r=s.sent,!r.success){s.next=9;break}return(d=o.current)===null||d===void 0||d.reload(),n([]),s.abrupt("return",!0);case 9:return H.default.error(r.message||"\u6DFB\u52A0\u5931\u8D25"),s.abrupt("return",!1);case 11:case"end":return s.stop()}},c)})),children:(0,y.jsx)(Q.ZP,{columns:a,bordered:!0,actionRef:o,params:{neRoleId:e.id},request:function(){var c=(0,I.Z)((0,E.Z)().mark(function r(d){return(0,E.Z)().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.abrupt("return",$.hi.loadUsers(d));case 1:case"end":return s.stop()}},r)}));return function(r){return c.apply(this,arguments)}}(),rowKey:"id",pagination:{defaultPageSize:10},rowSelection:{selectedRowKeys:t,onChange:function(r){n(r)}}})})},yt=function(l){var h=l.onClose,e=l.role,o=(0,b.useRef)(),v=(0,b.useState)([]),i=(0,M.Z)(v,2),t=i[0],n=i[1],a=(0,b.useState)(),c=(0,M.Z)(a,2),r=c[0],d=c[1],u=function(){var x=(0,I.Z)((0,E.Z)().mark(function p(){var C,m,g,k,K,Z=arguments;return(0,E.Z)().wrap(function(O){for(;;)switch(O.prev=O.next){case 0:for(C=Z.length,m=new Array(C),g=0;g<C;g++)m[g]=Z[g];return O.next=3,$.hi.updateRole({id:e.id,evictUserIds:m});case 3:if(k=O.sent,!k.success){O.next=9;break}return(K=o.current)===null||K===void 0||K.reload(),O.abrupt("return",!0);case 9:return H.default.error(k.message||"\u53D6\u6D88\u5931\u8D25"),O.abrupt("return",!1);case 11:case"end":return O.stop()}},p)}));return function(){return x.apply(this,arguments)}}(),s=[{title:"\u7EC4\u7EC7\u673A\u6784",dataIndex:"deptName",hideInSearch:!0,width:150},{title:"\u59D3\u540D",dataIndex:"name",ellipsis:!0,width:120},{title:"\u624B\u673A",dataIndex:"phone",width:150,search:!1},{title:"\u72B6\u6001",dataIndex:"status",search:!1,width:100,valueEnum:{0:{text:"\u5931\u6D3B",status:"Default"},1:{text:"\u6FC0\u6D3B",status:"Processing"}}},{title:"\u521B\u5EFA\u65F6\u95F4",key:"showTime",dataIndex:"createAt",width:180,hideInSearch:!0},{title:"\u64CD\u4F5C",valueType:"option",render:function(p,C){return[(0,y.jsx)(ht.Z,{title:(0,y.jsxs)(y.Fragment,{children:["\u786E\u5B9A\u53D6\u6D88\u7528\u6237 ",(0,y.jsx)("b",{children:e.name})," \u89D2\u8272\u5417\uFF1F"]}),onConfirm:(0,I.Z)((0,E.Z)().mark(function m(){return(0,E.Z)().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:u(C.id);case 1:case"end":return k.stop()}},m)})),children:(0,y.jsx)("a",{children:"\u53D6\u6D88\u6388\u6743"},"editable")})]}}];return(0,y.jsxs)(ft.Z,{title:"\u5206\u914D\u7528\u6237",onClose:h,open:!0,width:1e3,children:[(0,y.jsx)(Q.ZP,{columns:s,bordered:!0,actionRef:o,params:{roleId:e.id},request:function(){var x=(0,I.Z)((0,E.Z)().mark(function p(C){return(0,E.Z)().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return g.abrupt("return",$.hi.loadUsers(C));case 1:case"end":return g.stop()}},p)}));return function(p){return x.apply(this,arguments)}}(),rowKey:"id",pagination:{defaultPageSize:10},rowSelection:{selectedRowKeys:t,onChange:function(p){n(p)}},toolBarRender:function(){return[(0,y.jsx)(Y.Z,{icon:(0,y.jsx)(vt.Z,{}),type:"primary",onClick:function(){d(!0)},children:"\u6DFB\u52A0\u7528\u6237"},"batchAdd"),(0,y.jsx)(Y.Z,{danger:!0,disabled:t.length===0,icon:(0,y.jsx)(pt.Z,{}),type:"primary",onClick:(0,I.Z)((0,E.Z)().mark(function p(){return(0,E.Z)().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:return m.next=2,u.apply(void 0,(0,W.Z)(t));case 2:n([]);case 3:case"end":return m.stop()}},p)})),children:"\u6279\u91CF\u53D6\u6D88\u6388\u6743"},"batchCancel")]}}),r&&(0,y.jsx)(gt,{role:e,onClose:function(){var p;d(!1),(p=o.current)===null||p===void 0||p.reload()}})]})},xt=(0,ie.Pi)(function(){var f=(0,$.m2)(),l=(0,b.useRef)(),h=(0,b.useState)(),e=(0,M.Z)(h,2),o=e[0],v=e[1],i=(0,b.useState)(),t=(0,M.Z)(i,2),n=t[0],a=t[1];(0,b.useEffect)(function(){f.loadPermissions()},[f]);var c=[{title:"\u89D2\u8272\u540D\u79F0",dataIndex:"name",ellipsis:!0,width:150},{title:"\u7F16\u7801",dataIndex:"code",hideInSearch:!0,width:150},{title:"\u63CF\u8FF0",dataIndex:"remark",search:!1},{title:"\u72B6\u6001",dataIndex:"status",search:!1,renderText:function(d,u,s,x){return(0,y.jsx)(Ee.Z,{checked:d===1,onChange:function(C){$.hi.updateRole({id:u.id,status:C?1:0}).then(function(m){if(m.success){var g;(g=l.current)===null||g===void 0||g.reload()}})}})}},{title:"\u521B\u5EFA\u65F6\u95F4",key:"showTime",dataIndex:"createAt",valueType:"dateTime",hideInSearch:!0,width:200},{title:"\u64CD\u4F5C",valueType:"option",width:200,render:function(d,u){return[(0,y.jsx)("a",{onClick:function(){a({current:u,visible:!0})},children:"\u7F16\u8F91"},"edit"),(0,y.jsx)(we.Z,{onSelect:function(x){if(x==="delete"){if(u.id==="1"){J.Z.info({title:"\u63D0\u793A",content:"\u5185\u7F6E\u89D2\u8272\u4E0D\u80FD\u5220\u9664"});return}J.Z.confirm({title:"\u5220\u9664",content:"\u786E\u5B9A\u5220\u9664\u5F53\u524D\u89D2\u8272?",icon:(0,y.jsx)(Te.Z,{}),onOk:function(){f.deleteRole(u.id).then(function(C){if(C.success){var m;H.default.success("\u5220\u9664\u6210\u529F"),(m=l.current)===null||m===void 0||m.reload()}})}})}else x==="roleUser"&&v(u)},menus:u.code==="admin"?[{key:"roleUser",name:"\u5206\u914D\u7528\u6237"}]:[{key:"roleUser",name:"\u5206\u914D\u7528\u6237"},{key:"delete",name:"\u5220\u9664"}]},"actionGroup")]}}];return(0,y.jsxs)(Oe._z,{title:!1,children:[(0,y.jsx)(Q.ZP,{columns:c,bordered:!0,actionRef:l,dataSource:f.roles,request:function(){var r=(0,I.Z)((0,E.Z)().mark(function d(u){return(0,E.Z)().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:return x.abrupt("return",f.loadRoles(u));case 1:case"end":return x.stop()}},d)}));return function(d){return r.apply(this,arguments)}}(),scroll:{x:800},columnsState:{persistenceKey:"pro-table-singe-demos",persistenceType:"localStorage"},rowKey:"id",search:{labelWidth:"auto"},pagination:{defaultPageSize:5},dateFormatter:"string",headerTitle:"\u7CFB\u7EDF\u89D2\u8272\u5217\u8868",toolBarRender:function(){return[(0,y.jsx)(Y.Z,{icon:(0,y.jsx)(Pe.Z,{}),type:"primary",onClick:function(){a({visible:!0})},children:"\u65B0\u5EFA"},"button")]}}),(n==null?void 0:n.visible)&&(0,y.jsx)(ut,{onClose:function(){a(void 0)},onOk:function(){var d;a(void 0),(d=l.current)===null||d===void 0||d.reload()},role:n.current}),o&&(0,y.jsx)(yt,{role:o,onClose:function(){return v(void 0)}})]})}),Ct=xt}}]);
