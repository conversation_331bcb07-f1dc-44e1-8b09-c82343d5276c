(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[9043],{8166:function(c4,oa,ua){"use strict";ua.d(oa,{Z:function(){return s4}});class l0{constructor(e,t,a){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=a}static range(e,t){return t?!e||!e.loc||!t.loc||e.loc.lexer!==t.loc.lexer?null:new l0(e.loc.lexer,e.loc.start,t.loc.end):e&&e.loc}}class h0{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new h0(t,l0.range(this,e))}}class M{constructor(e,t){this.position=void 0;var a="KaTeX parse error: "+e,i,s=t&&t.loc;if(s&&s.start<=s.end){var o=s.lexer.input;i=s.start;var h=s.end;i===o.length?a+=" at end of input: ":a+=" at position "+(i+1)+": ";var c=o.slice(i,h).replace(/[^]/g,"$&\u0332"),p;i>15?p="\u2026"+o.slice(i-15,i):p=o.slice(0,i);var g;h+15<o.length?g=o.slice(h,h+15)+"\u2026":g=o.slice(h),a+=p+c+g}var b=new Error(a);return b.name="ParseError",b.__proto__=M.prototype,b.position=i,b}}M.prototype.__proto__=Error.prototype;var ha=function(e,t){return e.indexOf(t)!==-1},ma=function(e,t){return e===void 0?t:e},ca=/([A-Z])/g,da=function(e){return e.replace(ca,"-$1").toLowerCase()},fa={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},pa=/[&><"']/g;function va(r){return String(r).replace(pa,e=>fa[e])}var Dt=function r(e){return e.type==="ordgroup"||e.type==="color"?e.body.length===1?r(e.body[0]):e:e.type==="font"?r(e.body):e},ga=function(e){var t=Dt(e);return t.type==="mathord"||t.type==="textord"||t.type==="atom"},ba=function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e},ya=function(e){var t=/^\s*([^\\/#]*?)(?::|&#0*58|&#x0*3a)/i.exec(e);return t!=null?t[1]:"_relative"},R={contains:ha,deflt:ma,escape:va,hyphenate:da,getBaseElem:Dt,isCharacterBox:ga,protocolFromUrl:ya},ue={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:r=>"#"+r},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(r,e)=>(e.push(r),e)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:r=>Math.max(0,r),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:Infinity,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:r=>Math.max(0,r),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:r=>Math.max(0,r),cli:"-e, --max-expand <n>",cliProcessor:r=>r==="Infinity"?Infinity:parseInt(r)},globalGroup:{type:"boolean",cli:!1}};function xa(r){if(r.default)return r.default;var e=r.type,t=Array.isArray(e)?e[0]:e;if(typeof t!="string")return t.enum[0];switch(t){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class Re{constructor(e){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{};for(var t in ue)if(ue.hasOwnProperty(t)){var a=ue[t];this[t]=e[t]!==void 0?a.processor?a.processor(e[t]):e[t]:xa(a)}}reportNonstrict(e,t,a){var i=this.strict;if(typeof i=="function"&&(i=i(e,t,a)),!(!i||i==="ignore")){if(i===!0||i==="error")throw new M("LaTeX-incompatible input and strict mode is set to 'error': "+(t+" ["+e+"]"),a);i==="warn"?typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")):typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+i+"': "+t+" ["+e+"]"))}}useStrictBehavior(e,t,a){var i=this.strict;if(typeof i=="function")try{i=i(e,t,a)}catch(s){i="error"}return!i||i==="ignore"?!1:i===!0||i==="error"?!0:i==="warn"?(typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" ["+e+"]")),!1):(typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+i+"': "+t+" ["+e+"]")),!1)}isTrusted(e){e.url&&!e.protocol&&(e.protocol=R.protocolFromUrl(e.url));var t=typeof this.trust=="function"?this.trust(e):this.trust;return Boolean(t)}}class q0{constructor(e,t,a){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=a}sup(){return g0[wa[this.id]]}sub(){return g0[ka[this.id]]}fracNum(){return g0[Sa[this.id]]}fracDen(){return g0[Ma[this.id]]}cramp(){return g0[za[this.id]]}text(){return g0[Aa[this.id]]}isTight(){return this.size>=2}}var Ie=0,he=1,Y0=2,k0=3,ee=4,m0=5,X0=6,r0=7,g0=[new q0(Ie,0,!1),new q0(he,0,!0),new q0(Y0,1,!1),new q0(k0,1,!0),new q0(ee,2,!1),new q0(m0,2,!0),new q0(X0,3,!1),new q0(r0,3,!0)],wa=[ee,m0,ee,m0,X0,r0,X0,r0],ka=[m0,m0,m0,m0,r0,r0,r0,r0],Sa=[Y0,k0,ee,m0,X0,r0,X0,r0],Ma=[k0,k0,m0,m0,r0,r0,r0,r0],za=[he,he,k0,k0,m0,m0,r0,r0],Aa=[Ie,he,Y0,k0,Y0,k0,Y0,k0],q={DISPLAY:g0[Ie],TEXT:g0[Y0],SCRIPT:g0[ee],SCRIPTSCRIPT:g0[X0]},Oe=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function Ta(r){for(var e=0;e<Oe.length;e++)for(var t=Oe[e],a=0;a<t.blocks.length;a++){var i=t.blocks[a];if(r>=i[0]&&r<=i[1])return t.name}return null}var me=[];Oe.forEach(r=>r.blocks.forEach(e=>me.push(...e)));function Ct(r){for(var e=0;e<me.length;e+=2)if(r>=me[e]&&r<=me[e+1])return!0;return!1}var $0=80,Ba=function(e,t){return"M95,"+(622+e+t)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+e/2.075+" -"+e+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+e)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},Da=function(e,t){return"M263,"+(601+e+t)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+e/2.084+" -"+e+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+e)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},Ca=function(e,t){return"M983 "+(10+e+t)+`
l`+e/3.13+" -"+e+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+e)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+e)+" "+t+"h400000v"+(40+e)+"h-400000z"},Na=function(e,t){return"M424,"+(2398+e+t)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+e/4.223+" -"+e+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+e)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+e)+" "+t+`
h400000v`+(40+e)+"h-400000z"},qa=function(e,t){return"M473,"+(2713+e+t)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+e/5.298+" -"+e+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+e)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+e)+" "+t+"h400000v"+(40+e)+"H1017.7z"},Ea=function(e){var t=e/2;return"M400000 "+e+" H0 L"+t+" 0 l65 45 L145 "+(e-80)+" H400000z"},Ra=function(e,t,a){var i=a-54-t-e;return"M702 "+(e+t)+"H400000"+(40+e)+`
H742v`+i+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+t+"H400000v"+(40+e)+"H742z"},Ia=function(e,t,a){t=1e3*t;var i="";switch(e){case"sqrtMain":i=Ba(t,$0);break;case"sqrtSize1":i=Da(t,$0);break;case"sqrtSize2":i=Ca(t,$0);break;case"sqrtSize3":i=Na(t,$0);break;case"sqrtSize4":i=qa(t,$0);break;case"sqrtTall":i=Ra(t,$0,a)}return i},Oa=function(e,t){switch(e){case"\u239C":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"\u2223":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"\u2225":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V"+t+" H367z");case"\u239F":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"\u23A2":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"\u23A5":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"\u23AA":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"\u23D0":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"\u2016":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V"+t+" H478z");default:return""}},Nt={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`};class te{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return R.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){var e=t=>t.toText();return this.children.map(e).join("")}}var b0={"AMS-Regular":{"32":[0,0,0,0,.25],"65":[0,.68889,0,0,.72222],"66":[0,.68889,0,0,.66667],"67":[0,.68889,0,0,.72222],"68":[0,.68889,0,0,.72222],"69":[0,.68889,0,0,.66667],"70":[0,.68889,0,0,.61111],"71":[0,.68889,0,0,.77778],"72":[0,.68889,0,0,.77778],"73":[0,.68889,0,0,.38889],"74":[.16667,.68889,0,0,.5],"75":[0,.68889,0,0,.77778],"76":[0,.68889,0,0,.66667],"77":[0,.68889,0,0,.94445],"78":[0,.68889,0,0,.72222],"79":[.16667,.68889,0,0,.77778],"80":[0,.68889,0,0,.61111],"81":[.16667,.68889,0,0,.77778],"82":[0,.68889,0,0,.72222],"83":[0,.68889,0,0,.55556],"84":[0,.68889,0,0,.66667],"85":[0,.68889,0,0,.72222],"86":[0,.68889,0,0,.72222],"87":[0,.68889,0,0,1],"88":[0,.68889,0,0,.72222],"89":[0,.68889,0,0,.72222],"90":[0,.68889,0,0,.66667],"107":[0,.68889,0,0,.55556],"160":[0,0,0,0,.25],"165":[0,.675,.025,0,.75],"174":[.15559,.69224,0,0,.94666],"240":[0,.68889,0,0,.55556],"295":[0,.68889,0,0,.54028],"710":[0,.825,0,0,2.33334],"732":[0,.9,0,0,2.33334],"770":[0,.825,0,0,2.33334],"771":[0,.9,0,0,2.33334],"989":[.08167,.58167,0,0,.77778],"1008":[0,.43056,.04028,0,.66667],"8245":[0,.54986,0,0,.275],"8463":[0,.68889,0,0,.54028],"8487":[0,.68889,0,0,.72222],"8498":[0,.68889,0,0,.55556],"8502":[0,.68889,0,0,.66667],"8503":[0,.68889,0,0,.44445],"8504":[0,.68889,0,0,.66667],"8513":[0,.68889,0,0,.63889],"8592":[-.03598,.46402,0,0,.5],"8594":[-.03598,.46402,0,0,.5],"8602":[-.13313,.36687,0,0,1],"8603":[-.13313,.36687,0,0,1],"8606":[.01354,.52239,0,0,1],"8608":[.01354,.52239,0,0,1],"8610":[.01354,.52239,0,0,1.11111],"8611":[.01354,.52239,0,0,1.11111],"8619":[0,.54986,0,0,1],"8620":[0,.54986,0,0,1],"8621":[-.13313,.37788,0,0,1.38889],"8622":[-.13313,.36687,0,0,1],"8624":[0,.69224,0,0,.5],"8625":[0,.69224,0,0,.5],"8630":[0,.43056,0,0,1],"8631":[0,.43056,0,0,1],"8634":[.08198,.58198,0,0,.77778],"8635":[.08198,.58198,0,0,.77778],"8638":[.19444,.69224,0,0,.41667],"8639":[.19444,.69224,0,0,.41667],"8642":[.19444,.69224,0,0,.41667],"8643":[.19444,.69224,0,0,.41667],"8644":[.1808,.675,0,0,1],"8646":[.1808,.675,0,0,1],"8647":[.1808,.675,0,0,1],"8648":[.19444,.69224,0,0,.83334],"8649":[.1808,.675,0,0,1],"8650":[.19444,.69224,0,0,.83334],"8651":[.01354,.52239,0,0,1],"8652":[.01354,.52239,0,0,1],"8653":[-.13313,.36687,0,0,1],"8654":[-.13313,.36687,0,0,1],"8655":[-.13313,.36687,0,0,1],"8666":[.13667,.63667,0,0,1],"8667":[.13667,.63667,0,0,1],"8669":[-.13313,.37788,0,0,1],"8672":[-.064,.437,0,0,1.334],"8674":[-.064,.437,0,0,1.334],"8705":[0,.825,0,0,.5],"8708":[0,.68889,0,0,.55556],"8709":[.08167,.58167,0,0,.77778],"8717":[0,.43056,0,0,.42917],"8722":[-.03598,.46402,0,0,.5],"8724":[.08198,.69224,0,0,.77778],"8726":[.08167,.58167,0,0,.77778],"8733":[0,.69224,0,0,.77778],"8736":[0,.69224,0,0,.72222],"8737":[0,.69224,0,0,.72222],"8738":[.03517,.52239,0,0,.72222],"8739":[.08167,.58167,0,0,.22222],"8740":[.25142,.74111,0,0,.27778],"8741":[.08167,.58167,0,0,.38889],"8742":[.25142,.74111,0,0,.5],"8756":[0,.69224,0,0,.66667],"8757":[0,.69224,0,0,.66667],"8764":[-.13313,.36687,0,0,.77778],"8765":[-.13313,.37788,0,0,.77778],"8769":[-.13313,.36687,0,0,.77778],"8770":[-.03625,.46375,0,0,.77778],"8774":[.30274,.79383,0,0,.77778],"8776":[-.01688,.48312,0,0,.77778],"8778":[.08167,.58167,0,0,.77778],"8782":[.06062,.54986,0,0,.77778],"8783":[.06062,.54986,0,0,.77778],"8785":[.08198,.58198,0,0,.77778],"8786":[.08198,.58198,0,0,.77778],"8787":[.08198,.58198,0,0,.77778],"8790":[0,.69224,0,0,.77778],"8791":[.22958,.72958,0,0,.77778],"8796":[.08198,.91667,0,0,.77778],"8806":[.25583,.75583,0,0,.77778],"8807":[.25583,.75583,0,0,.77778],"8808":[.25142,.75726,0,0,.77778],"8809":[.25142,.75726,0,0,.77778],"8812":[.25583,.75583,0,0,.5],"8814":[.20576,.70576,0,0,.77778],"8815":[.20576,.70576,0,0,.77778],"8816":[.30274,.79383,0,0,.77778],"8817":[.30274,.79383,0,0,.77778],"8818":[.22958,.72958,0,0,.77778],"8819":[.22958,.72958,0,0,.77778],"8822":[.1808,.675,0,0,.77778],"8823":[.1808,.675,0,0,.77778],"8828":[.13667,.63667,0,0,.77778],"8829":[.13667,.63667,0,0,.77778],"8830":[.22958,.72958,0,0,.77778],"8831":[.22958,.72958,0,0,.77778],"8832":[.20576,.70576,0,0,.77778],"8833":[.20576,.70576,0,0,.77778],"8840":[.30274,.79383,0,0,.77778],"8841":[.30274,.79383,0,0,.77778],"8842":[.13597,.63597,0,0,.77778],"8843":[.13597,.63597,0,0,.77778],"8847":[.03517,.54986,0,0,.77778],"8848":[.03517,.54986,0,0,.77778],"8858":[.08198,.58198,0,0,.77778],"8859":[.08198,.58198,0,0,.77778],"8861":[.08198,.58198,0,0,.77778],"8862":[0,.675,0,0,.77778],"8863":[0,.675,0,0,.77778],"8864":[0,.675,0,0,.77778],"8865":[0,.675,0,0,.77778],"8872":[0,.69224,0,0,.61111],"8873":[0,.69224,0,0,.72222],"8874":[0,.69224,0,0,.88889],"8876":[0,.68889,0,0,.61111],"8877":[0,.68889,0,0,.61111],"8878":[0,.68889,0,0,.72222],"8879":[0,.68889,0,0,.72222],"8882":[.03517,.54986,0,0,.77778],"8883":[.03517,.54986,0,0,.77778],"8884":[.13667,.63667,0,0,.77778],"8885":[.13667,.63667,0,0,.77778],"8888":[0,.54986,0,0,1.11111],"8890":[.19444,.43056,0,0,.55556],"8891":[.19444,.69224,0,0,.61111],"8892":[.19444,.69224,0,0,.61111],"8901":[0,.54986,0,0,.27778],"8903":[.08167,.58167,0,0,.77778],"8905":[.08167,.58167,0,0,.77778],"8906":[.08167,.58167,0,0,.77778],"8907":[0,.69224,0,0,.77778],"8908":[0,.69224,0,0,.77778],"8909":[-.03598,.46402,0,0,.77778],"8910":[0,.54986,0,0,.76042],"8911":[0,.54986,0,0,.76042],"8912":[.03517,.54986,0,0,.77778],"8913":[.03517,.54986,0,0,.77778],"8914":[0,.54986,0,0,.66667],"8915":[0,.54986,0,0,.66667],"8916":[0,.69224,0,0,.66667],"8918":[.0391,.5391,0,0,.77778],"8919":[.0391,.5391,0,0,.77778],"8920":[.03517,.54986,0,0,1.33334],"8921":[.03517,.54986,0,0,1.33334],"8922":[.38569,.88569,0,0,.77778],"8923":[.38569,.88569,0,0,.77778],"8926":[.13667,.63667,0,0,.77778],"8927":[.13667,.63667,0,0,.77778],"8928":[.30274,.79383,0,0,.77778],"8929":[.30274,.79383,0,0,.77778],"8934":[.23222,.74111,0,0,.77778],"8935":[.23222,.74111,0,0,.77778],"8936":[.23222,.74111,0,0,.77778],"8937":[.23222,.74111,0,0,.77778],"8938":[.20576,.70576,0,0,.77778],"8939":[.20576,.70576,0,0,.77778],"8940":[.30274,.79383,0,0,.77778],"8941":[.30274,.79383,0,0,.77778],"8994":[.19444,.69224,0,0,.77778],"8995":[.19444,.69224,0,0,.77778],"9416":[.15559,.69224,0,0,.90222],"9484":[0,.69224,0,0,.5],"9488":[0,.69224,0,0,.5],"9492":[0,.37788,0,0,.5],"9496":[0,.37788,0,0,.5],"9585":[.19444,.68889,0,0,.88889],"9586":[.19444,.74111,0,0,.88889],"9632":[0,.675,0,0,.77778],"9633":[0,.675,0,0,.77778],"9650":[0,.54986,0,0,.72222],"9651":[0,.54986,0,0,.72222],"9654":[.03517,.54986,0,0,.77778],"9660":[0,.54986,0,0,.72222],"9661":[0,.54986,0,0,.72222],"9664":[.03517,.54986,0,0,.77778],"9674":[.11111,.69224,0,0,.66667],"9733":[.19444,.69224,0,0,.94445],"10003":[0,.69224,0,0,.83334],"10016":[0,.69224,0,0,.83334],"10731":[.11111,.69224,0,0,.66667],"10846":[.19444,.75583,0,0,.61111],"10877":[.13667,.63667,0,0,.77778],"10878":[.13667,.63667,0,0,.77778],"10885":[.25583,.75583,0,0,.77778],"10886":[.25583,.75583,0,0,.77778],"10887":[.13597,.63597,0,0,.77778],"10888":[.13597,.63597,0,0,.77778],"10889":[.26167,.75726,0,0,.77778],"10890":[.26167,.75726,0,0,.77778],"10891":[.48256,.98256,0,0,.77778],"10892":[.48256,.98256,0,0,.77778],"10901":[.13667,.63667,0,0,.77778],"10902":[.13667,.63667,0,0,.77778],"10933":[.25142,.75726,0,0,.77778],"10934":[.25142,.75726,0,0,.77778],"10935":[.26167,.75726,0,0,.77778],"10936":[.26167,.75726,0,0,.77778],"10937":[.26167,.75726,0,0,.77778],"10938":[.26167,.75726,0,0,.77778],"10949":[.25583,.75583,0,0,.77778],"10950":[.25583,.75583,0,0,.77778],"10955":[.28481,.79383,0,0,.77778],"10956":[.28481,.79383,0,0,.77778],"57350":[.08167,.58167,0,0,.22222],"57351":[.08167,.58167,0,0,.38889],"57352":[.08167,.58167,0,0,.77778],"57353":[0,.43056,.04028,0,.66667],"57356":[.25142,.75726,0,0,.77778],"57357":[.25142,.75726,0,0,.77778],"57358":[.41951,.91951,0,0,.77778],"57359":[.30274,.79383,0,0,.77778],"57360":[.30274,.79383,0,0,.77778],"57361":[.41951,.91951,0,0,.77778],"57366":[.25142,.75726,0,0,.77778],"57367":[.25142,.75726,0,0,.77778],"57368":[.25142,.75726,0,0,.77778],"57369":[.25142,.75726,0,0,.77778],"57370":[.13597,.63597,0,0,.77778],"57371":[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{"32":[0,0,0,0,.25],"65":[0,.68333,0,.19445,.79847],"66":[0,.68333,.03041,.13889,.65681],"67":[0,.68333,.05834,.13889,.52653],"68":[0,.68333,.02778,.08334,.77139],"69":[0,.68333,.08944,.11111,.52778],"70":[0,.68333,.09931,.11111,.71875],"71":[.09722,.68333,.0593,.11111,.59487],"72":[0,.68333,.00965,.11111,.84452],"73":[0,.68333,.07382,0,.54452],"74":[.09722,.68333,.18472,.16667,.67778],"75":[0,.68333,.01445,.05556,.76195],"76":[0,.68333,0,.13889,.68972],"77":[0,.68333,0,.13889,1.2009],"78":[0,.68333,.14736,.08334,.82049],"79":[0,.68333,.02778,.11111,.79611],"80":[0,.68333,.08222,.08334,.69556],"81":[.09722,.68333,0,.11111,.81667],"82":[0,.68333,0,.08334,.8475],"83":[0,.68333,.075,.13889,.60556],"84":[0,.68333,.25417,0,.54464],"85":[0,.68333,.09931,.08334,.62583],"86":[0,.68333,.08222,0,.61278],"87":[0,.68333,.08222,.08334,.98778],"88":[0,.68333,.14643,.13889,.7133],"89":[.09722,.68333,.08222,.08334,.66834],"90":[0,.68333,.07944,.13889,.72473],"160":[0,0,0,0,.25]},"Fraktur-Regular":{"32":[0,0,0,0,.25],"33":[0,.69141,0,0,.29574],"34":[0,.69141,0,0,.21471],"38":[0,.69141,0,0,.73786],"39":[0,.69141,0,0,.21201],"40":[.24982,.74947,0,0,.38865],"41":[.24982,.74947,0,0,.38865],"42":[0,.62119,0,0,.27764],"43":[.08319,.58283,0,0,.75623],"44":[0,.10803,0,0,.27764],"45":[.08319,.58283,0,0,.75623],"46":[0,.10803,0,0,.27764],"47":[.24982,.74947,0,0,.50181],"48":[0,.47534,0,0,.50181],"49":[0,.47534,0,0,.50181],"50":[0,.47534,0,0,.50181],"51":[.18906,.47534,0,0,.50181],"52":[.18906,.47534,0,0,.50181],"53":[.18906,.47534,0,0,.50181],"54":[0,.69141,0,0,.50181],"55":[.18906,.47534,0,0,.50181],"56":[0,.69141,0,0,.50181],"57":[.18906,.47534,0,0,.50181],"58":[0,.47534,0,0,.21606],"59":[.12604,.47534,0,0,.21606],"61":[-.13099,.36866,0,0,.75623],"63":[0,.69141,0,0,.36245],"65":[0,.69141,0,0,.7176],"66":[0,.69141,0,0,.88397],"67":[0,.69141,0,0,.61254],"68":[0,.69141,0,0,.83158],"69":[0,.69141,0,0,.66278],"70":[.12604,.69141,0,0,.61119],"71":[0,.69141,0,0,.78539],"72":[.06302,.69141,0,0,.7203],"73":[0,.69141,0,0,.55448],"74":[.12604,.69141,0,0,.55231],"75":[0,.69141,0,0,.66845],"76":[0,.69141,0,0,.66602],"77":[0,.69141,0,0,1.04953],"78":[0,.69141,0,0,.83212],"79":[0,.69141,0,0,.82699],"80":[.18906,.69141,0,0,.82753],"81":[.03781,.69141,0,0,.82699],"82":[0,.69141,0,0,.82807],"83":[0,.69141,0,0,.82861],"84":[0,.69141,0,0,.66899],"85":[0,.69141,0,0,.64576],"86":[0,.69141,0,0,.83131],"87":[0,.69141,0,0,1.04602],"88":[0,.69141,0,0,.71922],"89":[.18906,.69141,0,0,.83293],"90":[.12604,.69141,0,0,.60201],"91":[.24982,.74947,0,0,.27764],"93":[.24982,.74947,0,0,.27764],"94":[0,.69141,0,0,.49965],"97":[0,.47534,0,0,.50046],"98":[0,.69141,0,0,.51315],"99":[0,.47534,0,0,.38946],"100":[0,.62119,0,0,.49857],"101":[0,.47534,0,0,.40053],"102":[.18906,.69141,0,0,.32626],"103":[.18906,.47534,0,0,.5037],"104":[.18906,.69141,0,0,.52126],"105":[0,.69141,0,0,.27899],"106":[0,.69141,0,0,.28088],"107":[0,.69141,0,0,.38946],"108":[0,.69141,0,0,.27953],"109":[0,.47534,0,0,.76676],"110":[0,.47534,0,0,.52666],"111":[0,.47534,0,0,.48885],"112":[.18906,.52396,0,0,.50046],"113":[.18906,.47534,0,0,.48912],"114":[0,.47534,0,0,.38919],"115":[0,.47534,0,0,.44266],"116":[0,.62119,0,0,.33301],"117":[0,.47534,0,0,.5172],"118":[0,.52396,0,0,.5118],"119":[0,.52396,0,0,.77351],"120":[.18906,.47534,0,0,.38865],"121":[.18906,.47534,0,0,.49884],"122":[.18906,.47534,0,0,.39054],"160":[0,0,0,0,.25],"8216":[0,.69141,0,0,.21471],"8217":[0,.69141,0,0,.21471],"58112":[0,.62119,0,0,.49749],"58113":[0,.62119,0,0,.4983],"58114":[.18906,.69141,0,0,.33328],"58115":[.18906,.69141,0,0,.32923],"58116":[.18906,.47534,0,0,.50343],"58117":[0,.69141,0,0,.33301],"58118":[0,.62119,0,0,.33409],"58119":[0,.47534,0,0,.50073]},"Main-Bold":{"32":[0,0,0,0,.25],"33":[0,.69444,0,0,.35],"34":[0,.69444,0,0,.60278],"35":[.19444,.69444,0,0,.95833],"36":[.05556,.75,0,0,.575],"37":[.05556,.75,0,0,.95833],"38":[0,.69444,0,0,.89444],"39":[0,.69444,0,0,.31944],"40":[.25,.75,0,0,.44722],"41":[.25,.75,0,0,.44722],"42":[0,.75,0,0,.575],"43":[.13333,.63333,0,0,.89444],"44":[.19444,.15556,0,0,.31944],"45":[0,.44444,0,0,.38333],"46":[0,.15556,0,0,.31944],"47":[.25,.75,0,0,.575],"48":[0,.64444,0,0,.575],"49":[0,.64444,0,0,.575],"50":[0,.64444,0,0,.575],"51":[0,.64444,0,0,.575],"52":[0,.64444,0,0,.575],"53":[0,.64444,0,0,.575],"54":[0,.64444,0,0,.575],"55":[0,.64444,0,0,.575],"56":[0,.64444,0,0,.575],"57":[0,.64444,0,0,.575],"58":[0,.44444,0,0,.31944],"59":[.19444,.44444,0,0,.31944],"60":[.08556,.58556,0,0,.89444],"61":[-.10889,.39111,0,0,.89444],"62":[.08556,.58556,0,0,.89444],"63":[0,.69444,0,0,.54305],"64":[0,.69444,0,0,.89444],"65":[0,.68611,0,0,.86944],"66":[0,.68611,0,0,.81805],"67":[0,.68611,0,0,.83055],"68":[0,.68611,0,0,.88194],"69":[0,.68611,0,0,.75555],"70":[0,.68611,0,0,.72361],"71":[0,.68611,0,0,.90416],"72":[0,.68611,0,0,.9],"73":[0,.68611,0,0,.43611],"74":[0,.68611,0,0,.59444],"75":[0,.68611,0,0,.90138],"76":[0,.68611,0,0,.69166],"77":[0,.68611,0,0,1.09166],"78":[0,.68611,0,0,.9],"79":[0,.68611,0,0,.86388],"80":[0,.68611,0,0,.78611],"81":[.19444,.68611,0,0,.86388],"82":[0,.68611,0,0,.8625],"83":[0,.68611,0,0,.63889],"84":[0,.68611,0,0,.8],"85":[0,.68611,0,0,.88472],"86":[0,.68611,.01597,0,.86944],"87":[0,.68611,.01597,0,1.18888],"88":[0,.68611,0,0,.86944],"89":[0,.68611,.02875,0,.86944],"90":[0,.68611,0,0,.70277],"91":[.25,.75,0,0,.31944],"92":[.25,.75,0,0,.575],"93":[.25,.75,0,0,.31944],"94":[0,.69444,0,0,.575],"95":[.31,.13444,.03194,0,.575],"97":[0,.44444,0,0,.55902],"98":[0,.69444,0,0,.63889],"99":[0,.44444,0,0,.51111],"100":[0,.69444,0,0,.63889],"101":[0,.44444,0,0,.52708],"102":[0,.69444,.10903,0,.35139],"103":[.19444,.44444,.01597,0,.575],"104":[0,.69444,0,0,.63889],"105":[0,.69444,0,0,.31944],"106":[.19444,.69444,0,0,.35139],"107":[0,.69444,0,0,.60694],"108":[0,.69444,0,0,.31944],"109":[0,.44444,0,0,.95833],"110":[0,.44444,0,0,.63889],"111":[0,.44444,0,0,.575],"112":[.19444,.44444,0,0,.63889],"113":[.19444,.44444,0,0,.60694],"114":[0,.44444,0,0,.47361],"115":[0,.44444,0,0,.45361],"116":[0,.63492,0,0,.44722],"117":[0,.44444,0,0,.63889],"118":[0,.44444,.01597,0,.60694],"119":[0,.44444,.01597,0,.83055],"120":[0,.44444,0,0,.60694],"121":[.19444,.44444,.01597,0,.60694],"122":[0,.44444,0,0,.51111],"123":[.25,.75,0,0,.575],"124":[.25,.75,0,0,.31944],"125":[.25,.75,0,0,.575],"126":[.35,.34444,0,0,.575],"160":[0,0,0,0,.25],"163":[0,.69444,0,0,.86853],"168":[0,.69444,0,0,.575],"172":[0,.44444,0,0,.76666],"176":[0,.69444,0,0,.86944],"177":[.13333,.63333,0,0,.89444],"184":[.17014,0,0,0,.51111],"198":[0,.68611,0,0,1.04166],"215":[.13333,.63333,0,0,.89444],"216":[.04861,.73472,0,0,.89444],"223":[0,.69444,0,0,.59722],"230":[0,.44444,0,0,.83055],"247":[.13333,.63333,0,0,.89444],"248":[.09722,.54167,0,0,.575],"305":[0,.44444,0,0,.31944],"338":[0,.68611,0,0,1.16944],"339":[0,.44444,0,0,.89444],"567":[.19444,.44444,0,0,.35139],"710":[0,.69444,0,0,.575],"711":[0,.63194,0,0,.575],"713":[0,.59611,0,0,.575],"714":[0,.69444,0,0,.575],"715":[0,.69444,0,0,.575],"728":[0,.69444,0,0,.575],"729":[0,.69444,0,0,.31944],"730":[0,.69444,0,0,.86944],"732":[0,.69444,0,0,.575],"733":[0,.69444,0,0,.575],"915":[0,.68611,0,0,.69166],"916":[0,.68611,0,0,.95833],"920":[0,.68611,0,0,.89444],"923":[0,.68611,0,0,.80555],"926":[0,.68611,0,0,.76666],"928":[0,.68611,0,0,.9],"931":[0,.68611,0,0,.83055],"933":[0,.68611,0,0,.89444],"934":[0,.68611,0,0,.83055],"936":[0,.68611,0,0,.89444],"937":[0,.68611,0,0,.83055],"8211":[0,.44444,.03194,0,.575],"8212":[0,.44444,.03194,0,1.14999],"8216":[0,.69444,0,0,.31944],"8217":[0,.69444,0,0,.31944],"8220":[0,.69444,0,0,.60278],"8221":[0,.69444,0,0,.60278],"8224":[.19444,.69444,0,0,.51111],"8225":[.19444,.69444,0,0,.51111],"8242":[0,.55556,0,0,.34444],"8407":[0,.72444,.15486,0,.575],"8463":[0,.69444,0,0,.66759],"8465":[0,.69444,0,0,.83055],"8467":[0,.69444,0,0,.47361],"8472":[.19444,.44444,0,0,.74027],"8476":[0,.69444,0,0,.83055],"8501":[0,.69444,0,0,.70277],"8592":[-.10889,.39111,0,0,1.14999],"8593":[.19444,.69444,0,0,.575],"8594":[-.10889,.39111,0,0,1.14999],"8595":[.19444,.69444,0,0,.575],"8596":[-.10889,.39111,0,0,1.14999],"8597":[.25,.75,0,0,.575],"8598":[.19444,.69444,0,0,1.14999],"8599":[.19444,.69444,0,0,1.14999],"8600":[.19444,.69444,0,0,1.14999],"8601":[.19444,.69444,0,0,1.14999],"8636":[-.10889,.39111,0,0,1.14999],"8637":[-.10889,.39111,0,0,1.14999],"8640":[-.10889,.39111,0,0,1.14999],"8641":[-.10889,.39111,0,0,1.14999],"8656":[-.10889,.39111,0,0,1.14999],"8657":[.19444,.69444,0,0,.70277],"8658":[-.10889,.39111,0,0,1.14999],"8659":[.19444,.69444,0,0,.70277],"8660":[-.10889,.39111,0,0,1.14999],"8661":[.25,.75,0,0,.70277],"8704":[0,.69444,0,0,.63889],"8706":[0,.69444,.06389,0,.62847],"8707":[0,.69444,0,0,.63889],"8709":[.05556,.75,0,0,.575],"8711":[0,.68611,0,0,.95833],"8712":[.08556,.58556,0,0,.76666],"8715":[.08556,.58556,0,0,.76666],"8722":[.13333,.63333,0,0,.89444],"8723":[.13333,.63333,0,0,.89444],"8725":[.25,.75,0,0,.575],"8726":[.25,.75,0,0,.575],"8727":[-.02778,.47222,0,0,.575],"8728":[-.02639,.47361,0,0,.575],"8729":[-.02639,.47361,0,0,.575],"8730":[.18,.82,0,0,.95833],"8733":[0,.44444,0,0,.89444],"8734":[0,.44444,0,0,1.14999],"8736":[0,.69224,0,0,.72222],"8739":[.25,.75,0,0,.31944],"8741":[.25,.75,0,0,.575],"8743":[0,.55556,0,0,.76666],"8744":[0,.55556,0,0,.76666],"8745":[0,.55556,0,0,.76666],"8746":[0,.55556,0,0,.76666],"8747":[.19444,.69444,.12778,0,.56875],"8764":[-.10889,.39111,0,0,.89444],"8768":[.19444,.69444,0,0,.31944],"8771":[.00222,.50222,0,0,.89444],"8773":[.027,.638,0,0,.894],"8776":[.02444,.52444,0,0,.89444],"8781":[.00222,.50222,0,0,.89444],"8801":[.00222,.50222,0,0,.89444],"8804":[.19667,.69667,0,0,.89444],"8805":[.19667,.69667,0,0,.89444],"8810":[.08556,.58556,0,0,1.14999],"8811":[.08556,.58556,0,0,1.14999],"8826":[.08556,.58556,0,0,.89444],"8827":[.08556,.58556,0,0,.89444],"8834":[.08556,.58556,0,0,.89444],"8835":[.08556,.58556,0,0,.89444],"8838":[.19667,.69667,0,0,.89444],"8839":[.19667,.69667,0,0,.89444],"8846":[0,.55556,0,0,.76666],"8849":[.19667,.69667,0,0,.89444],"8850":[.19667,.69667,0,0,.89444],"8851":[0,.55556,0,0,.76666],"8852":[0,.55556,0,0,.76666],"8853":[.13333,.63333,0,0,.89444],"8854":[.13333,.63333,0,0,.89444],"8855":[.13333,.63333,0,0,.89444],"8856":[.13333,.63333,0,0,.89444],"8857":[.13333,.63333,0,0,.89444],"8866":[0,.69444,0,0,.70277],"8867":[0,.69444,0,0,.70277],"8868":[0,.69444,0,0,.89444],"8869":[0,.69444,0,0,.89444],"8900":[-.02639,.47361,0,0,.575],"8901":[-.02639,.47361,0,0,.31944],"8902":[-.02778,.47222,0,0,.575],"8968":[.25,.75,0,0,.51111],"8969":[.25,.75,0,0,.51111],"8970":[.25,.75,0,0,.51111],"8971":[.25,.75,0,0,.51111],"8994":[-.13889,.36111,0,0,1.14999],"8995":[-.13889,.36111,0,0,1.14999],"9651":[.19444,.69444,0,0,1.02222],"9657":[-.02778,.47222,0,0,.575],"9661":[.19444,.69444,0,0,1.02222],"9667":[-.02778,.47222,0,0,.575],"9711":[.19444,.69444,0,0,1.14999],"9824":[.12963,.69444,0,0,.89444],"9825":[.12963,.69444,0,0,.89444],"9826":[.12963,.69444,0,0,.89444],"9827":[.12963,.69444,0,0,.89444],"9837":[0,.75,0,0,.44722],"9838":[.19444,.69444,0,0,.44722],"9839":[.19444,.69444,0,0,.44722],"10216":[.25,.75,0,0,.44722],"10217":[.25,.75,0,0,.44722],"10815":[0,.68611,0,0,.9],"10927":[.19667,.69667,0,0,.89444],"10928":[.19667,.69667,0,0,.89444],"57376":[.19444,.69444,0,0,0]},"Main-BoldItalic":{"32":[0,0,0,0,.25],"33":[0,.69444,.11417,0,.38611],"34":[0,.69444,.07939,0,.62055],"35":[.19444,.69444,.06833,0,.94444],"37":[.05556,.75,.12861,0,.94444],"38":[0,.69444,.08528,0,.88555],"39":[0,.69444,.12945,0,.35555],"40":[.25,.75,.15806,0,.47333],"41":[.25,.75,.03306,0,.47333],"42":[0,.75,.14333,0,.59111],"43":[.10333,.60333,.03306,0,.88555],"44":[.19444,.14722,0,0,.35555],"45":[0,.44444,.02611,0,.41444],"46":[0,.14722,0,0,.35555],"47":[.25,.75,.15806,0,.59111],"48":[0,.64444,.13167,0,.59111],"49":[0,.64444,.13167,0,.59111],"50":[0,.64444,.13167,0,.59111],"51":[0,.64444,.13167,0,.59111],"52":[.19444,.64444,.13167,0,.59111],"53":[0,.64444,.13167,0,.59111],"54":[0,.64444,.13167,0,.59111],"55":[.19444,.64444,.13167,0,.59111],"56":[0,.64444,.13167,0,.59111],"57":[0,.64444,.13167,0,.59111],"58":[0,.44444,.06695,0,.35555],"59":[.19444,.44444,.06695,0,.35555],"61":[-.10889,.39111,.06833,0,.88555],"63":[0,.69444,.11472,0,.59111],"64":[0,.69444,.09208,0,.88555],"65":[0,.68611,0,0,.86555],"66":[0,.68611,.0992,0,.81666],"67":[0,.68611,.14208,0,.82666],"68":[0,.68611,.09062,0,.87555],"69":[0,.68611,.11431,0,.75666],"70":[0,.68611,.12903,0,.72722],"71":[0,.68611,.07347,0,.89527],"72":[0,.68611,.17208,0,.8961],"73":[0,.68611,.15681,0,.47166],"74":[0,.68611,.145,0,.61055],"75":[0,.68611,.14208,0,.89499],"76":[0,.68611,0,0,.69777],"77":[0,.68611,.17208,0,1.07277],"78":[0,.68611,.17208,0,.8961],"79":[0,.68611,.09062,0,.85499],"80":[0,.68611,.0992,0,.78721],"81":[.19444,.68611,.09062,0,.85499],"82":[0,.68611,.02559,0,.85944],"83":[0,.68611,.11264,0,.64999],"84":[0,.68611,.12903,0,.7961],"85":[0,.68611,.17208,0,.88083],"86":[0,.68611,.18625,0,.86555],"87":[0,.68611,.18625,0,1.15999],"88":[0,.68611,.15681,0,.86555],"89":[0,.68611,.19803,0,.86555],"90":[0,.68611,.14208,0,.70888],"91":[.25,.75,.1875,0,.35611],"93":[.25,.75,.09972,0,.35611],"94":[0,.69444,.06709,0,.59111],"95":[.31,.13444,.09811,0,.59111],"97":[0,.44444,.09426,0,.59111],"98":[0,.69444,.07861,0,.53222],"99":[0,.44444,.05222,0,.53222],"100":[0,.69444,.10861,0,.59111],"101":[0,.44444,.085,0,.53222],"102":[.19444,.69444,.21778,0,.4],"103":[.19444,.44444,.105,0,.53222],"104":[0,.69444,.09426,0,.59111],"105":[0,.69326,.11387,0,.35555],"106":[.19444,.69326,.1672,0,.35555],"107":[0,.69444,.11111,0,.53222],"108":[0,.69444,.10861,0,.29666],"109":[0,.44444,.09426,0,.94444],"110":[0,.44444,.09426,0,.64999],"111":[0,.44444,.07861,0,.59111],"112":[.19444,.44444,.07861,0,.59111],"113":[.19444,.44444,.105,0,.53222],"114":[0,.44444,.11111,0,.50167],"115":[0,.44444,.08167,0,.48694],"116":[0,.63492,.09639,0,.385],"117":[0,.44444,.09426,0,.62055],"118":[0,.44444,.11111,0,.53222],"119":[0,.44444,.11111,0,.76777],"120":[0,.44444,.12583,0,.56055],"121":[.19444,.44444,.105,0,.56166],"122":[0,.44444,.13889,0,.49055],"126":[.35,.34444,.11472,0,.59111],"160":[0,0,0,0,.25],"168":[0,.69444,.11473,0,.59111],"176":[0,.69444,0,0,.94888],"184":[.17014,0,0,0,.53222],"198":[0,.68611,.11431,0,1.02277],"216":[.04861,.73472,.09062,0,.88555],"223":[.19444,.69444,.09736,0,.665],"230":[0,.44444,.085,0,.82666],"248":[.09722,.54167,.09458,0,.59111],"305":[0,.44444,.09426,0,.35555],"338":[0,.68611,.11431,0,1.14054],"339":[0,.44444,.085,0,.82666],"567":[.19444,.44444,.04611,0,.385],"710":[0,.69444,.06709,0,.59111],"711":[0,.63194,.08271,0,.59111],"713":[0,.59444,.10444,0,.59111],"714":[0,.69444,.08528,0,.59111],"715":[0,.69444,0,0,.59111],"728":[0,.69444,.10333,0,.59111],"729":[0,.69444,.12945,0,.35555],"730":[0,.69444,0,0,.94888],"732":[0,.69444,.11472,0,.59111],"733":[0,.69444,.11472,0,.59111],"915":[0,.68611,.12903,0,.69777],"916":[0,.68611,0,0,.94444],"920":[0,.68611,.09062,0,.88555],"923":[0,.68611,0,0,.80666],"926":[0,.68611,.15092,0,.76777],"928":[0,.68611,.17208,0,.8961],"931":[0,.68611,.11431,0,.82666],"933":[0,.68611,.10778,0,.88555],"934":[0,.68611,.05632,0,.82666],"936":[0,.68611,.10778,0,.88555],"937":[0,.68611,.0992,0,.82666],"8211":[0,.44444,.09811,0,.59111],"8212":[0,.44444,.09811,0,1.18221],"8216":[0,.69444,.12945,0,.35555],"8217":[0,.69444,.12945,0,.35555],"8220":[0,.69444,.16772,0,.62055],"8221":[0,.69444,.07939,0,.62055]},"Main-Italic":{"32":[0,0,0,0,.25],"33":[0,.69444,.12417,0,.30667],"34":[0,.69444,.06961,0,.51444],"35":[.19444,.69444,.06616,0,.81777],"37":[.05556,.75,.13639,0,.81777],"38":[0,.69444,.09694,0,.76666],"39":[0,.69444,.12417,0,.30667],"40":[.25,.75,.16194,0,.40889],"41":[.25,.75,.03694,0,.40889],"42":[0,.75,.14917,0,.51111],"43":[.05667,.56167,.03694,0,.76666],"44":[.19444,.10556,0,0,.30667],"45":[0,.43056,.02826,0,.35778],"46":[0,.10556,0,0,.30667],"47":[.25,.75,.16194,0,.51111],"48":[0,.64444,.13556,0,.51111],"49":[0,.64444,.13556,0,.51111],"50":[0,.64444,.13556,0,.51111],"51":[0,.64444,.13556,0,.51111],"52":[.19444,.64444,.13556,0,.51111],"53":[0,.64444,.13556,0,.51111],"54":[0,.64444,.13556,0,.51111],"55":[.19444,.64444,.13556,0,.51111],"56":[0,.64444,.13556,0,.51111],"57":[0,.64444,.13556,0,.51111],"58":[0,.43056,.0582,0,.30667],"59":[.19444,.43056,.0582,0,.30667],"61":[-.13313,.36687,.06616,0,.76666],"63":[0,.69444,.1225,0,.51111],"64":[0,.69444,.09597,0,.76666],"65":[0,.68333,0,0,.74333],"66":[0,.68333,.10257,0,.70389],"67":[0,.68333,.14528,0,.71555],"68":[0,.68333,.09403,0,.755],"69":[0,.68333,.12028,0,.67833],"70":[0,.68333,.13305,0,.65277],"71":[0,.68333,.08722,0,.77361],"72":[0,.68333,.16389,0,.74333],"73":[0,.68333,.15806,0,.38555],"74":[0,.68333,.14028,0,.525],"75":[0,.68333,.14528,0,.76888],"76":[0,.68333,0,0,.62722],"77":[0,.68333,.16389,0,.89666],"78":[0,.68333,.16389,0,.74333],"79":[0,.68333,.09403,0,.76666],"80":[0,.68333,.10257,0,.67833],"81":[.19444,.68333,.09403,0,.76666],"82":[0,.68333,.03868,0,.72944],"83":[0,.68333,.11972,0,.56222],"84":[0,.68333,.13305,0,.71555],"85":[0,.68333,.16389,0,.74333],"86":[0,.68333,.18361,0,.74333],"87":[0,.68333,.18361,0,.99888],"88":[0,.68333,.15806,0,.74333],"89":[0,.68333,.19383,0,.74333],"90":[0,.68333,.14528,0,.61333],"91":[.25,.75,.1875,0,.30667],"93":[.25,.75,.10528,0,.30667],"94":[0,.69444,.06646,0,.51111],"95":[.31,.12056,.09208,0,.51111],"97":[0,.43056,.07671,0,.51111],"98":[0,.69444,.06312,0,.46],"99":[0,.43056,.05653,0,.46],"100":[0,.69444,.10333,0,.51111],"101":[0,.43056,.07514,0,.46],"102":[.19444,.69444,.21194,0,.30667],"103":[.19444,.43056,.08847,0,.46],"104":[0,.69444,.07671,0,.51111],"105":[0,.65536,.1019,0,.30667],"106":[.19444,.65536,.14467,0,.30667],"107":[0,.69444,.10764,0,.46],"108":[0,.69444,.10333,0,.25555],"109":[0,.43056,.07671,0,.81777],"110":[0,.43056,.07671,0,.56222],"111":[0,.43056,.06312,0,.51111],"112":[.19444,.43056,.06312,0,.51111],"113":[.19444,.43056,.08847,0,.46],"114":[0,.43056,.10764,0,.42166],"115":[0,.43056,.08208,0,.40889],"116":[0,.61508,.09486,0,.33222],"117":[0,.43056,.07671,0,.53666],"118":[0,.43056,.10764,0,.46],"119":[0,.43056,.10764,0,.66444],"120":[0,.43056,.12042,0,.46389],"121":[.19444,.43056,.08847,0,.48555],"122":[0,.43056,.12292,0,.40889],"126":[.35,.31786,.11585,0,.51111],"160":[0,0,0,0,.25],"168":[0,.66786,.10474,0,.51111],"176":[0,.69444,0,0,.83129],"184":[.17014,0,0,0,.46],"198":[0,.68333,.12028,0,.88277],"216":[.04861,.73194,.09403,0,.76666],"223":[.19444,.69444,.10514,0,.53666],"230":[0,.43056,.07514,0,.71555],"248":[.09722,.52778,.09194,0,.51111],"338":[0,.68333,.12028,0,.98499],"339":[0,.43056,.07514,0,.71555],"710":[0,.69444,.06646,0,.51111],"711":[0,.62847,.08295,0,.51111],"713":[0,.56167,.10333,0,.51111],"714":[0,.69444,.09694,0,.51111],"715":[0,.69444,0,0,.51111],"728":[0,.69444,.10806,0,.51111],"729":[0,.66786,.11752,0,.30667],"730":[0,.69444,0,0,.83129],"732":[0,.66786,.11585,0,.51111],"733":[0,.69444,.1225,0,.51111],"915":[0,.68333,.13305,0,.62722],"916":[0,.68333,0,0,.81777],"920":[0,.68333,.09403,0,.76666],"923":[0,.68333,0,0,.69222],"926":[0,.68333,.15294,0,.66444],"928":[0,.68333,.16389,0,.74333],"931":[0,.68333,.12028,0,.71555],"933":[0,.68333,.11111,0,.76666],"934":[0,.68333,.05986,0,.71555],"936":[0,.68333,.11111,0,.76666],"937":[0,.68333,.10257,0,.71555],"8211":[0,.43056,.09208,0,.51111],"8212":[0,.43056,.09208,0,1.02222],"8216":[0,.69444,.12417,0,.30667],"8217":[0,.69444,.12417,0,.30667],"8220":[0,.69444,.1685,0,.51444],"8221":[0,.69444,.06961,0,.51444],"8463":[0,.68889,0,0,.54028]},"Main-Regular":{"32":[0,0,0,0,.25],"33":[0,.69444,0,0,.27778],"34":[0,.69444,0,0,.5],"35":[.19444,.69444,0,0,.83334],"36":[.05556,.75,0,0,.5],"37":[.05556,.75,0,0,.83334],"38":[0,.69444,0,0,.77778],"39":[0,.69444,0,0,.27778],"40":[.25,.75,0,0,.38889],"41":[.25,.75,0,0,.38889],"42":[0,.75,0,0,.5],"43":[.08333,.58333,0,0,.77778],"44":[.19444,.10556,0,0,.27778],"45":[0,.43056,0,0,.33333],"46":[0,.10556,0,0,.27778],"47":[.25,.75,0,0,.5],"48":[0,.64444,0,0,.5],"49":[0,.64444,0,0,.5],"50":[0,.64444,0,0,.5],"51":[0,.64444,0,0,.5],"52":[0,.64444,0,0,.5],"53":[0,.64444,0,0,.5],"54":[0,.64444,0,0,.5],"55":[0,.64444,0,0,.5],"56":[0,.64444,0,0,.5],"57":[0,.64444,0,0,.5],"58":[0,.43056,0,0,.27778],"59":[.19444,.43056,0,0,.27778],"60":[.0391,.5391,0,0,.77778],"61":[-.13313,.36687,0,0,.77778],"62":[.0391,.5391,0,0,.77778],"63":[0,.69444,0,0,.47222],"64":[0,.69444,0,0,.77778],"65":[0,.68333,0,0,.75],"66":[0,.68333,0,0,.70834],"67":[0,.68333,0,0,.72222],"68":[0,.68333,0,0,.76389],"69":[0,.68333,0,0,.68056],"70":[0,.68333,0,0,.65278],"71":[0,.68333,0,0,.78472],"72":[0,.68333,0,0,.75],"73":[0,.68333,0,0,.36111],"74":[0,.68333,0,0,.51389],"75":[0,.68333,0,0,.77778],"76":[0,.68333,0,0,.625],"77":[0,.68333,0,0,.91667],"78":[0,.68333,0,0,.75],"79":[0,.68333,0,0,.77778],"80":[0,.68333,0,0,.68056],"81":[.19444,.68333,0,0,.77778],"82":[0,.68333,0,0,.73611],"83":[0,.68333,0,0,.55556],"84":[0,.68333,0,0,.72222],"85":[0,.68333,0,0,.75],"86":[0,.68333,.01389,0,.75],"87":[0,.68333,.01389,0,1.02778],"88":[0,.68333,0,0,.75],"89":[0,.68333,.025,0,.75],"90":[0,.68333,0,0,.61111],"91":[.25,.75,0,0,.27778],"92":[.25,.75,0,0,.5],"93":[.25,.75,0,0,.27778],"94":[0,.69444,0,0,.5],"95":[.31,.12056,.02778,0,.5],"97":[0,.43056,0,0,.5],"98":[0,.69444,0,0,.55556],"99":[0,.43056,0,0,.44445],"100":[0,.69444,0,0,.55556],"101":[0,.43056,0,0,.44445],"102":[0,.69444,.07778,0,.30556],"103":[.19444,.43056,.01389,0,.5],"104":[0,.69444,0,0,.55556],"105":[0,.66786,0,0,.27778],"106":[.19444,.66786,0,0,.30556],"107":[0,.69444,0,0,.52778],"108":[0,.69444,0,0,.27778],"109":[0,.43056,0,0,.83334],"110":[0,.43056,0,0,.55556],"111":[0,.43056,0,0,.5],"112":[.19444,.43056,0,0,.55556],"113":[.19444,.43056,0,0,.52778],"114":[0,.43056,0,0,.39167],"115":[0,.43056,0,0,.39445],"116":[0,.61508,0,0,.38889],"117":[0,.43056,0,0,.55556],"118":[0,.43056,.01389,0,.52778],"119":[0,.43056,.01389,0,.72222],"120":[0,.43056,0,0,.52778],"121":[.19444,.43056,.01389,0,.52778],"122":[0,.43056,0,0,.44445],"123":[.25,.75,0,0,.5],"124":[.25,.75,0,0,.27778],"125":[.25,.75,0,0,.5],"126":[.35,.31786,0,0,.5],"160":[0,0,0,0,.25],"163":[0,.69444,0,0,.76909],"167":[.19444,.69444,0,0,.44445],"168":[0,.66786,0,0,.5],"172":[0,.43056,0,0,.66667],"176":[0,.69444,0,0,.75],"177":[.08333,.58333,0,0,.77778],"182":[.19444,.69444,0,0,.61111],"184":[.17014,0,0,0,.44445],"198":[0,.68333,0,0,.90278],"215":[.08333,.58333,0,0,.77778],"216":[.04861,.73194,0,0,.77778],"223":[0,.69444,0,0,.5],"230":[0,.43056,0,0,.72222],"247":[.08333,.58333,0,0,.77778],"248":[.09722,.52778,0,0,.5],"305":[0,.43056,0,0,.27778],"338":[0,.68333,0,0,1.01389],"339":[0,.43056,0,0,.77778],"567":[.19444,.43056,0,0,.30556],"710":[0,.69444,0,0,.5],"711":[0,.62847,0,0,.5],"713":[0,.56778,0,0,.5],"714":[0,.69444,0,0,.5],"715":[0,.69444,0,0,.5],"728":[0,.69444,0,0,.5],"729":[0,.66786,0,0,.27778],"730":[0,.69444,0,0,.75],"732":[0,.66786,0,0,.5],"733":[0,.69444,0,0,.5],"915":[0,.68333,0,0,.625],"916":[0,.68333,0,0,.83334],"920":[0,.68333,0,0,.77778],"923":[0,.68333,0,0,.69445],"926":[0,.68333,0,0,.66667],"928":[0,.68333,0,0,.75],"931":[0,.68333,0,0,.72222],"933":[0,.68333,0,0,.77778],"934":[0,.68333,0,0,.72222],"936":[0,.68333,0,0,.77778],"937":[0,.68333,0,0,.72222],"8211":[0,.43056,.02778,0,.5],"8212":[0,.43056,.02778,0,1],"8216":[0,.69444,0,0,.27778],"8217":[0,.69444,0,0,.27778],"8220":[0,.69444,0,0,.5],"8221":[0,.69444,0,0,.5],"8224":[.19444,.69444,0,0,.44445],"8225":[.19444,.69444,0,0,.44445],"8230":[0,.123,0,0,1.172],"8242":[0,.55556,0,0,.275],"8407":[0,.71444,.15382,0,.5],"8463":[0,.68889,0,0,.54028],"8465":[0,.69444,0,0,.72222],"8467":[0,.69444,0,.11111,.41667],"8472":[.19444,.43056,0,.11111,.63646],"8476":[0,.69444,0,0,.72222],"8501":[0,.69444,0,0,.61111],"8592":[-.13313,.36687,0,0,1],"8593":[.19444,.69444,0,0,.5],"8594":[-.13313,.36687,0,0,1],"8595":[.19444,.69444,0,0,.5],"8596":[-.13313,.36687,0,0,1],"8597":[.25,.75,0,0,.5],"8598":[.19444,.69444,0,0,1],"8599":[.19444,.69444,0,0,1],"8600":[.19444,.69444,0,0,1],"8601":[.19444,.69444,0,0,1],"8614":[.011,.511,0,0,1],"8617":[.011,.511,0,0,1.126],"8618":[.011,.511,0,0,1.126],"8636":[-.13313,.36687,0,0,1],"8637":[-.13313,.36687,0,0,1],"8640":[-.13313,.36687,0,0,1],"8641":[-.13313,.36687,0,0,1],"8652":[.011,.671,0,0,1],"8656":[-.13313,.36687,0,0,1],"8657":[.19444,.69444,0,0,.61111],"8658":[-.13313,.36687,0,0,1],"8659":[.19444,.69444,0,0,.61111],"8660":[-.13313,.36687,0,0,1],"8661":[.25,.75,0,0,.61111],"8704":[0,.69444,0,0,.55556],"8706":[0,.69444,.05556,.08334,.5309],"8707":[0,.69444,0,0,.55556],"8709":[.05556,.75,0,0,.5],"8711":[0,.68333,0,0,.83334],"8712":[.0391,.5391,0,0,.66667],"8715":[.0391,.5391,0,0,.66667],"8722":[.08333,.58333,0,0,.77778],"8723":[.08333,.58333,0,0,.77778],"8725":[.25,.75,0,0,.5],"8726":[.25,.75,0,0,.5],"8727":[-.03472,.46528,0,0,.5],"8728":[-.05555,.44445,0,0,.5],"8729":[-.05555,.44445,0,0,.5],"8730":[.2,.8,0,0,.83334],"8733":[0,.43056,0,0,.77778],"8734":[0,.43056,0,0,1],"8736":[0,.69224,0,0,.72222],"8739":[.25,.75,0,0,.27778],"8741":[.25,.75,0,0,.5],"8743":[0,.55556,0,0,.66667],"8744":[0,.55556,0,0,.66667],"8745":[0,.55556,0,0,.66667],"8746":[0,.55556,0,0,.66667],"8747":[.19444,.69444,.11111,0,.41667],"8764":[-.13313,.36687,0,0,.77778],"8768":[.19444,.69444,0,0,.27778],"8771":[-.03625,.46375,0,0,.77778],"8773":[-.022,.589,0,0,.778],"8776":[-.01688,.48312,0,0,.77778],"8781":[-.03625,.46375,0,0,.77778],"8784":[-.133,.673,0,0,.778],"8801":[-.03625,.46375,0,0,.77778],"8804":[.13597,.63597,0,0,.77778],"8805":[.13597,.63597,0,0,.77778],"8810":[.0391,.5391,0,0,1],"8811":[.0391,.5391,0,0,1],"8826":[.0391,.5391,0,0,.77778],"8827":[.0391,.5391,0,0,.77778],"8834":[.0391,.5391,0,0,.77778],"8835":[.0391,.5391,0,0,.77778],"8838":[.13597,.63597,0,0,.77778],"8839":[.13597,.63597,0,0,.77778],"8846":[0,.55556,0,0,.66667],"8849":[.13597,.63597,0,0,.77778],"8850":[.13597,.63597,0,0,.77778],"8851":[0,.55556,0,0,.66667],"8852":[0,.55556,0,0,.66667],"8853":[.08333,.58333,0,0,.77778],"8854":[.08333,.58333,0,0,.77778],"8855":[.08333,.58333,0,0,.77778],"8856":[.08333,.58333,0,0,.77778],"8857":[.08333,.58333,0,0,.77778],"8866":[0,.69444,0,0,.61111],"8867":[0,.69444,0,0,.61111],"8868":[0,.69444,0,0,.77778],"8869":[0,.69444,0,0,.77778],"8872":[.249,.75,0,0,.867],"8900":[-.05555,.44445,0,0,.5],"8901":[-.05555,.44445,0,0,.27778],"8902":[-.03472,.46528,0,0,.5],"8904":[.005,.505,0,0,.9],"8942":[.03,.903,0,0,.278],"8943":[-.19,.313,0,0,1.172],"8945":[-.1,.823,0,0,1.282],"8968":[.25,.75,0,0,.44445],"8969":[.25,.75,0,0,.44445],"8970":[.25,.75,0,0,.44445],"8971":[.25,.75,0,0,.44445],"8994":[-.14236,.35764,0,0,1],"8995":[-.14236,.35764,0,0,1],"9136":[.244,.744,0,0,.412],"9137":[.244,.745,0,0,.412],"9651":[.19444,.69444,0,0,.88889],"9657":[-.03472,.46528,0,0,.5],"9661":[.19444,.69444,0,0,.88889],"9667":[-.03472,.46528,0,0,.5],"9711":[.19444,.69444,0,0,1],"9824":[.12963,.69444,0,0,.77778],"9825":[.12963,.69444,0,0,.77778],"9826":[.12963,.69444,0,0,.77778],"9827":[.12963,.69444,0,0,.77778],"9837":[0,.75,0,0,.38889],"9838":[.19444,.69444,0,0,.38889],"9839":[.19444,.69444,0,0,.38889],"10216":[.25,.75,0,0,.38889],"10217":[.25,.75,0,0,.38889],"10222":[.244,.744,0,0,.412],"10223":[.244,.745,0,0,.412],"10229":[.011,.511,0,0,1.609],"10230":[.011,.511,0,0,1.638],"10231":[.011,.511,0,0,1.859],"10232":[.024,.525,0,0,1.609],"10233":[.024,.525,0,0,1.638],"10234":[.024,.525,0,0,1.858],"10236":[.011,.511,0,0,1.638],"10815":[0,.68333,0,0,.75],"10927":[.13597,.63597,0,0,.77778],"10928":[.13597,.63597,0,0,.77778],"57376":[.19444,.69444,0,0,0]},"Math-BoldItalic":{"32":[0,0,0,0,.25],"48":[0,.44444,0,0,.575],"49":[0,.44444,0,0,.575],"50":[0,.44444,0,0,.575],"51":[.19444,.44444,0,0,.575],"52":[.19444,.44444,0,0,.575],"53":[.19444,.44444,0,0,.575],"54":[0,.64444,0,0,.575],"55":[.19444,.44444,0,0,.575],"56":[0,.64444,0,0,.575],"57":[.19444,.44444,0,0,.575],"65":[0,.68611,0,0,.86944],"66":[0,.68611,.04835,0,.8664],"67":[0,.68611,.06979,0,.81694],"68":[0,.68611,.03194,0,.93812],"69":[0,.68611,.05451,0,.81007],"70":[0,.68611,.15972,0,.68889],"71":[0,.68611,0,0,.88673],"72":[0,.68611,.08229,0,.98229],"73":[0,.68611,.07778,0,.51111],"74":[0,.68611,.10069,0,.63125],"75":[0,.68611,.06979,0,.97118],"76":[0,.68611,0,0,.75555],"77":[0,.68611,.11424,0,1.14201],"78":[0,.68611,.11424,0,.95034],"79":[0,.68611,.03194,0,.83666],"80":[0,.68611,.15972,0,.72309],"81":[.19444,.68611,0,0,.86861],"82":[0,.68611,.00421,0,.87235],"83":[0,.68611,.05382,0,.69271],"84":[0,.68611,.15972,0,.63663],"85":[0,.68611,.11424,0,.80027],"86":[0,.68611,.25555,0,.67778],"87":[0,.68611,.15972,0,1.09305],"88":[0,.68611,.07778,0,.94722],"89":[0,.68611,.25555,0,.67458],"90":[0,.68611,.06979,0,.77257],"97":[0,.44444,0,0,.63287],"98":[0,.69444,0,0,.52083],"99":[0,.44444,0,0,.51342],"100":[0,.69444,0,0,.60972],"101":[0,.44444,0,0,.55361],"102":[.19444,.69444,.11042,0,.56806],"103":[.19444,.44444,.03704,0,.5449],"104":[0,.69444,0,0,.66759],"105":[0,.69326,0,0,.4048],"106":[.19444,.69326,.0622,0,.47083],"107":[0,.69444,.01852,0,.6037],"108":[0,.69444,.0088,0,.34815],"109":[0,.44444,0,0,1.0324],"110":[0,.44444,0,0,.71296],"111":[0,.44444,0,0,.58472],"112":[.19444,.44444,0,0,.60092],"113":[.19444,.44444,.03704,0,.54213],"114":[0,.44444,.03194,0,.5287],"115":[0,.44444,0,0,.53125],"116":[0,.63492,0,0,.41528],"117":[0,.44444,0,0,.68102],"118":[0,.44444,.03704,0,.56666],"119":[0,.44444,.02778,0,.83148],"120":[0,.44444,0,0,.65903],"121":[.19444,.44444,.03704,0,.59028],"122":[0,.44444,.04213,0,.55509],"160":[0,0,0,0,.25],"915":[0,.68611,.15972,0,.65694],"916":[0,.68611,0,0,.95833],"920":[0,.68611,.03194,0,.86722],"923":[0,.68611,0,0,.80555],"926":[0,.68611,.07458,0,.84125],"928":[0,.68611,.08229,0,.98229],"931":[0,.68611,.05451,0,.88507],"933":[0,.68611,.15972,0,.67083],"934":[0,.68611,0,0,.76666],"936":[0,.68611,.11653,0,.71402],"937":[0,.68611,.04835,0,.8789],"945":[0,.44444,0,0,.76064],"946":[.19444,.69444,.03403,0,.65972],"947":[.19444,.44444,.06389,0,.59003],"948":[0,.69444,.03819,0,.52222],"949":[0,.44444,0,0,.52882],"950":[.19444,.69444,.06215,0,.50833],"951":[.19444,.44444,.03704,0,.6],"952":[0,.69444,.03194,0,.5618],"953":[0,.44444,0,0,.41204],"954":[0,.44444,0,0,.66759],"955":[0,.69444,0,0,.67083],"956":[.19444,.44444,0,0,.70787],"957":[0,.44444,.06898,0,.57685],"958":[.19444,.69444,.03021,0,.50833],"959":[0,.44444,0,0,.58472],"960":[0,.44444,.03704,0,.68241],"961":[.19444,.44444,0,0,.6118],"962":[.09722,.44444,.07917,0,.42361],"963":[0,.44444,.03704,0,.68588],"964":[0,.44444,.13472,0,.52083],"965":[0,.44444,.03704,0,.63055],"966":[.19444,.44444,0,0,.74722],"967":[.19444,.44444,0,0,.71805],"968":[.19444,.69444,.03704,0,.75833],"969":[0,.44444,.03704,0,.71782],"977":[0,.69444,0,0,.69155],"981":[.19444,.69444,0,0,.7125],"982":[0,.44444,.03194,0,.975],"1009":[.19444,.44444,0,0,.6118],"1013":[0,.44444,0,0,.48333],"57649":[0,.44444,0,0,.39352],"57911":[.19444,.44444,0,0,.43889]},"Math-Italic":{"32":[0,0,0,0,.25],"48":[0,.43056,0,0,.5],"49":[0,.43056,0,0,.5],"50":[0,.43056,0,0,.5],"51":[.19444,.43056,0,0,.5],"52":[.19444,.43056,0,0,.5],"53":[.19444,.43056,0,0,.5],"54":[0,.64444,0,0,.5],"55":[.19444,.43056,0,0,.5],"56":[0,.64444,0,0,.5],"57":[.19444,.43056,0,0,.5],"65":[0,.68333,0,.13889,.75],"66":[0,.68333,.05017,.08334,.75851],"67":[0,.68333,.07153,.08334,.71472],"68":[0,.68333,.02778,.05556,.82792],"69":[0,.68333,.05764,.08334,.7382],"70":[0,.68333,.13889,.08334,.64306],"71":[0,.68333,0,.08334,.78625],"72":[0,.68333,.08125,.05556,.83125],"73":[0,.68333,.07847,.11111,.43958],"74":[0,.68333,.09618,.16667,.55451],"75":[0,.68333,.07153,.05556,.84931],"76":[0,.68333,0,.02778,.68056],"77":[0,.68333,.10903,.08334,.97014],"78":[0,.68333,.10903,.08334,.80347],"79":[0,.68333,.02778,.08334,.76278],"80":[0,.68333,.13889,.08334,.64201],"81":[.19444,.68333,0,.08334,.79056],"82":[0,.68333,.00773,.08334,.75929],"83":[0,.68333,.05764,.08334,.6132],"84":[0,.68333,.13889,.08334,.58438],"85":[0,.68333,.10903,.02778,.68278],"86":[0,.68333,.22222,0,.58333],"87":[0,.68333,.13889,0,.94445],"88":[0,.68333,.07847,.08334,.82847],"89":[0,.68333,.22222,0,.58056],"90":[0,.68333,.07153,.08334,.68264],"97":[0,.43056,0,0,.52859],"98":[0,.69444,0,0,.42917],"99":[0,.43056,0,.05556,.43276],"100":[0,.69444,0,.16667,.52049],"101":[0,.43056,0,.05556,.46563],"102":[.19444,.69444,.10764,.16667,.48959],"103":[.19444,.43056,.03588,.02778,.47697],"104":[0,.69444,0,0,.57616],"105":[0,.65952,0,0,.34451],"106":[.19444,.65952,.05724,0,.41181],"107":[0,.69444,.03148,0,.5206],"108":[0,.69444,.01968,.08334,.29838],"109":[0,.43056,0,0,.87801],"110":[0,.43056,0,0,.60023],"111":[0,.43056,0,.05556,.48472],"112":[.19444,.43056,0,.08334,.50313],"113":[.19444,.43056,.03588,.08334,.44641],"114":[0,.43056,.02778,.05556,.45116],"115":[0,.43056,0,.05556,.46875],"116":[0,.61508,0,.08334,.36111],"117":[0,.43056,0,.02778,.57246],"118":[0,.43056,.03588,.02778,.48472],"119":[0,.43056,.02691,.08334,.71592],"120":[0,.43056,0,.02778,.57153],"121":[.19444,.43056,.03588,.05556,.49028],"122":[0,.43056,.04398,.05556,.46505],"160":[0,0,0,0,.25],"915":[0,.68333,.13889,.08334,.61528],"916":[0,.68333,0,.16667,.83334],"920":[0,.68333,.02778,.08334,.76278],"923":[0,.68333,0,.16667,.69445],"926":[0,.68333,.07569,.08334,.74236],"928":[0,.68333,.08125,.05556,.83125],"931":[0,.68333,.05764,.08334,.77986],"933":[0,.68333,.13889,.05556,.58333],"934":[0,.68333,0,.08334,.66667],"936":[0,.68333,.11,.05556,.61222],"937":[0,.68333,.05017,.08334,.7724],"945":[0,.43056,.0037,.02778,.6397],"946":[.19444,.69444,.05278,.08334,.56563],"947":[.19444,.43056,.05556,0,.51773],"948":[0,.69444,.03785,.05556,.44444],"949":[0,.43056,0,.08334,.46632],"950":[.19444,.69444,.07378,.08334,.4375],"951":[.19444,.43056,.03588,.05556,.49653],"952":[0,.69444,.02778,.08334,.46944],"953":[0,.43056,0,.05556,.35394],"954":[0,.43056,0,0,.57616],"955":[0,.69444,0,0,.58334],"956":[.19444,.43056,0,.02778,.60255],"957":[0,.43056,.06366,.02778,.49398],"958":[.19444,.69444,.04601,.11111,.4375],"959":[0,.43056,0,.05556,.48472],"960":[0,.43056,.03588,0,.57003],"961":[.19444,.43056,0,.08334,.51702],"962":[.09722,.43056,.07986,.08334,.36285],"963":[0,.43056,.03588,0,.57141],"964":[0,.43056,.1132,.02778,.43715],"965":[0,.43056,.03588,.02778,.54028],"966":[.19444,.43056,0,.08334,.65417],"967":[.19444,.43056,0,.05556,.62569],"968":[.19444,.69444,.03588,.11111,.65139],"969":[0,.43056,.03588,0,.62245],"977":[0,.69444,0,.08334,.59144],"981":[.19444,.69444,0,.08334,.59583],"982":[0,.43056,.02778,0,.82813],"1009":[.19444,.43056,0,.08334,.51702],"1013":[0,.43056,0,.05556,.4059],"57649":[0,.43056,0,.02778,.32246],"57911":[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{"32":[0,0,0,0,.25],"33":[0,.69444,0,0,.36667],"34":[0,.69444,0,0,.55834],"35":[.19444,.69444,0,0,.91667],"36":[.05556,.75,0,0,.55],"37":[.05556,.75,0,0,1.02912],"38":[0,.69444,0,0,.83056],"39":[0,.69444,0,0,.30556],"40":[.25,.75,0,0,.42778],"41":[.25,.75,0,0,.42778],"42":[0,.75,0,0,.55],"43":[.11667,.61667,0,0,.85556],"44":[.10556,.13056,0,0,.30556],"45":[0,.45833,0,0,.36667],"46":[0,.13056,0,0,.30556],"47":[.25,.75,0,0,.55],"48":[0,.69444,0,0,.55],"49":[0,.69444,0,0,.55],"50":[0,.69444,0,0,.55],"51":[0,.69444,0,0,.55],"52":[0,.69444,0,0,.55],"53":[0,.69444,0,0,.55],"54":[0,.69444,0,0,.55],"55":[0,.69444,0,0,.55],"56":[0,.69444,0,0,.55],"57":[0,.69444,0,0,.55],"58":[0,.45833,0,0,.30556],"59":[.10556,.45833,0,0,.30556],"61":[-.09375,.40625,0,0,.85556],"63":[0,.69444,0,0,.51945],"64":[0,.69444,0,0,.73334],"65":[0,.69444,0,0,.73334],"66":[0,.69444,0,0,.73334],"67":[0,.69444,0,0,.70278],"68":[0,.69444,0,0,.79445],"69":[0,.69444,0,0,.64167],"70":[0,.69444,0,0,.61111],"71":[0,.69444,0,0,.73334],"72":[0,.69444,0,0,.79445],"73":[0,.69444,0,0,.33056],"74":[0,.69444,0,0,.51945],"75":[0,.69444,0,0,.76389],"76":[0,.69444,0,0,.58056],"77":[0,.69444,0,0,.97778],"78":[0,.69444,0,0,.79445],"79":[0,.69444,0,0,.79445],"80":[0,.69444,0,0,.70278],"81":[.10556,.69444,0,0,.79445],"82":[0,.69444,0,0,.70278],"83":[0,.69444,0,0,.61111],"84":[0,.69444,0,0,.73334],"85":[0,.69444,0,0,.76389],"86":[0,.69444,.01528,0,.73334],"87":[0,.69444,.01528,0,1.03889],"88":[0,.69444,0,0,.73334],"89":[0,.69444,.0275,0,.73334],"90":[0,.69444,0,0,.67223],"91":[.25,.75,0,0,.34306],"93":[.25,.75,0,0,.34306],"94":[0,.69444,0,0,.55],"95":[.35,.10833,.03056,0,.55],"97":[0,.45833,0,0,.525],"98":[0,.69444,0,0,.56111],"99":[0,.45833,0,0,.48889],"100":[0,.69444,0,0,.56111],"101":[0,.45833,0,0,.51111],"102":[0,.69444,.07639,0,.33611],"103":[.19444,.45833,.01528,0,.55],"104":[0,.69444,0,0,.56111],"105":[0,.69444,0,0,.25556],"106":[.19444,.69444,0,0,.28611],"107":[0,.69444,0,0,.53056],"108":[0,.69444,0,0,.25556],"109":[0,.45833,0,0,.86667],"110":[0,.45833,0,0,.56111],"111":[0,.45833,0,0,.55],"112":[.19444,.45833,0,0,.56111],"113":[.19444,.45833,0,0,.56111],"114":[0,.45833,.01528,0,.37222],"115":[0,.45833,0,0,.42167],"116":[0,.58929,0,0,.40417],"117":[0,.45833,0,0,.56111],"118":[0,.45833,.01528,0,.5],"119":[0,.45833,.01528,0,.74445],"120":[0,.45833,0,0,.5],"121":[.19444,.45833,.01528,0,.5],"122":[0,.45833,0,0,.47639],"126":[.35,.34444,0,0,.55],"160":[0,0,0,0,.25],"168":[0,.69444,0,0,.55],"176":[0,.69444,0,0,.73334],"180":[0,.69444,0,0,.55],"184":[.17014,0,0,0,.48889],"305":[0,.45833,0,0,.25556],"567":[.19444,.45833,0,0,.28611],"710":[0,.69444,0,0,.55],"711":[0,.63542,0,0,.55],"713":[0,.63778,0,0,.55],"728":[0,.69444,0,0,.55],"729":[0,.69444,0,0,.30556],"730":[0,.69444,0,0,.73334],"732":[0,.69444,0,0,.55],"733":[0,.69444,0,0,.55],"915":[0,.69444,0,0,.58056],"916":[0,.69444,0,0,.91667],"920":[0,.69444,0,0,.85556],"923":[0,.69444,0,0,.67223],"926":[0,.69444,0,0,.73334],"928":[0,.69444,0,0,.79445],"931":[0,.69444,0,0,.79445],"933":[0,.69444,0,0,.85556],"934":[0,.69444,0,0,.79445],"936":[0,.69444,0,0,.85556],"937":[0,.69444,0,0,.79445],"8211":[0,.45833,.03056,0,.55],"8212":[0,.45833,.03056,0,1.10001],"8216":[0,.69444,0,0,.30556],"8217":[0,.69444,0,0,.30556],"8220":[0,.69444,0,0,.55834],"8221":[0,.69444,0,0,.55834]},"SansSerif-Italic":{"32":[0,0,0,0,.25],"33":[0,.69444,.05733,0,.31945],"34":[0,.69444,.00316,0,.5],"35":[.19444,.69444,.05087,0,.83334],"36":[.05556,.75,.11156,0,.5],"37":[.05556,.75,.03126,0,.83334],"38":[0,.69444,.03058,0,.75834],"39":[0,.69444,.07816,0,.27778],"40":[.25,.75,.13164,0,.38889],"41":[.25,.75,.02536,0,.38889],"42":[0,.75,.11775,0,.5],"43":[.08333,.58333,.02536,0,.77778],"44":[.125,.08333,0,0,.27778],"45":[0,.44444,.01946,0,.33333],"46":[0,.08333,0,0,.27778],"47":[.25,.75,.13164,0,.5],"48":[0,.65556,.11156,0,.5],"49":[0,.65556,.11156,0,.5],"50":[0,.65556,.11156,0,.5],"51":[0,.65556,.11156,0,.5],"52":[0,.65556,.11156,0,.5],"53":[0,.65556,.11156,0,.5],"54":[0,.65556,.11156,0,.5],"55":[0,.65556,.11156,0,.5],"56":[0,.65556,.11156,0,.5],"57":[0,.65556,.11156,0,.5],"58":[0,.44444,.02502,0,.27778],"59":[.125,.44444,.02502,0,.27778],"61":[-.13,.37,.05087,0,.77778],"63":[0,.69444,.11809,0,.47222],"64":[0,.69444,.07555,0,.66667],"65":[0,.69444,0,0,.66667],"66":[0,.69444,.08293,0,.66667],"67":[0,.69444,.11983,0,.63889],"68":[0,.69444,.07555,0,.72223],"69":[0,.69444,.11983,0,.59722],"70":[0,.69444,.13372,0,.56945],"71":[0,.69444,.11983,0,.66667],"72":[0,.69444,.08094,0,.70834],"73":[0,.69444,.13372,0,.27778],"74":[0,.69444,.08094,0,.47222],"75":[0,.69444,.11983,0,.69445],"76":[0,.69444,0,0,.54167],"77":[0,.69444,.08094,0,.875],"78":[0,.69444,.08094,0,.70834],"79":[0,.69444,.07555,0,.73611],"80":[0,.69444,.08293,0,.63889],"81":[.125,.69444,.07555,0,.73611],"82":[0,.69444,.08293,0,.64584],"83":[0,.69444,.09205,0,.55556],"84":[0,.69444,.13372,0,.68056],"85":[0,.69444,.08094,0,.6875],"86":[0,.69444,.1615,0,.66667],"87":[0,.69444,.1615,0,.94445],"88":[0,.69444,.13372,0,.66667],"89":[0,.69444,.17261,0,.66667],"90":[0,.69444,.11983,0,.61111],"91":[.25,.75,.15942,0,.28889],"93":[.25,.75,.08719,0,.28889],"94":[0,.69444,.0799,0,.5],"95":[.35,.09444,.08616,0,.5],"97":[0,.44444,.00981,0,.48056],"98":[0,.69444,.03057,0,.51667],"99":[0,.44444,.08336,0,.44445],"100":[0,.69444,.09483,0,.51667],"101":[0,.44444,.06778,0,.44445],"102":[0,.69444,.21705,0,.30556],"103":[.19444,.44444,.10836,0,.5],"104":[0,.69444,.01778,0,.51667],"105":[0,.67937,.09718,0,.23889],"106":[.19444,.67937,.09162,0,.26667],"107":[0,.69444,.08336,0,.48889],"108":[0,.69444,.09483,0,.23889],"109":[0,.44444,.01778,0,.79445],"110":[0,.44444,.01778,0,.51667],"111":[0,.44444,.06613,0,.5],"112":[.19444,.44444,.0389,0,.51667],"113":[.19444,.44444,.04169,0,.51667],"114":[0,.44444,.10836,0,.34167],"115":[0,.44444,.0778,0,.38333],"116":[0,.57143,.07225,0,.36111],"117":[0,.44444,.04169,0,.51667],"118":[0,.44444,.10836,0,.46111],"119":[0,.44444,.10836,0,.68334],"120":[0,.44444,.09169,0,.46111],"121":[.19444,.44444,.10836,0,.46111],"122":[0,.44444,.08752,0,.43472],"126":[.35,.32659,.08826,0,.5],"160":[0,0,0,0,.25],"168":[0,.67937,.06385,0,.5],"176":[0,.69444,0,0,.73752],"184":[.17014,0,0,0,.44445],"305":[0,.44444,.04169,0,.23889],"567":[.19444,.44444,.04169,0,.26667],"710":[0,.69444,.0799,0,.5],"711":[0,.63194,.08432,0,.5],"713":[0,.60889,.08776,0,.5],"714":[0,.69444,.09205,0,.5],"715":[0,.69444,0,0,.5],"728":[0,.69444,.09483,0,.5],"729":[0,.67937,.07774,0,.27778],"730":[0,.69444,0,0,.73752],"732":[0,.67659,.08826,0,.5],"733":[0,.69444,.09205,0,.5],"915":[0,.69444,.13372,0,.54167],"916":[0,.69444,0,0,.83334],"920":[0,.69444,.07555,0,.77778],"923":[0,.69444,0,0,.61111],"926":[0,.69444,.12816,0,.66667],"928":[0,.69444,.08094,0,.70834],"931":[0,.69444,.11983,0,.72222],"933":[0,.69444,.09031,0,.77778],"934":[0,.69444,.04603,0,.72222],"936":[0,.69444,.09031,0,.77778],"937":[0,.69444,.08293,0,.72222],"8211":[0,.44444,.08616,0,.5],"8212":[0,.44444,.08616,0,1],"8216":[0,.69444,.07816,0,.27778],"8217":[0,.69444,.07816,0,.27778],"8220":[0,.69444,.14205,0,.5],"8221":[0,.69444,.00316,0,.5]},"SansSerif-Regular":{"32":[0,0,0,0,.25],"33":[0,.69444,0,0,.31945],"34":[0,.69444,0,0,.5],"35":[.19444,.69444,0,0,.83334],"36":[.05556,.75,0,0,.5],"37":[.05556,.75,0,0,.83334],"38":[0,.69444,0,0,.75834],"39":[0,.69444,0,0,.27778],"40":[.25,.75,0,0,.38889],"41":[.25,.75,0,0,.38889],"42":[0,.75,0,0,.5],"43":[.08333,.58333,0,0,.77778],"44":[.125,.08333,0,0,.27778],"45":[0,.44444,0,0,.33333],"46":[0,.08333,0,0,.27778],"47":[.25,.75,0,0,.5],"48":[0,.65556,0,0,.5],"49":[0,.65556,0,0,.5],"50":[0,.65556,0,0,.5],"51":[0,.65556,0,0,.5],"52":[0,.65556,0,0,.5],"53":[0,.65556,0,0,.5],"54":[0,.65556,0,0,.5],"55":[0,.65556,0,0,.5],"56":[0,.65556,0,0,.5],"57":[0,.65556,0,0,.5],"58":[0,.44444,0,0,.27778],"59":[.125,.44444,0,0,.27778],"61":[-.13,.37,0,0,.77778],"63":[0,.69444,0,0,.47222],"64":[0,.69444,0,0,.66667],"65":[0,.69444,0,0,.66667],"66":[0,.69444,0,0,.66667],"67":[0,.69444,0,0,.63889],"68":[0,.69444,0,0,.72223],"69":[0,.69444,0,0,.59722],"70":[0,.69444,0,0,.56945],"71":[0,.69444,0,0,.66667],"72":[0,.69444,0,0,.70834],"73":[0,.69444,0,0,.27778],"74":[0,.69444,0,0,.47222],"75":[0,.69444,0,0,.69445],"76":[0,.69444,0,0,.54167],"77":[0,.69444,0,0,.875],"78":[0,.69444,0,0,.70834],"79":[0,.69444,0,0,.73611],"80":[0,.69444,0,0,.63889],"81":[.125,.69444,0,0,.73611],"82":[0,.69444,0,0,.64584],"83":[0,.69444,0,0,.55556],"84":[0,.69444,0,0,.68056],"85":[0,.69444,0,0,.6875],"86":[0,.69444,.01389,0,.66667],"87":[0,.69444,.01389,0,.94445],"88":[0,.69444,0,0,.66667],"89":[0,.69444,.025,0,.66667],"90":[0,.69444,0,0,.61111],"91":[.25,.75,0,0,.28889],"93":[.25,.75,0,0,.28889],"94":[0,.69444,0,0,.5],"95":[.35,.09444,.02778,0,.5],"97":[0,.44444,0,0,.48056],"98":[0,.69444,0,0,.51667],"99":[0,.44444,0,0,.44445],"100":[0,.69444,0,0,.51667],"101":[0,.44444,0,0,.44445],"102":[0,.69444,.06944,0,.30556],"103":[.19444,.44444,.01389,0,.5],"104":[0,.69444,0,0,.51667],"105":[0,.67937,0,0,.23889],"106":[.19444,.67937,0,0,.26667],"107":[0,.69444,0,0,.48889],"108":[0,.69444,0,0,.23889],"109":[0,.44444,0,0,.79445],"110":[0,.44444,0,0,.51667],"111":[0,.44444,0,0,.5],"112":[.19444,.44444,0,0,.51667],"113":[.19444,.44444,0,0,.51667],"114":[0,.44444,.01389,0,.34167],"115":[0,.44444,0,0,.38333],"116":[0,.57143,0,0,.36111],"117":[0,.44444,0,0,.51667],"118":[0,.44444,.01389,0,.46111],"119":[0,.44444,.01389,0,.68334],"120":[0,.44444,0,0,.46111],"121":[.19444,.44444,.01389,0,.46111],"122":[0,.44444,0,0,.43472],"126":[.35,.32659,0,0,.5],"160":[0,0,0,0,.25],"168":[0,.67937,0,0,.5],"176":[0,.69444,0,0,.66667],"184":[.17014,0,0,0,.44445],"305":[0,.44444,0,0,.23889],"567":[.19444,.44444,0,0,.26667],"710":[0,.69444,0,0,.5],"711":[0,.63194,0,0,.5],"713":[0,.60889,0,0,.5],"714":[0,.69444,0,0,.5],"715":[0,.69444,0,0,.5],"728":[0,.69444,0,0,.5],"729":[0,.67937,0,0,.27778],"730":[0,.69444,0,0,.66667],"732":[0,.67659,0,0,.5],"733":[0,.69444,0,0,.5],"915":[0,.69444,0,0,.54167],"916":[0,.69444,0,0,.83334],"920":[0,.69444,0,0,.77778],"923":[0,.69444,0,0,.61111],"926":[0,.69444,0,0,.66667],"928":[0,.69444,0,0,.70834],"931":[0,.69444,0,0,.72222],"933":[0,.69444,0,0,.77778],"934":[0,.69444,0,0,.72222],"936":[0,.69444,0,0,.77778],"937":[0,.69444,0,0,.72222],"8211":[0,.44444,.02778,0,.5],"8212":[0,.44444,.02778,0,1],"8216":[0,.69444,0,0,.27778],"8217":[0,.69444,0,0,.27778],"8220":[0,.69444,0,0,.5],"8221":[0,.69444,0,0,.5]},"Script-Regular":{"32":[0,0,0,0,.25],"65":[0,.7,.22925,0,.80253],"66":[0,.7,.04087,0,.90757],"67":[0,.7,.1689,0,.66619],"68":[0,.7,.09371,0,.77443],"69":[0,.7,.18583,0,.56162],"70":[0,.7,.13634,0,.89544],"71":[0,.7,.17322,0,.60961],"72":[0,.7,.29694,0,.96919],"73":[0,.7,.19189,0,.80907],"74":[.27778,.7,.19189,0,1.05159],"75":[0,.7,.31259,0,.91364],"76":[0,.7,.19189,0,.87373],"77":[0,.7,.15981,0,1.08031],"78":[0,.7,.3525,0,.9015],"79":[0,.7,.08078,0,.73787],"80":[0,.7,.08078,0,1.01262],"81":[0,.7,.03305,0,.88282],"82":[0,.7,.06259,0,.85],"83":[0,.7,.19189,0,.86767],"84":[0,.7,.29087,0,.74697],"85":[0,.7,.25815,0,.79996],"86":[0,.7,.27523,0,.62204],"87":[0,.7,.27523,0,.80532],"88":[0,.7,.26006,0,.94445],"89":[0,.7,.2939,0,.70961],"90":[0,.7,.24037,0,.8212],"160":[0,0,0,0,.25]},"Size1-Regular":{"32":[0,0,0,0,.25],"40":[.35001,.85,0,0,.45834],"41":[.35001,.85,0,0,.45834],"47":[.35001,.85,0,0,.57778],"91":[.35001,.85,0,0,.41667],"92":[.35001,.85,0,0,.57778],"93":[.35001,.85,0,0,.41667],"123":[.35001,.85,0,0,.58334],"125":[.35001,.85,0,0,.58334],"160":[0,0,0,0,.25],"710":[0,.72222,0,0,.55556],"732":[0,.72222,0,0,.55556],"770":[0,.72222,0,0,.55556],"771":[0,.72222,0,0,.55556],"8214":[-99e-5,.601,0,0,.77778],"8593":[1e-5,.6,0,0,.66667],"8595":[1e-5,.6,0,0,.66667],"8657":[1e-5,.6,0,0,.77778],"8659":[1e-5,.6,0,0,.77778],"8719":[.25001,.75,0,0,.94445],"8720":[.25001,.75,0,0,.94445],"8721":[.25001,.75,0,0,1.05556],"8730":[.35001,.85,0,0,1],"8739":[-.00599,.606,0,0,.33333],"8741":[-.00599,.606,0,0,.55556],"8747":[.30612,.805,.19445,0,.47222],"8748":[.306,.805,.19445,0,.47222],"8749":[.306,.805,.19445,0,.47222],"8750":[.30612,.805,.19445,0,.47222],"8896":[.25001,.75,0,0,.83334],"8897":[.25001,.75,0,0,.83334],"8898":[.25001,.75,0,0,.83334],"8899":[.25001,.75,0,0,.83334],"8968":[.35001,.85,0,0,.47222],"8969":[.35001,.85,0,0,.47222],"8970":[.35001,.85,0,0,.47222],"8971":[.35001,.85,0,0,.47222],"9168":[-99e-5,.601,0,0,.66667],"10216":[.35001,.85,0,0,.47222],"10217":[.35001,.85,0,0,.47222],"10752":[.25001,.75,0,0,1.11111],"10753":[.25001,.75,0,0,1.11111],"10754":[.25001,.75,0,0,1.11111],"10756":[.25001,.75,0,0,.83334],"10758":[.25001,.75,0,0,.83334]},"Size2-Regular":{"32":[0,0,0,0,.25],"40":[.65002,1.15,0,0,.59722],"41":[.65002,1.15,0,0,.59722],"47":[.65002,1.15,0,0,.81111],"91":[.65002,1.15,0,0,.47222],"92":[.65002,1.15,0,0,.81111],"93":[.65002,1.15,0,0,.47222],"123":[.65002,1.15,0,0,.66667],"125":[.65002,1.15,0,0,.66667],"160":[0,0,0,0,.25],"710":[0,.75,0,0,1],"732":[0,.75,0,0,1],"770":[0,.75,0,0,1],"771":[0,.75,0,0,1],"8719":[.55001,1.05,0,0,1.27778],"8720":[.55001,1.05,0,0,1.27778],"8721":[.55001,1.05,0,0,1.44445],"8730":[.65002,1.15,0,0,1],"8747":[.86225,1.36,.44445,0,.55556],"8748":[.862,1.36,.44445,0,.55556],"8749":[.862,1.36,.44445,0,.55556],"8750":[.86225,1.36,.44445,0,.55556],"8896":[.55001,1.05,0,0,1.11111],"8897":[.55001,1.05,0,0,1.11111],"8898":[.55001,1.05,0,0,1.11111],"8899":[.55001,1.05,0,0,1.11111],"8968":[.65002,1.15,0,0,.52778],"8969":[.65002,1.15,0,0,.52778],"8970":[.65002,1.15,0,0,.52778],"8971":[.65002,1.15,0,0,.52778],"10216":[.65002,1.15,0,0,.61111],"10217":[.65002,1.15,0,0,.61111],"10752":[.55001,1.05,0,0,1.51112],"10753":[.55001,1.05,0,0,1.51112],"10754":[.55001,1.05,0,0,1.51112],"10756":[.55001,1.05,0,0,1.11111],"10758":[.55001,1.05,0,0,1.11111]},"Size3-Regular":{"32":[0,0,0,0,.25],"40":[.95003,1.45,0,0,.73611],"41":[.95003,1.45,0,0,.73611],"47":[.95003,1.45,0,0,1.04445],"91":[.95003,1.45,0,0,.52778],"92":[.95003,1.45,0,0,1.04445],"93":[.95003,1.45,0,0,.52778],"123":[.95003,1.45,0,0,.75],"125":[.95003,1.45,0,0,.75],"160":[0,0,0,0,.25],"710":[0,.75,0,0,1.44445],"732":[0,.75,0,0,1.44445],"770":[0,.75,0,0,1.44445],"771":[0,.75,0,0,1.44445],"8730":[.95003,1.45,0,0,1],"8968":[.95003,1.45,0,0,.58334],"8969":[.95003,1.45,0,0,.58334],"8970":[.95003,1.45,0,0,.58334],"8971":[.95003,1.45,0,0,.58334],"10216":[.95003,1.45,0,0,.75],"10217":[.95003,1.45,0,0,.75]},"Size4-Regular":{"32":[0,0,0,0,.25],"40":[1.25003,1.75,0,0,.79167],"41":[1.25003,1.75,0,0,.79167],"47":[1.25003,1.75,0,0,1.27778],"91":[1.25003,1.75,0,0,.58334],"92":[1.25003,1.75,0,0,1.27778],"93":[1.25003,1.75,0,0,.58334],"123":[1.25003,1.75,0,0,.80556],"125":[1.25003,1.75,0,0,.80556],"160":[0,0,0,0,.25],"710":[0,.825,0,0,1.8889],"732":[0,.825,0,0,1.8889],"770":[0,.825,0,0,1.8889],"771":[0,.825,0,0,1.8889],"8730":[1.25003,1.75,0,0,1],"8968":[1.25003,1.75,0,0,.63889],"8969":[1.25003,1.75,0,0,.63889],"8970":[1.25003,1.75,0,0,.63889],"8971":[1.25003,1.75,0,0,.63889],"9115":[.64502,1.155,0,0,.875],"9116":[1e-5,.6,0,0,.875],"9117":[.64502,1.155,0,0,.875],"9118":[.64502,1.155,0,0,.875],"9119":[1e-5,.6,0,0,.875],"9120":[.64502,1.155,0,0,.875],"9121":[.64502,1.155,0,0,.66667],"9122":[-99e-5,.601,0,0,.66667],"9123":[.64502,1.155,0,0,.66667],"9124":[.64502,1.155,0,0,.66667],"9125":[-99e-5,.601,0,0,.66667],"9126":[.64502,1.155,0,0,.66667],"9127":[1e-5,.9,0,0,.88889],"9128":[.65002,1.15,0,0,.88889],"9129":[.90001,0,0,0,.88889],"9130":[0,.3,0,0,.88889],"9131":[1e-5,.9,0,0,.88889],"9132":[.65002,1.15,0,0,.88889],"9133":[.90001,0,0,0,.88889],"9143":[.88502,.915,0,0,1.05556],"10216":[1.25003,1.75,0,0,.80556],"10217":[1.25003,1.75,0,0,.80556],"57344":[-.00499,.605,0,0,1.05556],"57345":[-.00499,.605,0,0,1.05556],"57680":[0,.12,0,0,.45],"57681":[0,.12,0,0,.45],"57682":[0,.12,0,0,.45],"57683":[0,.12,0,0,.45]},"Typewriter-Regular":{"32":[0,0,0,0,.525],"33":[0,.61111,0,0,.525],"34":[0,.61111,0,0,.525],"35":[0,.61111,0,0,.525],"36":[.08333,.69444,0,0,.525],"37":[.08333,.69444,0,0,.525],"38":[0,.61111,0,0,.525],"39":[0,.61111,0,0,.525],"40":[.08333,.69444,0,0,.525],"41":[.08333,.69444,0,0,.525],"42":[0,.52083,0,0,.525],"43":[-.08056,.53055,0,0,.525],"44":[.13889,.125,0,0,.525],"45":[-.08056,.53055,0,0,.525],"46":[0,.125,0,0,.525],"47":[.08333,.69444,0,0,.525],"48":[0,.61111,0,0,.525],"49":[0,.61111,0,0,.525],"50":[0,.61111,0,0,.525],"51":[0,.61111,0,0,.525],"52":[0,.61111,0,0,.525],"53":[0,.61111,0,0,.525],"54":[0,.61111,0,0,.525],"55":[0,.61111,0,0,.525],"56":[0,.61111,0,0,.525],"57":[0,.61111,0,0,.525],"58":[0,.43056,0,0,.525],"59":[.13889,.43056,0,0,.525],"60":[-.05556,.55556,0,0,.525],"61":[-.19549,.41562,0,0,.525],"62":[-.05556,.55556,0,0,.525],"63":[0,.61111,0,0,.525],"64":[0,.61111,0,0,.525],"65":[0,.61111,0,0,.525],"66":[0,.61111,0,0,.525],"67":[0,.61111,0,0,.525],"68":[0,.61111,0,0,.525],"69":[0,.61111,0,0,.525],"70":[0,.61111,0,0,.525],"71":[0,.61111,0,0,.525],"72":[0,.61111,0,0,.525],"73":[0,.61111,0,0,.525],"74":[0,.61111,0,0,.525],"75":[0,.61111,0,0,.525],"76":[0,.61111,0,0,.525],"77":[0,.61111,0,0,.525],"78":[0,.61111,0,0,.525],"79":[0,.61111,0,0,.525],"80":[0,.61111,0,0,.525],"81":[.13889,.61111,0,0,.525],"82":[0,.61111,0,0,.525],"83":[0,.61111,0,0,.525],"84":[0,.61111,0,0,.525],"85":[0,.61111,0,0,.525],"86":[0,.61111,0,0,.525],"87":[0,.61111,0,0,.525],"88":[0,.61111,0,0,.525],"89":[0,.61111,0,0,.525],"90":[0,.61111,0,0,.525],"91":[.08333,.69444,0,0,.525],"92":[.08333,.69444,0,0,.525],"93":[.08333,.69444,0,0,.525],"94":[0,.61111,0,0,.525],"95":[.09514,0,0,0,.525],"96":[0,.61111,0,0,.525],"97":[0,.43056,0,0,.525],"98":[0,.61111,0,0,.525],"99":[0,.43056,0,0,.525],"100":[0,.61111,0,0,.525],"101":[0,.43056,0,0,.525],"102":[0,.61111,0,0,.525],"103":[.22222,.43056,0,0,.525],"104":[0,.61111,0,0,.525],"105":[0,.61111,0,0,.525],"106":[.22222,.61111,0,0,.525],"107":[0,.61111,0,0,.525],"108":[0,.61111,0,0,.525],"109":[0,.43056,0,0,.525],"110":[0,.43056,0,0,.525],"111":[0,.43056,0,0,.525],"112":[.22222,.43056,0,0,.525],"113":[.22222,.43056,0,0,.525],"114":[0,.43056,0,0,.525],"115":[0,.43056,0,0,.525],"116":[0,.55358,0,0,.525],"117":[0,.43056,0,0,.525],"118":[0,.43056,0,0,.525],"119":[0,.43056,0,0,.525],"120":[0,.43056,0,0,.525],"121":[.22222,.43056,0,0,.525],"122":[0,.43056,0,0,.525],"123":[.08333,.69444,0,0,.525],"124":[.08333,.69444,0,0,.525],"125":[.08333,.69444,0,0,.525],"126":[0,.61111,0,0,.525],"127":[0,.61111,0,0,.525],"160":[0,0,0,0,.525],"176":[0,.61111,0,0,.525],"184":[.19445,0,0,0,.525],"305":[0,.43056,0,0,.525],"567":[.22222,.43056,0,0,.525],"711":[0,.56597,0,0,.525],"713":[0,.56555,0,0,.525],"714":[0,.61111,0,0,.525],"715":[0,.61111,0,0,.525],"728":[0,.61111,0,0,.525],"730":[0,.61111,0,0,.525],"770":[0,.61111,0,0,.525],"771":[0,.61111,0,0,.525],"776":[0,.61111,0,0,.525],"915":[0,.61111,0,0,.525],"916":[0,.61111,0,0,.525],"920":[0,.61111,0,0,.525],"923":[0,.61111,0,0,.525],"926":[0,.61111,0,0,.525],"928":[0,.61111,0,0,.525],"931":[0,.61111,0,0,.525],"933":[0,.61111,0,0,.525],"934":[0,.61111,0,0,.525],"936":[0,.61111,0,0,.525],"937":[0,.61111,0,0,.525],"8216":[0,.61111,0,0,.525],"8217":[0,.61111,0,0,.525],"8242":[0,.61111,0,0,.525],"9251":[.11111,.21944,0,0,.525]}},ce={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},qt={\u00C5:"A",\u00D0:"D",\u00DE:"o",\u00E5:"a",\u00F0:"d",\u00FE:"o",\u0410:"A",\u0411:"B",\u0412:"B",\u0413:"F",\u0414:"A",\u0415:"E",\u0416:"K",\u0417:"3",\u0418:"N",\u0419:"N",\u041A:"K",\u041B:"N",\u041C:"M",\u041D:"H",\u041E:"O",\u041F:"N",\u0420:"P",\u0421:"C",\u0422:"T",\u0423:"y",\u0424:"O",\u0425:"X",\u0426:"U",\u0427:"h",\u0428:"W",\u0429:"W",\u042A:"B",\u042B:"X",\u042C:"B",\u042D:"3",\u042E:"X",\u042F:"R",\u0430:"a",\u0431:"b",\u0432:"a",\u0433:"r",\u0434:"y",\u0435:"e",\u0436:"m",\u0437:"e",\u0438:"n",\u0439:"n",\u043A:"n",\u043B:"n",\u043C:"m",\u043D:"n",\u043E:"o",\u043F:"n",\u0440:"p",\u0441:"c",\u0442:"o",\u0443:"y",\u0444:"b",\u0445:"x",\u0446:"n",\u0447:"n",\u0448:"w",\u0449:"w",\u044A:"a",\u044B:"m",\u044C:"a",\u044D:"e",\u044E:"m",\u044F:"r"};function Ha(r,e){b0[r]=e}function He(r,e,t){if(!b0[e])throw new Error("Font metrics not found for font: "+e+".");var a=r.charCodeAt(0),i=b0[e][a];if(!i&&r[0]in qt&&(a=qt[r[0]].charCodeAt(0),i=b0[e][a]),!i&&t==="text"&&Ct(a)&&(i=b0[e][77]),i)return{depth:i[0],height:i[1],italic:i[2],skew:i[3],width:i[4]}}var Le={};function La(r){var e;if(r>=5?e=0:r>=3?e=1:e=2,!Le[e]){var t=Le[e]={cssEmPerMu:ce.quad[e]/18};for(var a in ce)ce.hasOwnProperty(a)&&(t[a]=ce[a][e])}return Le[e]}var Fa=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],Et=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],Rt=function(e,t){return t.size<2?e:Fa[e-1][t.size-1]};class S0{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||S0.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=Et[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var a in e)e.hasOwnProperty(a)&&(t[a]=e[a]);return new S0(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:Rt(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:Et[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=Rt(S0.BASESIZE,e);return this.size===t&&this.textSize===S0.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==S0.BASESIZE?["sizing","reset-size"+this.size,"size"+S0.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=La(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}S0.BASESIZE=6;var Fe={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},Pa={ex:!0,em:!0,mu:!0},It=function(e){return typeof e!="string"&&(e=e.unit),e in Fe||e in Pa||e==="ex"},Z=function(e,t){var a;if(e.unit in Fe)a=Fe[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if(e.unit==="mu")a=t.fontMetrics().cssEmPerMu;else{var i;if(t.style.isTight()?i=t.havingStyle(t.style.text()):i=t,e.unit==="ex")a=i.fontMetrics().xHeight;else if(e.unit==="em")a=i.fontMetrics().quad;else throw new M("Invalid unit: '"+e.unit+"'");i!==t&&(a*=i.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*a,t.maxSize)},z=function(e){return+e.toFixed(4)+"em"},E0=function(e){return e.filter(t=>t).join(" ")},Ot=function(e,t,a){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=a||{},t){t.style.isTight()&&this.classes.push("mtight");var i=t.getColor();i&&(this.style.color=i)}},Ht=function(e){var t=document.createElement(e);t.className=E0(this.classes);for(var a in this.style)this.style.hasOwnProperty(a)&&(t.style[a]=this.style[a]);for(var i in this.attributes)this.attributes.hasOwnProperty(i)&&t.setAttribute(i,this.attributes[i]);for(var s=0;s<this.children.length;s++)t.appendChild(this.children[s].toNode());return t},Lt=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+R.escape(E0(this.classes))+'"');var a="";for(var i in this.style)this.style.hasOwnProperty(i)&&(a+=R.hyphenate(i)+":"+this.style[i]+";");a&&(t+=' style="'+R.escape(a)+'"');for(var s in this.attributes)this.attributes.hasOwnProperty(s)&&(t+=" "+s+'="'+R.escape(this.attributes[s])+'"');t+=">";for(var o=0;o<this.children.length;o++)t+=this.children[o].toMarkup();return t+="</"+e+">",t};class re{constructor(e,t,a,i){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,Ot.call(this,e,a,i),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return R.contains(this.classes,e)}toNode(){return Ht.call(this,"span")}toMarkup(){return Lt.call(this,"span")}}class Pe{constructor(e,t,a,i){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,Ot.call(this,t,i),this.children=a||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return R.contains(this.classes,e)}toNode(){return Ht.call(this,"a")}toMarkup(){return Lt.call(this,"a")}}class Ga{constructor(e,t,a){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=a}hasClass(e){return R.contains(this.classes,e)}toNode(){var e=document.createElement("img");e.src=this.src,e.alt=this.alt,e.className="mord";for(var t in this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e="<img  src='"+this.src+" 'alt='"+this.alt+"' ",t="";for(var a in this.style)this.style.hasOwnProperty(a)&&(t+=R.hyphenate(a)+":"+this.style[a]+";");return t&&(e+=' style="'+R.escape(t)+'"'),e+="'/>",e}}var Va={\u00EE:"\u0131\u0302",\u00EF:"\u0131\u0308",\u00ED:"\u0131\u0301",\u00EC:"\u0131\u0300"};class c0{constructor(e,t,a,i,s,o,h,c){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=a||0,this.italic=i||0,this.skew=s||0,this.width=o||0,this.classes=h||[],this.style=c||{},this.maxFontSize=0;var p=Ta(this.text.charCodeAt(0));p&&this.classes.push(p+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=Va[this.text])}hasClass(e){return R.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;this.italic>0&&(t=document.createElement("span"),t.style.marginRight=z(this.italic)),this.classes.length>0&&(t=t||document.createElement("span"),t.className=E0(this.classes));for(var a in this.style)this.style.hasOwnProperty(a)&&(t=t||document.createElement("span"),t.style[a]=this.style[a]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=R.escape(E0(this.classes)),t+='"');var a="";this.italic>0&&(a+="margin-right:"+this.italic+"em;");for(var i in this.style)this.style.hasOwnProperty(i)&&(a+=R.hyphenate(i)+":"+this.style[i]+";");a&&(e=!0,t+=' style="'+R.escape(a)+'"');var s=R.escape(this.text);return e?(t+=">",t+=s,t+="</span>",t):s}}class R0{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"svg");for(var a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&t.setAttribute(a,this.attributes[a]);for(var i=0;i<this.children.length;i++)t.appendChild(this.children[i].toNode());return t}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+"='"+this.attributes[t]+"'");e+=">";for(var a=0;a<this.children.length;a++)e+=this.children[a].toMarkup();return e+="</svg>",e}}class P0{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"path");return this.alternate?t.setAttribute("d",this.alternate):t.setAttribute("d",Nt[this.pathName]),t}toMarkup(){return this.alternate?"<path d='"+this.alternate+"'/>":"<path d='"+Nt[this.pathName]+"'/>"}}class Ge{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e="http://www.w3.org/2000/svg",t=document.createElementNS(e,"line");for(var a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&t.setAttribute(a,this.attributes[a]);return t}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+"='"+this.attributes[t]+"'");return e+="/>",e}}function Ft(r){if(r instanceof c0)return r;throw new Error("Expected symbolNode but got "+String(r)+".")}function Ua(r){if(r instanceof re)return r;throw new Error("Expected span<HtmlDomNode> but got "+String(r)+".")}var Ya={bin:1,close:1,inner:1,open:1,punct:1,rel:1},Xa={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},X={math:{},text:{}};function n(r,e,t,a,i,s){X[r][i]={font:e,group:t,replace:a},s&&a&&(X[r][a]=X[r][i])}var l="math",k="text",u="main",d="ams",$="accent-token",D="bin",a0="close",W0="inner",E="mathord",J="op-token",o0="open",de="punct",f="rel",M0="spacing",v="textord";n(l,u,f,"\u2261","\\equiv",!0),n(l,u,f,"\u227A","\\prec",!0),n(l,u,f,"\u227B","\\succ",!0),n(l,u,f,"\u223C","\\sim",!0),n(l,u,f,"\u22A5","\\perp"),n(l,u,f,"\u2AAF","\\preceq",!0),n(l,u,f,"\u2AB0","\\succeq",!0),n(l,u,f,"\u2243","\\simeq",!0),n(l,u,f,"\u2223","\\mid",!0),n(l,u,f,"\u226A","\\ll",!0),n(l,u,f,"\u226B","\\gg",!0),n(l,u,f,"\u224D","\\asymp",!0),n(l,u,f,"\u2225","\\parallel"),n(l,u,f,"\u22C8","\\bowtie",!0),n(l,u,f,"\u2323","\\smile",!0),n(l,u,f,"\u2291","\\sqsubseteq",!0),n(l,u,f,"\u2292","\\sqsupseteq",!0),n(l,u,f,"\u2250","\\doteq",!0),n(l,u,f,"\u2322","\\frown",!0),n(l,u,f,"\u220B","\\ni",!0),n(l,u,f,"\u221D","\\propto",!0),n(l,u,f,"\u22A2","\\vdash",!0),n(l,u,f,"\u22A3","\\dashv",!0),n(l,u,f,"\u220B","\\owns"),n(l,u,de,".","\\ldotp"),n(l,u,de,"\u22C5","\\cdotp"),n(l,u,v,"#","\\#"),n(k,u,v,"#","\\#"),n(l,u,v,"&","\\&"),n(k,u,v,"&","\\&"),n(l,u,v,"\u2135","\\aleph",!0),n(l,u,v,"\u2200","\\forall",!0),n(l,u,v,"\u210F","\\hbar",!0),n(l,u,v,"\u2203","\\exists",!0),n(l,u,v,"\u2207","\\nabla",!0),n(l,u,v,"\u266D","\\flat",!0),n(l,u,v,"\u2113","\\ell",!0),n(l,u,v,"\u266E","\\natural",!0),n(l,u,v,"\u2663","\\clubsuit",!0),n(l,u,v,"\u2118","\\wp",!0),n(l,u,v,"\u266F","\\sharp",!0),n(l,u,v,"\u2662","\\diamondsuit",!0),n(l,u,v,"\u211C","\\Re",!0),n(l,u,v,"\u2661","\\heartsuit",!0),n(l,u,v,"\u2111","\\Im",!0),n(l,u,v,"\u2660","\\spadesuit",!0),n(l,u,v,"\xA7","\\S",!0),n(k,u,v,"\xA7","\\S"),n(l,u,v,"\xB6","\\P",!0),n(k,u,v,"\xB6","\\P"),n(l,u,v,"\u2020","\\dag"),n(k,u,v,"\u2020","\\dag"),n(k,u,v,"\u2020","\\textdagger"),n(l,u,v,"\u2021","\\ddag"),n(k,u,v,"\u2021","\\ddag"),n(k,u,v,"\u2021","\\textdaggerdbl"),n(l,u,a0,"\u23B1","\\rmoustache",!0),n(l,u,o0,"\u23B0","\\lmoustache",!0),n(l,u,a0,"\u27EF","\\rgroup",!0),n(l,u,o0,"\u27EE","\\lgroup",!0),n(l,u,D,"\u2213","\\mp",!0),n(l,u,D,"\u2296","\\ominus",!0),n(l,u,D,"\u228E","\\uplus",!0),n(l,u,D,"\u2293","\\sqcap",!0),n(l,u,D,"\u2217","\\ast"),n(l,u,D,"\u2294","\\sqcup",!0),n(l,u,D,"\u25EF","\\bigcirc",!0),n(l,u,D,"\u2219","\\bullet",!0),n(l,u,D,"\u2021","\\ddagger"),n(l,u,D,"\u2240","\\wr",!0),n(l,u,D,"\u2A3F","\\amalg"),n(l,u,D,"&","\\And"),n(l,u,f,"\u27F5","\\longleftarrow",!0),n(l,u,f,"\u21D0","\\Leftarrow",!0),n(l,u,f,"\u27F8","\\Longleftarrow",!0),n(l,u,f,"\u27F6","\\longrightarrow",!0),n(l,u,f,"\u21D2","\\Rightarrow",!0),n(l,u,f,"\u27F9","\\Longrightarrow",!0),n(l,u,f,"\u2194","\\leftrightarrow",!0),n(l,u,f,"\u27F7","\\longleftrightarrow",!0),n(l,u,f,"\u21D4","\\Leftrightarrow",!0),n(l,u,f,"\u27FA","\\Longleftrightarrow",!0),n(l,u,f,"\u21A6","\\mapsto",!0),n(l,u,f,"\u27FC","\\longmapsto",!0),n(l,u,f,"\u2197","\\nearrow",!0),n(l,u,f,"\u21A9","\\hookleftarrow",!0),n(l,u,f,"\u21AA","\\hookrightarrow",!0),n(l,u,f,"\u2198","\\searrow",!0),n(l,u,f,"\u21BC","\\leftharpoonup",!0),n(l,u,f,"\u21C0","\\rightharpoonup",!0),n(l,u,f,"\u2199","\\swarrow",!0),n(l,u,f,"\u21BD","\\leftharpoondown",!0),n(l,u,f,"\u21C1","\\rightharpoondown",!0),n(l,u,f,"\u2196","\\nwarrow",!0),n(l,u,f,"\u21CC","\\rightleftharpoons",!0),n(l,d,f,"\u226E","\\nless",!0),n(l,d,f,"\uE010","\\@nleqslant"),n(l,d,f,"\uE011","\\@nleqq"),n(l,d,f,"\u2A87","\\lneq",!0),n(l,d,f,"\u2268","\\lneqq",!0),n(l,d,f,"\uE00C","\\@lvertneqq"),n(l,d,f,"\u22E6","\\lnsim",!0),n(l,d,f,"\u2A89","\\lnapprox",!0),n(l,d,f,"\u2280","\\nprec",!0),n(l,d,f,"\u22E0","\\npreceq",!0),n(l,d,f,"\u22E8","\\precnsim",!0),n(l,d,f,"\u2AB9","\\precnapprox",!0),n(l,d,f,"\u2241","\\nsim",!0),n(l,d,f,"\uE006","\\@nshortmid"),n(l,d,f,"\u2224","\\nmid",!0),n(l,d,f,"\u22AC","\\nvdash",!0),n(l,d,f,"\u22AD","\\nvDash",!0),n(l,d,f,"\u22EA","\\ntriangleleft"),n(l,d,f,"\u22EC","\\ntrianglelefteq",!0),n(l,d,f,"\u228A","\\subsetneq",!0),n(l,d,f,"\uE01A","\\@varsubsetneq"),n(l,d,f,"\u2ACB","\\subsetneqq",!0),n(l,d,f,"\uE017","\\@varsubsetneqq"),n(l,d,f,"\u226F","\\ngtr",!0),n(l,d,f,"\uE00F","\\@ngeqslant"),n(l,d,f,"\uE00E","\\@ngeqq"),n(l,d,f,"\u2A88","\\gneq",!0),n(l,d,f,"\u2269","\\gneqq",!0),n(l,d,f,"\uE00D","\\@gvertneqq"),n(l,d,f,"\u22E7","\\gnsim",!0),n(l,d,f,"\u2A8A","\\gnapprox",!0),n(l,d,f,"\u2281","\\nsucc",!0),n(l,d,f,"\u22E1","\\nsucceq",!0),n(l,d,f,"\u22E9","\\succnsim",!0),n(l,d,f,"\u2ABA","\\succnapprox",!0),n(l,d,f,"\u2246","\\ncong",!0),n(l,d,f,"\uE007","\\@nshortparallel"),n(l,d,f,"\u2226","\\nparallel",!0),n(l,d,f,"\u22AF","\\nVDash",!0),n(l,d,f,"\u22EB","\\ntriangleright"),n(l,d,f,"\u22ED","\\ntrianglerighteq",!0),n(l,d,f,"\uE018","\\@nsupseteqq"),n(l,d,f,"\u228B","\\supsetneq",!0),n(l,d,f,"\uE01B","\\@varsupsetneq"),n(l,d,f,"\u2ACC","\\supsetneqq",!0),n(l,d,f,"\uE019","\\@varsupsetneqq"),n(l,d,f,"\u22AE","\\nVdash",!0),n(l,d,f,"\u2AB5","\\precneqq",!0),n(l,d,f,"\u2AB6","\\succneqq",!0),n(l,d,f,"\uE016","\\@nsubseteqq"),n(l,d,D,"\u22B4","\\unlhd"),n(l,d,D,"\u22B5","\\unrhd"),n(l,d,f,"\u219A","\\nleftarrow",!0),n(l,d,f,"\u219B","\\nrightarrow",!0),n(l,d,f,"\u21CD","\\nLeftarrow",!0),n(l,d,f,"\u21CF","\\nRightarrow",!0),n(l,d,f,"\u21AE","\\nleftrightarrow",!0),n(l,d,f,"\u21CE","\\nLeftrightarrow",!0),n(l,d,f,"\u25B3","\\vartriangle"),n(l,d,v,"\u210F","\\hslash"),n(l,d,v,"\u25BD","\\triangledown"),n(l,d,v,"\u25CA","\\lozenge"),n(l,d,v,"\u24C8","\\circledS"),n(l,d,v,"\xAE","\\circledR"),n(k,d,v,"\xAE","\\circledR"),n(l,d,v,"\u2221","\\measuredangle",!0),n(l,d,v,"\u2204","\\nexists"),n(l,d,v,"\u2127","\\mho"),n(l,d,v,"\u2132","\\Finv",!0),n(l,d,v,"\u2141","\\Game",!0),n(l,d,v,"\u2035","\\backprime"),n(l,d,v,"\u25B2","\\blacktriangle"),n(l,d,v,"\u25BC","\\blacktriangledown"),n(l,d,v,"\u25A0","\\blacksquare"),n(l,d,v,"\u29EB","\\blacklozenge"),n(l,d,v,"\u2605","\\bigstar"),n(l,d,v,"\u2222","\\sphericalangle",!0),n(l,d,v,"\u2201","\\complement",!0),n(l,d,v,"\xF0","\\eth",!0),n(k,u,v,"\xF0","\xF0"),n(l,d,v,"\u2571","\\diagup"),n(l,d,v,"\u2572","\\diagdown"),n(l,d,v,"\u25A1","\\square"),n(l,d,v,"\u25A1","\\Box"),n(l,d,v,"\u25CA","\\Diamond"),n(l,d,v,"\xA5","\\yen",!0),n(k,d,v,"\xA5","\\yen",!0),n(l,d,v,"\u2713","\\checkmark",!0),n(k,d,v,"\u2713","\\checkmark"),n(l,d,v,"\u2136","\\beth",!0),n(l,d,v,"\u2138","\\daleth",!0),n(l,d,v,"\u2137","\\gimel",!0),n(l,d,v,"\u03DD","\\digamma",!0),n(l,d,v,"\u03F0","\\varkappa"),n(l,d,o0,"\u250C","\\@ulcorner",!0),n(l,d,a0,"\u2510","\\@urcorner",!0),n(l,d,o0,"\u2514","\\@llcorner",!0),n(l,d,a0,"\u2518","\\@lrcorner",!0),n(l,d,f,"\u2266","\\leqq",!0),n(l,d,f,"\u2A7D","\\leqslant",!0),n(l,d,f,"\u2A95","\\eqslantless",!0),n(l,d,f,"\u2272","\\lesssim",!0),n(l,d,f,"\u2A85","\\lessapprox",!0),n(l,d,f,"\u224A","\\approxeq",!0),n(l,d,D,"\u22D6","\\lessdot"),n(l,d,f,"\u22D8","\\lll",!0),n(l,d,f,"\u2276","\\lessgtr",!0),n(l,d,f,"\u22DA","\\lesseqgtr",!0),n(l,d,f,"\u2A8B","\\lesseqqgtr",!0),n(l,d,f,"\u2251","\\doteqdot"),n(l,d,f,"\u2253","\\risingdotseq",!0),n(l,d,f,"\u2252","\\fallingdotseq",!0),n(l,d,f,"\u223D","\\backsim",!0),n(l,d,f,"\u22CD","\\backsimeq",!0),n(l,d,f,"\u2AC5","\\subseteqq",!0),n(l,d,f,"\u22D0","\\Subset",!0),n(l,d,f,"\u228F","\\sqsubset",!0),n(l,d,f,"\u227C","\\preccurlyeq",!0),n(l,d,f,"\u22DE","\\curlyeqprec",!0),n(l,d,f,"\u227E","\\precsim",!0),n(l,d,f,"\u2AB7","\\precapprox",!0),n(l,d,f,"\u22B2","\\vartriangleleft"),n(l,d,f,"\u22B4","\\trianglelefteq"),n(l,d,f,"\u22A8","\\vDash",!0),n(l,d,f,"\u22AA","\\Vvdash",!0),n(l,d,f,"\u2323","\\smallsmile"),n(l,d,f,"\u2322","\\smallfrown"),n(l,d,f,"\u224F","\\bumpeq",!0),n(l,d,f,"\u224E","\\Bumpeq",!0),n(l,d,f,"\u2267","\\geqq",!0),n(l,d,f,"\u2A7E","\\geqslant",!0),n(l,d,f,"\u2A96","\\eqslantgtr",!0),n(l,d,f,"\u2273","\\gtrsim",!0),n(l,d,f,"\u2A86","\\gtrapprox",!0),n(l,d,D,"\u22D7","\\gtrdot"),n(l,d,f,"\u22D9","\\ggg",!0),n(l,d,f,"\u2277","\\gtrless",!0),n(l,d,f,"\u22DB","\\gtreqless",!0),n(l,d,f,"\u2A8C","\\gtreqqless",!0),n(l,d,f,"\u2256","\\eqcirc",!0),n(l,d,f,"\u2257","\\circeq",!0),n(l,d,f,"\u225C","\\triangleq",!0),n(l,d,f,"\u223C","\\thicksim"),n(l,d,f,"\u2248","\\thickapprox"),n(l,d,f,"\u2AC6","\\supseteqq",!0),n(l,d,f,"\u22D1","\\Supset",!0),n(l,d,f,"\u2290","\\sqsupset",!0),n(l,d,f,"\u227D","\\succcurlyeq",!0),n(l,d,f,"\u22DF","\\curlyeqsucc",!0),n(l,d,f,"\u227F","\\succsim",!0),n(l,d,f,"\u2AB8","\\succapprox",!0),n(l,d,f,"\u22B3","\\vartriangleright"),n(l,d,f,"\u22B5","\\trianglerighteq"),n(l,d,f,"\u22A9","\\Vdash",!0),n(l,d,f,"\u2223","\\shortmid"),n(l,d,f,"\u2225","\\shortparallel"),n(l,d,f,"\u226C","\\between",!0),n(l,d,f,"\u22D4","\\pitchfork",!0),n(l,d,f,"\u221D","\\varpropto"),n(l,d,f,"\u25C0","\\blacktriangleleft"),n(l,d,f,"\u2234","\\therefore",!0),n(l,d,f,"\u220D","\\backepsilon"),n(l,d,f,"\u25B6","\\blacktriangleright"),n(l,d,f,"\u2235","\\because",!0),n(l,d,f,"\u22D8","\\llless"),n(l,d,f,"\u22D9","\\gggtr"),n(l,d,D,"\u22B2","\\lhd"),n(l,d,D,"\u22B3","\\rhd"),n(l,d,f,"\u2242","\\eqsim",!0),n(l,u,f,"\u22C8","\\Join"),n(l,d,f,"\u2251","\\Doteq",!0),n(l,d,D,"\u2214","\\dotplus",!0),n(l,d,D,"\u2216","\\smallsetminus"),n(l,d,D,"\u22D2","\\Cap",!0),n(l,d,D,"\u22D3","\\Cup",!0),n(l,d,D,"\u2A5E","\\doublebarwedge",!0),n(l,d,D,"\u229F","\\boxminus",!0),n(l,d,D,"\u229E","\\boxplus",!0),n(l,d,D,"\u22C7","\\divideontimes",!0),n(l,d,D,"\u22C9","\\ltimes",!0),n(l,d,D,"\u22CA","\\rtimes",!0),n(l,d,D,"\u22CB","\\leftthreetimes",!0),n(l,d,D,"\u22CC","\\rightthreetimes",!0),n(l,d,D,"\u22CF","\\curlywedge",!0),n(l,d,D,"\u22CE","\\curlyvee",!0),n(l,d,D,"\u229D","\\circleddash",!0),n(l,d,D,"\u229B","\\circledast",!0),n(l,d,D,"\u22C5","\\centerdot"),n(l,d,D,"\u22BA","\\intercal",!0),n(l,d,D,"\u22D2","\\doublecap"),n(l,d,D,"\u22D3","\\doublecup"),n(l,d,D,"\u22A0","\\boxtimes",!0),n(l,d,f,"\u21E2","\\dashrightarrow",!0),n(l,d,f,"\u21E0","\\dashleftarrow",!0),n(l,d,f,"\u21C7","\\leftleftarrows",!0),n(l,d,f,"\u21C6","\\leftrightarrows",!0),n(l,d,f,"\u21DA","\\Lleftarrow",!0),n(l,d,f,"\u219E","\\twoheadleftarrow",!0),n(l,d,f,"\u21A2","\\leftarrowtail",!0),n(l,d,f,"\u21AB","\\looparrowleft",!0),n(l,d,f,"\u21CB","\\leftrightharpoons",!0),n(l,d,f,"\u21B6","\\curvearrowleft",!0),n(l,d,f,"\u21BA","\\circlearrowleft",!0),n(l,d,f,"\u21B0","\\Lsh",!0),n(l,d,f,"\u21C8","\\upuparrows",!0),n(l,d,f,"\u21BF","\\upharpoonleft",!0),n(l,d,f,"\u21C3","\\downharpoonleft",!0),n(l,u,f,"\u22B6","\\origof",!0),n(l,u,f,"\u22B7","\\imageof",!0),n(l,d,f,"\u22B8","\\multimap",!0),n(l,d,f,"\u21AD","\\leftrightsquigarrow",!0),n(l,d,f,"\u21C9","\\rightrightarrows",!0),n(l,d,f,"\u21C4","\\rightleftarrows",!0),n(l,d,f,"\u21A0","\\twoheadrightarrow",!0),n(l,d,f,"\u21A3","\\rightarrowtail",!0),n(l,d,f,"\u21AC","\\looparrowright",!0),n(l,d,f,"\u21B7","\\curvearrowright",!0),n(l,d,f,"\u21BB","\\circlearrowright",!0),n(l,d,f,"\u21B1","\\Rsh",!0),n(l,d,f,"\u21CA","\\downdownarrows",!0),n(l,d,f,"\u21BE","\\upharpoonright",!0),n(l,d,f,"\u21C2","\\downharpoonright",!0),n(l,d,f,"\u21DD","\\rightsquigarrow",!0),n(l,d,f,"\u21DD","\\leadsto"),n(l,d,f,"\u21DB","\\Rrightarrow",!0),n(l,d,f,"\u21BE","\\restriction"),n(l,u,v,"\u2018","`"),n(l,u,v,"$","\\$"),n(k,u,v,"$","\\$"),n(k,u,v,"$","\\textdollar"),n(l,u,v,"%","\\%"),n(k,u,v,"%","\\%"),n(l,u,v,"_","\\_"),n(k,u,v,"_","\\_"),n(k,u,v,"_","\\textunderscore"),n(l,u,v,"\u2220","\\angle",!0),n(l,u,v,"\u221E","\\infty",!0),n(l,u,v,"\u2032","\\prime"),n(l,u,v,"\u25B3","\\triangle"),n(l,u,v,"\u0393","\\Gamma",!0),n(l,u,v,"\u0394","\\Delta",!0),n(l,u,v,"\u0398","\\Theta",!0),n(l,u,v,"\u039B","\\Lambda",!0),n(l,u,v,"\u039E","\\Xi",!0),n(l,u,v,"\u03A0","\\Pi",!0),n(l,u,v,"\u03A3","\\Sigma",!0),n(l,u,v,"\u03A5","\\Upsilon",!0),n(l,u,v,"\u03A6","\\Phi",!0),n(l,u,v,"\u03A8","\\Psi",!0),n(l,u,v,"\u03A9","\\Omega",!0),n(l,u,v,"A","\u0391"),n(l,u,v,"B","\u0392"),n(l,u,v,"E","\u0395"),n(l,u,v,"Z","\u0396"),n(l,u,v,"H","\u0397"),n(l,u,v,"I","\u0399"),n(l,u,v,"K","\u039A"),n(l,u,v,"M","\u039C"),n(l,u,v,"N","\u039D"),n(l,u,v,"O","\u039F"),n(l,u,v,"P","\u03A1"),n(l,u,v,"T","\u03A4"),n(l,u,v,"X","\u03A7"),n(l,u,v,"\xAC","\\neg",!0),n(l,u,v,"\xAC","\\lnot"),n(l,u,v,"\u22A4","\\top"),n(l,u,v,"\u22A5","\\bot"),n(l,u,v,"\u2205","\\emptyset"),n(l,d,v,"\u2205","\\varnothing"),n(l,u,E,"\u03B1","\\alpha",!0),n(l,u,E,"\u03B2","\\beta",!0),n(l,u,E,"\u03B3","\\gamma",!0),n(l,u,E,"\u03B4","\\delta",!0),n(l,u,E,"\u03F5","\\epsilon",!0),n(l,u,E,"\u03B6","\\zeta",!0),n(l,u,E,"\u03B7","\\eta",!0),n(l,u,E,"\u03B8","\\theta",!0),n(l,u,E,"\u03B9","\\iota",!0),n(l,u,E,"\u03BA","\\kappa",!0),n(l,u,E,"\u03BB","\\lambda",!0),n(l,u,E,"\u03BC","\\mu",!0),n(l,u,E,"\u03BD","\\nu",!0),n(l,u,E,"\u03BE","\\xi",!0),n(l,u,E,"\u03BF","\\omicron",!0),n(l,u,E,"\u03C0","\\pi",!0),n(l,u,E,"\u03C1","\\rho",!0),n(l,u,E,"\u03C3","\\sigma",!0),n(l,u,E,"\u03C4","\\tau",!0),n(l,u,E,"\u03C5","\\upsilon",!0),n(l,u,E,"\u03D5","\\phi",!0),n(l,u,E,"\u03C7","\\chi",!0),n(l,u,E,"\u03C8","\\psi",!0),n(l,u,E,"\u03C9","\\omega",!0),n(l,u,E,"\u03B5","\\varepsilon",!0),n(l,u,E,"\u03D1","\\vartheta",!0),n(l,u,E,"\u03D6","\\varpi",!0),n(l,u,E,"\u03F1","\\varrho",!0),n(l,u,E,"\u03C2","\\varsigma",!0),n(l,u,E,"\u03C6","\\varphi",!0),n(l,u,D,"\u2217","*",!0),n(l,u,D,"+","+"),n(l,u,D,"\u2212","-",!0),n(l,u,D,"\u22C5","\\cdot",!0),n(l,u,D,"\u2218","\\circ",!0),n(l,u,D,"\xF7","\\div",!0),n(l,u,D,"\xB1","\\pm",!0),n(l,u,D,"\xD7","\\times",!0),n(l,u,D,"\u2229","\\cap",!0),n(l,u,D,"\u222A","\\cup",!0),n(l,u,D,"\u2216","\\setminus",!0),n(l,u,D,"\u2227","\\land"),n(l,u,D,"\u2228","\\lor"),n(l,u,D,"\u2227","\\wedge",!0),n(l,u,D,"\u2228","\\vee",!0),n(l,u,v,"\u221A","\\surd"),n(l,u,o0,"\u27E8","\\langle",!0),n(l,u,o0,"\u2223","\\lvert"),n(l,u,o0,"\u2225","\\lVert"),n(l,u,a0,"?","?"),n(l,u,a0,"!","!"),n(l,u,a0,"\u27E9","\\rangle",!0),n(l,u,a0,"\u2223","\\rvert"),n(l,u,a0,"\u2225","\\rVert"),n(l,u,f,"=","="),n(l,u,f,":",":"),n(l,u,f,"\u2248","\\approx",!0),n(l,u,f,"\u2245","\\cong",!0),n(l,u,f,"\u2265","\\ge"),n(l,u,f,"\u2265","\\geq",!0),n(l,u,f,"\u2190","\\gets"),n(l,u,f,">","\\gt",!0),n(l,u,f,"\u2208","\\in",!0),n(l,u,f,"\uE020","\\@not"),n(l,u,f,"\u2282","\\subset",!0),n(l,u,f,"\u2283","\\supset",!0),n(l,u,f,"\u2286","\\subseteq",!0),n(l,u,f,"\u2287","\\supseteq",!0),n(l,d,f,"\u2288","\\nsubseteq",!0),n(l,d,f,"\u2289","\\nsupseteq",!0),n(l,u,f,"\u22A8","\\models"),n(l,u,f,"\u2190","\\leftarrow",!0),n(l,u,f,"\u2264","\\le"),n(l,u,f,"\u2264","\\leq",!0),n(l,u,f,"<","\\lt",!0),n(l,u,f,"\u2192","\\rightarrow",!0),n(l,u,f,"\u2192","\\to"),n(l,d,f,"\u2271","\\ngeq",!0),n(l,d,f,"\u2270","\\nleq",!0),n(l,u,M0,"\xA0","\\ "),n(l,u,M0,"\xA0","\\space"),n(l,u,M0,"\xA0","\\nobreakspace"),n(k,u,M0,"\xA0","\\ "),n(k,u,M0,"\xA0"," "),n(k,u,M0,"\xA0","\\space"),n(k,u,M0,"\xA0","\\nobreakspace"),n(l,u,M0,null,"\\nobreak"),n(l,u,M0,null,"\\allowbreak"),n(l,u,de,",",","),n(l,u,de,";",";"),n(l,d,D,"\u22BC","\\barwedge",!0),n(l,d,D,"\u22BB","\\veebar",!0),n(l,u,D,"\u2299","\\odot",!0),n(l,u,D,"\u2295","\\oplus",!0),n(l,u,D,"\u2297","\\otimes",!0),n(l,u,v,"\u2202","\\partial",!0),n(l,u,D,"\u2298","\\oslash",!0),n(l,d,D,"\u229A","\\circledcirc",!0),n(l,d,D,"\u22A1","\\boxdot",!0),n(l,u,D,"\u25B3","\\bigtriangleup"),n(l,u,D,"\u25BD","\\bigtriangledown"),n(l,u,D,"\u2020","\\dagger"),n(l,u,D,"\u22C4","\\diamond"),n(l,u,D,"\u22C6","\\star"),n(l,u,D,"\u25C3","\\triangleleft"),n(l,u,D,"\u25B9","\\triangleright"),n(l,u,o0,"{","\\{"),n(k,u,v,"{","\\{"),n(k,u,v,"{","\\textbraceleft"),n(l,u,a0,"}","\\}"),n(k,u,v,"}","\\}"),n(k,u,v,"}","\\textbraceright"),n(l,u,o0,"{","\\lbrace"),n(l,u,a0,"}","\\rbrace"),n(l,u,o0,"[","\\lbrack",!0),n(k,u,v,"[","\\lbrack",!0),n(l,u,a0,"]","\\rbrack",!0),n(k,u,v,"]","\\rbrack",!0),n(l,u,o0,"(","\\lparen",!0),n(l,u,a0,")","\\rparen",!0),n(k,u,v,"<","\\textless",!0),n(k,u,v,">","\\textgreater",!0),n(l,u,o0,"\u230A","\\lfloor",!0),n(l,u,a0,"\u230B","\\rfloor",!0),n(l,u,o0,"\u2308","\\lceil",!0),n(l,u,a0,"\u2309","\\rceil",!0),n(l,u,v,"\\","\\backslash"),n(l,u,v,"\u2223","|"),n(l,u,v,"\u2223","\\vert"),n(k,u,v,"|","\\textbar",!0),n(l,u,v,"\u2225","\\|"),n(l,u,v,"\u2225","\\Vert"),n(k,u,v,"\u2225","\\textbardbl"),n(k,u,v,"~","\\textasciitilde"),n(k,u,v,"\\","\\textbackslash"),n(k,u,v,"^","\\textasciicircum"),n(l,u,f,"\u2191","\\uparrow",!0),n(l,u,f,"\u21D1","\\Uparrow",!0),n(l,u,f,"\u2193","\\downarrow",!0),n(l,u,f,"\u21D3","\\Downarrow",!0),n(l,u,f,"\u2195","\\updownarrow",!0),n(l,u,f,"\u21D5","\\Updownarrow",!0),n(l,u,J,"\u2210","\\coprod"),n(l,u,J,"\u22C1","\\bigvee"),n(l,u,J,"\u22C0","\\bigwedge"),n(l,u,J,"\u2A04","\\biguplus"),n(l,u,J,"\u22C2","\\bigcap"),n(l,u,J,"\u22C3","\\bigcup"),n(l,u,J,"\u222B","\\int"),n(l,u,J,"\u222B","\\intop"),n(l,u,J,"\u222C","\\iint"),n(l,u,J,"\u222D","\\iiint"),n(l,u,J,"\u220F","\\prod"),n(l,u,J,"\u2211","\\sum"),n(l,u,J,"\u2A02","\\bigotimes"),n(l,u,J,"\u2A01","\\bigoplus"),n(l,u,J,"\u2A00","\\bigodot"),n(l,u,J,"\u222E","\\oint"),n(l,u,J,"\u222F","\\oiint"),n(l,u,J,"\u2230","\\oiiint"),n(l,u,J,"\u2A06","\\bigsqcup"),n(l,u,J,"\u222B","\\smallint"),n(k,u,W0,"\u2026","\\textellipsis"),n(l,u,W0,"\u2026","\\mathellipsis"),n(k,u,W0,"\u2026","\\ldots",!0),n(l,u,W0,"\u2026","\\ldots",!0),n(l,u,W0,"\u22EF","\\@cdots",!0),n(l,u,W0,"\u22F1","\\ddots",!0),n(l,u,v,"\u22EE","\\varvdots"),n(l,u,$,"\u02CA","\\acute"),n(l,u,$,"\u02CB","\\grave"),n(l,u,$,"\xA8","\\ddot"),n(l,u,$,"~","\\tilde"),n(l,u,$,"\u02C9","\\bar"),n(l,u,$,"\u02D8","\\breve"),n(l,u,$,"\u02C7","\\check"),n(l,u,$,"^","\\hat"),n(l,u,$,"\u20D7","\\vec"),n(l,u,$,"\u02D9","\\dot"),n(l,u,$,"\u02DA","\\mathring"),n(l,u,E,"\uE131","\\@imath"),n(l,u,E,"\uE237","\\@jmath"),n(l,u,v,"\u0131","\u0131"),n(l,u,v,"\u0237","\u0237"),n(k,u,v,"\u0131","\\i",!0),n(k,u,v,"\u0237","\\j",!0),n(k,u,v,"\xDF","\\ss",!0),n(k,u,v,"\xE6","\\ae",!0),n(k,u,v,"\u0153","\\oe",!0),n(k,u,v,"\xF8","\\o",!0),n(k,u,v,"\xC6","\\AE",!0),n(k,u,v,"\u0152","\\OE",!0),n(k,u,v,"\xD8","\\O",!0),n(k,u,$,"\u02CA","\\'"),n(k,u,$,"\u02CB","\\`"),n(k,u,$,"\u02C6","\\^"),n(k,u,$,"\u02DC","\\~"),n(k,u,$,"\u02C9","\\="),n(k,u,$,"\u02D8","\\u"),n(k,u,$,"\u02D9","\\."),n(k,u,$,"\xB8","\\c"),n(k,u,$,"\u02DA","\\r"),n(k,u,$,"\u02C7","\\v"),n(k,u,$,"\xA8",'\\"'),n(k,u,$,"\u02DD","\\H"),n(k,u,$,"\u25EF","\\textcircled");var Pt={"--":!0,"---":!0,"``":!0,"''":!0};n(k,u,v,"\u2013","--",!0),n(k,u,v,"\u2013","\\textendash"),n(k,u,v,"\u2014","---",!0),n(k,u,v,"\u2014","\\textemdash"),n(k,u,v,"\u2018","`",!0),n(k,u,v,"\u2018","\\textquoteleft"),n(k,u,v,"\u2019","'",!0),n(k,u,v,"\u2019","\\textquoteright"),n(k,u,v,"\u201C","``",!0),n(k,u,v,"\u201C","\\textquotedblleft"),n(k,u,v,"\u201D","''",!0),n(k,u,v,"\u201D","\\textquotedblright"),n(l,u,v,"\xB0","\\degree",!0),n(k,u,v,"\xB0","\\degree"),n(k,u,v,"\xB0","\\textdegree",!0),n(l,u,v,"\xA3","\\pounds"),n(l,u,v,"\xA3","\\mathsterling",!0),n(k,u,v,"\xA3","\\pounds"),n(k,u,v,"\xA3","\\textsterling",!0),n(l,d,v,"\u2720","\\maltese"),n(k,d,v,"\u2720","\\maltese");for(var Gt='0123456789/@."',Ve=0;Ve<Gt.length;Ve++){var Vt=Gt.charAt(Ve);n(l,u,v,Vt,Vt)}for(var Ut='0123456789!@*()-=+";:?/.,',Ue=0;Ue<Ut.length;Ue++){var Yt=Ut.charAt(Ue);n(k,u,v,Yt,Yt)}for(var fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",Ye=0;Ye<fe.length;Ye++){var pe=fe.charAt(Ye);n(l,u,E,pe,pe),n(k,u,v,pe,pe)}n(l,d,v,"C","\u2102"),n(k,d,v,"C","\u2102"),n(l,d,v,"H","\u210D"),n(k,d,v,"H","\u210D"),n(l,d,v,"N","\u2115"),n(k,d,v,"N","\u2115"),n(l,d,v,"P","\u2119"),n(k,d,v,"P","\u2119"),n(l,d,v,"Q","\u211A"),n(k,d,v,"Q","\u211A"),n(l,d,v,"R","\u211D"),n(k,d,v,"R","\u211D"),n(l,d,v,"Z","\u2124"),n(k,d,v,"Z","\u2124"),n(l,u,E,"h","\u210E"),n(k,u,E,"h","\u210E");for(var O="",n0=0;n0<fe.length;n0++){var Q=fe.charAt(n0);O=String.fromCharCode(55349,56320+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),O=String.fromCharCode(55349,56372+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),O=String.fromCharCode(55349,56424+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),O=String.fromCharCode(55349,56580+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),O=String.fromCharCode(55349,56736+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),O=String.fromCharCode(55349,56788+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),O=String.fromCharCode(55349,56840+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),O=String.fromCharCode(55349,56944+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),n0<26&&(O=String.fromCharCode(55349,56632+n0),n(l,u,E,Q,O),n(k,u,v,Q,O),O=String.fromCharCode(55349,56476+n0),n(l,u,E,Q,O),n(k,u,v,Q,O))}O=String.fromCharCode(55349,56668),n(l,u,E,"k",O),n(k,u,v,"k",O);for(var G0=0;G0<10;G0++){var I0=G0.toString();O=String.fromCharCode(55349,57294+G0),n(l,u,E,I0,O),n(k,u,v,I0,O),O=String.fromCharCode(55349,57314+G0),n(l,u,E,I0,O),n(k,u,v,I0,O),O=String.fromCharCode(55349,57324+G0),n(l,u,E,I0,O),n(k,u,v,I0,O),O=String.fromCharCode(55349,57334+G0),n(l,u,E,I0,O),n(k,u,v,I0,O)}for(var Xe="\xD0\xDE\xFE",$e=0;$e<Xe.length;$e++){var ve=Xe.charAt($e);n(l,u,E,ve,ve),n(k,u,v,ve,ve)}var ge=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["","",""],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Xt=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],$a=function(e,t){var a=e.charCodeAt(0),i=e.charCodeAt(1),s=(a-55296)*1024+(i-56320)+65536,o=t==="math"?0:1;if(119808<=s&&s<120484){var h=Math.floor((s-119808)/26);return[ge[h][2],ge[h][o]]}else if(120782<=s&&s<=120831){var c=Math.floor((s-120782)/10);return[Xt[c][2],Xt[c][o]]}else{if(s===120485||s===120486)return[ge[0][2],ge[0][o]];if(120486<s&&s<120782)return["",""];throw new M("Unsupported character: "+e)}},be=function(e,t,a){return X[a][e]&&X[a][e].replace&&(e=X[a][e].replace),{value:e,metrics:He(e,t,a)}},v0=function(e,t,a,i,s){var o=be(e,t,a),h=o.metrics;e=o.value;var c;if(h){var p=h.italic;(a==="text"||i&&i.font==="mathit")&&(p=0),c=new c0(e,h.height,h.depth,p,h.skew,h.width,s)}else typeof console!="undefined"&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '"+a+"'")),c=new c0(e,0,0,0,0,0,s);if(i){c.maxFontSize=i.sizeMultiplier,i.style.isTight()&&c.classes.push("mtight");var g=i.getColor();g&&(c.style.color=g)}return c},Wa=function(e,t,a,i){return i===void 0&&(i=[]),a.font==="boldsymbol"&&be(e,"Main-Bold",t).metrics?v0(e,"Main-Bold",t,a,i.concat(["mathbf"])):e==="\\"||X[t][e].font==="main"?v0(e,"Main-Regular",t,a,i):v0(e,"AMS-Regular",t,a,i.concat(["amsrm"]))},ja=function(e,t,a,i,s){return s!=="textord"&&be(e,"Math-BoldItalic",t).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},Za=function(e,t,a){var i=e.mode,s=e.text,o=["mord"],h=i==="math"||i==="text"&&t.font,c=h?t.font:t.fontFamily;if(s.charCodeAt(0)===55349){var[p,g]=$a(s,i);return v0(s,p,i,t,o.concat(g))}else if(c){var b,x;if(c==="boldsymbol"){var w=ja(s,i,t,o,a);b=w.fontName,x=[w.fontClass]}else h?(b=jt[c].fontName,x=[c]):(b=ye(c,t.fontWeight,t.fontShape),x=[c,t.fontWeight,t.fontShape]);if(be(s,b,i).metrics)return v0(s,b,i,t,o.concat(x));if(Pt.hasOwnProperty(s)&&b.substr(0,10)==="Typewriter"){for(var A=[],T=0;T<s.length;T++)A.push(v0(s[T],b,i,t,o.concat(x)));return Wt(A)}}if(a==="mathord")return v0(s,"Math-Italic",i,t,o.concat(["mathnormal"]));if(a==="textord"){var N=X[i][s]&&X[i][s].font;if(N==="ams"){var C=ye("amsrm",t.fontWeight,t.fontShape);return v0(s,C,i,t,o.concat("amsrm",t.fontWeight,t.fontShape))}else if(N==="main"||!N){var I=ye("textrm",t.fontWeight,t.fontShape);return v0(s,I,i,t,o.concat(t.fontWeight,t.fontShape))}else{var L=ye(N,t.fontWeight,t.fontShape);return v0(s,L,i,t,o.concat(L,t.fontWeight,t.fontShape))}}else throw new Error("unexpected type: "+a+" in makeOrd")},Ka=(r,e)=>{if(E0(r.classes)!==E0(e.classes)||r.skew!==e.skew||r.maxFontSize!==e.maxFontSize)return!1;if(r.classes.length===1){var t=r.classes[0];if(t==="mbin"||t==="mord")return!1}for(var a in r.style)if(r.style.hasOwnProperty(a)&&r.style[a]!==e.style[a])return!1;for(var i in e.style)if(e.style.hasOwnProperty(i)&&r.style[i]!==e.style[i])return!1;return!0},Ja=r=>{for(var e=0;e<r.length-1;e++){var t=r[e],a=r[e+1];t instanceof c0&&a instanceof c0&&Ka(t,a)&&(t.text+=a.text,t.height=Math.max(t.height,a.height),t.depth=Math.max(t.depth,a.depth),t.italic=a.italic,r.splice(e+1,1),e--)}return r},We=function(e){for(var t=0,a=0,i=0,s=0;s<e.children.length;s++){var o=e.children[s];o.height>t&&(t=o.height),o.depth>a&&(a=o.depth),o.maxFontSize>i&&(i=o.maxFontSize)}e.height=t,e.depth=a,e.maxFontSize=i},s0=function(e,t,a,i){var s=new re(e,t,a,i);return We(s),s},$t=(r,e,t,a)=>new re(r,e,t,a),Qa=function(e,t,a){var i=s0([e],[],t);return i.height=Math.max(a||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),i.style.borderBottomWidth=z(i.height),i.maxFontSize=1,i},_a=function(e,t,a,i){var s=new Pe(e,t,a,i);return We(s),s},Wt=function(e){var t=new te(e);return We(t),t},e1=function(e,t){return e instanceof te?s0([],[e],t):e},t1=function(e){if(e.positionType==="individualShift"){for(var t=e.children,a=[t[0]],i=-t[0].shift-t[0].elem.depth,s=i,o=1;o<t.length;o++){var h=-t[o].shift-s-t[o].elem.depth,c=h-(t[o-1].elem.height+t[o-1].elem.depth);s=s+h,a.push({type:"kern",size:c}),a.push(t[o])}return{children:a,depth:i}}var p;if(e.positionType==="top"){for(var g=e.positionData,b=0;b<e.children.length;b++){var x=e.children[b];g-=x.type==="kern"?x.size:x.elem.height+x.elem.depth}p=g}else if(e.positionType==="bottom")p=-e.positionData;else{var w=e.children[0];if(w.type!=="elem")throw new Error('First child must have type "elem".');if(e.positionType==="shift")p=-w.elem.depth-e.positionData;else if(e.positionType==="firstBaseline")p=-w.elem.depth;else throw new Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:p}},r1=function(e,t){for(var{children:a,depth:i}=t1(e),s=0,o=0;o<a.length;o++){var h=a[o];if(h.type==="elem"){var c=h.elem;s=Math.max(s,c.maxFontSize,c.height)}}s+=2;var p=s0(["pstrut"],[]);p.style.height=z(s);for(var g=[],b=i,x=i,w=i,A=0;A<a.length;A++){var T=a[A];if(T.type==="kern")w+=T.size;else{var N=T.elem,C=T.wrapperClasses||[],I=T.wrapperStyle||{},L=s0(C,[p,N],void 0,I);L.style.top=z(-s-w-N.depth),T.marginLeft&&(L.style.marginLeft=T.marginLeft),T.marginRight&&(L.style.marginRight=T.marginRight),g.push(L),w+=N.height+N.depth}b=Math.min(b,w),x=Math.max(x,w)}var V=s0(["vlist"],g);V.style.height=z(x);var F;if(b<0){var U=s0([],[]),G=s0(["vlist"],[U]);G.style.height=z(-b);var W=s0(["vlist-s"],[new c0("\u200B")]);F=[s0(["vlist-r"],[V,W]),s0(["vlist-r"],[G])]}else F=[s0(["vlist-r"],[V])];var j=s0(["vlist-t"],F);return F.length===2&&j.classes.push("vlist-t2"),j.height=x,j.depth=-b,j},a1=(r,e)=>{var t=s0(["mspace"],[],e),a=Z(r,e);return t.style.marginRight=z(a),t},ye=function(e,t,a){var i="";switch(e){case"amsrm":i="AMS";break;case"textrm":i="Main";break;case"textsf":i="SansSerif";break;case"texttt":i="Typewriter";break;default:i=e}var s;return t==="textbf"&&a==="textit"?s="BoldItalic":t==="textbf"?s="Bold":t==="textit"?s="Italic":s="Regular",i+"-"+s},jt={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},Zt={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},i1=function(e,t){var[a,i,s]=Zt[e],o=new P0(a),h=new R0([o],{width:z(i),height:z(s),style:"width:"+z(i),viewBox:"0 0 "+1e3*i+" "+1e3*s,preserveAspectRatio:"xMinYMin"}),c=$t(["overlay"],[h],t);return c.height=s,c.style.height=z(s),c.style.width=z(i),c},y={fontMap:jt,makeSymbol:v0,mathsym:Wa,makeSpan:s0,makeSvgSpan:$t,makeLineSpan:Qa,makeAnchor:_a,makeFragment:Wt,wrapFragment:e1,makeVList:r1,makeOrd:Za,makeGlue:a1,staticSvg:i1,svgData:Zt,tryCombineChars:Ja},K={number:3,unit:"mu"},V0={number:4,unit:"mu"},z0={number:5,unit:"mu"},n1={mord:{mop:K,mbin:V0,mrel:z0,minner:K},mop:{mord:K,mop:K,mrel:z0,minner:K},mbin:{mord:V0,mop:V0,mopen:V0,minner:V0},mrel:{mord:z0,mop:z0,mopen:z0,minner:z0},mopen:{},mclose:{mop:K,mbin:V0,mrel:z0,minner:K},mpunct:{mord:K,mop:K,mrel:z0,mopen:K,mclose:K,mpunct:K,minner:K},minner:{mord:K,mop:K,mbin:V0,mrel:z0,mopen:K,mpunct:K,minner:K}},s1={mord:{mop:K},mop:{mord:K,mop:K},mbin:{},mrel:{},mopen:{},mclose:{mop:K},mpunct:{},minner:{mop:K}},Kt={},xe={},we={};function B(r){for(var{type:e,names:t,props:a,handler:i,htmlBuilder:s,mathmlBuilder:o}=r,h={type:e,numArgs:a.numArgs,argTypes:a.argTypes,allowedInArgument:!!a.allowedInArgument,allowedInText:!!a.allowedInText,allowedInMath:a.allowedInMath===void 0?!0:a.allowedInMath,numOptionalArgs:a.numOptionalArgs||0,infix:!!a.infix,primitive:!!a.primitive,handler:i},c=0;c<t.length;++c)Kt[t[c]]=h;e&&(s&&(xe[e]=s),o&&(we[e]=o))}function U0(r){var{type:e,htmlBuilder:t,mathmlBuilder:a}=r;B({type:e,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:t,mathmlBuilder:a})}var ke=function(e){return e.type==="ordgroup"&&e.body.length===1?e.body[0]:e},_=function(e){return e.type==="ordgroup"?e.body:[e]},A0=y.makeSpan,l1=["leftmost","mbin","mopen","mrel","mop","mpunct"],o1=["rightmost","mrel","mclose","mpunct"],u1={display:q.DISPLAY,text:q.TEXT,script:q.SCRIPT,scriptscript:q.SCRIPTSCRIPT},h1={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},e0=function(e,t,a,i){i===void 0&&(i=[null,null]);for(var s=[],o=0;o<e.length;o++){var h=P(e[o],t);if(h instanceof te){var c=h.children;s.push(...c)}else s.push(h)}if(y.tryCombineChars(s),!a)return s;var p=t;if(e.length===1){var g=e[0];g.type==="sizing"?p=t.havingSize(g.size):g.type==="styling"&&(p=t.havingStyle(u1[g.style]))}var b=A0([i[0]||"leftmost"],[],t),x=A0([i[1]||"rightmost"],[],t),w=a==="root";return Jt(s,(A,T)=>{var N=T.classes[0],C=A.classes[0];N==="mbin"&&R.contains(o1,C)?T.classes[0]="mord":C==="mbin"&&R.contains(l1,N)&&(A.classes[0]="mord")},{node:b},x,w),Jt(s,(A,T)=>{var N=je(T),C=je(A),I=N&&C?A.hasClass("mtight")?s1[N][C]:n1[N][C]:null;if(I)return y.makeGlue(I,p)},{node:b},x,w),s},Jt=function r(e,t,a,i,s){i&&e.push(i);for(var o=0;o<e.length;o++){var h=e[o],c=Qt(h);if(c){r(c.children,t,a,null,s);continue}var p=!h.hasClass("mspace");if(p){var g=t(h,a.node);g&&(a.insertAfter?a.insertAfter(g):(e.unshift(g),o++))}p?a.node=h:s&&h.hasClass("newline")&&(a.node=A0(["leftmost"])),a.insertAfter=(b=>x=>{e.splice(b+1,0,x),o++})(o)}i&&e.pop()},Qt=function(e){return e instanceof te||e instanceof Pe||e instanceof re&&e.hasClass("enclosing")?e:null},m1=function r(e,t){var a=Qt(e);if(a){var i=a.children;if(i.length){if(t==="right")return r(i[i.length-1],"right");if(t==="left")return r(i[0],"left")}}return e},je=function(e,t){return e?(t&&(e=m1(e,t)),h1[e.classes[0]]||null):null},ae=function(e,t){var a=["nulldelimiter"].concat(e.baseSizingClasses());return A0(t.concat(a))},P=function(e,t,a){if(!e)return A0();if(xe[e.type]){var i=xe[e.type](e,t);if(a&&t.size!==a.size){i=A0(t.sizingClasses(a),[i],t);var s=t.sizeMultiplier/a.sizeMultiplier;i.height*=s,i.depth*=s}return i}else throw new M("Got group of unknown type: '"+e.type+"'")};function Se(r,e){var t=A0(["base"],r,e),a=A0(["strut"]);return a.style.height=z(t.height+t.depth),t.depth&&(a.style.verticalAlign=z(-t.depth)),t.children.unshift(a),t}function Ze(r,e){var t=null;r.length===1&&r[0].type==="tag"&&(t=r[0].tag,r=r[0].body);var a=e0(r,e,"root"),i;a.length===2&&a[1].hasClass("tag")&&(i=a.pop());for(var s=[],o=[],h=0;h<a.length;h++)if(o.push(a[h]),a[h].hasClass("mbin")||a[h].hasClass("mrel")||a[h].hasClass("allowbreak")){for(var c=!1;h<a.length-1&&a[h+1].hasClass("mspace")&&!a[h+1].hasClass("newline");)h++,o.push(a[h]),a[h].hasClass("nobreak")&&(c=!0);c||(s.push(Se(o,e)),o=[])}else a[h].hasClass("newline")&&(o.pop(),o.length>0&&(s.push(Se(o,e)),o=[]),s.push(a[h]));o.length>0&&s.push(Se(o,e));var p;t?(p=Se(e0(t,e,!0)),p.classes=["tag"],s.push(p)):i&&s.push(i);var g=A0(["katex-html"],s);if(g.setAttribute("aria-hidden","true"),p){var b=p.children[0];b.style.height=z(g.height+g.depth),g.depth&&(b.style.verticalAlign=z(-g.depth))}return g}function _t(r){return new te(r)}class d0{constructor(e,t,a){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=a||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=E0(this.classes));for(var a=0;a<this.children.length;a++)e.appendChild(this.children[a].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=R.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+R.escape(E0(this.classes))+'"'),e+=">";for(var a=0;a<this.children.length;a++)e+=this.children[a].toMarkup();return e+="</"+this.type+">",e}toText(){return this.children.map(e=>e.toText()).join("")}}class ie{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return R.escape(this.toText())}toText(){return this.text}}class c1{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character="\u200A":e>=.1666&&e<=.1667?this.character="\u2009":e>=.2222&&e<=.2223?this.character="\u2005":e>=.2777&&e<=.2778?this.character="\u2005\u200A":e>=-.05556&&e<=-.05555?this.character="\u200A\u2063":e>=-.1667&&e<=-.1666?this.character="\u2009\u2063":e>=-.2223&&e<=-.2222?this.character="\u205F\u2063":e>=-.2778&&e<=-.2777?this.character="\u2005\u2063":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",z(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+z(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var S={MathNode:d0,TextNode:ie,SpaceNode:c1,newDocumentFragment:_t},f0=function(e,t,a){return X[t][e]&&X[t][e].replace&&e.charCodeAt(0)!==55349&&!(Pt.hasOwnProperty(e)&&a&&(a.fontFamily&&a.fontFamily.substr(4,2)==="tt"||a.font&&a.font.substr(4,2)==="tt"))&&(e=X[t][e].replace),new S.TextNode(e)},Ke=function(e){return e.length===1?e[0]:new S.MathNode("mrow",e)},Je=function(e,t){if(t.fontFamily==="texttt")return"monospace";if(t.fontFamily==="textsf")return t.fontShape==="textit"&&t.fontWeight==="textbf"?"sans-serif-bold-italic":t.fontShape==="textit"?"sans-serif-italic":t.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(t.fontShape==="textit"&&t.fontWeight==="textbf")return"bold-italic";if(t.fontShape==="textit")return"italic";if(t.fontWeight==="textbf")return"bold";var a=t.font;if(!a||a==="mathnormal")return null;var i=e.mode;if(a==="mathit")return"italic";if(a==="boldsymbol")return e.type==="textord"?"bold":"bold-italic";if(a==="mathbf")return"bold";if(a==="mathbb")return"double-struck";if(a==="mathfrak")return"fraktur";if(a==="mathscr"||a==="mathcal")return"script";if(a==="mathsf")return"sans-serif";if(a==="mathtt")return"monospace";var s=e.text;if(R.contains(["\\imath","\\jmath"],s))return null;X[i][s]&&X[i][s].replace&&(s=X[i][s].replace);var o=y.fontMap[a].fontName;return He(s,o,i)?y.fontMap[a].variant:null},u0=function(e,t,a){if(e.length===1){var i=Y(e[0],t);return a&&i instanceof d0&&i.type==="mo"&&(i.setAttribute("lspace","0em"),i.setAttribute("rspace","0em")),[i]}for(var s=[],o,h=0;h<e.length;h++){var c=Y(e[h],t);if(c instanceof d0&&o instanceof d0){if(c.type==="mtext"&&o.type==="mtext"&&c.getAttribute("mathvariant")===o.getAttribute("mathvariant")){o.children.push(...c.children);continue}else if(c.type==="mn"&&o.type==="mn"){o.children.push(...c.children);continue}else if(c.type==="mi"&&c.children.length===1&&o.type==="mn"){var p=c.children[0];if(p instanceof ie&&p.text==="."){o.children.push(...c.children);continue}}else if(o.type==="mi"&&o.children.length===1){var g=o.children[0];if(g instanceof ie&&g.text==="\u0338"&&(c.type==="mo"||c.type==="mi"||c.type==="mn")){var b=c.children[0];b instanceof ie&&b.text.length>0&&(b.text=b.text.slice(0,1)+"\u0338"+b.text.slice(1),s.pop())}}}s.push(c),o=c}return s},O0=function(e,t,a){return Ke(u0(e,t,a))},Y=function(e,t){if(!e)return new S.MathNode("mrow");if(we[e.type]){var a=we[e.type](e,t);return a}else throw new M("Got group of unknown type: '"+e.type+"'")};function er(r,e,t,a,i){var s=u0(r,t),o;s.length===1&&s[0]instanceof d0&&R.contains(["mrow","mtable"],s[0].type)?o=s[0]:o=new S.MathNode("mrow",s);var h=new S.MathNode("annotation",[new S.TextNode(e)]);h.setAttribute("encoding","application/x-tex");var c=new S.MathNode("semantics",[o,h]),p=new S.MathNode("math",[c]);p.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),a&&p.setAttribute("display","block");var g=i?"katex":"katex-mathml";return y.makeSpan([g],[p])}var tr=function(e){return new S0({style:e.displayMode?q.DISPLAY:q.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},rr=function(e,t){if(t.displayMode){var a=["katex-display"];t.leqno&&a.push("leqno"),t.fleqn&&a.push("fleqn"),e=y.makeSpan(a,[e])}return e},d1=function(e,t,a){var i=tr(a),s;if(a.output==="mathml")return er(e,t,i,a.displayMode,!0);if(a.output==="html"){var o=Ze(e,i);s=y.makeSpan(["katex"],[o])}else{var h=er(e,t,i,a.displayMode,!1),c=Ze(e,i);s=y.makeSpan(["katex"],[h,c])}return rr(s,a)},f1=function(e,t,a){var i=tr(a),s=Ze(e,i),o=y.makeSpan(["katex"],[s]);return rr(o,a)},p1={widehat:"^",widecheck:"\u02C7",widetilde:"~",utilde:"~",overleftarrow:"\u2190",underleftarrow:"\u2190",xleftarrow:"\u2190",overrightarrow:"\u2192",underrightarrow:"\u2192",xrightarrow:"\u2192",underbrace:"\u23DF",overbrace:"\u23DE",overgroup:"\u23E0",undergroup:"\u23E1",overleftrightarrow:"\u2194",underleftrightarrow:"\u2194",xleftrightarrow:"\u2194",Overrightarrow:"\u21D2",xRightarrow:"\u21D2",overleftharpoon:"\u21BC",xleftharpoonup:"\u21BC",overrightharpoon:"\u21C0",xrightharpoonup:"\u21C0",xLeftarrow:"\u21D0",xLeftrightarrow:"\u21D4",xhookleftarrow:"\u21A9",xhookrightarrow:"\u21AA",xmapsto:"\u21A6",xrightharpoondown:"\u21C1",xleftharpoondown:"\u21BD",xrightleftharpoons:"\u21CC",xleftrightharpoons:"\u21CB",xtwoheadleftarrow:"\u219E",xtwoheadrightarrow:"\u21A0",xlongequal:"=",xtofrom:"\u21C4",xrightleftarrows:"\u21C4",xrightequilibrium:"\u21CC",xleftequilibrium:"\u21CB","\\cdrightarrow":"\u2192","\\cdleftarrow":"\u2190","\\cdlongequal":"="},v1=function(e){var t=new S.MathNode("mo",[new S.TextNode(p1[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},g1={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},b1=function(e){return e.type==="ordgroup"?e.body.length:1},y1=function(e,t){function a(){var h=4e5,c=e.label.substr(1);if(R.contains(["widehat","widecheck","widetilde","utilde"],c)){var p=e,g=b1(p.base),b,x,w;if(g>5)c==="widehat"||c==="widecheck"?(b=420,h=2364,w=.42,x=c+"4"):(b=312,h=2340,w=.34,x="tilde4");else{var A=[1,1,2,2,3,3][g];c==="widehat"||c==="widecheck"?(h=[0,1062,2364,2364,2364][A],b=[0,239,300,360,420][A],w=[0,.24,.3,.3,.36,.42][A],x=c+A):(h=[0,600,1033,2339,2340][A],b=[0,260,286,306,312][A],w=[0,.26,.286,.3,.306,.34][A],x="tilde"+A)}var T=new P0(x),N=new R0([T],{width:"100%",height:z(w),viewBox:"0 0 "+h+" "+b,preserveAspectRatio:"none"});return{span:y.makeSvgSpan([],[N],t),minWidth:0,height:w}}else{var C=[],I=g1[c],[L,V,F]=I,U=F/1e3,G=L.length,W,j;if(G===1){var t0=I[3];W=["hide-tail"],j=[t0]}else if(G===2)W=["halfarrow-left","halfarrow-right"],j=["xMinYMin","xMaxYMin"];else if(G===3)W=["brace-left","brace-center","brace-right"],j=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+G+" children.");for(var i0=0;i0<G;i0++){var D0=new P0(L[i0]),F0=new R0([D0],{width:"400em",height:z(U),viewBox:"0 0 "+h+" "+F,preserveAspectRatio:j[i0]+" slice"}),p0=y.makeSvgSpan([W[i0]],[F0],t);if(G===1)return{span:p0,minWidth:V,height:U};p0.style.height=z(U),C.push(p0)}return{span:y.makeSpan(["stretchy"],C,t),minWidth:V,height:U}}}var{span:i,minWidth:s,height:o}=a();return i.height=o,i.style.height=z(o),s>0&&(i.style.minWidth=z(s)),i},x1=function(e,t,a,i,s){var o,h=e.height+e.depth+a+i;if(/fbox|color|angl/.test(t)){if(o=y.makeSpan(["stretchy",t],[],s),t==="fbox"){var c=s.color&&s.getColor();c&&(o.style.borderColor=c)}}else{var p=[];/^[bx]cancel$/.test(t)&&p.push(new Ge({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&p.push(new Ge({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var g=new R0(p,{width:"100%",height:z(h)});o=y.makeSvgSpan([],[g],s)}return o.height=h,o.style.height=z(h),o},T0={encloseSpan:x1,mathMLnode:v1,svgSpan:y1};function H(r,e){if(!r||r.type!==e)throw new Error("Expected node of type "+e+", but got "+(r?"node of type "+r.type:String(r)));return r}function Qe(r){var e=Me(r);if(!e)throw new Error("Expected node of symbol group type, but got "+(r?"node of type "+r.type:String(r)));return e}function Me(r){return r&&(r.type==="atom"||Xa.hasOwnProperty(r.type))?r:null}var _e=(r,e)=>{var t,a,i;r&&r.type==="supsub"?(a=H(r.base,"accent"),t=a.base,r.base=t,i=Ua(P(r,e)),r.base=a):(a=H(r,"accent"),t=a.base);var s=P(t,e.havingCrampedStyle()),o=a.isShifty&&R.isCharacterBox(t),h=0;if(o){var c=R.getBaseElem(t),p=P(c,e.havingCrampedStyle());h=Ft(p).skew}var g=a.label==="\\c",b=g?s.height+s.depth:Math.min(s.height,e.fontMetrics().xHeight),x;if(a.isStretchy)x=T0.svgSpan(a,e),x=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"elem",elem:x,wrapperClasses:["svg-align"],wrapperStyle:h>0?{width:"calc(100% - "+z(2*h)+")",marginLeft:z(2*h)}:void 0}]},e);else{var w,A;a.label==="\\vec"?(w=y.staticSvg("vec",e),A=y.svgData.vec[1]):(w=y.makeOrd({mode:a.mode,text:a.label},e,"textord"),w=Ft(w),w.italic=0,A=w.width,g&&(b+=w.depth)),x=y.makeSpan(["accent-body"],[w]);var T=a.label==="\\textcircled";T&&(x.classes.push("accent-full"),b=s.height);var N=h;T||(N-=A/2),x.style.left=z(N),a.label==="\\textcircled"&&(x.style.top=".2em"),x=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"kern",size:-b},{type:"elem",elem:x}]},e)}var C=y.makeSpan(["mord","accent"],[x],e);return i?(i.children[0]=C,i.height=Math.max(C.height,i.height),i.classes[0]="mord",i):C},ar=(r,e)=>{var t=r.isStretchy?T0.mathMLnode(r.label):new S.MathNode("mo",[f0(r.label,r.mode)]),a=new S.MathNode("mover",[Y(r.base,e),t]);return a.setAttribute("accent","true"),a},w1=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(r=>"\\"+r).join("|"));B({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(r,e)=>{var t=ke(e[0]),a=!w1.test(r.funcName),i=!a||r.funcName==="\\widehat"||r.funcName==="\\widetilde"||r.funcName==="\\widecheck";return{type:"accent",mode:r.parser.mode,label:r.funcName,isStretchy:a,isShifty:i,base:t}},htmlBuilder:_e,mathmlBuilder:ar}),B({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(r,e)=>{var t=e[0],a=r.parser.mode;return a==="math"&&(r.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+r.funcName+" works only in text mode"),a="text"),{type:"accent",mode:a,label:r.funcName,isStretchy:!1,isShifty:!0,base:t}},htmlBuilder:_e,mathmlBuilder:ar}),B({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(r,e)=>{var{parser:t,funcName:a}=r,i=e[0];return{type:"accentUnder",mode:t.mode,label:a,base:i}},htmlBuilder:(r,e)=>{var t=P(r.base,e),a=T0.svgSpan(r,e),i=r.label==="\\utilde"?.12:0,s=y.makeVList({positionType:"top",positionData:t.height,children:[{type:"elem",elem:a,wrapperClasses:["svg-align"]},{type:"kern",size:i},{type:"elem",elem:t}]},e);return y.makeSpan(["mord","accentunder"],[s],e)},mathmlBuilder:(r,e)=>{var t=T0.mathMLnode(r.label),a=new S.MathNode("munder",[Y(r.base,e),t]);return a.setAttribute("accentunder","true"),a}});var ze=r=>{var e=new S.MathNode("mpadded",r?[r]:[]);return e.setAttribute("width","+0.6em"),e.setAttribute("lspace","0.3em"),e};B({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(r,e,t){var{parser:a,funcName:i}=r;return{type:"xArrow",mode:a.mode,label:i,body:e[0],below:t[0]}},htmlBuilder(r,e){var t=e.style,a=e.havingStyle(t.sup()),i=y.wrapFragment(P(r.body,a,e),e),s=r.label.slice(0,2)==="\\x"?"x":"cd";i.classes.push(s+"-arrow-pad");var o;r.below&&(a=e.havingStyle(t.sub()),o=y.wrapFragment(P(r.below,a,e),e),o.classes.push(s+"-arrow-pad"));var h=T0.svgSpan(r,e),c=-e.fontMetrics().axisHeight+.5*h.height,p=-e.fontMetrics().axisHeight-.5*h.height-.111;(i.depth>.25||r.label==="\\xleftequilibrium")&&(p-=i.depth);var g;if(o){var b=-e.fontMetrics().axisHeight+o.height+.5*h.height+.111;g=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:p},{type:"elem",elem:h,shift:c},{type:"elem",elem:o,shift:b}]},e)}else g=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:p},{type:"elem",elem:h,shift:c}]},e);return g.children[0].children[0].children[1].classes.push("svg-align"),y.makeSpan(["mrel","x-arrow"],[g],e)},mathmlBuilder(r,e){var t=T0.mathMLnode(r.label);t.setAttribute("minsize",r.label.charAt(0)==="x"?"1.75em":"3.0em");var a;if(r.body){var i=ze(Y(r.body,e));if(r.below){var s=ze(Y(r.below,e));a=new S.MathNode("munderover",[t,s,i])}else a=new S.MathNode("mover",[t,i])}else if(r.below){var o=ze(Y(r.below,e));a=new S.MathNode("munder",[t,o])}else a=ze(),a=new S.MathNode("mover",[t,a]);return a}});var k1={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},ir=()=>({type:"styling",body:[],mode:"math",style:"display"}),nr=r=>r.type==="textord"&&r.text==="@",S1=(r,e)=>(r.type==="mathord"||r.type==="atom")&&r.text===e;function M1(r,e,t){var a=k1[r];switch(a){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return t.callFunction(a,[e[0]],[e[1]]);case"\\uparrow":case"\\downarrow":{var i=t.callFunction("\\\\cdleft",[e[0]],[]),s={type:"atom",text:a,mode:"math",family:"rel"},o=t.callFunction("\\Big",[s],[]),h=t.callFunction("\\\\cdright",[e[1]],[]),c={type:"ordgroup",mode:"math",body:[i,o,h]};return t.callFunction("\\\\cdparent",[c],[])}case"\\\\cdlongequal":return t.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{var p={type:"textord",text:"\\Vert",mode:"math"};return t.callFunction("\\Big",[p],[])}default:return{type:"textord",text:" ",mode:"math"}}}function z1(r){var e=[];for(r.gullet.beginGroup(),r.gullet.macros.set("\\cr","\\\\\\relax"),r.gullet.beginGroup();;){e.push(r.parseExpression(!1,"\\\\")),r.gullet.endGroup(),r.gullet.beginGroup();var t=r.fetch().text;if(t==="&"||t==="\\\\")r.consume();else if(t==="\\end"){e[e.length-1].length===0&&e.pop();break}else throw new M("Expected \\\\ or \\cr or \\end",r.nextToken)}for(var a=[],i=[a],s=0;s<e.length;s++){for(var o=e[s],h=ir(),c=0;c<o.length;c++)if(!nr(o[c]))h.body.push(o[c]);else{a.push(h),c+=1;var p=Qe(o[c]).text,g=new Array(2);if(g[0]={type:"ordgroup",mode:"math",body:[]},g[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(p)>-1))if("<>AV".indexOf(p)>-1)for(var b=0;b<2;b++){for(var x=!0,w=c+1;w<o.length;w++){if(S1(o[w],p)){x=!1,c=w;break}if(nr(o[w]))throw new M("Missing a "+p+" character to complete a CD arrow.",o[w]);g[b].body.push(o[w])}if(x)throw new M("Missing a "+p+" character to complete a CD arrow.",o[c])}else throw new M('Expected one of "<>AV=|." after @',o[c]);var A=M1(p,g,r),T={type:"styling",body:[A],mode:"math",style:"display"};a.push(T),h=ir()}s%2==0?a.push(h):a.shift(),a=[],i.push(a)}r.gullet.endGroup(),r.gullet.endGroup();var N=new Array(i[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:i,arraystretch:1,addJot:!0,rowGaps:[null],cols:N,colSeparationType:"CD",hLinesBeforeRow:new Array(i.length+1).fill([])}}B({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r;return{type:"cdlabel",mode:t.mode,side:a.slice(4),label:e[0]}},htmlBuilder(r,e){var t=e.havingStyle(e.style.sup()),a=y.wrapFragment(P(r.label,t,e),e);return a.classes.push("cd-label-"+r.side),a.style.bottom=z(.8-a.depth),a.height=0,a.depth=0,a},mathmlBuilder(r,e){var t=new S.MathNode("mrow",[Y(r.label,e)]);return t=new S.MathNode("mpadded",[t]),t.setAttribute("width","0"),r.side==="left"&&t.setAttribute("lspace","-1width"),t.setAttribute("voffset","0.7em"),t=new S.MathNode("mstyle",[t]),t.setAttribute("displaystyle","false"),t.setAttribute("scriptlevel","1"),t}}),B({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(r,e){var{parser:t}=r;return{type:"cdlabelparent",mode:t.mode,fragment:e[0]}},htmlBuilder(r,e){var t=y.wrapFragment(P(r.fragment,e),e);return t.classes.push("cd-vert-arrow"),t},mathmlBuilder(r,e){return new S.MathNode("mrow",[Y(r.fragment,e)])}}),B({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(r,e){for(var{parser:t}=r,a=H(e[0],"ordgroup"),i=a.body,s="",o=0;o<i.length;o++){var h=H(i[o],"textord");s+=h.text}var c=parseInt(s),p;if(isNaN(c))throw new M("\\@char has non-numeric argument "+s);if(c<0||c>=1114111)throw new M("\\@char with invalid code point "+s);return c<=65535?p=String.fromCharCode(c):(c-=65536,p=String.fromCharCode((c>>10)+55296,(c&1023)+56320)),{type:"textord",mode:t.mode,text:p}}});var sr=(r,e)=>{var t=e0(r.body,e.withColor(r.color),!1);return y.makeFragment(t)},lr=(r,e)=>{var t=u0(r.body,e.withColor(r.color)),a=new S.MathNode("mstyle",t);return a.setAttribute("mathcolor",r.color),a};B({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(r,e){var{parser:t}=r,a=H(e[0],"color-token").color,i=e[1];return{type:"color",mode:t.mode,color:a,body:_(i)}},htmlBuilder:sr,mathmlBuilder:lr}),B({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(r,e){var{parser:t,breakOnTokenText:a}=r,i=H(e[0],"color-token").color;t.gullet.macros.set("\\current@color",i);var s=t.parseExpression(!0,a);return{type:"color",mode:t.mode,color:i,body:s}},htmlBuilder:sr,mathmlBuilder:lr}),B({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:1,argTypes:["size"],allowedInText:!0},handler(r,e,t){var{parser:a}=r,i=t[0],s=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:s,size:i&&H(i,"size").value}},htmlBuilder(r,e){var t=y.makeSpan(["mspace"],[],e);return r.newLine&&(t.classes.push("newline"),r.size&&(t.style.marginTop=z(Z(r.size,e)))),t},mathmlBuilder(r,e){var t=new S.MathNode("mspace");return r.newLine&&(t.setAttribute("linebreak","newline"),r.size&&t.setAttribute("height",z(Z(r.size,e)))),t}});var et={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},or=r=>{var e=r.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(e))throw new M("Expected a control sequence",r);return e},A1=r=>{var e=r.gullet.popToken();return e.text==="="&&(e=r.gullet.popToken(),e.text===" "&&(e=r.gullet.popToken())),e},ur=(r,e,t,a)=>{var i=r.gullet.macros.get(t.text);i==null&&(t.noexpand=!0,i={tokens:[t],numArgs:0,unexpandable:!r.gullet.isExpandable(t.text)}),r.gullet.macros.set(e,i,a)};B({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(r){var{parser:e,funcName:t}=r;e.consumeSpaces();var a=e.fetch();if(et[a.text])return(t==="\\global"||t==="\\\\globallong")&&(a.text=et[a.text]),H(e.parseFunction(),"internal");throw new M("Invalid token after macro prefix",a)}}),B({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=e.gullet.popToken(),i=a.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(i))throw new M("Expected a control sequence",a);for(var s=0,o,h=[[]];e.gullet.future().text!=="{";)if(a=e.gullet.popToken(),a.text==="#"){if(e.gullet.future().text==="{"){o=e.gullet.future(),h[s].push("{");break}if(a=e.gullet.popToken(),!/^[1-9]$/.test(a.text))throw new M('Invalid argument number "'+a.text+'"');if(parseInt(a.text)!==s+1)throw new M('Argument number "'+a.text+'" out of order');s++,h.push([])}else{if(a.text==="EOF")throw new M("Expected a macro definition");h[s].push(a.text)}var{tokens:c}=e.gullet.consumeArg();return o&&c.unshift(o),(t==="\\edef"||t==="\\xdef")&&(c=e.gullet.expandTokens(c),c.reverse()),e.gullet.macros.set(i,{tokens:c,numArgs:s,delimiters:h},t===et[t]),{type:"internal",mode:e.mode}}}),B({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=or(e.gullet.popToken());e.gullet.consumeSpaces();var i=A1(e);return ur(e,a,i,t==="\\\\globallet"),{type:"internal",mode:e.mode}}}),B({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r){var{parser:e,funcName:t}=r,a=or(e.gullet.popToken()),i=e.gullet.popToken(),s=e.gullet.popToken();return ur(e,a,s,t==="\\\\globalfuture"),e.gullet.pushToken(s),e.gullet.pushToken(i),{type:"internal",mode:e.mode}}});var ne=function(e,t,a){var i=X.math[e]&&X.math[e].replace,s=He(i||e,t,a);if(!s)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return s},tt=function(e,t,a,i){var s=a.havingBaseStyle(t),o=y.makeSpan(i.concat(s.sizingClasses(a)),[e],a),h=s.sizeMultiplier/a.sizeMultiplier;return o.height*=h,o.depth*=h,o.maxFontSize=s.sizeMultiplier,o},hr=function(e,t,a){var i=t.havingBaseStyle(a),s=(1-t.sizeMultiplier/i.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=z(s),e.height-=s,e.depth+=s},T1=function(e,t,a,i,s,o){var h=y.makeSymbol(e,"Main-Regular",s,i),c=tt(h,t,i,o);return a&&hr(c,i,t),c},B1=function(e,t,a,i){return y.makeSymbol(e,"Size"+t+"-Regular",a,i)},mr=function(e,t,a,i,s,o){var h=B1(e,t,s,i),c=tt(y.makeSpan(["delimsizing","size"+t],[h],i),q.TEXT,i,o);return a&&hr(c,i,q.TEXT),c},rt=function(e,t,a){var i;t==="Size1-Regular"?i="delim-size1":i="delim-size4";var s=y.makeSpan(["delimsizinginner",i],[y.makeSpan([],[y.makeSymbol(e,t,a)])]);return{type:"elem",elem:s}},at=function(e,t,a){var i=b0["Size4-Regular"][e.charCodeAt(0)]?b0["Size4-Regular"][e.charCodeAt(0)][4]:b0["Size1-Regular"][e.charCodeAt(0)][4],s=new P0("inner",Oa(e,Math.round(1e3*t))),o=new R0([s],{width:z(i),height:z(t),style:"width:"+z(i),viewBox:"0 0 "+1e3*i+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),h=y.makeSvgSpan([],[o],a);return h.height=t,h.style.height=z(t),h.style.width=z(i),{type:"elem",elem:h}},it=.008,Ae={type:"kern",size:-1*it},D1=["|","\\lvert","\\rvert","\\vert"],C1=["\\|","\\lVert","\\rVert","\\Vert"],cr=function(e,t,a,i,s,o){var h,c,p,g;h=p=g=e,c=null;var b="Size1-Regular";e==="\\uparrow"?p=g="\u23D0":e==="\\Uparrow"?p=g="\u2016":e==="\\downarrow"?h=p="\u23D0":e==="\\Downarrow"?h=p="\u2016":e==="\\updownarrow"?(h="\\uparrow",p="\u23D0",g="\\downarrow"):e==="\\Updownarrow"?(h="\\Uparrow",p="\u2016",g="\\Downarrow"):R.contains(D1,e)?p="\u2223":R.contains(C1,e)?p="\u2225":e==="["||e==="\\lbrack"?(h="\u23A1",p="\u23A2",g="\u23A3",b="Size4-Regular"):e==="]"||e==="\\rbrack"?(h="\u23A4",p="\u23A5",g="\u23A6",b="Size4-Regular"):e==="\\lfloor"||e==="\u230A"?(p=h="\u23A2",g="\u23A3",b="Size4-Regular"):e==="\\lceil"||e==="\u2308"?(h="\u23A1",p=g="\u23A2",b="Size4-Regular"):e==="\\rfloor"||e==="\u230B"?(p=h="\u23A5",g="\u23A6",b="Size4-Regular"):e==="\\rceil"||e==="\u2309"?(h="\u23A4",p=g="\u23A5",b="Size4-Regular"):e==="("||e==="\\lparen"?(h="\u239B",p="\u239C",g="\u239D",b="Size4-Regular"):e===")"||e==="\\rparen"?(h="\u239E",p="\u239F",g="\u23A0",b="Size4-Regular"):e==="\\{"||e==="\\lbrace"?(h="\u23A7",c="\u23A8",g="\u23A9",p="\u23AA",b="Size4-Regular"):e==="\\}"||e==="\\rbrace"?(h="\u23AB",c="\u23AC",g="\u23AD",p="\u23AA",b="Size4-Regular"):e==="\\lgroup"||e==="\u27EE"?(h="\u23A7",g="\u23A9",p="\u23AA",b="Size4-Regular"):e==="\\rgroup"||e==="\u27EF"?(h="\u23AB",g="\u23AD",p="\u23AA",b="Size4-Regular"):e==="\\lmoustache"||e==="\u23B0"?(h="\u23A7",g="\u23AD",p="\u23AA",b="Size4-Regular"):(e==="\\rmoustache"||e==="\u23B1")&&(h="\u23AB",g="\u23A9",p="\u23AA",b="Size4-Regular");var x=ne(h,b,s),w=x.height+x.depth,A=ne(p,b,s),T=A.height+A.depth,N=ne(g,b,s),C=N.height+N.depth,I=0,L=1;if(c!==null){var V=ne(c,b,s);I=V.height+V.depth,L=2}var F=w+C+I,U=Math.max(0,Math.ceil((t-F)/(L*T))),G=F+U*L*T,W=i.fontMetrics().axisHeight;a&&(W*=i.sizeMultiplier);var j=G/2-W,t0=[];if(t0.push(rt(g,b,s)),t0.push(Ae),c===null){var i0=G-w-C+2*it;t0.push(at(p,i0,i))}else{var D0=(G-w-C-I)/2+2*it;t0.push(at(p,D0,i)),t0.push(Ae),t0.push(rt(c,b,s)),t0.push(Ae),t0.push(at(p,D0,i))}t0.push(Ae),t0.push(rt(h,b,s));var F0=i.havingBaseStyle(q.TEXT),p0=y.makeVList({positionType:"bottom",positionData:j,children:t0},F0);return tt(y.makeSpan(["delimsizing","mult"],[p0],F0),q.TEXT,i,o)},nt=80,st=.08,lt=function(e,t,a,i,s){var o=Ia(e,i,a),h=new P0(e,o),c=new R0([h],{width:"400em",height:z(t),viewBox:"0 0 400000 "+a,preserveAspectRatio:"xMinYMin slice"});return y.makeSvgSpan(["hide-tail"],[c],s)},N1=function(e,t){var a=t.havingBaseSizing(),i=vr("\\surd",e*a.sizeMultiplier,pr,a),s=a.sizeMultiplier,o=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),h,c=0,p=0,g=0,b;return i.type==="small"?(g=1e3+1e3*o+nt,e<1?s=1:e<1.4&&(s=.7),c=(1+o+st)/s,p=(1+o)/s,h=lt("sqrtMain",c,g,o,t),h.style.minWidth="0.853em",b=.833/s):i.type==="large"?(g=(1e3+nt)*se[i.size],p=(se[i.size]+o)/s,c=(se[i.size]+o+st)/s,h=lt("sqrtSize"+i.size,c,g,o,t),h.style.minWidth="1.02em",b=1/s):(c=e+o+st,p=e+o,g=Math.floor(1e3*e+o)+nt,h=lt("sqrtTall",c,g,o,t),h.style.minWidth="0.742em",b=1.056),h.height=p,h.style.height=z(c),{span:h,advanceWidth:b,ruleWidth:(t.fontMetrics().sqrtRuleThickness+o)*s}},dr=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230A","\u230B","\\lceil","\\rceil","\u2308","\u2309","\\surd"],q1=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27EE","\u27EF","\\lmoustache","\\rmoustache","\u23B0","\u23B1"],fr=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],se=[0,1.2,1.8,2.4,3],E1=function(e,t,a,i,s){if(e==="<"||e==="\\lt"||e==="\u27E8"?e="\\langle":(e===">"||e==="\\gt"||e==="\u27E9")&&(e="\\rangle"),R.contains(dr,e)||R.contains(fr,e))return mr(e,t,!1,a,i,s);if(R.contains(q1,e))return cr(e,se[t],!1,a,i,s);throw new M("Illegal delimiter: '"+e+"'")},R1=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],I1=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"stack"}],pr=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],O1=function(e){if(e.type==="small")return"Main-Regular";if(e.type==="large")return"Size"+e.size+"-Regular";if(e.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},vr=function(e,t,a,i){for(var s=Math.min(2,3-i.style.size),o=s;o<a.length&&a[o].type!=="stack";o++){var h=ne(e,O1(a[o]),"math"),c=h.height+h.depth;if(a[o].type==="small"){var p=i.havingBaseStyle(a[o].style);c*=p.sizeMultiplier}if(c>t)return a[o]}return a[a.length-1]},gr=function(e,t,a,i,s,o){e==="<"||e==="\\lt"||e==="\u27E8"?e="\\langle":(e===">"||e==="\\gt"||e==="\u27E9")&&(e="\\rangle");var h;R.contains(fr,e)?h=R1:R.contains(dr,e)?h=pr:h=I1;var c=vr(e,t,h,i);return c.type==="small"?T1(e,c.style,a,i,s,o):c.type==="large"?mr(e,c.size,a,i,s,o):cr(e,t,a,i,s,o)},H1=function(e,t,a,i,s,o){var h=i.fontMetrics().axisHeight*i.sizeMultiplier,c=901,p=5/i.fontMetrics().ptPerEm,g=Math.max(t-h,a+h),b=Math.max(g/500*c,2*g-p);return gr(e,b,!0,i,s,o)},B0={sqrtImage:N1,sizedDelim:E1,sizeToMaxHeight:se,customSizedDelim:gr,leftRightDelim:H1},br={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},L1=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230A","\u230B","\\lceil","\\rceil","\u2308","\u2309","<",">","\\langle","\u27E8","\\rangle","\u27E9","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27EE","\u27EF","\\lmoustache","\\rmoustache","\u23B0","\u23B1","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function Te(r,e){var t=Me(r);if(t&&R.contains(L1,t.text))return t;throw t?new M("Invalid delimiter '"+t.text+"' after '"+e.funcName+"'",r):new M("Invalid delimiter type '"+r.type+"'",r)}B({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(r,e)=>{var t=Te(e[0],r);return{type:"delimsizing",mode:r.parser.mode,size:br[r.funcName].size,mclass:br[r.funcName].mclass,delim:t.text}},htmlBuilder:(r,e)=>r.delim==="."?y.makeSpan([r.mclass]):B0.sizedDelim(r.delim,r.size,e,r.mode,[r.mclass]),mathmlBuilder:r=>{var e=[];r.delim!=="."&&e.push(f0(r.delim,r.mode));var t=new S.MathNode("mo",e);r.mclass==="mopen"||r.mclass==="mclose"?t.setAttribute("fence","true"):t.setAttribute("fence","false"),t.setAttribute("stretchy","true");var a=z(B0.sizeToMaxHeight[r.size]);return t.setAttribute("minsize",a),t.setAttribute("maxsize",a),t}});function yr(r){if(!r.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}B({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(r,e)=>{var t=r.parser.gullet.macros.get("\\current@color");if(t&&typeof t!="string")throw new M("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:r.parser.mode,delim:Te(e[0],r).text,color:t}}}),B({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(r,e)=>{var t=Te(e[0],r),a=r.parser;++a.leftrightDepth;var i=a.parseExpression(!1);--a.leftrightDepth,a.expect("\\right",!1);var s=H(a.parseFunction(),"leftright-right");return{type:"leftright",mode:a.mode,body:i,left:t.text,right:s.delim,rightColor:s.color}},htmlBuilder:(r,e)=>{yr(r);for(var t=e0(r.body,e,!0,["mopen","mclose"]),a=0,i=0,s=!1,o=0;o<t.length;o++)t[o].isMiddle?s=!0:(a=Math.max(t[o].height,a),i=Math.max(t[o].depth,i));a*=e.sizeMultiplier,i*=e.sizeMultiplier;var h;if(r.left==="."?h=ae(e,["mopen"]):h=B0.leftRightDelim(r.left,a,i,e,r.mode,["mopen"]),t.unshift(h),s)for(var c=1;c<t.length;c++){var p=t[c],g=p.isMiddle;g&&(t[c]=B0.leftRightDelim(g.delim,a,i,g.options,r.mode,[]))}var b;if(r.right===".")b=ae(e,["mclose"]);else{var x=r.rightColor?e.withColor(r.rightColor):e;b=B0.leftRightDelim(r.right,a,i,x,r.mode,["mclose"])}return t.push(b),y.makeSpan(["minner"],t,e)},mathmlBuilder:(r,e)=>{yr(r);var t=u0(r.body,e);if(r.left!=="."){var a=new S.MathNode("mo",[f0(r.left,r.mode)]);a.setAttribute("fence","true"),t.unshift(a)}if(r.right!=="."){var i=new S.MathNode("mo",[f0(r.right,r.mode)]);i.setAttribute("fence","true"),r.rightColor&&i.setAttribute("mathcolor",r.rightColor),t.push(i)}return Ke(t)}}),B({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(r,e)=>{var t=Te(e[0],r);if(!r.parser.leftrightDepth)throw new M("\\middle without preceding \\left",t);return{type:"middle",mode:r.parser.mode,delim:t.text}},htmlBuilder:(r,e)=>{var t;if(r.delim===".")t=ae(e,[]);else{t=B0.sizedDelim(r.delim,1,e,r.mode,[]);var a={delim:r.delim,options:e};t.isMiddle=a}return t},mathmlBuilder:(r,e)=>{var t=r.delim==="\\vert"||r.delim==="|"?f0("|","text"):f0(r.delim,r.mode),a=new S.MathNode("mo",[t]);return a.setAttribute("fence","true"),a.setAttribute("lspace","0.05em"),a.setAttribute("rspace","0.05em"),a}});var ot=(r,e)=>{var t=y.wrapFragment(P(r.body,e),e),a=r.label.substr(1),i=e.sizeMultiplier,s,o=0,h=R.isCharacterBox(r.body);if(a==="sout")s=y.makeSpan(["stretchy","sout"]),s.height=e.fontMetrics().defaultRuleThickness/i,o=-.5*e.fontMetrics().xHeight;else if(a==="phase"){var c=Z({number:.6,unit:"pt"},e),p=Z({number:.35,unit:"ex"},e),g=e.havingBaseSizing();i=i/g.sizeMultiplier;var b=t.height+t.depth+c+p;t.style.paddingLeft=z(b/2+c);var x=Math.floor(1e3*b*i),w=Ea(x),A=new R0([new P0("phase",w)],{width:"400em",height:z(x/1e3),viewBox:"0 0 400000 "+x,preserveAspectRatio:"xMinYMin slice"});s=y.makeSvgSpan(["hide-tail"],[A],e),s.style.height=z(b),o=t.depth+c+p}else{/cancel/.test(a)?h||t.classes.push("cancel-pad"):a==="angl"?t.classes.push("anglpad"):t.classes.push("boxpad");var T=0,N=0,C=0;/box/.test(a)?(C=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness),T=e.fontMetrics().fboxsep+(a==="colorbox"?0:C),N=T):a==="angl"?(C=Math.max(e.fontMetrics().defaultRuleThickness,e.minRuleThickness),T=4*C,N=Math.max(0,.25-t.depth)):(T=h?.2:0,N=T),s=T0.encloseSpan(t,a,T,N,e),/fbox|boxed|fcolorbox/.test(a)?(s.style.borderStyle="solid",s.style.borderWidth=z(C)):a==="angl"&&C!==.049&&(s.style.borderTopWidth=z(C),s.style.borderRightWidth=z(C)),o=t.depth+N,r.backgroundColor&&(s.style.backgroundColor=r.backgroundColor,r.borderColor&&(s.style.borderColor=r.borderColor))}var I;if(r.backgroundColor)I=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:o},{type:"elem",elem:t,shift:0}]},e);else{var L=/cancel|phase/.test(a)?["svg-align"]:[];I=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:t,shift:0},{type:"elem",elem:s,shift:o,wrapperClasses:L}]},e)}return/cancel/.test(a)&&(I.height=t.height,I.depth=t.depth),/cancel/.test(a)&&!h?y.makeSpan(["mord","cancel-lap"],[I],e):y.makeSpan(["mord"],[I],e)},ut=(r,e)=>{var t=0,a=new S.MathNode(r.label.indexOf("colorbox")>-1?"mpadded":"menclose",[Y(r.body,e)]);switch(r.label){case"\\cancel":a.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":a.setAttribute("notation","downdiagonalstrike");break;case"\\phase":a.setAttribute("notation","phasorangle");break;case"\\sout":a.setAttribute("notation","horizontalstrike");break;case"\\fbox":a.setAttribute("notation","box");break;case"\\angl":a.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(t=e.fontMetrics().fboxsep*e.fontMetrics().ptPerEm,a.setAttribute("width","+"+2*t+"pt"),a.setAttribute("height","+"+2*t+"pt"),a.setAttribute("lspace",t+"pt"),a.setAttribute("voffset",t+"pt"),r.label==="\\fcolorbox"){var i=Math.max(e.fontMetrics().fboxrule,e.minRuleThickness);a.setAttribute("style","border: "+i+"em solid "+String(r.borderColor))}break;case"\\xcancel":a.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return r.backgroundColor&&a.setAttribute("mathbackground",r.backgroundColor),a};B({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(r,e,t){var{parser:a,funcName:i}=r,s=H(e[0],"color-token").color,o=e[1];return{type:"enclose",mode:a.mode,label:i,backgroundColor:s,body:o}},htmlBuilder:ot,mathmlBuilder:ut}),B({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(r,e,t){var{parser:a,funcName:i}=r,s=H(e[0],"color-token").color,o=H(e[1],"color-token").color,h=e[2];return{type:"enclose",mode:a.mode,label:i,backgroundColor:o,borderColor:s,body:h}},htmlBuilder:ot,mathmlBuilder:ut}),B({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(r,e){var{parser:t}=r;return{type:"enclose",mode:t.mode,label:"\\fbox",body:e[0]}}}),B({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r,i=e[0];return{type:"enclose",mode:t.mode,label:a,body:i}},htmlBuilder:ot,mathmlBuilder:ut}),B({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(r,e){var{parser:t}=r;return{type:"enclose",mode:t.mode,label:"\\angl",body:e[0]}}});var xr={};function y0(r){for(var{type:e,names:t,props:a,handler:i,htmlBuilder:s,mathmlBuilder:o}=r,h={type:e,numArgs:a.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:i},c=0;c<t.length;++c)xr[t[c]]=h;s&&(xe[e]=s),o&&(we[e]=o)}var wr={};function m(r,e){wr[r]=e}function kr(r){var e=[];r.consumeSpaces();for(var t=r.fetch().text;t==="\\hline"||t==="\\hdashline";)r.consume(),e.push(t==="\\hdashline"),r.consumeSpaces(),t=r.fetch().text;return e}var Be=r=>{var e=r.parser.settings;if(!e.displayMode)throw new M("{"+r.envName+"} can be used only in display mode.")};function ht(r){if(r.indexOf("ed")===-1)return r.indexOf("*")===-1}function H0(r,e,t){var{hskipBeforeAndAfter:a,addJot:i,cols:s,arraystretch:o,colSeparationType:h,autoTag:c,singleRow:p,emptySingleRow:g,maxNumCols:b,leqno:x}=e;if(r.gullet.beginGroup(),p||r.gullet.macros.set("\\cr","\\\\\\relax"),!o){var w=r.gullet.expandMacroAsText("\\arraystretch");if(w==null)o=1;else if(o=parseFloat(w),!o||o<0)throw new M("Invalid \\arraystretch: "+w)}r.gullet.beginGroup();var A=[],T=[A],N=[],C=[],I=c!=null?[]:void 0;function L(){c&&r.gullet.macros.set("\\@eqnsw","1",!0)}function V(){I&&(r.gullet.macros.get("\\df@tag")?(I.push(r.subparse([new h0("\\df@tag")])),r.gullet.macros.set("\\df@tag",void 0,!0)):I.push(Boolean(c)&&r.gullet.macros.get("\\@eqnsw")==="1"))}for(L(),C.push(kr(r));;){var F=r.parseExpression(!1,p?"\\end":"\\\\");r.gullet.endGroup(),r.gullet.beginGroup(),F={type:"ordgroup",mode:r.mode,body:F},t&&(F={type:"styling",mode:r.mode,style:t,body:[F]}),A.push(F);var U=r.fetch().text;if(U==="&"){if(b&&A.length===b){if(p||h)throw new M("Too many tab characters: &",r.nextToken);r.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}r.consume()}else if(U==="\\end"){V(),A.length===1&&F.type==="styling"&&F.body[0].body.length===0&&(T.length>1||!g)&&T.pop(),C.length<T.length+1&&C.push([]);break}else if(U==="\\\\"){r.consume();var G=void 0;r.gullet.future().text!==" "&&(G=r.parseSizeGroup(!0)),N.push(G?G.value:null),V(),C.push(kr(r)),A=[],T.push(A),L()}else throw new M("Expected & or \\\\ or \\cr or \\end",r.nextToken)}return r.gullet.endGroup(),r.gullet.endGroup(),{type:"array",mode:r.mode,addJot:i,arraystretch:o,body:T,cols:s,rowGaps:N,hskipBeforeAndAfter:a,hLinesBeforeRow:C,colSeparationType:h,tags:I,leqno:x}}function mt(r){return r.substr(0,1)==="d"?"display":"text"}var x0=function(e,t){var a,i,s=e.body.length,o=e.hLinesBeforeRow,h=0,c=new Array(s),p=[],g=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),b=1/t.fontMetrics().ptPerEm,x=5*b;if(e.colSeparationType&&e.colSeparationType==="small"){var w=t.havingStyle(q.SCRIPT).sizeMultiplier;x=.2778*(w/t.sizeMultiplier)}var A=e.colSeparationType==="CD"?Z({number:3,unit:"ex"},t):12*b,T=3*b,N=e.arraystretch*A,C=.7*N,I=.3*N,L=0;function V(qe){for(var Ee=0;Ee<qe.length;++Ee)Ee>0&&(L+=.25),p.push({pos:L,isDashed:qe[Ee]})}for(V(o[0]),a=0;a<e.body.length;++a){var F=e.body[a],U=C,G=I;h<F.length&&(h=F.length);var W=new Array(F.length);for(i=0;i<F.length;++i){var j=P(F[i],t);G<j.depth&&(G=j.depth),U<j.height&&(U=j.height),W[i]=j}var t0=e.rowGaps[a],i0=0;t0&&(i0=Z(t0,t),i0>0&&(i0+=I,G<i0&&(G=i0),i0=0)),e.addJot&&(G+=T),W.height=U,W.depth=G,L+=U,W.pos=L,L+=G+i0,c[a]=W,V(o[a+1])}var D0=L/2+t.fontMetrics().axisHeight,F0=e.cols||[],p0=[],C0,Z0,Mt=[];if(e.tags&&e.tags.some(qe=>qe))for(a=0;a<s;++a){var zt=c[a],l4=zt.pos-D0,At=e.tags[a],K0=void 0;At===!0?K0=y.makeSpan(["eqn-num"],[],t):At===!1?K0=y.makeSpan([],[],t):K0=y.makeSpan([],e0(At,t,!0),t),K0.depth=zt.depth,K0.height=zt.height,Mt.push({type:"elem",elem:K0,shift:l4})}for(i=0,Z0=0;i<h||Z0<F0.length;++i,++Z0){for(var N0=F0[Z0]||{},ia=!0;N0.type==="separator";){if(ia||(C0=y.makeSpan(["arraycolsep"],[]),C0.style.width=z(t.fontMetrics().doubleRuleSep),p0.push(C0)),N0.separator==="|"||N0.separator===":"){var o4=N0.separator==="|"?"solid":"dashed",J0=y.makeSpan(["vertical-separator"],[],t);J0.style.height=z(L),J0.style.borderRightWidth=z(g),J0.style.borderRightStyle=o4,J0.style.margin="0 "+z(-g/2);var na=L-D0;na&&(J0.style.verticalAlign=z(-na)),p0.push(J0)}else throw new M("Invalid separator type: "+N0.separator);Z0++,N0=F0[Z0]||{},ia=!1}if(!(i>=h)){var Q0=void 0;(i>0||e.hskipBeforeAndAfter)&&(Q0=R.deflt(N0.pregap,x),Q0!==0&&(C0=y.makeSpan(["arraycolsep"],[]),C0.style.width=z(Q0),p0.push(C0)));var _0=[];for(a=0;a<s;++a){var Ce=c[a],Ne=Ce[i];if(!!Ne){var u4=Ce.pos-D0;Ne.depth=Ce.depth,Ne.height=Ce.height,_0.push({type:"elem",elem:Ne,shift:u4})}}_0=y.makeVList({positionType:"individualShift",children:_0},t),_0=y.makeSpan(["col-align-"+(N0.align||"c")],[_0]),p0.push(_0),(i<h-1||e.hskipBeforeAndAfter)&&(Q0=R.deflt(N0.postgap,x),Q0!==0&&(C0=y.makeSpan(["arraycolsep"],[]),C0.style.width=z(Q0),p0.push(C0)))}}if(c=y.makeSpan(["mtable"],p0),p.length>0){for(var h4=y.makeLineSpan("hline",t,g),m4=y.makeLineSpan("hdashline",t,g),Tt=[{type:"elem",elem:c,shift:0}];p.length>0;){var sa=p.pop(),la=sa.pos-D0;sa.isDashed?Tt.push({type:"elem",elem:m4,shift:la}):Tt.push({type:"elem",elem:h4,shift:la})}c=y.makeVList({positionType:"individualShift",children:Tt},t)}if(Mt.length===0)return y.makeSpan(["mord"],[c],t);var Bt=y.makeVList({positionType:"individualShift",children:Mt},t);return Bt=y.makeSpan(["tag"],[Bt],t),y.makeFragment([c,Bt])},F1={c:"center ",l:"left ",r:"right "},w0=function(e,t){for(var a=[],i=new S.MathNode("mtd",[],["mtr-glue"]),s=new S.MathNode("mtd",[],["mml-eqn-num"]),o=0;o<e.body.length;o++){for(var h=e.body[o],c=[],p=0;p<h.length;p++)c.push(new S.MathNode("mtd",[Y(h[p],t)]));e.tags&&e.tags[o]&&(c.unshift(i),c.push(i),e.leqno?c.unshift(s):c.push(s)),a.push(new S.MathNode("mtr",c))}var g=new S.MathNode("mtable",a),b=e.arraystretch===.5?.1:.16+e.arraystretch-1+(e.addJot?.09:0);g.setAttribute("rowspacing",z(b));var x="",w="";if(e.cols&&e.cols.length>0){var A=e.cols,T="",N=!1,C=0,I=A.length;A[0].type==="separator"&&(x+="top ",C=1),A[A.length-1].type==="separator"&&(x+="bottom ",I-=1);for(var L=C;L<I;L++)A[L].type==="align"?(w+=F1[A[L].align],N&&(T+="none "),N=!0):A[L].type==="separator"&&N&&(T+=A[L].separator==="|"?"solid ":"dashed ",N=!1);g.setAttribute("columnalign",w.trim()),/[sd]/.test(T)&&g.setAttribute("columnlines",T.trim())}if(e.colSeparationType==="align"){for(var V=e.cols||[],F="",U=1;U<V.length;U++)F+=U%2?"0em ":"1em ";g.setAttribute("columnspacing",F.trim())}else e.colSeparationType==="alignat"||e.colSeparationType==="gather"?g.setAttribute("columnspacing","0em"):e.colSeparationType==="small"?g.setAttribute("columnspacing","0.2778em"):e.colSeparationType==="CD"?g.setAttribute("columnspacing","0.5em"):g.setAttribute("columnspacing","1em");var G="",W=e.hLinesBeforeRow;x+=W[0].length>0?"left ":"",x+=W[W.length-1].length>0?"right ":"";for(var j=1;j<W.length-1;j++)G+=W[j].length===0?"none ":W[j][0]?"dashed ":"solid ";return/[sd]/.test(G)&&g.setAttribute("rowlines",G.trim()),x!==""&&(g=new S.MathNode("menclose",[g]),g.setAttribute("notation",x.trim())),e.arraystretch&&e.arraystretch<1&&(g=new S.MathNode("mstyle",[g]),g.setAttribute("scriptlevel","1")),g},Sr=function(e,t){e.envName.indexOf("ed")===-1&&Be(e);var a=[],i=e.envName.indexOf("at")>-1?"alignat":"align",s=e.envName==="split",o=H0(e.parser,{cols:a,addJot:!0,autoTag:s?void 0:ht(e.envName),emptySingleRow:!0,colSeparationType:i,maxNumCols:s?2:void 0,leqno:e.parser.settings.leqno},"display"),h,c=0,p={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&t[0].type==="ordgroup"){for(var g="",b=0;b<t[0].body.length;b++){var x=H(t[0].body[b],"textord");g+=x.text}h=Number(g),c=h*2}var w=!c;o.body.forEach(function(C){for(var I=1;I<C.length;I+=2){var L=H(C[I],"styling"),V=H(L.body[0],"ordgroup");V.body.unshift(p)}if(w)c<C.length&&(c=C.length);else{var F=C.length/2;if(h<F)throw new M("Too many math in a row: "+("expected "+h+", but got "+F),C[0])}});for(var A=0;A<c;++A){var T="r",N=0;A%2==1?T="l":A>0&&w&&(N=1),a[A]={type:"align",align:T,pregap:N,postgap:0}}return o.colSeparationType=w?"align":"alignat",o};y0({type:"array",names:["array","darray"],props:{numArgs:1},handler(r,e){var t=Me(e[0]),a=t?[e[0]]:H(e[0],"ordgroup").body,i=a.map(function(o){var h=Qe(o),c=h.text;if("lcr".indexOf(c)!==-1)return{type:"align",align:c};if(c==="|")return{type:"separator",separator:"|"};if(c===":")return{type:"separator",separator:":"};throw new M("Unknown column alignment: "+c,o)}),s={cols:i,hskipBeforeAndAfter:!0,maxNumCols:i.length};return H0(r.parser,s,mt(r.envName))},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(r){var e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[r.envName.replace("*","")],t="c",a={hskipBeforeAndAfter:!1,cols:[{type:"align",align:t}]};if(r.envName.charAt(r.envName.length-1)==="*"){var i=r.parser;if(i.consumeSpaces(),i.fetch().text==="["){if(i.consume(),i.consumeSpaces(),t=i.fetch().text,"lcr".indexOf(t)===-1)throw new M("Expected l or c or r",i.nextToken);i.consume(),i.consumeSpaces(),i.expect("]"),i.consume(),a.cols=[{type:"align",align:t}]}}var s=H0(r.parser,a,mt(r.envName)),o=Math.max(0,...s.body.map(h=>h.length));return s.cols=new Array(o).fill({type:"align",align:t}),e?{type:"leftright",mode:r.mode,body:[s],left:e[0],right:e[1],rightColor:void 0}:s},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(r){var e={arraystretch:.5},t=H0(r.parser,e,"script");return t.colSeparationType="small",t},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["subarray"],props:{numArgs:1},handler(r,e){var t=Me(e[0]),a=t?[e[0]]:H(e[0],"ordgroup").body,i=a.map(function(o){var h=Qe(o),c=h.text;if("lc".indexOf(c)!==-1)return{type:"align",align:c};throw new M("Unknown column alignment: "+c,o)});if(i.length>1)throw new M("{subarray} can contain only one column");var s={cols:i,hskipBeforeAndAfter:!1,arraystretch:.5};if(s=H0(r.parser,s,"script"),s.body.length>0&&s.body[0].length>1)throw new M("{subarray} can contain only one column");return s},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(r){var e={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},t=H0(r.parser,e,mt(r.envName));return{type:"leftright",mode:r.mode,body:[t],left:r.envName.indexOf("r")>-1?".":"\\{",right:r.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:Sr,htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(r){R.contains(["gather","gather*"],r.envName)&&Be(r);var e={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:ht(r.envName),emptySingleRow:!0,leqno:r.parser.settings.leqno};return H0(r.parser,e,"display")},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:Sr,htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(r){Be(r);var e={autoTag:ht(r.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:r.parser.settings.leqno};return H0(r.parser,e,"display")},htmlBuilder:x0,mathmlBuilder:w0}),y0({type:"array",names:["CD"],props:{numArgs:0},handler(r){return Be(r),z1(r.parser)},htmlBuilder:x0,mathmlBuilder:w0}),m("\\nonumber","\\gdef\\@eqnsw{0}"),m("\\notag","\\nonumber"),B({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(r,e){throw new M(r.funcName+" valid only within array environment")}});var Mr=xr;B({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(r,e){var{parser:t,funcName:a}=r,i=e[0];if(i.type!=="ordgroup")throw new M("Invalid environment name",i);for(var s="",o=0;o<i.body.length;++o)s+=H(i.body[o],"textord").text;if(a==="\\begin"){if(!Mr.hasOwnProperty(s))throw new M("No such environment: "+s,i);var h=Mr[s],{args:c,optArgs:p}=t.parseArguments("\\begin{"+s+"}",h),g={mode:t.mode,envName:s,parser:t},b=h.handler(g,c,p);t.expect("\\end",!1);var x=t.nextToken,w=H(t.parseFunction(),"environment");if(w.name!==s)throw new M("Mismatch: \\begin{"+s+"} matched by \\end{"+w.name+"}",x);return b}return{type:"environment",mode:t.mode,name:s,nameGroup:i}}});var P1=y.makeSpan;function zr(r,e){var t=e0(r.body,e,!0);return P1([r.mclass],t,e)}function Ar(r,e){var t,a=u0(r.body,e);return r.mclass==="minner"?t=new S.MathNode("mpadded",a):r.mclass==="mord"?r.isCharacterBox?(t=a[0],t.type="mi"):t=new S.MathNode("mi",a):(r.isCharacterBox?(t=a[0],t.type="mo"):t=new S.MathNode("mo",a),r.mclass==="mbin"?(t.attributes.lspace="0.22em",t.attributes.rspace="0.22em"):r.mclass==="mpunct"?(t.attributes.lspace="0em",t.attributes.rspace="0.17em"):r.mclass==="mopen"||r.mclass==="mclose"?(t.attributes.lspace="0em",t.attributes.rspace="0em"):r.mclass==="minner"&&(t.attributes.lspace="0.0556em",t.attributes.width="+0.1111em")),t}B({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(r,e){var{parser:t,funcName:a}=r,i=e[0];return{type:"mclass",mode:t.mode,mclass:"m"+a.substr(5),body:_(i),isCharacterBox:R.isCharacterBox(i)}},htmlBuilder:zr,mathmlBuilder:Ar});var ct=r=>{var e=r.type==="ordgroup"&&r.body.length?r.body[0]:r;return e.type==="atom"&&(e.family==="bin"||e.family==="rel")?"m"+e.family:"mord"};B({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(r,e){var{parser:t}=r;return{type:"mclass",mode:t.mode,mclass:ct(e[0]),body:_(e[1]),isCharacterBox:R.isCharacterBox(e[1])}}}),B({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(r,e){var{parser:t,funcName:a}=r,i=e[1],s=e[0],o;a!=="\\stackrel"?o=ct(i):o="mrel";var h={type:"op",mode:i.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:a!=="\\stackrel",body:_(i)},c={type:"supsub",mode:s.mode,base:h,sup:a==="\\underset"?null:s,sub:a==="\\underset"?s:null};return{type:"mclass",mode:t.mode,mclass:o,body:[c],isCharacterBox:R.isCharacterBox(c)}},htmlBuilder:zr,mathmlBuilder:Ar});var Tr=(r,e)=>{var t=r.font,a=e.withFont(t);return P(r.body,a)},Br=(r,e)=>{var t=r.font,a=e.withFont(t);return Y(r.body,a)},Dr={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};B({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(r,e)=>{var{parser:t,funcName:a}=r,i=ke(e[0]),s=a;return s in Dr&&(s=Dr[s]),{type:"font",mode:t.mode,font:s.slice(1),body:i}},htmlBuilder:Tr,mathmlBuilder:Br}),B({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(r,e)=>{var{parser:t}=r,a=e[0],i=R.isCharacterBox(a);return{type:"mclass",mode:t.mode,mclass:ct(a),body:[{type:"font",mode:t.mode,font:"boldsymbol",body:a}],isCharacterBox:i}}}),B({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(r,e)=>{var{parser:t,funcName:a,breakOnTokenText:i}=r,{mode:s}=t,o=t.parseExpression(!0,i),h="math"+a.slice(1);return{type:"font",mode:s,font:h,body:{type:"ordgroup",mode:t.mode,body:o}}},htmlBuilder:Tr,mathmlBuilder:Br});var Cr=(r,e)=>{var t=e;return r==="display"?t=t.id>=q.SCRIPT.id?t.text():q.DISPLAY:r==="text"&&t.size===q.DISPLAY.size?t=q.TEXT:r==="script"?t=q.SCRIPT:r==="scriptscript"&&(t=q.SCRIPTSCRIPT),t},dt=(r,e)=>{var t=Cr(r.size,e.style),a=t.fracNum(),i=t.fracDen(),s;s=e.havingStyle(a);var o=P(r.numer,s,e);if(r.continued){var h=8.5/e.fontMetrics().ptPerEm,c=3.5/e.fontMetrics().ptPerEm;o.height=o.height<h?h:o.height,o.depth=o.depth<c?c:o.depth}s=e.havingStyle(i);var p=P(r.denom,s,e),g,b,x;r.hasBarLine?(r.barSize?(b=Z(r.barSize,e),g=y.makeLineSpan("frac-line",e,b)):g=y.makeLineSpan("frac-line",e),b=g.height,x=g.height):(g=null,b=0,x=e.fontMetrics().defaultRuleThickness);var w,A,T;t.size===q.DISPLAY.size||r.size==="display"?(w=e.fontMetrics().num1,b>0?A=3*x:A=7*x,T=e.fontMetrics().denom1):(b>0?(w=e.fontMetrics().num2,A=x):(w=e.fontMetrics().num3,A=3*x),T=e.fontMetrics().denom2);var N;if(g){var I=e.fontMetrics().axisHeight;w-o.depth-(I+.5*b)<A&&(w+=A-(w-o.depth-(I+.5*b))),I-.5*b-(p.height-T)<A&&(T+=A-(I-.5*b-(p.height-T)));var L=-(I-.5*b);N=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:p,shift:T},{type:"elem",elem:g,shift:L},{type:"elem",elem:o,shift:-w}]},e)}else{var C=w-o.depth-(p.height-T);C<A&&(w+=.5*(A-C),T+=.5*(A-C)),N=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:p,shift:T},{type:"elem",elem:o,shift:-w}]},e)}s=e.havingStyle(t),N.height*=s.sizeMultiplier/e.sizeMultiplier,N.depth*=s.sizeMultiplier/e.sizeMultiplier;var V;t.size===q.DISPLAY.size?V=e.fontMetrics().delim1:t.size===q.SCRIPTSCRIPT.size?V=e.havingStyle(q.SCRIPT).fontMetrics().delim2:V=e.fontMetrics().delim2;var F,U;return r.leftDelim==null?F=ae(e,["mopen"]):F=B0.customSizedDelim(r.leftDelim,V,!0,e.havingStyle(t),r.mode,["mopen"]),r.continued?U=y.makeSpan([]):r.rightDelim==null?U=ae(e,["mclose"]):U=B0.customSizedDelim(r.rightDelim,V,!0,e.havingStyle(t),r.mode,["mclose"]),y.makeSpan(["mord"].concat(s.sizingClasses(e)),[F,y.makeSpan(["mfrac"],[N]),U],e)},ft=(r,e)=>{var t=new S.MathNode("mfrac",[Y(r.numer,e),Y(r.denom,e)]);if(!r.hasBarLine)t.setAttribute("linethickness","0px");else if(r.barSize){var a=Z(r.barSize,e);t.setAttribute("linethickness",z(a))}var i=Cr(r.size,e.style);if(i.size!==e.style.size){t=new S.MathNode("mstyle",[t]);var s=i.size===q.DISPLAY.size?"true":"false";t.setAttribute("displaystyle",s),t.setAttribute("scriptlevel","0")}if(r.leftDelim!=null||r.rightDelim!=null){var o=[];if(r.leftDelim!=null){var h=new S.MathNode("mo",[new S.TextNode(r.leftDelim.replace("\\",""))]);h.setAttribute("fence","true"),o.push(h)}if(o.push(t),r.rightDelim!=null){var c=new S.MathNode("mo",[new S.TextNode(r.rightDelim.replace("\\",""))]);c.setAttribute("fence","true"),o.push(c)}return Ke(o)}return t};B({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(r,e)=>{var{parser:t,funcName:a}=r,i=e[0],s=e[1],o,h=null,c=null,p="auto";switch(a){case"\\dfrac":case"\\frac":case"\\tfrac":o=!0;break;case"\\\\atopfrac":o=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":o=!1,h="(",c=")";break;case"\\\\bracefrac":o=!1,h="\\{",c="\\}";break;case"\\\\brackfrac":o=!1,h="[",c="]";break;default:throw new Error("Unrecognized genfrac command")}switch(a){case"\\dfrac":case"\\dbinom":p="display";break;case"\\tfrac":case"\\tbinom":p="text";break}return{type:"genfrac",mode:t.mode,continued:!1,numer:i,denom:s,hasBarLine:o,leftDelim:h,rightDelim:c,size:p,barSize:null}},htmlBuilder:dt,mathmlBuilder:ft}),B({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(r,e)=>{var{parser:t,funcName:a}=r,i=e[0],s=e[1];return{type:"genfrac",mode:t.mode,continued:!0,numer:i,denom:s,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),B({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(r){var{parser:e,funcName:t,token:a}=r,i;switch(t){case"\\over":i="\\frac";break;case"\\choose":i="\\binom";break;case"\\atop":i="\\\\atopfrac";break;case"\\brace":i="\\\\bracefrac";break;case"\\brack":i="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:e.mode,replaceWith:i,token:a}}});var Nr=["display","text","script","scriptscript"],qr=function(e){var t=null;return e.length>0&&(t=e,t=t==="."?null:t),t};B({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(r,e){var{parser:t}=r,a=e[4],i=e[5],s=ke(e[0]),o=s.type==="atom"&&s.family==="open"?qr(s.text):null,h=ke(e[1]),c=h.type==="atom"&&h.family==="close"?qr(h.text):null,p=H(e[2],"size"),g,b=null;p.isBlank?g=!0:(b=p.value,g=b.number>0);var x="auto",w=e[3];if(w.type==="ordgroup"){if(w.body.length>0){var A=H(w.body[0],"textord");x=Nr[Number(A.text)]}}else w=H(w,"textord"),x=Nr[Number(w.text)];return{type:"genfrac",mode:t.mode,numer:a,denom:i,continued:!1,hasBarLine:g,barSize:b,leftDelim:o,rightDelim:c,size:x}},htmlBuilder:dt,mathmlBuilder:ft}),B({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(r,e){var{parser:t,funcName:a,token:i}=r;return{type:"infix",mode:t.mode,replaceWith:"\\\\abovefrac",size:H(e[0],"size").value,token:i}}}),B({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(r,e)=>{var{parser:t,funcName:a}=r,i=e[0],s=ba(H(e[1],"infix").size),o=e[2],h=s.number>0;return{type:"genfrac",mode:t.mode,numer:i,denom:o,continued:!1,hasBarLine:h,barSize:s,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:dt,mathmlBuilder:ft});var Er=(r,e)=>{var t=e.style,a,i;r.type==="supsub"?(a=r.sup?P(r.sup,e.havingStyle(t.sup()),e):P(r.sub,e.havingStyle(t.sub()),e),i=H(r.base,"horizBrace")):i=H(r,"horizBrace");var s=P(i.base,e.havingBaseStyle(q.DISPLAY)),o=T0.svgSpan(i,e),h;if(i.isOver?(h=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"kern",size:.1},{type:"elem",elem:o}]},e),h.children[0].children[0].children[1].classes.push("svg-align")):(h=y.makeVList({positionType:"bottom",positionData:s.depth+.1+o.height,children:[{type:"elem",elem:o},{type:"kern",size:.1},{type:"elem",elem:s}]},e),h.children[0].children[0].children[0].classes.push("svg-align")),a){var c=y.makeSpan(["mord",i.isOver?"mover":"munder"],[h],e);i.isOver?h=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:c},{type:"kern",size:.2},{type:"elem",elem:a}]},e):h=y.makeVList({positionType:"bottom",positionData:c.depth+.2+a.height+a.depth,children:[{type:"elem",elem:a},{type:"kern",size:.2},{type:"elem",elem:c}]},e)}return y.makeSpan(["mord",i.isOver?"mover":"munder"],[h],e)},G1=(r,e)=>{var t=T0.mathMLnode(r.label);return new S.MathNode(r.isOver?"mover":"munder",[Y(r.base,e),t])};B({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(r,e){var{parser:t,funcName:a}=r;return{type:"horizBrace",mode:t.mode,label:a,isOver:/^\\over/.test(a),base:e[0]}},htmlBuilder:Er,mathmlBuilder:G1}),B({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=e[1],i=H(e[0],"url").url;return t.settings.isTrusted({command:"\\href",url:i})?{type:"href",mode:t.mode,href:i,body:_(a)}:t.formatUnsupportedCmd("\\href")},htmlBuilder:(r,e)=>{var t=e0(r.body,e,!1);return y.makeAnchor(r.href,[],t,e)},mathmlBuilder:(r,e)=>{var t=O0(r.body,e);return t instanceof d0||(t=new d0("mrow",[t])),t.setAttribute("href",r.href),t}}),B({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=H(e[0],"url").url;if(!t.settings.isTrusted({command:"\\url",url:a}))return t.formatUnsupportedCmd("\\url");for(var i=[],s=0;s<a.length;s++){var o=a[s];o==="~"&&(o="\\textasciitilde"),i.push({type:"textord",mode:"text",text:o})}var h={type:"text",mode:t.mode,font:"\\texttt",body:i};return{type:"href",mode:t.mode,href:a,body:_(h)}}}),B({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(r,e){var{parser:t}=r;return{type:"hbox",mode:t.mode,body:_(e[0])}},htmlBuilder(r,e){var t=e0(r.body,e,!1);return y.makeFragment(t)},mathmlBuilder(r,e){return new S.MathNode("mrow",u0(r.body,e))}}),B({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(r,e)=>{var{parser:t,funcName:a,token:i}=r,s=H(e[0],"raw").string,o=e[1];t.settings.strict&&t.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var h,c={};switch(a){case"\\htmlClass":c.class=s,h={command:"\\htmlClass",class:s};break;case"\\htmlId":c.id=s,h={command:"\\htmlId",id:s};break;case"\\htmlStyle":c.style=s,h={command:"\\htmlStyle",style:s};break;case"\\htmlData":{for(var p=s.split(","),g=0;g<p.length;g++){var b=p[g].split("=");if(b.length!==2)throw new M("Error parsing key-value for \\htmlData");c["data-"+b[0].trim()]=b[1].trim()}h={command:"\\htmlData",attributes:c};break}default:throw new Error("Unrecognized html command")}return t.settings.isTrusted(h)?{type:"html",mode:t.mode,attributes:c,body:_(o)}:t.formatUnsupportedCmd(a)},htmlBuilder:(r,e)=>{var t=e0(r.body,e,!1),a=["enclosing"];r.attributes.class&&a.push(...r.attributes.class.trim().split(/\s+/));var i=y.makeSpan(a,t,e);for(var s in r.attributes)s!=="class"&&r.attributes.hasOwnProperty(s)&&i.setAttribute(s,r.attributes[s]);return i},mathmlBuilder:(r,e)=>O0(r.body,e)}),B({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(r,e)=>{var{parser:t}=r;return{type:"htmlmathml",mode:t.mode,html:_(e[0]),mathml:_(e[1])}},htmlBuilder:(r,e)=>{var t=e0(r.html,e,!1);return y.makeFragment(t)},mathmlBuilder:(r,e)=>O0(r.mathml,e)});var pt=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new M("Invalid size: '"+e+"' in \\includegraphics");var a={number:+(t[1]+t[2]),unit:t[3]};if(!It(a))throw new M("Invalid unit: '"+a.unit+"' in \\includegraphics.");return a};B({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(r,e,t)=>{var{parser:a}=r,i={number:0,unit:"em"},s={number:.9,unit:"em"},o={number:0,unit:"em"},h="";if(t[0])for(var c=H(t[0],"raw").string,p=c.split(","),g=0;g<p.length;g++){var b=p[g].split("=");if(b.length===2){var x=b[1].trim();switch(b[0].trim()){case"alt":h=x;break;case"width":i=pt(x);break;case"height":s=pt(x);break;case"totalheight":o=pt(x);break;default:throw new M("Invalid key: '"+b[0]+"' in \\includegraphics.")}}}var w=H(e[0],"url").url;return h===""&&(h=w,h=h.replace(/^.*[\\/]/,""),h=h.substring(0,h.lastIndexOf("."))),a.settings.isTrusted({command:"\\includegraphics",url:w})?{type:"includegraphics",mode:a.mode,alt:h,width:i,height:s,totalheight:o,src:w}:a.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(r,e)=>{var t=Z(r.height,e),a=0;r.totalheight.number>0&&(a=Z(r.totalheight,e)-t);var i=0;r.width.number>0&&(i=Z(r.width,e));var s={height:z(t+a)};i>0&&(s.width=z(i)),a>0&&(s.verticalAlign=z(-a));var o=new Ga(r.src,r.alt,s);return o.height=t,o.depth=a,o},mathmlBuilder:(r,e)=>{var t=new S.MathNode("mglyph",[]);t.setAttribute("alt",r.alt);var a=Z(r.height,e),i=0;if(r.totalheight.number>0&&(i=Z(r.totalheight,e)-a,t.setAttribute("valign",z(-i))),t.setAttribute("height",z(a+i)),r.width.number>0){var s=Z(r.width,e);t.setAttribute("width",z(s))}return t.setAttribute("src",r.src),t}}),B({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(r,e){var{parser:t,funcName:a}=r,i=H(e[0],"size");if(t.settings.strict){var s=a[1]==="m",o=i.value.unit==="mu";s?(o||t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" supports only mu units, "+("not "+i.value.unit+" units")),t.mode!=="math"&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" works only in math mode")):o&&t.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" doesn't support mu units")}return{type:"kern",mode:t.mode,dimension:i.value}},htmlBuilder(r,e){return y.makeGlue(r.dimension,e)},mathmlBuilder(r,e){var t=Z(r.dimension,e);return new S.SpaceNode(t)}}),B({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(r,e)=>{var{parser:t,funcName:a}=r,i=e[0];return{type:"lap",mode:t.mode,alignment:a.slice(5),body:i}},htmlBuilder:(r,e)=>{var t;r.alignment==="clap"?(t=y.makeSpan([],[P(r.body,e)]),t=y.makeSpan(["inner"],[t],e)):t=y.makeSpan(["inner"],[P(r.body,e)]);var a=y.makeSpan(["fix"],[]),i=y.makeSpan([r.alignment],[t,a],e),s=y.makeSpan(["strut"]);return s.style.height=z(i.height+i.depth),i.depth&&(s.style.verticalAlign=z(-i.depth)),i.children.unshift(s),i=y.makeSpan(["thinbox"],[i],e),y.makeSpan(["mord","vbox"],[i],e)},mathmlBuilder:(r,e)=>{var t=new S.MathNode("mpadded",[Y(r.body,e)]);if(r.alignment!=="rlap"){var a=r.alignment==="llap"?"-1":"-0.5";t.setAttribute("lspace",a+"width")}return t.setAttribute("width","0px"),t}}),B({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(r,e){var{funcName:t,parser:a}=r,i=a.mode;a.switchMode("math");var s=t==="\\("?"\\)":"$",o=a.parseExpression(!1,s);return a.expect(s),a.switchMode(i),{type:"styling",mode:a.mode,style:"text",body:o}}}),B({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(r,e){throw new M("Mismatched "+r.funcName)}});var Rr=(r,e)=>{switch(e.style.size){case q.DISPLAY.size:return r.display;case q.TEXT.size:return r.text;case q.SCRIPT.size:return r.script;case q.SCRIPTSCRIPT.size:return r.scriptscript;default:return r.text}};B({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(r,e)=>{var{parser:t}=r;return{type:"mathchoice",mode:t.mode,display:_(e[0]),text:_(e[1]),script:_(e[2]),scriptscript:_(e[3])}},htmlBuilder:(r,e)=>{var t=Rr(r,e),a=e0(t,e,!1);return y.makeFragment(a)},mathmlBuilder:(r,e)=>{var t=Rr(r,e);return O0(t,e)}});var Ir=(r,e,t,a,i,s,o)=>{r=y.makeSpan([],[r]);var h=t&&R.isCharacterBox(t),c,p;if(e){var g=P(e,a.havingStyle(i.sup()),a);p={elem:g,kern:Math.max(a.fontMetrics().bigOpSpacing1,a.fontMetrics().bigOpSpacing3-g.depth)}}if(t){var b=P(t,a.havingStyle(i.sub()),a);c={elem:b,kern:Math.max(a.fontMetrics().bigOpSpacing2,a.fontMetrics().bigOpSpacing4-b.height)}}var x;if(p&&c){var w=a.fontMetrics().bigOpSpacing5+c.elem.height+c.elem.depth+c.kern+r.depth+o;x=y.makeVList({positionType:"bottom",positionData:w,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:c.elem,marginLeft:z(-s)},{type:"kern",size:c.kern},{type:"elem",elem:r},{type:"kern",size:p.kern},{type:"elem",elem:p.elem,marginLeft:z(s)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else if(c){var A=r.height-o;x=y.makeVList({positionType:"top",positionData:A,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:c.elem,marginLeft:z(-s)},{type:"kern",size:c.kern},{type:"elem",elem:r}]},a)}else if(p){var T=r.depth+o;x=y.makeVList({positionType:"bottom",positionData:T,children:[{type:"elem",elem:r},{type:"kern",size:p.kern},{type:"elem",elem:p.elem,marginLeft:z(s)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else return r;var N=[x];if(c&&s!==0&&!h){var C=y.makeSpan(["mspace"],[],a);C.style.marginRight=z(s),N.unshift(C)}return y.makeSpan(["mop","op-limits"],N,a)},Or=["\\smallint"],j0=(r,e)=>{var t,a,i=!1,s;r.type==="supsub"?(t=r.sup,a=r.sub,s=H(r.base,"op"),i=!0):s=H(r,"op");var o=e.style,h=!1;o.size===q.DISPLAY.size&&s.symbol&&!R.contains(Or,s.name)&&(h=!0);var c;if(s.symbol){var p=h?"Size2-Regular":"Size1-Regular",g="";if((s.name==="\\oiint"||s.name==="\\oiiint")&&(g=s.name.substr(1),s.name=g==="oiint"?"\\iint":"\\iiint"),c=y.makeSymbol(s.name,p,"math",e,["mop","op-symbol",h?"large-op":"small-op"]),g.length>0){var b=c.italic,x=y.staticSvg(g+"Size"+(h?"2":"1"),e);c=y.makeVList({positionType:"individualShift",children:[{type:"elem",elem:c,shift:0},{type:"elem",elem:x,shift:h?.08:0}]},e),s.name="\\"+g,c.classes.unshift("mop"),c.italic=b}}else if(s.body){var w=e0(s.body,e,!0);w.length===1&&w[0]instanceof c0?(c=w[0],c.classes[0]="mop"):c=y.makeSpan(["mop"],w,e)}else{for(var A=[],T=1;T<s.name.length;T++)A.push(y.mathsym(s.name[T],s.mode,e));c=y.makeSpan(["mop"],A,e)}var N=0,C=0;return(c instanceof c0||s.name==="\\oiint"||s.name==="\\oiiint")&&!s.suppressBaseShift&&(N=(c.height-c.depth)/2-e.fontMetrics().axisHeight,C=c.italic),i?Ir(c,t,a,e,o,C,N):(N&&(c.style.position="relative",c.style.top=z(N)),c)},le=(r,e)=>{var t;if(r.symbol)t=new d0("mo",[f0(r.name,r.mode)]),R.contains(Or,r.name)&&t.setAttribute("largeop","false");else if(r.body)t=new d0("mo",u0(r.body,e));else{t=new d0("mi",[new ie(r.name.slice(1))]);var a=new d0("mo",[f0("\u2061","text")]);r.parentIsSupSub?t=new d0("mrow",[t,a]):t=_t([t,a])}return t},V1={"\u220F":"\\prod","\u2210":"\\coprod","\u2211":"\\sum","\u22C0":"\\bigwedge","\u22C1":"\\bigvee","\u22C2":"\\bigcap","\u22C3":"\\bigcup","\u2A00":"\\bigodot","\u2A01":"\\bigoplus","\u2A02":"\\bigotimes","\u2A04":"\\biguplus","\u2A06":"\\bigsqcup"};B({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","\u220F","\u2210","\u2211","\u22C0","\u22C1","\u22C2","\u22C3","\u2A00","\u2A01","\u2A02","\u2A04","\u2A06"],props:{numArgs:0},handler:(r,e)=>{var{parser:t,funcName:a}=r,i=a;return i.length===1&&(i=V1[i]),{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:i}},htmlBuilder:j0,mathmlBuilder:le}),B({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(r,e)=>{var{parser:t}=r,a=e[0];return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:_(a)}},htmlBuilder:j0,mathmlBuilder:le});var U1={"\u222B":"\\int","\u222C":"\\iint","\u222D":"\\iiint","\u222E":"\\oint","\u222F":"\\oiint","\u2230":"\\oiiint"};B({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r;return{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:j0,mathmlBuilder:le}),B({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r;return{type:"op",mode:e.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:t}},htmlBuilder:j0,mathmlBuilder:le}),B({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","\u222B","\u222C","\u222D","\u222E","\u222F","\u2230"],props:{numArgs:0},handler(r){var{parser:e,funcName:t}=r,a=t;return a.length===1&&(a=U1[a]),{type:"op",mode:e.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:j0,mathmlBuilder:le});var Hr=(r,e)=>{var t,a,i=!1,s;r.type==="supsub"?(t=r.sup,a=r.sub,s=H(r.base,"operatorname"),i=!0):s=H(r,"operatorname");var o;if(s.body.length>0){for(var h=s.body.map(b=>{var x=b.text;return typeof x=="string"?{type:"textord",mode:b.mode,text:x}:b}),c=e0(h,e.withFont("mathrm"),!0),p=0;p<c.length;p++){var g=c[p];g instanceof c0&&(g.text=g.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}o=y.makeSpan(["mop"],c,e)}else o=y.makeSpan(["mop"],[],e);return i?Ir(o,t,a,e,e.style,0,0):o},Y1=(r,e)=>{for(var t=u0(r.body,e.withFont("mathrm")),a=!0,i=0;i<t.length;i++){var s=t[i];if(!(s instanceof S.SpaceNode))if(s instanceof S.MathNode)switch(s.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{var o=s.children[0];s.children.length===1&&o instanceof S.TextNode?o.text=o.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):a=!1;break}default:a=!1}else a=!1}if(a){var h=t.map(g=>g.toText()).join("");t=[new S.TextNode(h)]}var c=new S.MathNode("mi",t);c.setAttribute("mathvariant","normal");var p=new S.MathNode("mo",[f0("\u2061","text")]);return r.parentIsSupSub?new S.MathNode("mrow",[c,p]):S.newDocumentFragment([c,p])};B({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(r,e)=>{var{parser:t,funcName:a}=r,i=e[0];return{type:"operatorname",mode:t.mode,body:_(i),alwaysHandleSupSub:a==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:Hr,mathmlBuilder:Y1}),m("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),U0({type:"ordgroup",htmlBuilder(r,e){return r.semisimple?y.makeFragment(e0(r.body,e,!1)):y.makeSpan(["mord"],e0(r.body,e,!0),e)},mathmlBuilder(r,e){return O0(r.body,e,!0)}}),B({type:"overline",names:["\\overline"],props:{numArgs:1},handler(r,e){var{parser:t}=r,a=e[0];return{type:"overline",mode:t.mode,body:a}},htmlBuilder(r,e){var t=P(r.body,e.havingCrampedStyle()),a=y.makeLineSpan("overline-line",e),i=e.fontMetrics().defaultRuleThickness,s=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t},{type:"kern",size:3*i},{type:"elem",elem:a},{type:"kern",size:i}]},e);return y.makeSpan(["mord","overline"],[s],e)},mathmlBuilder(r,e){var t=new S.MathNode("mo",[new S.TextNode("\u203E")]);t.setAttribute("stretchy","true");var a=new S.MathNode("mover",[Y(r.body,e),t]);return a.setAttribute("accent","true"),a}}),B({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=e[0];return{type:"phantom",mode:t.mode,body:_(a)}},htmlBuilder:(r,e)=>{var t=e0(r.body,e.withPhantom(),!1);return y.makeFragment(t)},mathmlBuilder:(r,e)=>{var t=u0(r.body,e);return new S.MathNode("mphantom",t)}}),B({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=e[0];return{type:"hphantom",mode:t.mode,body:a}},htmlBuilder:(r,e)=>{var t=y.makeSpan([],[P(r.body,e.withPhantom())]);if(t.height=0,t.depth=0,t.children)for(var a=0;a<t.children.length;a++)t.children[a].height=0,t.children[a].depth=0;return t=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e),y.makeSpan(["mord"],[t],e)},mathmlBuilder:(r,e)=>{var t=u0(_(r.body),e),a=new S.MathNode("mphantom",t),i=new S.MathNode("mpadded",[a]);return i.setAttribute("height","0px"),i.setAttribute("depth","0px"),i}}),B({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(r,e)=>{var{parser:t}=r,a=e[0];return{type:"vphantom",mode:t.mode,body:a}},htmlBuilder:(r,e)=>{var t=y.makeSpan(["inner"],[P(r.body,e.withPhantom())]),a=y.makeSpan(["fix"],[]);return y.makeSpan(["mord","rlap"],[t,a],e)},mathmlBuilder:(r,e)=>{var t=u0(_(r.body),e),a=new S.MathNode("mphantom",t),i=new S.MathNode("mpadded",[a]);return i.setAttribute("width","0px"),i}}),B({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(r,e){var{parser:t}=r,a=H(e[0],"size").value,i=e[1];return{type:"raisebox",mode:t.mode,dy:a,body:i}},htmlBuilder(r,e){var t=P(r.body,e),a=Z(r.dy,e);return y.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(r,e){var t=new S.MathNode("mpadded",[Y(r.body,e)]),a=r.dy.number+r.dy.unit;return t.setAttribute("voffset",a),t}}),B({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0},handler(r){var{parser:e}=r;return{type:"internal",mode:e.mode}}}),B({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler(r,e,t){var{parser:a}=r,i=t[0],s=H(e[0],"size"),o=H(e[1],"size");return{type:"rule",mode:a.mode,shift:i&&H(i,"size").value,width:s.value,height:o.value}},htmlBuilder(r,e){var t=y.makeSpan(["mord","rule"],[],e),a=Z(r.width,e),i=Z(r.height,e),s=r.shift?Z(r.shift,e):0;return t.style.borderRightWidth=z(a),t.style.borderTopWidth=z(i),t.style.bottom=z(s),t.width=a,t.height=i+s,t.depth=-s,t.maxFontSize=i*1.125*e.sizeMultiplier,t},mathmlBuilder(r,e){var t=Z(r.width,e),a=Z(r.height,e),i=r.shift?Z(r.shift,e):0,s=e.color&&e.getColor()||"black",o=new S.MathNode("mspace");o.setAttribute("mathbackground",s),o.setAttribute("width",z(t)),o.setAttribute("height",z(a));var h=new S.MathNode("mpadded",[o]);return i>=0?h.setAttribute("height",z(i)):(h.setAttribute("height",z(i)),h.setAttribute("depth",z(-i))),h.setAttribute("voffset",z(i)),h}});function Lr(r,e,t){for(var a=e0(r,e,!1),i=e.sizeMultiplier/t.sizeMultiplier,s=0;s<a.length;s++){var o=a[s].classes.indexOf("sizing");o<0?Array.prototype.push.apply(a[s].classes,e.sizingClasses(t)):a[s].classes[o+1]==="reset-size"+e.size&&(a[s].classes[o+1]="reset-size"+t.size),a[s].height*=i,a[s].depth*=i}return y.makeFragment(a)}var Fr=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],X1=(r,e)=>{var t=e.havingSize(r.size);return Lr(r.body,t,e)};B({type:"sizing",names:Fr,props:{numArgs:0,allowedInText:!0},handler:(r,e)=>{var{breakOnTokenText:t,funcName:a,parser:i}=r,s=i.parseExpression(!1,t);return{type:"sizing",mode:i.mode,size:Fr.indexOf(a)+1,body:s}},htmlBuilder:X1,mathmlBuilder:(r,e)=>{var t=e.havingSize(r.size),a=u0(r.body,t),i=new S.MathNode("mstyle",a);return i.setAttribute("mathsize",z(t.sizeMultiplier)),i}}),B({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(r,e,t)=>{var{parser:a}=r,i=!1,s=!1,o=t[0]&&H(t[0],"ordgroup");if(o)for(var h="",c=0;c<o.body.length;++c){var p=o.body[c];if(h=p.text,h==="t")i=!0;else if(h==="b")s=!0;else{i=!1,s=!1;break}}else i=!0,s=!0;var g=e[0];return{type:"smash",mode:a.mode,body:g,smashHeight:i,smashDepth:s}},htmlBuilder:(r,e)=>{var t=y.makeSpan([],[P(r.body,e)]);if(!r.smashHeight&&!r.smashDepth)return t;if(r.smashHeight&&(t.height=0,t.children))for(var a=0;a<t.children.length;a++)t.children[a].height=0;if(r.smashDepth&&(t.depth=0,t.children))for(var i=0;i<t.children.length;i++)t.children[i].depth=0;var s=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t}]},e);return y.makeSpan(["mord"],[s],e)},mathmlBuilder:(r,e)=>{var t=new S.MathNode("mpadded",[Y(r.body,e)]);return r.smashHeight&&t.setAttribute("height","0px"),r.smashDepth&&t.setAttribute("depth","0px"),t}}),B({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(r,e,t){var{parser:a}=r,i=t[0],s=e[0];return{type:"sqrt",mode:a.mode,body:s,index:i}},htmlBuilder(r,e){var t=P(r.body,e.havingCrampedStyle());t.height===0&&(t.height=e.fontMetrics().xHeight),t=y.wrapFragment(t,e);var a=e.fontMetrics(),i=a.defaultRuleThickness,s=i;e.style.id<q.TEXT.id&&(s=e.fontMetrics().xHeight);var o=i+s/4,h=t.height+t.depth+o+i,{span:c,ruleWidth:p,advanceWidth:g}=B0.sqrtImage(h,e),b=c.height-p;b>t.height+t.depth+o&&(o=(o+b-t.height-t.depth)/2);var x=c.height-t.height-o-p;t.style.paddingLeft=z(g);var w=y.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:t,wrapperClasses:["svg-align"]},{type:"kern",size:-(t.height+x)},{type:"elem",elem:c},{type:"kern",size:p}]},e);if(r.index){var A=e.havingStyle(q.SCRIPTSCRIPT),T=P(r.index,A,e),N=.6*(w.height-w.depth),C=y.makeVList({positionType:"shift",positionData:-N,children:[{type:"elem",elem:T}]},e),I=y.makeSpan(["root"],[C]);return y.makeSpan(["mord","sqrt"],[I,w],e)}else return y.makeSpan(["mord","sqrt"],[w],e)},mathmlBuilder(r,e){var{body:t,index:a}=r;return a?new S.MathNode("mroot",[Y(t,e),Y(a,e)]):new S.MathNode("msqrt",[Y(t,e)])}});var Pr={display:q.DISPLAY,text:q.TEXT,script:q.SCRIPT,scriptscript:q.SCRIPTSCRIPT};B({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(r,e){var{breakOnTokenText:t,funcName:a,parser:i}=r,s=i.parseExpression(!0,t),o=a.slice(1,a.length-5);return{type:"styling",mode:i.mode,style:o,body:s}},htmlBuilder(r,e){var t=Pr[r.style],a=e.havingStyle(t).withFont("");return Lr(r.body,a,e)},mathmlBuilder(r,e){var t=Pr[r.style],a=e.havingStyle(t),i=u0(r.body,a),s=new S.MathNode("mstyle",i),o={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]},h=o[r.style];return s.setAttribute("scriptlevel",h[0]),s.setAttribute("displaystyle",h[1]),s}});var $1=function(e,t){var a=e.base;if(a)if(a.type==="op"){var i=a.limits&&(t.style.size===q.DISPLAY.size||a.alwaysHandleSupSub);return i?j0:null}else if(a.type==="operatorname"){var s=a.alwaysHandleSupSub&&(t.style.size===q.DISPLAY.size||a.limits);return s?Hr:null}else{if(a.type==="accent")return R.isCharacterBox(a.base)?_e:null;if(a.type==="horizBrace"){var o=!e.sub;return o===a.isOver?Er:null}else return null}else return null};U0({type:"supsub",htmlBuilder(r,e){var t=$1(r,e);if(t)return t(r,e);var{base:a,sup:i,sub:s}=r,o=P(a,e),h,c,p=e.fontMetrics(),g=0,b=0,x=a&&R.isCharacterBox(a);if(i){var w=e.havingStyle(e.style.sup());h=P(i,w,e),x||(g=o.height-w.fontMetrics().supDrop*w.sizeMultiplier/e.sizeMultiplier)}if(s){var A=e.havingStyle(e.style.sub());c=P(s,A,e),x||(b=o.depth+A.fontMetrics().subDrop*A.sizeMultiplier/e.sizeMultiplier)}var T;e.style===q.DISPLAY?T=p.sup1:e.style.cramped?T=p.sup3:T=p.sup2;var N=e.sizeMultiplier,C=z(.5/p.ptPerEm/N),I=null;if(c){var L=r.base&&r.base.type==="op"&&r.base.name&&(r.base.name==="\\oiint"||r.base.name==="\\oiiint");(o instanceof c0||L)&&(I=z(-o.italic))}var V;if(h&&c){g=Math.max(g,T,h.depth+.25*p.xHeight),b=Math.max(b,p.sub2);var F=p.defaultRuleThickness,U=4*F;if(g-h.depth-(c.height-b)<U){b=U-(g-h.depth)+c.height;var G=.8*p.xHeight-(g-h.depth);G>0&&(g+=G,b-=G)}var W=[{type:"elem",elem:c,shift:b,marginRight:C,marginLeft:I},{type:"elem",elem:h,shift:-g,marginRight:C}];V=y.makeVList({positionType:"individualShift",children:W},e)}else if(c){b=Math.max(b,p.sub1,c.height-.8*p.xHeight);var j=[{type:"elem",elem:c,marginLeft:I,marginRight:C}];V=y.makeVList({positionType:"shift",positionData:b,children:j},e)}else if(h)g=Math.max(g,T,h.depth+.25*p.xHeight),V=y.makeVList({positionType:"shift",positionData:-g,children:[{type:"elem",elem:h,marginRight:C}]},e);else throw new Error("supsub must have either sup or sub.");var t0=je(o,"right")||"mord";return y.makeSpan([t0],[o,y.makeSpan(["msupsub"],[V])],e)},mathmlBuilder(r,e){var t=!1,a,i;r.base&&r.base.type==="horizBrace"&&(i=!!r.sup,i===r.base.isOver&&(t=!0,a=r.base.isOver)),r.base&&(r.base.type==="op"||r.base.type==="operatorname")&&(r.base.parentIsSupSub=!0);var s=[Y(r.base,e)];r.sub&&s.push(Y(r.sub,e)),r.sup&&s.push(Y(r.sup,e));var o;if(t)o=a?"mover":"munder";else if(r.sub)if(r.sup){var p=r.base;p&&p.type==="op"&&p.limits&&e.style===q.DISPLAY||p&&p.type==="operatorname"&&p.alwaysHandleSupSub&&(e.style===q.DISPLAY||p.limits)?o="munderover":o="msubsup"}else{var c=r.base;c&&c.type==="op"&&c.limits&&(e.style===q.DISPLAY||c.alwaysHandleSupSub)||c&&c.type==="operatorname"&&c.alwaysHandleSupSub&&(c.limits||e.style===q.DISPLAY)?o="munder":o="msub"}else{var h=r.base;h&&h.type==="op"&&h.limits&&(e.style===q.DISPLAY||h.alwaysHandleSupSub)||h&&h.type==="operatorname"&&h.alwaysHandleSupSub&&(h.limits||e.style===q.DISPLAY)?o="mover":o="msup"}return new S.MathNode(o,s)}}),U0({type:"atom",htmlBuilder(r,e){return y.mathsym(r.text,r.mode,e,["m"+r.family])},mathmlBuilder(r,e){var t=new S.MathNode("mo",[f0(r.text,r.mode)]);if(r.family==="bin"){var a=Je(r,e);a==="bold-italic"&&t.setAttribute("mathvariant",a)}else r.family==="punct"?t.setAttribute("separator","true"):(r.family==="open"||r.family==="close")&&t.setAttribute("stretchy","false");return t}});var Gr={mi:"italic",mn:"normal",mtext:"normal"};U0({type:"mathord",htmlBuilder(r,e){return y.makeOrd(r,e,"mathord")},mathmlBuilder(r,e){var t=new S.MathNode("mi",[f0(r.text,r.mode,e)]),a=Je(r,e)||"italic";return a!==Gr[t.type]&&t.setAttribute("mathvariant",a),t}}),U0({type:"textord",htmlBuilder(r,e){return y.makeOrd(r,e,"textord")},mathmlBuilder(r,e){var t=f0(r.text,r.mode,e),a=Je(r,e)||"normal",i;return r.mode==="text"?i=new S.MathNode("mtext",[t]):/[0-9]/.test(r.text)?i=new S.MathNode("mn",[t]):r.text==="\\prime"?i=new S.MathNode("mo",[t]):i=new S.MathNode("mi",[t]),a!==Gr[i.type]&&i.setAttribute("mathvariant",a),i}});var vt={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},gt={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};U0({type:"spacing",htmlBuilder(r,e){if(gt.hasOwnProperty(r.text)){var t=gt[r.text].className||"";if(r.mode==="text"){var a=y.makeOrd(r,e,"textord");return a.classes.push(t),a}else return y.makeSpan(["mspace",t],[y.mathsym(r.text,r.mode,e)],e)}else{if(vt.hasOwnProperty(r.text))return y.makeSpan(["mspace",vt[r.text]],[],e);throw new M('Unknown type of space "'+r.text+'"')}},mathmlBuilder(r,e){var t;if(gt.hasOwnProperty(r.text))t=new S.MathNode("mtext",[new S.TextNode("\xA0")]);else{if(vt.hasOwnProperty(r.text))return new S.MathNode("mspace");throw new M('Unknown type of space "'+r.text+'"')}return t}});var Vr=()=>{var r=new S.MathNode("mtd",[]);return r.setAttribute("width","50%"),r};U0({type:"tag",mathmlBuilder(r,e){var t=new S.MathNode("mtable",[new S.MathNode("mtr",[Vr(),new S.MathNode("mtd",[O0(r.body,e)]),Vr(),new S.MathNode("mtd",[O0(r.tag,e)])])]);return t.setAttribute("width","100%"),t}});var Ur={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Yr={"\\textbf":"textbf","\\textmd":"textmd"},W1={"\\textit":"textit","\\textup":"textup"},Xr=(r,e)=>{var t=r.font;return t?Ur[t]?e.withTextFontFamily(Ur[t]):Yr[t]?e.withTextFontWeight(Yr[t]):e.withTextFontShape(W1[t]):e};B({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(r,e){var{parser:t,funcName:a}=r,i=e[0];return{type:"text",mode:t.mode,body:_(i),font:a}},htmlBuilder(r,e){var t=Xr(r,e),a=e0(r.body,t,!0);return y.makeSpan(["mord","text"],a,t)},mathmlBuilder(r,e){var t=Xr(r,e);return O0(r.body,t)}}),B({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(r,e){var{parser:t}=r;return{type:"underline",mode:t.mode,body:e[0]}},htmlBuilder(r,e){var t=P(r.body,e),a=y.makeLineSpan("underline-line",e),i=e.fontMetrics().defaultRuleThickness,s=y.makeVList({positionType:"top",positionData:t.height,children:[{type:"kern",size:i},{type:"elem",elem:a},{type:"kern",size:3*i},{type:"elem",elem:t}]},e);return y.makeSpan(["mord","underline"],[s],e)},mathmlBuilder(r,e){var t=new S.MathNode("mo",[new S.TextNode("\u203E")]);t.setAttribute("stretchy","true");var a=new S.MathNode("munder",[Y(r.body,e),t]);return a.setAttribute("accentunder","true"),a}}),B({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(r,e){var{parser:t}=r;return{type:"vcenter",mode:t.mode,body:e[0]}},htmlBuilder(r,e){var t=P(r.body,e),a=e.fontMetrics().axisHeight,i=.5*(t.height-a-(t.depth+a));return y.makeVList({positionType:"shift",positionData:i,children:[{type:"elem",elem:t}]},e)},mathmlBuilder(r,e){return new S.MathNode("mpadded",[Y(r.body,e)],["vcenter"])}}),B({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(r,e,t){throw new M("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(r,e){for(var t=$r(r),a=[],i=e.havingStyle(e.style.text()),s=0;s<t.length;s++){var o=t[s];o==="~"&&(o="\\textasciitilde"),a.push(y.makeSymbol(o,"Typewriter-Regular",r.mode,i,["mord","texttt"]))}return y.makeSpan(["mord","text"].concat(i.sizingClasses(e)),y.tryCombineChars(a),i)},mathmlBuilder(r,e){var t=new S.TextNode($r(r)),a=new S.MathNode("mtext",[t]);return a.setAttribute("mathvariant","monospace"),a}});var $r=r=>r.body.replace(/ /g,r.star?"\u2423":"\xA0"),L0=Kt,Wr=`[ \r
	]`,j1="\\\\[a-zA-Z@]+",Z1="\\\\[^\uD800-\uDFFF]",K1="("+j1+")"+Wr+"*",J1=`\\\\(
|[ \r	]+
?)[ \r	]*`,bt="[\u0300-\u036F]",Q1=new RegExp(bt+"+$"),_1="("+Wr+"+)|"+(J1+"|")+"([!-\\[\\]-\u2027\u202A-\uD7FF\uF900-\uFFFF]"+(bt+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(bt+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+K1)+("|"+Z1+")");class jr{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=new RegExp(_1,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new h0("EOF",new l0(this,t,t));var a=this.tokenRegex.exec(e);if(a===null||a.index!==t)throw new M("Unexpected character: '"+e[t]+"'",new h0(e[t],new l0(this,t,t+1)));var i=a[6]||a[3]||(a[2]?"\\ ":" ");if(this.catcodes[i]===14){var s=e.indexOf(`
`,this.tokenRegex.lastIndex);return s===-1?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=s+1,this.lex()}return new h0(i,new l0(this,t,this.tokenRegex.lastIndex))}}class e4{constructor(e,t){e===void 0&&(e={}),t===void 0&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new M("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(e[t]==null?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,a){if(a===void 0&&(a=!1),a){for(var i=0;i<this.undefStack.length;i++)delete this.undefStack[i][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var s=this.undefStack[this.undefStack.length-1];s&&!s.hasOwnProperty(e)&&(s[e]=this.current[e])}t==null?delete this.current[e]:this.current[e]=t}}var t4=wr;m("\\noexpand",function(r){var e=r.popToken();return r.isExpandable(e.text)&&(e.noexpand=!0,e.treatAsRelax=!0),{tokens:[e],numArgs:0}}),m("\\expandafter",function(r){var e=r.popToken();return r.expandOnce(!0),{tokens:[e],numArgs:0}}),m("\\@firstoftwo",function(r){var e=r.consumeArgs(2);return{tokens:e[0],numArgs:0}}),m("\\@secondoftwo",function(r){var e=r.consumeArgs(2);return{tokens:e[1],numArgs:0}}),m("\\@ifnextchar",function(r){var e=r.consumeArgs(3);r.consumeSpaces();var t=r.future();return e[0].length===1&&e[0][0].text===t.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}}),m("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),m("\\TextOrMath",function(r){var e=r.consumeArgs(2);return r.mode==="text"?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});var Zr={"0":0,"1":1,"2":2,"3":3,"4":4,"5":5,"6":6,"7":7,"8":8,"9":9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};m("\\char",function(r){var e=r.popToken(),t,a="";if(e.text==="'")t=8,e=r.popToken();else if(e.text==='"')t=16,e=r.popToken();else if(e.text==="`")if(e=r.popToken(),e.text[0]==="\\")a=e.text.charCodeAt(1);else{if(e.text==="EOF")throw new M("\\char` missing argument");a=e.text.charCodeAt(0)}else t=10;if(t){if(a=Zr[e.text],a==null||a>=t)throw new M("Invalid base-"+t+" digit "+e.text);for(var i;(i=Zr[r.future().text])!=null&&i<t;)a*=t,a+=i,r.popToken()}return"\\@char{"+a+"}"});var yt=(r,e,t)=>{var a=r.consumeArg().tokens;if(a.length!==1)throw new M("\\newcommand's first argument must be a macro name");var i=a[0].text,s=r.isDefined(i);if(s&&!e)throw new M("\\newcommand{"+i+"} attempting to redefine "+(i+"; use \\renewcommand"));if(!s&&!t)throw new M("\\renewcommand{"+i+"} when command "+i+" does not yet exist; use \\newcommand");var o=0;if(a=r.consumeArg().tokens,a.length===1&&a[0].text==="["){for(var h="",c=r.expandNextToken();c.text!=="]"&&c.text!=="EOF";)h+=c.text,c=r.expandNextToken();if(!h.match(/^\s*[0-9]+\s*$/))throw new M("Invalid number of arguments: "+h);o=parseInt(h),a=r.consumeArg().tokens}return r.macros.set(i,{tokens:a,numArgs:o}),""};m("\\newcommand",r=>yt(r,!1,!0)),m("\\renewcommand",r=>yt(r,!0,!1)),m("\\providecommand",r=>yt(r,!0,!0)),m("\\message",r=>{var e=r.consumeArgs(1)[0];return console.log(e.reverse().map(t=>t.text).join("")),""}),m("\\errmessage",r=>{var e=r.consumeArgs(1)[0];return console.error(e.reverse().map(t=>t.text).join("")),""}),m("\\show",r=>{var e=r.popToken(),t=e.text;return console.log(e,r.macros.get(t),L0[t],X.math[t],X.text[t]),""}),m("\\bgroup","{"),m("\\egroup","}"),m("~","\\nobreakspace"),m("\\lq","`"),m("\\rq","'"),m("\\aa","\\r a"),m("\\AA","\\r A"),m("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`\xA9}"),m("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),m("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xAE}"),m("\u212C","\\mathscr{B}"),m("\u2130","\\mathscr{E}"),m("\u2131","\\mathscr{F}"),m("\u210B","\\mathscr{H}"),m("\u2110","\\mathscr{I}"),m("\u2112","\\mathscr{L}"),m("\u2133","\\mathscr{M}"),m("\u211B","\\mathscr{R}"),m("\u212D","\\mathfrak{C}"),m("\u210C","\\mathfrak{H}"),m("\u2128","\\mathfrak{Z}"),m("\\Bbbk","\\Bbb{k}"),m("\xB7","\\cdotp"),m("\\llap","\\mathllap{\\textrm{#1}}"),m("\\rlap","\\mathrlap{\\textrm{#1}}"),m("\\clap","\\mathclap{\\textrm{#1}}"),m("\\mathstrut","\\vphantom{(}"),m("\\underbar","\\underline{\\text{#1}}"),m("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),m("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`\u2260}}"),m("\\ne","\\neq"),m("\u2260","\\neq"),m("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`\u2209}}"),m("\u2209","\\notin"),m("\u2258","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`\u2258}}"),m("\u2259","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`\u2258}}"),m("\u225A","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`\u225A}}"),m("\u225B","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`\u225B}}"),m("\u225D","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`\u225D}}"),m("\u225E","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`\u225E}}"),m("\u225F","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`\u225F}}"),m("\u27C2","\\perp"),m("\u203C","\\mathclose{!\\mkern-0.8mu!}"),m("\u220C","\\notni"),m("\u231C","\\ulcorner"),m("\u231D","\\urcorner"),m("\u231E","\\llcorner"),m("\u231F","\\lrcorner"),m("\xA9","\\copyright"),m("\xAE","\\textregistered"),m("\uFE0F","\\textregistered"),m("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),m("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),m("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),m("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),m("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}"),m("\u22EE","\\vdots"),m("\\varGamma","\\mathit{\\Gamma}"),m("\\varDelta","\\mathit{\\Delta}"),m("\\varTheta","\\mathit{\\Theta}"),m("\\varLambda","\\mathit{\\Lambda}"),m("\\varXi","\\mathit{\\Xi}"),m("\\varPi","\\mathit{\\Pi}"),m("\\varSigma","\\mathit{\\Sigma}"),m("\\varUpsilon","\\mathit{\\Upsilon}"),m("\\varPhi","\\mathit{\\Phi}"),m("\\varPsi","\\mathit{\\Psi}"),m("\\varOmega","\\mathit{\\Omega}"),m("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),m("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),m("\\boxed","\\fbox{$\\displaystyle{#1}$}"),m("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),m("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),m("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");var Kr={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};m("\\dots",function(r){var e="\\dotso",t=r.expandAfterFuture().text;return t in Kr?e=Kr[t]:(t.substr(0,4)==="\\not"||t in X.math&&R.contains(["bin","rel"],X.math[t].group))&&(e="\\dotsb"),e});var xt={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};m("\\dotso",function(r){var e=r.future().text;return e in xt?"\\ldots\\,":"\\ldots"}),m("\\dotsc",function(r){var e=r.future().text;return e in xt&&e!==","?"\\ldots\\,":"\\ldots"}),m("\\cdots",function(r){var e=r.future().text;return e in xt?"\\@cdots\\,":"\\@cdots"}),m("\\dotsb","\\cdots"),m("\\dotsm","\\cdots"),m("\\dotsi","\\!\\cdots"),m("\\dotsx","\\ldots\\,"),m("\\DOTSI","\\relax"),m("\\DOTSB","\\relax"),m("\\DOTSX","\\relax"),m("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),m("\\,","\\tmspace+{3mu}{.1667em}"),m("\\thinspace","\\,"),m("\\>","\\mskip{4mu}"),m("\\:","\\tmspace+{4mu}{.2222em}"),m("\\medspace","\\:"),m("\\;","\\tmspace+{5mu}{.2777em}"),m("\\thickspace","\\;"),m("\\!","\\tmspace-{3mu}{.1667em}"),m("\\negthinspace","\\!"),m("\\negmedspace","\\tmspace-{4mu}{.2222em}"),m("\\negthickspace","\\tmspace-{5mu}{.277em}"),m("\\enspace","\\kern.5em "),m("\\enskip","\\hskip.5em\\relax"),m("\\quad","\\hskip1em\\relax"),m("\\qquad","\\hskip2em\\relax"),m("\\tag","\\@ifstar\\tag@literal\\tag@paren"),m("\\tag@paren","\\tag@literal{({#1})}"),m("\\tag@literal",r=>{if(r.macros.get("\\df@tag"))throw new M("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),m("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),m("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),m("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),m("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),m("\\pmb","\\html@mathml{\\@binrel{#1}{\\mathrlap{#1}\\kern0.5px#1}}{\\mathbf{#1}}"),m("\\newline","\\\\\\relax"),m("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var Jr=z(b0["Main-Regular"]["T".charCodeAt(0)][1]-.7*b0["Main-Regular"]["A".charCodeAt(0)][1]);m("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+Jr+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}"),m("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+Jr+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}"),m("\\hspace","\\@ifstar\\@hspacer\\@hspace"),m("\\@hspace","\\hskip #1\\relax"),m("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),m("\\ordinarycolon",":"),m("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),m("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),m("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),m("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),m("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),m("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),m("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),m("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),m("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),m("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),m("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),m("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),m("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),m("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),m("\u2237","\\dblcolon"),m("\u2239","\\eqcolon"),m("\u2254","\\coloneqq"),m("\u2255","\\eqqcolon"),m("\u2A74","\\Coloneqq"),m("\\ratio","\\vcentcolon"),m("\\coloncolon","\\dblcolon"),m("\\colonequals","\\coloneqq"),m("\\coloncolonequals","\\Coloneqq"),m("\\equalscolon","\\eqqcolon"),m("\\equalscoloncolon","\\Eqqcolon"),m("\\colonminus","\\coloneq"),m("\\coloncolonminus","\\Coloneq"),m("\\minuscolon","\\eqcolon"),m("\\minuscoloncolon","\\Eqcolon"),m("\\coloncolonapprox","\\Colonapprox"),m("\\coloncolonsim","\\Colonsim"),m("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),m("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),m("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),m("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),m("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`\u220C}}"),m("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),m("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),m("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),m("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),m("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),m("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),m("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),m("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),m("\\gvertneqq","\\html@mathml{\\@gvertneqq}{\u2269}"),m("\\lvertneqq","\\html@mathml{\\@lvertneqq}{\u2268}"),m("\\ngeqq","\\html@mathml{\\@ngeqq}{\u2271}"),m("\\ngeqslant","\\html@mathml{\\@ngeqslant}{\u2271}"),m("\\nleqq","\\html@mathml{\\@nleqq}{\u2270}"),m("\\nleqslant","\\html@mathml{\\@nleqslant}{\u2270}"),m("\\nshortmid","\\html@mathml{\\@nshortmid}{\u2224}"),m("\\nshortparallel","\\html@mathml{\\@nshortparallel}{\u2226}"),m("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{\u2288}"),m("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{\u2289}"),m("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{\u228A}"),m("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{\u2ACB}"),m("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{\u228B}"),m("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{\u2ACC}"),m("\\imath","\\html@mathml{\\@imath}{\u0131}"),m("\\jmath","\\html@mathml{\\@jmath}{\u0237}"),m("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`\u27E6}}"),m("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`\u27E7}}"),m("\u27E6","\\llbracket"),m("\u27E7","\\rrbracket"),m("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`\u2983}}"),m("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`\u2984}}"),m("\u2983","\\lBrace"),m("\u2984","\\rBrace"),m("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`\u29B5}}"),m("\u29B5","\\minuso"),m("\\darr","\\downarrow"),m("\\dArr","\\Downarrow"),m("\\Darr","\\Downarrow"),m("\\lang","\\langle"),m("\\rang","\\rangle"),m("\\uarr","\\uparrow"),m("\\uArr","\\Uparrow"),m("\\Uarr","\\Uparrow"),m("\\N","\\mathbb{N}"),m("\\R","\\mathbb{R}"),m("\\Z","\\mathbb{Z}"),m("\\alef","\\aleph"),m("\\alefsym","\\aleph"),m("\\Alpha","\\mathrm{A}"),m("\\Beta","\\mathrm{B}"),m("\\bull","\\bullet"),m("\\Chi","\\mathrm{X}"),m("\\clubs","\\clubsuit"),m("\\cnums","\\mathbb{C}"),m("\\Complex","\\mathbb{C}"),m("\\Dagger","\\ddagger"),m("\\diamonds","\\diamondsuit"),m("\\empty","\\emptyset"),m("\\Epsilon","\\mathrm{E}"),m("\\Eta","\\mathrm{H}"),m("\\exist","\\exists"),m("\\harr","\\leftrightarrow"),m("\\hArr","\\Leftrightarrow"),m("\\Harr","\\Leftrightarrow"),m("\\hearts","\\heartsuit"),m("\\image","\\Im"),m("\\infin","\\infty"),m("\\Iota","\\mathrm{I}"),m("\\isin","\\in"),m("\\Kappa","\\mathrm{K}"),m("\\larr","\\leftarrow"),m("\\lArr","\\Leftarrow"),m("\\Larr","\\Leftarrow"),m("\\lrarr","\\leftrightarrow"),m("\\lrArr","\\Leftrightarrow"),m("\\Lrarr","\\Leftrightarrow"),m("\\Mu","\\mathrm{M}"),m("\\natnums","\\mathbb{N}"),m("\\Nu","\\mathrm{N}"),m("\\Omicron","\\mathrm{O}"),m("\\plusmn","\\pm"),m("\\rarr","\\rightarrow"),m("\\rArr","\\Rightarrow"),m("\\Rarr","\\Rightarrow"),m("\\real","\\Re"),m("\\reals","\\mathbb{R}"),m("\\Reals","\\mathbb{R}"),m("\\Rho","\\mathrm{P}"),m("\\sdot","\\cdot"),m("\\sect","\\S"),m("\\spades","\\spadesuit"),m("\\sub","\\subset"),m("\\sube","\\subseteq"),m("\\supe","\\supseteq"),m("\\Tau","\\mathrm{T}"),m("\\thetasym","\\vartheta"),m("\\weierp","\\wp"),m("\\Zeta","\\mathrm{Z}"),m("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),m("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),m("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),m("\\bra","\\mathinner{\\langle{#1}|}"),m("\\ket","\\mathinner{|{#1}\\rangle}"),m("\\braket","\\mathinner{\\langle{#1}\\rangle}"),m("\\Bra","\\left\\langle#1\\right|"),m("\\Ket","\\left|#1\\right\\rangle");var Qr=r=>e=>{var t=e.consumeArg().tokens,a=e.consumeArg().tokens,i=e.consumeArg().tokens,s=e.consumeArg().tokens,o=e.macros.get("|"),h=e.macros.get("\\|");e.macros.beginGroup();var c=b=>x=>{r&&(x.macros.set("|",o),i.length&&x.macros.set("\\|",h));var w=b;if(!b&&i.length){var A=x.future();A.text==="|"&&(x.popToken(),w=!0)}return{tokens:w?i:a,numArgs:0}};e.macros.set("|",c(!1)),i.length&&e.macros.set("\\|",c(!0));var p=e.consumeArg().tokens,g=e.expandTokens([...s,...p,...t]);return e.macros.endGroup(),{tokens:g.reverse(),numArgs:0}};m("\\bra@ket",Qr(!1)),m("\\bra@set",Qr(!0)),m("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),m("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),m("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),m("\\angln","{\\angl n}"),m("\\blue","\\textcolor{##6495ed}{#1}"),m("\\orange","\\textcolor{##ffa500}{#1}"),m("\\pink","\\textcolor{##ff00af}{#1}"),m("\\red","\\textcolor{##df0030}{#1}"),m("\\green","\\textcolor{##28ae7b}{#1}"),m("\\gray","\\textcolor{gray}{#1}"),m("\\purple","\\textcolor{##9d38bd}{#1}"),m("\\blueA","\\textcolor{##ccfaff}{#1}"),m("\\blueB","\\textcolor{##80f6ff}{#1}"),m("\\blueC","\\textcolor{##63d9ea}{#1}"),m("\\blueD","\\textcolor{##11accd}{#1}"),m("\\blueE","\\textcolor{##0c7f99}{#1}"),m("\\tealA","\\textcolor{##94fff5}{#1}"),m("\\tealB","\\textcolor{##26edd5}{#1}"),m("\\tealC","\\textcolor{##01d1c1}{#1}"),m("\\tealD","\\textcolor{##01a995}{#1}"),m("\\tealE","\\textcolor{##208170}{#1}"),m("\\greenA","\\textcolor{##b6ffb0}{#1}"),m("\\greenB","\\textcolor{##8af281}{#1}"),m("\\greenC","\\textcolor{##74cf70}{#1}"),m("\\greenD","\\textcolor{##1fab54}{#1}"),m("\\greenE","\\textcolor{##0d923f}{#1}"),m("\\goldA","\\textcolor{##ffd0a9}{#1}"),m("\\goldB","\\textcolor{##ffbb71}{#1}"),m("\\goldC","\\textcolor{##ff9c39}{#1}"),m("\\goldD","\\textcolor{##e07d10}{#1}"),m("\\goldE","\\textcolor{##a75a05}{#1}"),m("\\redA","\\textcolor{##fca9a9}{#1}"),m("\\redB","\\textcolor{##ff8482}{#1}"),m("\\redC","\\textcolor{##f9685d}{#1}"),m("\\redD","\\textcolor{##e84d39}{#1}"),m("\\redE","\\textcolor{##bc2612}{#1}"),m("\\maroonA","\\textcolor{##ffbde0}{#1}"),m("\\maroonB","\\textcolor{##ff92c6}{#1}"),m("\\maroonC","\\textcolor{##ed5fa6}{#1}"),m("\\maroonD","\\textcolor{##ca337c}{#1}"),m("\\maroonE","\\textcolor{##9e034e}{#1}"),m("\\purpleA","\\textcolor{##ddd7ff}{#1}"),m("\\purpleB","\\textcolor{##c6b9fc}{#1}"),m("\\purpleC","\\textcolor{##aa87ff}{#1}"),m("\\purpleD","\\textcolor{##7854ab}{#1}"),m("\\purpleE","\\textcolor{##543b78}{#1}"),m("\\mintA","\\textcolor{##f5f9e8}{#1}"),m("\\mintB","\\textcolor{##edf2df}{#1}"),m("\\mintC","\\textcolor{##e0e5cc}{#1}"),m("\\grayA","\\textcolor{##f6f7f7}{#1}"),m("\\grayB","\\textcolor{##f0f1f2}{#1}"),m("\\grayC","\\textcolor{##e3e5e6}{#1}"),m("\\grayD","\\textcolor{##d6d8da}{#1}"),m("\\grayE","\\textcolor{##babec2}{#1}"),m("\\grayF","\\textcolor{##888d93}{#1}"),m("\\grayG","\\textcolor{##626569}{#1}"),m("\\grayH","\\textcolor{##3b3e40}{#1}"),m("\\grayI","\\textcolor{##21242c}{#1}"),m("\\kaBlue","\\textcolor{##314453}{#1}"),m("\\kaGreen","\\textcolor{##71B307}{#1}");var _r={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class r4{constructor(e,t,a){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new e4(t4,t.macros),this.mode=a,this.stack=[]}feed(e){this.lexer=new jr(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var t,a,i;if(e){if(this.consumeSpaces(),this.future().text!=="[")return null;t=this.popToken(),{tokens:i,end:a}=this.consumeArg(["]"])}else({tokens:i,start:t,end:a}=this.consumeArg());return this.pushToken(new h0("EOF",a.loc)),this.pushTokens(i),t.range(a,"")}consumeSpaces(){for(;;){var e=this.future();if(e.text===" ")this.stack.pop();else break}}consumeArg(e){var t=[],a=e&&e.length>0;a||this.consumeSpaces();var i=this.future(),s,o=0,h=0;do{if(s=this.popToken(),t.push(s),s.text==="{")++o;else if(s.text==="}"){if(--o,o===-1)throw new M("Extra }",s)}else if(s.text==="EOF")throw new M("Unexpected end of input in a macro argument, expected '"+(e&&a?e[h]:"}")+"'",s);if(e&&a)if((o===0||o===1&&e[h]==="{")&&s.text===e[h]){if(++h,h===e.length){t.splice(-h,h);break}}else h=0}while(o!==0||a);return i.text==="{"&&t[t.length-1].text==="}"&&(t.pop(),t.shift()),t.reverse(),{tokens:t,start:i,end:s}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new M("The length of delimiters doesn't match the number of args!");for(var a=t[0],i=0;i<a.length;i++){var s=this.popToken();if(a[i]!==s.text)throw new M("Use of the macro doesn't match its definition",s)}}for(var o=[],h=0;h<e;h++)o.push(this.consumeArg(t&&t[h+1]).tokens);return o}expandOnce(e){var t=this.popToken(),a=t.text,i=t.noexpand?null:this._getExpansion(a);if(i==null||e&&i.unexpandable){if(e&&i==null&&a[0]==="\\"&&!this.isDefined(a))throw new M("Undefined control sequence: "+a);return this.pushToken(t),t}if(this.expansionCount++,this.expansionCount>this.settings.maxExpand)throw new M("Too many expansions: infinite loop or need to increase maxExpand setting");var s=i.tokens,o=this.consumeArgs(i.numArgs,i.delimiters);if(i.numArgs){s=s.slice();for(var h=s.length-1;h>=0;--h){var c=s[h];if(c.text==="#"){if(h===0)throw new M("Incomplete placeholder at end of macro body",c);if(c=s[--h],c.text==="#")s.splice(h+1,1);else if(/^[1-9]$/.test(c.text))s.splice(h,2,...o[+c.text-1]);else throw new M("Not a valid argument number",c)}}}return this.pushTokens(s),s}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;){var e=this.expandOnce();if(e instanceof h0)return e.treatAsRelax&&(e.text="\\relax"),this.stack.pop()}throw new Error}expandMacro(e){return this.macros.has(e)?this.expandTokens([new h0(e)]):void 0}expandTokens(e){var t=[],a=this.stack.length;for(this.pushTokens(e);this.stack.length>a;){var i=this.expandOnce(!0);i instanceof h0&&(i.treatAsRelax&&(i.noexpand=!1,i.treatAsRelax=!1),t.push(this.stack.pop()))}return t}expandMacroAsText(e){var t=this.expandMacro(e);return t&&t.map(a=>a.text).join("")}_getExpansion(e){var t=this.macros.get(e);if(t==null)return t;if(e.length===1){var a=this.lexer.catcodes[e];if(a!=null&&a!==13)return}var i=typeof t=="function"?t(this):t;if(typeof i=="string"){var s=0;if(i.indexOf("#")!==-1)for(var o=i.replace(/##/g,"");o.indexOf("#"+(s+1))!==-1;)++s;for(var h=new jr(i,this.settings),c=[],p=h.lex();p.text!=="EOF";)c.push(p),p=h.lex();c.reverse();var g={tokens:c,numArgs:s};return g}return i}isDefined(e){return this.macros.has(e)||L0.hasOwnProperty(e)||X.math.hasOwnProperty(e)||X.text.hasOwnProperty(e)||_r.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return t!=null?typeof t=="string"||typeof t=="function"||!t.unexpandable:L0.hasOwnProperty(e)&&!L0[e].primitive}}var ea=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,De=Object.freeze({"\u208A":"+","\u208B":"-","\u208C":"=","\u208D":"(","\u208E":")","\u2080":"0","\u2081":"1","\u2082":"2","\u2083":"3","\u2084":"4","\u2085":"5","\u2086":"6","\u2087":"7","\u2088":"8","\u2089":"9",\u2090:"a",\u2091:"e",\u2095:"h",\u1D62:"i",\u2C7C:"j",\u2096:"k",\u2097:"l",\u2098:"m",\u2099:"n",\u2092:"o",\u209A:"p",\u1D63:"r",\u209B:"s",\u209C:"t",\u1D64:"u",\u1D65:"v",\u2093:"x",\u1D66:"\u03B2",\u1D67:"\u03B3",\u1D68:"\u03C1",\u1D69:"\u03D5",\u1D6A:"\u03C7","\u207A":"+","\u207B":"-","\u207C":"=","\u207D":"(","\u207E":")","\u2070":"0","\xB9":"1","\xB2":"2","\xB3":"3","\u2074":"4","\u2075":"5","\u2076":"6","\u2077":"7","\u2078":"8","\u2079":"9",\u1D2C:"A",\u1D2E:"B",\u1D30:"D",\u1D31:"E",\u1D33:"G",\u1D34:"H",\u1D35:"I",\u1D36:"J",\u1D37:"K",\u1D38:"L",\u1D39:"M",\u1D3A:"N",\u1D3C:"O",\u1D3E:"P",\u1D3F:"R",\u1D40:"T",\u1D41:"U",\u2C7D:"V",\u1D42:"W",\u1D43:"a",\u1D47:"b",\u1D9C:"c",\u1D48:"d",\u1D49:"e",\u1DA0:"f",\u1D4D:"g",\u02B0:"h",\u2071:"i",\u02B2:"j",\u1D4F:"k",\u02E1:"l",\u1D50:"m",\u207F:"n",\u1D52:"o",\u1D56:"p",\u02B3:"r",\u02E2:"s",\u1D57:"t",\u1D58:"u",\u1D5B:"v",\u02B7:"w",\u02E3:"x",\u02B8:"y",\u1DBB:"z",\u1D5D:"\u03B2",\u1D5E:"\u03B3",\u1D5F:"\u03B4",\u1D60:"\u03D5",\u1D61:"\u03C7",\u1DBF:"\u03B8"}),wt={"\u0301":{text:"\\'",math:"\\acute"},"\u0300":{text:"\\`",math:"\\grave"},"\u0308":{text:'\\"',math:"\\ddot"},"\u0303":{text:"\\~",math:"\\tilde"},"\u0304":{text:"\\=",math:"\\bar"},"\u0306":{text:"\\u",math:"\\breve"},"\u030C":{text:"\\v",math:"\\check"},"\u0302":{text:"\\^",math:"\\hat"},"\u0307":{text:"\\.",math:"\\dot"},"\u030A":{text:"\\r",math:"\\mathring"},"\u030B":{text:"\\H"},"\u0327":{text:"\\c"}},ta={\u00E1:"a\u0301",\u00E0:"a\u0300",\u00E4:"a\u0308",\u01DF:"a\u0308\u0304",\u00E3:"a\u0303",\u0101:"a\u0304",\u0103:"a\u0306",\u1EAF:"a\u0306\u0301",\u1EB1:"a\u0306\u0300",\u1EB5:"a\u0306\u0303",\u01CE:"a\u030C",\u00E2:"a\u0302",\u1EA5:"a\u0302\u0301",\u1EA7:"a\u0302\u0300",\u1EAB:"a\u0302\u0303",\u0227:"a\u0307",\u01E1:"a\u0307\u0304",\u00E5:"a\u030A",\u01FB:"a\u030A\u0301",\u1E03:"b\u0307",\u0107:"c\u0301",\u1E09:"c\u0327\u0301",\u010D:"c\u030C",\u0109:"c\u0302",\u010B:"c\u0307",\u00E7:"c\u0327",\u010F:"d\u030C",\u1E0B:"d\u0307",\u1E11:"d\u0327",\u00E9:"e\u0301",\u00E8:"e\u0300",\u00EB:"e\u0308",\u1EBD:"e\u0303",\u0113:"e\u0304",\u1E17:"e\u0304\u0301",\u1E15:"e\u0304\u0300",\u0115:"e\u0306",\u1E1D:"e\u0327\u0306",\u011B:"e\u030C",\u00EA:"e\u0302",\u1EBF:"e\u0302\u0301",\u1EC1:"e\u0302\u0300",\u1EC5:"e\u0302\u0303",\u0117:"e\u0307",\u0229:"e\u0327",\u1E1F:"f\u0307",\u01F5:"g\u0301",\u1E21:"g\u0304",\u011F:"g\u0306",\u01E7:"g\u030C",\u011D:"g\u0302",\u0121:"g\u0307",\u0123:"g\u0327",\u1E27:"h\u0308",\u021F:"h\u030C",\u0125:"h\u0302",\u1E23:"h\u0307",\u1E29:"h\u0327",\u00ED:"i\u0301",\u00EC:"i\u0300",\u00EF:"i\u0308",\u1E2F:"i\u0308\u0301",\u0129:"i\u0303",\u012B:"i\u0304",\u012D:"i\u0306",\u01D0:"i\u030C",\u00EE:"i\u0302",\u01F0:"j\u030C",\u0135:"j\u0302",\u1E31:"k\u0301",\u01E9:"k\u030C",\u0137:"k\u0327",\u013A:"l\u0301",\u013E:"l\u030C",\u013C:"l\u0327",\u1E3F:"m\u0301",\u1E41:"m\u0307",\u0144:"n\u0301",\u01F9:"n\u0300",\u00F1:"n\u0303",\u0148:"n\u030C",\u1E45:"n\u0307",\u0146:"n\u0327",\u00F3:"o\u0301",\u00F2:"o\u0300",\u00F6:"o\u0308",\u022B:"o\u0308\u0304",\u00F5:"o\u0303",\u1E4D:"o\u0303\u0301",\u1E4F:"o\u0303\u0308",\u022D:"o\u0303\u0304",\u014D:"o\u0304",\u1E53:"o\u0304\u0301",\u1E51:"o\u0304\u0300",\u014F:"o\u0306",\u01D2:"o\u030C",\u00F4:"o\u0302",\u1ED1:"o\u0302\u0301",\u1ED3:"o\u0302\u0300",\u1ED7:"o\u0302\u0303",\u022F:"o\u0307",\u0231:"o\u0307\u0304",\u0151:"o\u030B",\u1E55:"p\u0301",\u1E57:"p\u0307",\u0155:"r\u0301",\u0159:"r\u030C",\u1E59:"r\u0307",\u0157:"r\u0327",\u015B:"s\u0301",\u1E65:"s\u0301\u0307",\u0161:"s\u030C",\u1E67:"s\u030C\u0307",\u015D:"s\u0302",\u1E61:"s\u0307",\u015F:"s\u0327",\u1E97:"t\u0308",\u0165:"t\u030C",\u1E6B:"t\u0307",\u0163:"t\u0327",\u00FA:"u\u0301",\u00F9:"u\u0300",\u00FC:"u\u0308",\u01D8:"u\u0308\u0301",\u01DC:"u\u0308\u0300",\u01D6:"u\u0308\u0304",\u01DA:"u\u0308\u030C",\u0169:"u\u0303",\u1E79:"u\u0303\u0301",\u016B:"u\u0304",\u1E7B:"u\u0304\u0308",\u016D:"u\u0306",\u01D4:"u\u030C",\u00FB:"u\u0302",\u016F:"u\u030A",\u0171:"u\u030B",\u1E7D:"v\u0303",\u1E83:"w\u0301",\u1E81:"w\u0300",\u1E85:"w\u0308",\u0175:"w\u0302",\u1E87:"w\u0307",\u1E98:"w\u030A",\u1E8D:"x\u0308",\u1E8B:"x\u0307",\u00FD:"y\u0301",\u1EF3:"y\u0300",\u00FF:"y\u0308",\u1EF9:"y\u0303",\u0233:"y\u0304",\u0177:"y\u0302",\u1E8F:"y\u0307",\u1E99:"y\u030A",\u017A:"z\u0301",\u017E:"z\u030C",\u1E91:"z\u0302",\u017C:"z\u0307",\u00C1:"A\u0301",\u00C0:"A\u0300",\u00C4:"A\u0308",\u01DE:"A\u0308\u0304",\u00C3:"A\u0303",\u0100:"A\u0304",\u0102:"A\u0306",\u1EAE:"A\u0306\u0301",\u1EB0:"A\u0306\u0300",\u1EB4:"A\u0306\u0303",\u01CD:"A\u030C",\u00C2:"A\u0302",\u1EA4:"A\u0302\u0301",\u1EA6:"A\u0302\u0300",\u1EAA:"A\u0302\u0303",\u0226:"A\u0307",\u01E0:"A\u0307\u0304",\u00C5:"A\u030A",\u01FA:"A\u030A\u0301",\u1E02:"B\u0307",\u0106:"C\u0301",\u1E08:"C\u0327\u0301",\u010C:"C\u030C",\u0108:"C\u0302",\u010A:"C\u0307",\u00C7:"C\u0327",\u010E:"D\u030C",\u1E0A:"D\u0307",\u1E10:"D\u0327",\u00C9:"E\u0301",\u00C8:"E\u0300",\u00CB:"E\u0308",\u1EBC:"E\u0303",\u0112:"E\u0304",\u1E16:"E\u0304\u0301",\u1E14:"E\u0304\u0300",\u0114:"E\u0306",\u1E1C:"E\u0327\u0306",\u011A:"E\u030C",\u00CA:"E\u0302",\u1EBE:"E\u0302\u0301",\u1EC0:"E\u0302\u0300",\u1EC4:"E\u0302\u0303",\u0116:"E\u0307",\u0228:"E\u0327",\u1E1E:"F\u0307",\u01F4:"G\u0301",\u1E20:"G\u0304",\u011E:"G\u0306",\u01E6:"G\u030C",\u011C:"G\u0302",\u0120:"G\u0307",\u0122:"G\u0327",\u1E26:"H\u0308",\u021E:"H\u030C",\u0124:"H\u0302",\u1E22:"H\u0307",\u1E28:"H\u0327",\u00CD:"I\u0301",\u00CC:"I\u0300",\u00CF:"I\u0308",\u1E2E:"I\u0308\u0301",\u0128:"I\u0303",\u012A:"I\u0304",\u012C:"I\u0306",\u01CF:"I\u030C",\u00CE:"I\u0302",\u0130:"I\u0307",\u0134:"J\u0302",\u1E30:"K\u0301",\u01E8:"K\u030C",\u0136:"K\u0327",\u0139:"L\u0301",\u013D:"L\u030C",\u013B:"L\u0327",\u1E3E:"M\u0301",\u1E40:"M\u0307",\u0143:"N\u0301",\u01F8:"N\u0300",\u00D1:"N\u0303",\u0147:"N\u030C",\u1E44:"N\u0307",\u0145:"N\u0327",\u00D3:"O\u0301",\u00D2:"O\u0300",\u00D6:"O\u0308",\u022A:"O\u0308\u0304",\u00D5:"O\u0303",\u1E4C:"O\u0303\u0301",\u1E4E:"O\u0303\u0308",\u022C:"O\u0303\u0304",\u014C:"O\u0304",\u1E52:"O\u0304\u0301",\u1E50:"O\u0304\u0300",\u014E:"O\u0306",\u01D1:"O\u030C",\u00D4:"O\u0302",\u1ED0:"O\u0302\u0301",\u1ED2:"O\u0302\u0300",\u1ED6:"O\u0302\u0303",\u022E:"O\u0307",\u0230:"O\u0307\u0304",\u0150:"O\u030B",\u1E54:"P\u0301",\u1E56:"P\u0307",\u0154:"R\u0301",\u0158:"R\u030C",\u1E58:"R\u0307",\u0156:"R\u0327",\u015A:"S\u0301",\u1E64:"S\u0301\u0307",\u0160:"S\u030C",\u1E66:"S\u030C\u0307",\u015C:"S\u0302",\u1E60:"S\u0307",\u015E:"S\u0327",\u0164:"T\u030C",\u1E6A:"T\u0307",\u0162:"T\u0327",\u00DA:"U\u0301",\u00D9:"U\u0300",\u00DC:"U\u0308",\u01D7:"U\u0308\u0301",\u01DB:"U\u0308\u0300",\u01D5:"U\u0308\u0304",\u01D9:"U\u0308\u030C",\u0168:"U\u0303",\u1E78:"U\u0303\u0301",\u016A:"U\u0304",\u1E7A:"U\u0304\u0308",\u016C:"U\u0306",\u01D3:"U\u030C",\u00DB:"U\u0302",\u016E:"U\u030A",\u0170:"U\u030B",\u1E7C:"V\u0303",\u1E82:"W\u0301",\u1E80:"W\u0300",\u1E84:"W\u0308",\u0174:"W\u0302",\u1E86:"W\u0307",\u1E8C:"X\u0308",\u1E8A:"X\u0307",\u00DD:"Y\u0301",\u1EF2:"Y\u0300",\u0178:"Y\u0308",\u1EF8:"Y\u0303",\u0232:"Y\u0304",\u0176:"Y\u0302",\u1E8E:"Y\u0307",\u0179:"Z\u0301",\u017D:"Z\u030C",\u1E90:"Z\u0302",\u017B:"Z\u0307",\u03AC:"\u03B1\u0301",\u1F70:"\u03B1\u0300",\u1FB1:"\u03B1\u0304",\u1FB0:"\u03B1\u0306",\u03AD:"\u03B5\u0301",\u1F72:"\u03B5\u0300",\u03AE:"\u03B7\u0301",\u1F74:"\u03B7\u0300",\u03AF:"\u03B9\u0301",\u1F76:"\u03B9\u0300",\u03CA:"\u03B9\u0308",\u0390:"\u03B9\u0308\u0301",\u1FD2:"\u03B9\u0308\u0300",\u1FD1:"\u03B9\u0304",\u1FD0:"\u03B9\u0306",\u03CC:"\u03BF\u0301",\u1F78:"\u03BF\u0300",\u03CD:"\u03C5\u0301",\u1F7A:"\u03C5\u0300",\u03CB:"\u03C5\u0308",\u03B0:"\u03C5\u0308\u0301",\u1FE2:"\u03C5\u0308\u0300",\u1FE1:"\u03C5\u0304",\u1FE0:"\u03C5\u0306",\u03CE:"\u03C9\u0301",\u1F7C:"\u03C9\u0300",\u038E:"\u03A5\u0301",\u1FEA:"\u03A5\u0300",\u03AB:"\u03A5\u0308",\u1FE9:"\u03A5\u0304",\u1FE8:"\u03A5\u0306",\u038F:"\u03A9\u0301",\u1FFA:"\u03A9\u0300"};class oe{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new r4(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(t===void 0&&(t=!0),this.fetch().text!==e)throw new M("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var t=this.nextToken;this.consume(),this.gullet.pushToken(new h0("}")),this.gullet.pushTokens(e);var a=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,a}parseExpression(e,t){for(var a=[];;){this.mode==="math"&&this.consumeSpaces();var i=this.fetch();if(oe.endOfExpression.indexOf(i.text)!==-1||t&&i.text===t||e&&L0[i.text]&&L0[i.text].infix)break;var s=this.parseAtom(t);if(s){if(s.type==="internal")continue}else break;a.push(s)}return this.mode==="text"&&this.formLigatures(a),this.handleInfixNodes(a)}handleInfixNodes(e){for(var t=-1,a,i=0;i<e.length;i++)if(e[i].type==="infix"){if(t!==-1)throw new M("only one infix operator per group",e[i].token);t=i,a=e[i].replaceWith}if(t!==-1&&a){var s,o,h=e.slice(0,t),c=e.slice(t+1);h.length===1&&h[0].type==="ordgroup"?s=h[0]:s={type:"ordgroup",mode:this.mode,body:h},c.length===1&&c[0].type==="ordgroup"?o=c[0]:o={type:"ordgroup",mode:this.mode,body:c};var p;return a==="\\\\abovefrac"?p=this.callFunction(a,[s,e[t],o],[]):p=this.callFunction(a,[s,o],[]),[p]}else return e}handleSupSubscript(e){var t=this.fetch(),a=t.text;this.consume(),this.consumeSpaces();var i=this.parseGroup(e);if(!i)throw new M("Expected group after '"+a+"'",t);return i}formatUnsupportedCmd(e){for(var t=[],a=0;a<e.length;a++)t.push({type:"textord",mode:"text",text:e[a]});var i={type:"text",mode:this.mode,body:t},s={type:"color",mode:this.mode,color:this.settings.errorColor,body:[i]};return s}parseAtom(e){var t=this.parseGroup("atom",e);if(this.mode==="text")return t;for(var a,i;;){this.consumeSpaces();var s=this.fetch();if(s.text==="\\limits"||s.text==="\\nolimits"){if(t&&t.type==="op"){var o=s.text==="\\limits";t.limits=o,t.alwaysHandleSupSub=!0}else if(t&&t.type==="operatorname")t.alwaysHandleSupSub&&(t.limits=s.text==="\\limits");else throw new M("Limit controls must follow a math operator",s);this.consume()}else if(s.text==="^"){if(a)throw new M("Double superscript",s);a=this.handleSupSubscript("superscript")}else if(s.text==="_"){if(i)throw new M("Double subscript",s);i=this.handleSupSubscript("subscript")}else if(s.text==="'"){if(a)throw new M("Double superscript",s);var h={type:"textord",mode:this.mode,text:"\\prime"},c=[h];for(this.consume();this.fetch().text==="'";)c.push(h),this.consume();this.fetch().text==="^"&&c.push(this.handleSupSubscript("superscript")),a={type:"ordgroup",mode:this.mode,body:c}}else if(De[s.text]){var p=De[s.text],g=ea.test(s.text);for(this.consume();;){var b=this.fetch().text;if(!De[b]||ea.test(b)!==g)break;this.consume(),p+=De[b]}var x=new oe(p,this.settings).parse();g?i={type:"ordgroup",mode:"math",body:x}:a={type:"ordgroup",mode:"math",body:x}}else break}return a||i?{type:"supsub",mode:this.mode,base:t,sup:a,sub:i}:t}parseFunction(e,t){var a=this.fetch(),i=a.text,s=L0[i];if(!s)return null;if(this.consume(),t&&t!=="atom"&&!s.allowedInArgument)throw new M("Got function '"+i+"' with no arguments"+(t?" as "+t:""),a);if(this.mode==="text"&&!s.allowedInText)throw new M("Can't use function '"+i+"' in text mode",a);if(this.mode==="math"&&s.allowedInMath===!1)throw new M("Can't use function '"+i+"' in math mode",a);var{args:o,optArgs:h}=this.parseArguments(i,s);return this.callFunction(i,o,h,a,e)}callFunction(e,t,a,i,s){var o={funcName:e,parser:this,token:i,breakOnTokenText:s},h=L0[e];if(h&&h.handler)return h.handler(o,t,a);throw new M("No function handler for "+e)}parseArguments(e,t){var a=t.numArgs+t.numOptionalArgs;if(a===0)return{args:[],optArgs:[]};for(var i=[],s=[],o=0;o<a;o++){var h=t.argTypes&&t.argTypes[o],c=o<t.numOptionalArgs;(t.primitive&&h==null||t.type==="sqrt"&&o===1&&s[0]==null)&&(h="primitive");var p=this.parseGroupOfType("argument to '"+e+"'",h,c);if(c)s.push(p);else if(p!=null)i.push(p);else throw new M("Null argument, please report this as a bug")}return{args:i,optArgs:s}}parseGroupOfType(e,t,a){switch(t){case"color":return this.parseColorGroup(a);case"size":return this.parseSizeGroup(a);case"url":return this.parseUrlGroup(a);case"math":case"text":return this.parseArgumentGroup(a,t);case"hbox":{var i=this.parseArgumentGroup(a,"text");return i!=null?{type:"styling",mode:i.mode,body:[i],style:"text"}:null}case"raw":{var s=this.parseStringGroup("raw",a);return s!=null?{type:"raw",mode:"text",string:s.text}:null}case"primitive":{if(a)throw new M("A primitive argument cannot be optional");var o=this.parseGroup(e);if(o==null)throw new M("Expected group as "+e,this.fetch());return o}case"original":case null:case void 0:return this.parseArgumentGroup(a);default:throw new M("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(e,t){var a=this.gullet.scanArgument(t);if(a==null)return null;for(var i="",s;(s=this.fetch()).text!=="EOF";)i+=s.text,this.consume();return this.consume(),a.text=i,a}parseRegexGroup(e,t){for(var a=this.fetch(),i=a,s="",o;(o=this.fetch()).text!=="EOF"&&e.test(s+o.text);)i=o,s+=i.text,this.consume();if(s==="")throw new M("Invalid "+t+": '"+a.text+"'",a);return a.range(i,s)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(t==null)return null;var a=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!a)throw new M("Invalid color: '"+t.text+"'",t);var i=a[0];return/^[0-9a-f]{6}$/i.test(i)&&(i="#"+i),{type:"color-token",mode:this.mode,color:i}}parseSizeGroup(e){var t,a=!1;if(this.gullet.consumeSpaces(),!e&&this.gullet.future().text!=="{"?t=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):t=this.parseStringGroup("size",e),!t)return null;!e&&t.text.length===0&&(t.text="0pt",a=!0);var i=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!i)throw new M("Invalid size: '"+t.text+"'",t);var s={number:+(i[1]+i[2]),unit:i[3]};if(!It(s))throw new M("Invalid unit: '"+s.unit+"'",t);return{type:"size",mode:this.mode,value:s,isBlank:a}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),t==null)return null;var a=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:a}}parseArgumentGroup(e,t){var a=this.gullet.scanArgument(e);if(a==null)return null;var i=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var s=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var o={type:"ordgroup",mode:this.mode,loc:a.loc,body:s};return t&&this.switchMode(i),o}parseGroup(e,t){var a=this.fetch(),i=a.text,s;if(i==="{"||i==="\\begingroup"){this.consume();var o=i==="{"?"}":"\\endgroup";this.gullet.beginGroup();var h=this.parseExpression(!1,o),c=this.fetch();this.expect(o),this.gullet.endGroup(),s={type:"ordgroup",mode:this.mode,loc:l0.range(a,c),body:h,semisimple:i==="\\begingroup"||void 0}}else if(s=this.parseFunction(t,e)||this.parseSymbol(),s==null&&i[0]==="\\"&&!_r.hasOwnProperty(i)){if(this.settings.throwOnError)throw new M("Undefined control sequence: "+i,a);s=this.formatUnsupportedCmd(i),this.consume()}return s}formLigatures(e){for(var t=e.length-1,a=0;a<t;++a){var i=e[a],s=i.text;s==="-"&&e[a+1].text==="-"&&(a+1<t&&e[a+2].text==="-"?(e.splice(a,3,{type:"textord",mode:"text",loc:l0.range(i,e[a+2]),text:"---"}),t-=2):(e.splice(a,2,{type:"textord",mode:"text",loc:l0.range(i,e[a+1]),text:"--"}),t-=1)),(s==="'"||s==="`")&&e[a+1].text===s&&(e.splice(a,2,{type:"textord",mode:"text",loc:l0.range(i,e[a+1]),text:s+s}),t-=1)}}parseSymbol(){var e=this.fetch(),t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var a=t.slice(5),i=a.charAt(0)==="*";if(i&&(a=a.slice(1)),a.length<2||a.charAt(0)!==a.slice(-1))throw new M(`\\verb assertion failed --
                    please report what input caused this bug`);return a=a.slice(1,-1),{type:"verb",mode:"text",body:a,star:i}}ta.hasOwnProperty(t[0])&&!X[this.mode][t[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=ta[t[0]]+t.substr(1));var s=Q1.exec(t);s&&(t=t.substring(0,s.index),t==="i"?t="\u0131":t==="j"&&(t="\u0237"));var o;if(X[this.mode][t]){this.settings.strict&&this.mode==="math"&&Xe.indexOf(t)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var h=X[this.mode][t].group,c=l0.range(e),p;if(Ya.hasOwnProperty(h)){var g=h;p={type:"atom",mode:this.mode,family:g,loc:c,text:t}}else p={type:h,mode:this.mode,loc:c,text:t};o=p}else if(t.charCodeAt(0)>=128)this.settings.strict&&(Ct(t.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'"'+(" ("+t.charCodeAt(0)+")"),e)),o={type:"textord",mode:"text",loc:l0.range(e),text:t};else return null;if(this.consume(),s)for(var b=0;b<s[0].length;b++){var x=s[0][b];if(!wt[x])throw new M("Unknown accent ' "+x+"'",e);var w=wt[x][this.mode]||wt[x].text;if(!w)throw new M("Accent "+x+" unsupported in "+this.mode+" mode",e);o={type:"accent",mode:this.mode,loc:l0.range(e),label:w,isStretchy:!1,isShifty:!0,base:o}}return o}}oe.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var kt=function(e,t){if(!(typeof e=="string"||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var a=new oe(e,t);delete a.gullet.macros.current["\\df@tag"];var i=a.parse();if(delete a.gullet.macros.current["\\current@color"],delete a.gullet.macros.current["\\color"],a.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new M("\\tag works only in display equations");i=[{type:"tag",mode:"text",body:i,tag:a.subparse([new h0("\\df@tag")])}]}return i},ra=function(e,t,a){t.textContent="";var i=St(e,a).toNode();t.appendChild(i)};typeof document!="undefined"&&document.compatMode!=="CSS1Compat"&&(typeof console!="undefined"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),ra=function(){throw new M("KaTeX doesn't work in quirks mode.")});var a4=function(e,t){var a=St(e,t).toMarkup();return a},i4=function(e,t){var a=new Re(t);return kt(e,a)},aa=function(e,t,a){if(a.throwOnError||!(e instanceof M))throw e;var i=y.makeSpan(["katex-error"],[new c0(t)]);return i.setAttribute("title",e.toString()),i.setAttribute("style","color:"+a.errorColor),i},St=function(e,t){var a=new Re(t);try{var i=kt(e,a);return d1(i,e,a)}catch(s){return aa(s,e,a)}},n4=function(e,t){var a=new Re(t);try{var i=kt(e,a);return f1(i,e,a)}catch(s){return aa(s,e,a)}},s4={version:"0.15.6",render:ra,renderToString:a4,ParseError:M,SETTINGS_SCHEMA:ue,__parse:i4,__renderToDomTree:St,__renderToHTMLTree:n4,__setFontMetrics:Ha,__defineSymbol:n,__defineMacro:m,__domTree:{Span:re,Anchor:Pe,SymbolNode:c0,SvgNode:R0,PathNode:P0,LineNode:Ge}}}}]);
