(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[250],{13277:function(){},75952:function(z,T,e){"use strict";e.r(T),e.d(T,{default:function(){return K}});var y=e(9761),g=e(67294),I=e(5977),A=e(11849),G=e(34792),R=e(48086),p=e(39428),v=e(3182),B=e(69610),D=e(54941),N=e(3980),C=e(54531),L=function(){function i(r){(0,B.Z)(this,i),this.id=void 0,this.resultId=void 0,this.query=void 0,this.queryErrorMsg=void 0,this.queryForm=void 0,this.queryResult=void 0,this.queryFormValue={},this.id=r.id,this.resultId=r.resultId,this.query=r.query,this.makeObservable()}return(0,D.Z)(i,[{key:"makeObservable",value:function(){(0,C.Ou)(this,{id:C.LO.ref,resultId:C.LO.ref,query:C.LO.ref,queryForm:C.LO.ref,queryResult:C.LO.ref,queryFormValue:C.LO.ref,queryErrorMsg:C.LO.ref,loadQuery:C.aD,getQueryResult:C.aD})}},{key:"loadQuery",value:function(){var r=(0,v.Z)((0,p.Z)().mark(function o(){var s;return(0,p.Z)().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,N.hi.loadQuery({id:this.id,resultId:this.resultId});case 2:s=h.sent,s.success?this.queryForm=s.data.schema:this.queryErrorMsg=s.message;case 4:case"end":return h.stop()}},o,this)}));function f(){return r.apply(this,arguments)}return f}()},{key:"getQueryResult",value:function(){var r=(0,v.Z)((0,p.Z)().mark(function o(s){var u;return(0,p.Z)().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,N.hi.getQueryResult({id:this.id,resultId:this.resultId,answer:s,query:this.query});case 2:u=n.sent,this.queryFormValue=s,u.code!==200?R.default.error(u.message):this.queryResult=u.data;case 5:case"end":return n.stop()}},o,this)}));function f(o){return r.apply(this,arguments)}return f}()},{key:"updateAnswer",value:function(){var r=(0,v.Z)((0,p.Z)().mark(function o(s,u){return(0,p.Z)().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,N.hi.post("/api/public/saveAnswer",{id:u,queryId:this.resultId,answer:s,projectId:this.id});case 2:return n.abrupt("return",n.sent);case 3:case"end":return n.stop()}},o,this)}));function f(o,s){return r.apply(this,arguments)}return f}()},{key:"upload",value:function(){var r=(0,v.Z)((0,p.Z)().mark(function o(s,u){return(0,p.Z)().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",N.hi.upload("/api/public/upload",(0,A.Z)((0,A.Z)({},s),{},{fileType:4,basePath:this.id}),u));case 1:case"end":return n.stop()}},o,this)}));function f(o,s){return r.apply(this,arguments)}return f}()}]),i}(),V=g.createContext(new L({id:"123456",resultId:"123456"}));function M(){return(0,g.useContext)(V)}var U=e(68068),W=e(8373),t=e(85893),k=(0,y.Pi)(function(){var i=M(),r=i.queryForm,f=i.queryResult,o=i.query;return(0,g.useEffect)(function(){i.loadQuery()},[]),(0,g.useEffect)(function(){var s;r&&((s=r.children)===null||s===void 0?void 0:s.filter(function(u){var h;return!((h=u.children)!==null&&h!==void 0&&h.find(function(n){return!!o[n.id]}))}).length)===0&&i.getQueryResult({})},[r,o]),f?(0,t.jsx)(t.Fragment,{}):r?(0,t.jsx)("div",{className:"public-query",children:(0,t.jsx)("div",{className:"query-form",children:!f&&(0,t.jsx)(U.Z,{theme:"antdForm",schema:r,questionNumber:!1,onSubmit:function(u){i.getQueryResult(u)}})})}):(0,t.jsx)(W.gb,{})}),H=e(57663),d=e(71577),l=e(49111),m=e(19650),a=e(71153),c=e(60331),O=e(83279),j=e(18106),S=e(72488),x=e(94184),b=e.n(x),Q=e(96389),P=S.Z.TabPane,$=(0,y.Pi)(function(){var i=M(),r=i.queryResult,f=i.queryFormValue,o=(0,g.useMemo)(function(){return(0,Q.SB)()},[]);return(0,g.useEffect)(function(){},[]),r?(0,t.jsxs)("div",{className:b()("query-result",o?"mobile":"pc"),children:[(0,t.jsxs)("div",{className:"query-result-criteria",children:[(0,t.jsx)("h3",{children:"\u67E5\u8BE2\u6761\u4EF6"}),(0,t.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",margin:"12px 0"},children:[(0,t.jsxs)("div",{children:["\u67E5\u8BE2\u6761\u4EF6\uFF1A",(0,t.jsx)(m.Z,{children:Object.values(f).reduce(function(s,u){return s.push.apply(s,(0,O.Z)(Object.values(u))),s},[]).map(function(s){return(0,t.jsx)(c.Z,{color:"blue",children:s},s)})})]}),(0,t.jsx)(d.Z,{type:"primary",onClick:function(){i.queryResult=void 0},children:"\u91CD\u65B0\u67E5\u8BE2"})]})]}),(0,t.jsx)("div",{className:"query-result-container",children:(0,t.jsxs)("div",{className:"query-result-data",children:[(0,t.jsxs)("div",{className:"query-result-data-total",children:["\u67E5\u8BE2\u5230 ",r.answers.length," \u6761\u7B26\u5408\u6761\u4EF6\u7684\u6570\u636E"]}),(0,t.jsx)("div",{className:"query-result-data-content",children:(0,t.jsx)(S.Z,{type:"card",children:r.answers.map(function(s){return(0,t.jsx)(P,{tab:s.createAt,children:(0,t.jsx)(U.Z,{theme:o?"antdMobile":"antd",schema:r==null?void 0:r.schema,initialValues:s.answer,headerVisible:!1,footerVisible:!!(r!=null&&r.schema.attribute),paginationVisible:!1,fieldPermission:r.fieldPermission,onUpload:function(h,n){return i.upload(h,n)},onSubmit:function(h){i.updateAnswer(h,s.answerId).then(function(n){n.success?R.default.success("\u7B54\u6848\u66F4\u65B0\u6210\u529F"):R.default.error(n.message)})}})},s.answerId)})})})]})})]}):(0,t.jsx)(t.Fragment,{})}),E=e(12997),F=(0,y.Pi)(function(){var i=M(),r=i.queryErrorMsg;return r?(0,t.jsx)("div",{className:"render-failure  phone",children:(0,t.jsxs)("div",{className:"content",children:[(0,t.jsx)("div",{children:(0,t.jsx)(E.AT,{})}),(0,t.jsx)("h2",{children:r})]})}):(0,t.jsx)(t.Fragment,{})}),K=(0,y.Pi)(function(){var r=(0,I.UO)(),f=r.id,o=r.resultId,s=(0,I.TH)(),u=(0,g.useMemo)(function(){return new L({id:f,resultId:o,query:s.query})},[f,o]);return(0,t.jsxs)(V.Provider,{value:u,children:[(0,t.jsx)(F,{}),(0,t.jsx)(k,{}),(0,t.jsx)($,{})]})})},60331:function(z,T,e){"use strict";e.d(T,{Z:function(){return H}});var y=e(96156),g=e(22122),I=e(28481),A=e(62208),G=e(94184),R=e.n(G),p=e(98423),v=e(67294),B=e(53124),D=e(98787),N=e(21790),C=function(d,l){var m={};for(var a in d)Object.prototype.hasOwnProperty.call(d,a)&&l.indexOf(a)<0&&(m[a]=d[a]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,a=Object.getOwnPropertySymbols(d);c<a.length;c++)l.indexOf(a[c])<0&&Object.prototype.propertyIsEnumerable.call(d,a[c])&&(m[a[c]]=d[a[c]]);return m},L=function(l){var m=l.prefixCls,a=l.className,c=l.checked,O=l.onChange,j=l.onClick,S=C(l,["prefixCls","className","checked","onChange","onClick"]),x=v.useContext(B.E_),b=x.getPrefixCls,Q=function(F){O==null||O(!c),j==null||j(F)},P=b("tag",m),$=R()(P,(0,y.Z)((0,y.Z)({},"".concat(P,"-checkable"),!0),"".concat(P,"-checkable-checked"),c),a);return v.createElement("span",(0,g.Z)({},S,{className:$,onClick:Q}))},V=L,M=function(d,l){var m={};for(var a in d)Object.prototype.hasOwnProperty.call(d,a)&&l.indexOf(a)<0&&(m[a]=d[a]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,a=Object.getOwnPropertySymbols(d);c<a.length;c++)l.indexOf(a[c])<0&&Object.prototype.propertyIsEnumerable.call(d,a[c])&&(m[a[c]]=d[a[c]]);return m},U=new RegExp("^(".concat(D.Y.join("|"),")(-inverse)?$")),W=new RegExp("^(".concat(D.E.join("|"),")$")),t=function(l,m){var a=l.prefixCls,c=l.className,O=l.style,j=l.children,S=l.icon,x=l.color,b=l.onClose,Q=l.closeIcon,P=l.closable,$=P===void 0?!1:P,E=M(l,["prefixCls","className","style","children","icon","color","onClose","closeIcon","closable"]),F=v.useContext(B.E_),K=F.getPrefixCls,i=F.direction,r=v.useState(!0),f=(0,I.Z)(r,2),o=f[0],s=f[1];v.useEffect(function(){"visible"in E&&s(E.visible)},[E.visible]);var u=function(){return x?U.test(x)||W.test(x):!1},h=(0,g.Z)({backgroundColor:x&&!u()?x:void 0},O),n=u(),Z=K("tag",a),_=R()(Z,(0,y.Z)((0,y.Z)((0,y.Z)((0,y.Z)({},"".concat(Z,"-").concat(x),n),"".concat(Z,"-has-color"),x&&!n),"".concat(Z,"-hidden"),!o),"".concat(Z,"-rtl"),i==="rtl"),c),Y=function(q){q.stopPropagation(),b==null||b(q),!q.defaultPrevented&&("visible"in E||s(!1))},ee=function(){return $?Q?v.createElement("span",{className:"".concat(Z,"-close-icon"),onClick:Y},Q):v.createElement(A.Z,{className:"".concat(Z,"-close-icon"),onClick:Y}):null},re="onClick"in E||j&&j.type==="a",te=(0,p.Z)(E,["visible"]),J=S||null,se=J?v.createElement(v.Fragment,null,J,v.createElement("span",null,j)):j,X=v.createElement("span",(0,g.Z)({},te,{ref:m,className:_,style:h}),se,ee());return re?v.createElement(N.Z,null,X):X},k=v.forwardRef(t);k.CheckableTag=V;var H=k},71153:function(z,T,e){"use strict";var y=e(38663),g=e.n(y),I=e(13277),A=e.n(I)}}]);
