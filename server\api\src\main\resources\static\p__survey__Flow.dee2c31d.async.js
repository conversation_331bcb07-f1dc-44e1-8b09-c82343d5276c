(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[6132],{55686:function(_e,fe,o){"use strict";var k=o(11849),Ce=o(94233),ee=o(51890),he=o(93224),<PERSON>=o(89366),J=o(85893),Pe=["user"],Oe=function(Le){var z=Le.user,K=(0,he.Z)(Le,Pe);return!z||!z.avatar?(0,J.jsx)(ee.C,(0,k.Z)({icon:(0,J.jsx)(H.Z,{}),size:K.size||"small"},K)):(0,J.jsx)(ee.C,(0,k.Z)({src:"/api/public/preview/".concat(z.avatar),size:K.size||"small"},K))};fe.Z=Oe},90586:function(_e,fe,o){"use strict";o.d(fe,{P:function(){return Ie}});var k=o(11849),Ce=o(93224),ee=o(9761),he=o(67294),H=o(30381),J=o.n(H),Pe=o(85893),Oe=["children","syncDisplay","isMoment"],Ie=function(z){var K=z.children,S=z.syncDisplay,g=z.isMoment,j=(0,Ce.Z)(z,Oe),$=(0,he.useMemo)(function(){return(0,ee.$j)(function(T){var D=T.value,be=T.onChange,d=D;return g&&D&&(d=J()(D)),he.cloneElement(K,{value:d,onChange:function(Ue){be(Ue)}})},(0,ee.jM)(function(T,D){return{value:T.value,onChange:function(d){J().isMoment(d)?D.onInput(d.valueOf()):D.onInput(d)}}}))},[]);return(0,Pe.jsx)(ee.gN,(0,k.Z)((0,k.Z)({},j),{},{component:[$,z],reactions:[function(T){if(S)if(typeof S=="string"){var D=T.query(T.path.parent().concat(S)).get("value");D?T.display="visible":T.display="none"}else typeof S=="object"&&Object.keys(S).forEach(function(be){var d=T.query(T.path.parent().concat(be)).get("value");S[be].includes(d)?T.display="visible":T.display="none"})}]}))};fe.Z=Ie},1997:function(_e,fe,o){"use strict";o.d(fe,{ML:function(){return Ue},tC:function(){return ct},sJ:function(){return Qe},ZP:function(){return dt}});var k=o(71194),Ce=o(50146),ee=o(57663),he=o(71577),H=o(49111),J=o(19650),Pe=o(77576),Oe=o(12028),Ie=o(22385),Le=o(94199),z=o(402),K=o(56118),S=o(94184),g=o.n(S),j=o(67294),$=o(94657),T=o(29656),D=o(63138),be=o(88386),d=o(85893),Re=(0,j.forwardRef)(function(m,h){var E=m.value,F=m.readOnly,b=m.placeholder,A=(0,j.useState)(),N=(0,$.Z)(A,2),P=N[0],V=N[1];(0,j.useEffect)(function(){P&&P.focus()},[P]),(0,j.useImperativeHandle)(h,function(){return{getValue:function(){return P.getValue()},focus:function(){P.focus()},getDoc:function(){return P.getDoc()}}});var te=(0,j.useMemo)(function(){return{mode:"text/x-spreadsheet",line:!0,lineWrapping:!0,readOnly:F,placeholder:b}},[F]),se=function(R,e,O,ne,p){var U=R.getDoc(),I=document.createElement("span");I.innerText=ne.title||ne.path,I.className="formula-tag ".concat(p||"formula-tag-value"),U.markText(e,O,{replacedWith:I})},M=function G(R,e,O){var ne=arguments.length>3&&arguments[3]!==void 0?arguments[3]:[];(ne||[]).forEach(function(p){for(var U="#{".concat(p.path,"}"),I=new RegExp(U,"g");I.exec(e)!==null;){var Fe={line:O,ch:I.lastIndex-U.length},vt={line:O,ch:I.lastIndex};se(R,Fe,vt,p)}for(U="#{_".concat(p.path,"}"),I=new RegExp(U,"g");I.exec(e)!==null;){var re={line:O,ch:I.lastIndex-U.length},ge={line:O,ch:I.lastIndex};se(R,re,ge,p,"formula-tag-text")}p.children&&p.children.length>0&&G(R,e,O,p.children)})},oe=function(R,e){var O=e.split(`
`);O.forEach(function(ne,p){return M(R,ne,p,m.variable)})},xe=function(R,e){V(R),E!=null&&E!==""&&oe(R,e)},ue=function(R,e,O){(O!=null||O!=="")&&oe(R,O),m.onChange&&m.onChange(O)};return(0,d.jsx)("div",{className:g()("formula-editor-content",{readOnly:F}),children:(0,d.jsx)(T.Rt,{autoCursor:!1,value:E,options:te,editorDidMount:xe,onChange:ue})})}),Ue=Re,Y=o(63783),Lt=o(18106),et=o(72488),ft=o(47673),tt=o(77808),nt=o(83279),ht=o(20136),pt=o(55241),pe=o(7359),He=o(27279),it=He.Z.Panel,Xe=K.Z.Link;function ke(m,h,E){if(m.popover){var F,b;return(0,d.jsx)(pt.Z,{content:(0,d.jsxs)("div",{className:"fn-popover-content",children:[(0,d.jsxs)("div",{className:"fn-popover-content-header",children:["\u516C\u5F0F\u7B80\u4ECB",(0,d.jsx)(Xe,{href:m.popover.docHref,target:"_blank",children:"\u6587\u6863"})]}),(F=m.popover)===null||F===void 0?void 0:F.content]}),title:(b=m.popover)===null||b===void 0?void 0:b.title,placement:"right",className:"fn-popover",children:(0,d.jsxs)("div",{className:"function-item",onClick:function(){return E&&E(m.name)},children:[(0,d.jsx)("div",{className:"function-item__name",children:m.name}),(0,d.jsx)("div",{className:"function-item__desc",children:m.description})]})},h)}return(0,d.jsxs)("div",{className:"function-item",onClick:function(){return E&&E(m.name)},children:[(0,d.jsx)("div",{className:"function-item__name",children:m.name}),(0,d.jsx)("div",{className:"function-item__desc",children:m.description})]},h)}var mt=function(h){var E=h.dataSource,F=E===void 0?[]:E,b=h.check,A="function-store";return(0,d.jsx)(He.Z,{className:A,bordered:!1,defaultActiveKey:[0],expandIconPosition:"right",ghost:!0,children:F.map(function(N,P){return(0,d.jsx)(it,{className:"".concat(A,"-group"),header:N.name,children:N.functions.map(function(V,te){return ke(V,te,b)})},P)})})},xt=mt,Ve=o(11849),gt=function(m){return(0,d.jsx)("svg",(0,Ve.Z)((0,Ve.Z)({width:"1em",height:"1.2em",viewBox:"0 0 24 24"},m),{},{children:(0,d.jsx)("path",{d:"M12.42 5.29c-1.1-.1-2.07.71-2.17 1.82L10 10h2.82v2h-3l-.44 5.07A4.001 4.001 0 0 1 2 18.83l1.5-1.5c.33 1.05 1.46 1.64 2.5 1.3c.78-.24 1.33-.93 1.4-1.74L7.82 12h-3v-2H8l.27-3.07a4.01 4.01 0 0 1 4.33-3.65c1.26.11 2.4.81 3.06 1.89l-1.5 1.5c-.25-.77-.93-1.31-1.74-1.38M22 13.65l-1.41-1.41l-2.83 2.83l-2.83-2.83l-1.43 1.41l2.85 2.85l-2.85 2.81l1.43 1.41l2.83-2.83l2.83 2.83L22 19.31l-2.83-2.81L22 13.65z",fill:"currentColor"})}))},Ke=function(m){return(0,d.jsx)("svg",(0,Ve.Z)((0,Ve.Z)({width:"1em",height:"1em",viewBox:"0 0 16 16"},m),{},{children:(0,d.jsx)("g",{fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 5h2V4H1.5l-.5.5v8l.5.5H4v-1H2V5zm12.5-1H12v1h2v7h-2v1h2.5l.5-.5v-8l-.5-.5zm-2.74 2.57L12 7v2.51l-.3.45l-4.5 2h-.46l-2.5-1.5l-.24-.43v-2.5l.3-.46l4.5-2h.46l2.5 1.5zM5 9.71l1.5.9V9.28L5 8.38v1.33zm.58-2.15l1.45.87l3.39-1.5l-1.45-.87l-3.39 1.5zm1.95 3.17l3.5-1.56v-1.4l-3.5 1.55v1.41z"})})}))},Rt=o(32157),rt=o(38614),me=o(34804),yt=function(h){var E=h.id,F=h.type,b=h.title,A=h.pick,N=(0,j.useMemo)(function(){return F==="SplitLine"},[]);return(0,d.jsx)("div",{className:g()("field-variable",{disabled:N}),onClick:function(){A&&A()},children:b})},Ut=yt;function Q(m,h){return{title:(0,d.jsx)(Ut,{type:m.type,id:m.id,title:m.title,pick:function(){return h&&h(m)}}),key:m.id,children:(m.children||[]).map(function(E){return Q(E,h)})}}var q=function(h){var E=h.dataSource,F=h.pick,b=(0,j.useMemo)(function(){return E.map(function(A){return Q(A,F)})},[F]);return(0,d.jsx)(rt.Z,{blockNode:!0,defaultExpandAll:!0,showLine:{showLeafIcon:!1},switcherIcon:(0,d.jsx)(me.Z,{}),className:"hide-file-icon",treeData:b,selectable:!1})},We=q,$e=o(40110),ie=o(54421),_=o(38272),at=function(h){var E=h.check,F=h.dataSource;return(0,d.jsx)(_.ZP,{bordered:!1,dataSource:F,className:"function-store-group",renderItem:function(A){return(0,d.jsx)(_.ZP.Item,{children:ke(A,A.name,E)})}})},st=at,Et=function(h){var E=h.variables,F=E===void 0?[]:E,b=h.insertFun,A=h.insertVariable,N=h.functionVisible,P=N===void 0?!0:N,V=h.helperVariables,te="formula-editor-toolbar",se=(0,j.useState)([]),M=(0,$.Z)(se,2),oe=M[0],xe=M[1],ue=(0,j.useCallback)(function(e){return(0,d.jsxs)("span",{children:[e.icon?(0,d.jsx)("span",{role:"img",className:"anticon",children:e.icon}):null,e.name]})},[]),G=function(O){var ne;if(!O){xe([]);return}var p=new Set;xe(((ne=h.functions)===null||ne===void 0?void 0:ne.reduce(function(U,I){return U.push.apply(U,(0,nt.Z)(I.functions.filter(function(Fe){return Fe.name.includes(O.toUpperCase())}))),U},[]).filter(function(U){return p.has(U.name)?!1:(p.add(U.name),!0)}))||[])},R=[{key:"field",label:(0,d.jsx)(ue,{name:"\u95EE\u9898",icon:(0,d.jsx)(Ke,{})}),children:(0,d.jsx)("div",{children:(0,d.jsx)(We,{dataSource:F,pick:A})})}];return V&&R.push({key:"helperFiled",label:(0,d.jsx)(ue,{name:"\u547D\u540D\u53D8\u91CF",icon:(0,d.jsx)(Ke,{})}),children:(0,d.jsx)("div",{children:(0,d.jsx)(We,{dataSource:V,pick:A})})}),P&&R.push({key:"fx",label:(0,d.jsx)(ue,{name:"\u51FD\u6570",icon:(0,d.jsx)(gt,{})}),children:(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(tt.Z,{placeholder:"\u641C\u7D22\u516C\u5F0F",prefix:(0,d.jsx)($e.Z,{}),allowClear:!0,onChange:function(O){return G(O.target.value)}}),oe.length>0&&(0,d.jsx)(st,{dataSource:oe,check:b}),oe.length===0&&(0,d.jsx)(xt,{dataSource:h.functions,check:b})]})}),(0,d.jsx)("div",{className:te,children:(0,d.jsx)(et.Z,{defaultActiveKey:"field",centered:P,items:R})})};function Ye(m){for(var h=arguments.length,E=new Array(h>1?h-1:0),F=1;F<h;F++)E[F-1]=arguments[F];return(0,d.jsxs)("div",{className:"fn-popover-title",children:[m,"(",E.map(function(b,A){return(0,d.jsxs)("span",{className:"fn-popover-arg",children:[b," ",A===E.length-1?"":","]},A)}),")"]})}var ot="https://support.microsoft.com/zh-cn/office/",Je=[{name:"\u903B\u8F91",functions:[{name:"IF",description:"\u6839\u636E\u5224\u65AD\u6761\u4EF6\uFF0C\u8FD4\u56DE\u6B63\u786E\u6216\u9519\u8BEF\u7684\u503C"},{name:"AND",description:"\u8FD4\u56DE\u903B\u8F91\u503C\uFF1A\u5982\u679C\u6240\u6709\u53C2\u6570\u503C\u5747\u4E3A\u903B\u8F91\u201Ctrue\u201D\uFF0C\u5219\u8FD4\u56DE\u903B\u8F91\u201Ctrue\u201D\uFF0C\u53CD\u4E4B\u8FD4\u56DE\u903B\u8F91\u201Cfalse",popover:{title:Ye("AND","logical1","logical2","..."),content:(0,d.jsxs)("div",{className:"fn-popover-content",children:["\u4EFB\u4F55\u4E00\u4E2A\u53C2\u6570\u7684\u903B\u8F91\u503C\u4E3Afalse\uFF0C\u5373\u8FD4\u56DE false\uFF1B",(0,d.jsx)("br",{})," \u53EA\u6709\u5F53\u6240\u6709\u53C2\u6570\u7684\u903B\u8F91\u503C\u4E3Atrue\uFF0C\u624D\u8FD4\u56DE true\u3002",(0,d.jsx)("br",{}),"logical:\u903B\u8F91\u503C\uFF0C\u4F8B\u5982\uFF0C2>1\u7684\u903B\u8F91\u503C\u4E3A true"]})}},{name:"OR",description:"\u4EFB\u4F55\u4E00\u4E2A\u53C2\u6570\u903B\u8F91\u503C\u4E3Atrue\uFF0C\u5373\u8FD4\u56DEtrue\uFF1B\u53EA\u6709\u5F53\u6240\u6709\u903B\u8F91\u53C2\u6570\u503C\u4E3Afalse\uFF0C\u624D\u8FD4\u56DEfalse"},{name:"XOR",description:"\u8FD4\u56DE\u6240\u6709\u53C2\u6570\u7684\u5F02\u6216\u503C"},{name:"NOT",description:"\u5BF9\u53C2\u6570\u903B\u8F91\u503C\u6C42\u53CD"}]},{name:"\u6587\u5B57",functions:[{name:"SUBSTITUTE",description:"\u5728\u6587\u672C\u5B57\u7B26\u4E32\u4E2D\u7528 new_text \u66FF\u6362 old_text",popover:{title:Ye("SUBSTITUTE","old_text","new_text","[instance_num]"),content:(0,d.jsxs)("div",{className:"fn-popover-content",children:["\u5728\u6587\u672C\u5B57\u7B26\u4E32\u4E2D\u7528 new_text \u66FF\u6362 old_text\u3002",(0,d.jsx)("br",{}),"\u5982\u679C\u9700\u8981\u5728\u67D0\u4E00\u6587\u672C\u5B57\u7B26\u4E32\u4E2D\u66FF\u6362\u6307\u5B9A\u7684\u6587\u672C\uFF0C\u8BF7\u4F7F\u7528\u51FD\u6570 SUBSTITUTE\uFF1B",(0,d.jsx)("br",{}),"\u5982\u679C\u9700\u8981\u5728\u67D0\u4E00\u6587\u672C\u5B57\u7B26\u4E32\u4E2D\u66FF\u6362\u7279\u5B9A\u4F4D\u7F6E\u5904\u7684\u4EFB\u610F\u6587\u672C\uFF0C\u8BF7\u4F7F\u7528\u51FD\u6570 REPLACE\u3002"]}),docHref:"".concat(ot,"substitute-\u51FD\u6570-6434944e-a904-4336-a9b0-1e58df3bc332")}},{name:"CONCATENATE",description:"\u5C06\u591A\u4E2A\u6587\u5B57\u5408\u5E76"},{name:"LEFT",description:"\u4ECE\u5DE6\u8FB9\u622A\u53D6\u6307\u5B9A\u957F\u5EA6\u7684\u6587\u5B57"},{name:"RIGHT",description:"\u4ECE\u53F3\u8FB9\u8FB9\u622A\u53D6\u6307\u5B9A\u957F\u5EA6\u7684\u6587\u5B57"},{name:"MID",description:"\u622A\u53D6\u6307\u5B9A\u4F4D\u7F6E\u7684\u6587\u5B57"},{name:"REPLACE",description:"\u66FF\u6362\u6587\u672C\u4E2D\u7684\u6307\u5B9A\u6587\u5B57",popover:{title:Ye("REPLACE","old_text","start_num","num_chars","new_text"),content:(0,d.jsxs)("div",{className:"fn-popover-content",children:["\u6839\u636E\u6307\u5B9A\u7684\u5B57\u7B26\u6570\uFF0CREPLACE \u5C06\u90E8\u5206\u6587\u672C\u5B57\u7B26\u4E32\u66FF\u6362\u4E3A\u4E0D\u540C\u7684\u6587\u672C\u5B57\u7B26\u4E32\u3002",(0,d.jsx)("br",{}),"REPLACEB \u4F7F\u7528\u5176\u4ED6\u6587\u672C\u5B57\u7B26\u4E32\u5E76\u6839\u636E\u6240\u6307\u5B9A\u7684\u5B57\u8282\u6570\u66FF\u6362\u67D0\u6587\u672C\u5B57\u7B26\u4E32\u4E2D\u7684\u90E8\u5206\u6587\u672C\u3002"]}),docHref:"".concat(ot,"replace-replaceb-\u51FD\u6570-8d799074-2425-4a8a-84bc-82472868878a")}},{name:"TRIM",description:"\u79FB\u9664\u6587\u5B57\u5934\u5C3E\u7684\u7A7A\u683C"},{name:"LEN",description:"\u540E\u53BB\u6587\u5B57\u7684\u957F\u5EA6"},{name:"LOWER",description:"\u8F6C\u6210\u5C0F\u5199\u6587\u5B57"},{name:"UPPER",description:"\u8F6C\u6210\u5927\u5199\u6587\u5B57"}]},{name:"\u6570\u5B57",functions:[{name:"AVERAGE",description:"\u8BA1\u7B97\u6240\u6709\u53C2\u4E0E\u8FD0\u7B97\u5B57\u6BB5\u7684\u5E73\u5747\u503C"},{name:"COUNT",description:"\u8FD4\u56DE\u6240\u6709\u53C2\u4E0E\u8FD0\u7B97\u5B57\u6BB5\u4E2D\u503C\u6570\u5B57\u7684\u6570\u91CF"},{name:"COUNTA",description:"\u8FD4\u56DE\u6240\u6709\u53C2\u4E0E\u8FD0\u7B97\u5B57\u6BB5\u4E2D\u503C\u4E0D\u4E3A\u7A7A\u7684\u6570\u91CF"},{name:"COUNTIF",description:"COUNTIF(\u8868\u683C[\u91D1\u989D], 100)\uFF0C\u53EF\u5F97\u5230\u8868\u683C\u4E2D\u91D1\u989D\u4E3A100\u7684\u6570\u636E\u6761\u6570"},{name:"MAX",description:"\u8FD4\u56DE\u6240\u6709\u53C2\u4E0E\u5B57\u6BB5\u4E2D\u7684\u6700\u5927\u503C"},{name:"MIN",description:"\u8FD4\u56DE\u6240\u6709\u53C2\u4E0E\u5B57\u6BB5\u4E2D\u7684\u6700\u5C0F\u503C"},{name:"ROUND",description:"\u5C06\u6570\u5B57\u56DB\u820D\u4E94\u5165\u5230\u6307\u5B9A\u7684\u4F4D\u6570"},{name:"INT",description:"\u5C06\u6570\u5B57\u5411\u4E0B\u53D6\u6574"},{name:"MOD",description:"\u8FD4\u56DE\u4E24\u6570\u76F8\u9664\u7684\u4F59\u6570"},{name:"PRODUCT",description:"\u8FD4\u56DE\u6240\u6709\u53C2\u4E0E\u5B57\u6BB5\u4E2D\u6570\u503C\u7684\u4E58\u79EF"},{name:"SUM",description:"\u8FD4\u56DE\u6240\u6709\u53C2\u4E0E\u5B57\u6BB5\u4E2D\u6570\u503C\u7684\u603B\u548C"},{name:"SUMPRODUCT",description:"\u8FD4\u56DE\u6240\u6709\u53C2\u4E0E\u5B57\u6BB5\u4E2D\u6570\u503C\u7684\u603B\u548C"},{name:"SUMIF",description:"\u7EDF\u8BA1\u8868\u683C\u4E2D\u7B26\u5408\u6761\u4EF6\u7684\u6570\u503C\uFF0C\u5E76\u6C42\u548C"},{name:"ROUNDUP",description:"\u5C06\u6570\u5B57\u4FDD\u7559\u6307\u5B9A\u7684\u4F4D\u6570\uFF0C\u6700\u540E\u4E00\u4F4D\u5411\u4E0A\u53D6"},{name:"ROUNDDOWN",description:"\u5C06\u6570\u5B57\u4FDD\u7559\u6307\u5B9A\u7684\u4F4D\u6570\uFF0C\u6700\u540E\u4E00\u4F4D\u5411\u4E0B\u53D6"},{name:"POWER",description:"\u8BA1\u7B97\u6570\u5B57num\u7684n\u6B21\u65B9\uFF0Cn\u53EF\u4EE5\u4E3A\u5206\u6570\u6216\u8005\u6574\u6570"},{name:"LN",description:"\u8BA1\u7B97\u6307\u5B9A\u6570\u5B57\u7684\u81EA\u7136\u5BF9\u6570"},{name:"SQRT",description:"\u8BA1\u7B97\u6307\u5B9A\u6570\u5B57\u7684\u5E73\u65B9\u6839"}]},{name:"\u65F6\u95F4",functions:[{name:"YEAR",description:"\u8FD4\u56DE\u65E5\u671F\u4E2D\u7684\u5E74\u4EFD"},{name:"MONTH",description:"\u8FD4\u56DE\u65E5\u671F\u4E2D\u7684\u6708\u4EFD"},{name:"DAY",description:"\u8FD4\u56DE\u65E5\u671F\u5728\u4E00\u4E2A\u6708\u4E2D\u7684\u7B2C\u51E0\u5929\u7684\u6570\u503C"},{name:"HOUR",description:"\u8FD4\u56DE\u65E5\u671F\u4E2D\u7684\u5C0F\u65F6\uFF0C\u65E5\u671F\u5B57\u6BB5\u5FC5\u987B\u7CBE\u786E\u5230\u65F6\u95F4\u624D\u53EF\u4EE5\u8BA1\u7B97\u51FA\u6765"},{name:"MINUTE",description:"\u8FD4\u56DE\u65E5\u671F\u4E2D\u7684\u5206\u949F\uFF0C\u65E5\u671F\u5B57\u6BB5\u5FC5\u987B\u7CBE\u786E\u5230\u65F6\u95F4\u624D\u53EF\u4EE5\u8BA1\u7B97\u51FA\u6765"},{name:"SECOND",description:"\u8FD4\u56DE\u65E5\u671F\u4E2D\u7684\u79D2\uFF0C\u65E5\u671F\u5B57\u6BB5\u5FC5\u987B\u7CBE\u786E\u5230\u65F6\u95F4\u624D\u53EF\u4EE5\u8BA1\u7B97\u51FA\u6765"},{name:"DATE",description:"\u5C06\u6570\u5B57\u62FC\u63A5\u6210\u4E3A\u5E74\u4EFD\uFF0C\u6570\u5B57\u5B57\u6BB5\u987A\u5E8F\u4E3A\uFF1A\u5E74\uFF0F\u6708\uFF0F\u65E5\uFF0F\u65F6\uFF0F\u5206\uFF0F\u79D2"},{name:"CURDATE",description:"\u8FD4\u56DE\u5F53\u524D\u65E5\u671F"},{name:"NOW",description:"\u8FD4\u56DE\u5F53\u524D\u65F6\u95F4\uFF0C\u7CBE\u786E\u5230\u65F6/\u5206/\u79D2"},{name:"DAYS",description:"\u8FD4\u56DE\u65E5\u671F\u5B57\u6BB51\u4E0E\u65E5\u671F\u5B57\u6BB52\u7684\u5DEE\u503C\uFF0C\u5355\u4F4D\u4E3A\u5929"},{name:"DATEDELTA",description:"\u5C06\u6307\u5B9A\u65E5\u671F\u52A0/\u51CF\u6307\u5B9A\u5929\u6570\uFF0C\u5E76\u8FD4\u56DE\u76EE\u6807\u65E5\u671F"}]},{name:"\u81EA\u5B9A\u4E49\u51FD\u6570",functions:[{name:"CURRENT_DATE",description:"\u8FD4\u56DE\u5F53\u524D\u65E5\u671F\uFF0C\u683C\u5F0F 2022-01-01"},{name:"CURRENT_TIME",description:"\u8FD4\u56DE\u5F53\u524D\u65F6\u95F4\uFF0C\u683C\u5F0F 18:20:30"},{name:"CURRENT_DATETIME",description:"\u8FD4\u56DE\u5F53\u524D\u65E5\u671F\u65F6\u95F4\uFF0C\u683C\u5F0F 2022-01-01 18:20:30"},{name:"UUID",description:"UUID \u51FD\u6570\u53EF\u4EE5\u7528\u4E8E\u751F\u6210\u968F\u673A\u7801\uFF0C\u9ED8\u8BA4\u7684\u8F93\u51FA\u683C\u5F0F\u4E3A 32 \u4F4D\u5B57\u7B26\u4E32 + 4 \u4E2A\u201C-\u201D\uFF0C\u4E00\u5171 36 \u4F4D\u3002"}]}];function Ze(m){if(m){var h=Je.map(function(F){return F.functions}).reduce(function(F,b){return F.concat(b)},[]),E=m.map(function(F){return h.find(function(b){return b.name===F})});return[{name:"\u5E38\u7528",functions:E}].concat(Je)}return Je}var ut=o(76826);function jt(m){return m==="SplitLine"||m==="Pagination"||m==="Remark"||m==="Signature"||m==="Upload"}function kt(m){return!!["Dept","FillBlank","User","Score","Nps","Textarea"].includes(m)}var Vt=function m(h){var E;(E=h.children)===null||E===void 0||E.forEach(function(F){F.parent=h,m(F)})};function lt(m){if(m.type==="Upload"){var h,E,F=(h=m.children)===null||h===void 0||(E=h[0].attribute)===null||E===void 0?void 0:E.ocrType;if(F==="idCard"){var b=["\u4F4F\u5740","\u516C\u6C11\u8EAB\u4EFD\u53F7\u7801","\u51FA\u751F","\u59D3\u540D","\u6027\u522B","\u6C11\u65CF","\u5931\u6548\u65E5\u671F","\u7B7E\u53D1\u673A\u5173","\u7B7E\u53D1\u65E5\u671F"];m.children=b.map(function(A){return{id:"ocr.".concat(A),title:A,type:"Option"}})}}return m}var Qe=function(h,E){var F=function(N){var P,V=(P=N.parent)===null||P===void 0?void 0:P.type;return!(V&&["FillBlank","Textarea","Barcode","Nps"].includes(V)&&N.id!=="score"||V==="Upload"&&!N.id.startsWith("ocr")||["Pagination"].includes(N.type)||E!=null&&E.fieldTypes&&!E.fieldTypes.includes(N.type)||(E==null?void 0:E.optionVisible)===!1&&(N.type==="Option"||N.type===void 0))},b=function A(N,P){var V,te,se=[];return N==null||(V=N.children)===null||V===void 0||(te=V.map(function(M){return lt(M)}))===null||te===void 0||te.forEach(function(M,oe){if(M.parent=N,F(M)){var xe=P?"".concat(P,".").concat(M.id):M.id,ue={id:M.id,path:xe,title:(0,ut.WO)(M.title)||"\u9009\u9879".concat(oe+1),type:M.type,tooltip:M.description,children:M.row?M.row.map(function(G){var R="".concat(M.id,".").concat(G.id),e={id:G.id,path:R,title:G.title,type:M.type,tooltip:G.title,children:A(M,R)};return e}):A(M,xe)};se.push(ue)}}),se};return b(h,"")},Ct=K.Z.Link,ct=function(h){var E=h.title,F=h.subTitle,b=h.helpLink,A=h.onOk,N=h.value,P=h.className,V=h.onCancel,te=h.fieldAll,se=h.visible,M=se===void 0?!0:se,oe=h.schema,xe=h.current,ue=h.functions,G=h.functionVisible,R=h.fieldTypes,e=h.helperVariables,O=h.optionVisible,ne=O===void 0?!0:O,p=(0,j.useRef)(null),U="formula-editor",I=(0,j.useRef)(""),Fe=(0,j.useCallback)(function(ge){if(p.current!=null){var Z=p.current.getDoc(),De=Z.getCursor();Z.replaceRange("".concat(ge,"()"),De),De.ch+=ge.length+1,Z.setCursor(De),p.current.focus()}},[]),vt=(0,j.useCallback)(function(ge){if(p.current!=null){var Z=p.current.getDoc(),De=Z.getCursor();Z.replaceRange("#{".concat(I.current).concat(ge.path,"}"),De,De),p.current.focus()}},[]),re=(0,j.useMemo)(function(){return Qe(oe,xe,te,R,ne)},[oe,xe,te,R]);return(0,d.jsx)(Ce.Z,{onCancel:V,open:M,maskClosable:!1,getContainer:h.getContainer,title:E,width:1e3,footer:!1,onOk:function(Z){Z.stopPropagation(),A(p.current.getValue())},keyboard:!1,children:(0,d.jsxs)("div",{className:g()(U,P),children:[(0,d.jsx)(Et,{functionVisible:G,functions:ue||Ze(h.commonUseFunctionNames),variables:re,helperVariables:e,insertFun:Fe,insertVariable:vt}),(0,d.jsxs)("div",{className:"".concat(U,"-main"),children:[(0,d.jsxs)("div",{className:"".concat(U,"-main__code"),children:[(0,d.jsxs)("h1",{children:[(0,d.jsxs)("span",{className:"equle",children:[F,"="]}),(0,d.jsxs)("span",{style:{float:"right"},children:[(0,d.jsx)(Le.Z,{title:"\u903B\u8F91\u8868\u793A\u4F7F\u7528\u5177\u4F53\u503C\u53C2\u4E0E\u903B\u8F91\u8BA1\u7B97\uFF0C\u5982\u5355\u9009\u9898\u9009\u4E2D\u4E3A true;\u6587\u672C\u8868\u793A\u4F7F\u7528\u6807\u9898\u53C2\u4E0E\u6587\u672C\u8BA1\u7B97\uFF0C\u5982\u5355\u9009\u9898\u9009\u4E2D\u9009\u9879\u7684\u6587\u672C\u3002",children:(0,d.jsx)(Y.Z,{style:{fontSize:16,marginRight:10}})}),(0,d.jsx)(Oe.Z,{checkedChildren:"\u903B\u8F91",defaultChecked:!0,unCheckedChildren:"\u6587\u672C",onChange:function(Z){Z?I.current="":I.current="_"}})]})]}),(0,d.jsx)("div",{style:{fontSize:14,color:"#b0b0b9"},children:"\u5728\u5DE6\u4FA7\u9009\u62E9\u51FD\u6570\u6216\u5B57\u6BB5\u53D8\u91CF\uFF0C\u4E14\u5728\u82F1\u6587\u8F93\u5165\u6CD5\u4E0B\u7F16\u8F91"})]}),(0,d.jsx)("div",{className:"".concat(U,"-main__panel"),children:(0,d.jsx)(Ue,{ref:p,variable:re.concat(e||[]),value:N})}),(0,d.jsxs)("div",{className:"".concat(U,"-main__footer"),children:[(0,d.jsxs)(J.Z,{children:[(0,d.jsx)("span",{children:b}),(0,d.jsx)(Ct,{href:"https://surveyking.cn/help/logic/formula",target:"_blank",children:"\u516C\u5F0F\u6587\u6863"})]}),(0,d.jsxs)(J.Z,{children:[(0,d.jsx)(he.Z,{onClick:function(){V&&V()},children:"\u53D6\u6D88"}),(0,d.jsx)(he.Z,{type:"primary",onClick:function(Z){Z.stopPropagation(),A(p.current.getValue())},children:"\u786E\u5B9A"})]})]})]})]})})},dt=ct},1903:function(_e,fe,o){"use strict";o.r(fe),o.d(fe,{Flow:function(){return en},default:function(){return $n}});var k=o(11849),Ce=o(49111),ee=o(19650),he=o(57663),H=o(71577),J=o(39428),Pe=o(3182),Oe=o(71194),Ie=o(50146),Le=o(34792),z=o(48086),K=o(83279),S=o(94657),g=o(67294),j=o(59782),$=o.n(j),T=o(29954),D={pluginName:"control",__lf:null,__controlItems:[{key:"zoom-out",iconClass:"lf-control-zoomOut",title:"\u7F29\u5C0F\u6D41\u7A0B\u56FE",text:"\u7F29\u5C0F",onClick:function(){D.__lf.zoom(!1)}},{key:"zoom-in",iconClass:"lf-control-zoomIn",title:"\u653E\u5927\u6D41\u7A0B\u56FE",text:"\u653E\u5927",onClick:function(){D.__lf.zoom(!0)}},{key:"reset",iconClass:"lf-control-fit",title:"\u6062\u590D\u6D41\u7A0B\u539F\u6709\u5C3A\u5BF8",text:"\u9002\u5E94",onClick:function(){D.__lf.resetZoom()}},{key:"undo",iconClass:"lf-control-undo",title:"\u56DE\u5230\u4E0A\u4E00\u6B65",text:"\u4E0A\u4E00\u6B65",onClick:function(){D.__lf.undo()}},{key:"redo",iconClass:"lf-control-redo",title:"\u79FB\u5230\u4E0B\u4E00\u6B65",text:"\u4E0B\u4E00\u6B65",onClick:function(){D.__lf.redo()}}],addItem:function(n){D.__controlItems.push(n)},removeItem:function(n){var t=D.__controlItems.findIndex(function(a){return a.key===n});return D.__controlItems.splice(t,1)[0]},install:function(){},render:function(n,t){D.__lf=n,D.__domContainer=t,D.__tool=this.__getControlTool(),t.appendChild(D.__tool)},destroy:function(){try{var n;(n=D.__domContainer)===null||n===void 0||n.removeChild(D.__tool)}catch(t){console.warn("unexpect destory error",t)}},__getControlTool:function(){var n="lf-control-item",t="lf-control-item disabled",a=document.createElement("div"),r=[];return a.className="lf-control",D.__controlItems.forEach(function(s){var u=document.createElement("div"),l=document.createElement("i"),c=document.createElement("span");switch(u.className=t,s.onClick&&(u.onclick=s.onClick.bind(null,D.__lf)),s.onMouseEnter&&(u.onmouseenter=s.onMouseEnter.bind(null,D.__lf)),s.onMouseLeave&&(u.onmouseleave=s.onMouseLeave.bind(null,D.__lf)),l.className=s.iconClass,c.className="lf-control-text",c.title=s.title,c.innerText=s.text,u.append(l,c),s.text){case"\u4E0A\u4E00\u6B65":D.__lf.on("history:change",function(v){var f=v.data.undoAble;u.className=f?n:t});break;case"\u4E0B\u4E00\u6B65":D.__lf.on("history:change",function(v){var f=v.data.redoAble;u.className=f?n:t});break;default:u.className=n;break}r.push(u)}),a.append.apply(a,r),a}},be=null;function d(i,n){for(var t=0;t<n;t++)i+="  ";return i}function Re(i,n,t,a){var r="";if(i instanceof Array)for(var s=0,u=i.length;s<u;s++)r+=d(t,a)+Re(i[s],n,t,a+1);else if(typeof i=="object"){var l=!1;r+=d(t,a)+"<"+n;for(var c in i)c.charAt(0)=="-"?r+=" "+c.substr(1)+'="'+i[c].toString().replace("<","&lt;")+'"':l=!0;if(r+=l?">":" />",l){for(var v in i)v=="#text"?r+=i[v]:v=="#cdata"?r+="<![CDATA["+i[v]+"]]>":v.charAt(0)!="-"&&(r+=Re(i[v],v,t,a+1));r+=d(t,a)+"</"+n+">"}}else r+=d(t,a)+"<"+n+">"+i.toString()+"</"+n+">";return r}function Ue(i){var n="";for(var t in i)n+=Re(i[t],t,`	
`,0);return n}var Y=function(){};Y.ObjTree=function(){return this},Y.ObjTree.VERSION="0.23",Y.ObjTree.prototype.xmlDecl=`<?xml version="1.0" encoding="UTF-8" ?>
`,Y.ObjTree.prototype.attr_prefix="-",Y.ObjTree.prototype.parseXML=function(i){var n;if(window.DOMParser){var t=new DOMParser,a=t.parseFromString(i,"application/xml");if(!a)return;n=a.documentElement}else window.ActiveXObject&&(t=new ActiveXObject("Microsoft.XMLDOM"),t.async=!1,t.loadXML(i),n=t.documentElement);if(!!n)return this.parseDOM(n)},Y.ObjTree.prototype.parseHTTP=function(i,n,t){var a={};for(var r in n)a[r]=n[r];if(a.method||(typeof a.postBody=="undefined"&&typeof a.postbody=="undefined"&&typeof a.parameters=="undefined"?a.method="get":a.method="post"),t){a.asynchronous=!0;var s=this,u=t,l=a.onComplete;a.onComplete=function(f){var x;f&&f.responseXML&&f.responseXML.documentElement&&(x=s.parseDOM(f.responseXML.documentElement)),u(x,f),l&&l(f)}}else a.asynchronous=!1;var c;if(typeof HTTP!="undefined"&&HTTP.Request){a.uri=i;var v=new HTTP.Request(a);v&&(c=v.transport)}else if(typeof Ajax!="undefined"&&Ajax.Request){var v=new Ajax.Request(i,a);v&&(c=v.transport)}if(t)return c;if(c&&c.responseXML&&c.responseXML.documentElement)return this.parseDOM(c.responseXML.documentElement)},Y.ObjTree.prototype.parseDOM=function(i){if(!!i){if(this.__force_array={},this.force_array)for(var n=0;n<this.force_array.length;n++)this.__force_array[this.force_array[n]]=1;var t=this.parseElement(i);if(this.__force_array[i.nodeName]&&(t=[t]),i.nodeType!=11){var a={};a[i.nodeName]=t,t=a}return t}},Y.ObjTree.prototype.parseElement=function(i){if(i.nodeType!=7){if(i.nodeType==3||i.nodeType==4){var n=i.nodeValue.match(/[^\x00-\x20]/);return n==null?void 0:i.nodeValue}var t,a={};if(i.attributes&&i.attributes.length){t={};for(var r=0;r<i.attributes.length;r++){var s=i.attributes[r].nodeName;if(typeof s=="string"){var u=i.attributes[r].nodeValue;!u||(s=this.attr_prefix+s,typeof a[s]=="undefined"&&(a[s]=0),a[s]++,this.addNode(t,s,a[s],u))}}}if(i.childNodes&&i.childNodes.length){var l=!0;t&&(l=!1);for(var r=0;r<i.childNodes.length&&l;r++){var c=i.childNodes[r].nodeType;c==3||c==4||(l=!1)}if(l){t||(t="");for(var r=0;r<i.childNodes.length;r++)t+=i.childNodes[r].nodeValue}else{t||(t={});for(var r=0;r<i.childNodes.length;r++){var s=i.childNodes[r].nodeName;if(typeof s=="string"){var u=this.parseElement(i.childNodes[r]);!u||(typeof a[s]=="undefined"&&(a[s]=0),a[s]++,this.addNode(t,s,a[s],u))}}}}return t}},Y.ObjTree.prototype.addNode=function(i,n,t,a){this.__force_array[n]?(t==1&&(i[n]=[]),i[n][i[n].length]=a):t==1?i[n]=a:t==2?i[n]=[i[n],a]:i[n][i[n].length]=a},Y.ObjTree.prototype.writeXML=function(i){var n=this.hash_to_xml(null,i);return this.xmlDecl+n},Y.ObjTree.prototype.hash_to_xml=function(i,n){var t=[],a=[];for(var r in n)if(!!n.hasOwnProperty(r)){var s=n[r];r.charAt(0)!=this.attr_prefix?typeof s=="undefined"||s==null?t[t.length]="<"+r+" />":typeof s=="object"&&s.constructor==Array?t[t.length]=this.array_to_xml(r,s):typeof s=="object"?t[t.length]=this.hash_to_xml(r,s):t[t.length]=this.scalar_to_xml(r,s):a[a.length]=" "+r.substring(1)+'="'+this.xml_escape(s)+'"'}var u=a.join(""),l=t.join("");return typeof i=="undefined"||i==null||(t.length>0?l.match(/\n/)?l="<"+i+u+`>
`+l+"</"+i+`>
`:l="<"+i+u+">"+l+"</"+i+`>
`:l="<"+i+u+` />
`),l},Y.ObjTree.prototype.array_to_xml=function(i,n){for(var t=[],a=0;a<n.length;a++){var r=n[a];typeof r=="undefined"||r==null?t[t.length]="<"+i+" />":typeof r=="object"&&r.constructor==Array?t[t.length]=this.array_to_xml(i,r):typeof r=="object"?t[t.length]=this.hash_to_xml(i,r):t[t.length]=this.scalar_to_xml(i,r)}return t.join("")},Y.ObjTree.prototype.scalar_to_xml=function(i,n){return i=="#text"?this.xml_escape(n):"<"+i+">"+this.xml_escape(n)+"</"+i+`>
`},Y.ObjTree.prototype.xml_escape=function(i){return i.replace(/&/g,"&").replace(/</g,"<").replace(/>/g,">").replace(/"/g,'"')};var Lt=Y,et={width:40,height:40},ft={width:40,height:40},tt={width:40,height:40},nt={width:100,height:80},ht={width:100,height:80},pt={rect:{radius:5,stroke:"rgb(24, 125, 255)"},circle:{r:18,stroke:"rgb(24, 125, 255)"},polygon:{stroke:"rgb(24, 125, 255)"},polyline:{stroke:"rgb(24, 125, 255)",hoverStroke:"rgb(24, 125, 255)",selectedStroke:"rgb(24, 125, 255)"},edgeText:{background:{fill:"white",height:14,stroke:"transparent",radius:3}}},pe;(function(i){i.START="startEvent",i.END="endEvent",i.GATEWAY="exclusiveGateway",i.USER="userTask",i.SYSTEM="serviceTask",i.FLOW="sequenceFlow"})(pe||(pe={}));var He=["startEvent","endEvent","exclusiveGateway","userTask","serviceTask","sequenceFlow","completionCondition","multiInstanceLoopCharacteristics","conditionExpression","#text"],it=["-name","-id","incoming","outgoing","-sourceRef","-targetRef"];function Xe(i){var n={};return Object.entries(i).forEach(function(t){var a=(0,S.Z)(t,2),r=a[0],s=a[1];s!=null&&(typeof s!="object"?r.indexOf("-")===0||He.includes(r)?n[r]=s:n["-".concat(r)]=s:n[r]=Xe(s))}),n}function ke(i){var n={};return Object.entries(i).forEach(function(t){var a=(0,S.Z)(t,2),r=a[0],s=a[1];typeof s=="string"?r.indexOf("-")===0?n[r.substr(1)]=s:n[r]=s:typeof s=="object"?n[r]=ke(s):n[r]=s}),n}function mt(i,n){var t=new Map;n.nodes.forEach(function(r){var s,u={"-id":r.id};if((s=r.text)!==null&&s!==void 0&&s.value&&(u["-name"]=r.text.value),r.properties){var l=Xe(r.properties);Object.assign(u,l)}t.set(r.id,u),i[r.type]?Array.isArray(i[r.type])?i[r.type].push(u):i[r.type]=[i[r.type],u]:i[r.type]=u});var a=n.edges.map(function(r){var s,u={"-id":r.id,"-sourceRef":r.sourceNodeId,"-targetRef":r.targetNodeId};if((s=r.text)!==null&&s!==void 0&&s.value){var l;u["-name"]=(l=r.text)===null||l===void 0?void 0:l.value}if(r.properties){var c=Xe(r.properties);Object.assign(u,c)}return u});i[pe.FLOW]=a}function xt(i,n){i["bpmndi:BPMNEdge"]=n.edges.map(function(t){var a,r=t.id,s=t.pointsList.map(function(l){var c=l.x,v=l.y;return{"-x":c,"-y":v}}),u={"-id":"".concat(r,"_di"),"-bpmnElement":r,"di:waypoint":s};return(a=t.text)!==null&&a!==void 0&&a.value&&(u["bpmndi:BPMNLabel"]={"dc:Bounds":{"-x":t.text.x-t.text.value.length*10/2,"-y":t.text.y-7,"-width":t.text.value.length*10,"-height":14}}),u}),i["bpmndi:BPMNShape"]=n.nodes.map(function(t){var a,r=t.id,s=100,u=80,l=t.x,c=t.y,v=me.shapeConfigMap.get(t.type);v&&(s=v.width,u=v.height),l-=s/2,c-=u/2;var f={"-id":"".concat(r,"_di"),"-bpmnElement":r,"dc:Bounds":{"-x":l,"-y":c,"-width":s,"-height":u}};return(a=t.text)!==null&&a!==void 0&&a.value&&(f["bpmndi:BPMNLabel"]={"dc:Bounds":{"-x":t.text.x-t.text.value.length*10/2,"-y":t.text.y-7,"-width":t.text.value.length*10,"-height":14}}),f})}function Ve(i){var n=[],t=[],a=i.definitions;if(a){var r=a.process;Object.keys(r).forEach(function(s){if(He.includes(s)){var u=r[s];if(s===pe.FLOW){var l=a["bpmndi:BPMNDiagram"]["bpmndi:BPMNPlane"]["bpmndi:BPMNEdge"];t=Rt(u,l)}else{var c=a["bpmndi:BPMNDiagram"]["bpmndi:BPMNPlane"]["bpmndi:BPMNShape"];n=n.concat(gt(u,c,s))}}})}return{nodes:n,edges:t}}function gt(i,n,t){var a=[];if(Array.isArray(i))i.forEach(function(u){var l;Array.isArray(n)?l=n.find(function(v){return v["-bpmnElement"]===u["-id"]}):l=n;var c=Ke(l,t,u);a.push(c)});else{var r;Array.isArray(n)?r=n.find(function(u){return u["-bpmnElement"]===i["-id"]}):r=n;var s=Ke(r,t,i);a.push(s)}return a}function Ke(i,n,t){var a=Number(i["dc:Bounds"]["-x"]),r=Number(i["dc:Bounds"]["-y"]),s=t["-name"],u=me.shapeConfigMap.get(n);u&&(a+=u.width/2,r+=u.height/2);var l;Object.entries(t).forEach(function(x){var y=(0,S.Z)(x,2),L=y[0],le=y[1];it.indexOf(L)===-1&&(l||(l={}),l[L]=le)}),l&&(l=ke(l));var c;if(s&&(c={x:a,y:r,value:s},i["bpmndi:BPMNLabel"]&&i["bpmndi:BPMNLabel"]["dc:Bounds"])){var v=i["bpmndi:BPMNLabel"]["dc:Bounds"];c.x=Number(v["-x"])+Number(v["-width"])/2,c.y=Number(v["-y"])+Number(v["-height"])/2}var f={id:i["-bpmnElement"],type:n,x:a,y:r,properties:l};return c&&(f.text=c),f}function Rt(i,n){var t=[];if(Array.isArray(i))i.forEach(function(r){var s;Array.isArray(n)?s=n.find(function(u){return u["-bpmnElement"]===r["-id"]}):s=n,t.push(rt(s,r))});else{var a;Array.isArray(n)?a=n.find(function(r){return r["-bpmnElement"]===i["-id"]}):a=n,t.push(rt(a,i))}return t}function rt(i,n){var t,a=n["-name"];if(a){var r=i["bpmndi:BPMNLabel"]["dc:Bounds"],s=0;a.split(`
`).forEach(function(c){s<c.length&&(s=c.length)}),t={value:a,x:Number(r["-x"])+s*10/2,y:Number(r["-y"])+7}}var u;Object.entries(n).forEach(function(c){var v=(0,S.Z)(c,2),f=v[0],x=v[1];it.indexOf(f)===-1&&(u||(u={}),u[f]=x)}),u&&(u=ke(u));var l={id:n["-id"],type:pe.FLOW,pointsList:i["di:waypoint"].map(function(c){return{x:Number(c["-x"]),y:Number(c["-y"])}}),sourceNodeId:n["-sourceRef"],targetNodeId:n["-targetRef"],properties:u};return t&&(l.text=t),l}var me={pluginName:"bpmn-adapter",install:function(n){n.adapterIn=this.adapterIn,n.adapterOut=this.adapterOut},shapeConfigMap:new Map,setCustomShape:function(n,t){this.shapeConfigMap.set(n,t)},adapterOut:function(n,t){var a={"-id":"".concat(t),"-isExecutable":"true"};mt(a,n);var r={"-id":"BPMNPlane_1","-bpmnElement":a["-id"]};xt(r,n);var s={definitions:{"-id":"Definitions_".concat(t),"-xmlns":"http://www.omg.org/spec/BPMN/20100524/MODEL","-xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","-xmlns:bpmn":"http://www.omg.org/spec/BPMN/20100524/MODEL","-xmlns:bpmndi":"http://www.omg.org/spec/BPMN/20100524/DI","-xmlns:dc":"http://www.omg.org/spec/DD/20100524/DC","-xmlns:di":"http://www.omg.org/spec/DD/20100524/DI","-xmlns:flowable":"http://flowable.org/bpmn","-xmlns:s":"http://surveyking.cn/bpmn","-targetNamespace":"http://bpmn.io/schema/bpmn","-exporter":"bpmn-js (https://demo.bpmn.io)","-exporterVersion":"7.3.0",process:a,"bpmndi:BPMNDiagram":{"-id":"BPMNDiagram_1","bpmndi:BPMNPlane":r}}};return s},adapterIn:function(n){if(n)return Ve(n)}};me.shapeConfigMap.set(pe.START,{width:et.width,height:et.height}),me.shapeConfigMap.set(pe.END,{width:ft.width,height:ft.height}),me.shapeConfigMap.set(pe.GATEWAY,{width:tt.width,height:tt.height}),me.shapeConfigMap.set(pe.SYSTEM,{width:nt.width,height:nt.height}),me.shapeConfigMap.set(pe.USER,{width:ht.width,height:ht.height});var yt={pluginName:"bpmn-xml-adapter",install:function(n){n.adapterIn=this.adapterXmlIn,n.adapterOut=this.adapterXmlOut},adapterXmlIn:function(n){var t=new Lt.ObjTree().parseXML(n);return me.adapterIn(t)},adapterXmlOut:function(n){var t=me.adapterOut(n,this.options.extraConf.id);return Ue(t)}},Ut=null,Q=o(54941),q=o(69610),We=o(63543),$e=o(94663),ie=o(43028),_=o(15818),at=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(a,r){return(0,q.Z)(this,t),a.id||(a.id="starter"),a.text||(a.text=""),a.text&&typeof a.text=="string"&&(a.text={value:a.text,x:a.x,y:a.y+40}),n.call(this,a,r)}return(0,Q.Z)(t,[{key:"setAttributes",value:function(){this.r=18}},{key:"getConnectedTargetRules",value:function(){var r=(0,We.Z)((0,$e.Z)(t.prototype),"getConnectedTargetRules",this).call(this),s={message:"\u8D77\u59CB\u8282\u70B9\u4E0D\u80FD\u4F5C\u4E3A\u8FB9\u7684\u7EC8\u70B9",validate:function(){return!1}};return r.push(s),r}}]),t}(j.CircleNodeModel);at.extendKey="StartEventModel";var st=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(){return(0,q.Z)(this,t),n.apply(this,arguments)}return(0,Q.Z)(t,[{key:"getShape",value:function(){var r=this.props.model,s=r.x,u=r.y,l=this.props.model.getNodeStyle(),c=l.stroke;return(0,j.h)("svg",{x:s-64/2+10,y:u-64/2+10,width:64,height:64,viewBox:"0 0 32 32"},(0,j.h)("path",{fill:c,d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zM10.622 8.415l4.879 3.252a.4.4 0 0 1 0 .666l-4.88 3.252a.4.4 0 0 1-.621-.332V8.747a.4.4 0 0 1 .622-.332z"}))}}]),t}(j.CircleNode);st.extendKey="StartEventNode";var Et={type:"startEvent",view:st,model:at},Ye=Et,ot=o(17244),Je=(0,ot.kP)("_0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",10);function Ze(){return Je()}var ut=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(a,r){return(0,q.Z)(this,t),a.id||(a.id="Event_".concat(Ze())),a.text||(a.text=""),a.text&&typeof a.text=="string"&&(a.text={value:a.text,x:a.x,y:a.y+40}),n.call(this,a,r)}return(0,Q.Z)(t,[{key:"setAttributes",value:function(){this.r=18}},{key:"getConnectedSourceRules",value:function(){var r=(0,We.Z)((0,$e.Z)(t.prototype),"getConnectedSourceRules",this).call(this),s={message:"\u7ED3\u675F\u8282\u70B9\u4E0D\u80FD\u4F5C\u4E3A\u8FB9\u7684\u8D77\u70B9",validate:function(){return!1}};return r.push(s),r}}]),t}(j.CircleNodeModel);ut.extendKey="EndEventModel";var jt=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(){return(0,q.Z)(this,t),n.apply(this,arguments)}return(0,Q.Z)(t,[{key:"getAnchorStyle",value:function(){return{visibility:"hidden"}}},{key:"getShape",value:function(){var r=this.props.model,s=r.getNodeStyle(),u=r.x,l=r.y,c=r.r,v=r.width,f=r.height,x=(0,We.Z)((0,$e.Z)(t.prototype),"getShape",this).call(this),y=this.props.model.getNodeStyle(),L=y.fill,le=y.stroke,ce=y.strokeWidth,we=(0,j.h)("svg",{x:u-64/2+10,y:l-64/2+10,width:64,height:64,viewBox:"0 0 32 32"},(0,j.h)("path",{fill:le,d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zM9 9h6v6H9V9z"}));return we}}]),t}(j.CircleNode);jt.extendKey="EndEventView";var kt={type:"endEvent",view:jt,model:ut},Vt=kt,lt=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(a,r){var s;return(0,q.Z)(this,t),a.id||(a.id="Gateway_".concat(Ze())),a.text||(a.text=""),a.text&&typeof a.text=="string"&&(a.text={value:a.text,x:a.x,y:a.y+40}),s=n.call(this,a,r),s.points=[[25,0],[50,25],[25,50],[0,25]],s}return(0,Q.Z)(t)}(j.PolygonNodeModel);lt.extendKey="ExclusiveGatewayModel";var Qe=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(){return(0,q.Z)(this,t),n.apply(this,arguments)}return(0,Q.Z)(t,[{key:"getShape",value:function(){var r=this.props.model,s=r.x,u=r.y,l=r.width,c=r.height,v=r.points,f=r.getNodeStyle();return(0,j.h)("g",{transform:"matrix(1 0 0 1 ".concat(s-l/2," ").concat(u-c/2,")")},(0,j.h)("polygon",(0,k.Z)((0,k.Z)({},f),{},{x:s,y:u,points:v})),(0,j.h)("path",(0,k.Z)({d:"m 16,15 7.42857142857143,9.714285714285715 -7.42857142857143,9.714285714285715 3.428571428571429,0 5.714285714285715,-7.464228571428572 5.714285714285715,7.464228571428572 3.428571428571429,0 -7.42857142857143,-9.714285714285715 7.42857142857143,-9.714285714285715 -3.428571428571429,0 -5.714285714285715,7.464228571428572 -5.714285714285715,-7.464228571428572 -3.428571428571429,0 z"},f)))}}]),t}(j.PolygonNode);Qe.extendKey="ExclusiveGatewayNode";var Ct={type:"exclusiveGateway",view:Qe,model:lt},ct=Ct,dt=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(a,r){return(0,q.Z)(this,t),a.id||(a.id="Activity_".concat(Ze())),n.call(this,a,r)}return(0,Q.Z)(t)}(j.RectNodeModel);dt.extendKey="UserTaskModel";var m=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(){return(0,q.Z)(this,t),n.apply(this,arguments)}return(0,Q.Z)(t,[{key:"getLabelShape",value:function(){var r=this.props.model,s=r.x,u=r.y,l=r.width,c=r.height,v=r.getNodeStyle();return(0,j.h)("svg",{x:s-l/2+5,y:u-c/2+5,width:25,height:25,viewBox:"0 0 1274 1024"},(0,j.h)("path",{fill:v.stroke,d:"M655.807326 287.35973m-223.989415 0a218.879 218.879 0 1 0 447.978829 0 218.879 218.879 0 1 0-447.978829 0ZM1039.955839 895.482975c-0.490184-212.177424-172.287821-384.030443-384.148513-384.030443-211.862739 0-383.660376 171.85302-384.15056 384.030443L1039.955839 895.482975z"}))}},{key:"getShape",value:function(){var r=this.props.model,s=r.x,u=r.y,l=r.width,c=r.height,v=r.radius,f=r.getNodeStyle();return(0,j.h)("g",{},[(0,j.h)("rect",(0,k.Z)((0,k.Z)({},f),{},{x:s-l/2,y:u-c/2,rx:v,ry:v,width:l,height:c})),this.getLabelShape()])}}]),t}(j.RectNode);m.extendKey="UserTaskNode";var h={type:"userTask",view:m,model:dt},E=h,F=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(a,r){return(0,q.Z)(this,t),a.id||(a.id="Activity_".concat(Ze())),n.call(this,a,r)}return(0,Q.Z)(t)}(j.RectNodeModel);F.extendKey="ServiceTaskModel";var b=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(){return(0,q.Z)(this,t),n.apply(this,arguments)}return(0,Q.Z)(t,[{key:"getLabelShape",value:function(){var r=this.props.model,s=r.x,u=r.y,l=r.width,c=r.height,v=r.getNodeStyle();return(0,j.h)("svg",{x:s-l/2+5,y:u-c/2+5,width:30,height:30,viewBox:"0 0 1274 1024"},(0,j.h)("path",{fill:v.stroke,d:"M882.527918 434.149934c-2.234901-5.303796-7.311523-8.853645-13.059434-9.138124l-61.390185-3.009544c-6.635117-20.973684-15.521508-41.175795-26.513864-60.282968l42.051745-47.743374c4.308119-4.889357 4.955872-12.004405 1.602498-17.59268-46.384423-77.30362-103.969956-101.422947-106.400309-102.410438-5.332449-2.170432-11.432377-1.090844-15.693424 2.77009L654.674467 240.664222c-17.004279-8.654101-35.092239-15.756869-53.995775-21.210068l-3.26537-66.490344c-0.280386-5.747911-3.833305-10.824533-9.134031-13.059434-1.683339-0.709151-30.193673-12.391215-76.866668-12.051477-46.672996-0.339738-75.18333 11.342326-76.866668 12.051477-5.300726 2.234901-8.853645 7.311523-9.134031 13.059434l-3.26537 66.490344c-18.903535 5.453199-36.991496 12.555967-53.995775 21.210068l-48.450479-43.922349c-4.261047-3.860934-10.360975-4.940522-15.693424-2.77009-2.430352 0.98749-60.015885 25.106818-106.400309 102.410438-3.353374 5.588275-2.705622 12.703323 1.602498 17.59268l42.051745 47.743374c-10.992355 19.107173-19.878746 39.309284-26.513864 60.282968l-61.390185 3.009544c-5.747911 0.284479-10.824533 3.834328-13.059434 9.138124-1.01512 2.415003-24.687262 60.190871-2.822278 147.651828 1.583055 6.324032 7.072069 10.893094 13.57518 11.308557 5.892197 0.37146 11.751648 0.523933 17.419741 0.667196 14.498202 0.372483 28.193109 0.723477 40.908712 4.63353 4.212952 1.294482 6.435573 8.270361 9.349949 18.763342 1.287319 4.640694 2.617617 9.43693 4.484128 14.010085 1.794879 4.393054 3.75758 8.570189 5.66093 12.607132 1.302669 2.765997 2.529613 5.380544 3.689019 8.018627 2.986007 6.803963 2.682086 9.773598 2.578732 10.349719-3.061732 3.672646-6.391571 7.238868-9.91379 11.015891-1.810229 1.943258-3.680832 3.949962-5.523807 5.980201l-22.560832 24.8909c-3.865028 4.261047-4.940522 10.365068-2.774183 15.693424 0.991584 2.426259 25.102724 60.011792 102.414531 106.400309 5.588275 3.353374 12.703323 2.701528 17.591657-1.603521l23.476691-20.682042c2.346441-2.061962 4.64888-4.336772 6.875594-6.534833 9.05319-8.93858 14.018272-12.95608 17.73185-11.576663 3.305279 1.222851 6.907317 3.166109 10.720156 5.228071 3.325745 1.794879 6.764054 3.650133 10.465352 5.288446 6.016017 2.662643 12.120039 4.688789 18.019399 6.65149 6.827499 2.266623 13.279445 4.409426 18.819624 7.275707 1.518586 0.782829 1.926886 0.994654 2.358721 7.830339 0.726547 11.496845 1.25048 23.276123 1.753947 34.672684 0.264013 5.900384 0.528026 11.803837 0.815575 17.700127 0.284479 5.743818 3.833305 10.82044 9.138124 13.05534 1.654686 0.698918 29.371958 12.063757 74.869175 12.063757 0.328481 0 3.65832 0 3.986801 0 45.497217 0 73.214489-11.364839 74.869175-12.063757 5.304819-2.234901 8.853645-7.311523 9.138124-13.05534 0.287549-5.89629 0.551562-11.799744 0.815575-17.700127 0.503467-11.396561 1.027399-23.175839 1.753947-34.672684 0.431835-6.835685 0.840134-7.04751 2.358721-7.830339 5.54018-2.866281 11.992125-5.009084 18.819624-7.275707 5.89936-1.962701 12.003382-3.988848 18.019399-6.65149 3.701299-1.638313 7.139607-3.493567 10.465352-5.288446 3.812839-2.061962 7.414877-4.00522 10.720156-5.228071 3.713578-1.379417 8.67866 2.638083 17.73185 11.576663 2.226714 2.198062 4.529153 4.472871 6.875594 6.534833l23.476691 20.682042c4.888334 4.305049 12.003382 4.956895 17.591657 1.603521 77.311807-46.388517 101.422947-103.97405 102.414531-106.400309 2.166339-5.328355 1.090844-11.432377-2.774183-15.693424l-22.560832-24.8909c-1.842974-2.030239-3.713578-4.036943-5.523807-5.980201-3.52222-3.777023-6.852058-7.343245-9.91379-11.015891-0.103354-0.576121-0.407276-3.545756 2.578732-10.349719 1.159406-2.638083 2.38635-5.252631 3.689019-8.018627 1.90335-4.036943 3.866051-8.214079 5.66093-12.607132 1.866511-4.573155 3.196809-9.369392 4.484128-14.010085 2.914376-10.492982 5.136997-17.46886 9.349949-18.763342 12.715603-3.910053 26.41051-4.261047 40.908712-4.63353 5.668093-0.143263 11.527544-0.295735 17.419741-0.667196 6.503111-0.415462 11.992125-4.984524 13.57518-11.308557C907.21518 494.340805 883.543038 436.564937 882.527918 434.149934zM643.49894 643.761929c-35.280528 35.280528-82.191954 54.711066-132.086317 54.711066s-96.806813-19.430538-132.086317-54.711066c-35.280528-35.279504-54.711066-82.191954-54.711066-132.086317 0-49.894364 19.430538-96.80272 54.711066-132.082224 35.283598-35.284621 82.191954-54.711066 132.086317-54.711066s96.80579 19.426445 132.086317 54.711066c35.279504 35.279504 54.711066 82.187861 54.711066 132.082224C698.210006 561.569976 678.782537 608.482425 643.49894 643.761929z"}))}},{key:"getShape",value:function(){var r=this.props.model,s=r.x,u=r.y,l=r.width,c=r.height,v=r.radius,f=r.getNodeStyle();return(0,j.h)("g",{},[(0,j.h)("rect",(0,k.Z)({x:s-l/2,y:u-c/2,rx:v,ry:v,width:l,height:c},f)),this.getLabelShape()])}}]),t}(j.RectNode);b.extendKey="ServiceTaskNode";var A={type:"serviceTask",view:b,model:F},N=A,P=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(a,r){return(0,q.Z)(this,t),a.id||(a.id="Flow_".concat(Ze())),n.call(this,a,r)}return(0,Q.Z)(t)}(j.PolylineEdgeModel);P.extendKey="SequenceFlowModel";var V=function(i){(0,ie.Z)(t,i);var n=(0,_.Z)(t);function t(){return(0,q.Z)(this,t),n.apply(this,arguments)}return(0,Q.Z)(t)}(j.PolylineEdge);V.extendKey="SequenceFlowEdge";var te={type:"sequenceFlow",view:V,model:P},se=te,M=(0,Q.Z)(function i(n){var t=n.lf;(0,q.Z)(this,i),t.setTheme(pt),t.register(Ye),t.register(Vt),t.register(ct),t.register(E),t.register(N),t.options.customBpmnEdge||(t.register(se),t.setDefaultEdgeType("sequenceFlow"))});M.pluginName="BpmnElement";var oe=o(4150),xe=o(57338),ue=o(273),G=o(29656),R=o(29589),e=o(85893),O={mode:"application/xml",lineWrapping:!0,lineNumbers:!0},ne=function(n){var t=n.visible,a=n.xml,r=n.onClose,s=(0,g.useState)(),u=(0,S.Z)(s,2),l=u[0],c=u[1];return(0,e.jsx)(ue.Z,{open:t,onClose:function(){return r(l==null?void 0:l.getValue())},width:850,className:"flow-drawer",children:(0,e.jsx)(G.Rt,{autoCursor:!1,value:a,options:O,editorDidMount:function(f){return c(f)},onChange:function(){}})})},p=o(9761),U={};function I(i,n){U[i]=n}function Fe(i){return U[i]}var vt=o(18106),re=o(72488),ge=o(43358),Z=o(34041),De=o(32059),Yn=o(13254),Wt=o(14277),Jn=o(88983),Ft=o(66253),ze=o(11628),rn=o(76826),zt=o(96486),an=["FillBlank","MultipleBlank","Textarea","Signature","Radio","Checkbox","Select","Cascader","MatrixAuto","MatrixRadio","MatrixCheckbox","MatrixFillBlank","MatrixScore","Upload","Score","Nps","Remark","SplitLine","User","Dept"],sn=["Pagination","Remark","SplitLine"],on={Pagination:"\u5206\u9875",Remark:"\u5907\u6CE8",SplitLine:"\u5206\u5272\u7EBF"};function Gt(i){var n=[];return i&&((an.includes(i.type)||sn.includes(i.type))&&n.push({id:i.id,title:on[i.type]||(0,rn.WO)(i.title),type:i.type}),i.children&&i.children.map(function(t){var a=Gt(t);a&&n.push.apply(n,(0,K.Z)(a))})),n}var un=function(n){var t=n.value,a=n.onChange;return(0,e.jsxs)("tr",{children:[(0,e.jsx)("td",{children:n.title}),(0,e.jsx)("td",{onClick:function(){return a(2)},children:(0,e.jsx)(Ft.ZP,{value:2,checked:t===2})}),(0,e.jsx)("td",{onClick:function(){return a(1)},children:(0,e.jsx)(Ft.ZP,{value:1,checked:t===1})}),(0,e.jsx)("td",{onClick:function(){return a(0)},children:(0,e.jsx)(Ft.ZP,{value:0,checked:t===0})})]},n.id)},Bt=function(n){var t=n.onChange,a=n.value,r=a===void 0?{}:a,s=(0,ze.IE)(),u=s.schema,l=(0,g.useMemo)(function(){return Gt(u)},[u]);(0,g.useEffect)(function(){if((0,zt.isEmpty)(r)&&l.length>0){var v={};l.forEach(function(f){return v[f.id]=2}),t&&t(v)}},[u,r,l,t]);var c=function(f){t&&t(l.reduce(function(x,y){return x[y.id]=f,x},{}))};return u?(0,e.jsx)("div",{children:(0,e.jsxs)("table",{className:"field-permission-table",children:[(0,e.jsx)("thead",{children:(0,e.jsxs)("tr",{children:[(0,e.jsx)("th",{style:{width:"64%"},children:"\u5B57\u6BB5\u540D"}),(0,e.jsx)("th",{style:{width:"12%"},children:"\u53EF\u7F16\u8F91"}),(0,e.jsx)("th",{style:{width:"12%"},children:"\u4EC5\u53EF\u89C1"}),(0,e.jsx)("th",{style:{width:"12%"},children:"\u9690\u85CF"})]})}),(0,e.jsx)("tbody",{children:l.map(function(v){return(0,g.createElement)(un,(0,k.Z)((0,k.Z)({},v),{},{value:r[v.id],onChange:function(x){t&&t((0,k.Z)((0,k.Z)({},r),{},(0,De.Z)({},v.id,x)))},key:v.id}))})}),(0,e.jsx)("tfoot",{children:(0,e.jsxs)("tr",{children:[(0,e.jsx)("td",{}),(0,e.jsx)("td",{children:(0,e.jsx)(H.Z,{size:"small",type:"link",onClick:function(){return c(2)},children:"\u5168\u9009"})}),(0,e.jsx)("td",{children:(0,e.jsx)(H.Z,{size:"small",type:"link",onClick:function(){return c(1)},children:"\u5168\u9009"})}),(0,e.jsx)("td",{children:(0,e.jsx)(H.Z,{size:"small",type:"link",onClick:function(){return c(0)},children:"\u5168\u9009"})})]})})]})}):(0,e.jsx)("div",{children:(0,e.jsx)(Wt.Z,{image:Wt.Z.PRESENTED_IMAGE_SIMPLE,description:"\u95EE\u5377\u4E3A\u7A7A"})})},Qn=o(20136),ln=o(55241),qn=o(94233),Dt=o(51890),Ne=o(3980),Ht=o(51042),_n=o(22385),Xt=o(94199),cn=o(8913),At=function(n){var t=n.title,a=n.onClose,r=(0,g.useState)(!1),s=(0,S.Z)(r,2),u=s[0],l=s[1];return(0,e.jsx)(Xt.Z,{title:t,children:(0,e.jsxs)("div",{style:{position:"relative"},onMouseOver:function(){return l(!0)},onMouseLeave:function(){return l(!1)},children:[n.children,(0,e.jsx)(cn.Z,{onClick:function(){a&&a()},style:{color:"#f5222d",cursor:"pointer",visibility:u?"visible":"hidden",position:"absolute",right:0,top:0,transform:"translate(25%, -25%)",transformOrigin:"100% 0%"}})]})})},ei=o(71153),wt=o(60331),ti=o(13062),Ge=o(71230),ni=o(48736),Kt=o(27049),ii=o(89032),Ee=o(15746),ri=o(62999),$t=o(54680),dn=o(60780),vn=o.n(dn),fn=Z.Z.Option,hn=(0,p.Pi)(function(i){var n=i.onChange,t=i.orgTreeData,a=i.positions,r=(0,g.useState)(),s=(0,S.Z)(r,2),u=s[0],l=s[1],c=(0,g.useState)(),v=(0,S.Z)(c,2),f=v[0],x=v[1];return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(Ge.Z,{gutter:10,children:[(0,e.jsx)(Ee.Z,{span:16,children:(0,e.jsx)($t.Z,{onChange:function(L){return l(L)},style:{width:"100%"},dropdownStyle:{maxHeight:400,overflow:"auto"},treeData:[{title:"\u53D1\u8D77\u4EBA\u5F53\u524D\u90E8\u95E8",value:"${currentDeptId}",key:"${currentDeptId}"},{title:"\u53D1\u8D77\u4EBA\u4E0A\u7EA7\u90E8\u95E8",value:"${parentDeptId}",key:"${parentDeptId}"}].concat((0,K.Z)(t)),placeholder:"\u8BF7\u9009\u62E9\u673A\u6784",treeDefaultExpandAll:!0,allowClear:!0,value:u})}),(0,e.jsx)(Ee.Z,{span:8,children:(0,e.jsx)(Z.Z,{style:{width:"100%"},placeholder:"\u8BF7\u9009\u62E9\u5C97\u4F4D",allowClear:!0,value:f,onChange:function(L){return x(L)},children:a.map(function(y){return(0,e.jsx)(fn,{value:y.id,children:y.name},y.id)})})})]}),(0,e.jsx)(Ge.Z,{style:{marginTop:10},children:(0,e.jsx)(Ee.Z,{children:(0,e.jsx)(H.Z,{icon:(0,e.jsx)(Ht.Z,{}),onClick:function(){(u||f)&&n("P:".concat(u||"",":").concat(f||""))},children:"\u6DFB\u52A0"})})})]})}),bt=re.Z.TabPane,Yt=Z.Z.Option,pn=(0,p.Pi)(function(i){var n=i.value,t=n===void 0?[]:n,a=i.onChange,r=(0,Ne.m2)(),s=(0,Ne.LF)(t),u=r.depts,l=r.users,c=r.roles,v=r.positions,f=(0,g.useState)(),x=(0,S.Z)(f,2),y=x[0],L=x[1];(0,g.useEffect)(function(){y&&r.loadUsers({deptId:y,pageSize:1024})},[y,r]);var le=(0,g.useMemo)(function(){return vn()(u.map(function(C){return{value:C.id,title:C.name,key:C.id,parentId:C.parentId}}),{parentProperty:"parentId",customID:"value"})},[u]),ce=function(W){var w=(0,K.Z)(t);W.forEach(function(B){w.includes(B)||w.push(B)}),a(w)},we=function(){for(var W=arguments.length,w=new Array(W),B=0;B<W;B++)w[B]=arguments[B];a((0,K.Z)(t.filter(function(X){return!w.includes(X)})))};return(0,e.jsx)("div",{style:{width:400},children:(0,e.jsxs)(re.Z,{defaultActiveKey:"1",children:[(0,e.jsx)(bt,{tab:"\u6210\u5458",children:(0,e.jsxs)("div",{style:{height:400,overflowY:"auto",overflowX:"hidden"},children:[(0,e.jsxs)(Ge.Z,{gutter:20,children:[(0,e.jsx)(Ee.Z,{span:16,children:(0,e.jsx)($t.Z,{onChange:function(W){return L(W)},style:{width:"100%"},dropdownStyle:{maxHeight:400,overflow:"auto"},treeData:le,placeholder:"\u8BF7\u9009\u62E9\u673A\u6784",treeDefaultExpandAll:!0,allowClear:!0})}),(0,e.jsx)(Ee.Z,{span:8,children:(0,e.jsx)(Z.Z,{style:{width:"100%"},placeholder:"\u8BF7\u9009\u62E9\u6210\u5458",allowClear:!0,showSearch:!0,mode:"multiple",onChange:function(W){return ce(W)},maxTagCount:0,value:t.filter(function(C){return C.startsWith("U:")}),dropdownRender:function(W){return(0,e.jsxs)("div",{children:[W,(0,e.jsx)(Kt.Z,{style:{margin:"4px 0"}}),(0,e.jsx)("div",{style:{display:"flex",flexWrap:"nowrap",padding:8},children:(0,e.jsx)(H.Z,{size:"small",type:"link",onClick:function(){return ce(l.map(function(B){return"U:".concat(B.id)}))},children:"\u5168\u9009"})})]})},children:l.map(function(C){return(0,e.jsx)(Yt,{value:"U:".concat(C.id),children:C.name},C.id)})})})]}),(0,e.jsx)(ee.Z,{style:{marginTop:20},wrap:!0,children:t.filter(function(C){return C.startsWith("U:")}).map(function(C){return(0,e.jsx)(wt.Z,{closable:!0,color:"blue",onClose:function(){return we(C)},children:s.user[C.split(":")[1]]},C)})})]})},"fieldPermission"),(0,e.jsx)(bt,{tab:"\u89D2\u8272",children:(0,e.jsxs)("div",{style:{height:400,overflowY:"auto",overflowX:"hidden"},children:[(0,e.jsx)(Z.Z,{style:{width:"100%"},placeholder:"\u8BF7\u9009\u62E9\u89D2\u8272",allowClear:!0,showSearch:!0,mode:"multiple",value:t.filter(function(C){return C.startsWith("R:")}),onChange:function(W){return ce(W)},maxTagCount:0,children:c.map(function(C){return(0,e.jsx)(Yt,{value:"R:".concat(C.id),children:C.name},C.id)})}),(0,e.jsx)(ee.Z,{style:{marginTop:20},wrap:!0,children:t.filter(function(C){return C.startsWith("R:")}).map(function(C){return(0,e.jsx)(wt.Z,{closable:!0,color:"blue",onClose:function(){return we(C)},children:s.role[C.split(":")[1]]},C)})})]})},"basicSetting"),(0,e.jsx)(bt,{tab:"\u673A\u6784\u5C97\u4F4D",children:(0,e.jsxs)("div",{style:{height:400,overflowY:"auto",overflowX:"hidden"},children:[(0,e.jsx)(hn,{positions:v,orgTreeData:le,onChange:function(W){return ce([W])}}),(0,e.jsx)(ee.Z,{style:{marginTop:20},wrap:!0,children:t.filter(function(C){return C.startsWith("P:")}).map(function(C){var W=C.split(":"),w=(0,S.Z)(W,3),B=w[1],X=w[2];return(0,e.jsxs)(wt.Z,{closable:!0,color:"blue",onClose:function(){return we(C)},children:[s.org[B],"-",s.position[X]]},B+":"+X)})})]})},"advanceSetting")]})})}),mn=o(55686),qe=(0,Ne.Gr)(function(i){var n=i.value,t=n===void 0?[]:n,a=i.onChange,r=(0,Ne.LF)(t),s=(0,Ne.m2)(),u=s.users,l=function(){for(var v=arguments.length,f=new Array(v),x=0;x<v;x++)f[x]=arguments[x];a((0,K.Z)(t.filter(function(y){return!f.includes(y)})))};return(0,e.jsx)("div",{style:{lineHeight:"36px",paddingTop:10},children:(0,e.jsxs)(ee.Z,{wrap:!0,children:[(0,e.jsx)(ln.Z,{content:(0,e.jsx)(pn,{onChange:a,value:t}),trigger:"click",placement:"leftTop",children:(0,e.jsx)(Dt.C,{className:"add-user",icon:(0,e.jsx)(Ht.Z,{}),size:24})}),t.map(function(c){var v=c.split(":"),f=(0,S.Z)(v,3),x=f[0],y=f[1],L=f[2];if(x=="U"){var le=u.find(function(ce){return ce.id===y});return(0,e.jsx)(At,{title:r.user[y],onClose:function(){return l(c)},children:(0,e.jsx)(mn.Z,{user:{avatar:le==null?void 0:le.avatar},style:{backgroundColor:"#87d068"},size:24})},y)}else return x==="R"?(0,e.jsx)(At,{title:r.role[y],onClose:function(){return l(c)},children:(0,e.jsx)(Dt.C,{style:{backgroundColor:"#40a9ff"},size:24,children:"R"})},y):x==="P"?(0,e.jsx)(At,{title:"".concat(r.org[y],"-").concat(r.position[L]),onClose:function(){return l(c)},children:(0,e.jsx)(Dt.C,{style:{backgroundColor:"#f56a00"},size:24,children:"P"})},"".concat(y,"-").concat(L)):(0,e.jsx)(e.Fragment,{})})]})})},{name:"identity"}),ai=null,si=o(47673),Zt=o(77808),oi=o(77576),xn=o(12028),gn=o(2603),yn=o(11075),En=o(13105),jn=o(3157),Cn=o(65602),Fn=o(89792),Bn=o(15360),Se=(0,Ne.Gr)(function(i){var n=i.value,t=i.onChange,a=i.initialValue;return(0,e.jsx)(xn.Z,{checkedChildren:"\u5F00\u542F",unCheckedChildren:"\u5173\u95ED",defaultChecked:!!a,checked:n,onChange:function(s){return t(s)}})}),Dn=(0,Ne.Gr)(function(i){var n=i.value,t=i.onChange;return(0,e.jsx)(Zt.Z,{value:n,onChange:t})}),Nt=(0,p.Pi)(function(i){var n=i.settings;return(0,e.jsxs)(p.Wo,{name:"setting",children:[n.includes("passwordRequired")&&(0,e.jsxs)("div",{className:"basic-setting",children:[(0,e.jsx)(gn.Z,{className:"setting-icon"}),(0,e.jsxs)("div",{className:"setting-area",children:[(0,e.jsxs)("div",{className:"setting-title",children:[(0,e.jsx)("span",{children:"\u586B\u5199\u5BC6\u7801"}),(0,e.jsx)(Se,{name:"passwordRequired",initialValue:!0})]}),(0,e.jsx)("div",{className:"setting-hint",children:"\u51ED\u5BC6\u7801\u624D\u80FD\u586B\u5199\u8868\u5355"}),(0,e.jsx)("div",{className:"setting-content",children:(0,e.jsx)(Dn,{name:"password",syncDisplay:"passwordRequired"})})]})]}),n.includes("allowWithdrawStarter")&&(0,e.jsxs)("div",{className:"basic-setting",children:[(0,e.jsx)(yn.Z,{className:"setting-icon"}),(0,e.jsxs)("div",{className:"setting-area",children:[(0,e.jsxs)("div",{className:"setting-title",children:[(0,e.jsx)("span",{children:"\u5141\u8BB8\u64A4\u56DE"}),(0,e.jsx)(Se,{name:"allowWithdraw",initialValue:!0})]}),(0,e.jsx)("div",{className:"setting-hint",children:"\u5141\u8BB8\u7533\u8BF7\u4EBA\u5BF9\u672A\u8FDB\u5165\u6D41\u7A0B\uFF08\u7B2C\u4E00\u4E2A\u6D41\u7A0B\u8282\u70B9\u4E3A\u5F85\u5904\u7406\u72B6\u6001\uFF09\u7684\u7533\u8BF7\u8FDB\u884C\u64A4\u56DE"})]})]}),n.includes("flowLogVisible")&&(0,e.jsxs)("div",{className:"basic-setting",children:[(0,e.jsx)(En.Z,{className:"setting-icon"}),(0,e.jsxs)("div",{className:"setting-area",children:[(0,e.jsxs)("div",{className:"setting-title",children:[(0,e.jsx)("span",{children:"\u6D41\u7A0B\u65E5\u5FD7"}),(0,e.jsx)(Se,{name:"flowLogVisible",initialValue:!0})]}),(0,e.jsx)("div",{className:"setting-hint",children:"\u5141\u8BB8\u67E5\u770B\u6D41\u7A0B\u65E5\u5FD7"})]})]}),n.includes("allowAssignee")&&(0,e.jsxs)("div",{className:"basic-setting",children:[(0,e.jsx)(jn.Z,{className:"setting-icon"}),(0,e.jsxs)("div",{className:"setting-area",children:[(0,e.jsxs)("div",{className:"setting-title",children:[(0,e.jsx)("span",{children:"\u4EE3\u529E\u8F6C\u4EA4"}),(0,e.jsx)(Se,{name:"allowAssignee",initialValue:!0})]}),(0,e.jsx)("div",{className:"setting-hint",children:"\u8282\u70B9\u8D1F\u8D23\u4EBA\u53EF\u5C06\u5F85\u529E\u4E8B\u9879\u8F6C\u4EA4\u7ED9\u5176\u4ED6\u6210\u5458\u5904\u7406"}),(0,e.jsx)("div",{className:"setting-content",children:(0,e.jsx)(qe,{name:"assignee",syncDisplay:"allowAssignee"})})]})]}),n.includes("allowWithdrawUser")&&(0,e.jsxs)("div",{className:"basic-setting",children:[(0,e.jsx)(Cn.Z,{className:"setting-icon"}),(0,e.jsxs)("div",{className:"setting-area",children:[(0,e.jsxs)("div",{className:"setting-title",children:[(0,e.jsx)("span",{children:"\u6D41\u7A0B\u64A4\u56DE"}),(0,e.jsx)(Se,{name:"allowWithdraw",initialValue:!0})]}),(0,e.jsx)("div",{className:"setting-hint",children:"\u8282\u70B9\u8D1F\u8D23\u4EBA\u53EF\u5BF9\u5DF2\u5904\u7406\u8FC7\u7684\u5F85\u529E\u6570\u636E\u8FDB\u884C\u64A4\u56DE"})]})]}),n.includes("allowGoBack")&&(0,e.jsxs)("div",{className:"basic-setting",children:[(0,e.jsx)(Fn.Z,{className:"setting-icon"}),(0,e.jsxs)("div",{className:"setting-area",children:[(0,e.jsxs)("div",{className:"setting-title",children:[(0,e.jsx)("span",{children:"\u6D41\u7A0B\u56DE\u9000"}),(0,e.jsx)(Se,{name:"allowGoBack",initialValue:!0})]}),(0,e.jsx)("div",{className:"setting-hint",children:"\u8282\u70B9\u8D1F\u8D23\u4EBA\u53EF\u5C06\u6D41\u7A0B\u56DE\u9000\u5230\u4E4B\u524D\u7684\u8282\u70B9\uFF0C\u4EC5\u5BA1\u6279\u8282\u70B9\u53EF\u7528"})]})]}),n.includes("allowFinishFlow")&&(0,e.jsxs)("div",{className:"basic-setting",children:[(0,e.jsx)(Bn.Z,{className:"setting-icon"}),(0,e.jsxs)("div",{className:"setting-area",children:[(0,e.jsxs)("div",{className:"setting-title",children:[(0,e.jsx)("span",{children:"\u6D41\u7A0B\u62D2\u7EDD"}),(0,e.jsx)(Se,{name:"allowFinishFlow",initialValue:!0})]}),(0,e.jsx)("div",{className:"setting-hint",children:"\u8282\u70B9\u8D1F\u8D23\u4EBA\u53EF\u4EE5\u62D2\u7EDD\u8BE5\u6D41\u7A0B\uFF08\u62D2\u7EDD\u540E\u6D41\u7A0B\u76F4\u63A5\u7ED3\u675F\uFF0C\u6807\u8BB0\u4E3A\u5DF2\u62D2\u7EDD\uFF09"})]})]})]})}),An=o(54977),wn=function(n){var t=n.value,a=n.onChange,r=(0,g.useState)(!1),s=(0,S.Z)(r,2),u=s[0],l=s[1],c=(0,g.useRef)(null),v=function(x){a&&a(x)};return(0,g.useEffect)(function(){if(u){var f;(f=c.current)===null||f===void 0||f.focus()}},[u]),u?(0,e.jsx)(Zt.Z,{ref:c,value:t,onChange:function(x){return v(x.target.value)},onKeyUp:function(x){x.key==="Enter"&&l(!1)},onBlur:function(x){v(x.target.value),l(!1)}}):(0,e.jsxs)("div",{style:{cursor:"pointer"},className:"title",onClick:function(){return l(!0)},children:[(0,e.jsx)("span",{children:t||"\u70B9\u51FB\u8BBE\u7F6E\u540D\u79F0"}),(0,e.jsx)(An.Z,{style:{marginLeft:10}})]})},ye=o(90586),Jt=Z.Z.Option,St=re.Z.TabPane,bn=(0,p.Pi)(function(){var i=(0,ze.IE)(),n=(0,g.useContext)(Ae),t=n.active,a=n.flow,r=(0,p.cI)(),s=r.query("".concat(i.id,".identity")).get("value"),u=(0,g.useState)(s?"assign":"all"),l=(0,S.Z)(u,2),c=l[0],v=l[1];return(0,g.useEffect)(function(){var f=c==="all"?"none":"visible",x=r.query("".concat(i.id,".identity")).take();x&&x.setDisplay(f)},[c]),(0,g.useEffect)(function(){a==null||a.setProperties(t.id,{candidateStarterUsers:s?s.filter(function(f){return f.startsWith("U:")}).join(","):void 0,candidateStarterGroups:s?s.map(function(f){return!f.startsWith("U:")}).join(","):void 0})},[s]),(0,e.jsxs)(p.Wo,{name:i.id,children:[(0,e.jsx)(p.gN,{name:"id",value:i.id}),(0,e.jsx)(p.gN,{name:"flowType",value:"process"}),(0,e.jsxs)("div",{children:[(0,e.jsx)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:"\u7533\u8BF7\u4EBA"}),(0,e.jsx)("div",{children:(0,e.jsxs)(Z.Z,{style:{width:200},onChange:function(x){return v(x)},value:c,children:[(0,e.jsx)(Jt,{value:"all",children:"\u6240\u6709\u4EBA\u53EF\u586B"}),(0,e.jsx)(Jt,{value:"assign",children:"\u6307\u5B9A\u6210\u5458\u53EF\u586B"})]})}),c==="assign"&&(0,e.jsx)(qe,{}),(0,e.jsxs)(re.Z,{defaultActiveKey:"1",children:[(0,e.jsx)(St,{tab:"\u5B57\u6BB5\u8BBE\u7F6E",children:(0,e.jsx)(ye.Z,{name:"fieldPermission",children:(0,e.jsx)(Bt,{})})},"fieldPermission"),(0,e.jsx)(St,{tab:"\u57FA\u7840\u8BBE\u7F6E",children:(0,e.jsx)(Nt,{settings:["passwordRequired","flowLogVisible","allowWithdrawStarter"]})},"basicSetting"),(0,e.jsx)(St,{tab:"\u9AD8\u7EA7\u8BBE\u7F6E",children:"\u9AD8\u7EA7\u8BBE\u7F6E"},"advanceSetting")]})]})]})});I("Process",bn);var Zn=(0,p.Pi)(function(){var i,n=(0,g.useContext)(Ae),t=n.flow,a=n.active,r=function(u){t==null||t.updateText(a.id,u)};return(0,e.jsxs)(p.Wo,{name:a.id,children:[(0,e.jsx)(p.gN,{name:"id",value:a.id}),(0,e.jsxs)("div",{children:[(0,e.jsx)(Ge.Z,{gutter:[10,10],children:(0,e.jsxs)(Ee.Z,{span:12,children:[(0,e.jsx)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:"\u670D\u52A1\u540D\u79F0"}),(0,e.jsx)("div",{children:(0,e.jsx)(Zt.Z,{placeholder:"\u8BBE\u7F6E\u540D\u79F0",onChange:function(u){return r(u.target.value)},defaultValue:(i=a.text)===null||i===void 0?void 0:i.value})})]})}),(0,e.jsx)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:"\u6761\u4EF6\u516C\u5F0F"})]})]})});I("StartEvent",Zn);var Nn=o(63783),Qt=Z.Z.Option,Mt=re.Z.TabPane,Sn=(0,p.Pi)(function(){var i=(0,g.useContext)(Ae),n=i.flow,t=i.active,a=(0,p.cI)(),r=a.query("".concat(t.id,".setting.isSequential")).get("value"),s=a.query("".concat(t.id,".identity")).get("value"),u=a.query("".concat(t.id,".setting.approveType")).get("value");return(0,g.useEffect)(function(){if((s==null?void 0:s.length)===1&&s[0].startsWith("U:")){n==null||n.setProperties(t.id,{"flowable:assignee":s[0].replace("U:",""),"flowable:candidateUsers":null,"flowable:candidateGroups":null,multiInstanceLoopCharacteristics:null});return}if(s&&r===1&&u===1){s.every(function(l){return l.startsWith("U:")})?n==null||n.setProperties(t.id,{"flowable:assignee":null,"flowable:candidateUsers":s==null?void 0:s.filter(function(l){return l.startsWith("U:")}).map(function(l){return l.replace("U:","")}).join(","),"flowable:candidateGroups":null,multiInstanceLoopCharacteristics:null}):n==null||n.setProperties(t.id,{"flowable:assignee":null,"flowable:candidateUsers":"${t.getUsers('".concat(t.id,"', starterUserId)}"),"flowable:candidateGroups":null,multiInstanceLoopCharacteristics:null});return}n==null||n.setProperties(t.id,{"flowable:assignee":"${t.getAssignee(user)}","flowable:candidateGroups":"${t.getCandidateGroups(user)}",multiInstanceLoopCharacteristics:{isSequential:r===1?"false":"true","flowable:collection":"${t.getUser(execution)}","flowable:elementVariable":"user",completionCondition:"${t.isComplete(execution) }"}})},[r,s,u]),(0,e.jsxs)(p.Wo,{name:t.id,children:[(0,e.jsx)(p.gN,{name:"id",value:t.id}),(0,e.jsx)(p.gN,{name:"flowType",value:"userTask"}),(0,e.jsxs)("div",{children:[(0,e.jsxs)(Ge.Z,{gutter:[10,10],children:[(0,e.jsxs)(Ee.Z,{span:12,children:[(0,e.jsx)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:"\u8D1F\u8D23\u4EBA"}),(0,e.jsx)("div",{children:(0,e.jsx)(p.Wo,{name:"setting",children:(0,e.jsx)(ye.Z,{name:"isSequential",initialValue:1,children:(0,e.jsxs)(Z.Z,{style:{width:"100%"},children:[(0,e.jsx)(Z.Z.Option,{value:1,children:"\u5E38\u89C4\u5BA1\u6279"}),(0,e.jsx)(Z.Z.Option,{value:2,children:"\u9010\u7EA7\u5BA1\u6279"})]})})})})]}),r===1&&(0,e.jsxs)(Ee.Z,{span:12,children:[(0,e.jsxs)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:["\u5BA1\u6279\u7C7B\u578B",(0,e.jsx)(Xt.Z,{title:"\u8D1F\u8D23\u4EBA\u4E3A\u591A\u4EBA\u65F6\u7684\u5BA1\u6279\u65B9\u5F0F",children:(0,e.jsx)(Nn.Z,{style:{marginLeft:5,color:"rgba(43,52,65,.4)",cursor:"pointer"}})})]}),(0,e.jsx)("div",{children:(0,e.jsx)(p.Wo,{name:"setting",children:(0,e.jsx)(ye.Z,{name:"approveType",initialValue:1,syncDisplay:{isSequential:[1]},children:(0,e.jsxs)(Z.Z,{style:{width:"100%"},children:[(0,e.jsx)(Qt,{value:1,children:"\u6216\u7B7E(\u4E00\u540D\u8D1F\u8D23\u4EBA\u901A\u8FC7\u6216\u8005\u62D2\u7EDD\u5373\u53EF)"}),(0,e.jsx)(Qt,{value:2,children:"\u4F1A\u7B7E(\u9700\u6240\u6709\u8D1F\u8D23\u4EBA\u901A\u8FC7)"})]})})})})]}),(0,e.jsx)(Ee.Z,{span:24,children:(0,e.jsx)(qe,{})})]}),(0,e.jsxs)(re.Z,{defaultActiveKey:"1",children:[(0,e.jsx)(Mt,{tab:"\u5B57\u6BB5\u8BBE\u7F6E",children:(0,e.jsx)(ye.Z,{name:"fieldPermission",children:(0,e.jsx)(Bt,{})})},"fieldPermission"),(0,e.jsx)(Mt,{tab:"\u57FA\u7840\u8BBE\u7F6E",children:(0,e.jsx)(Nt,{settings:["allowAssignee","allowFinishFlow","allowGoBack","allowWithdrawStarter","flowLogVisible"]})},"basicSetting"),(0,e.jsx)(Mt,{tab:"\u9AD8\u7EA7\u8BBE\u7F6E",children:"\u9AD8\u7EA7\u8BBE\u7F6E"},"advanceSetting")]})]})]})});I("userTask",Sn);var Mn=function(){return(0,e.jsx)("div",{})};I("EndEvent",Mn);var je=o(1997),Tn=(0,p.Pi)(function(){var i=(0,g.useContext)(Ae),n=i.flow,t=i.active,a=(0,p.cI)(),r=(0,ze.IE)(),s=r.schema,u=(0,g.useState)(!1),l=(0,S.Z)(u,2),c=l[0],v=l[1],f=(0,g.useMemo)(function(){return(0,je.sJ)(r.schema)},[r]),x=a.query("".concat(t.id,".expression")).get("value");return(0,g.useEffect)(function(){n==null||n.setProperties(t.id,{conditionExpression:{"xsi:type":"tFormalExpression","#text":"${t.condition(execution)}"}})},[x]),(0,e.jsxs)(p.Wo,{name:t.id,children:[(0,e.jsx)(p.gN,{name:"id",value:t.id}),(0,e.jsxs)("div",{children:[(0,e.jsxs)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:["\u6761\u4EF6\u516C\u5F0F",(0,e.jsx)(H.Z,{type:"link",onClick:function(){return v(!0)},children:"\u70B9\u51FB\u8BBE\u7F6E"})]}),(0,e.jsx)("div",{className:"formula-container",children:(0,e.jsx)(ye.Z,{name:"expression",children:(0,e.jsx)(je.ML,{variable:f,readOnly:!0,placeholder:"\u516C\u5F0F\u4E3A\u7A7A\uFF0C\u8BF7\u8BBE\u7F6E"})})}),r.schema&&(0,e.jsx)(je.ZP,{visible:c,value:x,title:"\u6761\u4EF6\u516C\u5F0F\u8BBE\u7F6E",schema:s,onCancel:function(){return v(!1)},functions:[{name:"\u57FA\u7840",functions:[{name:"hah",description:"heih"}]}],onOk:function(L){a.setValuesIn("".concat(t.id,".expression"),L),v(!1)}})]})]})});I("sequenceFlow",Tn);var Tt=re.Z.TabPane,Pn=(0,p.Pi)(function(){var i=(0,g.useContext)(Ae),n=i.flow,t=i.active,a=(0,p.cI)(),r=(0,ze.IE)(),s=(0,g.useState)(!1),u=(0,S.Z)(s,2),l=u[0],c=u[1],v=(0,g.useMemo)(function(){return(0,je.sJ)(r.schema)},[r]),f=a.query("".concat(t.id,".expression")).get("value"),x=a.query("".concat(t.id,".flowType")).get("value");return(0,g.useEffect)(function(){n==null||n.setProperties(t.id,{"flowable:expression":"${t.serve(execution)}"})},[f,t]),(0,g.useEffect)(function(){if(x){var y;a.query("*(".concat(t.id,".copyToFlowType,").concat(t.id,".mailFlowType,").concat(t.id,".smsFlowType,").concat(t.id,".httpFlowType)")).forEach(function(L){return L.setDisplay("none")}),(y=a.query("".concat(t.id,".").concat(x,"FlowType")).take())===null||y===void 0||y.setDisplay("visible"),n==null||n.setProperties(t.id,{"s:type":x})}},[x,t]),(0,e.jsxs)(p.Wo,{name:t.id,children:[(0,e.jsx)(p.gN,{name:"id",value:t.id}),(0,e.jsxs)("div",{children:[(0,e.jsx)(Ge.Z,{gutter:[10,10],children:(0,e.jsxs)(Ee.Z,{span:12,children:[(0,e.jsx)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:"\u670D\u52A1\u7C7B\u578B"}),(0,e.jsx)("div",{children:(0,e.jsx)(ye.Z,{name:"flowType",initialValue:"copyTo",children:(0,e.jsxs)(Z.Z,{style:{width:200},children:[(0,e.jsx)(Z.Z.Option,{value:"copyTo",children:"\u6284\u9001\u4EFB\u52A1"}),(0,e.jsx)(Z.Z.Option,{value:"mail",children:"\u53D1\u9001\u90AE\u4EF6"}),(0,e.jsx)(Z.Z.Option,{value:"sms",children:"\u53D1\u9001\u77ED\u4FE1"}),(0,e.jsx)(Z.Z.Option,{value:"http",children:"\u8C03\u7528\u63A5\u53E3"})]})})})]})}),x==="copyTo"&&(0,e.jsx)(p.jG,{name:"sendFlowType",children:(0,e.jsxs)(re.Z,{defaultActiveKey:"1",children:[(0,e.jsx)(Tt,{tab:"\u5B57\u6BB5\u8BBE\u7F6E",children:(0,e.jsx)(ye.Z,{name:"fieldPermission",children:(0,e.jsx)(Bt,{})})},"fieldPermission"),(0,e.jsx)(Tt,{tab:"\u57FA\u7840\u8BBE\u7F6E",children:(0,e.jsx)(Nt,{settings:["flowLogVisible"]})},"basicSetting"),(0,e.jsx)(Tt,{tab:"\u9AD8\u7EA7\u8BBE\u7F6E",children:"\u9AD8\u7EA7\u8BBE\u7F6E"},"advanceSetting")]})}),x==="mail"&&(0,e.jsxs)(p.jG,{name:"mailFlowType",children:[(0,e.jsx)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:"\u6536\u4EF6\u4EBA"}),(0,e.jsxs)("div",{children:[(0,e.jsx)(qe,{name:"identity"}),(0,e.jsxs)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:["\u6761\u4EF6\u516C\u5F0F",(0,e.jsx)(H.Z,{type:"link",onClick:function(){return c(!0)},children:"\u70B9\u51FB\u8BBE\u7F6E"})]}),(0,e.jsx)("div",{className:"formula-container",children:(0,e.jsx)(ye.Z,{name:"expression",children:(0,e.jsx)(je.ML,{variable:v,readOnly:!0,placeholder:"\u6A21\u677F\u4E3A\u7A7A\uFF0C\u8BF7\u8BBE\u7F6E"})})}),r.schema&&(0,e.jsx)(je.ZP,{visible:l,value:f,title:"\u90AE\u4EF6\u6A21\u677F\u8BBE\u7F6E",schema:r.schema,onCancel:function(){return c(!1)},functionVisible:!1,onOk:function(L){a.setValuesIn("".concat(t.id,".expression"),L),c(!1)}})]})]}),x==="sms"&&(0,e.jsxs)(p.jG,{name:"smsFlowType",children:[(0,e.jsx)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:"\u6536\u4EF6\u4EBA"}),(0,e.jsxs)("div",{children:[(0,e.jsx)(qe,{name:"identity"}),(0,e.jsxs)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:["\u6761\u4EF6\u516C\u5F0F",(0,e.jsx)(H.Z,{type:"link",onClick:function(){return c(!0)},children:"\u70B9\u51FB\u8BBE\u7F6E"})]}),(0,e.jsx)("div",{className:"formula-container",children:(0,e.jsx)(ye.Z,{name:"expression",children:(0,e.jsx)(je.ML,{variable:v,readOnly:!0,placeholder:"\u6A21\u677F\u4E3A\u7A7A\uFF0C\u8BF7\u8BBE\u7F6E"})})}),r.schema&&(0,e.jsx)(je.ZP,{visible:l,value:f,title:"\u77ED\u4FE1\u6A21\u677F\u8BBE\u7F6E",schema:r.schema,onCancel:function(){return c(!1)},functionVisible:!1,onOk:function(L){a.setValuesIn("".concat(t.id,".expression"),L),c(!1)}})]})]}),x==="http"&&(0,e.jsxs)(p.jG,{name:"httpFlowType",children:[(0,e.jsxs)("div",{style:{lineHeight:"40px",fontWeight:"bold"},children:["\u63A5\u53E3\u5730\u5740\u914D\u7F6E",(0,e.jsx)(H.Z,{type:"link",onClick:function(){return c(!0)},children:"\u70B9\u51FB\u8BBE\u7F6E"})]}),(0,e.jsx)("div",{className:"formula-container",children:(0,e.jsx)(ye.Z,{name:"expression",children:(0,e.jsx)(je.ML,{variable:v,readOnly:!0,placeholder:"\u8BF7\u8F93\u5165\u63A5\u53E3\u5730\u5740"})})}),r.schema&&(0,e.jsx)(je.ZP,{visible:l,value:f,title:"\u63A5\u53E3\u5730\u5740\u8BBE\u7F6E",schema:r.schema,onCancel:function(){return c(!1)},functionVisible:!1,onOk:function(L){a.setValuesIn("".concat(t.id,".expression"),L),c(!1)}})]})]})]})});I("serviceTask",Pn);var On=o(82061),In=o(94184),Ln=o.n(In),Rn={Process:"\u53D1\u8D77\u4EBA",serviceTask:"\u53D1\u9001\u8282\u70B9",userTask:"\u5BA1\u6279\u8282\u70B9",sequenceFlow:"\u6D41\u7A0B\u5206\u652F"},Un=(0,p.Pi)(function(){var i=(0,g.useContext)(Ae),n=i.active,t=i.flow,a=(0,p.cI)(),r=a.query("".concat(n.id,".name")).get("value");return(0,g.useEffect)(function(){t==null||t.updateText(n.id,r)},[n,r,t]),(0,e.jsxs)("div",{className:Ln()("header",(0,zt.snakeCase)(n.type)),children:[(0,e.jsx)("div",{children:(0,e.jsx)(ye.P,{name:"name",basePath:n.id,children:(0,e.jsx)(wn,{})})}),(0,e.jsxs)("div",{className:"action",children:[Rn[n.type],(0,e.jsx)(Kt.Z,{type:"vertical"}),(0,e.jsx)("span",{className:"action-delete",children:(0,e.jsx)(On.Z,{})})]})]})}),kn=Un,Ae=g.createContext({}),Vn=(0,p.Pi)(function(){var i=(0,g.useContext)(Ae),n=i.active,t=function(){var r=Fe(n.type);return n&&n.type&&!r&&console.log(n.type+"\u672A\u6CE8\u518C"),n&&n.type&&r?g.createElement(Fe(n.type)):g.createElement(Fe("Process"))};return(0,e.jsx)("div",{children:(0,e.jsxs)(ue.Z,{open:!0,mask:!1,closable:!1,width:500,className:"properties-panel",children:[(0,e.jsx)(kn,{}),t()]})})}),Wn=Vn,zn=o(99008),qt=o(54531);function Gn(i){var n=i.lf;function t(){n.dnd.startDrag({type:"startEvent",text:"\u5F00\u59CB"})}function a(){n.dnd.startDrag({type:"userTask"})}function r(){n.dnd.startDrag({type:"serviceTask"})}function s(){n.dnd.startDrag({type:"exclusiveGateway"})}function u(){n.dnd.startDrag({type:"endEvent",text:"\u7ED3\u675F"})}function l(){n.updateEditConfig({stopMoveGraph:!0})}return n&&n.on("selection:selected",function(){n.updateEditConfig({stopMoveGraph:!1})}),(0,e.jsxs)("div",{className:"pattern",children:[(0,e.jsx)("div",{className:"pattern-selection",onMouseDown:function(){return l()}}),(0,e.jsx)("div",{children:"\u9009\u533A"}),(0,e.jsx)("div",{className:"pattern-start",onMouseDown:function(){return t()}}),(0,e.jsx)("div",{children:"\u5F00\u59CB"}),(0,e.jsx)("div",{className:"pattern-user",onMouseDown:function(){return a()}}),(0,e.jsx)("div",{children:"\u5BA1\u6279\u8282\u70B9"}),(0,e.jsx)("div",{className:"pattern-send",onMouseDown:function(){return r()}}),(0,e.jsx)("div",{children:"\u6284\u9001\u8282\u70B9"}),(0,e.jsx)("div",{className:"pattern-condition",onMouseDown:function(){return s()}}),(0,e.jsx)("div",{children:"\u6761\u4EF6\u5224\u65AD"}),(0,e.jsx)("div",{className:"pattern-end",onMouseDown:function(){return u()}}),(0,e.jsx)("div",{children:"\u7ED3\u675F"})]})}var Hn=function(n){return`
<definitions id="Definitions_`.concat(n,`" xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:flowable="http://flowable.org/bpmn" xmlns:s="http://surveyking.cn/bpmn" targetNamespace="http://bpmn.io/schema/bpmn" exporter="bpmn-js (https://demo.bpmn.io)" exporterVersion="7.3.0">
  <process id="`).concat(n,`" isExecutable="true">
    <startEvent id="starter" />
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="`).concat(n,`">
        <bpmndi:BPMNShape id="starter_di" bpmnElement="starter">
          <dc:Bounds x="180" y="120" width="40" height="40" />
        </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
`)},ui={type:"startEvent",text:"\u5F00\u59CB",label:"\u5F00\u59CB",icon:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zM10.622 8.415l4.879 3.252a.4.4 0 0 1 0 .666l-4.88 3.252a.4.4 0 0 1-.621-.332V8.747a.4.4 0 0 1 .622-.332z' fill='rgba(24,125,255,1)'/%3E%3C/svg%3E"},Pt={type:"userTask",label:"\u5BA1\u6279",icon:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M4 22a8 8 0 1 1 16 0h-2a6 6 0 1 0-12 0H4zm8-9c-3.315 0-6-2.685-6-6s2.685-6 6-6 6 2.685 6 6-2.685 6-6 6zm0-2c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4z' fill='rgba(24,125,255,1)'/%3E%3C/svg%3E",properties:{}},Ot={type:"serviceTask",label:"\u53D1\u9001",icon:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M1.923 9.37c-.51-.205-.504-.51.034-.689l19.086-6.362c.529-.176.832.12.684.638l-5.454 19.086c-.15.529-.475.553-.717.07L11 13 1.923 9.37zm4.89-.2l5.636 2.255 3.04 6.082 3.546-12.41L6.812 9.17z' fill='rgba(24,125,255,1)'/%3E%3C/svg%3E",cls:"import_icon"},_t={type:"exclusiveGateway",label:"\u6761\u4EF6\u5224\u65AD",icon:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32'%3E%3Cpath fill='none' d='M0 0H24V24H0z'/%3E%3Cpath d='M9.536 13H7.329c-.412 1.166-1.523 2-2.829 2-1.657 0-3-1.343-3-3s1.343-3 3-3c1.306 0 2.418.835 2.83 2h2.206L13 5h3.17c.412-1.165 1.524-2 2.83-2 1.657 0 3 1.343 3 3s-1.343 3-3 3c-1.306 0-2.417-.834-2.829-2h-2.017l-2.886 4.999L14.155 17h2.016c.411-1.165 1.523-2 2.829-2 1.657 0 3 1.343 3 3s-1.343 3-3 3c-1.306 0-2.417-.834-2.829-2H13l-3.464-6zM19 17c-.552 0-1 .448-1 1s.448 1 1 1 1-.448 1-1-.448-1-1-1zM4.5 11c-.552 0-1 .448-1 1s.448 1 1 1 1-.448 1-1-.448-1-1-1zM19 5c-.552 0-1 .448-1 1s.448 1 1 1 1-.448 1-1-.448-1-1-1z' fill='rgba(24,125,255,1)'/%3E%3C/svg%3E"},Xn={type:"endEvent",label:"\u7ED3\u675F",icon:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zM9 9h6v6H9V9z' fill='rgba(24,125,255,1)'/%3E%3C/svg%3E"},li={label:"\u9009\u533A",icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAAH6ji2bAAAABGdBTUEAALGPC/xhBQAAAOVJREFUOBGtVMENwzAIjKP++2026ETdpv10iy7WFbqFyyW6GBywLCv5gI+Dw2Bluj1znuSjhb99Gkn6QILDY2imo60p8nsnc9bEo3+QJ+AKHfMdZHnl78wyTnyHZD53Zzx73MRSgYvnqgCUHj6gwdck7Zsp1VOrz0Uz8NbKunzAW+Gu4fYW28bUYutYlzSa7B84Fh7d1kjLwhcSdYAYrdkMQVpsBr5XgDGuXwQfQr0y9zwLda+DUYXLaGKdd2ZTtvbolaO87pdo24hP7ov16N0zArH1ur3iwJpXxm+v7oAJNR4JEP8DoAuSFEkYH7cAAAAASUVORK5CYII=",callback:function(){lf.openSelectionSelect(),lf.once("selection:selected",function(){lf.closeSelectionSelect()})}};function Kn(i){var n={icon:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='32' height='32'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M17 6h5v2h-2v13a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V8H2V6h5V3a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v3zm1 2H6v12h12V8zm-4.586 6l1.768 1.768-1.414 1.414L12 15.414l-1.768 1.768-1.414-1.414L10.586 14l-1.768-1.768 1.414-1.414L12 12.586l1.768-1.768 1.414 1.414L13.414 14zM9 4v2h6V4H9z' fill='rgba(24,125,255,1)'/%3E%3C/svg%3E",callback:function(a){i.deleteElement(a.id),i.hideContextMenu()}};i.setContextMenuItems(n),i.setContextMenuByType("startEvent",[Pt,Ot,_t]),i.setContextMenuByType("userTask",[Pt,Ot,_t,Xn]),i.setContextMenuByType("exclusiveGateway",[Pt,Ot])}$().use(M),$().use(yt),$().use(T.a9),$().use(T.Qs),$().use(T.jX),$().use(T.Ou),$().use(T.v2),$().use(T.xV),$().use(D),$().use(T.aC),$().use(T.Ju);var en=(0,p.Pi)(function(){var i=(0,ze.IE)(),n=(0,ze.Ge)(),t=i.id,a=i.name,r=n.loading,s=n.bpmnXml,u=(0,g.useRef)(null),l=(0,g.useRef)(),c=(0,g.useMemo)(function(){return{type:"Process",x:0,y:0,id:t}},[]),v=(0,g.useState)(c),f=(0,S.Z)(v,2),x=f[0],y=f[1];(0,g.useEffect)(function(){if(x.id){var w;(w=l.current)===null||w===void 0||w.selectElementById(x.id)}},[x]),(0,g.useEffect)(function(){if(s){var w;(w=l.current)===null||w===void 0||w.render(s)}},[s]),(0,g.useEffect)(function(){n.getFlow(t)},[n,t]);var L=function(){var B,X,Me,Be=(B=l.current)===null||B===void 0?void 0:B.getGraphData(),ae=(X=l.current)===null||X===void 0?void 0:X.getGraphRawData(),Te=ae==null?void 0:(Me=ae.edges.map(function(de){return de.id})).concat.apply(Me,(0,K.Z)(ae.nodes.map(function(de){return de.id}))),It=Object.values(n.form.values).filter(function(de){return(Te==null?void 0:Te.includes(de.id))||de.id===t});Be&&n.saveFlow({bpmnXml:Be,projectId:t,nodes:It}).then(function(de){de.success?z.default.success("\u4FDD\u5B58\u6210\u529F"):z.default.error(de.message)})},le=function(){var B,X,Me,Be=(B=l.current)===null||B===void 0?void 0:B.getGraphData(),ae=(X=l.current)===null||X===void 0?void 0:X.getGraphRawData(),Te=ae==null?void 0:(Me=ae.edges.map(function(ve){return ve.id})).concat.apply(Me,(0,K.Z)(ae.nodes.map(function(ve){return ve.id}))),It=Object.values(n.form.values).filter(function(ve){return(Te==null?void 0:Te.includes(ve.id))||ve.id===t}),de=ae.nodes.filter(function(ve){return ve.type==="startEvent"});if(de.length===0){z.default.error("\u5F00\u59CB\u8282\u70B9\u672A\u8BBE\u7F6E");return}if(de.length!==1){z.default.error("\u5F00\u59CB\u8282\u70B9\u53EA\u80FD\u8BBE\u7F6E\u4E00\u4E2A");return}var tn=ae.nodes.filter(function(ve){return ve.type==="endEvent"});if(tn.length===0){z.default.error("\u7ED3\u675F\u8282\u70B9\u672A\u8BBE\u7F6E");return}if(tn.length!==1){z.default.error("\u7ED3\u675F\u8282\u70B9\u53EA\u80FD\u8BBE\u7F6E\u4E00\u4E2A");return}Be&&n.saveFlow({bpmnXml:Be,projectId:t,nodes:It}).then(function(ve){ve.success&&n.deploy(t).then(function(nn){nn.success?z.default.success("\u53D1\u5E03\u6210\u529F"):z.default.error(nn.message)})})};(0,g.useEffect)(function(){if(u.current){D.addItem({key:"save",iconClass:"save-icon",title:"",text:"\u4FDD\u5B58",onClick:function(){return L()}}),D.addItem({key:"deploy",iconClass:"deploy-icon",title:"",text:"\u53D1\u5E03",onClick:function(){Ie.Z.confirm({type:"confirm",title:"\u6D41\u7A0B\u53D1\u5E03",content:"\u786E\u5B9A\u53D1\u5E03\u5F53\u524D\u6D41\u7A0B\u5417\uFF1F",onOk:function(){le()}})}});var w=new($())({extraConf:{id:t},container:u.current,hideAnchors:!1,grid:{type:"dot",size:20},keyboard:{enabled:!0},snapline:!0});l.current=w,Kn(w),w.render(s||Hn(t)),w.on("element:click",function(B){var X=["starter","exclusiveGateway","endEvent"];X.includes(B.data.type)?y(c):y(B.data)}),w.on("blank:click",function(B){y(c)}),w.on("node:add",function(B){y(B.data)})}return function(){D.removeItem("deploy"),D.removeItem("save")}},[]);var ce=(0,g.useState)({visible:!1}),we=(0,S.Z)(ce,2),C=we[0],W=we[1];return(0,e.jsx)(p.RV,{form:n.form,children:(0,e.jsxs)("div",{style:{height:"100vh",position:"relative"},className:"flow",children:[(0,e.jsx)("div",{ref:u,style:{height:"100%",flex:1}}),(0,e.jsx)(Gn,{lf:l.current}),(0,e.jsx)("div",{style:{width:500},children:(0,e.jsx)(Ae.Provider,{value:{flow:l.current,active:x},children:(0,e.jsx)(Wn,{})})}),(0,e.jsxs)(ee.Z,{style:{position:"absolute",left:100,bottom:10},children:[(0,e.jsx)(H.Z,{onClick:(0,Pe.Z)((0,J.Z)().mark(function w(){var B,X;return(0,J.Z)().wrap(function(Be){for(;;)switch(Be.prev=Be.next){case 0:try{X=(B=l.current)===null||B===void 0?void 0:B.getGraphData(),W({visible:!0,xml:X})}catch(ae){console.error(ae)}case 1:case"end":return Be.stop()}},w)})),icon:(0,e.jsx)(zn.Z,{}),size:"small",children:"XML"}),(0,e.jsx)(H.Z,{onClick:function(){var B;console.log((B=l.current)===null||B===void 0?void 0:B.getGraphRawData()),console.log((0,qt.ZN)(n.form.values[x.id])),console.log((0,qt.ZN)(n.form.values))},children:"\u6570\u636E"})]}),(0,e.jsx)(ne,(0,k.Z)((0,k.Z)({},C),{},{onClose:function(B){W({visible:!1})}}))]})})}),$n=en},11628:function(_e,fe,o){"use strict";o.d(fe,{xI:function(){return Ce},Ge:function(){return H},IE:function(){return he}});var k=o(67294),Ce=(0,k.createContext)({}),ee=Ce.Provider;function he(){var J=(0,k.useContext)(Ce);return J.store}function H(){var J=he();return J.flowStore}}}]);
