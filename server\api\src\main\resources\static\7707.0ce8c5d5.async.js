(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[7707],{63783:function(_t,ae,R){"use strict";var G=R(28991),M=R(67294),ne=R(36688),Ae=R(27029),P=function(J,W){return M.createElement(Ae.Z,(0,G.Z)((0,G.Z)({},J),{},{ref:W,icon:ne.Z}))};P.displayName="QuestionCircleOutlined",ae.Z=M.forwardRef(P)},50596:function(){},27279:function(_t,ae,R){"use strict";R.d(ae,{Z:function(){return Et}});var G=R(22122),M=R(96156),ne=R(62994),Ae=R(94184),P=R.n(Ae),I=R(85061),J=R(6610),W=R(5991),te=R(10379),ve=R(60446),Ne=R(90484),Y=R(50344),m=R(67294),c=R(96774),C=R.n(c),T=R(81253),S=R(5461),j=R(28481),ue=m.forwardRef(function(fe,$){var oe,K=fe.prefixCls,D=fe.forceRender,Q=fe.className,F=fe.style,q=fe.children,U=fe.isActive,Z=fe.role,ee=m.useState(U||D),se=(0,j.Z)(ee,2),We=se[0],ge=se[1];return m.useEffect(function(){(D||U)&&ge(!0)},[D,U]),We?m.createElement("div",{ref:$,className:P()("".concat(K,"-content"),(oe={},(0,M.Z)(oe,"".concat(K,"-content-active"),U),(0,M.Z)(oe,"".concat(K,"-content-inactive"),!U),oe),Q),style:F,role:Z},m.createElement("div",{className:"".concat(K,"-content-box")},q)):null});ue.displayName="PanelContent";var Or=ue,nt=["className","id","style","prefixCls","headerClass","children","isActive","destroyInactivePanel","accordion","forceRender","openMotion","extra","collapsible"],Ye=function(fe){(0,te.Z)(oe,fe);var $=(0,ve.Z)(oe);function oe(){var K;(0,J.Z)(this,oe);for(var D=arguments.length,Q=new Array(D),F=0;F<D;F++)Q[F]=arguments[F];return K=$.call.apply($,[this].concat(Q)),K.onItemClick=function(){var q=K.props,U=q.onItemClick,Z=q.panelKey;typeof U=="function"&&U(Z)},K.handleKeyPress=function(q){(q.key==="Enter"||q.keyCode===13||q.which===13)&&K.onItemClick()},K.renderIcon=function(){var q=K.props,U=q.showArrow,Z=q.expandIcon,ee=q.prefixCls,se=q.collapsible;if(!U)return null;var We=typeof Z=="function"?Z(K.props):m.createElement("i",{className:"arrow"});return We&&m.createElement("div",{className:"".concat(ee,"-expand-icon"),onClick:se==="header"||se==="icon"?K.onItemClick:null},We)},K.renderTitle=function(){var q=K.props,U=q.header,Z=q.prefixCls,ee=q.collapsible;return m.createElement("span",{className:"".concat(Z,"-header-text"),onClick:ee==="header"?K.onItemClick:null},U)},K}return(0,W.Z)(oe,[{key:"shouldComponentUpdate",value:function(D){return!C()(this.props,D)}},{key:"render",value:function(){var D,Q,F=this.props,q=F.className,U=F.id,Z=F.style,ee=F.prefixCls,se=F.headerClass,We=F.children,ge=F.isActive,vt=F.destroyInactivePanel,X=F.accordion,at=F.forceRender,Zt=F.openMotion,Qe=F.extra,$e=F.collapsible,ze=(0,T.Z)(F,nt),we=$e==="disabled",Ie=$e==="header",et=$e==="icon",St=P()((D={},(0,M.Z)(D,"".concat(ee,"-item"),!0),(0,M.Z)(D,"".concat(ee,"-item-active"),ge),(0,M.Z)(D,"".concat(ee,"-item-disabled"),we),D),q),st=P()("".concat(ee,"-header"),(Q={},(0,M.Z)(Q,se,se),(0,M.Z)(Q,"".concat(ee,"-header-collapsible-only"),Ie),(0,M.Z)(Q,"".concat(ee,"-icon-collapsible-only"),et),Q)),Je={className:st,"aria-expanded":ge,"aria-disabled":we,onKeyPress:this.handleKeyPress};!Ie&&!et&&(Je.onClick=this.onItemClick,Je.role=X?"tab":"button",Je.tabIndex=we?-1:0);var qe=Qe!=null&&typeof Qe!="boolean";return delete ze.header,delete ze.panelKey,delete ze.onItemClick,delete ze.showArrow,delete ze.expandIcon,m.createElement("div",(0,G.Z)({},ze,{className:St,style:Z,id:U}),m.createElement("div",Je,this.renderIcon(),this.renderTitle(),qe&&m.createElement("div",{className:"".concat(ee,"-extra")},Qe)),m.createElement(S.default,(0,G.Z)({visible:ge,leavedClassName:"".concat(ee,"-content-hidden")},Zt,{forceRender:at,removeOnLeave:vt}),function(ar,Xt){var Yt=ar.className,Ui=ar.style;return m.createElement(Or,{ref:Xt,prefixCls:ee,className:Yt,style:Ui,isActive:ge,forceRender:at,role:X?"tabpanel":null},We)}))}}]),oe}(m.Component);Ye.defaultProps={showArrow:!0,isActive:!1,onItemClick:function(){},headerClass:"",forceRender:!1};var Ar=Ye;function xt(fe){var $=fe;if(!Array.isArray($)){var oe=(0,Ne.Z)($);$=oe==="number"||oe==="string"?[$]:[]}return $.map(function(K){return String(K)})}var ot=function(fe){(0,te.Z)(oe,fe);var $=(0,ve.Z)(oe);function oe(K){var D;(0,J.Z)(this,oe),D=$.call(this,K),D.onClickItem=function(U){var Z=D.state.activeKey;if(D.props.accordion)Z=Z[0]===U?[]:[U];else{Z=(0,I.Z)(Z);var ee=Z.indexOf(U),se=ee>-1;se?Z.splice(ee,1):Z.push(U)}D.setActiveKey(Z)},D.getNewChild=function(U,Z){if(!U)return null;var ee=D.state.activeKey,se=D.props,We=se.prefixCls,ge=se.openMotion,vt=se.accordion,X=se.destroyInactivePanel,at=se.expandIcon,Zt=se.collapsible,Qe=U.key||String(Z),$e=U.props,ze=$e.header,we=$e.headerClass,Ie=$e.destroyInactivePanel,et=$e.collapsible,St=!1;vt?St=ee[0]===Qe:St=ee.indexOf(Qe)>-1;var st=et!=null?et:Zt,Je={key:Qe,panelKey:Qe,header:ze,headerClass:we,isActive:St,prefixCls:We,destroyInactivePanel:Ie!=null?Ie:X,openMotion:ge,accordion:vt,children:U.props.children,onItemClick:st==="disabled"?null:D.onClickItem,expandIcon:at,collapsible:st};return typeof U.type=="string"?U:(Object.keys(Je).forEach(function(qe){typeof Je[qe]=="undefined"&&delete Je[qe]}),m.cloneElement(U,Je))},D.getItems=function(){var U=D.props.children;return(0,Y.Z)(U).map(D.getNewChild)},D.setActiveKey=function(U){"activeKey"in D.props||D.setState({activeKey:U}),D.props.onChange(D.props.accordion?U[0]:U)};var Q=K.activeKey,F=K.defaultActiveKey,q=F;return"activeKey"in K&&(q=Q),D.state={activeKey:xt(q)},D}return(0,W.Z)(oe,[{key:"shouldComponentUpdate",value:function(D,Q){return!C()(this.props,D)||!C()(this.state,Q)}},{key:"render",value:function(){var D,Q=this.props,F=Q.prefixCls,q=Q.className,U=Q.style,Z=Q.accordion,ee=P()((D={},(0,M.Z)(D,F,!0),(0,M.Z)(D,q,!!q),D));return m.createElement("div",{className:ee,style:U,role:Z?"tablist":null},this.getItems())}}],[{key:"getDerivedStateFromProps",value:function(D){var Q={};return"activeKey"in D&&(Q.activeKey=xt(D.activeKey)),Q}}]),oe}(m.Component);ot.defaultProps={prefixCls:"rc-collapse",onChange:function(){},accordion:!1,destroyInactivePanel:!1},ot.Panel=Ar;var lt=ot,He=lt,E=lt.Panel,At=R(98423),dt=R(53124),pt=R(33603),Be=R(96159),wt=function($){var oe=m.useContext(dt.E_),K=oe.getPrefixCls,D=$.prefixCls,Q=$.className,F=Q===void 0?"":Q,q=$.showArrow,U=q===void 0?!0:q,Z=K("collapse",D),ee=P()((0,M.Z)({},"".concat(Z,"-no-arrow"),!U),F);return m.createElement(He.Panel,(0,G.Z)({},$,{prefixCls:Z,className:ee}))},Wr=wt,Wt=function($){var oe=m.useContext(dt.E_),K=oe.getPrefixCls,D=oe.direction,Q=$.prefixCls,F=$.className,q=F===void 0?"":F,U=$.bordered,Z=U===void 0?!0:U,ee=$.ghost,se=$.expandIconPosition,We=se===void 0?"start":se,ge=K("collapse",Q),vt=m.useMemo(function(){return We==="left"?"start":We==="right"?"end":We},[We]),X=function(){var ze=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},we=$.expandIcon,Ie=we?we(ze):m.createElement(ne.Z,{rotate:ze.isActive?90:void 0});return(0,Be.Tm)(Ie,function(){return{className:P()(Ie.props.className,"".concat(ge,"-arrow"))}})},at=P()("".concat(ge,"-icon-position-").concat(vt),(0,M.Z)((0,M.Z)((0,M.Z)({},"".concat(ge,"-borderless"),!Z),"".concat(ge,"-rtl"),D==="rtl"),"".concat(ge,"-ghost"),!!ee),q),Zt=(0,G.Z)((0,G.Z)({},pt.ZP),{motionAppear:!1,leavedClassName:"".concat(ge,"-content-hidden")}),Qe=function(){var ze=$.children;return(0,Y.Z)(ze).map(function(we,Ie){var et;if((et=we.props)===null||et===void 0?void 0:et.disabled){var St=we.key||String(Ie),st=we.props,Je=st.disabled,qe=st.collapsible,ar=(0,G.Z)((0,G.Z)({},(0,At.Z)(we.props,["disabled"])),{key:St,collapsible:qe!=null?qe:Je?"disabled":void 0});return(0,Be.Tm)(we,ar)}return we})};return m.createElement(He,(0,G.Z)({openMotion:Zt},$,{expandIcon:X,prefixCls:ge,className:at}),Qe())};Wt.Panel=Wr;var Gt=Wt,Et=Gt},7359:function(_t,ae,R){"use strict";var G=R(38663),M=R.n(G),ne=R(50596),Ae=R.n(ne)},88386:function(_t,ae,R){(function(G){G(R(4631))})(function(G){G.defineOption("placeholder","",function(W,te,ve){var Ne=ve&&ve!=G.Init;if(te&&!Ne)W.on("blur",P),W.on("change",I),W.on("swapDoc",I),G.on(W.getInputField(),"compositionupdate",W.state.placeholderCompose=function(){Ae(W)}),I(W);else if(!te&&Ne){W.off("blur",P),W.off("change",I),W.off("swapDoc",I),G.off(W.getInputField(),"compositionupdate",W.state.placeholderCompose),M(W);var Y=W.getWrapperElement();Y.className=Y.className.replace(" CodeMirror-empty","")}te&&!W.hasFocus()&&P(W)});function M(W){W.state.placeholder&&(W.state.placeholder.parentNode.removeChild(W.state.placeholder),W.state.placeholder=null)}function ne(W){M(W);var te=W.state.placeholder=document.createElement("pre");te.style.cssText="height: 0; overflow: visible",te.style.direction=W.getOption("direction"),te.className="CodeMirror-placeholder CodeMirror-line-like";var ve=W.getOption("placeholder");typeof ve=="string"&&(ve=document.createTextNode(ve)),te.appendChild(ve),W.display.lineSpace.insertBefore(te,W.display.lineSpace.firstChild)}function Ae(W){setTimeout(function(){var te=!1;if(W.lineCount()==1){var ve=W.getInputField();te=ve.nodeName=="TEXTAREA"?!W.getLine(0).length:!/[^\u200b]/.test(ve.querySelector(".CodeMirror-line").textContent)}te?ne(W):M(W)},20)}function P(W){J(W)&&ne(W)}function I(W){var te=W.getWrapperElement(),ve=J(W);te.className=te.className.replace(" CodeMirror-empty","")+(ve?" CodeMirror-empty":""),ve?ne(W):M(W)}function J(W){return W.lineCount()===1&&W.getLine(0)===""}})},4631:function(_t){(function(ae,R){_t.exports=R()})(this,function(){"use strict";var ae=navigator.userAgent,R=navigator.platform,G=/gecko\/\d/i.test(ae),M=/MSIE \d/.test(ae),ne=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(ae),Ae=/Edge\/(\d+)/.exec(ae),P=M||ne||Ae,I=P&&(M?document.documentMode||6:+(Ae||ne)[1]),J=!Ae&&/WebKit\//.test(ae),W=J&&/Qt\/\d+\.\d+/.test(ae),te=!Ae&&/Chrome\/(\d+)/.exec(ae),ve=te&&+te[1],Ne=/Opera\//.test(ae),Y=/Apple Computer/.test(navigator.vendor),m=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(ae),c=/PhantomJS/.test(ae),C=Y&&(/Mobile\/\w+/.test(ae)||navigator.maxTouchPoints>2),T=/Android/.test(ae),S=C||T||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(ae),j=C||/Mac/.test(R),ue=/\bCrOS\b/.test(ae),Or=/win/i.test(R),nt=Ne&&ae.match(/Version\/(\d*\.\d*)/);nt&&(nt=Number(nt[1])),nt&&nt>=15&&(Ne=!1,J=!0);var Ye=j&&(W||Ne&&(nt==null||nt<12.11)),Ar=G||P&&I>=9;function xt(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var ot=function(e,t){var i=e.className,r=xt(t).exec(i);if(r){var n=i.slice(r.index+r[0].length);e.className=i.slice(0,r.index)+(n?r[1]+n:"")}};function lt(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function He(e,t){return lt(e).appendChild(t)}function E(e,t,i,r){var n=document.createElement(e);if(i&&(n.className=i),r&&(n.style.cssText=r),typeof t=="string")n.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)n.appendChild(t[o]);return n}function At(e,t,i,r){var n=E(e,t,i,r);return n.setAttribute("role","presentation"),n}var dt;document.createRange?dt=function(e,t,i,r){var n=document.createRange();return n.setEnd(r||e,i),n.setStart(e,t),n}:dt=function(e,t,i){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(n){return r}return r.collapse(!0),r.moveEnd("character",i),r.moveStart("character",t),r};function pt(e,t){if(t.nodeType==3&&(t=t.parentNode),e.contains)return e.contains(t);do if(t.nodeType==11&&(t=t.host),t==e)return!0;while(t=t.parentNode)}function Be(e){var t=e.ownerDocument||e,i;try{i=e.activeElement}catch(r){i=t.body||null}for(;i&&i.shadowRoot&&i.shadowRoot.activeElement;)i=i.shadowRoot.activeElement;return i}function wt(e,t){var i=e.className;xt(t).test(i)||(e.className+=(i?" ":"")+t)}function Wr(e,t){for(var i=e.split(" "),r=0;r<i.length;r++)i[r]&&!xt(i[r]).test(t)&&(t+=" "+i[r]);return t}var Wt=function(e){e.select()};C?Wt=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:P&&(Wt=function(e){try{e.select()}catch(t){}});function Gt(e){return e.display.wrapper.ownerDocument}function Et(e){return fe(e.display.wrapper)}function fe(e){return e.getRootNode?e.getRootNode():e.ownerDocument}function $(e){return Gt(e).defaultView}function oe(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function K(e,t,i){t||(t={});for(var r in e)e.hasOwnProperty(r)&&(i!==!1||!t.hasOwnProperty(r))&&(t[r]=e[r]);return t}function D(e,t,i,r,n){t==null&&(t=e.search(/[^\s\u00a0]/),t==-1&&(t=e.length));for(var o=r||0,l=n||0;;){var a=e.indexOf("	",o);if(a<0||a>=t)return l+(t-o);l+=a-o,l+=i-l%i,o=a+1}}var Q=function(){this.id=null,this.f=null,this.time=0,this.handler=oe(this.onTimeout,this)};Q.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},Q.prototype.set=function(e,t){this.f=t;var i=+new Date+e;(!this.id||i<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=i)};function F(e,t){for(var i=0;i<e.length;++i)if(e[i]==t)return i;return-1}var q=50,U={toString:function(){return"CodeMirror.Pass"}},Z={scroll:!1},ee={origin:"*mouse"},se={origin:"+move"};function We(e,t,i){for(var r=0,n=0;;){var o=e.indexOf("	",r);o==-1&&(o=e.length);var l=o-r;if(o==e.length||n+l>=t)return r+Math.min(l,t-n);if(n+=o-r,n+=i-n%i,r=o+1,n>=t)return r}}var ge=[""];function vt(e){for(;ge.length<=e;)ge.push(X(ge)+" ");return ge[e]}function X(e){return e[e.length-1]}function at(e,t){for(var i=[],r=0;r<e.length;r++)i[r]=t(e[r],r);return i}function Zt(e,t,i){for(var r=0,n=i(t);r<e.length&&i(e[r])<=n;)r++;e.splice(r,0,t)}function Qe(){}function $e(e,t){var i;return Object.create?i=Object.create(e):(Qe.prototype=e,i=new Qe),t&&K(t,i),i}var ze=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function we(e){return/\w/.test(e)||e>"\x80"&&(e.toUpperCase()!=e.toLowerCase()||ze.test(e))}function Ie(e,t){return t?t.source.indexOf("\\w")>-1&&we(e)?!0:t.test(e):we(e)}function et(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var St=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function st(e){return e.charCodeAt(0)>=768&&St.test(e)}function Je(e,t,i){for(;(i<0?t>0:t<e.length)&&st(e.charAt(t));)t+=i;return t}function qe(e,t,i){for(var r=t>i?-1:1;;){if(t==i)return t;var n=(t+i)/2,o=r<0?Math.ceil(n):Math.floor(n);if(o==t)return e(o)?t:i;e(o)?i=o:t=o+r}}function ar(e,t,i,r){if(!e)return r(t,i,"ltr",0);for(var n=!1,o=0;o<e.length;++o){var l=e[o];(l.from<i&&l.to>t||t==i&&l.to==t)&&(r(Math.max(l.from,t),Math.min(l.to,i),l.level==1?"rtl":"ltr",o),n=!0)}n||r(t,i,"ltr")}var Xt=null;function Yt(e,t,i){var r;Xt=null;for(var n=0;n<e.length;++n){var o=e[n];if(o.from<t&&o.to>t)return n;o.to==t&&(o.from!=o.to&&i=="before"?r=n:Xt=n),o.from==t&&(o.from!=o.to&&i!="before"?r=n:Xt=n)}return r!=null?r:Xt}var Ui=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function i(u){return u<=247?e.charAt(u):1424<=u&&u<=1524?"R":1536<=u&&u<=1785?t.charAt(u-1536):1774<=u&&u<=2220?"r":8192<=u&&u<=8203?"w":u==8204?"b":"L"}var r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,n=/[stwN]/,o=/[LRr]/,l=/[Lb1n]/,a=/[1n]/;function s(u,f,h){this.level=u,this.from=f,this.to=h}return function(u,f){var h=f=="ltr"?"L":"R";if(u.length==0||f=="ltr"&&!r.test(u))return!1;for(var p=u.length,d=[],v=0;v<p;++v)d.push(i(u.charCodeAt(v)));for(var g=0,b=h;g<p;++g){var x=d[g];x=="m"?d[g]=b:b=x}for(var L=0,w=h;L<p;++L){var k=d[L];k=="1"&&w=="r"?d[L]="n":o.test(k)&&(w=k,k=="r"&&(d[L]="R"))}for(var A=1,O=d[0];A<p-1;++A){var _=d[A];_=="+"&&O=="1"&&d[A+1]=="1"?d[A]="1":_==","&&O==d[A+1]&&(O=="1"||O=="n")&&(d[A]=O),O=_}for(var le=0;le<p;++le){var Me=d[le];if(Me==",")d[le]="N";else if(Me=="%"){var ce=void 0;for(ce=le+1;ce<p&&d[ce]=="%";++ce);for(var Ve=le&&d[le-1]=="!"||ce<p&&d[ce]=="1"?"1":"N",Ge=le;Ge<ce;++Ge)d[Ge]=Ve;le=ce-1}}for(var Ce=0,Ze=h;Ce<p;++Ce){var Oe=d[Ce];Ze=="L"&&Oe=="1"?d[Ce]="L":o.test(Oe)&&(Ze=Oe)}for(var xe=0;xe<p;++xe)if(n.test(d[xe])){var be=void 0;for(be=xe+1;be<p&&n.test(d[be]);++be);for(var de=(xe?d[xe-1]:h)=="L",Xe=(be<p?d[be]:h)=="L",Dr=de==Xe?de?"L":"R":h,Kt=xe;Kt<be;++Kt)d[Kt]=Dr;xe=be-1}for(var Pe=[],bt,De=0;De<p;)if(l.test(d[De])){var Qn=De;for(++De;De<p&&l.test(d[De]);++De);Pe.push(new s(0,Qn,De))}else{var Ot=De,or=Pe.length,lr=f=="rtl"?1:0;for(++De;De<p&&d[De]!="L";++De);for(var Re=Ot;Re<De;)if(a.test(d[Re])){Ot<Re&&(Pe.splice(or,0,new s(1,Ot,Re)),or+=lr);var Nr=Re;for(++Re;Re<De&&a.test(d[Re]);++Re);Pe.splice(or,0,new s(2,Nr,Re)),or+=lr,Ot=Re}else++Re;Ot<De&&Pe.splice(or,0,new s(1,Ot,De))}return f=="ltr"&&(Pe[0].level==1&&(bt=u.match(/^\s+/))&&(Pe[0].from=bt[0].length,Pe.unshift(new s(0,0,bt[0].length))),X(Pe).level==1&&(bt=u.match(/\s+$/))&&(X(Pe).to-=bt[0].length,Pe.push(new s(0,p-bt[0].length,p)))),f=="rtl"?Pe.reverse():Pe}}();function Lt(e,t){var i=e.order;return i==null&&(i=e.order=Ui(e.text,t)),i}var qn=[],H=function(e,t,i){if(e.addEventListener)e.addEventListener(t,i,!1);else if(e.attachEvent)e.attachEvent("on"+t,i);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||qn).concat(i)}};function Ki(e,t){return e._handlers&&e._handlers[t]||qn}function tt(e,t,i){if(e.removeEventListener)e.removeEventListener(t,i,!1);else if(e.detachEvent)e.detachEvent("on"+t,i);else{var r=e._handlers,n=r&&r[t];if(n){var o=F(n,i);o>-1&&(r[t]=n.slice(0,o).concat(n.slice(o+1)))}}}function ye(e,t){var i=Ki(e,t);if(!!i.length)for(var r=Array.prototype.slice.call(arguments,2),n=0;n<i.length;++n)i[n].apply(null,r)}function Se(e,t,i){return typeof t=="string"&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),ye(e,i||t.type,e,t),_i(t)||t.codemirrorIgnore}function jn(e){var t=e._handlers&&e._handlers.cursorActivity;if(!!t)for(var i=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)F(i,t[r])==-1&&i.push(t[r])}function it(e,t){return Ki(e,t).length>0}function sr(e){e.prototype.on=function(t,i){H(this,t,i)},e.prototype.off=function(t,i){tt(this,t,i)}}function Ue(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Vn(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function _i(e){return e.defaultPrevented!=null?e.defaultPrevented:e.returnValue==!1}function Er(e){Ue(e),Vn(e)}function Gi(e){return e.target||e.srcElement}function $n(e){var t=e.which;return t==null&&(e.button&1?t=1:e.button&2?t=3:e.button&4&&(t=2)),j&&e.ctrlKey&&t==1&&(t=3),t}var ta=function(){if(P&&I<9)return!1;var e=E("div");return"draggable"in e||"dragDrop"in e}(),Zi;function ra(e){if(Zi==null){var t=E("span","\u200B");He(e,E("span",[t,document.createTextNode("x")])),e.firstChild.offsetHeight!=0&&(Zi=t.offsetWidth<=1&&t.offsetHeight>2&&!(P&&I<8))}var i=Zi?E("span","\u200B"):E("span","\xA0",null,"display: inline-block; width: 1px; margin-right: -1px");return i.setAttribute("cm-text",""),i}var Xi;function ia(e){if(Xi!=null)return Xi;var t=He(e,document.createTextNode("A\u062EA")),i=dt(t,0,1).getBoundingClientRect(),r=dt(t,1,2).getBoundingClientRect();return lt(e),!i||i.left==i.right?!1:Xi=r.right-i.right<3}var Yi=`

b`.split(/\n/).length!=3?function(e){for(var t=0,i=[],r=e.length;t<=r;){var n=e.indexOf(`
`,t);n==-1&&(n=e.length);var o=e.slice(t,e.charAt(n-1)=="\r"?n-1:n),l=o.indexOf("\r");l!=-1?(i.push(o.slice(0,l)),t+=l+1):(i.push(o),t=n+1)}return i}:function(e){return e.split(/\r\n?|\n/)},na=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(t){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(i){}return!t||t.parentElement()!=e?!1:t.compareEndPoints("StartToEnd",t)!=0},oa=function(){var e=E("div");return"oncopy"in e?!0:(e.setAttribute("oncopy","return;"),typeof e.oncopy=="function")}(),Qi=null;function la(e){if(Qi!=null)return Qi;var t=He(e,E("span","x")),i=t.getBoundingClientRect(),r=dt(t,0,1).getBoundingClientRect();return Qi=Math.abs(i.left-r.left)>1}var Ji={},ur={};function aa(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ji[e]=t}function sa(e,t){ur[e]=t}function si(e){if(typeof e=="string"&&ur.hasOwnProperty(e))e=ur[e];else if(e&&typeof e.name=="string"&&ur.hasOwnProperty(e.name)){var t=ur[e.name];typeof t=="string"&&(t={name:t}),e=$e(t,e),e.name=t.name}else{if(typeof e=="string"&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return si("application/xml");if(typeof e=="string"&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return si("application/json")}return typeof e=="string"?{name:e}:e||{name:"null"}}function qi(e,t){t=si(t);var i=Ji[t.name];if(!i)return qi(e,"text/plain");var r=i(e,t);if(fr.hasOwnProperty(t.name)){var n=fr[t.name];for(var o in n)!n.hasOwnProperty(o)||(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=n[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)r[l]=t.modeProps[l];return r}var fr={};function ua(e,t){var i=fr.hasOwnProperty(e)?fr[e]:fr[e]={};K(t,i)}function Qt(e,t){if(t===!0)return t;if(e.copyState)return e.copyState(t);var i={};for(var r in t){var n=t[r];n instanceof Array&&(n=n.concat([])),i[r]=n}return i}function ji(e,t){for(var i;e.innerMode&&(i=e.innerMode(t),!(!i||i.mode==e));)t=i.state,e=i.mode;return i||{mode:e,state:t}}function eo(e,t,i){return e.startState?e.startState(t,i):!0}var me=function(e,t,i){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=i};me.prototype.eol=function(){return this.pos>=this.string.length},me.prototype.sol=function(){return this.pos==this.lineStart},me.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},me.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},me.prototype.eat=function(e){var t=this.string.charAt(this.pos),i;if(typeof e=="string"?i=t==e:i=t&&(e.test?e.test(t):e(t)),i)return++this.pos,t},me.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},me.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},me.prototype.skipToEnd=function(){this.pos=this.string.length},me.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},me.prototype.backUp=function(e){this.pos-=e},me.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=D(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?D(this.string,this.lineStart,this.tabSize):0)},me.prototype.indentation=function(){return D(this.string,null,this.tabSize)-(this.lineStart?D(this.string,this.lineStart,this.tabSize):0)},me.prototype.match=function(e,t,i){if(typeof e=="string"){var r=function(l){return i?l.toLowerCase():l},n=this.string.substr(this.pos,e.length);if(r(n)==r(e))return t!==!1&&(this.pos+=e.length),!0}else{var o=this.string.slice(this.pos).match(e);return o&&o.index>0?null:(o&&t!==!1&&(this.pos+=o[0].length),o)}},me.prototype.current=function(){return this.string.slice(this.start,this.pos)},me.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},me.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},me.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};function N(e,t){if(t-=e.first,t<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var i=e;!i.lines;)for(var r=0;;++r){var n=i.children[r],o=n.chunkSize();if(t<o){i=n;break}t-=o}return i.lines[t]}function Jt(e,t,i){var r=[],n=t.line;return e.iter(t.line,i.line+1,function(o){var l=o.text;n==i.line&&(l=l.slice(0,i.ch)),n==t.line&&(l=l.slice(t.ch)),r.push(l),++n}),r}function Vi(e,t,i){var r=[];return e.iter(t,i,function(n){r.push(n.text)}),r}function gt(e,t){var i=t-e.height;if(i)for(var r=e;r;r=r.parent)r.height+=i}function re(e){if(e.parent==null)return null;for(var t=e.parent,i=F(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var n=0;r.children[n]!=t;++n)i+=r.children[n].chunkSize();return i+t.first}function qt(e,t){var i=e.first;e:do{for(var r=0;r<e.children.length;++r){var n=e.children[r],o=n.height;if(t<o){e=n;continue e}t-=o,i+=n.chunkSize()}return i}while(!e.lines);for(var l=0;l<e.lines.length;++l){var a=e.lines[l],s=a.height;if(t<s)break;t-=s}return i+l}function Pr(e,t){return t>=e.first&&t<e.first+e.size}function $i(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function y(e,t,i){if(i===void 0&&(i=null),!(this instanceof y))return new y(e,t,i);this.line=e,this.ch=t,this.sticky=i}function B(e,t){return e.line-t.line||e.ch-t.ch}function en(e,t){return e.sticky==t.sticky&&B(e,t)==0}function tn(e){return y(e.line,e.ch)}function ui(e,t){return B(e,t)<0?t:e}function fi(e,t){return B(e,t)<0?e:t}function to(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function z(e,t){if(t.line<e.first)return y(e.first,0);var i=e.first+e.size-1;return t.line>i?y(i,N(e,i).text.length):fa(t,N(e,t.line).text.length)}function fa(e,t){var i=e.ch;return i==null||i>t?y(e.line,t):i<0?y(e.line,0):e}function ro(e,t){for(var i=[],r=0;r<t.length;r++)i[r]=z(e,t[r]);return i}var hi=function(e,t){this.state=e,this.lookAhead=t},yt=function(e,t,i,r){this.state=t,this.doc=e,this.line=i,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};yt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return t!=null&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},yt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},yt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},yt.fromSaved=function(e,t,i){return t instanceof hi?new yt(e,Qt(e.mode,t.state),i,t.lookAhead):new yt(e,Qt(e.mode,t),i)},yt.prototype.save=function(e){var t=e!==!1?Qt(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new hi(t,this.maxLookAhead):t};function io(e,t,i,r){var n=[e.state.modeGen],o={};uo(e,t.text,e.doc.mode,i,function(u,f){return n.push(u,f)},o,r);for(var l=i.state,a=function(u){i.baseTokens=n;var f=e.state.overlays[u],h=1,p=0;i.state=!0,uo(e,t.text,f.mode,i,function(d,v){for(var g=h;p<d;){var b=n[h];b>d&&n.splice(h,1,d,n[h+1],b),h+=2,p=Math.min(d,b)}if(!!v)if(f.opaque)n.splice(g,h-g,d,"overlay "+v),h=g+2;else for(;g<h;g+=2){var x=n[g+1];n[g+1]=(x?x+" ":"")+"overlay "+v}},o),i.state=l,i.baseTokens=null,i.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)a(s);return{styles:n,classes:o.bgClass||o.textClass?o:null}}function no(e,t,i){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=Hr(e,re(t)),n=t.text.length>e.options.maxHighlightLength&&Qt(e.doc.mode,r.state),o=io(e,t,r);n&&(r.state=n),t.stateAfter=r.save(!n),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),i===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function Hr(e,t,i){var r=e.doc,n=e.display;if(!r.mode.startState)return new yt(r,!0,t);var o=ha(e,t,i),l=o>r.first&&N(r,o-1).stateAfter,a=l?yt.fromSaved(r,l,o):new yt(r,eo(r.mode),o);return r.iter(o,t,function(s){rn(e,s.text,a);var u=a.line;s.stateAfter=u==t-1||u%5==0||u>=n.viewFrom&&u<n.viewTo?a.save():null,a.nextLine()}),i&&(r.modeFrontier=a.line),a}function rn(e,t,i,r){var n=e.doc.mode,o=new me(t,e.options.tabSize,i);for(o.start=o.pos=r||0,t==""&&oo(n,i.state);!o.eol();)nn(n,o,i.state),o.start=o.pos}function oo(e,t){if(e.blankLine)return e.blankLine(t);if(!!e.innerMode){var i=ji(e,t);if(i.mode.blankLine)return i.mode.blankLine(i.state)}}function nn(e,t,i,r){for(var n=0;n<10;n++){r&&(r[0]=ji(e,i).mode);var o=e.token(t,i);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}var lo=function(e,t,i){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=i};function ao(e,t,i,r){var n=e.doc,o=n.mode,l;t=z(n,t);var a=N(n,t.line),s=Hr(e,t.line,i),u=new me(a.text,e.options.tabSize,s),f;for(r&&(f=[]);(r||u.pos<t.ch)&&!u.eol();)u.start=u.pos,l=nn(o,u,s.state),r&&f.push(new lo(u,l,Qt(n.mode,s.state)));return r?f:new lo(u,l,s.state)}function so(e,t){if(e)for(;;){var i=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!i)break;e=e.slice(0,i.index)+e.slice(i.index+i[0].length);var r=i[1]?"bgClass":"textClass";t[r]==null?t[r]=i[2]:new RegExp("(?:^|\\s)"+i[2]+"(?:$|\\s)").test(t[r])||(t[r]+=" "+i[2])}return e}function uo(e,t,i,r,n,o,l){var a=i.flattenSpans;a==null&&(a=e.options.flattenSpans);var s=0,u=null,f=new me(t,e.options.tabSize,r),h,p=e.options.addModeClass&&[null];for(t==""&&so(oo(i,r.state),o);!f.eol();){if(f.pos>e.options.maxHighlightLength?(a=!1,l&&rn(e,t,r,f.pos),f.pos=t.length,h=null):h=so(nn(i,f,r.state,p),o),p){var d=p[0].name;d&&(h="m-"+(h?d+" "+h:d))}if(!a||u!=h){for(;s<f.start;)s=Math.min(f.start,s+5e3),n(s,u);u=h}f.start=f.pos}for(;s<f.pos;){var v=Math.min(f.pos,s+5e3);n(v,u),s=v}}function ha(e,t,i){for(var r,n,o=e.doc,l=i?-1:t-(e.doc.mode.innerMode?1e3:100),a=t;a>l;--a){if(a<=o.first)return o.first;var s=N(o,a-1),u=s.stateAfter;if(u&&(!i||a+(u instanceof hi?u.lookAhead:0)<=o.modeFrontier))return a;var f=D(s.text,null,e.options.tabSize);(n==null||r>f)&&(n=a-1,r=f)}return n}function ca(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var i=e.first,r=t-1;r>i;r--){var n=N(e,r).stateAfter;if(n&&(!(n instanceof hi)||r+n.lookAhead<t)){i=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,i)}}var fo=!1,kt=!1;function da(){fo=!0}function pa(){kt=!0}function ci(e,t,i){this.marker=e,this.from=t,this.to=i}function Ir(e,t){if(e)for(var i=0;i<e.length;++i){var r=e[i];if(r.marker==t)return r}}function va(e,t){for(var i,r=0;r<e.length;++r)e[r]!=t&&(i||(i=[])).push(e[r]);return i}function ga(e,t,i){var r=i&&window.WeakSet&&(i.markedSpans||(i.markedSpans=new WeakSet));r&&e.markedSpans&&r.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],r&&r.add(e.markedSpans)),t.marker.attachLine(e)}function ya(e,t,i){var r;if(e)for(var n=0;n<e.length;++n){var o=e[n],l=o.marker,a=o.from==null||(l.inclusiveLeft?o.from<=t:o.from<t);if(a||o.from==t&&l.type=="bookmark"&&(!i||!o.marker.insertLeft)){var s=o.to==null||(l.inclusiveRight?o.to>=t:o.to>t);(r||(r=[])).push(new ci(l,o.from,s?null:o.to))}}return r}function ma(e,t,i){var r;if(e)for(var n=0;n<e.length;++n){var o=e[n],l=o.marker,a=o.to==null||(l.inclusiveRight?o.to>=t:o.to>t);if(a||o.from==t&&l.type=="bookmark"&&(!i||o.marker.insertLeft)){var s=o.from==null||(l.inclusiveLeft?o.from<=t:o.from<t);(r||(r=[])).push(new ci(l,s?null:o.from-t,o.to==null?null:o.to-t))}}return r}function on(e,t){if(t.full)return null;var i=Pr(e,t.from.line)&&N(e,t.from.line).markedSpans,r=Pr(e,t.to.line)&&N(e,t.to.line).markedSpans;if(!i&&!r)return null;var n=t.from.ch,o=t.to.ch,l=B(t.from,t.to)==0,a=ya(i,n,l),s=ma(r,o,l),u=t.text.length==1,f=X(t.text).length+(u?n:0);if(a)for(var h=0;h<a.length;++h){var p=a[h];if(p.to==null){var d=Ir(s,p.marker);d?u&&(p.to=d.to==null?null:d.to+f):p.to=n}}if(s)for(var v=0;v<s.length;++v){var g=s[v];if(g.to!=null&&(g.to+=f),g.from==null){var b=Ir(a,g.marker);b||(g.from=f,u&&(a||(a=[])).push(g))}else g.from+=f,u&&(a||(a=[])).push(g)}a&&(a=ho(a)),s&&s!=a&&(s=ho(s));var x=[a];if(!u){var L=t.text.length-2,w;if(L>0&&a)for(var k=0;k<a.length;++k)a[k].to==null&&(w||(w=[])).push(new ci(a[k].marker,null,null));for(var A=0;A<L;++A)x.push(w);x.push(s)}return x}function ho(e){for(var t=0;t<e.length;++t){var i=e[t];i.from!=null&&i.from==i.to&&i.marker.clearWhenEmpty!==!1&&e.splice(t--,1)}return e.length?e:null}function Ca(e,t,i){var r=null;if(e.iter(t.line,i.line+1,function(d){if(d.markedSpans)for(var v=0;v<d.markedSpans.length;++v){var g=d.markedSpans[v].marker;g.readOnly&&(!r||F(r,g)==-1)&&(r||(r=[])).push(g)}}),!r)return null;for(var n=[{from:t,to:i}],o=0;o<r.length;++o)for(var l=r[o],a=l.find(0),s=0;s<n.length;++s){var u=n[s];if(!(B(u.to,a.from)<0||B(u.from,a.to)>0)){var f=[s,1],h=B(u.from,a.from),p=B(u.to,a.to);(h<0||!l.inclusiveLeft&&!h)&&f.push({from:u.from,to:a.from}),(p>0||!l.inclusiveRight&&!p)&&f.push({from:a.to,to:u.to}),n.splice.apply(n,f),s+=f.length-3}}return n}function co(e){var t=e.markedSpans;if(!!t){for(var i=0;i<t.length;++i)t[i].marker.detachLine(e);e.markedSpans=null}}function po(e,t){if(!!t){for(var i=0;i<t.length;++i)t[i].marker.attachLine(e);e.markedSpans=t}}function di(e){return e.inclusiveLeft?-1:0}function pi(e){return e.inclusiveRight?1:0}function ln(e,t){var i=e.lines.length-t.lines.length;if(i!=0)return i;var r=e.find(),n=t.find(),o=B(r.from,n.from)||di(e)-di(t);if(o)return-o;var l=B(r.to,n.to)||pi(e)-pi(t);return l||t.id-e.id}function vo(e,t){var i=kt&&e.markedSpans,r;if(i)for(var n=void 0,o=0;o<i.length;++o)n=i[o],n.marker.collapsed&&(t?n.from:n.to)==null&&(!r||ln(r,n.marker)<0)&&(r=n.marker);return r}function go(e){return vo(e,!0)}function vi(e){return vo(e,!1)}function ba(e,t){var i=kt&&e.markedSpans,r;if(i)for(var n=0;n<i.length;++n){var o=i[n];o.marker.collapsed&&(o.from==null||o.from<t)&&(o.to==null||o.to>t)&&(!r||ln(r,o.marker)<0)&&(r=o.marker)}return r}function yo(e,t,i,r,n){var o=N(e,t),l=kt&&o.markedSpans;if(l)for(var a=0;a<l.length;++a){var s=l[a];if(!!s.marker.collapsed){var u=s.marker.find(0),f=B(u.from,i)||di(s.marker)-di(n),h=B(u.to,r)||pi(s.marker)-pi(n);if(!(f>=0&&h<=0||f<=0&&h>=0)&&(f<=0&&(s.marker.inclusiveRight&&n.inclusiveLeft?B(u.to,i)>=0:B(u.to,i)>0)||f>=0&&(s.marker.inclusiveRight&&n.inclusiveLeft?B(u.from,r)<=0:B(u.from,r)<0)))return!0}}}function ut(e){for(var t;t=go(e);)e=t.find(-1,!0).line;return e}function xa(e){for(var t;t=vi(e);)e=t.find(1,!0).line;return e}function wa(e){for(var t,i;t=vi(e);)e=t.find(1,!0).line,(i||(i=[])).push(e);return i}function an(e,t){var i=N(e,t),r=ut(i);return i==r?t:re(r)}function mo(e,t){if(t>e.lastLine())return t;var i=N(e,t),r;if(!Pt(e,i))return t;for(;r=vi(i);)i=r.find(1,!0).line;return re(i)+1}function Pt(e,t){var i=kt&&t.markedSpans;if(i){for(var r=void 0,n=0;n<i.length;++n)if(r=i[n],!!r.marker.collapsed){if(r.from==null)return!0;if(!r.marker.widgetNode&&r.from==0&&r.marker.inclusiveLeft&&sn(e,t,r))return!0}}}function sn(e,t,i){if(i.to==null){var r=i.marker.find(1,!0);return sn(e,r.line,Ir(r.line.markedSpans,i.marker))}if(i.marker.inclusiveRight&&i.to==t.text.length)return!0;for(var n=void 0,o=0;o<t.markedSpans.length;++o)if(n=t.markedSpans[o],n.marker.collapsed&&!n.marker.widgetNode&&n.from==i.to&&(n.to==null||n.to!=i.from)&&(n.marker.inclusiveLeft||i.marker.inclusiveRight)&&sn(e,t,n))return!0}function Tt(e){e=ut(e);for(var t=0,i=e.parent,r=0;r<i.lines.length;++r){var n=i.lines[r];if(n==e)break;t+=n.height}for(var o=i.parent;o;i=o,o=i.parent)for(var l=0;l<o.children.length;++l){var a=o.children[l];if(a==i)break;t+=a.height}return t}function gi(e){if(e.height==0)return 0;for(var t=e.text.length,i,r=e;i=go(r);){var n=i.find(0,!0);r=n.from.line,t+=n.from.ch-n.to.ch}for(r=e;i=vi(r);){var o=i.find(0,!0);t-=r.text.length-o.from.ch,r=o.to.line,t+=r.text.length-o.to.ch}return t}function un(e){var t=e.display,i=e.doc;t.maxLine=N(i,i.first),t.maxLineLength=gi(t.maxLine),t.maxLineChanged=!0,i.iter(function(r){var n=gi(r);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=r)})}var hr=function(e,t,i){this.text=e,po(this,t),this.height=i?i(this):1};hr.prototype.lineNo=function(){return re(this)},sr(hr);function Sa(e,t,i,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),e.order!=null&&(e.order=null),co(e),po(e,i);var n=r?r(e):1;n!=e.height&&gt(e,n)}function La(e){e.parent=null,co(e)}var ka={},Ta={};function Co(e,t){if(!e||/^\s*$/.test(e))return null;var i=t.addModeClass?Ta:ka;return i[e]||(i[e]=e.replace(/\S+/g,"cm-$&"))}function bo(e,t){var i=At("span",null,null,J?"padding-right: .1px":null),r={pre:At("pre",[i],"CodeMirror-line"),content:i,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var n=0;n<=(t.rest?t.rest.length:0);n++){var o=n?t.rest[n-1]:t.line,l=void 0;r.pos=0,r.addToken=Da,ia(e.display.measure)&&(l=Lt(o,e.doc.direction))&&(r.addToken=Oa(r.addToken,l)),r.map=[];var a=t!=e.display.externalMeasured&&re(o);Aa(o,r,no(e,o,a)),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=Wr(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=Wr(o.styleClasses.textClass,r.textClass||""))),r.map.length==0&&r.map.push(0,0,r.content.appendChild(ra(e.display.measure))),n==0?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(J){var s=r.content.lastChild;(/\bcm-tab\b/.test(s.className)||s.querySelector&&s.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return ye(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=Wr(r.pre.className,r.textClass||"")),r}function Ma(e){var t=E("span","\u2022","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Da(e,t,i,r,n,o,l){if(!!t){var a=e.splitSpaces?Na(t,e.trailingSpace):t,s=e.cm.state.specialChars,u=!1,f;if(!s.test(t))e.col+=t.length,f=document.createTextNode(a),e.map.push(e.pos,e.pos+t.length,f),P&&I<9&&(u=!0),e.pos+=t.length;else{f=document.createDocumentFragment();for(var h=0;;){s.lastIndex=h;var p=s.exec(t),d=p?p.index-h:t.length-h;if(d){var v=document.createTextNode(a.slice(h,h+d));P&&I<9?f.appendChild(E("span",[v])):f.appendChild(v),e.map.push(e.pos,e.pos+d,v),e.col+=d,e.pos+=d}if(!p)break;h+=d+1;var g=void 0;if(p[0]=="	"){var b=e.cm.options.tabSize,x=b-e.col%b;g=f.appendChild(E("span",vt(x),"cm-tab")),g.setAttribute("role","presentation"),g.setAttribute("cm-text","	"),e.col+=x}else p[0]=="\r"||p[0]==`
`?(g=f.appendChild(E("span",p[0]=="\r"?"\u240D":"\u2424","cm-invalidchar")),g.setAttribute("cm-text",p[0]),e.col+=1):(g=e.cm.options.specialCharPlaceholder(p[0]),g.setAttribute("cm-text",p[0]),P&&I<9?f.appendChild(E("span",[g])):f.appendChild(g),e.col+=1);e.map.push(e.pos,e.pos+1,g),e.pos++}}if(e.trailingSpace=a.charCodeAt(t.length-1)==32,i||r||n||u||o||l){var L=i||"";r&&(L+=r),n&&(L+=n);var w=E("span",[f],L,o);if(l)for(var k in l)l.hasOwnProperty(k)&&k!="style"&&k!="class"&&w.setAttribute(k,l[k]);return e.content.appendChild(w)}e.content.appendChild(f)}}function Na(e,t){if(e.length>1&&!/  /.test(e))return e;for(var i=t,r="",n=0;n<e.length;n++){var o=e.charAt(n);o==" "&&i&&(n==e.length-1||e.charCodeAt(n+1)==32)&&(o="\xA0"),r+=o,i=o==" "}return r}function Oa(e,t){return function(i,r,n,o,l,a,s){n=n?n+" cm-force-border":"cm-force-border";for(var u=i.pos,f=u+r.length;;){for(var h=void 0,p=0;p<t.length&&(h=t[p],!(h.to>u&&h.from<=u));p++);if(h.to>=f)return e(i,r,n,o,l,a,s);e(i,r.slice(0,h.to-u),n,o,null,a,s),o=null,r=r.slice(h.to-u),u=h.to}}}function xo(e,t,i,r){var n=!r&&i.widgetNode;n&&e.map.push(e.pos,e.pos+t,n),!r&&e.cm.display.input.needsContentAttribute&&(n||(n=e.content.appendChild(document.createElement("span"))),n.setAttribute("cm-marker",i.id)),n&&(e.cm.display.input.setUneditable(n),e.content.appendChild(n)),e.pos+=t,e.trailingSpace=!1}function Aa(e,t,i){var r=e.markedSpans,n=e.text,o=0;if(!r){for(var l=1;l<i.length;l+=2)t.addToken(t,n.slice(o,o=i[l]),Co(i[l+1],t.cm.options));return}for(var a=n.length,s=0,u=1,f="",h,p,d=0,v,g,b,x,L;;){if(d==s){v=g=b=p="",L=null,x=null,d=Infinity;for(var w=[],k=void 0,A=0;A<r.length;++A){var O=r[A],_=O.marker;if(_.type=="bookmark"&&O.from==s&&_.widgetNode)w.push(_);else if(O.from<=s&&(O.to==null||O.to>s||_.collapsed&&O.to==s&&O.from==s)){if(O.to!=null&&O.to!=s&&d>O.to&&(d=O.to,g=""),_.className&&(v+=" "+_.className),_.css&&(p=(p?p+";":"")+_.css),_.startStyle&&O.from==s&&(b+=" "+_.startStyle),_.endStyle&&O.to==d&&(k||(k=[])).push(_.endStyle,O.to),_.title&&((L||(L={})).title=_.title),_.attributes)for(var le in _.attributes)(L||(L={}))[le]=_.attributes[le];_.collapsed&&(!x||ln(x.marker,_)<0)&&(x=O)}else O.from>s&&d>O.from&&(d=O.from)}if(k)for(var Me=0;Me<k.length;Me+=2)k[Me+1]==d&&(g+=" "+k[Me]);if(!x||x.from==s)for(var ce=0;ce<w.length;++ce)xo(t,0,w[ce]);if(x&&(x.from||0)==s){if(xo(t,(x.to==null?a+1:x.to)-s,x.marker,x.from==null),x.to==null)return;x.to==s&&(x=!1)}}if(s>=a)break;for(var Ve=Math.min(a,d);;){if(f){var Ge=s+f.length;if(!x){var Ce=Ge>Ve?f.slice(0,Ve-s):f;t.addToken(t,Ce,h?h+v:v,b,s+Ce.length==d?g:"",p,L)}if(Ge>=Ve){f=f.slice(Ve-s),s=Ve;break}s=Ge,b=""}f=n.slice(o,o=i[u++]),h=Co(i[u++],t.cm.options)}}}function wo(e,t,i){this.line=t,this.rest=wa(t),this.size=this.rest?re(X(this.rest))-i+1:1,this.node=this.text=null,this.hidden=Pt(e,t)}function yi(e,t,i){for(var r=[],n,o=t;o<i;o=n){var l=new wo(e.doc,N(e.doc,o),o);n=o+l.size,r.push(l)}return r}var cr=null;function Wa(e){cr?cr.ops.push(e):e.ownsGroup=cr={ops:[e],delayedCallbacks:[]}}function Ea(e){var t=e.delayedCallbacks,i=0;do{for(;i<t.length;i++)t[i].call(null);for(var r=0;r<e.ops.length;r++){var n=e.ops[r];if(n.cursorActivityHandlers)for(;n.cursorActivityCalled<n.cursorActivityHandlers.length;)n.cursorActivityHandlers[n.cursorActivityCalled++].call(null,n.cm)}}while(i<t.length)}function Pa(e,t){var i=e.ownsGroup;if(!!i)try{Ea(i)}finally{cr=null,t(i)}}var Fr=null;function Le(e,t){var i=Ki(e,t);if(!!i.length){var r=Array.prototype.slice.call(arguments,2),n;cr?n=cr.delayedCallbacks:Fr?n=Fr:(n=Fr=[],setTimeout(Ha,0));for(var o=function(a){n.push(function(){return i[a].apply(null,r)})},l=0;l<i.length;++l)o(l)}}function Ha(){var e=Fr;Fr=null;for(var t=0;t<e.length;++t)e[t]()}function So(e,t,i,r){for(var n=0;n<t.changes.length;n++){var o=t.changes[n];o=="text"?Fa(e,t):o=="gutter"?ko(e,t,i,r):o=="class"?fn(e,t):o=="widget"&&Ra(e,t,r)}t.changes=null}function Rr(e){return e.node==e.text&&(e.node=E("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),P&&I<8&&(e.node.style.zIndex=2)),e.node}function Ia(e,t){var i=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(i&&(i+=" CodeMirror-linebackground"),t.background)i?t.background.className=i:(t.background.parentNode.removeChild(t.background),t.background=null);else if(i){var r=Rr(t);t.background=r.insertBefore(E("div",null,i),r.firstChild),e.display.input.setUneditable(t.background)}}function Lo(e,t){var i=e.display.externalMeasured;return i&&i.line==t.line?(e.display.externalMeasured=null,t.measure=i.measure,i.built):bo(e,t)}function Fa(e,t){var i=t.text.className,r=Lo(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,fn(e,t)):i&&(t.text.className=i)}function fn(e,t){Ia(e,t),t.line.wrapClass?Rr(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var i=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=i||""}function ko(e,t,i,r){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var n=Rr(t);t.gutterBackground=E("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),n.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=Rr(t),a=t.gutter=E("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(a.setAttribute("aria-hidden","true"),e.display.input.setUneditable(a),l.insertBefore(a,t.text),t.line.gutterClass&&(a.className+=" "+t.line.gutterClass),e.options.lineNumbers&&(!o||!o["CodeMirror-linenumbers"])&&(t.lineNumber=a.appendChild(E("div",$i(e.options,i),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.display.gutterSpecs.length;++s){var u=e.display.gutterSpecs[s].className,f=o.hasOwnProperty(u)&&o[u];f&&a.appendChild(E("div",[f],"CodeMirror-gutter-elt","left: "+r.gutterLeft[u]+"px; width: "+r.gutterWidth[u]+"px"))}}}function Ra(e,t,i){t.alignable&&(t.alignable=null);for(var r=xt("CodeMirror-linewidget"),n=t.node.firstChild,o=void 0;n;n=o)o=n.nextSibling,r.test(n.className)&&t.node.removeChild(n);To(e,t,i)}function Ba(e,t,i,r){var n=Lo(e,t);return t.text=t.node=n.pre,n.bgClass&&(t.bgClass=n.bgClass),n.textClass&&(t.textClass=n.textClass),fn(e,t),ko(e,t,i,r),To(e,t,r),t.node}function To(e,t,i){if(Mo(e,t.line,t,i,!0),t.rest)for(var r=0;r<t.rest.length;r++)Mo(e,t.rest[r],t,i,!1)}function Mo(e,t,i,r,n){if(!!t.widgets)for(var o=Rr(i),l=0,a=t.widgets;l<a.length;++l){var s=a[l],u=E("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),za(s,u,i,r),e.display.input.setUneditable(u),n&&s.above?o.insertBefore(u,i.gutter||i.text):o.appendChild(u),Le(s,"redraw")}}function za(e,t,i,r){if(e.noHScroll){(i.alignable||(i.alignable=[])).push(t);var n=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(n-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=n+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function Br(e){if(e.height!=null)return e.height;var t=e.doc.cm;if(!t)return 0;if(!pt(document.body,e.node)){var i="position: relative;";e.coverGutter&&(i+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(i+="width: "+t.display.wrapper.clientWidth+"px;"),He(t.display.measure,E("div",[e.node],null,i))}return e.height=e.node.parentNode.offsetHeight}function Mt(e,t){for(var i=Gi(t);i!=e.wrapper;i=i.parentNode)if(!i||i.nodeType==1&&i.getAttribute("cm-ignore-events")=="true"||i.parentNode==e.sizer&&i!=e.mover)return!0}function mi(e){return e.lineSpace.offsetTop}function hn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Do(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=He(e.measure,E("pre","x","CodeMirror-line-like")),i=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(i.paddingLeft),right:parseInt(i.paddingRight)};return!isNaN(r.left)&&!isNaN(r.right)&&(e.cachedPaddingH=r),r}function mt(e){return q-e.display.nativeBarWidth}function jt(e){return e.display.scroller.clientWidth-mt(e)-e.display.barWidth}function cn(e){return e.display.scroller.clientHeight-mt(e)-e.display.barHeight}function Ua(e,t,i){var r=e.options.lineWrapping,n=r&&jt(e);if(!t.measure.heights||r&&t.measure.width!=n){var o=t.measure.heights=[];if(r){t.measure.width=n;for(var l=t.text.firstChild.getClientRects(),a=0;a<l.length-1;a++){var s=l[a],u=l[a+1];Math.abs(s.bottom-u.bottom)>2&&o.push((s.bottom+u.top)/2-i.top)}}o.push(i.bottom-i.top)}}function No(e,t,i){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var n=0;n<e.rest.length;n++)if(re(e.rest[n])>i)return{map:e.measure.maps[n],cache:e.measure.caches[n],before:!0}}}function Ka(e,t){t=ut(t);var i=re(t),r=e.display.externalMeasured=new wo(e.doc,t,i);r.lineN=i;var n=r.built=bo(e,r);return r.text=n.pre,He(e.display.lineMeasure,n.pre),r}function Oo(e,t,i,r){return Ct(e,dr(e,t),i,r)}function dn(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[er(e,t)];var i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size)return i}function dr(e,t){var i=re(t),r=dn(e,i);r&&!r.text?r=null:r&&r.changes&&(So(e,r,i,mn(e)),e.curOp.forceUpdate=!0),r||(r=Ka(e,t));var n=No(r,t,i);return{line:t,view:r,rect:null,map:n.map,cache:n.cache,before:n.before,hasHeights:!1}}function Ct(e,t,i,r,n){t.before&&(i=-1);var o=i+(r||""),l;return t.cache.hasOwnProperty(o)?l=t.cache[o]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(Ua(e,t.view,t.rect),t.hasHeights=!0),l=Ga(e,t,i,r),l.bogus||(t.cache[o]=l)),{left:l.left,right:l.right,top:n?l.rtop:l.top,bottom:n?l.rbottom:l.bottom}}var Ao={left:0,right:0,top:0,bottom:0};function Wo(e,t,i){for(var r,n,o,l,a,s,u=0;u<e.length;u+=3)if(a=e[u],s=e[u+1],t<a?(n=0,o=1,l="left"):t<s?(n=t-a,o=n+1):(u==e.length-3||t==s&&e[u+3]>t)&&(o=s-a,n=o-1,t>=s&&(l="right")),n!=null){if(r=e[u+2],a==s&&i==(r.insertLeft?"left":"right")&&(l=i),i=="left"&&n==0)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)r=e[(u-=3)+2],l="left";if(i=="right"&&n==s-a)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)r=e[(u+=3)+2],l="right";break}return{node:r,start:n,end:o,collapse:l,coverStart:a,coverEnd:s}}function _a(e,t){var i=Ao;if(t=="left")for(var r=0;r<e.length&&(i=e[r]).left==i.right;r++);else for(var n=e.length-1;n>=0&&(i=e[n]).left==i.right;n--);return i}function Ga(e,t,i,r){var n=Wo(t.map,i,r),o=n.node,l=n.start,a=n.end,s=n.collapse,u;if(o.nodeType==3){for(var f=0;f<4;f++){for(;l&&st(t.line.text.charAt(n.coverStart+l));)--l;for(;n.coverStart+a<n.coverEnd&&st(t.line.text.charAt(n.coverStart+a));)++a;if(P&&I<9&&l==0&&a==n.coverEnd-n.coverStart?u=o.parentNode.getBoundingClientRect():u=_a(dt(o,l,a).getClientRects(),r),u.left||u.right||l==0)break;a=l,l=l-1,s="right"}P&&I<11&&(u=Za(e.display.measure,u))}else{l>0&&(s=r="right");var h;e.options.lineWrapping&&(h=o.getClientRects()).length>1?u=h[r=="right"?h.length-1:0]:u=o.getBoundingClientRect()}if(P&&I<9&&!l&&(!u||!u.left&&!u.right)){var p=o.parentNode.getClientRects()[0];p?u={left:p.left,right:p.left+vr(e.display),top:p.top,bottom:p.bottom}:u=Ao}for(var d=u.top-t.rect.top,v=u.bottom-t.rect.top,g=(d+v)/2,b=t.view.measure.heights,x=0;x<b.length-1&&!(g<b[x]);x++);var L=x?b[x-1]:0,w=b[x],k={left:(s=="right"?u.right:u.left)-t.rect.left,right:(s=="left"?u.left:u.right)-t.rect.left,top:L,bottom:w};return!u.left&&!u.right&&(k.bogus=!0),e.options.singleCursorHeightPerLine||(k.rtop=d,k.rbottom=v),k}function Za(e,t){if(!window.screen||screen.logicalXDPI==null||screen.logicalXDPI==screen.deviceXDPI||!la(e))return t;var i=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*i,right:t.right*i,top:t.top*r,bottom:t.bottom*r}}function Eo(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Po(e){e.display.externalMeasure=null,lt(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Eo(e.display.view[t])}function zr(e){Po(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Ho(e){return te&&T?-(e.body.getBoundingClientRect().left-parseInt(getComputedStyle(e.body).marginLeft)):e.defaultView.pageXOffset||(e.documentElement||e.body).scrollLeft}function Io(e){return te&&T?-(e.body.getBoundingClientRect().top-parseInt(getComputedStyle(e.body).marginTop)):e.defaultView.pageYOffset||(e.documentElement||e.body).scrollTop}function pn(e){var t=ut(e),i=t.widgets,r=0;if(i)for(var n=0;n<i.length;++n)i[n].above&&(r+=Br(i[n]));return r}function Ci(e,t,i,r,n){if(!n){var o=pn(t);i.top+=o,i.bottom+=o}if(r=="line")return i;r||(r="local");var l=Tt(t);if(r=="local"?l+=mi(e.display):l-=e.display.viewOffset,r=="page"||r=="window"){var a=e.display.lineSpace.getBoundingClientRect();l+=a.top+(r=="window"?0:Io(Gt(e)));var s=a.left+(r=="window"?0:Ho(Gt(e)));i.left+=s,i.right+=s}return i.top+=l,i.bottom+=l,i}function Fo(e,t,i){if(i=="div")return t;var r=t.left,n=t.top;if(i=="page")r-=Ho(Gt(e)),n-=Io(Gt(e));else if(i=="local"||!i){var o=e.display.sizer.getBoundingClientRect();r+=o.left,n+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:r-l.left,top:n-l.top}}function bi(e,t,i,r,n){return r||(r=N(e.doc,t.line)),Ci(e,r,Oo(e,r,t.ch,n),i)}function ft(e,t,i,r,n,o){r=r||N(e.doc,t.line),n||(n=dr(e,r));function l(v,g){var b=Ct(e,n,v,g?"right":"left",o);return g?b.left=b.right:b.right=b.left,Ci(e,r,b,i)}var a=Lt(r,e.doc.direction),s=t.ch,u=t.sticky;if(s>=r.text.length?(s=r.text.length,u="before"):s<=0&&(s=0,u="after"),!a)return l(u=="before"?s-1:s,u=="before");function f(v,g,b){var x=a[g],L=x.level==1;return l(b?v-1:v,L!=b)}var h=Yt(a,s,u),p=Xt,d=f(s,h,u=="before");return p!=null&&(d.other=f(s,p,u!="before")),d}function Ro(e,t){var i=0;t=z(e.doc,t),e.options.lineWrapping||(i=vr(e.display)*t.ch);var r=N(e.doc,t.line),n=Tt(r)+mi(e.display);return{left:i,right:i,top:n,bottom:n+r.height}}function vn(e,t,i,r,n){var o=y(e,t,i);return o.xRel=n,r&&(o.outside=r),o}function gn(e,t,i){var r=e.doc;if(i+=e.display.viewOffset,i<0)return vn(r.first,0,null,-1,-1);var n=qt(r,i),o=r.first+r.size-1;if(n>o)return vn(r.first+r.size-1,N(r,o).text.length,null,1,1);t<0&&(t=0);for(var l=N(r,n);;){var a=Xa(e,l,n,t,i),s=ba(l,a.ch+(a.xRel>0||a.outside>0?1:0));if(!s)return a;var u=s.find(1);if(u.line==n)return u;l=N(r,n=u.line)}}function Bo(e,t,i,r){r-=pn(t);var n=t.text.length,o=qe(function(l){return Ct(e,i,l-1).bottom<=r},n,0);return n=qe(function(l){return Ct(e,i,l).top>r},o,n),{begin:o,end:n}}function zo(e,t,i,r){i||(i=dr(e,t));var n=Ci(e,t,Ct(e,i,r),"line").top;return Bo(e,t,i,n)}function yn(e,t,i,r){return e.bottom<=i?!1:e.top>i?!0:(r?e.left:e.right)>t}function Xa(e,t,i,r,n){n-=Tt(t);var o=dr(e,t),l=pn(t),a=0,s=t.text.length,u=!0,f=Lt(t,e.doc.direction);if(f){var h=(e.options.lineWrapping?Qa:Ya)(e,t,i,o,f,r,n);u=h.level!=1,a=u?h.from:h.to-1,s=u?h.to:h.from-1}var p=null,d=null,v=qe(function(A){var O=Ct(e,o,A);return O.top+=l,O.bottom+=l,yn(O,r,n,!1)?(O.top<=n&&O.left<=r&&(p=A,d=O),!0):!1},a,s),g,b,x=!1;if(d){var L=r-d.left<d.right-r,w=L==u;v=p+(w?0:1),b=w?"after":"before",g=L?d.left:d.right}else{!u&&(v==s||v==a)&&v++,b=v==0?"after":v==t.text.length?"before":Ct(e,o,v-(u?1:0)).bottom+l<=n==u?"after":"before";var k=ft(e,y(i,v,b),"line",t,o);g=k.left,x=n<k.top?-1:n>=k.bottom?1:0}return v=Je(t.text,v,1),vn(i,v,b,x,r-g)}function Ya(e,t,i,r,n,o,l){var a=qe(function(h){var p=n[h],d=p.level!=1;return yn(ft(e,y(i,d?p.to:p.from,d?"before":"after"),"line",t,r),o,l,!0)},0,n.length-1),s=n[a];if(a>0){var u=s.level!=1,f=ft(e,y(i,u?s.from:s.to,u?"after":"before"),"line",t,r);yn(f,o,l,!0)&&f.top>l&&(s=n[a-1])}return s}function Qa(e,t,i,r,n,o,l){var a=Bo(e,t,r,l),s=a.begin,u=a.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var f=null,h=null,p=0;p<n.length;p++){var d=n[p];if(!(d.from>=u||d.to<=s)){var v=d.level!=1,g=Ct(e,r,v?Math.min(u,d.to)-1:Math.max(s,d.from)).right,b=g<o?o-g+1e9:g-o;(!f||h>b)&&(f=d,h=b)}}return f||(f=n[n.length-1]),f.from<s&&(f={from:s,to:f.to,level:f.level}),f.to>u&&(f={from:f.from,to:u,level:f.level}),f}var Vt;function pr(e){if(e.cachedTextHeight!=null)return e.cachedTextHeight;if(Vt==null){Vt=E("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)Vt.appendChild(document.createTextNode("x")),Vt.appendChild(E("br"));Vt.appendChild(document.createTextNode("x"))}He(e.measure,Vt);var i=Vt.offsetHeight/50;return i>3&&(e.cachedTextHeight=i),lt(e.measure),i||1}function vr(e){if(e.cachedCharWidth!=null)return e.cachedCharWidth;var t=E("span","xxxxxxxxxx"),i=E("pre",[t],"CodeMirror-line-like");He(e.measure,i);var r=t.getBoundingClientRect(),n=(r.right-r.left)/10;return n>2&&(e.cachedCharWidth=n),n||10}function mn(e){for(var t=e.display,i={},r={},n=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var a=e.display.gutterSpecs[l].className;i[a]=o.offsetLeft+o.clientLeft+n,r[a]=o.clientWidth}return{fixedPos:Cn(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:i,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function Cn(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Uo(e){var t=pr(e.display),i=e.options.lineWrapping,r=i&&Math.max(5,e.display.scroller.clientWidth/vr(e.display)-3);return function(n){if(Pt(e.doc,n))return 0;var o=0;if(n.widgets)for(var l=0;l<n.widgets.length;l++)n.widgets[l].height&&(o+=n.widgets[l].height);return i?o+(Math.ceil(n.text.length/r)||1)*t:o+t}}function bn(e){var t=e.doc,i=Uo(e);t.iter(function(r){var n=i(r);n!=r.height&&gt(r,n)})}function $t(e,t,i,r){var n=e.display;if(!i&&Gi(t).getAttribute("cm-not-content")=="true")return null;var o,l,a=n.lineSpace.getBoundingClientRect();try{o=t.clientX-a.left,l=t.clientY-a.top}catch(h){return null}var s=gn(e,o,l),u;if(r&&s.xRel>0&&(u=N(e.doc,s.line).text).length==s.ch){var f=D(u,u.length,e.options.tabSize)-u.length;s=y(s.line,Math.max(0,Math.round((o-Do(e.display).left)/vr(e.display))-f))}return s}function er(e,t){if(t>=e.display.viewTo||(t-=e.display.viewFrom,t<0))return null;for(var i=e.display.view,r=0;r<i.length;r++)if(t-=i[r].size,t<0)return r}function Ke(e,t,i,r){t==null&&(t=e.doc.first),i==null&&(i=e.doc.first+e.doc.size),r||(r=0);var n=e.display;if(r&&i<n.viewTo&&(n.updateLineNumbers==null||n.updateLineNumbers>t)&&(n.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=n.viewTo)kt&&an(e.doc,t)<n.viewTo&&It(e);else if(i<=n.viewFrom)kt&&mo(e.doc,i+r)>n.viewFrom?It(e):(n.viewFrom+=r,n.viewTo+=r);else if(t<=n.viewFrom&&i>=n.viewTo)It(e);else if(t<=n.viewFrom){var o=xi(e,i,i+r,1);o?(n.view=n.view.slice(o.index),n.viewFrom=o.lineN,n.viewTo+=r):It(e)}else if(i>=n.viewTo){var l=xi(e,t,t,-1);l?(n.view=n.view.slice(0,l.index),n.viewTo=l.lineN):It(e)}else{var a=xi(e,t,t,-1),s=xi(e,i,i+r,1);a&&s?(n.view=n.view.slice(0,a.index).concat(yi(e,a.lineN,s.lineN)).concat(n.view.slice(s.index)),n.viewTo+=r):It(e)}var u=n.externalMeasured;u&&(i<u.lineN?u.lineN+=r:t<u.lineN+u.size&&(n.externalMeasured=null))}function Ht(e,t,i){e.curOp.viewChanged=!0;var r=e.display,n=e.display.externalMeasured;if(n&&t>=n.lineN&&t<n.lineN+n.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var o=r.view[er(e,t)];if(o.node!=null){var l=o.changes||(o.changes=[]);F(l,i)==-1&&l.push(i)}}}function It(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function xi(e,t,i,r){var n=er(e,t),o,l=e.display.view;if(!kt||i==e.doc.first+e.doc.size)return{index:n,lineN:i};for(var a=e.display.viewFrom,s=0;s<n;s++)a+=l[s].size;if(a!=t){if(r>0){if(n==l.length-1)return null;o=a+l[n].size-t,n++}else o=a-t;t+=o,i+=o}for(;an(e.doc,i)!=i;){if(n==(r<0?0:l.length-1))return null;i+=r*l[n-(r<0?1:0)].size,n+=r}return{index:n,lineN:i}}function Ja(e,t,i){var r=e.display,n=r.view;n.length==0||t>=r.viewTo||i<=r.viewFrom?(r.view=yi(e,t,i),r.viewFrom=t):(r.viewFrom>t?r.view=yi(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(er(e,t))),r.viewFrom=t,r.viewTo<i?r.view=r.view.concat(yi(e,r.viewTo,i)):r.viewTo>i&&(r.view=r.view.slice(0,er(e,i)))),r.viewTo=i}function Ko(e){for(var t=e.display.view,i=0,r=0;r<t.length;r++){var n=t[r];!n.hidden&&(!n.node||n.changes)&&++i}return i}function Ur(e){e.display.input.showSelection(e.display.input.prepareSelection())}function _o(e,t){t===void 0&&(t=!0);var i=e.doc,r={},n=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),l=e.options.$customCursor;l&&(t=!0);for(var a=0;a<i.sel.ranges.length;a++)if(!(!t&&a==i.sel.primIndex)){var s=i.sel.ranges[a];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var u=s.empty();if(l){var f=l(e,s);f&&xn(e,f,n)}else(u||e.options.showCursorWhenSelecting)&&xn(e,s.head,n);u||qa(e,s,o)}}return r}function xn(e,t,i){var r=ft(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),n=i.appendChild(E("div","\xA0","CodeMirror-cursor"));if(n.style.left=r.left+"px",n.style.top=r.top+"px",n.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=bi(e,t,"div",null,null),l=o.right-o.left;n.style.width=(l>0?l:e.defaultCharWidth())+"px"}if(r.other){var a=i.appendChild(E("div","\xA0","CodeMirror-cursor CodeMirror-secondarycursor"));a.style.display="",a.style.left=r.other.left+"px",a.style.top=r.other.top+"px",a.style.height=(r.other.bottom-r.other.top)*.85+"px"}}function wi(e,t){return e.top-t.top||e.left-t.left}function qa(e,t,i){var r=e.display,n=e.doc,o=document.createDocumentFragment(),l=Do(e.display),a=l.left,s=Math.max(r.sizerWidth,jt(e)-r.sizer.offsetLeft)-l.right,u=n.direction=="ltr";function f(w,k,A,O){k<0&&(k=0),k=Math.round(k),O=Math.round(O),o.appendChild(E("div",null,"CodeMirror-selected","position: absolute; left: "+w+`px;
                             top: `+k+"px; width: "+(A==null?s-w:A)+`px;
                             height: `+(O-k)+"px"))}function h(w,k,A){var O=N(n,w),_=O.text.length,le,Me;function ce(Ce,Ze){return bi(e,y(w,Ce),"div",O,Ze)}function Ve(Ce,Ze,Oe){var xe=zo(e,O,null,Ce),be=Ze=="ltr"==(Oe=="after")?"left":"right",de=Oe=="after"?xe.begin:xe.end-(/\s/.test(O.text.charAt(xe.end-1))?2:1);return ce(de,be)[be]}var Ge=Lt(O,n.direction);return ar(Ge,k||0,A==null?_:A,function(Ce,Ze,Oe,xe){var be=Oe=="ltr",de=ce(Ce,be?"left":"right"),Xe=ce(Ze-1,be?"right":"left"),Dr=k==null&&Ce==0,Kt=A==null&&Ze==_,Pe=xe==0,bt=!Ge||xe==Ge.length-1;if(Xe.top-de.top<=3){var De=(u?Dr:Kt)&&Pe,Qn=(u?Kt:Dr)&&bt,Ot=De?a:(be?de:Xe).left,or=Qn?s:(be?Xe:de).right;f(Ot,de.top,or-Ot,de.bottom)}else{var lr,Re,Nr,Jn;be?(lr=u&&Dr&&Pe?a:de.left,Re=u?s:Ve(Ce,Oe,"before"),Nr=u?a:Ve(Ze,Oe,"after"),Jn=u&&Kt&&bt?s:Xe.right):(lr=u?Ve(Ce,Oe,"before"):a,Re=!u&&Dr&&Pe?s:de.right,Nr=!u&&Kt&&bt?a:Xe.left,Jn=u?Ve(Ze,Oe,"after"):s),f(lr,de.top,Re-lr,de.bottom),de.bottom<Xe.top&&f(a,de.bottom,null,Xe.top),f(Nr,Xe.top,Jn-Nr,Xe.bottom)}(!le||wi(de,le)<0)&&(le=de),wi(Xe,le)<0&&(le=Xe),(!Me||wi(de,Me)<0)&&(Me=de),wi(Xe,Me)<0&&(Me=Xe)}),{start:le,end:Me}}var p=t.from(),d=t.to();if(p.line==d.line)h(p.line,p.ch,d.ch);else{var v=N(n,p.line),g=N(n,d.line),b=ut(v)==ut(g),x=h(p.line,p.ch,b?v.text.length+1:null).end,L=h(d.line,b?0:null,d.ch).start;b&&(x.top<L.top-2?(f(x.right,x.top,null,x.bottom),f(a,L.top,L.left,L.bottom)):f(x.right,x.top,L.left-x.right,x.bottom)),x.bottom<L.top&&f(a,x.bottom,null,L.top)}i.appendChild(o)}function wn(e){if(!!e.state.focused){var t=e.display;clearInterval(t.blinker);var i=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){e.hasFocus()||gr(e),t.cursorDiv.style.visibility=(i=!i)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function Go(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||Ln(e))}function Sn(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&gr(e))},100)}function Ln(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),e.options.readOnly!="nocursor"&&(e.state.focused||(ye(e,"focus",e,t),e.state.focused=!0,wt(e.display.wrapper,"CodeMirror-focused"),!e.curOp&&e.display.selForContextMenu!=e.doc.sel&&(e.display.input.reset(),J&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),wn(e))}function gr(e,t){e.state.delayingBlurEvent||(e.state.focused&&(ye(e,"blur",e,t),e.state.focused=!1,ot(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function Si(e){for(var t=e.display,i=t.lineDiv.offsetTop,r=Math.max(0,t.scroller.getBoundingClientRect().top),n=t.lineDiv.getBoundingClientRect().top,o=0,l=0;l<t.view.length;l++){var a=t.view[l],s=e.options.lineWrapping,u=void 0,f=0;if(!a.hidden){if(n+=a.line.height,P&&I<8){var h=a.node.offsetTop+a.node.offsetHeight;u=h-i,i=h}else{var p=a.node.getBoundingClientRect();u=p.bottom-p.top,!s&&a.text.firstChild&&(f=a.text.firstChild.getBoundingClientRect().right-p.left-1)}var d=a.line.height-u;if((d>.005||d<-.005)&&(n<r&&(o-=d),gt(a.line,u),Zo(a.line),a.rest))for(var v=0;v<a.rest.length;v++)Zo(a.rest[v]);if(f>e.display.sizerWidth){var g=Math.ceil(f/vr(e.display));g>e.display.maxLineLength&&(e.display.maxLineLength=g,e.display.maxLine=a.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function Zo(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var i=e.widgets[t],r=i.node.parentNode;r&&(i.height=r.offsetHeight)}}function Li(e,t,i){var r=i&&i.top!=null?Math.max(0,i.top):e.scroller.scrollTop;r=Math.floor(r-mi(e));var n=i&&i.bottom!=null?i.bottom:r+e.wrapper.clientHeight,o=qt(t,r),l=qt(t,n);if(i&&i.ensure){var a=i.ensure.from.line,s=i.ensure.to.line;a<o?(o=a,l=qt(t,Tt(N(t,a))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=l&&(o=qt(t,Tt(N(t,s))-e.wrapper.clientHeight),l=s)}return{from:o,to:Math.max(l,o+1)}}function ja(e,t){if(!Se(e,"scrollCursorIntoView")){var i=e.display,r=i.sizer.getBoundingClientRect(),n=null,o=i.wrapper.ownerDocument;if(t.top+r.top<0?n=!0:t.bottom+r.top>(o.defaultView.innerHeight||o.documentElement.clientHeight)&&(n=!1),n!=null&&!c){var l=E("div","\u200B",null,`position: absolute;
                         top: `+(t.top-i.viewOffset-mi(e.display))+`px;
                         height: `+(t.bottom-t.top+mt(e)+i.barHeight)+`px;
                         left: `+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(l),l.scrollIntoView(n),e.display.lineSpace.removeChild(l)}}}function Va(e,t,i,r){r==null&&(r=0);var n;!e.options.lineWrapping&&t==i&&(i=t.sticky=="before"?y(t.line,t.ch+1,"before"):t,t=t.ch?y(t.line,t.sticky=="before"?t.ch-1:t.ch,"after"):t);for(var o=0;o<5;o++){var l=!1,a=ft(e,t),s=!i||i==t?a:ft(e,i);n={left:Math.min(a.left,s.left),top:Math.min(a.top,s.top)-r,right:Math.max(a.left,s.left),bottom:Math.max(a.bottom,s.bottom)+r};var u=kn(e,n),f=e.doc.scrollTop,h=e.doc.scrollLeft;if(u.scrollTop!=null&&(_r(e,u.scrollTop),Math.abs(e.doc.scrollTop-f)>1&&(l=!0)),u.scrollLeft!=null&&(tr(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-h)>1&&(l=!0)),!l)break}return n}function $a(e,t){var i=kn(e,t);i.scrollTop!=null&&_r(e,i.scrollTop),i.scrollLeft!=null&&tr(e,i.scrollLeft)}function kn(e,t){var i=e.display,r=pr(e.display);t.top<0&&(t.top=0);var n=e.curOp&&e.curOp.scrollTop!=null?e.curOp.scrollTop:i.scroller.scrollTop,o=cn(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var a=e.doc.height+hn(i),s=t.top<r,u=t.bottom>a-r;if(t.top<n)l.scrollTop=s?0:t.top;else if(t.bottom>n+o){var f=Math.min(t.top,(u?a:t.bottom)-o);f!=n&&(l.scrollTop=f)}var h=e.options.fixedGutter?0:i.gutters.offsetWidth,p=e.curOp&&e.curOp.scrollLeft!=null?e.curOp.scrollLeft:i.scroller.scrollLeft-h,d=jt(e)-i.gutters.offsetWidth,v=t.right-t.left>d;return v&&(t.right=t.left+d),t.left<10?l.scrollLeft=0:t.left<p?l.scrollLeft=Math.max(0,t.left+h-(v?0:10)):t.right>d+p-3&&(l.scrollLeft=t.right+(v?0:10)-d),l}function Tn(e,t){t!=null&&(ki(e),e.curOp.scrollTop=(e.curOp.scrollTop==null?e.doc.scrollTop:e.curOp.scrollTop)+t)}function yr(e){ki(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Kr(e,t,i){(t!=null||i!=null)&&ki(e),t!=null&&(e.curOp.scrollLeft=t),i!=null&&(e.curOp.scrollTop=i)}function es(e,t){ki(e),e.curOp.scrollToPos=t}function ki(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var i=Ro(e,t.from),r=Ro(e,t.to);Xo(e,i,r,t.margin)}}function Xo(e,t,i,r){var n=kn(e,{left:Math.min(t.left,i.left),top:Math.min(t.top,i.top)-r,right:Math.max(t.right,i.right),bottom:Math.max(t.bottom,i.bottom)+r});Kr(e,n.scrollLeft,n.scrollTop)}function _r(e,t){Math.abs(e.doc.scrollTop-t)<2||(G||Dn(e,{top:t}),Yo(e,t,!0),G&&Dn(e),Xr(e,100))}function Yo(e,t,i){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),!(e.display.scroller.scrollTop==t&&!i)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function tr(e,t,i,r){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),!((i?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r)&&(e.doc.scrollLeft=t,Vo(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Gr(e){var t=e.display,i=t.gutters.offsetWidth,r=Math.round(e.doc.height+hn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?i:0,docHeight:r,scrollHeight:r+mt(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:i}}var rr=function(e,t,i){this.cm=i;var r=this.vert=E("div",[E("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),n=this.horiz=E("div",[E("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=n.tabIndex=-1,e(r),e(n),H(r,"scroll",function(){r.clientHeight&&t(r.scrollTop,"vertical")}),H(n,"scroll",function(){n.clientWidth&&t(n.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,P&&I<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};rr.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,i=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(i){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var n=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+n)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=i?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(i?r:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(r==0&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:i?r:0,bottom:t?r:0}},rr.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},rr.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},rr.prototype.zeroWidthHack=function(){var e=j&&!m?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new Q,this.disableVert=new Q},rr.prototype.enableZeroWidthBar=function(e,t,i){e.style.visibility="";function r(){var n=e.getBoundingClientRect(),o=i=="vert"?document.elementFromPoint(n.right-1,(n.top+n.bottom)/2):document.elementFromPoint((n.right+n.left)/2,n.bottom-1);o!=e?e.style.visibility="hidden":t.set(1e3,r)}t.set(1e3,r)},rr.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var Zr=function(){};Zr.prototype.update=function(){return{bottom:0,right:0}},Zr.prototype.setScrollLeft=function(){},Zr.prototype.setScrollTop=function(){},Zr.prototype.clear=function(){};function mr(e,t){t||(t=Gr(e));var i=e.display.barWidth,r=e.display.barHeight;Qo(e,t);for(var n=0;n<4&&i!=e.display.barWidth||r!=e.display.barHeight;n++)i!=e.display.barWidth&&e.options.lineWrapping&&Si(e),Qo(e,Gr(e)),i=e.display.barWidth,r=e.display.barHeight}function Qo(e,t){var i=e.display,r=i.scrollbars.update(t);i.sizer.style.paddingRight=(i.barWidth=r.right)+"px",i.sizer.style.paddingBottom=(i.barHeight=r.bottom)+"px",i.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(i.scrollbarFiller.style.display="block",i.scrollbarFiller.style.height=r.bottom+"px",i.scrollbarFiller.style.width=r.right+"px"):i.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(i.gutterFiller.style.display="block",i.gutterFiller.style.height=r.bottom+"px",i.gutterFiller.style.width=t.gutterWidth+"px"):i.gutterFiller.style.display=""}var Jo={native:rr,null:Zr};function qo(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&ot(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new Jo[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),H(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,i){i=="horizontal"?tr(e,t):_r(e,t)},e),e.display.scrollbars.addClass&&wt(e.display.wrapper,e.display.scrollbars.addClass)}var ts=0;function ir(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++ts,markArrays:null},Wa(e.curOp)}function nr(e){var t=e.curOp;t&&Pa(t,function(i){for(var r=0;r<i.ops.length;r++)i.ops[r].cm.curOp=null;rs(i)})}function rs(e){for(var t=e.ops,i=0;i<t.length;i++)is(t[i]);for(var r=0;r<t.length;r++)ns(t[r]);for(var n=0;n<t.length;n++)os(t[n]);for(var o=0;o<t.length;o++)ls(t[o]);for(var l=0;l<t.length;l++)as(t[l])}function is(e){var t=e.cm,i=t.display;us(t),e.updateMaxLine&&un(t),e.mustUpdate=e.viewChanged||e.forceUpdate||e.scrollTop!=null||e.scrollToPos&&(e.scrollToPos.from.line<i.viewFrom||e.scrollToPos.to.line>=i.viewTo)||i.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Ti(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function ns(e){e.updatedDisplay=e.mustUpdate&&Mn(e.cm,e.update)}function os(e){var t=e.cm,i=t.display;e.updatedDisplay&&Si(t),e.barMeasure=Gr(t),i.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Oo(t,i.maxLine,i.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(i.scroller.clientWidth,i.sizer.offsetLeft+e.adjustWidthTo+mt(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,i.sizer.offsetLeft+e.adjustWidthTo-jt(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=i.input.prepareSelection())}function ls(e){var t=e.cm;e.adjustWidthTo!=null&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&tr(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var i=e.focus&&e.focus==Be(Et(t));e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,i),(e.updatedDisplay||e.startHeight!=t.doc.height)&&mr(t,e.barMeasure),e.updatedDisplay&&On(t,e.barMeasure),e.selectionChanged&&wn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),i&&Go(e.cm)}function as(e){var t=e.cm,i=t.display,r=t.doc;if(e.updatedDisplay&&jo(t,e.update),i.wheelStartX!=null&&(e.scrollTop!=null||e.scrollLeft!=null||e.scrollToPos)&&(i.wheelStartX=i.wheelStartY=null),e.scrollTop!=null&&Yo(t,e.scrollTop,e.forceScroll),e.scrollLeft!=null&&tr(t,e.scrollLeft,!0,!0),e.scrollToPos){var n=Va(t,z(r,e.scrollToPos.from),z(r,e.scrollToPos.to),e.scrollToPos.margin);ja(t,n)}var o=e.maybeHiddenMarkers,l=e.maybeUnhiddenMarkers;if(o)for(var a=0;a<o.length;++a)o[a].lines.length||ye(o[a],"hide");if(l)for(var s=0;s<l.length;++s)l[s].lines.length&&ye(l[s],"unhide");i.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&ye(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function je(e,t){if(e.curOp)return t();ir(e);try{return t()}finally{nr(e)}}function ke(e,t){return function(){if(e.curOp)return t.apply(e,arguments);ir(e);try{return t.apply(e,arguments)}finally{nr(e)}}}function Fe(e){return function(){if(this.curOp)return e.apply(this,arguments);ir(this);try{return e.apply(this,arguments)}finally{nr(this)}}}function Te(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);ir(t);try{return e.apply(this,arguments)}finally{nr(t)}}}function Xr(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,oe(ss,e))}function ss(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var i=+new Date+e.options.workTime,r=Hr(e,t.highlightFrontier),n=[];t.iter(r.line,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(r.line>=e.display.viewFrom){var l=o.styles,a=o.text.length>e.options.maxHighlightLength?Qt(t.mode,r.state):null,s=io(e,o,r,!0);a&&(r.state=a),o.styles=s.styles;var u=o.styleClasses,f=s.classes;f?o.styleClasses=f:u&&(o.styleClasses=null);for(var h=!l||l.length!=o.styles.length||u!=f&&(!u||!f||u.bgClass!=f.bgClass||u.textClass!=f.textClass),p=0;!h&&p<l.length;++p)h=l[p]!=o.styles[p];h&&n.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=e.options.maxHighlightLength&&rn(e,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>i)return Xr(e,e.options.workDelay),!0}),t.highlightFrontier=r.line,t.modeFrontier=Math.max(t.modeFrontier,r.line),n.length&&je(e,function(){for(var o=0;o<n.length;o++)Ht(e,n[o],"text")})}}var Ti=function(e,t,i){var r=e.display;this.viewport=t,this.visible=Li(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=jt(e),this.force=i,this.dims=mn(e),this.events=[]};Ti.prototype.signal=function(e,t){it(e,t)&&this.events.push(arguments)},Ti.prototype.finish=function(){for(var e=0;e<this.events.length;e++)ye.apply(null,this.events[e])};function us(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=mt(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=mt(e)+"px",t.scrollbarsClipped=!0)}function fs(e){if(e.hasFocus())return null;var t=Be(Et(e));if(!t||!pt(e.display.lineDiv,t))return null;var i={activeElt:t};if(window.getSelection){var r=$(e).getSelection();r.anchorNode&&r.extend&&pt(e.display.lineDiv,r.anchorNode)&&(i.anchorNode=r.anchorNode,i.anchorOffset=r.anchorOffset,i.focusNode=r.focusNode,i.focusOffset=r.focusOffset)}return i}function hs(e){if(!(!e||!e.activeElt||e.activeElt==Be(fe(e.activeElt)))&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&pt(document.body,e.anchorNode)&&pt(document.body,e.focusNode))){var t=e.activeElt.ownerDocument,i=t.defaultView.getSelection(),r=t.createRange();r.setEnd(e.anchorNode,e.anchorOffset),r.collapse(!1),i.removeAllRanges(),i.addRange(r),i.extend(e.focusNode,e.focusOffset)}}function Mn(e,t){var i=e.display,r=e.doc;if(t.editorIsHidden)return It(e),!1;if(!t.force&&t.visible.from>=i.viewFrom&&t.visible.to<=i.viewTo&&(i.updateLineNumbers==null||i.updateLineNumbers>=i.viewTo)&&i.renderedView==i.view&&Ko(e)==0)return!1;$o(e)&&(It(e),t.dims=mn(e));var n=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),l=Math.min(n,t.visible.to+e.options.viewportMargin);i.viewFrom<o&&o-i.viewFrom<20&&(o=Math.max(r.first,i.viewFrom)),i.viewTo>l&&i.viewTo-l<20&&(l=Math.min(n,i.viewTo)),kt&&(o=an(e.doc,o),l=mo(e.doc,l));var a=o!=i.viewFrom||l!=i.viewTo||i.lastWrapHeight!=t.wrapperHeight||i.lastWrapWidth!=t.wrapperWidth;Ja(e,o,l),i.viewOffset=Tt(N(e.doc,i.viewFrom)),e.display.mover.style.top=i.viewOffset+"px";var s=Ko(e);if(!a&&s==0&&!t.force&&i.renderedView==i.view&&(i.updateLineNumbers==null||i.updateLineNumbers>=i.viewTo))return!1;var u=fs(e);return s>4&&(i.lineDiv.style.display="none"),cs(e,i.updateLineNumbers,t.dims),s>4&&(i.lineDiv.style.display=""),i.renderedView=i.view,hs(u),lt(i.cursorDiv),lt(i.selectionDiv),i.gutters.style.height=i.sizer.style.minHeight=0,a&&(i.lastWrapHeight=t.wrapperHeight,i.lastWrapWidth=t.wrapperWidth,Xr(e,400)),i.updateLineNumbers=null,!0}function jo(e,t){for(var i=t.viewport,r=!0;;r=!1){if(!r||!e.options.lineWrapping||t.oldDisplayWidth==jt(e)){if(i&&i.top!=null&&(i={top:Math.min(e.doc.height+hn(e.display)-cn(e),i.top)}),t.visible=Li(e.display,e.doc,i),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break}else r&&(t.visible=Li(e.display,e.doc,i));if(!Mn(e,t))break;Si(e);var n=Gr(e);Ur(e),mr(e,n),On(e,n),t.force=!1}t.signal(e,"update",e),(e.display.viewFrom!=e.display.reportedViewFrom||e.display.viewTo!=e.display.reportedViewTo)&&(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Dn(e,t){var i=new Ti(e,t);if(Mn(e,i)){Si(e),jo(e,i);var r=Gr(e);Ur(e),mr(e,r),On(e,r),i.finish()}}function cs(e,t,i){var r=e.display,n=e.options.lineNumbers,o=r.lineDiv,l=o.firstChild;function a(v){var g=v.nextSibling;return J&&j&&e.display.currentWheelTarget==v?v.style.display="none":v.parentNode.removeChild(v),g}for(var s=r.view,u=r.viewFrom,f=0;f<s.length;f++){var h=s[f];if(!h.hidden)if(!h.node||h.node.parentNode!=o){var p=Ba(e,h,u,i);o.insertBefore(p,l)}else{for(;l!=h.node;)l=a(l);var d=n&&t!=null&&t<=u&&h.lineNumber;h.changes&&(F(h.changes,"gutter")>-1&&(d=!1),So(e,h,u,i)),d&&(lt(h.lineNumber),h.lineNumber.appendChild(document.createTextNode($i(e.options,u)))),l=h.node.nextSibling}u+=h.size}for(;l;)l=a(l)}function Nn(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",Le(e,"gutterChanged",e)}function On(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+mt(e)+"px"}function Vo(e){var t=e.display,i=t.view;if(!(!t.alignWidgets&&(!t.gutters.firstChild||!e.options.fixedGutter))){for(var r=Cn(t)-t.scroller.scrollLeft+e.doc.scrollLeft,n=t.gutters.offsetWidth,o=r+"px",l=0;l<i.length;l++)if(!i[l].hidden){e.options.fixedGutter&&(i[l].gutter&&(i[l].gutter.style.left=o),i[l].gutterBackground&&(i[l].gutterBackground.style.left=o));var a=i[l].alignable;if(a)for(var s=0;s<a.length;s++)a[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+n+"px")}}function $o(e){if(!e.options.lineNumbers)return!1;var t=e.doc,i=$i(e.options,t.first+t.size-1),r=e.display;if(i.length!=r.lineNumChars){var n=r.measure.appendChild(E("div",[E("div",i)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=n.firstChild.offsetWidth,l=n.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-l)+1,r.lineNumWidth=r.lineNumInnerWidth+l,r.lineNumChars=r.lineNumInnerWidth?i.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",Nn(e.display),!0}return!1}function An(e,t){for(var i=[],r=!1,n=0;n<e.length;n++){var o=e[n],l=null;if(typeof o!="string"&&(l=o.style,o=o.className),o=="CodeMirror-linenumbers")if(t)r=!0;else continue;i.push({className:o,style:l})}return t&&!r&&i.push({className:"CodeMirror-linenumbers",style:null}),i}function el(e){var t=e.gutters,i=e.gutterSpecs;lt(t),e.lineGutter=null;for(var r=0;r<i.length;++r){var n=i[r],o=n.className,l=n.style,a=t.appendChild(E("div",null,"CodeMirror-gutter "+o));l&&(a.style.cssText=l),o=="CodeMirror-linenumbers"&&(e.lineGutter=a,a.style.width=(e.lineNumWidth||1)+"px")}t.style.display=i.length?"":"none",Nn(e)}function Yr(e){el(e.display),Ke(e),Vo(e)}function ds(e,t,i,r){var n=this;this.input=i,n.scrollbarFiller=E("div",null,"CodeMirror-scrollbar-filler"),n.scrollbarFiller.setAttribute("cm-not-content","true"),n.gutterFiller=E("div",null,"CodeMirror-gutter-filler"),n.gutterFiller.setAttribute("cm-not-content","true"),n.lineDiv=At("div",null,"CodeMirror-code"),n.selectionDiv=E("div",null,null,"position: relative; z-index: 1"),n.cursorDiv=E("div",null,"CodeMirror-cursors"),n.measure=E("div",null,"CodeMirror-measure"),n.lineMeasure=E("div",null,"CodeMirror-measure"),n.lineSpace=At("div",[n.measure,n.lineMeasure,n.selectionDiv,n.cursorDiv,n.lineDiv],null,"position: relative; outline: none");var o=At("div",[n.lineSpace],"CodeMirror-lines");n.mover=E("div",[o],null,"position: relative"),n.sizer=E("div",[n.mover],"CodeMirror-sizer"),n.sizerWidth=null,n.heightForcer=E("div",null,null,"position: absolute; height: "+q+"px; width: 1px;"),n.gutters=E("div",null,"CodeMirror-gutters"),n.lineGutter=null,n.scroller=E("div",[n.sizer,n.heightForcer,n.gutters],"CodeMirror-scroll"),n.scroller.setAttribute("tabIndex","-1"),n.wrapper=E("div",[n.scrollbarFiller,n.gutterFiller,n.scroller],"CodeMirror"),te&&ve>=105&&(n.wrapper.style.clipPath="inset(0px)"),n.wrapper.setAttribute("translate","no"),P&&I<8&&(n.gutters.style.zIndex=-1,n.scroller.style.paddingRight=0),!J&&!(G&&S)&&(n.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(n.wrapper):e(n.wrapper)),n.viewFrom=n.viewTo=t.first,n.reportedViewFrom=n.reportedViewTo=t.first,n.view=[],n.renderedView=null,n.externalMeasured=null,n.viewOffset=0,n.lastWrapHeight=n.lastWrapWidth=0,n.updateLineNumbers=null,n.nativeBarWidth=n.barHeight=n.barWidth=0,n.scrollbarsClipped=!1,n.lineNumWidth=n.lineNumInnerWidth=n.lineNumChars=null,n.alignWidgets=!1,n.cachedCharWidth=n.cachedTextHeight=n.cachedPaddingH=null,n.maxLine=null,n.maxLineLength=0,n.maxLineChanged=!1,n.wheelDX=n.wheelDY=n.wheelStartX=n.wheelStartY=null,n.shift=!1,n.selForContextMenu=null,n.activeTouch=null,n.gutterSpecs=An(r.gutters,r.lineNumbers),el(n),i.init(n)}var Mi=0,Dt=null;P?Dt=-.53:G?Dt=15:te?Dt=-.7:Y&&(Dt=-1/3);function tl(e){var t=e.wheelDeltaX,i=e.wheelDeltaY;return t==null&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),i==null&&e.detail&&e.axis==e.VERTICAL_AXIS?i=e.detail:i==null&&(i=e.wheelDelta),{x:t,y:i}}function ps(e){var t=tl(e);return t.x*=Dt,t.y*=Dt,t}function rl(e,t){te&&ve==102&&(e.display.chromeScrollHack==null?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout(function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""},100));var i=tl(t),r=i.x,n=i.y,o=Dt;t.deltaMode===0&&(r=t.deltaX,n=t.deltaY,o=1);var l=e.display,a=l.scroller,s=a.scrollWidth>a.clientWidth,u=a.scrollHeight>a.clientHeight;if(!!(r&&s||n&&u)){if(n&&j&&J){e:for(var f=t.target,h=l.view;f!=a;f=f.parentNode)for(var p=0;p<h.length;p++)if(h[p].node==f){e.display.currentWheelTarget=f;break e}}if(r&&!G&&!Ne&&o!=null){n&&u&&_r(e,Math.max(0,a.scrollTop+n*o)),tr(e,Math.max(0,a.scrollLeft+r*o)),(!n||n&&u)&&Ue(t),l.wheelStartX=null;return}if(n&&o!=null){var d=n*o,v=e.doc.scrollTop,g=v+l.wrapper.clientHeight;d<0?v=Math.max(0,v+d-50):g=Math.min(e.doc.height,g+d+50),Dn(e,{top:v,bottom:g})}Mi<20&&t.deltaMode!==0&&(l.wheelStartX==null?(l.wheelStartX=a.scrollLeft,l.wheelStartY=a.scrollTop,l.wheelDX=r,l.wheelDY=n,setTimeout(function(){if(l.wheelStartX!=null){var b=a.scrollLeft-l.wheelStartX,x=a.scrollTop-l.wheelStartY,L=x&&l.wheelDY&&x/l.wheelDY||b&&l.wheelDX&&b/l.wheelDX;l.wheelStartX=l.wheelStartY=null,!!L&&(Dt=(Dt*Mi+L)/(Mi+1),++Mi)}},200)):(l.wheelDX+=r,l.wheelDY+=n))}}var rt=function(e,t){this.ranges=e,this.primIndex=t};rt.prototype.primary=function(){return this.ranges[this.primIndex]},rt.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var i=this.ranges[t],r=e.ranges[t];if(!en(i.anchor,r.anchor)||!en(i.head,r.head))return!1}return!0},rt.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new V(tn(this.ranges[t].anchor),tn(this.ranges[t].head));return new rt(e,this.primIndex)},rt.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},rt.prototype.contains=function(e,t){t||(t=e);for(var i=0;i<this.ranges.length;i++){var r=this.ranges[i];if(B(t,r.from())>=0&&B(e,r.to())<=0)return i}return-1};var V=function(e,t){this.anchor=e,this.head=t};V.prototype.from=function(){return fi(this.anchor,this.head)},V.prototype.to=function(){return ui(this.anchor,this.head)},V.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch};function ht(e,t,i){var r=e&&e.options.selectionsMayTouch,n=t[i];t.sort(function(p,d){return B(p.from(),d.from())}),i=F(t,n);for(var o=1;o<t.length;o++){var l=t[o],a=t[o-1],s=B(a.to(),l.from());if(r&&!l.empty()?s>0:s>=0){var u=fi(a.from(),l.from()),f=ui(a.to(),l.to()),h=a.empty()?l.from()==l.head:a.from()==a.head;o<=i&&--i,t.splice(--o,2,new V(h?f:u,h?u:f))}}return new rt(t,i)}function Ft(e,t){return new rt([new V(e,t||e)],0)}function Rt(e){return e.text?y(e.from.line+e.text.length-1,X(e.text).length+(e.text.length==1?e.from.ch:0)):e.to}function il(e,t){if(B(e,t.from)<0)return e;if(B(e,t.to)<=0)return Rt(t);var i=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=Rt(t).ch-t.to.ch),y(i,r)}function Wn(e,t){for(var i=[],r=0;r<e.sel.ranges.length;r++){var n=e.sel.ranges[r];i.push(new V(il(n.anchor,t),il(n.head,t)))}return ht(e.cm,i,e.sel.primIndex)}function nl(e,t,i){return e.line==t.line?y(i.line,e.ch-t.ch+i.ch):y(i.line+(e.line-t.line),e.ch)}function vs(e,t,i){for(var r=[],n=y(e.first,0),o=n,l=0;l<t.length;l++){var a=t[l],s=nl(a.from,n,o),u=nl(Rt(a),n,o);if(n=a.to,o=u,i=="around"){var f=e.sel.ranges[l],h=B(f.head,f.anchor)<0;r[l]=new V(h?u:s,h?s:u)}else r[l]=new V(s,s)}return new rt(r,e.sel.primIndex)}function En(e){e.doc.mode=qi(e.options,e.doc.modeOption),Qr(e)}function Qr(e){e.doc.iter(function(t){t.stateAfter&&(t.stateAfter=null),t.styles&&(t.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Xr(e,100),e.state.modeGen++,e.curOp&&Ke(e)}function ol(e,t){return t.from.ch==0&&t.to.ch==0&&X(t.text)==""&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Pn(e,t,i,r){function n(L){return i?i[L]:null}function o(L,w,k){Sa(L,w,k,r),Le(L,"change",L,t)}function l(L,w){for(var k=[],A=L;A<w;++A)k.push(new hr(u[A],n(A),r));return k}var a=t.from,s=t.to,u=t.text,f=N(e,a.line),h=N(e,s.line),p=X(u),d=n(u.length-1),v=s.line-a.line;if(t.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(ol(e,t)){var g=l(0,u.length-1);o(h,h.text,d),v&&e.remove(a.line,v),g.length&&e.insert(a.line,g)}else if(f==h)if(u.length==1)o(f,f.text.slice(0,a.ch)+p+f.text.slice(s.ch),d);else{var b=l(1,u.length-1);b.push(new hr(p+f.text.slice(s.ch),d,r)),o(f,f.text.slice(0,a.ch)+u[0],n(0)),e.insert(a.line+1,b)}else if(u.length==1)o(f,f.text.slice(0,a.ch)+u[0]+h.text.slice(s.ch),n(0)),e.remove(a.line+1,v);else{o(f,f.text.slice(0,a.ch)+u[0],n(0)),o(h,p+h.text.slice(s.ch),d);var x=l(1,u.length-1);v>1&&e.remove(a.line+1,v-1),e.insert(a.line+1,x)}Le(e,"change",e,t)}function Bt(e,t,i){function r(n,o,l){if(n.linked)for(var a=0;a<n.linked.length;++a){var s=n.linked[a];if(s.doc!=o){var u=l&&s.sharedHist;i&&!u||(t(s.doc,u),r(s.doc,n,u))}}}r(e,null,!0)}function ll(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,bn(e),En(e),al(e),e.options.direction=t.direction,e.options.lineWrapping||un(e),e.options.mode=t.modeOption,Ke(e)}function al(e){(e.doc.direction=="rtl"?wt:ot)(e.display.lineDiv,"CodeMirror-rtl")}function gs(e){je(e,function(){al(e),Ke(e)})}function Di(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:Infinity,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function Hn(e,t){var i={from:tn(t.from),to:Rt(t),text:Jt(e,t.from,t.to)};return fl(e,i,t.from.line,t.to.line+1),Bt(e,function(r){return fl(r,i,t.from.line,t.to.line+1)},!0),i}function sl(e){for(;e.length;){var t=X(e);if(t.ranges)e.pop();else break}}function ys(e,t){if(t)return sl(e.done),X(e.done);if(e.done.length&&!X(e.done).ranges)return X(e.done);if(e.done.length>1&&!e.done[e.done.length-2].ranges)return e.done.pop(),X(e.done)}function ul(e,t,i,r){var n=e.history;n.undone.length=0;var o=+new Date,l,a;if((n.lastOp==r||n.lastOrigin==t.origin&&t.origin&&(t.origin.charAt(0)=="+"&&n.lastModTime>o-(e.cm?e.cm.options.historyEventDelay:500)||t.origin.charAt(0)=="*"))&&(l=ys(n,n.lastOp==r)))a=X(l.changes),B(t.from,t.to)==0&&B(t.from,a.to)==0?a.to=Rt(t):l.changes.push(Hn(e,t));else{var s=X(n.done);for((!s||!s.ranges)&&Ni(e.sel,n.done),l={changes:[Hn(e,t)],generation:n.generation},n.done.push(l);n.done.length>n.undoDepth;)n.done.shift(),n.done[0].ranges||n.done.shift()}n.done.push(i),n.generation=++n.maxGeneration,n.lastModTime=n.lastSelTime=o,n.lastOp=n.lastSelOp=r,n.lastOrigin=n.lastSelOrigin=t.origin,a||ye(e,"historyAdded")}function ms(e,t,i,r){var n=t.charAt(0);return n=="*"||n=="+"&&i.ranges.length==r.ranges.length&&i.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function Cs(e,t,i,r){var n=e.history,o=r&&r.origin;i==n.lastSelOp||o&&n.lastSelOrigin==o&&(n.lastModTime==n.lastSelTime&&n.lastOrigin==o||ms(e,o,X(n.done),t))?n.done[n.done.length-1]=t:Ni(t,n.done),n.lastSelTime=+new Date,n.lastSelOrigin=o,n.lastSelOp=i,r&&r.clearRedo!==!1&&sl(n.undone)}function Ni(e,t){var i=X(t);i&&i.ranges&&i.equals(e)||t.push(e)}function fl(e,t,i,r){var n=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,i),Math.min(e.first+e.size,r),function(l){l.markedSpans&&((n||(n=t["spans_"+e.id]={}))[o]=l.markedSpans),++o})}function bs(e){if(!e)return null;for(var t,i=0;i<e.length;++i)e[i].marker.explicitlyCleared?t||(t=e.slice(0,i)):t&&t.push(e[i]);return t?t.length?t:null:e}function xs(e,t){var i=t["spans_"+e.id];if(!i)return null;for(var r=[],n=0;n<t.text.length;++n)r.push(bs(i[n]));return r}function hl(e,t){var i=xs(e,t),r=on(e,t);if(!i)return r;if(!r)return i;for(var n=0;n<i.length;++n){var o=i[n],l=r[n];if(o&&l){e:for(var a=0;a<l.length;++a){for(var s=l[a],u=0;u<o.length;++u)if(o[u].marker==s.marker)continue e;o.push(s)}}else l&&(i[n]=l)}return i}function Cr(e,t,i){for(var r=[],n=0;n<e.length;++n){var o=e[n];if(o.ranges){r.push(i?rt.prototype.deepCopy.call(o):o);continue}var l=o.changes,a=[];r.push({changes:a});for(var s=0;s<l.length;++s){var u=l[s],f=void 0;if(a.push({from:u.from,to:u.to,text:u.text}),t)for(var h in u)(f=h.match(/^spans_(\d+)$/))&&F(t,Number(f[1]))>-1&&(X(a)[h]=u[h],delete u[h])}}return r}function In(e,t,i,r){if(r){var n=e.anchor;if(i){var o=B(t,n)<0;o!=B(i,n)<0?(n=t,t=i):o!=B(t,i)<0&&(t=i)}return new V(n,t)}else return new V(i||t,t)}function Oi(e,t,i,r,n){n==null&&(n=e.cm&&(e.cm.display.shift||e.extend)),Ee(e,new rt([In(e.sel.primary(),t,i,n)],0),r)}function cl(e,t,i){for(var r=[],n=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=In(e.sel.ranges[o],t[o],null,n);var l=ht(e.cm,r,e.sel.primIndex);Ee(e,l,i)}function Fn(e,t,i,r){var n=e.sel.ranges.slice(0);n[t]=i,Ee(e,ht(e.cm,n,e.sel.primIndex),r)}function dl(e,t,i,r){Ee(e,Ft(t,i),r)}function ws(e,t,i){var r={ranges:t.ranges,update:function(n){this.ranges=[];for(var o=0;o<n.length;o++)this.ranges[o]=new V(z(e,n[o].anchor),z(e,n[o].head))},origin:i&&i.origin};return ye(e,"beforeSelectionChange",e,r),e.cm&&ye(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?ht(e.cm,r.ranges,r.ranges.length-1):t}function pl(e,t,i){var r=e.history.done,n=X(r);n&&n.ranges?(r[r.length-1]=t,Ai(e,t,i)):Ee(e,t,i)}function Ee(e,t,i){Ai(e,t,i),Cs(e,e.sel,e.cm?e.cm.curOp.id:NaN,i)}function Ai(e,t,i){(it(e,"beforeSelectionChange")||e.cm&&it(e.cm,"beforeSelectionChange"))&&(t=ws(e,t,i));var r=i&&i.bias||(B(t.primary().head,e.sel.primary().head)<0?-1:1);vl(e,yl(e,t,r,!0)),!(i&&i.scroll===!1)&&e.cm&&e.cm.getOption("readOnly")!="nocursor"&&yr(e.cm)}function vl(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,jn(e.cm)),Le(e,"cursorActivity",e))}function gl(e){vl(e,yl(e,e.sel,null,!1))}function yl(e,t,i,r){for(var n,o=0;o<t.ranges.length;o++){var l=t.ranges[o],a=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=Wi(e,l.anchor,a&&a.anchor,i,r),u=l.head==l.anchor?s:Wi(e,l.head,a&&a.head,i,r);(n||s!=l.anchor||u!=l.head)&&(n||(n=t.ranges.slice(0,o)),n[o]=new V(s,u))}return n?ht(e.cm,n,t.primIndex):t}function br(e,t,i,r,n){var o=N(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var a=o.markedSpans[l],s=a.marker,u="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,f="selectRight"in s?!s.selectRight:s.inclusiveRight;if((a.from==null||(u?a.from<=t.ch:a.from<t.ch))&&(a.to==null||(f?a.to>=t.ch:a.to>t.ch))){if(n&&(ye(s,"beforeCursorEnter"),s.explicitlyCleared))if(o.markedSpans){--l;continue}else break;if(!s.atomic)continue;if(i){var h=s.find(r<0?1:-1),p=void 0;if((r<0?f:u)&&(h=ml(e,h,-r,h&&h.line==t.line?o:null)),h&&h.line==t.line&&(p=B(h,i))&&(r<0?p<0:p>0))return br(e,h,t,r,n)}var d=s.find(r<0?-1:1);return(r<0?u:f)&&(d=ml(e,d,r,d.line==t.line?o:null)),d?br(e,d,t,r,n):null}}return t}function Wi(e,t,i,r,n){var o=r||1,l=br(e,t,i,o,n)||!n&&br(e,t,i,o,!0)||br(e,t,i,-o,n)||!n&&br(e,t,i,-o,!0);return l||(e.cantEdit=!0,y(e.first,0))}function ml(e,t,i,r){return i<0&&t.ch==0?t.line>e.first?z(e,y(t.line-1)):null:i>0&&t.ch==(r||N(e,t.line)).text.length?t.line<e.first+e.size-1?y(t.line+1,0):null:new y(t.line,t.ch+i)}function Cl(e){e.setSelection(y(e.firstLine(),0),y(e.lastLine()),Z)}function bl(e,t,i){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return i&&(r.update=function(n,o,l,a){n&&(r.from=z(e,n)),o&&(r.to=z(e,o)),l&&(r.text=l),a!==void 0&&(r.origin=a)}),ye(e,"beforeChange",e,r),e.cm&&ye(e.cm,"beforeChange",e.cm,r),r.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:r.from,to:r.to,text:r.text,origin:r.origin}}function xr(e,t,i){if(e.cm){if(!e.cm.curOp)return ke(e.cm,xr)(e,t,i);if(e.cm.state.suppressEdits)return}if(!((it(e,"beforeChange")||e.cm&&it(e.cm,"beforeChange"))&&(t=bl(e,t,!0),!t))){var r=fo&&!i&&Ca(e,t.from,t.to);if(r)for(var n=r.length-1;n>=0;--n)xl(e,{from:r[n].from,to:r[n].to,text:n?[""]:t.text,origin:t.origin});else xl(e,t)}}function xl(e,t){if(!(t.text.length==1&&t.text[0]==""&&B(t.from,t.to)==0)){var i=Wn(e,t);ul(e,t,i,e.cm?e.cm.curOp.id:NaN),Jr(e,t,i,on(e,t));var r=[];Bt(e,function(n,o){!o&&F(r,n.history)==-1&&(kl(n.history,t),r.push(n.history)),Jr(n,t,null,on(n,t))})}}function Ei(e,t,i){var r=e.cm&&e.cm.state.suppressEdits;if(!(r&&!i)){for(var n=e.history,o,l=e.sel,a=t=="undo"?n.done:n.undone,s=t=="undo"?n.undone:n.done,u=0;u<a.length&&(o=a[u],!(i?o.ranges&&!o.equals(e.sel):!o.ranges));u++);if(u!=a.length){for(n.lastOrigin=n.lastSelOrigin=null;;)if(o=a.pop(),o.ranges){if(Ni(o,s),i&&!o.equals(e.sel)){Ee(e,o,{clearRedo:!1});return}l=o}else if(r){a.push(o);return}else break;var f=[];Ni(l,s),s.push({changes:f,generation:n.generation}),n.generation=o.generation||++n.maxGeneration;for(var h=it(e,"beforeChange")||e.cm&&it(e.cm,"beforeChange"),p=function(g){var b=o.changes[g];if(b.origin=t,h&&!bl(e,b,!1))return a.length=0,{};f.push(Hn(e,b));var x=g?Wn(e,b):X(a);Jr(e,b,x,hl(e,b)),!g&&e.cm&&e.cm.scrollIntoView({from:b.from,to:Rt(b)});var L=[];Bt(e,function(w,k){!k&&F(L,w.history)==-1&&(kl(w.history,b),L.push(w.history)),Jr(w,b,null,hl(w,b))})},d=o.changes.length-1;d>=0;--d){var v=p(d);if(v)return v.v}}}}function wl(e,t){if(t!=0&&(e.first+=t,e.sel=new rt(at(e.sel.ranges,function(n){return new V(y(n.anchor.line+t,n.anchor.ch),y(n.head.line+t,n.head.ch))}),e.sel.primIndex),e.cm)){Ke(e.cm,e.first,e.first-t,t);for(var i=e.cm.display,r=i.viewFrom;r<i.viewTo;r++)Ht(e.cm,r,"gutter")}}function Jr(e,t,i,r){if(e.cm&&!e.cm.curOp)return ke(e.cm,Jr)(e,t,i,r);if(t.to.line<e.first){wl(e,t.text.length-1-(t.to.line-t.from.line));return}if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var n=t.text.length-1-(e.first-t.from.line);wl(e,n),t={from:y(e.first,0),to:y(t.to.line+n,t.to.ch),text:[X(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:y(o,N(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=Jt(e,t.from,t.to),i||(i=Wn(e,t)),e.cm?Ss(e.cm,t,r):Pn(e,t,r),Ai(e,i,Z),e.cantEdit&&Wi(e,y(e.firstLine(),0))&&(e.cantEdit=!1)}}function Ss(e,t,i){var r=e.doc,n=e.display,o=t.from,l=t.to,a=!1,s=o.line;e.options.lineWrapping||(s=re(ut(N(r,o.line))),r.iter(s,l.line+1,function(d){if(d==n.maxLine)return a=!0,!0})),r.sel.contains(t.from,t.to)>-1&&jn(e),Pn(r,t,i,Uo(e)),e.options.lineWrapping||(r.iter(s,o.line+t.text.length,function(d){var v=gi(d);v>n.maxLineLength&&(n.maxLine=d,n.maxLineLength=v,n.maxLineChanged=!0,a=!1)}),a&&(e.curOp.updateMaxLine=!0)),ca(r,o.line),Xr(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?Ke(e):o.line==l.line&&t.text.length==1&&!ol(e.doc,t)?Ht(e,o.line,"text"):Ke(e,o.line,l.line+1,u);var f=it(e,"changes"),h=it(e,"change");if(h||f){var p={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};h&&Le(e,"change",e,p),f&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(p)}e.display.selForContextMenu=null}function wr(e,t,i,r,n){var o;r||(r=i),B(r,i)<0&&(o=[r,i],i=o[0],r=o[1]),typeof t=="string"&&(t=e.splitLines(t)),xr(e,{from:i,to:r,text:t,origin:n})}function Sl(e,t,i,r){i<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function Ll(e,t,i,r){for(var n=0;n<e.length;++n){var o=e[n],l=!0;if(o.ranges){o.copied||(o=e[n]=o.deepCopy(),o.copied=!0);for(var a=0;a<o.ranges.length;a++)Sl(o.ranges[a].anchor,t,i,r),Sl(o.ranges[a].head,t,i,r);continue}for(var s=0;s<o.changes.length;++s){var u=o.changes[s];if(i<u.from.line)u.from=y(u.from.line+r,u.from.ch),u.to=y(u.to.line+r,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,n+1),n=0)}}function kl(e,t){var i=t.from.line,r=t.to.line,n=t.text.length-(r-i)-1;Ll(e.done,i,r,n),Ll(e.undone,i,r,n)}function qr(e,t,i,r){var n=t,o=t;return typeof t=="number"?o=N(e,to(e,t)):n=re(t),n==null?null:(r(o,n)&&e.cm&&Ht(e.cm,n,i),o)}function jr(e){this.lines=e,this.parent=null;for(var t=0,i=0;i<e.length;++i)e[i].parent=this,t+=e[i].height;this.height=t}jr.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var i=e,r=e+t;i<r;++i){var n=this.lines[i];this.height-=n.height,La(n),Le(n,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,i){this.height+=i,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,i){for(var r=e+t;e<r;++e)if(i(this.lines[e]))return!0}};function Vr(e){this.children=e;for(var t=0,i=0,r=0;r<e.length;++r){var n=e[r];t+=n.chunkSize(),i+=n.height,n.parent=this}this.size=t,this.height=i,this.parent=null}Vr.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var i=0;i<this.children.length;++i){var r=this.children[i],n=r.chunkSize();if(e<n){var o=Math.min(t,n-e),l=r.height;if(r.removeInner(e,o),this.height-=l-r.height,n==o&&(this.children.splice(i--,1),r.parent=null),(t-=o)==0)break;e=0}else e-=n}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof jr))){var a=[];this.collapse(a),this.children=[new jr(a)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,i){this.size+=t.length,this.height+=i;for(var r=0;r<this.children.length;++r){var n=this.children[r],o=n.chunkSize();if(e<=o){if(n.insertInner(e,t,i),n.lines&&n.lines.length>50){for(var l=n.lines.length%25+25,a=l;a<n.lines.length;){var s=new jr(n.lines.slice(a,a+=25));n.height-=s.height,this.children.splice(++r,0,s),s.parent=this}n.lines=n.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),i=new Vr(t);if(e.parent){e.size-=i.size,e.height-=i.height;var n=F(e.parent.children,e);e.parent.children.splice(n+1,0,i)}else{var r=new Vr(e.children);r.parent=e,e.children=[r,i],e=r}i.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,i){for(var r=0;r<this.children.length;++r){var n=this.children[r],o=n.chunkSize();if(e<o){var l=Math.min(t,o-e);if(n.iterN(e,l,i))return!0;if((t-=l)==0)break;e=0}else e-=o}}};var $r=function(e,t,i){if(i)for(var r in i)i.hasOwnProperty(r)&&(this[r]=i[r]);this.doc=e,this.node=t};$r.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,i=this.line,r=re(i);if(!(r==null||!t)){for(var n=0;n<t.length;++n)t[n]==this&&t.splice(n--,1);t.length||(i.widgets=null);var o=Br(this);gt(i,Math.max(0,i.height-o)),e&&(je(e,function(){Tl(e,i,-o),Ht(e,r,"widget")}),Le(e,"lineWidgetCleared",e,this,r))}},$r.prototype.changed=function(){var e=this,t=this.height,i=this.doc.cm,r=this.line;this.height=null;var n=Br(this)-t;!n||(Pt(this.doc,r)||gt(r,r.height+n),i&&je(i,function(){i.curOp.forceUpdate=!0,Tl(i,r,n),Le(i,"lineWidgetChanged",i,e,re(r))}))},sr($r);function Tl(e,t,i){Tt(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Tn(e,i)}function Ls(e,t,i,r){var n=new $r(e,i,r),o=e.cm;return o&&n.noHScroll&&(o.display.alignWidgets=!0),qr(e,t,"widget",function(l){var a=l.widgets||(l.widgets=[]);if(n.insertAt==null?a.push(n):a.splice(Math.min(a.length,Math.max(0,n.insertAt)),0,n),n.line=l,o&&!Pt(e,l)){var s=Tt(l)<e.scrollTop;gt(l,l.height+Br(n)),s&&Tn(o,n.height),o.curOp.forceUpdate=!0}return!0}),o&&Le(o,"lineWidgetAdded",o,n,typeof t=="number"?t:re(t)),n}var Ml=0,zt=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++Ml};zt.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&ir(e),it(this,"clear")){var i=this.find();i&&Le(this,"clear",i.from,i.to)}for(var r=null,n=null,o=0;o<this.lines.length;++o){var l=this.lines[o],a=Ir(l.markedSpans,this);e&&!this.collapsed?Ht(e,re(l),"text"):e&&(a.to!=null&&(n=re(l)),a.from!=null&&(r=re(l))),l.markedSpans=va(l.markedSpans,a),a.from==null&&this.collapsed&&!Pt(this.doc,l)&&e&&gt(l,pr(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var u=ut(this.lines[s]),f=gi(u);f>e.display.maxLineLength&&(e.display.maxLine=u,e.display.maxLineLength=f,e.display.maxLineChanged=!0)}r!=null&&e&&this.collapsed&&Ke(e,r,n+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&gl(e.doc)),e&&Le(e,"markerCleared",e,this,r,n),t&&nr(e),this.parent&&this.parent.clear()}},zt.prototype.find=function(e,t){e==null&&this.type=="bookmark"&&(e=1);for(var i,r,n=0;n<this.lines.length;++n){var o=this.lines[n],l=Ir(o.markedSpans,this);if(l.from!=null&&(i=y(t?o:re(o),l.from),e==-1))return i;if(l.to!=null&&(r=y(t?o:re(o),l.to),e==1))return r}return i&&{from:i,to:r}},zt.prototype.changed=function(){var e=this,t=this.find(-1,!0),i=this,r=this.doc.cm;!t||!r||je(r,function(){var n=t.line,o=re(t.line),l=dn(r,o);if(l&&(Eo(l),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!Pt(i.doc,n)&&i.height!=null){var a=i.height;i.height=null;var s=Br(i)-a;s&&gt(n,n.height+s)}Le(r,"markerChanged",r,e)})},zt.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(!t.maybeHiddenMarkers||F(t.maybeHiddenMarkers,this)==-1)&&(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},zt.prototype.detachLine=function(e){if(this.lines.splice(F(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},sr(zt);function Sr(e,t,i,r,n){if(r&&r.shared)return ks(e,t,i,r,n);if(e.cm&&!e.cm.curOp)return ke(e.cm,Sr)(e,t,i,r,n);var o=new zt(e,n),l=B(t,i);if(r&&K(r,o,!1),l>0||l==0&&o.clearWhenEmpty!==!1)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=At("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(yo(e,t.line,t,i,o)||t.line!=i.line&&yo(e,i.line,t,i,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");pa()}o.addToHistory&&ul(e,{from:t,to:i,origin:"markText"},e.sel,NaN);var a=t.line,s=e.cm,u;if(e.iter(a,i.line+1,function(h){s&&o.collapsed&&!s.options.lineWrapping&&ut(h)==s.display.maxLine&&(u=!0),o.collapsed&&a!=t.line&&gt(h,0),ga(h,new ci(o,a==t.line?t.ch:null,a==i.line?i.ch:null),e.cm&&e.cm.curOp),++a}),o.collapsed&&e.iter(t.line,i.line+1,function(h){Pt(e,h)&&gt(h,0)}),o.clearOnEnter&&H(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(da(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++Ml,o.atomic=!0),s){if(u&&(s.curOp.updateMaxLine=!0),o.collapsed)Ke(s,t.line,i.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var f=t.line;f<=i.line;f++)Ht(s,f,"text");o.atomic&&gl(s.doc),Le(s,"markerAdded",s,o)}return o}var ei=function(e,t){this.markers=e,this.primary=t;for(var i=0;i<e.length;++i)e[i].parent=this};ei.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();Le(this,"clear")}},ei.prototype.find=function(e,t){return this.primary.find(e,t)},sr(ei);function ks(e,t,i,r,n){r=K(r),r.shared=!1;var o=[Sr(e,t,i,r,n)],l=o[0],a=r.widgetNode;return Bt(e,function(s){a&&(r.widgetNode=a.cloneNode(!0)),o.push(Sr(s,z(s,t),z(s,i),r,n));for(var u=0;u<s.linked.length;++u)if(s.linked[u].isParent)return;l=X(o)}),new ei(o,l)}function Dl(e){return e.findMarks(y(e.first,0),e.clipPos(y(e.lastLine())),function(t){return t.parent})}function Ts(e,t){for(var i=0;i<t.length;i++){var r=t[i],n=r.find(),o=e.clipPos(n.from),l=e.clipPos(n.to);if(B(o,l)){var a=Sr(e,o,l,r.primary,r.primary.type);r.markers.push(a),a.parent=r}}}function Ms(e){for(var t=function(r){var n=e[r],o=[n.primary.doc];Bt(n.primary.doc,function(s){return o.push(s)});for(var l=0;l<n.markers.length;l++){var a=n.markers[l];F(o,a.doc)==-1&&(a.parent=null,n.markers.splice(l--,1))}},i=0;i<e.length;i++)t(i)}var Ds=0,_e=function(e,t,i,r,n){if(!(this instanceof _e))return new _e(e,t,i,r,n);i==null&&(i=0),Vr.call(this,[new jr([new hr("",null)])]),this.first=i,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=i;var o=y(i,0);this.sel=Ft(o),this.history=new Di(null),this.id=++Ds,this.modeOption=t,this.lineSep=r,this.direction=n=="rtl"?"rtl":"ltr",this.extend=!1,typeof e=="string"&&(e=this.splitLines(e)),Pn(this,{from:o,to:o,text:e}),Ee(this,Ft(o),Z)};_e.prototype=$e(Vr.prototype,{constructor:_e,iter:function(e,t,i){i?this.iterN(e-this.first,t-e,i):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var i=0,r=0;r<t.length;++r)i+=t[r].height;this.insertInner(e-this.first,t,i)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=Vi(this,this.first,this.first+this.size);return e===!1?t:t.join(e||this.lineSeparator())},setValue:Te(function(e){var t=y(this.first,0),i=this.first+this.size-1;xr(this,{from:t,to:y(i,N(this,i).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Kr(this.cm,0,0),Ee(this,Ft(t),Z)}),replaceRange:function(e,t,i,r){t=z(this,t),i=i?z(this,i):t,wr(this,e,t,i,r)},getRange:function(e,t,i){var r=Jt(this,z(this,e),z(this,t));return i===!1?r:i===""?r.join(""):r.join(i||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(Pr(this,e))return N(this,e)},getLineNumber:function(e){return re(e)},getLineHandleVisualStart:function(e){return typeof e=="number"&&(e=N(this,e)),ut(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return z(this,e)},getCursor:function(e){var t=this.sel.primary(),i;return e==null||e=="head"?i=t.head:e=="anchor"?i=t.anchor:e=="end"||e=="to"||e===!1?i=t.to():i=t.from(),i},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Te(function(e,t,i){dl(this,z(this,typeof e=="number"?y(e,t||0):e),null,i)}),setSelection:Te(function(e,t,i){dl(this,z(this,e),z(this,t||e),i)}),extendSelection:Te(function(e,t,i){Oi(this,z(this,e),t&&z(this,t),i)}),extendSelections:Te(function(e,t){cl(this,ro(this,e),t)}),extendSelectionsBy:Te(function(e,t){var i=at(this.sel.ranges,e);cl(this,ro(this,i),t)}),setSelections:Te(function(e,t,i){if(!!e.length){for(var r=[],n=0;n<e.length;n++)r[n]=new V(z(this,e[n].anchor),z(this,e[n].head||e[n].anchor));t==null&&(t=Math.min(e.length-1,this.sel.primIndex)),Ee(this,ht(this.cm,r,t),i)}}),addSelection:Te(function(e,t,i){var r=this.sel.ranges.slice(0);r.push(new V(z(this,e),z(this,t||e))),Ee(this,ht(this.cm,r,r.length-1),i)}),getSelection:function(e){for(var t=this.sel.ranges,i,r=0;r<t.length;r++){var n=Jt(this,t[r].from(),t[r].to());i=i?i.concat(n):n}return e===!1?i:i.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],i=this.sel.ranges,r=0;r<i.length;r++){var n=Jt(this,i[r].from(),i[r].to());e!==!1&&(n=n.join(e||this.lineSeparator())),t[r]=n}return t},replaceSelection:function(e,t,i){for(var r=[],n=0;n<this.sel.ranges.length;n++)r[n]=e;this.replaceSelections(r,t,i||"+input")},replaceSelections:Te(function(e,t,i){for(var r=[],n=this.sel,o=0;o<n.ranges.length;o++){var l=n.ranges[o];r[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:i}}for(var a=t&&t!="end"&&vs(this,r,t),s=r.length-1;s>=0;s--)xr(this,r[s]);a?pl(this,a):this.cm&&yr(this.cm)}),undo:Te(function(){Ei(this,"undo")}),redo:Te(function(){Ei(this,"redo")}),undoSelection:Te(function(){Ei(this,"undo",!0)}),redoSelection:Te(function(){Ei(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,i=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var n=0;n<e.undone.length;n++)e.undone[n].ranges||++i;return{undo:t,redo:i}},clearHistory:function(){var e=this;this.history=new Di(this.history),Bt(this,function(t){return t.history=e.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Cr(this.history.done),undone:Cr(this.history.undone)}},setHistory:function(e){var t=this.history=new Di(this.history);t.done=Cr(e.done.slice(0),null,!0),t.undone=Cr(e.undone.slice(0),null,!0)},setGutterMarker:Te(function(e,t,i){return qr(this,e,"gutter",function(r){var n=r.gutterMarkers||(r.gutterMarkers={});return n[t]=i,!i&&et(n)&&(r.gutterMarkers=null),!0})}),clearGutter:Te(function(e){var t=this;this.iter(function(i){i.gutterMarkers&&i.gutterMarkers[e]&&qr(t,i,"gutter",function(){return i.gutterMarkers[e]=null,et(i.gutterMarkers)&&(i.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if(typeof e=="number"){if(!Pr(this,e)||(t=e,e=N(this,e),!e))return null}else if(t=re(e),t==null)return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Te(function(e,t,i){return qr(this,e,t=="gutter"?"gutter":"class",function(r){var n=t=="text"?"textClass":t=="background"?"bgClass":t=="gutter"?"gutterClass":"wrapClass";if(!r[n])r[n]=i;else{if(xt(i).test(r[n]))return!1;r[n]+=" "+i}return!0})}),removeLineClass:Te(function(e,t,i){return qr(this,e,t=="gutter"?"gutter":"class",function(r){var n=t=="text"?"textClass":t=="background"?"bgClass":t=="gutter"?"gutterClass":"wrapClass",o=r[n];if(o)if(i==null)r[n]=null;else{var l=o.match(xt(i));if(!l)return!1;var a=l.index+l[0].length;r[n]=o.slice(0,l.index)+(!l.index||a==o.length?"":" ")+o.slice(a)||null}else return!1;return!0})}),addLineWidget:Te(function(e,t,i){return Ls(this,e,t,i)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,i){return Sr(this,z(this,e),z(this,t),i,i&&i.type||"range")},setBookmark:function(e,t){var i={replacedWith:t&&(t.nodeType==null?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return e=z(this,e),Sr(this,e,e,i,"bookmark")},findMarksAt:function(e){e=z(this,e);var t=[],i=N(this,e.line).markedSpans;if(i)for(var r=0;r<i.length;++r){var n=i[r];(n.from==null||n.from<=e.ch)&&(n.to==null||n.to>=e.ch)&&t.push(n.marker.parent||n.marker)}return t},findMarks:function(e,t,i){e=z(this,e),t=z(this,t);var r=[],n=e.line;return this.iter(e.line,t.line+1,function(o){var l=o.markedSpans;if(l)for(var a=0;a<l.length;a++){var s=l[a];!(s.to!=null&&n==e.line&&e.ch>=s.to||s.from==null&&n!=e.line||s.from!=null&&n==t.line&&s.from>=t.ch)&&(!i||i(s.marker))&&r.push(s.marker.parent||s.marker)}++n}),r},getAllMarks:function(){var e=[];return this.iter(function(t){var i=t.markedSpans;if(i)for(var r=0;r<i.length;++r)i[r].from!=null&&e.push(i[r].marker)}),e},posFromIndex:function(e){var t,i=this.first,r=this.lineSeparator().length;return this.iter(function(n){var o=n.text.length+r;if(o>e)return t=e,!0;e-=o,++i}),z(this,y(i,t))},indexFromPos:function(e){e=z(this,e);var t=e.ch;if(e.line<this.first||e.ch<0)return 0;var i=this.lineSeparator().length;return this.iter(this.first,e.line,function(r){t+=r.text.length+i}),t},copy:function(e){var t=new _e(Vi(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,i=this.first+this.size;e.from!=null&&e.from>t&&(t=e.from),e.to!=null&&e.to<i&&(i=e.to);var r=new _e(Vi(this,t,i),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],Ts(r,Dl(this)),r},unlinkDoc:function(e){if(e instanceof he&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){var i=this.linked[t];if(i.doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Ms(Dl(this));break}}if(e.history==this.history){var r=[e.id];Bt(e,function(n){return r.push(n.id)},!0),e.history=new Di(null),e.history.done=Cr(this.history.done,r),e.history.undone=Cr(this.history.undone,r)}},iterLinkedDocs:function(e){Bt(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Yi(e)},lineSeparator:function(){return this.lineSep||`
`},setDirection:Te(function(e){e!="rtl"&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(t){return t.order=null}),this.cm&&gs(this.cm))})}),_e.prototype.eachLine=_e.prototype.iter;var Nl=0;function Ns(e){var t=this;if(Ol(t),!(Se(t,e)||Mt(t.display,e))){Ue(e),P&&(Nl=+new Date);var i=$t(t,e,!0),r=e.dataTransfer.files;if(!(!i||t.isReadOnly()))if(r&&r.length&&window.FileReader&&window.File)for(var n=r.length,o=Array(n),l=0,a=function(){++l==n&&ke(t,function(){i=z(t.doc,i);var d={from:i,to:i,text:t.doc.splitLines(o.filter(function(v){return v!=null}).join(t.doc.lineSeparator())),origin:"paste"};xr(t.doc,d),pl(t.doc,Ft(z(t.doc,i),z(t.doc,Rt(d))))})()},s=function(d,v){if(t.options.allowDropFileTypes&&F(t.options.allowDropFileTypes,d.type)==-1){a();return}var g=new FileReader;g.onerror=function(){return a()},g.onload=function(){var b=g.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(b)){a();return}o[v]=b,a()},g.readAsText(d)},u=0;u<r.length;u++)s(r[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(i)>-1){t.state.draggingText(e),setTimeout(function(){return t.display.input.focus()},20);return}try{var f=e.dataTransfer.getData("Text");if(f){var h;if(t.state.draggingText&&!t.state.draggingText.copy&&(h=t.listSelections()),Ai(t.doc,Ft(i,i)),h)for(var p=0;p<h.length;++p)wr(t.doc,"",h[p].anchor,h[p].head,"drag");t.replaceSelection(f,"around","paste"),t.display.input.focus()}}catch(d){}}}}function Os(e,t){if(P&&(!e.state.draggingText||+new Date-Nl<100)){Er(t);return}if(!(Se(e,t)||Mt(e.display,t))&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!Y)){var i=E("img",null,null,"position: fixed; left: 0; top: 0;");i.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",Ne&&(i.width=i.height=1,e.display.wrapper.appendChild(i),i._top=i.offsetTop),t.dataTransfer.setDragImage(i,0,0),Ne&&i.parentNode.removeChild(i)}}function As(e,t){var i=$t(e,t);if(!!i){var r=document.createDocumentFragment();xn(e,i,r),e.display.dragCursor||(e.display.dragCursor=E("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),He(e.display.dragCursor,r)}}function Ol(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Al(e){if(!!document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),i=[],r=0;r<t.length;r++){var n=t[r].CodeMirror;n&&i.push(n)}i.length&&i[0].operation(function(){for(var o=0;o<i.length;o++)e(i[o])})}}var Wl=!1;function Ws(){Wl||(Es(),Wl=!0)}function Es(){var e;H(window,"resize",function(){e==null&&(e=setTimeout(function(){e=null,Al(Ps)},100))}),H(window,"blur",function(){return Al(gr)})}function Ps(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Ut={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},ti=0;ti<10;ti++)Ut[ti+48]=Ut[ti+96]=String(ti);for(var Pi=65;Pi<=90;Pi++)Ut[Pi]=String.fromCharCode(Pi);for(var ri=1;ri<=12;ri++)Ut[ri+111]=Ut[ri+63235]="F"+ri;var Nt={};Nt.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Nt.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Nt.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Nt.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Nt.default=j?Nt.macDefault:Nt.pcDefault;function Hs(e){var t=e.split(/-(?!$)/);e=t[t.length-1];for(var i,r,n,o,l=0;l<t.length-1;l++){var a=t[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))i=!0;else if(/^(c|ctrl|control)$/i.test(a))r=!0;else if(/^s(hift)?$/i.test(a))n=!0;else throw new Error("Unrecognized modifier name: "+a)}return i&&(e="Alt-"+e),r&&(e="Ctrl-"+e),o&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function Is(e){var t={};for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(/^(name|fallthrough|(de|at)tach)$/.test(i))continue;if(r=="..."){delete e[i];continue}for(var n=at(i.split(" "),Hs),o=0;o<n.length;o++){var l=void 0,a=void 0;o==n.length-1?(a=n.join(" "),l=r):(a=n.slice(0,o+1).join(" "),l="...");var s=t[a];if(!s)t[a]=l;else if(s!=l)throw new Error("Inconsistent bindings for "+a)}delete e[i]}for(var u in t)e[u]=t[u];return e}function Lr(e,t,i,r){t=Hi(t);var n=t.call?t.call(e,r):t[e];if(n===!1)return"nothing";if(n==="...")return"multi";if(n!=null&&i(n))return"handled";if(t.fallthrough){if(Object.prototype.toString.call(t.fallthrough)!="[object Array]")return Lr(e,t.fallthrough,i,r);for(var o=0;o<t.fallthrough.length;o++){var l=Lr(e,t.fallthrough[o],i,r);if(l)return l}}}function El(e){var t=typeof e=="string"?e:Ut[e.keyCode];return t=="Ctrl"||t=="Alt"||t=="Shift"||t=="Mod"}function Pl(e,t,i){var r=e;return t.altKey&&r!="Alt"&&(e="Alt-"+e),(Ye?t.metaKey:t.ctrlKey)&&r!="Ctrl"&&(e="Ctrl-"+e),(Ye?t.ctrlKey:t.metaKey)&&r!="Mod"&&(e="Cmd-"+e),!i&&t.shiftKey&&r!="Shift"&&(e="Shift-"+e),e}function Hl(e,t){if(Ne&&e.keyCode==34&&e.char)return!1;var i=Ut[e.keyCode];return i==null||e.altGraphKey?!1:(e.keyCode==3&&e.code&&(i=e.code),Pl(i,e,t))}function Hi(e){return typeof e=="string"?Nt[e]:e}function kr(e,t){for(var i=e.doc.sel.ranges,r=[],n=0;n<i.length;n++){for(var o=t(i[n]);r.length&&B(o.from,X(r).to)<=0;){var l=r.pop();if(B(l.from,o.from)<0){o.from=l.from;break}}r.push(o)}je(e,function(){for(var a=r.length-1;a>=0;a--)wr(e.doc,"",r[a].from,r[a].to,"+delete");yr(e)})}function Rn(e,t,i){var r=Je(e.text,t+i,i);return r<0||r>e.text.length?null:r}function Bn(e,t,i){var r=Rn(e,t.ch,i);return r==null?null:new y(t.line,r,i<0?"after":"before")}function zn(e,t,i,r,n){if(e){t.doc.direction=="rtl"&&(n=-n);var o=Lt(i,t.doc.direction);if(o){var l=n<0?X(o):o[0],a=n<0==(l.level==1),s=a?"after":"before",u;if(l.level>0||t.doc.direction=="rtl"){var f=dr(t,i);u=n<0?i.text.length-1:0;var h=Ct(t,f,u).top;u=qe(function(p){return Ct(t,f,p).top==h},n<0==(l.level==1)?l.from:l.to-1,u),s=="before"&&(u=Rn(i,u,1))}else u=n<0?l.to:l.from;return new y(r,u,s)}}return new y(r,n<0?i.text.length:0,n<0?"before":"after")}function Fs(e,t,i,r){var n=Lt(t,e.doc.direction);if(!n)return Bn(t,i,r);i.ch>=t.text.length?(i.ch=t.text.length,i.sticky="before"):i.ch<=0&&(i.ch=0,i.sticky="after");var o=Yt(n,i.ch,i.sticky),l=n[o];if(e.doc.direction=="ltr"&&l.level%2==0&&(r>0?l.to>i.ch:l.from<i.ch))return Bn(t,i,r);var a=function(x,L){return Rn(t,x instanceof y?x.ch:x,L)},s,u=function(x){return e.options.lineWrapping?(s=s||dr(e,t),zo(e,t,s,x)):{begin:0,end:t.text.length}},f=u(i.sticky=="before"?a(i,-1):i.ch);if(e.doc.direction=="rtl"||l.level==1){var h=l.level==1==r<0,p=a(i,h?1:-1);if(p!=null&&(h?p<=l.to&&p<=f.end:p>=l.from&&p>=f.begin)){var d=h?"before":"after";return new y(i.line,p,d)}}var v=function(x,L,w){for(var k=function(le,Me){return Me?new y(i.line,a(le,1),"before"):new y(i.line,le,"after")};x>=0&&x<n.length;x+=L){var A=n[x],O=L>0==(A.level!=1),_=O?w.begin:a(w.end,-1);if(A.from<=_&&_<A.to||(_=O?A.from:a(A.to,-1),w.begin<=_&&_<w.end))return k(_,O)}},g=v(o+r,r,f);if(g)return g;var b=r>0?f.end:a(f.begin,-1);return b!=null&&!(r>0&&b==t.text.length)&&(g=v(r>0?0:n.length-1,r,u(b)),g)?g:null}var ii={selectAll:Cl,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),Z)},killLine:function(e){return kr(e,function(t){if(t.empty()){var i=N(e.doc,t.head.line).text.length;return t.head.ch==i&&t.head.line<e.lastLine()?{from:t.head,to:y(t.head.line+1,0)}:{from:t.head,to:y(t.head.line,i)}}else return{from:t.from(),to:t.to()}})},deleteLine:function(e){return kr(e,function(t){return{from:y(t.from().line,0),to:z(e.doc,y(t.to().line+1,0))}})},delLineLeft:function(e){return kr(e,function(t){return{from:y(t.from().line,0),to:t.from()}})},delWrappedLineLeft:function(e){return kr(e,function(t){var i=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:i},"div");return{from:r,to:t.from()}})},delWrappedLineRight:function(e){return kr(e,function(t){var i=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:i},"div");return{from:t.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(y(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(y(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return Il(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return Fl(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return Rs(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var i=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:i},"div")},se)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var i=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:i},"div")},se)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var i=e.cursorCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:i},"div");return r.ch<e.getLine(r.line).search(/\S/)?Fl(e,t.head):r},se)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("	")},insertSoftTab:function(e){for(var t=[],i=e.listSelections(),r=e.options.tabSize,n=0;n<i.length;n++){var o=i[n].from(),l=D(e.getLine(o.line),o.ch,r);t.push(vt(r-l%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return je(e,function(){for(var t=e.listSelections(),i=[],r=0;r<t.length;r++)if(!!t[r].empty()){var n=t[r].head,o=N(e.doc,n.line).text;if(o){if(n.ch==o.length&&(n=new y(n.line,n.ch-1)),n.ch>0)n=new y(n.line,n.ch+1),e.replaceRange(o.charAt(n.ch-1)+o.charAt(n.ch-2),y(n.line,n.ch-2),n,"+transpose");else if(n.line>e.doc.first){var l=N(e.doc,n.line-1).text;l&&(n=new y(n.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),y(n.line-1,l.length-1),n,"+transpose"))}}i.push(new V(n,n))}e.setSelections(i)})},newlineAndIndent:function(e){return je(e,function(){for(var t=e.listSelections(),i=t.length-1;i>=0;i--)e.replaceRange(e.doc.lineSeparator(),t[i].anchor,t[i].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);yr(e)})},openLine:function(e){return e.replaceSelection(`
`,"start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Il(e,t){var i=N(e.doc,t),r=ut(i);return r!=i&&(t=re(r)),zn(!0,e,r,t,1)}function Rs(e,t){var i=N(e.doc,t),r=xa(i);return r!=i&&(t=re(r)),zn(!0,e,i,t,-1)}function Fl(e,t){var i=Il(e,t.line),r=N(e.doc,i.line),n=Lt(r,e.doc.direction);if(!n||n[0].level==0){var o=Math.max(i.ch,r.text.search(/\S/)),l=t.line==i.line&&t.ch<=o&&t.ch;return y(i.line,l?0:o,i.sticky)}return i}function Ii(e,t,i){if(typeof t=="string"&&(t=ii[t],!t))return!1;e.display.input.ensurePolled();var r=e.display.shift,n=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),i&&(e.display.shift=!1),n=t(e)!=U}finally{e.display.shift=r,e.state.suppressEdits=!1}return n}function Bs(e,t,i){for(var r=0;r<e.state.keyMaps.length;r++){var n=Lr(t,e.state.keyMaps[r],i,e);if(n)return n}return e.options.extraKeys&&Lr(t,e.options.extraKeys,i,e)||Lr(t,e.options.keyMap,i,e)}var zs=new Q;function ni(e,t,i,r){var n=e.state.keySeq;if(n){if(El(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:zs.set(50,function(){e.state.keySeq==n&&(e.state.keySeq=null,e.display.input.reset())}),Rl(e,n+" "+t,i,r))return!0}return Rl(e,t,i,r)}function Rl(e,t,i,r){var n=Bs(e,t,r);return n=="multi"&&(e.state.keySeq=t),n=="handled"&&Le(e,"keyHandled",e,t,i),(n=="handled"||n=="multi")&&(Ue(i),wn(e)),!!n}function Bl(e,t){var i=Hl(t,!0);return i?t.shiftKey&&!e.state.keySeq?ni(e,"Shift-"+i,t,function(r){return Ii(e,r,!0)})||ni(e,i,t,function(r){if(typeof r=="string"?/^go[A-Z]/.test(r):r.motion)return Ii(e,r)}):ni(e,i,t,function(r){return Ii(e,r)}):!1}function Us(e,t,i){return ni(e,"'"+i+"'",t,function(r){return Ii(e,r,!0)})}var Un=null;function zl(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField())&&(t.curOp.focus=Be(Et(t)),!Se(t,e))){P&&I<11&&e.keyCode==27&&(e.returnValue=!1);var i=e.keyCode;t.display.shift=i==16||e.shiftKey;var r=Bl(t,e);Ne&&(Un=r?i:null,!r&&i==88&&!oa&&(j?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),G&&!j&&!r&&i==46&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),i==18&&!/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)&&Ks(t)}}function Ks(e){var t=e.display.lineDiv;wt(t,"CodeMirror-crosshair");function i(r){(r.keyCode==18||!r.altKey)&&(ot(t,"CodeMirror-crosshair"),tt(document,"keyup",i),tt(document,"mouseover",i))}H(document,"keyup",i),H(document,"mouseover",i)}function Ul(e){e.keyCode==16&&(this.doc.sel.shift=!1),Se(this,e)}function Kl(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField())&&!(Mt(t.display,e)||Se(t,e)||e.ctrlKey&&!e.altKey||j&&e.metaKey)){var i=e.keyCode,r=e.charCode;if(Ne&&i==Un){Un=null,Ue(e);return}if(!(Ne&&(!e.which||e.which<10)&&Bl(t,e))){var n=String.fromCharCode(r==null?i:r);n!="\b"&&(Us(t,e,n)||t.display.input.onKeyPress(e))}}}var _s=400,Kn=function(e,t,i){this.time=e,this.pos=t,this.button=i};Kn.prototype.compare=function(e,t,i){return this.time+_s>e&&B(t,this.pos)==0&&i==this.button};var oi,li;function Gs(e,t){var i=+new Date;return li&&li.compare(i,e,t)?(oi=li=null,"triple"):oi&&oi.compare(i,e,t)?(li=new Kn(i,e,t),oi=null,"double"):(oi=new Kn(i,e,t),li=null,"single")}function _l(e){var t=this,i=t.display;if(!(Se(t,e)||i.activeTouch&&i.input.supportsTouch())){if(i.input.ensurePolled(),i.shift=e.shiftKey,Mt(i,e)){J||(i.scroller.draggable=!1,setTimeout(function(){return i.scroller.draggable=!0},100));return}if(!_n(t,e)){var r=$t(t,e),n=$n(e),o=r?Gs(r,n):"single";$(t).focus(),n==1&&t.state.selectingText&&t.state.selectingText(e),!(r&&Zs(t,n,r,o,e))&&(n==1?r?Ys(t,r,o,e):Gi(e)==i.scroller&&Ue(e):n==2?(r&&Oi(t.doc,r),setTimeout(function(){return i.input.focus()},20)):n==3&&(Ar?t.display.input.onContextMenu(e):Sn(t)))}}}function Zs(e,t,i,r,n){var o="Click";return r=="double"?o="Double"+o:r=="triple"&&(o="Triple"+o),o=(t==1?"Left":t==2?"Middle":"Right")+o,ni(e,Pl(o,n),n,function(l){if(typeof l=="string"&&(l=ii[l]),!l)return!1;var a=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),a=l(e,i)!=U}finally{e.state.suppressEdits=!1}return a})}function Xs(e,t,i){var r=e.getOption("configureMouse"),n=r?r(e,t,i):{};if(n.unit==null){var o=ue?i.shiftKey&&i.metaKey:i.altKey;n.unit=o?"rectangle":t=="single"?"char":t=="double"?"word":"line"}return(n.extend==null||e.doc.extend)&&(n.extend=e.doc.extend||i.shiftKey),n.addNew==null&&(n.addNew=j?i.metaKey:i.ctrlKey),n.moveOnDrag==null&&(n.moveOnDrag=!(j?i.altKey:i.ctrlKey)),n}function Ys(e,t,i,r){P?setTimeout(oe(Go,e),0):e.curOp.focus=Be(Et(e));var n=Xs(e,i,r),o=e.doc.sel,l;e.options.dragDrop&&ta&&!e.isReadOnly()&&i=="single"&&(l=o.contains(t))>-1&&(B((l=o.ranges[l]).from(),t)<0||t.xRel>0)&&(B(l.to(),t)>0||t.xRel<0)?Qs(e,r,t,n):Js(e,r,t,n)}function Qs(e,t,i,r){var n=e.display,o=!1,l=ke(e,function(u){J&&(n.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:Sn(e)),tt(n.wrapper.ownerDocument,"mouseup",l),tt(n.wrapper.ownerDocument,"mousemove",a),tt(n.scroller,"dragstart",s),tt(n.scroller,"drop",l),o||(Ue(u),r.addNew||Oi(e.doc,i,null,null,r.extend),J&&!Y||P&&I==9?setTimeout(function(){n.wrapper.ownerDocument.body.focus({preventScroll:!0}),n.input.focus()},20):n.input.focus())}),a=function(u){o=o||Math.abs(t.clientX-u.clientX)+Math.abs(t.clientY-u.clientY)>=10},s=function(){return o=!0};J&&(n.scroller.draggable=!0),e.state.draggingText=l,l.copy=!r.moveOnDrag,H(n.wrapper.ownerDocument,"mouseup",l),H(n.wrapper.ownerDocument,"mousemove",a),H(n.scroller,"dragstart",s),H(n.scroller,"drop",l),e.state.delayingBlurEvent=!0,setTimeout(function(){return n.input.focus()},20),n.scroller.dragDrop&&n.scroller.dragDrop()}function Gl(e,t,i){if(i=="char")return new V(t,t);if(i=="word")return e.findWordAt(t);if(i=="line")return new V(y(t.line,0),z(e.doc,y(t.line+1,0)));var r=i(e,t);return new V(r.from,r.to)}function Js(e,t,i,r){P&&Sn(e);var n=e.display,o=e.doc;Ue(t);var l,a,s=o.sel,u=s.ranges;if(r.addNew&&!r.extend?(a=o.sel.contains(i),a>-1?l=u[a]:l=new V(i,i)):(l=o.sel.primary(),a=o.sel.primIndex),r.unit=="rectangle")r.addNew||(l=new V(i,i)),i=$t(e,t,!0,!0),a=-1;else{var f=Gl(e,i,r.unit);r.extend?l=In(l,f.anchor,f.head,r.extend):l=f}r.addNew?a==-1?(a=u.length,Ee(o,ht(e,u.concat([l]),a),{scroll:!1,origin:"*mouse"})):u.length>1&&u[a].empty()&&r.unit=="char"&&!r.extend?(Ee(o,ht(e,u.slice(0,a).concat(u.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),s=o.sel):Fn(o,a,l,ee):(a=0,Ee(o,new rt([l],0),ee),s=o.sel);var h=i;function p(w){if(B(h,w)!=0)if(h=w,r.unit=="rectangle"){for(var k=[],A=e.options.tabSize,O=D(N(o,i.line).text,i.ch,A),_=D(N(o,w.line).text,w.ch,A),le=Math.min(O,_),Me=Math.max(O,_),ce=Math.min(i.line,w.line),Ve=Math.min(e.lastLine(),Math.max(i.line,w.line));ce<=Ve;ce++){var Ge=N(o,ce).text,Ce=We(Ge,le,A);le==Me?k.push(new V(y(ce,Ce),y(ce,Ce))):Ge.length>Ce&&k.push(new V(y(ce,Ce),y(ce,We(Ge,Me,A))))}k.length||k.push(new V(i,i)),Ee(o,ht(e,s.ranges.slice(0,a).concat(k),a),{origin:"*mouse",scroll:!1}),e.scrollIntoView(w)}else{var Ze=l,Oe=Gl(e,w,r.unit),xe=Ze.anchor,be;B(Oe.anchor,xe)>0?(be=Oe.head,xe=fi(Ze.from(),Oe.anchor)):(be=Oe.anchor,xe=ui(Ze.to(),Oe.head));var de=s.ranges.slice(0);de[a]=qs(e,new V(z(o,xe),be)),Ee(o,ht(e,de,a),ee)}}var d=n.wrapper.getBoundingClientRect(),v=0;function g(w){var k=++v,A=$t(e,w,!0,r.unit=="rectangle");if(!!A)if(B(A,h)!=0){e.curOp.focus=Be(Et(e)),p(A);var O=Li(n,o);(A.line>=O.to||A.line<O.from)&&setTimeout(ke(e,function(){v==k&&g(w)}),150)}else{var _=w.clientY<d.top?-20:w.clientY>d.bottom?20:0;_&&setTimeout(ke(e,function(){v==k&&(n.scroller.scrollTop+=_,g(w))}),50)}}function b(w){e.state.selectingText=!1,v=Infinity,w&&(Ue(w),n.input.focus()),tt(n.wrapper.ownerDocument,"mousemove",x),tt(n.wrapper.ownerDocument,"mouseup",L),o.history.lastSelOrigin=null}var x=ke(e,function(w){w.buttons===0||!$n(w)?b(w):g(w)}),L=ke(e,b);e.state.selectingText=L,H(n.wrapper.ownerDocument,"mousemove",x),H(n.wrapper.ownerDocument,"mouseup",L)}function qs(e,t){var i=t.anchor,r=t.head,n=N(e.doc,i.line);if(B(i,r)==0&&i.sticky==r.sticky)return t;var o=Lt(n);if(!o)return t;var l=Yt(o,i.ch,i.sticky),a=o[l];if(a.from!=i.ch&&a.to!=i.ch)return t;var s=l+(a.from==i.ch==(a.level!=1)?0:1);if(s==0||s==o.length)return t;var u;if(r.line!=i.line)u=(r.line-i.line)*(e.doc.direction=="ltr"?1:-1)>0;else{var f=Yt(o,r.ch,r.sticky),h=f-l||(r.ch-i.ch)*(a.level==1?-1:1);f==s-1||f==s?u=h<0:u=h>0}var p=o[s+(u?-1:0)],d=u==(p.level==1),v=d?p.from:p.to,g=d?"after":"before";return i.ch==v&&i.sticky==g?t:new V(new y(i.line,v,g),r)}function Zl(e,t,i,r){var n,o;if(t.touches)n=t.touches[0].clientX,o=t.touches[0].clientY;else try{n=t.clientX,o=t.clientY}catch(p){return!1}if(n>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&Ue(t);var l=e.display,a=l.lineDiv.getBoundingClientRect();if(o>a.bottom||!it(e,i))return _i(t);o-=a.top-l.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var u=l.gutters.childNodes[s];if(u&&u.getBoundingClientRect().right>=n){var f=qt(e.doc,o),h=e.display.gutterSpecs[s];return ye(e,i,e,f,h.className,t),_i(t)}}}function _n(e,t){return Zl(e,t,"gutterClick",!0)}function Xl(e,t){Mt(e.display,t)||js(e,t)||Se(e,t,"contextmenu")||Ar||e.display.input.onContextMenu(t)}function js(e,t){return it(e,"gutterContextMenu")?Zl(e,t,"gutterContextMenu",!1):!1}function Yl(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),zr(e)}var Tr={toString:function(){return"CodeMirror.Init"}},Ql={},Fi={};function Vs(e){var t=e.optionHandlers;function i(r,n,o,l){e.defaults[r]=n,o&&(t[r]=l?function(a,s,u){u!=Tr&&o(a,s,u)}:o)}e.defineOption=i,e.Init=Tr,i("value","",function(r,n){return r.setValue(n)},!0),i("mode",null,function(r,n){r.doc.modeOption=n,En(r)},!0),i("indentUnit",2,En,!0),i("indentWithTabs",!1),i("smartIndent",!0),i("tabSize",4,function(r){Qr(r),zr(r),Ke(r)},!0),i("lineSeparator",null,function(r,n){if(r.doc.lineSep=n,!!n){var o=[],l=r.doc.first;r.doc.iter(function(s){for(var u=0;;){var f=s.text.indexOf(n,u);if(f==-1)break;u=f+n.length,o.push(y(l,f))}l++});for(var a=o.length-1;a>=0;a--)wr(r.doc,n,o[a],y(o[a].line,o[a].ch+n.length))}}),i("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,function(r,n,o){r.state.specialChars=new RegExp(n.source+(n.test("	")?"":"|	"),"g"),o!=Tr&&r.refresh()}),i("specialCharPlaceholder",Ma,function(r){return r.refresh()},!0),i("electricChars",!0),i("inputStyle",S?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),i("spellcheck",!1,function(r,n){return r.getInputField().spellcheck=n},!0),i("autocorrect",!1,function(r,n){return r.getInputField().autocorrect=n},!0),i("autocapitalize",!1,function(r,n){return r.getInputField().autocapitalize=n},!0),i("rtlMoveVisually",!Or),i("wholeLineUpdateBefore",!0),i("theme","default",function(r){Yl(r),Yr(r)},!0),i("keyMap","default",function(r,n,o){var l=Hi(n),a=o!=Tr&&Hi(o);a&&a.detach&&a.detach(r,l),l.attach&&l.attach(r,a||null)}),i("extraKeys",null),i("configureMouse",null),i("lineWrapping",!1,eu,!0),i("gutters",[],function(r,n){r.display.gutterSpecs=An(n,r.options.lineNumbers),Yr(r)},!0),i("fixedGutter",!0,function(r,n){r.display.gutters.style.left=n?Cn(r.display)+"px":"0",r.refresh()},!0),i("coverGutterNextToScrollbar",!1,function(r){return mr(r)},!0),i("scrollbarStyle","native",function(r){qo(r),mr(r),r.display.scrollbars.setScrollTop(r.doc.scrollTop),r.display.scrollbars.setScrollLeft(r.doc.scrollLeft)},!0),i("lineNumbers",!1,function(r,n){r.display.gutterSpecs=An(r.options.gutters,n),Yr(r)},!0),i("firstLineNumber",1,Yr,!0),i("lineNumberFormatter",function(r){return r},Yr,!0),i("showCursorWhenSelecting",!1,Ur,!0),i("resetSelectionOnContextMenu",!0),i("lineWiseCopyCut",!0),i("pasteLinesPerSelection",!0),i("selectionsMayTouch",!1),i("readOnly",!1,function(r,n){n=="nocursor"&&(gr(r),r.display.input.blur()),r.display.input.readOnlyChanged(n)}),i("screenReaderLabel",null,function(r,n){n=n===""?null:n,r.display.input.screenReaderLabelChanged(n)}),i("disableInput",!1,function(r,n){n||r.display.input.reset()},!0),i("dragDrop",!0,$s),i("allowDropFileTypes",null),i("cursorBlinkRate",530),i("cursorScrollMargin",0),i("cursorHeight",1,Ur,!0),i("singleCursorHeightPerLine",!0,Ur,!0),i("workTime",100),i("workDelay",100),i("flattenSpans",!0,Qr,!0),i("addModeClass",!1,Qr,!0),i("pollInterval",100),i("undoDepth",200,function(r,n){return r.doc.history.undoDepth=n}),i("historyEventDelay",1250),i("viewportMargin",10,function(r){return r.refresh()},!0),i("maxHighlightLength",1e4,Qr,!0),i("moveInputWithCursor",!0,function(r,n){n||r.display.input.resetPosition()}),i("tabindex",null,function(r,n){return r.display.input.getField().tabIndex=n||""}),i("autofocus",null),i("direction","ltr",function(r,n){return r.doc.setDirection(n)},!0),i("phrases",null)}function $s(e,t,i){var r=i&&i!=Tr;if(!t!=!r){var n=e.display.dragFunctions,o=t?H:tt;o(e.display.scroller,"dragstart",n.start),o(e.display.scroller,"dragenter",n.enter),o(e.display.scroller,"dragover",n.over),o(e.display.scroller,"dragleave",n.leave),o(e.display.scroller,"drop",n.drop)}}function eu(e){e.options.lineWrapping?(wt(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(ot(e.display.wrapper,"CodeMirror-wrap"),un(e)),bn(e),Ke(e),zr(e),setTimeout(function(){return mr(e)},100)}function he(e,t){var i=this;if(!(this instanceof he))return new he(e,t);this.options=t=t?K(t):{},K(Ql,t,!1);var r=t.value;typeof r=="string"?r=new _e(r,t.mode,null,t.lineSeparator,t.direction):t.mode&&(r.modeOption=t.mode),this.doc=r;var n=new he.inputStyles[t.inputStyle](this),o=this.display=new ds(e,r,n,t);o.wrapper.CodeMirror=this,Yl(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),qo(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new Q,keySeq:null,specialChars:null},t.autofocus&&!S&&o.input.focus(),P&&I<11&&setTimeout(function(){return i.display.input.reset(!0)},20),tu(this),Ws(),ir(this),this.curOp.forceUpdate=!0,ll(this,r),t.autofocus&&!S||this.hasFocus()?setTimeout(function(){i.hasFocus()&&!i.state.focused&&Ln(i)},20):gr(this);for(var l in Fi)Fi.hasOwnProperty(l)&&Fi[l](this,t[l],Tr);$o(this),t.finishInit&&t.finishInit(this);for(var a=0;a<Gn.length;++a)Gn[a](this);nr(this),J&&t.lineWrapping&&getComputedStyle(o.lineDiv).textRendering=="optimizelegibility"&&(o.lineDiv.style.textRendering="auto")}he.defaults=Ql,he.optionHandlers=Fi;function tu(e){var t=e.display;H(t.scroller,"mousedown",ke(e,_l)),P&&I<11?H(t.scroller,"dblclick",ke(e,function(s){if(!Se(e,s)){var u=$t(e,s);if(!(!u||_n(e,s)||Mt(e.display,s))){Ue(s);var f=e.findWordAt(u);Oi(e.doc,f.anchor,f.head)}}})):H(t.scroller,"dblclick",function(s){return Se(e,s)||Ue(s)}),H(t.scroller,"contextmenu",function(s){return Xl(e,s)}),H(t.input.getField(),"contextmenu",function(s){t.scroller.contains(s.target)||Xl(e,s)});var i,r={end:0};function n(){t.activeTouch&&(i=setTimeout(function(){return t.activeTouch=null},1e3),r=t.activeTouch,r.end=+new Date)}function o(s){if(s.touches.length!=1)return!1;var u=s.touches[0];return u.radiusX<=1&&u.radiusY<=1}function l(s,u){if(u.left==null)return!0;var f=u.left-s.left,h=u.top-s.top;return f*f+h*h>20*20}H(t.scroller,"touchstart",function(s){if(!Se(e,s)&&!o(s)&&!_n(e,s)){t.input.ensurePolled(),clearTimeout(i);var u=+new Date;t.activeTouch={start:u,moved:!1,prev:u-r.end<=300?r:null},s.touches.length==1&&(t.activeTouch.left=s.touches[0].pageX,t.activeTouch.top=s.touches[0].pageY)}}),H(t.scroller,"touchmove",function(){t.activeTouch&&(t.activeTouch.moved=!0)}),H(t.scroller,"touchend",function(s){var u=t.activeTouch;if(u&&!Mt(t,s)&&u.left!=null&&!u.moved&&new Date-u.start<300){var f=e.coordsChar(t.activeTouch,"page"),h;!u.prev||l(u,u.prev)?h=new V(f,f):!u.prev.prev||l(u,u.prev.prev)?h=e.findWordAt(f):h=new V(y(f.line,0),z(e.doc,y(f.line+1,0))),e.setSelection(h.anchor,h.head),e.focus(),Ue(s)}n()}),H(t.scroller,"touchcancel",n),H(t.scroller,"scroll",function(){t.scroller.clientHeight&&(_r(e,t.scroller.scrollTop),tr(e,t.scroller.scrollLeft,!0),ye(e,"scroll",e))}),H(t.scroller,"mousewheel",function(s){return rl(e,s)}),H(t.scroller,"DOMMouseScroll",function(s){return rl(e,s)}),H(t.wrapper,"scroll",function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0}),t.dragFunctions={enter:function(s){Se(e,s)||Er(s)},over:function(s){Se(e,s)||(As(e,s),Er(s))},start:function(s){return Os(e,s)},drop:ke(e,Ns),leave:function(s){Se(e,s)||Ol(e)}};var a=t.input.getField();H(a,"keyup",function(s){return Ul.call(e,s)}),H(a,"keydown",ke(e,zl)),H(a,"keypress",ke(e,Kl)),H(a,"focus",function(s){return Ln(e,s)}),H(a,"blur",function(s){return gr(e,s)})}var Gn=[];he.defineInitHook=function(e){return Gn.push(e)};function ai(e,t,i,r){var n=e.doc,o;i==null&&(i="add"),i=="smart"&&(n.mode.indent?o=Hr(e,t).state:i="prev");var l=e.options.tabSize,a=N(n,t),s=D(a.text,null,l);a.stateAfter&&(a.stateAfter=null);var u=a.text.match(/^\s*/)[0],f;if(!r&&!/\S/.test(a.text))f=0,i="not";else if(i=="smart"&&(f=n.mode.indent(o,a.text.slice(u.length),a.text),f==U||f>150)){if(!r)return;i="prev"}i=="prev"?t>n.first?f=D(N(n,t-1).text,null,l):f=0:i=="add"?f=s+e.options.indentUnit:i=="subtract"?f=s-e.options.indentUnit:typeof i=="number"&&(f=s+i),f=Math.max(0,f);var h="",p=0;if(e.options.indentWithTabs)for(var d=Math.floor(f/l);d;--d)p+=l,h+="	";if(p<f&&(h+=vt(f-p)),h!=u)return wr(n,h,y(t,0),y(t,u.length),"+input"),a.stateAfter=null,!0;for(var v=0;v<n.sel.ranges.length;v++){var g=n.sel.ranges[v];if(g.head.line==t&&g.head.ch<u.length){var b=y(t,u.length);Fn(n,v,new V(b,b));break}}}var ct=null;function Ri(e){ct=e}function Zn(e,t,i,r,n){var o=e.doc;e.display.shift=!1,r||(r=o.sel);var l=+new Date-200,a=n=="paste"||e.state.pasteIncoming>l,s=Yi(t),u=null;if(a&&r.ranges.length>1)if(ct&&ct.text.join(`
`)==t){if(r.ranges.length%ct.text.length==0){u=[];for(var f=0;f<ct.text.length;f++)u.push(o.splitLines(ct.text[f]))}}else s.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(u=at(s,function(x){return[x]}));for(var h=e.curOp.updateInput,p=r.ranges.length-1;p>=0;p--){var d=r.ranges[p],v=d.from(),g=d.to();d.empty()&&(i&&i>0?v=y(v.line,v.ch-i):e.state.overwrite&&!a?g=y(g.line,Math.min(N(o,g.line).text.length,g.ch+X(s).length)):a&&ct&&ct.lineWise&&ct.text.join(`
`)==s.join(`
`)&&(v=g=y(v.line,0)));var b={from:v,to:g,text:u?u[p%u.length]:s,origin:n||(a?"paste":e.state.cutIncoming>l?"cut":"+input")};xr(e.doc,b),Le(e,"inputRead",e,b)}t&&!a&&ql(e,t),yr(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Jl(e,t){var i=e.clipboardData&&e.clipboardData.getData("Text");if(i)return e.preventDefault(),!t.isReadOnly()&&!t.options.disableInput&&t.hasFocus()&&je(t,function(){return Zn(t,i,0,null,"paste")}),!0}function ql(e,t){if(!(!e.options.electricChars||!e.options.smartIndent))for(var i=e.doc.sel,r=i.ranges.length-1;r>=0;r--){var n=i.ranges[r];if(!(n.head.ch>100||r&&i.ranges[r-1].head.line==n.head.line)){var o=e.getModeAt(n.head),l=!1;if(o.electricChars){for(var a=0;a<o.electricChars.length;a++)if(t.indexOf(o.electricChars.charAt(a))>-1){l=ai(e,n.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(N(e.doc,n.head.line).text.slice(0,n.head.ch))&&(l=ai(e,n.head.line,"smart"));l&&Le(e,"electricInput",e,n.head.line)}}}function jl(e){for(var t=[],i=[],r=0;r<e.doc.sel.ranges.length;r++){var n=e.doc.sel.ranges[r].head.line,o={anchor:y(n,0),head:y(n+1,0)};i.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:i}}function Xn(e,t,i,r){e.setAttribute("autocorrect",i?"on":"off"),e.setAttribute("autocapitalize",r?"on":"off"),e.setAttribute("spellcheck",!!t)}function Vl(){var e=E("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=E("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return J?e.style.width="1000px":e.setAttribute("wrap","off"),C&&(e.style.border="1px solid black"),t}function ru(e){var t=e.optionHandlers,i=e.helpers={};e.prototype={constructor:e,focus:function(){$(this).focus(),this.display.input.focus()},setOption:function(r,n){var o=this.options,l=o[r];o[r]==n&&r!="mode"||(o[r]=n,t.hasOwnProperty(r)&&ke(this,t[r])(this,n,l),ye(this,"optionChange",this,r))},getOption:function(r){return this.options[r]},getDoc:function(){return this.doc},addKeyMap:function(r,n){this.state.keyMaps[n?"push":"unshift"](Hi(r))},removeKeyMap:function(r){for(var n=this.state.keyMaps,o=0;o<n.length;++o)if(n[o]==r||n[o].name==r)return n.splice(o,1),!0},addOverlay:Fe(function(r,n){var o=r.token?r:e.getMode(this.options,r);if(o.startState)throw new Error("Overlays may not be stateful.");Zt(this.state.overlays,{mode:o,modeSpec:r,opaque:n&&n.opaque,priority:n&&n.priority||0},function(l){return l.priority}),this.state.modeGen++,Ke(this)}),removeOverlay:Fe(function(r){for(var n=this.state.overlays,o=0;o<n.length;++o){var l=n[o].modeSpec;if(l==r||typeof r=="string"&&l.name==r){n.splice(o,1),this.state.modeGen++,Ke(this);return}}}),indentLine:Fe(function(r,n,o){typeof n!="string"&&typeof n!="number"&&(n==null?n=this.options.smartIndent?"smart":"prev":n=n?"add":"subtract"),Pr(this.doc,r)&&ai(this,r,n,o)}),indentSelection:Fe(function(r){for(var n=this.doc.sel.ranges,o=-1,l=0;l<n.length;l++){var a=n[l];if(a.empty())a.head.line>o&&(ai(this,a.head.line,r,!0),o=a.head.line,l==this.doc.sel.primIndex&&yr(this));else{var s=a.from(),u=a.to(),f=Math.max(o,s.line);o=Math.min(this.lastLine(),u.line-(u.ch?0:1))+1;for(var h=f;h<o;++h)ai(this,h,r);var p=this.doc.sel.ranges;s.ch==0&&n.length==p.length&&p[l].from().ch>0&&Fn(this.doc,l,new V(s,p[l].to()),Z)}}}),getTokenAt:function(r,n){return ao(this,r,n)},getLineTokens:function(r,n){return ao(this,y(r),n,!0)},getTokenTypeAt:function(r){r=z(this.doc,r);var n=no(this,N(this.doc,r.line)),o=0,l=(n.length-1)/2,a=r.ch,s;if(a==0)s=n[2];else for(;;){var u=o+l>>1;if((u?n[u*2-1]:0)>=a)l=u;else if(n[u*2+1]<a)o=u+1;else{s=n[u*2+2];break}}var f=s?s.indexOf("overlay "):-1;return f<0?s:f==0?null:s.slice(0,f-1)},getModeAt:function(r){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(r).state).mode:n},getHelper:function(r,n){return this.getHelpers(r,n)[0]},getHelpers:function(r,n){var o=[];if(!i.hasOwnProperty(n))return o;var l=i[n],a=this.getModeAt(r);if(typeof a[n]=="string")l[a[n]]&&o.push(l[a[n]]);else if(a[n])for(var s=0;s<a[n].length;s++){var u=l[a[n][s]];u&&o.push(u)}else a.helperType&&l[a.helperType]?o.push(l[a.helperType]):l[a.name]&&o.push(l[a.name]);for(var f=0;f<l._global.length;f++){var h=l._global[f];h.pred(a,this)&&F(o,h.val)==-1&&o.push(h.val)}return o},getStateAfter:function(r,n){var o=this.doc;return r=to(o,r==null?o.first+o.size-1:r),Hr(this,r+1,n).state},cursorCoords:function(r,n){var o,l=this.doc.sel.primary();return r==null?o=l.head:typeof r=="object"?o=z(this.doc,r):o=r?l.from():l.to(),ft(this,o,n||"page")},charCoords:function(r,n){return bi(this,z(this.doc,r),n||"page")},coordsChar:function(r,n){return r=Fo(this,r,n||"page"),gn(this,r.left,r.top)},lineAtHeight:function(r,n){return r=Fo(this,{top:r,left:0},n||"page").top,qt(this.doc,r+this.display.viewOffset)},heightAtLine:function(r,n,o){var l=!1,a;if(typeof r=="number"){var s=this.doc.first+this.doc.size-1;r<this.doc.first?r=this.doc.first:r>s&&(r=s,l=!0),a=N(this.doc,r)}else a=r;return Ci(this,a,{top:0,left:0},n||"page",o||l).top+(l?this.doc.height-Tt(a):0)},defaultTextHeight:function(){return pr(this.display)},defaultCharWidth:function(){return vr(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(r,n,o,l,a){var s=this.display;r=ft(this,z(this.doc,r));var u=r.bottom,f=r.left;if(n.style.position="absolute",n.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(n),s.sizer.appendChild(n),l=="over")u=r.top;else if(l=="above"||l=="near"){var h=Math.max(s.wrapper.clientHeight,this.doc.height),p=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth);(l=="above"||r.bottom+n.offsetHeight>h)&&r.top>n.offsetHeight?u=r.top-n.offsetHeight:r.bottom+n.offsetHeight<=h&&(u=r.bottom),f+n.offsetWidth>p&&(f=p-n.offsetWidth)}n.style.top=u+"px",n.style.left=n.style.right="",a=="right"?(f=s.sizer.clientWidth-n.offsetWidth,n.style.right="0px"):(a=="left"?f=0:a=="middle"&&(f=(s.sizer.clientWidth-n.offsetWidth)/2),n.style.left=f+"px"),o&&$a(this,{left:f,top:u,right:f+n.offsetWidth,bottom:u+n.offsetHeight})},triggerOnKeyDown:Fe(zl),triggerOnKeyPress:Fe(Kl),triggerOnKeyUp:Ul,triggerOnMouseDown:Fe(_l),execCommand:function(r){if(ii.hasOwnProperty(r))return ii[r].call(null,this)},triggerElectric:Fe(function(r){ql(this,r)}),findPosH:function(r,n,o,l){var a=1;n<0&&(a=-1,n=-n);for(var s=z(this.doc,r),u=0;u<n&&(s=Yn(this.doc,s,a,o,l),!s.hitSide);++u);return s},moveH:Fe(function(r,n){var o=this;this.extendSelectionsBy(function(l){return o.display.shift||o.doc.extend||l.empty()?Yn(o.doc,l.head,r,n,o.options.rtlMoveVisually):r<0?l.from():l.to()},se)}),deleteH:Fe(function(r,n){var o=this.doc.sel,l=this.doc;o.somethingSelected()?l.replaceSelection("",null,"+delete"):kr(this,function(a){var s=Yn(l,a.head,r,n,!1);return r<0?{from:s,to:a.head}:{from:a.head,to:s}})}),findPosV:function(r,n,o,l){var a=1,s=l;n<0&&(a=-1,n=-n);for(var u=z(this.doc,r),f=0;f<n;++f){var h=ft(this,u,"div");if(s==null?s=h.left:h.left=s,u=$l(this,h,a,o),u.hitSide)break}return u},moveV:Fe(function(r,n){var o=this,l=this.doc,a=[],s=!this.display.shift&&!l.extend&&l.sel.somethingSelected();if(l.extendSelectionsBy(function(f){if(s)return r<0?f.from():f.to();var h=ft(o,f.head,"div");f.goalColumn!=null&&(h.left=f.goalColumn),a.push(h.left);var p=$l(o,h,r,n);return n=="page"&&f==l.sel.primary()&&Tn(o,bi(o,p,"div").top-h.top),p},se),a.length)for(var u=0;u<l.sel.ranges.length;u++)l.sel.ranges[u].goalColumn=a[u]}),findWordAt:function(r){var n=this.doc,o=N(n,r.line).text,l=r.ch,a=r.ch;if(o){var s=this.getHelper(r,"wordChars");(r.sticky=="before"||a==o.length)&&l?--l:++a;for(var u=o.charAt(l),f=Ie(u,s)?function(h){return Ie(h,s)}:/\s/.test(u)?function(h){return/\s/.test(h)}:function(h){return!/\s/.test(h)&&!Ie(h)};l>0&&f(o.charAt(l-1));)--l;for(;a<o.length&&f(o.charAt(a));)++a}return new V(y(r.line,l),y(r.line,a))},toggleOverwrite:function(r){r!=null&&r==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?wt(this.display.cursorDiv,"CodeMirror-overwrite"):ot(this.display.cursorDiv,"CodeMirror-overwrite"),ye(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==Be(Et(this))},isReadOnly:function(){return!!(this.options.readOnly||this.doc.cantEdit)},scrollTo:Fe(function(r,n){Kr(this,r,n)}),getScrollInfo:function(){var r=this.display.scroller;return{left:r.scrollLeft,top:r.scrollTop,height:r.scrollHeight-mt(this)-this.display.barHeight,width:r.scrollWidth-mt(this)-this.display.barWidth,clientHeight:cn(this),clientWidth:jt(this)}},scrollIntoView:Fe(function(r,n){r==null?(r={from:this.doc.sel.primary().head,to:null},n==null&&(n=this.options.cursorScrollMargin)):typeof r=="number"?r={from:y(r,0),to:null}:r.from==null&&(r={from:r,to:null}),r.to||(r.to=r.from),r.margin=n||0,r.from.line!=null?es(this,r):Xo(this,r.from,r.to,r.margin)}),setSize:Fe(function(r,n){var o=this,l=function(s){return typeof s=="number"||/^\d+$/.test(String(s))?s+"px":s};r!=null&&(this.display.wrapper.style.width=l(r)),n!=null&&(this.display.wrapper.style.height=l(n)),this.options.lineWrapping&&Po(this);var a=this.display.viewFrom;this.doc.iter(a,this.display.viewTo,function(s){if(s.widgets){for(var u=0;u<s.widgets.length;u++)if(s.widgets[u].noHScroll){Ht(o,a,"widget");break}}++a}),this.curOp.forceUpdate=!0,ye(this,"refresh",this)}),operation:function(r){return je(this,r)},startOperation:function(){return ir(this)},endOperation:function(){return nr(this)},refresh:Fe(function(){var r=this.display.cachedTextHeight;Ke(this),this.curOp.forceUpdate=!0,zr(this),Kr(this,this.doc.scrollLeft,this.doc.scrollTop),Nn(this.display),(r==null||Math.abs(r-pr(this.display))>.5||this.options.lineWrapping)&&bn(this),ye(this,"refresh",this)}),swapDoc:Fe(function(r){var n=this.doc;return n.cm=null,this.state.selectingText&&this.state.selectingText(),ll(this,r),zr(this),this.display.input.reset(),Kr(this,r.scrollLeft,r.scrollTop),this.curOp.forceScroll=!0,Le(this,"swapDoc",this,n),n}),phrase:function(r){var n=this.options.phrases;return n&&Object.prototype.hasOwnProperty.call(n,r)?n[r]:r},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},sr(e),e.registerHelper=function(r,n,o){i.hasOwnProperty(r)||(i[r]=e[r]={_global:[]}),i[r][n]=o},e.registerGlobalHelper=function(r,n,o,l){e.registerHelper(r,n,l),i[r]._global.push({pred:o,val:l})}}function Yn(e,t,i,r,n){var o=t,l=i,a=N(e,t.line),s=n&&e.direction=="rtl"?-i:i;function u(){var L=t.line+s;return L<e.first||L>=e.first+e.size?!1:(t=new y(L,t.ch,t.sticky),a=N(e,L))}function f(L){var w;if(r=="codepoint"){var k=a.text.charCodeAt(t.ch+(i>0?0:-1));if(isNaN(k))w=null;else{var A=i>0?k>=55296&&k<56320:k>=56320&&k<57343;w=new y(t.line,Math.max(0,Math.min(a.text.length,t.ch+i*(A?2:1))),-i)}}else n?w=Fs(e.cm,a,t,i):w=Bn(a,t,i);if(w==null)if(!L&&u())t=zn(n,e.cm,a,t.line,s);else return!1;else t=w;return!0}if(r=="char"||r=="codepoint")f();else if(r=="column")f(!0);else if(r=="word"||r=="group")for(var h=null,p=r=="group",d=e.cm&&e.cm.getHelper(t,"wordChars"),v=!0;!(i<0&&!f(!v));v=!1){var g=a.text.charAt(t.ch)||`
`,b=Ie(g,d)?"w":p&&g==`
`?"n":!p||/\s/.test(g)?null:"p";if(p&&!v&&!b&&(b="s"),h&&h!=b){i<0&&(i=1,f(),t.sticky="after");break}if(b&&(h=b),i>0&&!f(!v))break}var x=Wi(e,t,o,l,!0);return en(o,x)&&(x.hitSide=!0),x}function $l(e,t,i,r){var n=e.doc,o=t.left,l;if(r=="page"){var a=Math.min(e.display.wrapper.clientHeight,$(e).innerHeight||n(e).documentElement.clientHeight),s=Math.max(a-.5*pr(e.display),3);l=(i>0?t.bottom:t.top)+i*s}else r=="line"&&(l=i>0?t.bottom+3:t.top-3);for(var u;u=gn(e,o,l),!!u.outside;){if(i<0?l<=0:l>=n.height){u.hitSide=!0;break}l+=i*5}return u}var ie=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new Q,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};ie.prototype.init=function(e){var t=this,i=this,r=i.cm,n=i.div=e.lineDiv;n.contentEditable=!0,Xn(n,r.options.spellcheck,r.options.autocorrect,r.options.autocapitalize);function o(a){for(var s=a.target;s;s=s.parentNode){if(s==n)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(s.className))break}return!1}H(n,"paste",function(a){!o(a)||Se(r,a)||Jl(a,r)||I<=11&&setTimeout(ke(r,function(){return t.updateFromDOM()}),20)}),H(n,"compositionstart",function(a){t.composing={data:a.data,done:!1}}),H(n,"compositionupdate",function(a){t.composing||(t.composing={data:a.data,done:!1})}),H(n,"compositionend",function(a){t.composing&&(a.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),H(n,"touchstart",function(){return i.forceCompositionEnd()}),H(n,"input",function(){t.composing||t.readFromDOMSoon()});function l(a){if(!(!o(a)||Se(r,a))){if(r.somethingSelected())Ri({lineWise:!1,text:r.getSelections()}),a.type=="cut"&&r.replaceSelection("",null,"cut");else if(r.options.lineWiseCopyCut){var s=jl(r);Ri({lineWise:!0,text:s.text}),a.type=="cut"&&r.operation(function(){r.setSelections(s.ranges,0,Z),r.replaceSelection("",null,"cut")})}else return;if(a.clipboardData){a.clipboardData.clearData();var u=ct.text.join(`
`);if(a.clipboardData.setData("Text",u),a.clipboardData.getData("Text")==u){a.preventDefault();return}}var f=Vl(),h=f.firstChild;Xn(h),r.display.lineSpace.insertBefore(f,r.display.lineSpace.firstChild),h.value=ct.text.join(`
`);var p=Be(fe(n));Wt(h),setTimeout(function(){r.display.lineSpace.removeChild(f),p.focus(),p==n&&i.showPrimarySelection()},50)}}H(n,"copy",l),H(n,"cut",l)},ie.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},ie.prototype.prepareSelection=function(){var e=_o(this.cm,!1);return e.focus=Be(fe(this.div))==this.div,e},ie.prototype.showSelection=function(e,t){!e||!this.cm.display.view.length||((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},ie.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},ie.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,i=t.doc.sel.primary(),r=i.from(),n=i.to();if(t.display.viewTo==t.display.viewFrom||r.line>=t.display.viewTo||n.line<t.display.viewFrom){e.removeAllRanges();return}var o=Bi(t,e.anchorNode,e.anchorOffset),l=Bi(t,e.focusNode,e.focusOffset);if(!(o&&!o.bad&&l&&!l.bad&&B(fi(o,l),r)==0&&B(ui(o,l),n)==0)){var a=t.display.view,s=r.line>=t.display.viewFrom&&ea(t,r)||{node:a[0].measure.map[2],offset:0},u=n.line<t.display.viewTo&&ea(t,n);if(!u){var f=a[a.length-1].measure,h=f.maps?f.maps[f.maps.length-1]:f.map;u={node:h[h.length-1],offset:h[h.length-2]-h[h.length-3]}}if(!s||!u){e.removeAllRanges();return}var p=e.rangeCount&&e.getRangeAt(0),d;try{d=dt(s.node,s.offset,u.offset,u.node)}catch(v){}d&&(!G&&t.state.focused?(e.collapse(s.node,s.offset),d.collapsed||(e.removeAllRanges(),e.addRange(d))):(e.removeAllRanges(),e.addRange(d)),p&&e.anchorNode==null?e.addRange(p):G&&this.startGracePeriod()),this.rememberSelection()}},ie.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},ie.prototype.showMultipleSelections=function(e){He(this.cm.display.cursorDiv,e.cursors),He(this.cm.display.selectionDiv,e.selection)},ie.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},ie.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return pt(this.div,t)},ie.prototype.focus=function(){this.cm.options.readOnly!="nocursor"&&((!this.selectionInEditor()||Be(fe(this.div))!=this.div)&&this.showSelection(this.prepareSelection(),!0),this.div.focus())},ie.prototype.blur=function(){this.div.blur()},ie.prototype.getField=function(){return this.div},ie.prototype.supportsTouch=function(){return!0},ie.prototype.receivedFocus=function(){var e=this,t=this;this.selectionInEditor()?setTimeout(function(){return e.pollSelection()},20):je(this.cm,function(){return t.cm.curOp.selectionChanged=!0});function i(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,i))}this.polling.set(this.cm.options.pollInterval,i)},ie.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},ie.prototype.pollSelection=function(){if(!(this.readDOMTimeout!=null||this.gracePeriod||!this.selectionChanged())){var e=this.getSelection(),t=this.cm;if(T&&te&&this.cm.display.gutterSpecs.length&&iu(e.anchorNode)){this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),this.focus();return}if(!this.composing){this.rememberSelection();var i=Bi(t,e.anchorNode,e.anchorOffset),r=Bi(t,e.focusNode,e.focusOffset);i&&r&&je(t,function(){Ee(t.doc,Ft(i,r),Z),(i.bad||r.bad)&&(t.curOp.selectionChanged=!0)})}}},ie.prototype.pollContent=function(){this.readDOMTimeout!=null&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e=this.cm,t=e.display,i=e.doc.sel.primary(),r=i.from(),n=i.to();if(r.ch==0&&r.line>e.firstLine()&&(r=y(r.line-1,N(e.doc,r.line-1).length)),n.ch==N(e.doc,n.line).text.length&&n.line<e.lastLine()&&(n=y(n.line+1,0)),r.line<t.viewFrom||n.line>t.viewTo-1)return!1;var o,l,a;r.line==t.viewFrom||(o=er(e,r.line))==0?(l=re(t.view[0].line),a=t.view[0].node):(l=re(t.view[o].line),a=t.view[o-1].node.nextSibling);var s=er(e,n.line),u,f;if(s==t.view.length-1?(u=t.viewTo-1,f=t.lineDiv.lastChild):(u=re(t.view[s+1].line)-1,f=t.view[s+1].node.previousSibling),!a)return!1;for(var h=e.doc.splitLines(nu(e,a,f,l,u)),p=Jt(e.doc,y(l,0),y(u,N(e.doc,u).text.length));h.length>1&&p.length>1;)if(X(h)==X(p))h.pop(),p.pop(),u--;else if(h[0]==p[0])h.shift(),p.shift(),l++;else break;for(var d=0,v=0,g=h[0],b=p[0],x=Math.min(g.length,b.length);d<x&&g.charCodeAt(d)==b.charCodeAt(d);)++d;for(var L=X(h),w=X(p),k=Math.min(L.length-(h.length==1?d:0),w.length-(p.length==1?d:0));v<k&&L.charCodeAt(L.length-v-1)==w.charCodeAt(w.length-v-1);)++v;if(h.length==1&&p.length==1&&l==r.line)for(;d&&d>r.ch&&L.charCodeAt(L.length-v-1)==w.charCodeAt(w.length-v-1);)d--,v++;h[h.length-1]=L.slice(0,L.length-v).replace(/^\u200b+/,""),h[0]=h[0].slice(d).replace(/\u200b+$/,"");var A=y(l,d),O=y(u,p.length?X(p).length-v:0);if(h.length>1||h[0]||B(A,O))return wr(e.doc,h,A,O,"+input"),!0},ie.prototype.ensurePolled=function(){this.forceCompositionEnd()},ie.prototype.reset=function(){this.forceCompositionEnd()},ie.prototype.forceCompositionEnd=function(){!this.composing||(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},ie.prototype.readFromDOMSoon=function(){var e=this;this.readDOMTimeout==null&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing)if(e.composing.done)e.composing=null;else return;e.updateFromDOM()},80))},ie.prototype.updateFromDOM=function(){var e=this;(this.cm.isReadOnly()||!this.pollContent())&&je(this.cm,function(){return Ke(e.cm)})},ie.prototype.setUneditable=function(e){e.contentEditable="false"},ie.prototype.onKeyPress=function(e){e.charCode==0||this.composing||(e.preventDefault(),this.cm.isReadOnly()||ke(this.cm,Zn)(this.cm,String.fromCharCode(e.charCode==null?e.keyCode:e.charCode),0))},ie.prototype.readOnlyChanged=function(e){this.div.contentEditable=String(e!="nocursor")},ie.prototype.onContextMenu=function(){},ie.prototype.resetPosition=function(){},ie.prototype.needsContentAttribute=!0;function ea(e,t){var i=dn(e,t.line);if(!i||i.hidden)return null;var r=N(e.doc,t.line),n=No(i,r,t.line),o=Lt(r,e.doc.direction),l="left";if(o){var a=Yt(o,t.ch);l=a%2?"right":"left"}var s=Wo(n.map,t.ch,l);return s.offset=s.collapse=="right"?s.end:s.start,s}function iu(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function Mr(e,t){return t&&(e.bad=!0),e}function nu(e,t,i,r,n){var o="",l=!1,a=e.doc.lineSeparator(),s=!1;function u(d){return function(v){return v.id==d}}function f(){l&&(o+=a,s&&(o+=a),l=s=!1)}function h(d){d&&(f(),o+=d)}function p(d){if(d.nodeType==1){var v=d.getAttribute("cm-text");if(v){h(v);return}var g=d.getAttribute("cm-marker"),b;if(g){var x=e.findMarks(y(r,0),y(n+1,0),u(+g));x.length&&(b=x[0].find(0))&&h(Jt(e.doc,b.from,b.to).join(a));return}if(d.getAttribute("contenteditable")=="false")return;var L=/^(pre|div|p|li|table|br)$/i.test(d.nodeName);if(!/^br$/i.test(d.nodeName)&&d.textContent.length==0)return;L&&f();for(var w=0;w<d.childNodes.length;w++)p(d.childNodes[w]);/^(pre|p)$/i.test(d.nodeName)&&(s=!0),L&&(l=!0)}else d.nodeType==3&&h(d.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;p(t),t!=i;)t=t.nextSibling,s=!1;return o}function Bi(e,t,i){var r;if(t==e.display.lineDiv){if(r=e.display.lineDiv.childNodes[i],!r)return Mr(e.clipPos(y(e.display.viewTo-1)),!0);t=null,i=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var n=0;n<e.display.view.length;n++){var o=e.display.view[n];if(o.node==r)return ou(o,t,i)}}function ou(e,t,i){var r=e.text.firstChild,n=!1;if(!t||!pt(r,t))return Mr(y(re(e.line),0),!0);if(t==r&&(n=!0,t=r.childNodes[i],i=0,!t)){var o=e.rest?X(e.rest):e.line;return Mr(y(re(o),o.text.length),n)}var l=t.nodeType==3?t:null,a=t;for(!l&&t.childNodes.length==1&&t.firstChild.nodeType==3&&(l=t.firstChild,i&&(i=l.nodeValue.length));a.parentNode!=r;)a=a.parentNode;var s=e.measure,u=s.maps;function f(b,x,L){for(var w=-1;w<(u?u.length:0);w++)for(var k=w<0?s.map:u[w],A=0;A<k.length;A+=3){var O=k[A+2];if(O==b||O==x){var _=re(w<0?e.line:e.rest[w]),le=k[A]+L;return(L<0||O!=b)&&(le=k[A+(L?1:0)]),y(_,le)}}}var h=f(l,a,i);if(h)return Mr(h,n);for(var p=a.nextSibling,d=l?l.nodeValue.length-i:0;p;p=p.nextSibling){if(h=f(p,p.firstChild,0),h)return Mr(y(h.line,h.ch-d),n);d+=p.textContent.length}for(var v=a.previousSibling,g=i;v;v=v.previousSibling){if(h=f(v,v.firstChild,-1),h)return Mr(y(h.line,h.ch+g),n);g+=v.textContent.length}}var pe=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new Q,this.hasSelection=!1,this.composing=null,this.resetting=!1};pe.prototype.init=function(e){var t=this,i=this,r=this.cm;this.createField(e);var n=this.textarea;e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),C&&(n.style.width="0px"),H(n,"input",function(){P&&I>=9&&t.hasSelection&&(t.hasSelection=null),i.poll()}),H(n,"paste",function(l){Se(r,l)||Jl(l,r)||(r.state.pasteIncoming=+new Date,i.fastPoll())});function o(l){if(!Se(r,l)){if(r.somethingSelected())Ri({lineWise:!1,text:r.getSelections()});else if(r.options.lineWiseCopyCut){var a=jl(r);Ri({lineWise:!0,text:a.text}),l.type=="cut"?r.setSelections(a.ranges,null,Z):(i.prevInput="",n.value=a.text.join(`
`),Wt(n))}else return;l.type=="cut"&&(r.state.cutIncoming=+new Date)}}H(n,"cut",o),H(n,"copy",o),H(e.scroller,"paste",function(l){if(!(Mt(e,l)||Se(r,l))){if(!n.dispatchEvent){r.state.pasteIncoming=+new Date,i.focus();return}var a=new Event("paste");a.clipboardData=l.clipboardData,n.dispatchEvent(a)}}),H(e.lineSpace,"selectstart",function(l){Mt(e,l)||Ue(l)}),H(n,"compositionstart",function(){var l=r.getCursor("from");i.composing&&i.composing.range.clear(),i.composing={start:l,range:r.markText(l,r.getCursor("to"),{className:"CodeMirror-composing"})}}),H(n,"compositionend",function(){i.composing&&(i.poll(),i.composing.range.clear(),i.composing=null)})},pe.prototype.createField=function(e){this.wrapper=Vl(),this.textarea=this.wrapper.firstChild;var t=this.cm.options;Xn(this.textarea,t.spellcheck,t.autocorrect,t.autocapitalize)},pe.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},pe.prototype.prepareSelection=function(){var e=this.cm,t=e.display,i=e.doc,r=_o(e);if(e.options.moveInputWithCursor){var n=ft(e,i.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,n.top+l.top-o.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,n.left+l.left-o.left))}return r},pe.prototype.showSelection=function(e){var t=this.cm,i=t.display;He(i.cursorDiv,e.cursors),He(i.selectionDiv,e.selection),e.teTop!=null&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},pe.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing&&e)){var t=this.cm;if(this.resetting=!0,t.somethingSelected()){this.prevInput="";var i=t.getSelection();this.textarea.value=i,t.state.focused&&Wt(this.textarea),P&&I>=9&&(this.hasSelection=i)}else e||(this.prevInput=this.textarea.value="",P&&I>=9&&(this.hasSelection=null));this.resetting=!1}},pe.prototype.getField=function(){return this.textarea},pe.prototype.supportsTouch=function(){return!1},pe.prototype.focus=function(){if(this.cm.options.readOnly!="nocursor"&&(!S||Be(fe(this.textarea))!=this.textarea))try{this.textarea.focus()}catch(e){}},pe.prototype.blur=function(){this.textarea.blur()},pe.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},pe.prototype.receivedFocus=function(){this.slowPoll()},pe.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},pe.prototype.fastPoll=function(){var e=!1,t=this;t.pollingFast=!0;function i(){var r=t.poll();!r&&!e?(e=!0,t.polling.set(60,i)):(t.pollingFast=!1,t.slowPoll())}t.polling.set(20,i)},pe.prototype.poll=function(){var e=this,t=this.cm,i=this.textarea,r=this.prevInput;if(this.contextMenuPending||this.resetting||!t.state.focused||na(i)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var n=i.value;if(n==r&&!t.somethingSelected())return!1;if(P&&I>=9&&this.hasSelection===n||j&&/[\uf700-\uf7ff]/.test(n))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=n.charCodeAt(0);if(o==8203&&!r&&(r="\u200B"),o==8666)return this.reset(),this.cm.execCommand("undo")}for(var l=0,a=Math.min(r.length,n.length);l<a&&r.charCodeAt(l)==n.charCodeAt(l);)++l;return je(t,function(){Zn(t,n.slice(l),r.length-l,null,e.composing?"*compose":null),n.length>1e3||n.indexOf(`
`)>-1?i.value=e.prevInput="":e.prevInput=n,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},pe.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},pe.prototype.onKeyPress=function(){P&&I>=9&&(this.hasSelection=null),this.fastPoll()},pe.prototype.onContextMenu=function(e){var t=this,i=t.cm,r=i.display,n=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=$t(i,e),l=r.scroller.scrollTop;if(!o||Ne)return;var a=i.options.resetSelectionOnContextMenu;a&&i.doc.sel.contains(o)==-1&&ke(i,Ee)(i.doc,Ft(o),Z);var s=n.style.cssText,u=t.wrapper.style.cssText,f=t.wrapper.offsetParent.getBoundingClientRect();t.wrapper.style.cssText="position: static",n.style.cssText=`position: absolute; width: 30px; height: 30px;
      top: `+(e.clientY-f.top-5)+"px; left: "+(e.clientX-f.left-5)+`px;
      z-index: 1000; background: `+(P?"rgba(255, 255, 255, .05)":"transparent")+`;
      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);`;var h;J&&(h=n.ownerDocument.defaultView.scrollY),r.input.focus(),J&&n.ownerDocument.defaultView.scrollTo(null,h),r.input.reset(),i.somethingSelected()||(n.value=t.prevInput=" "),t.contextMenuPending=d,r.selForContextMenu=i.doc.sel,clearTimeout(r.detectingSelectAll);function p(){if(n.selectionStart!=null){var g=i.somethingSelected(),b="\u200B"+(g?n.value:"");n.value="\u21DA",n.value=b,t.prevInput=g?"":"\u200B",n.selectionStart=1,n.selectionEnd=b.length,r.selForContextMenu=i.doc.sel}}function d(){if(t.contextMenuPending==d&&(t.contextMenuPending=!1,t.wrapper.style.cssText=u,n.style.cssText=s,P&&I<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=l),n.selectionStart!=null)){(!P||P&&I<9)&&p();var g=0,b=function(){r.selForContextMenu==i.doc.sel&&n.selectionStart==0&&n.selectionEnd>0&&t.prevInput=="\u200B"?ke(i,Cl)(i):g++<10?r.detectingSelectAll=setTimeout(b,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(b,200)}}if(P&&I>=9&&p(),Ar){Er(e);var v=function(){tt(window,"mouseup",v),setTimeout(d,20)};H(window,"mouseup",v)}else setTimeout(d,50)},pe.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled=e=="nocursor",this.textarea.readOnly=!!e},pe.prototype.setUneditable=function(){},pe.prototype.needsContentAttribute=!1;function lu(e,t){if(t=t?K(t):{},t.value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),t.autofocus==null){var i=Be(fe(e));t.autofocus=i==e||e.getAttribute("autofocus")!=null&&i==document.body}function r(){e.value=a.getValue()}var n;if(e.form&&(H(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var o=e.form;n=o.submit;try{var l=o.submit=function(){r(),o.submit=n,o.submit(),o.submit=l}}catch(s){}}t.finishInit=function(s){s.save=r,s.getTextArea=function(){return e},s.toTextArea=function(){s.toTextArea=isNaN,r(),e.parentNode.removeChild(s.getWrapperElement()),e.style.display="",e.form&&(tt(e.form,"submit",r),!t.leaveSubmitMethodAlone&&typeof e.form.submit=="function"&&(e.form.submit=n))}},e.style.display="none";var a=he(function(s){return e.parentNode.insertBefore(s,e.nextSibling)},t);return a}function au(e){e.off=tt,e.on=H,e.wheelEventPixels=ps,e.Doc=_e,e.splitLines=Yi,e.countColumn=D,e.findColumn=We,e.isWordChar=we,e.Pass=U,e.signal=ye,e.Line=hr,e.changeEnd=Rt,e.scrollbarModel=Jo,e.Pos=y,e.cmpPos=B,e.modes=Ji,e.mimeModes=ur,e.resolveMode=si,e.getMode=qi,e.modeExtensions=fr,e.extendMode=ua,e.copyState=Qt,e.startState=eo,e.innerMode=ji,e.commands=ii,e.keyMap=Nt,e.keyName=Hl,e.isModifierKey=El,e.lookupKey=Lr,e.normalizeKeyMap=Is,e.StringStream=me,e.SharedTextMarker=ei,e.TextMarker=zt,e.LineWidget=$r,e.e_preventDefault=Ue,e.e_stopPropagation=Vn,e.e_stop=Er,e.addClass=wt,e.contains=pt,e.rmClass=ot,e.keyNames=Ut}Vs(he),ru(he);var su="iter insert remove copy getEditor constructor".split(" ");for(var zi in _e.prototype)_e.prototype.hasOwnProperty(zi)&&F(su,zi)<0&&(he.prototype[zi]=function(e){return function(){return e.apply(this.doc,arguments)}}(_e.prototype[zi]));return sr(_e),he.inputStyles={textarea:pe,contenteditable:ie},he.defineMode=function(e){!he.defaults.mode&&e!="null"&&(he.defaults.mode=e),aa.apply(this,arguments)},he.defineMIME=sa,he.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),he.defineMIME("text/plain","null"),he.defineExtension=function(e,t){he.prototype[e]=t},he.defineDocExtension=function(e,t){_e.prototype[e]=t},he.fromTextArea=lu,au(he),he.version="5.65.16",he})},63138:function(_t,ae,R){(function(G){G(R(4631))})(function(G){"use strict";G.defineMode("spreadsheet",function(){return{startState:function(){return{stringType:null,stack:[]}},token:function(M,ne){if(!!M){switch(ne.stack.length===0&&(M.peek()=='"'||M.peek()=="'")&&(ne.stringType=M.peek(),M.next(),ne.stack.unshift("string")),ne.stack[0]){case"string":for(;ne.stack[0]==="string"&&!M.eol();)M.peek()===ne.stringType?(M.next(),ne.stack.shift()):M.peek()==="\\"?(M.next(),M.next()):M.match(/^.[^\\\"\']*/);return"string";case"characterClass":for(;ne.stack[0]==="characterClass"&&!M.eol();)M.match(/^[^\]\\]+/)||M.match(/^\\./)||ne.stack.shift();return"operator"}var Ae=M.peek();switch(Ae){case"[":return M.next(),ne.stack.unshift("characterClass"),"bracket";case":":return M.next(),"operator";case"\\":return M.match(/\\[a-z]+/)?"string-2":(M.next(),"atom");case".":case",":case";":case"*":case"-":case"+":case"^":case"<":case"/":case"=":return M.next(),"atom";case"$":return M.next(),"builtin"}return M.match(/\d+/)?M.match(/^\w+/)?"error":"number":M.match(/^[a-zA-Z_]\w*/)?M.match(/(?=[\(.])/,!1)?"keyword":"variable-2":["[","]","(",")","{","}"].indexOf(Ae)!=-1?(M.next(),"bracket"):(M.eatSpace()||M.next(),null)}}}}),G.defineMIME("text/x-spreadsheet","spreadsheet")})},29656:function(_t,ae,R){"use strict";var G;function M(){return M=Object.assign||function(Y){for(var m=1;m<arguments.length;m++){var c=arguments[m];for(var C in c)Object.prototype.hasOwnProperty.call(c,C)&&(Y[C]=c[C])}return Y},M.apply(this,arguments)}function ne(Y){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?ne=function(c){return typeof c}:ne=function(c){return c&&typeof Symbol=="function"&&c.constructor===Symbol&&c!==Symbol.prototype?"symbol":typeof c},ne(Y)}var Ae=function(){var Y=function(c,C){return Y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(T,S){T.__proto__=S}||function(T,S){for(var j in S)S.hasOwnProperty(j)&&(T[j]=S[j])},Y(c,C)};return function(m,c){Y(m,c);function C(){this.constructor=m}m.prototype=c===null?Object.create(c):(C.prototype=c.prototype,new C)}}();G={value:!0},ae.Rt=G=void 0;var P=R(67294),I=typeof navigator=="undefined"||typeof R.g!="undefined"&&R.g.PREVENT_CODEMIRROR_RENDER===!0,J;I||(J=R(4631));var W=function(){function Y(){}return Y.equals=function(m,c){var C=this,T=Object.keys,S=ne(m),j=ne(c);return m&&c&&S==="object"&&S===j?T(m).length===T(c).length&&T(m).every(function(ue){return C.equals(m[ue],c[ue])}):m===c},Y}(),te=function(){function Y(m,c){this.editor=m,this.props=c}return Y.prototype.delegateCursor=function(m,c,C){var T=this.editor.getDoc();C&&this.editor.focus(),c?T.setCursor(m):T.setCursor(m,null,{scroll:!1})},Y.prototype.delegateScroll=function(m){this.editor.scrollTo(m.x,m.y)},Y.prototype.delegateSelection=function(m,c){var C=this.editor.getDoc();C.setSelections(m),c&&this.editor.focus()},Y.prototype.apply=function(m){m&&m.selection&&m.selection.ranges&&this.delegateSelection(m.selection.ranges,m.selection.focus||!1),m&&m.cursor&&this.delegateCursor(m.cursor,m.autoScroll||!1,this.editor.getOption("autofocus")||!1),m&&m.scroll&&this.delegateScroll(m.scroll)},Y.prototype.applyNext=function(m,c,C){m&&m.selection&&m.selection.ranges&&c&&c.selection&&c.selection.ranges&&!W.equals(m.selection.ranges,c.selection.ranges)&&this.delegateSelection(c.selection.ranges,c.selection.focus||!1),m&&m.cursor&&c&&c.cursor&&!W.equals(m.cursor,c.cursor)&&this.delegateCursor(C.cursor||c.cursor,c.autoScroll||!1,c.autoCursor||!1),m&&m.scroll&&c&&c.scroll&&!W.equals(m.scroll,c.scroll)&&this.delegateScroll(c.scroll)},Y.prototype.applyUserDefined=function(m,c){c&&c.cursor&&this.delegateCursor(c.cursor,m.autoScroll||!1,this.editor.getOption("autofocus")||!1)},Y.prototype.wire=function(m){var c=this;Object.keys(m||{}).filter(function(C){return/^on/.test(C)}).forEach(function(C){switch(C){case"onBlur":c.editor.on("blur",function(T,S){c.props.onBlur(c.editor,S)});break;case"onContextMenu":{c.editor.on("contextmenu",function(T,S){c.props.onContextMenu(c.editor,S)});break}case"onCopy":{c.editor.on("copy",function(T,S){c.props.onCopy(c.editor,S)});break}case"onCursor":c.editor.on("cursorActivity",function(T){c.props.onCursor(c.editor,c.editor.getDoc().getCursor())});break;case"onCursorActivity":c.editor.on("cursorActivity",function(T){c.props.onCursorActivity(c.editor)});break;case"onCut":{c.editor.on("cut",function(T,S){c.props.onCut(c.editor,S)});break}case"onDblClick":{c.editor.on("dblclick",function(T,S){c.props.onDblClick(c.editor,S)});break}case"onDragEnter":c.editor.on("dragenter",function(T,S){c.props.onDragEnter(c.editor,S)});break;case"onDragLeave":{c.editor.on("dragleave",function(T,S){c.props.onDragLeave(c.editor,S)});break}case"onDragOver":c.editor.on("dragover",function(T,S){c.props.onDragOver(c.editor,S)});break;case"onDragStart":{c.editor.on("dragstart",function(T,S){c.props.onDragStart(c.editor,S)});break}case"onDrop":c.editor.on("drop",function(T,S){c.props.onDrop(c.editor,S)});break;case"onFocus":c.editor.on("focus",function(T,S){c.props.onFocus(c.editor,S)});break;case"onGutterClick":c.editor.on("gutterClick",function(T,S,j,ue){c.props.onGutterClick(c.editor,S,j,ue)});break;case"onInputRead":c.editor.on("inputRead",function(T,S){c.props.onInputRead(c.editor,S)});break;case"onKeyDown":c.editor.on("keydown",function(T,S){c.props.onKeyDown(c.editor,S)});break;case"onKeyHandled":c.editor.on("keyHandled",function(T,S,j){c.props.onKeyHandled(c.editor,S,j)});break;case"onKeyPress":c.editor.on("keypress",function(T,S){c.props.onKeyPress(c.editor,S)});break;case"onKeyUp":c.editor.on("keyup",function(T,S){c.props.onKeyUp(c.editor,S)});break;case"onMouseDown":{c.editor.on("mousedown",function(T,S){c.props.onMouseDown(c.editor,S)});break}case"onPaste":{c.editor.on("paste",function(T,S){c.props.onPaste(c.editor,S)});break}case"onRenderLine":{c.editor.on("renderLine",function(T,S,j){c.props.onRenderLine(c.editor,S,j)});break}case"onScroll":c.editor.on("scroll",function(T){c.props.onScroll(c.editor,c.editor.getScrollInfo())});break;case"onSelection":c.editor.on("beforeSelectionChange",function(T,S){c.props.onSelection(c.editor,S)});break;case"onTouchStart":{c.editor.on("touchstart",function(T,S){c.props.onTouchStart(c.editor,S)});break}case"onUpdate":c.editor.on("update",function(T){c.props.onUpdate(c.editor)});break;case"onViewportChange":c.editor.on("viewportChange",function(T,S,j){c.props.onViewportChange(c.editor,S,j)});break}})},Y}(),ve=function(Y){Ae(m,Y);function m(c){var C=Y.call(this,c)||this;return I||(C.applied=!1,C.appliedNext=!1,C.appliedUserDefined=!1,C.deferred=null,C.emulating=!1,C.hydrated=!1,C.initCb=function(){C.props.editorDidConfigure&&C.props.editorDidConfigure(C.editor)},C.mounted=!1),C}return m.prototype.hydrate=function(c){var C=this,T=c&&c.options?c.options:{},S=M({},J.defaults,this.editor.options,T),j=Object.keys(S).some(function(ue){return C.editor.getOption(ue)!==S[ue]});j&&Object.keys(S).forEach(function(ue){T.hasOwnProperty(ue)&&C.editor.getOption(ue)!==S[ue]&&(C.editor.setOption(ue,S[ue]),C.mirror.setOption(ue,S[ue]))}),this.hydrated||(this.deferred?this.resolveChange(c.value):this.initChange(c.value||"")),this.hydrated=!0},m.prototype.initChange=function(c){this.emulating=!0;var C=this.editor.getDoc(),T=C.lastLine(),S=C.getLine(C.lastLine()).length;C.replaceRange(c||"",{line:0,ch:0},{line:T,ch:S}),this.mirror.setValue(c),C.clearHistory(),this.mirror.clearHistory(),this.emulating=!1},m.prototype.resolveChange=function(c){this.emulating=!0;var C=this.editor.getDoc();if(this.deferred.origin==="undo"?C.undo():this.deferred.origin==="redo"?C.redo():C.replaceRange(this.deferred.text,this.deferred.from,this.deferred.to,this.deferred.origin),c&&c!==C.getValue()){var T=C.getCursor();C.setValue(c),C.setCursor(T)}this.emulating=!1,this.deferred=null},m.prototype.mirrorChange=function(c){var C=this.editor.getDoc();return c.origin==="undo"?(C.setHistory(this.mirror.getHistory()),this.mirror.undo()):c.origin==="redo"?(C.setHistory(this.mirror.getHistory()),this.mirror.redo()):this.mirror.replaceRange(c.text,c.from,c.to,c.origin),this.mirror.getValue()},m.prototype.componentDidMount=function(){var c=this;I||(this.props.defineMode&&this.props.defineMode.name&&this.props.defineMode.fn&&J.defineMode(this.props.defineMode.name,this.props.defineMode.fn),this.editor=J(this.ref,this.props.options),this.shared=new te(this.editor,this.props),this.mirror=J(function(){},this.props.options),this.editor.on("electricInput",function(){c.mirror.setHistory(c.editor.getDoc().getHistory())}),this.editor.on("cursorActivity",function(){c.mirror.setCursor(c.editor.getDoc().getCursor())}),this.editor.on("beforeChange",function(C,T){if(!c.emulating){T.cancel(),c.deferred=T;var S=c.mirrorChange(c.deferred);c.props.onBeforeChange&&c.props.onBeforeChange(c.editor,c.deferred,S)}}),this.editor.on("change",function(C,T){!c.mounted||c.props.onChange&&c.props.onChange(c.editor,T,c.editor.getValue())}),this.hydrate(this.props),this.shared.apply(this.props),this.applied=!0,this.mounted=!0,this.shared.wire(this.props),this.editor.getOption("autofocus")&&this.editor.focus(),this.props.editorDidMount&&this.props.editorDidMount(this.editor,this.editor.getValue(),this.initCb))},m.prototype.componentDidUpdate=function(c){if(!I){var C={cursor:null};this.props.value!==c.value&&(this.hydrated=!1),!this.props.autoCursor&&this.props.autoCursor!==void 0&&(C.cursor=this.editor.getDoc().getCursor()),this.hydrate(this.props),this.appliedNext||(this.shared.applyNext(c,this.props,C),this.appliedNext=!0),this.shared.applyUserDefined(c,C),this.appliedUserDefined=!0}},m.prototype.componentWillUnmount=function(){I||this.props.editorWillUnmount&&this.props.editorWillUnmount(J)},m.prototype.shouldComponentUpdate=function(c,C){return!I},m.prototype.render=function(){var c=this;if(I)return null;var C=this.props.className?"react-codemirror2 "+this.props.className:"react-codemirror2";return P.createElement("div",{className:C,ref:function(S){return c.ref=S}})},m}(P.Component);G=ve;var Ne=function(Y){Ae(m,Y);function m(c){var C=Y.call(this,c)||this;return I||(C.applied=!1,C.appliedUserDefined=!1,C.continueChange=!1,C.detached=!1,C.hydrated=!1,C.initCb=function(){C.props.editorDidConfigure&&C.props.editorDidConfigure(C.editor)},C.mounted=!1,C.onBeforeChangeCb=function(){C.continueChange=!0}),C}return m.prototype.hydrate=function(c){var C=this,T=c&&c.options?c.options:{},S=M({},J.defaults,this.editor.options,T),j=Object.keys(S).some(function(Ye){return C.editor.getOption(Ye)!==S[Ye]});if(j&&Object.keys(S).forEach(function(Ye){T.hasOwnProperty(Ye)&&C.editor.getOption(Ye)!==S[Ye]&&C.editor.setOption(Ye,S[Ye])}),!this.hydrated){var ue=this.editor.getDoc(),Or=ue.lastLine(),nt=ue.getLine(ue.lastLine()).length;ue.replaceRange(c.value||"",{line:0,ch:0},{line:Or,ch:nt})}this.hydrated=!0},m.prototype.componentDidMount=function(){var c=this;I||(this.detached=this.props.detach===!0,this.props.defineMode&&this.props.defineMode.name&&this.props.defineMode.fn&&J.defineMode(this.props.defineMode.name,this.props.defineMode.fn),this.editor=J(this.ref,this.props.options),this.shared=new te(this.editor,this.props),this.editor.on("beforeChange",function(C,T){c.props.onBeforeChange&&c.props.onBeforeChange(c.editor,T,c.editor.getValue(),c.onBeforeChangeCb)}),this.editor.on("change",function(C,T){!c.mounted||!c.props.onChange||(c.props.onBeforeChange?c.continueChange&&c.props.onChange(c.editor,T,c.editor.getValue()):c.props.onChange(c.editor,T,c.editor.getValue()))}),this.hydrate(this.props),this.shared.apply(this.props),this.applied=!0,this.mounted=!0,this.shared.wire(this.props),this.editor.getDoc().clearHistory(),this.props.editorDidMount&&this.props.editorDidMount(this.editor,this.editor.getValue(),this.initCb))},m.prototype.componentDidUpdate=function(c){if(this.detached&&this.props.detach===!1&&(this.detached=!1,c.editorDidAttach&&c.editorDidAttach(this.editor)),!this.detached&&this.props.detach===!0&&(this.detached=!0,c.editorDidDetach&&c.editorDidDetach(this.editor)),!(I||this.detached)){var C={cursor:null};this.props.value!==c.value&&(this.hydrated=!1,this.applied=!1,this.appliedUserDefined=!1),!c.autoCursor&&c.autoCursor!==void 0&&(C.cursor=this.editor.getDoc().getCursor()),this.hydrate(this.props),this.applied||(this.shared.apply(c),this.applied=!0),this.appliedUserDefined||(this.shared.applyUserDefined(c,C),this.appliedUserDefined=!0)}},m.prototype.componentWillUnmount=function(){I||this.props.editorWillUnmount&&this.props.editorWillUnmount(J)},m.prototype.shouldComponentUpdate=function(c,C){var T=!0;return I&&(T=!1),this.detached&&c.detach&&(T=!1),T},m.prototype.render=function(){var c=this;if(I)return null;var C=this.props.className?"react-codemirror2 "+this.props.className:"react-codemirror2";return P.createElement("div",{className:C,ref:function(S){return c.ref=S}})},m}(P.Component);ae.Rt=Ne}}]);
