---
id: public_query
title: 公开查询
---
import myImageUrl from '../../static/img/public_query_1.jpg';

能实现和[金山快查](https://www.kdocs.cn/l/ctXRLNFXVlqv)相同的功能，甚至比他更强大。

主要功能特性：

1. 支持大数据量 excel 导入，实测导入 10 万数据查询、修改都非常流畅
2. 可以对同一个问卷创建多个公开查询
3. 支持自由组合多个查询条件
4. 支持查询项权限过滤，每个查询项支持三种模式，只读、允许修改、隐藏，支持查询结果查看、修改
5. 支持查询结果，图片上传、签名等

:::tip 原理

导入 excel 之后，会将 excel的行头和数据分别解析为问卷和答案，目前支持将所有的都解析成填空题。导入之后，你可以通过问卷编辑器继续编辑问卷，设置字段属性，甚至添加签名题、图片上传等。

所以卷王的可发挥空间更大。

:::

演示地址：

- [根据学号查询个人成绩，并可以根据个人成绩提示不同的信息](https://wj.surveyking.cn/s/T8z5XS/result/vglkl5)
- [根据学号和姓名查询并可以修改邮箱](https://wj.surveyking.cn/s/T8z5XS/result/LPD0Di)
- [根据学号查询个人成绩，确认完毕之后签名才能提交](https://wj.surveyking.cn/s/T8z5XS/result/IuMISu)
- [直接根据发布的链接查询自己的信息，无需手动输入查询信息](https://wj.surveyking.cn/s/T8z5XS/result/GRoS4J?7fFhxc=1000)
- 更多玩法等待你去发现，👏

## 如何设置

### 公开查询设置页面说明

创建的流程为：问卷 -> 设置 -> 公开查询设置 -> 新增查询页面

<img src={myImageUrl} alt="Example banner" width="480" />;

- **页面标题**，设置查询页面的标题
- **页面查询描述信息**，设置查询页面的描述
- **查询条件**，点击设置可以选择多个查询条件，必须满足所有的查询条件才能输出结果
- **查询结果**，可以控制每一个字段的权限，目前支持三种模式
  - 可编辑，可以在查询结果里面修改该字段
  - 仅可见，该字段在查询结果里面只读
  - 隐藏，该字段不会再查询结果里面显示
- **查看权限**，可以添加密码，这样在查询表单里面只有输入正确的密码才会显示结果
- **链接有效期**，该查询链接只有在该时间段内才能打开

## 更多玩法

### 公开查询数据需要签名确认

:::warning 如何添加签名？
通过 Excel 导入数据创建完公开查询表单，我们想让查询的人签名确认表单数据，可以做到吗？

当然没问题，使用卷王可以很容易做到。
:::

- 首先我们进入到问卷编辑页面，可以看到导入的问题，此时我们点击或者拖拽将问题添加或者插入到问卷里面。
- 然后呢，点开公开查询设置页面，在查询结果列里面我们可以看到刚才添加的签名题
- 设置签名题为可编辑的
- 开启公开查询，此时查询的人就可以在查询结果里面签名确认了

### 如何通过 url 参数来公开查询

:::warning 如何直接通过链接查询而无需手动输入查询条件？
有的时候我们不想让用户手动输入查询条件，用户直接点击链接就能获取自己的信息。

卷王提供了这个功能~
:::

只需要链接后面添加参数就可以了，支持多个参数，参数名为问题的id，参数值为具体的值。

> <https://wj.surveyking.cn/s/T8z5XS/result/GRoS4J?7fFhxc=1000>

### 添加签名

用户查询完个人信息之后，我们需要他签名确认

## [视频演示如何导入Excel快速创建公开查询](https://www.bilibili.com/video/BV17g411o77v/)

<iframe src="https://player.bilibili.com/player.html?aid=511848771&bvid=BV17g411o77v&cid=725883790&page=1" scrolling="no" border="0" frameBorder="no" framespacing="0" allowFullScreen width="800" height="600"> </iframe>
