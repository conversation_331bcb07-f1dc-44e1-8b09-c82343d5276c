(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[6853],{80341:function(){},56853:function(ue,W,s){"use strict";s.r(W),s.d(W,{default:function(){return re}});var x=s(94657),E=s(1615),m=s(67294),$=s(11849),Z=s(39428),z=s(83279),l=s(3182),Q=s(54029),A=s(79166),ie=s(402),k=s(56118),q=s(3980),_=s(27484),Y=s.n(_),v=s(85893),ee=k.Z.Text,te=function(w){var H=w.itemKey,D=w.mode,a=w.type,e=(0,m.useState)([]),c=(0,x.Z)(e,2),n=c[0],r=c[1],o=(0,m.useRef)({current:1,pageSize:10}),p=function(t){var i=t.status;return D==="exam"&&a==="history"?(0,v.jsxs)("div",{style:{textAlign:"right"},children:[(0,v.jsx)(ee,{type:"danger",strong:!0,style:{fontSize:18,marginRight:5},children:t.examTotal}),(0,v.jsx)("span",{style:{fontSize:12},children:"\u5206"}),(0,v.jsx)("div",{style:{fontSize:12,color:"#8c8c8c"},children:Y()(t.endTime).format("YYYY-MM-DD HH:mm")})]}):D==="survey"&&a==="history"?(0,v.jsx)(v.Fragment,{children:(0,v.jsx)("div",{style:{fontSize:12,color:"#8c8c8c"},children:Y()(t.endTime).format("YYYY-MM-DD HH:mm")})}):i===0?(0,v.jsx)(A.Z,{status:"default",text:"\u672A\u5F00\u59CB"}):i===1?(0,v.jsx)(A.Z,{status:"processing",text:"\u8FDB\u884C\u4E2D"}):(0,v.jsx)(A.Z,{status:"success",text:"\u5DF2\u5B8C\u6210"})};function y(f){return h.apply(this,arguments)}function h(){return h=(0,l.Z)((0,Z.Z)().mark(function f(t){var i,u,d,T,O,b;return(0,Z.Z)().wrap(function(R){for(;;)switch(R.prev=R.next){case 0:return S&&!t&&n.length>0&&(o.current.current=o.current.current+1),R.next=3,q.hi.getWithPagination(a==="todo"?"/api/listUserTask":"/api/listHistoryTask",(0,$.Z)((0,$.Z)({},o.current),{},{type:D,current:t||o.current.current}));case 3:return i=R.sent,u=o.current,d=u.pageSize,T=d===void 0?10:d,O=u.total,b=O===void 0?0:O,t||(o.current=(0,$.Z)((0,$.Z)({},o.current),{},{total:i.total}),I(b/T>o.current.current)),R.abrupt("return",i.list);case 7:case"end":return R.stop()}},f)})),h.apply(this,arguments)}var N=(0,m.useState)(!0),j=(0,x.Z)(N,2),S=j[0],I=j[1],g=function(){var f=(0,l.Z)((0,Z.Z)().mark(function t(){var i;return(0,Z.Z)().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return d.next=2,y();case 2:i=d.sent,r([].concat((0,z.Z)(n),(0,z.Z)(i)));case 4:case"end":return d.stop()}},t)}));return function(){return f.apply(this,arguments)}}(),P=function(t){var i=new Date().getTime(),u=[];return a==="history"&&(D==="exam"?u.push({key:"start",text:"\u67E5\u770B\u7ED3\u679C",primary:!0,onClick:function(){window.open("/s/".concat(t.projectId,"/exam-result/").concat(t.answerId))}}):u.push({key:"start",text:"\u67E5\u770B\u7B54\u6848",primary:!0,onClick:function(){window.open("/s/".concat(t.projectId,"/").concat(t.answerId,"?pattern=readPretty"))}})),a=="todo"&&(D==="exam"?t.examStartTime&&t.examStartTime>i?u.push({key:"start",text:"\u8003\u8BD5\u672A\u5F00\u59CB",danger:!0,primary:!0}):t.examEndTime&&i>t.examEndTime?t.status===2?u.push({key:"result",text:"\u67E5\u770B\u7ED3\u679C",primary:!0,onClick:function(){window.open("/s/".concat(t.projectId,"/exam-result/").concat(t.answerId))}}):u.push({key:"result",text:"\u8003\u8BD5\u5DF2\u7ED3\u675F",primary:!0}):(t.status===1||t.status===0)&&u.push({key:"start",text:"\u5F00\u59CB\u8003\u8BD5",primary:!0,onClick:function(){window.open("/s/".concat(t.projectId))}}):t.endTime&&i>t.endTime?u.push({key:"start",text:"\u95EE\u5377\u5DF2\u7ED3\u675F",danger:!0}):u.push({key:"start",text:"\u5F00\u59CB\u7B54\u5377",primary:!0,onClick:function(){window.open("/s/".concat(t.projectId))}})),u.push({key:"cancel",text:"\u53D6\u6D88"}),u};return(0,v.jsx)("div",{style:{height:"100vh",overflowY:"scroll"},children:(0,v.jsxs)(E.t8,{onRefresh:(0,l.Z)((0,Z.Z)().mark(function f(){var t;return(0,Z.Z)().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,y(1);case 2:t=u.sent,r([].concat((0,z.Z)(t),(0,z.Z)(n.filter(function(d){return!t.map(function(T){return T.projectId}).includes(d.projectId)}))));case 4:case"end":return u.stop()}},f)})),children:[(0,v.jsx)(E.aV,{children:n.map(function(f,t){return(0,v.jsx)(E.aV.Item,{onClick:function(){E.u_.show({content:f.name,closeOnAction:!0,actions:P(f)})},extra:p(f),description:f.examStartTime?Y()(f.examStartTime).format("YYYY-MM-DD HH:mm"):void 0,children:f.name},t)})}),(0,v.jsx)(E.v_,{loadMore:g,hasMore:S})]},H)})},ne=te;function re(){var F=(0,m.useRef)(null),w=[{key:"survey",title:"\u6211\u7684\u95EE\u5377",type:"todo",mode:"survey"},{key:"exam",title:"\u6211\u7684\u8003\u8BD5",type:"todo",mode:"exam"},{key:"surveyHistory",title:"\u95EE\u5377\u8BB0\u5F55",type:"history",mode:"survey"},{key:"examHistory",title:"\u8003\u8BD5\u8BB0\u5F55",type:"history",mode:"exam"}],H=(0,m.useState)(0),D=(0,x.Z)(H,2),a=D[0],e=D[1];return(0,v.jsxs)("div",{className:"mobile-home",children:[(0,v.jsx)(E.mQ,{activeKey:w[a].key,onChange:function(n){var r,o=w.findIndex(function(p){return p.key===n});e(o),(r=F.current)===null||r===void 0||r.swipeTo(o)},children:w.map(function(c){return(0,v.jsx)(E.mQ.Tab,{title:c.title},c.key)})}),(0,v.jsx)(E.tq,{direction:"horizontal",loop:!0,indicator:function(){return null},ref:F,defaultIndex:a,onIndexChange:function(n){e(n)},children:w.map(function(c){return(0,v.jsx)(E.tq.Item,{children:(0,v.jsx)("div",{className:"survey",children:(0,v.jsx)(ne,{mode:c.mode,type:c.type})})})})})]})}},79166:function(ue,W,s){"use strict";s.d(W,{Z:function(){return D}});var x=s(96156),E=s(90484),m=s(22122),$=s(94184),Z=s.n($),z=s(5461),l=s(67294),Q=s(53124),A=s(96159),ie=s(98787);function k(a){return ie.Y.includes(a)}var q=function(e){var c=e.className,n=e.prefixCls,r=e.style,o=e.color,p=e.children,y=e.text,h=e.placement,N=h===void 0?"end":h,j=l.useContext(Q.E_),S=j.getPrefixCls,I=j.direction,g=S("ribbon",n),P=k(o),f=Z()(g,"".concat(g,"-placement-").concat(N),(0,x.Z)((0,x.Z)({},"".concat(g,"-rtl"),I==="rtl"),"".concat(g,"-color-").concat(o),P),c),t={},i={};return o&&!P&&(t.background=o,i.color=o),l.createElement("div",{className:"".concat(g,"-wrapper")},p,l.createElement("div",{className:f,style:(0,m.Z)((0,m.Z)({},t),r)},l.createElement("span",{className:"".concat(g,"-text")},y),l.createElement("div",{className:"".concat(g,"-corner"),style:i})))},_=q,Y=s(28481);function v(a){var e=a.prefixCls,c=a.value,n=a.current,r=a.offset,o=r===void 0?0:r,p;return o&&(p={position:"absolute",top:"".concat(o,"00%"),left:0}),l.createElement("span",{style:p,className:Z()("".concat(e,"-only-unit"),{current:n})},c)}function ee(a,e,c){for(var n=a,r=0;(n+10)%10!==e;)n+=c,r+=c;return r}function te(a){var e=a.prefixCls,c=a.count,n=a.value,r=Number(n),o=Math.abs(c),p=l.useState(r),y=(0,Y.Z)(p,2),h=y[0],N=y[1],j=l.useState(o),S=(0,Y.Z)(j,2),I=S[0],g=S[1],P=function(){N(r),g(o)};l.useEffect(function(){var b=setTimeout(function(){P()},1e3);return function(){clearTimeout(b)}},[r]);var f,t;if(h===r||Number.isNaN(r)||Number.isNaN(h))f=[l.createElement(v,(0,m.Z)({},a,{key:r,current:!0}))],t={transition:"none"};else{f=[];for(var i=r+10,u=[],d=r;d<=i;d+=1)u.push(d);var T=u.findIndex(function(b){return b%10===h});f=u.map(function(b,B){var R=b%10;return l.createElement(v,(0,m.Z)({},a,{key:b,value:R,offset:B-T,current:B===T}))});var O=I<o?1:-1;t={transform:"translateY(".concat(-ee(h,r,O),"00%)")}}return l.createElement("span",{className:"".concat(e,"-only"),style:t,onTransitionEnd:P},f)}var ne=function(a,e){var c={};for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&e.indexOf(n)<0&&(c[n]=a[n]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(a);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(a,n[r])&&(c[n[r]]=a[n[r]]);return c},re=function(e){var c=e.prefixCls,n=e.count,r=e.className,o=e.motionClassName,p=e.style,y=e.title,h=e.show,N=e.component,j=N===void 0?"sup":N,S=e.children,I=ne(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),g=l.useContext(Q.E_),P=g.getPrefixCls,f=P("scroll-number",c),t=(0,m.Z)((0,m.Z)({},I),{"data-show":h,style:p,className:Z()(f,r,o),title:y}),i=n;if(n&&Number(n)%1==0){var u=String(n).split("");i=u.map(function(d,T){return l.createElement(te,{prefixCls:f,count:Number(n),value:d,key:u.length-T})})}return p&&p.borderColor&&(t.style=(0,m.Z)((0,m.Z)({},p),{boxShadow:"0 0 0 1px ".concat(p.borderColor," inset")})),S?(0,A.Tm)(S,function(d){return{className:Z()("".concat(f,"-custom-component"),d==null?void 0:d.className,o)}}):l.createElement(j,t,i)},F=re,w=function(a,e){var c={};for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&e.indexOf(n)<0&&(c[n]=a[n]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(a);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(a,n[r])&&(c[n[r]]=a[n[r]]);return c},H=function(e){var c=e.prefixCls,n=e.scrollNumberPrefixCls,r=e.children,o=e.status,p=e.text,y=e.color,h=e.count,N=h===void 0?null:h,j=e.overflowCount,S=j===void 0?99:j,I=e.dot,g=I===void 0?!1:I,P=e.size,f=P===void 0?"default":P,t=e.title,i=e.offset,u=e.style,d=e.className,T=e.showZero,O=T===void 0?!1:T,b=w(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","showZero"]),B=l.useContext(Q.E_),R=B.getPrefixCls,ae=B.direction,C=R("badge",c),se=N>S?"".concat(S,"+"):N,L=se==="0"||se===0,pe=N===null||L&&!O,oe=(o!=null||y!=null)&&pe,G=g&&!L,K=G?"":se,U=(0,l.useMemo)(function(){var M=K==null||K==="";return(M||L&&!O)&&!G},[K,L,O,G]),fe=(0,l.useRef)(N);U||(fe.current=N);var V=fe.current,de=(0,l.useRef)(K);U||(de.current=K);var le=de.current,ve=(0,l.useRef)(G);U||(ve.current=G);var J=(0,l.useMemo)(function(){if(!i)return(0,m.Z)({},u);var M={marginTop:i[1]};return ae==="rtl"?M.left=parseInt(i[0],10):M.right=-parseInt(i[0],10),(0,m.Z)((0,m.Z)({},M),u)},[ae,i,u]),xe=t!=null?t:typeof V=="string"||typeof V=="number"?V:void 0,he=U||!p?null:l.createElement("span",{className:"".concat(C,"-status-text")},p),Ce=!V||(0,E.Z)(V)!=="object"?void 0:(0,A.Tm)(V,function(M){return{style:(0,m.Z)((0,m.Z)({},J),M.style)}}),ge=Z()((0,x.Z)((0,x.Z)((0,x.Z)({},"".concat(C,"-status-dot"),oe),"".concat(C,"-status-").concat(o),!!o),"".concat(C,"-status-").concat(y),k(y))),me={};y&&!k(y)&&(me.background=y);var ye=Z()(C,(0,x.Z)((0,x.Z)((0,x.Z)({},"".concat(C,"-status"),oe),"".concat(C,"-not-a-wrapper"),!r),"".concat(C,"-rtl"),ae==="rtl"),d);if(!r&&oe){var Ze=J.color;return l.createElement("span",(0,m.Z)({},b,{className:ye,style:J}),l.createElement("span",{className:ge,style:me}),p&&l.createElement("span",{style:{color:Ze},className:"".concat(C,"-status-text")},p))}return l.createElement("span",(0,m.Z)({},b,{className:ye}),r,l.createElement(z.default,{visible:!U,motionName:"".concat(C,"-zoom"),motionAppear:!1,motionDeadline:1e3},function(M){var Ne=M.className,Se=R("scroll-number",n),ce=ve.current,be=Z()((0,x.Z)((0,x.Z)((0,x.Z)((0,x.Z)((0,x.Z)((0,x.Z)({},"".concat(C,"-dot"),ce),"".concat(C,"-count"),!ce),"".concat(C,"-count-sm"),f==="small"),"".concat(C,"-multiple-words"),!ce&&le&&le.toString().length>1),"".concat(C,"-status-").concat(o),!!o),"".concat(C,"-status-").concat(y),k(y))),X=(0,m.Z)({},J);return y&&!k(y)&&(X=X||{},X.background=y),l.createElement(F,{prefixCls:Se,show:!U,motionClassName:Ne,className:be,count:le,title:xe,style:X,key:"scrollNumber"},Ce)}),he)};H.Ribbon=_;var D=H},54029:function(ue,W,s){"use strict";var x=s(38663),E=s.n(x),m=s(80341),$=s.n(m)}}]);
