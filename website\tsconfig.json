{"extends": "@tsconfig/docusaurus/tsconfig.json", "compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "noImplicitAny": false, "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "sourceMap": true, "declaration": true, "noImplicitReturns": true, "noImplicitThis": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "jsxImportSource": "@emotion/react"}, "include": ["src"]}