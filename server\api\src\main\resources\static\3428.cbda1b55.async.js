(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[3428],{48898:function(ae,_){"use strict";var s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};_.Z=s},85118:function(ae,_){"use strict";var s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"};_.Z=s},16165:function(ae,_,s){"use strict";var P=s(28991),w=s(96156),B=s(81253),I=s(67294),Z=s(94184),A=s.n(Z),o=s(63017),u=s(41755),f=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],M=I.forwardRef(function(e,U){var K=e.className,V=e.component,F=e.viewBox,W=e.spin,j=e.rotate,se=e.tabIndex,R=e.onClick,q=e.children,G=(0,B.Z)(e,f);(0,u.Kp)(Boolean(V||q),"Should have `component` prop or `children`."),(0,u.C3)();var me=I.useContext(o.Z),xe=me.prefixCls,ce=xe===void 0?"anticon":xe,pe=A()(ce,K),Ne=A()((0,w.Z)({},"".concat(ce,"-spin"),!!W)),he=j?{msTransform:"rotate(".concat(j,"deg)"),transform:"rotate(".concat(j,"deg)")}:void 0,Ae=(0,P.Z)((0,P.Z)({},u.vD),{},{className:Ne,style:he,viewBox:F});F||delete Ae.viewBox;var Re=function(){return V?I.createElement(V,(0,P.Z)({},Ae),q):q?((0,u.Kp)(Boolean(F)||I.Children.count(q)===1&&I.isValidElement(q)&&I.Children.only(q).type==="use","Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),I.createElement("svg",(0,P.Z)((0,P.Z)({},Ae),{},{viewBox:F}),q)):null},_e=se;return _e===void 0&&R&&(_e=-1),I.createElement("span",(0,P.Z)((0,P.Z)({role:"img"},G),{},{ref:U,tabIndex:_e,onClick:R,className:pe}),Re())});M.displayName="AntdIcon",_.Z=M},50675:function(ae,_,s){"use strict";var P=s(28991),w=s(67294),B=s(72961),I=s(27029),Z=function(o,u){return w.createElement(I.Z,(0,P.Z)((0,P.Z)({},o),{},{ref:u,icon:B.Z}))};Z.displayName="CheckCircleFilled",_.Z=w.forwardRef(Z)},46533:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM695.5 365.7l-210.6 292a31.8 31.8 0 01-51.7 0L308.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H689c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-square",theme:"filled"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="CheckSquareFilled";var o=w.forwardRef(A)},30071:function(ae,_,s){"use strict";var P=s(28991),w=s(67294),B=s(99011),I=s(27029),Z=function(o,u){return w.createElement(I.Z,(0,P.Z)((0,P.Z)({},o),{},{ref:u,icon:B.Z}))};Z.displayName="ClockCircleOutlined",_.Z=w.forwardRef(Z)},8913:function(ae,_,s){"use strict";var P=s(28991),w=s(67294),B=s(1085),I=s(27029),Z=function(o,u){return w.createElement(I.Z,(0,P.Z)((0,P.Z)({},o),{},{ref:u,icon:B.Z}))};Z.displayName="CloseCircleFilled",_.Z=w.forwardRef(Z)},36862:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112c17.7 0 32 14.3 32 32v736c0 17.7-14.3 32-32 32H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32zM639.98 338.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-square",theme:"filled"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="CloseSquareFilled";var o=w.forwardRef(A)},41687:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="FrownOutlined";var o=w.forwardRef(A)},57206:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9zM512 814.8S156 586.7 156 385.5C156 283.6 240.3 201 344.3 201c73.1 0 136.5 40.8 167.7 100.4C543.2 241.8 606.6 201 679.7 201c104 0 188.3 82.6 188.3 184.5 0 201.2-356 429.3-356 429.3z"}}]},name:"heart",theme:"outlined"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="HeartOutlined";var o=w.forwardRef(A)},46870:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 565H360c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}}]},name:"meh",theme:"outlined"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="MehOutlined";var o=w.forwardRef(A)},54121:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h368c4.4 0 8 3.6 8 8v48z"}}]},name:"minus-circle",theme:"filled"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="MinusCircleFilled";var o=w.forwardRef(A)},51042:function(ae,_,s){"use strict";var P=s(28991),w=s(67294),B=s(42110),I=s(27029),Z=function(o,u){return w.createElement(I.Z,(0,P.Z)((0,P.Z)({},o),{},{ref:u,icon:B.Z}))};Z.displayName="PlusOutlined",_.Z=w.forwardRef(Z)},81473:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M136 384h56c4.4 0 8-3.6 8-8V200h176c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-37.6 0-68 30.4-68 68v180c0 4.4 3.6 8 8 8zm512-184h176v176c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V196c0-37.6-30.4-68-68-68H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zM376 824H200V648c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v180c0 37.6 30.4 68 68 68h180c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm512-184h-56c-4.4 0-8 3.6-8 8v176H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h180c37.6 0 68-30.4 68-68V648c0-4.4-3.6-8-8-8zm16-164H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"scan",theme:"outlined"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="ScanOutlined";var o=w.forwardRef(A)},18613:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"}}]},name:"smile",theme:"outlined"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="SmileOutlined";var o=w.forwardRef(A)},89366:function(ae,_,s){"use strict";s.d(_,{Z:function(){return o}});var P=s(28991),w=s(67294),B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"},I=B,Z=s(27029),A=function(f,M){return w.createElement(Z.Z,(0,P.Z)((0,P.Z)({},f),{},{ref:M,icon:I}))};A.displayName="UserOutlined";var o=w.forwardRef(A)},34442:function(){},16695:function(){},76898:function(ae,_,s){"use strict";s.d(_,{Ff:function(){return tt},rF:function(){return Ne},M2:function(){return pe}});var P=s(22385),w=s(94199),B=s(11849),I=s(93224),Z=s(77883),A=s(48592),o=s(34792),u=s(48086),f=s(54707),M=s(49914),e=s(9761),U=s(94184),K=s.n(U),V=s(94657),F=s(30071),W=s(67294),j=s(85893);function se(H){if(H=Number(H),H<=0)return"0:0:0";var ie=Math.floor(H/3600),re=Math.floor(H%3600/60),le=Math.floor(H%3600%60),oe="".concat(ie).padStart(2,"0"),de="".concat(re).padStart(2,"0"),ge="".concat(le).padStart(2,"0");return"".concat(oe,":").concat(de,":").concat(ge)}function R(H){return H<=60?"#ff4d4f":H<=180?"#faad14":"#52c41a"}var q=(0,e.Pi)(function(){var H,ie,re=(0,M.H)(),le=(0,f.Rk)(),oe=re.startTime,de=((H=re.project)===null||H===void 0||(ie=H.setting.examSetting)===null||ie===void 0?void 0:ie.maxSubmitMinutes)||0,ge=(0,W.useState)(0),Ke=(0,V.Z)(ge,2),Pe=Ke[0],Le=Ke[1],Ye=oe+de*60*1e3,Se=(0,W.useRef)();return(0,W.useEffect)(function(){if(de!==0)return Se.current=window.setInterval(function(){Le((Ye-new Date().getTime())/1e3)},1e3),function(){clearInterval(Se.current)}},[Ye]),(0,W.useEffect)(function(){Pe<0&&(le.submit(),clearInterval(Se.current))},[Pe]),de===0?(0,j.jsx)(j.Fragment,{}):(0,j.jsxs)("div",{style:{position:"absolute",right:0,fontSize:13},children:[(0,j.jsx)(F.Z,{style:{marginRight:5,color:"#1890ff"}}),"\u5269\u4F59:",(0,j.jsx)("span",{style:{marginLeft:3,color:R(Pe)},children:se(Pe)})]})}),G=s(27400),me=s(3980),xe=["size","marked"],ce=(0,e.Pi)(function(){var H=(0,f.Rk)(),ie=H.lazy,re=(0,G.a)().user;return!ie||!re?(0,j.jsx)(j.Fragment,{}):(0,j.jsxs)("div",{style:{marginTop:10,textWrap:"wrap",lineHeight:"24px",fontSize:12,color:"#707070"},children:["\u8FDE\u7EED\u505A\u5BF9",(0,j.jsx)(A.Z,{size:"small",style:{width:60,marginInline:4},defaultValue:re.correctTimes,min:0,max:100,onChange:function(oe){oe!==null&&me.hi.updateUserInfo({correctTimes:oe}).then(function(de){de.success&&u.default.success("\u66F4\u65B0\u6210\u529F")})}}),"\u2003\u6B21\uFF0C\u81EA\u52A8\u79FB\u51FA\u9519\u9898/0\u4EE3\u8868\u6C38\u4E0D\u79FB\u51FA"]})}),pe=(0,e.Pi)(function(H){var ie=(0,f.Rk)(),re=ie.props.answerSheetVisible,le=H.size,oe=H.marked,de=oe===void 0?!0:oe,ge=(0,I.Z)(H,xe);return re?(0,j.jsx)(w.Z,{title:"\u6807\u8BB0",children:(0,j.jsx)("svg",(0,B.Z)((0,B.Z)({viewBox:"0 0 1024 1024",version:"1.1",width:H.size||24,height:H.size||24,className:"marked"},ge),{},{children:(0,j.jsx)("path",{d:"M662.04973405 329.56834123l108.40905238-142.19888693c4.22372932-5.63163908 4.22372932-11.26327817 1.40790978-18.30282702-2.81581955-5.63163908-8.44745863-9.8553684-15.48700748-9.85536841H218.55815604c-9.8553684 0-16.89491725 7.03954885-16.89491725 16.89491726V867.38987389c0 9.8553684 7.03954885 16.89491725 16.89491725 16.89491726s16.89491725-7.03954885 16.89491726-16.89491726V487.25423562h522.33452519c7.03954885 0 12.67118794-4.22372932 15.48700749-9.8553684s1.40790977-12.67118794-2.81581955-18.30282703l-108.40905238-129.52769896z",fill:de?"#EA4942":"#d8d8d8"})}))}):(0,j.jsx)(j.Fragment,{})}),Ne=(0,e.Pi)(function(H){var ie,re,le,oe=H.id,de=H.name,ge=(0,f.Rk)(),Ke=ge.answerSheet,Pe=Ke.find(function(He){return He.id===oe}),Le=(0,M.H)(),Ye=Le==null||(ie=Le.project)===null||ie===void 0?void 0:ie.isAuthenticated,Se=(re=Le.project)===null||re===void 0||(le=re.setting.examSetting)===null||le===void 0?void 0:le.randomSurveyWrong,Ue=ge.exerciseMode;return(!Ue||!Ye||oe.length<=10)&&!Se?(0,j.jsx)(j.Fragment,{}):Pe!=null&&Pe.favorite?(0,j.jsx)(w.Z,{title:Se?"\u79FB\u51FA\u9519\u9898":"\u79FB\u51FA\u6536\u85CF",children:(0,j.jsx)("svg",{viewBox:"0 0 1024 1024",version:"1.1",width:"24",height:"24",style:{float:"right"},onClick:function(){ge.favoriteQuestion({templateId:oe,name:de},!1)},children:(0,j.jsx)("path",{d:"M484.266667 272.021333l6.634666 6.72c5.973333 5.973333 13.013333 12.842667 21.098667 20.629334l9.194667-8.917334c7.253333-7.04 13.44-13.184 18.56-18.432a193.28 193.28 0 0 1 277.44 0c75.904 77.525333 76.629333 202.794667 2.133333 281.194667L512 853.333333 204.672 553.237333c-74.474667-78.421333-73.770667-203.690667 2.133333-281.216a193.28 193.28 0 0 1 277.44 0z","p-id":"2829",fill:"#d81e06"})})}):(0,j.jsx)(w.Z,{title:Se?"\u6DFB\u52A0\u5230\u9519\u9898":"\u6536\u85CF",children:(0,j.jsx)("svg",{viewBox:"0 0 1024 1024",version:"1.1",width:24,height:24,style:{float:"right"},onClick:function(){ge.favoriteQuestion({templateId:oe,name:de})},children:(0,j.jsx)("path",{d:"M484.267 272.021l6.634 6.72c5.974 5.974 13.014 12.843 21.099 20.63l9.195-8.918c7.253-7.04 13.44-13.184 18.56-18.432a193.28 193.28 0 0 1 277.44 0c75.904 77.526 76.629 202.795 2.133 281.195L512 853.333 204.672 553.237c-74.475-78.421-73.77-203.69 2.133-281.216a193.28 193.28 0 0 1 277.44 0z m293.162 232.15c46.272-53.76 44.182-136.15-5.973-187.371a129.28 129.28 0 0 0-185.984 0l-15.125 15.104a1687.253 1687.253 0 0 1-4.395 4.31L512 388.18l-49.28-47.445-13.227-12.928-10.965-11.008a129.28 129.28 0 0 0-186.005 0c-51.456 52.565-52.31 137.963-2.198 191.573L512 763.883l261.675-255.531 3.754-4.181z","p-id":"2674",fill:"#d8d8d8"})})})}),he=(0,e.Pi)(function(H){var ie=(0,f.Rk)(),re=H.finished,le=H.id,oe=H.marked,de=H.seqNo,ge=H.isCorrect;return(0,j.jsxs)("div",{className:K()("answer-sheet-item",{marked:oe,finished:re,correct:ge===!0,wrong:ge===!1}),onClick:function(){return ie.changePageIndex(le)},children:[de,oe&&(0,j.jsx)(pe,{style:{position:"absolute",left:20,bottom:18},size:18})]})}),Ae=(0,e.Pi)(function(){var H=(0,f.Rk)(),ie=H.answerSheet;return(0,j.jsx)(j.Fragment,{children:ie.filter(function(re){return re.display!=="none"}).map(function(re,le){return(0,W.createElement)(he,(0,B.Z)((0,B.Z)({},re),{},{seqNo:le+1,key:re.id}))})})}),Re=(0,e.Pi)(function(){var H=(0,f.Rk)(),ie=H.answerSheet,re=ie.filter(function(de){return de.finished}).length,le=ie.filter(function(de){return!de.finished}).length,oe=ie.filter(function(de){return de.marked}).length;return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)("div",{className:"finished",children:["\u5DF2\u7B54",(0,j.jsx)("span",{style:{marginLeft:4},children:re})]}),(0,j.jsxs)("div",{className:"unFinished",children:["\u672A\u7B54",(0,j.jsx)("span",{style:{marginLeft:4},children:le})]}),(0,j.jsxs)("div",{className:"marked",children:[(0,j.jsx)(pe,{size:18}),"\u6807\u8BB0",(0,j.jsx)("span",{style:{marginLeft:4},children:oe})]})]})}),_e=(0,e.Pi)(function(){return(0,j.jsxs)("div",{className:"answer-sheet-header",children:["\u7B54\u9898\u5361",(0,j.jsx)(q,{})]})}),tt=(0,e.Pi)(function(){var H=(0,f.Rk)(),ie=H.props.answerSheetVisible;return ie?(0,j.jsxs)("div",{className:"answer-sheet",children:[(0,j.jsx)(_e,{}),(0,j.jsx)("div",{className:"answer-sheet-content",children:(0,j.jsx)(Ae,{})}),(0,j.jsx)("div",{className:"answer-sheet-footer",children:(0,j.jsx)(Re,{})}),(0,j.jsx)(ce,{})]}):(0,j.jsx)(j.Fragment,{})})},6727:function(ae,_,s){"use strict";var P=s(47673),w=s(77808),B=s(11849),I=s(94657),Z=s(93224),A=s(67294),o=s(3980),u=s(40807),f=s(85893),M=["value","onChange","style"],e=function(K){var V=K.value,F=V===void 0?"":V,W=K.onChange,j=K.style,se=j===void 0?{}:j,R=(0,Z.Z)(K,M),q=(0,A.useState)(80),G=(0,I.Z)(q,2),me=G[0],xe=G[1],ce=(0,A.useRef)(null),pe=(0,o.dD)();return(0,A.useEffect)(function(){if(ce.current){for(var Ne=ce.current.offsetWidth,he=Ne+34,Ae=ce.current.parentElement;Ae&&!Ae.getAttribute("data-id");)Ae=Ae.parentElement;if(Ae){var Re=Ae.offsetWidth;pe&&(Re-=40),he=Math.min(he,Re)}se.width&&(he=Math.max(he,parseFloat(se.width.toString()))),xe(Math.max(he,100))}},[F,se.width]),(0,f.jsxs)("div",{style:{display:"inline-block",verticalAlign:(0,u.ob)()?"top":"unset",marginInline:5},children:[(0,f.jsx)("div",{ref:ce,style:{position:"absolute",visibility:"hidden",whiteSpace:"pre",fontSize:"14px",fontFamily:"Arial, sans-serif",padding:"4px 11px",border:"1px solid #d9d9d9",borderRadius:"4px",overflow:"hidden",height:12,lineHeight:"12px"},children:F||" "}),(0,f.jsx)(w.Z,(0,B.Z)((0,B.Z)({},R),{},{value:F,onChange:W,style:(0,B.Z)((0,B.Z)({},se),{},{width:"".concat(me,"px"),margin:0,padding:0})}))]})};_.Z=e},61616:function(ae,_,s){"use strict";s.r(_),s.d(_,{default:function(){return Ln}});var P=s(9715),w=s(55246),B=s(94657),I=s(40807),Z=s(78210),A=s(9761),o=s(67294),u=s(54707),f=s(48187),M=s(76898),e=s(85893),U=function(){var n=(0,u.AK)(),t=(0,o.useContext)(I.ZX),i=t.schema;return(0,e.jsxs)("div",{className:n+"-header",children:[(0,e.jsx)("div",{className:n+"-header-title",children:(0,e.jsx)(Z.Z,{html:i.title})}),(0,e.jsx)("div",{className:n+"-header-description",children:(0,e.jsx)(Z.Z,{html:i.description})})]})},K=function(n){var t=(0,u.AK)();return(0,e.jsx)("div",{className:t+"-body",children:n.children})},V=s(57663),F=s(71577),W=(0,A.Pi)(function(){var r,n,t,i=(0,u.AK)("-footer"),a=(0,o.useContext)(I.ZX),l=a.schema,v=a.loading,d=(0,u.Rk)(),c=d.pagination,m=c.pageSize>1,h=c.current===1,g=c.current===c.pageSize;return(0,e.jsxs)("div",{className:i,children:[g&&(0,e.jsx)("div",{className:i+"-desc",style:{userSelect:"none",WebkitTouchCallout:"none"},children:(0,e.jsx)(Z.Z,{html:l==null||(r=l.attribute)===null||r===void 0?void 0:r.suffix})}),(0,e.jsxs)("div",{className:i+"-pagination",children:[m&&!h&&(0,e.jsx)("div",{children:(0,e.jsx)(F.Z,{type:"primary",block:!0,onClick:function(){return d.handlePrev()},children:"\u4E0A\u4E00\u9875"})}),m&&!g&&(0,e.jsx)("div",{children:(0,e.jsx)(F.Z,{type:"primary",block:!0,onClick:function(){return d.handleNext()},children:"\u4E0B\u4E00\u9875"})}),g&&!m&&(0,e.jsx)("div",{style:{width:"100%",padding:"0 20px"},children:(0,e.jsx)(F.Z,{type:"primary",block:!0,onClick:function(){return d.submit()},loading:v,children:(0,e.jsx)(Z.Z,{html:((n=l.attribute)===null||n===void 0?void 0:n.submitButton)||"\u63D0\u4EA4"})})}),g&&m&&(0,e.jsx)("div",{children:(0,e.jsx)(F.Z,{type:"primary",onClick:function(){return d.submit()},loading:v,children:(0,e.jsx)(Z.Z,{html:((t=l.attribute)===null||t===void 0?void 0:t.submitButton)||"\u63D0\u4EA4"})})}),m&&(0,e.jsxs)("div",{className:i+"-pagination-info",children:["\u7B2C",c.current,"\u9875/\u5171",c.pageSize,"\u9875"]})]})]})}),j=s(11849),se=s(47673),R=s(77808),q=s(43358),G=s(34041),me=s(29985),xe=s(46842),ce=s(64302),pe=s(6727),Ne=function(n){var t=n.dictCode,i=n.value,a=n.onChange,l=n.style,v=n.bordered,d=n.className,c=n.parentValue,m=n.cascaderLevel,h=m===void 0?1:m,g=(0,u.Al)(),p=(0,o.useState)([]),x=(0,B.Z)(p,2),C=x[0],y=x[1],E=(0,u.zE)(function(S){h&&h>1&&!c||g.onDictSearch&&g.onDictSearch({dictCode:t,searchValue:S,parentValue:c==null?void 0:c.split("|")[0],cascaderLevel:h}).then(function(b){y(b||[])})},2);return(0,o.useEffect)(function(){h&&h>1&&(c?E(void 0):y([]))},[c,h]),(0,o.useEffect)(function(){!i&&h<=1&&t&&E(void 0),i&&C.length===0&&E(i)},[i]),(0,e.jsx)(G.Z,{options:C,showSearch:!0,filterOption:function(b,D){var k;return((k=D==null?void 0:D.label)!==null&&k!==void 0?k:"").includes(b)},value:(i==null?void 0:i.split("|")[0])||"",onChange:function(b){var D;if(b===void 0){a(void 0);return}a("".concat(b||"","|").concat(((D=C.find(function(k){return k.value===b}))===null||D===void 0?void 0:D.label)||""))},style:l,bordered:v,className:d,allowClear:!0})},he=function(n){var t,i=n.onChange,a=n.type,l=n.value,v=n.suffix,d=n.prefix,c=n.style,m=c===void 0?{width:"100%"}:c,h=n.bordered,g=h===void 0?!0:h,p=n.className,x=n.dictCode,C=n.parentValue,y=n.cascaderLevel,E=n.icon,S=E===void 0?!0:E,b=n.readOnly,D=n.schema,k=n.dateTimeFormat,T=n.onBlur,L=T===void 0?function(){}:T,Y=n.placeholder,X=Y===void 0?D==null||(t=D.attribute)===null||t===void 0?void 0:t.placeholder:Y,N=n.autoSize;if(a==="horzBlank")return(0,e.jsx)(e.Fragment,{});if(a==="selectDict"&&x)return(0,e.jsx)(Ne,{value:l,onChange:i,dictCode:x,style:m,bordered:g,className:p,parentValue:C,cascaderLevel:y});if(a==="select"&&D!==null&&D!==void 0&&D.dataSource)return(0,e.jsx)(G.Z,{options:D.dataSource,style:{width:"100%",opacity:"unset"},bordered:g,onChange:i,className:p,value:l});if(a==="date"){var Q;return(0,e.jsx)(te,{onChange:function(z){return i(z)},value:l,style:m,bordered:g,placeholder:X,readOnly:b,className:p,dateTimeFormat:k||(D==null||(Q=D.attribute)===null||Q===void 0?void 0:Q.dateTimeFormat)})}return a==="mobile"?(0,e.jsx)(R.Z,{prefix:S?d||(0,e.jsx)(me.Z,{}):void 0,onChange:function(z){return i(z.target.value)},value:l,bordered:g,placeholder:X,className:p,readOnly:b,onBlur:function(z){L(z.target.value)}}):a==="idCard"?(0,e.jsx)(R.Z,{prefix:S?d||(0,e.jsx)(xe.Z,{}):void 0,onChange:function(z){return i(z.target.value)},value:l,style:m,bordered:g,placeholder:X,className:p,readOnly:b,onBlur:function(z){L(z.target.value)}}):N?(0,e.jsx)(pe.Z,{onChange:function(z){return i(z.target.value)},onBlur:function(z){return L(z.target.value)},value:l,suffix:v,prefix:d,style:m,bordered:g,placeholder:X,className:p,readOnly:b}):a==="number"?(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(R.Z,{onChange:function(z){z.target.value===""?i(""):i(z.target.value)},prefix:d,type:"number",value:l,style:(0,j.Z)((0,j.Z)({},m),{},{paddingLeft:20}),onBlur:function(z){var fe=z.target.value;L(fe===""?void 0:parseFloat(fe))},suffix:v,inputMode:"decimal",bordered:g,placeholder:X,className:p,readOnly:b})}):a==="email"?(0,e.jsx)(R.Z,{prefix:S?d||(0,e.jsx)(ce.Z,{}):void 0,onChange:function(z){return i(z.target.value)},value:l,style:m,bordered:g,placeholder:X,className:p,readOnly:b,onBlur:function(z){L(z.target.value)}}):(0,e.jsx)(R.Z,{onChange:function(z){return i(z.target.value)},value:l,suffix:v,prefix:d,style:m,bordered:g,placeholder:X,className:p,readOnly:b,onBlur:function(z){L(z.target.value)}})},Ae=s(34804),Re=function(n){var t=n.options,i=n.onChange,a=n.value;return(0,e.jsxs)("div",{className:"native-select",children:[(0,e.jsxs)("select",{onChange:function(v){return i(v.target.value)},value:a,className:"native-select__select",children:[(0,e.jsx)("option",{children:"\u8BF7\u9009\u62E9"}),t==null?void 0:t.map(function(l){return(0,e.jsx)("option",{value:l.value,children:l.label},l.value)})]}),(0,e.jsx)("span",{className:"native-select__arrow",children:(0,e.jsx)(Ae.Z,{})})]})};/*!
 * mobileSelect.js
 * (c) 2017-present onlyhom
 * Released under the MIT License.
 */function _e(r,n){return r.getElementsByClassName(n)}function tt(r){this.mobileSelect,this.wheelsData=r.wheels,this.jsonType=!1,this.cascadeJsonData=[],this.displayJson=[],this.curValue=null,this.curIndexArr=[],this.cascade=!1,this.startY,this.moveEndY,this.moveY,this.oldMoveY,this.offset=0,this.offsetSum=0,this.oversizeBorder,this.curDistance=[],this.clickStatus=!1,this.isPC=!0,this.init(r)}tt.prototype={constructor:tt,init:function(n){var t=this;if(t.keyMap=n.keyMap?n.keyMap:{id:"id",value:"value",childs:"childs"},t.checkDataType(),t.renderWheels(t.wheelsData,n.cancelBtnText,n.ensureBtnText,n.container),t.wheel=_e(t.mobileSelect,"wheel"),t.slider=_e(t.mobileSelect,"selectContainer"),t.wheels=t.mobileSelect.querySelector(".wheels"),t.liHeight=t.mobileSelect.querySelector("li").offsetHeight,t.ensureBtn=t.mobileSelect.querySelector(".ensure"),t.cancelBtn=t.mobileSelect.querySelector(".cancel"),t.grayLayer=t.mobileSelect.querySelector(".grayLayer"),t.popUp=t.mobileSelect.querySelector(".content"),t.callback=n.callback||function(){},t.transitionEnd=n.transitionEnd||function(){},t.onShow=n.onShow||function(){},t.onHide=n.onHide||function(){},t.initPosition=n.position||[],t.titleText=n.title||"",t.connector=n.connector||" ",t.triggerDisplayData=typeof n.triggerDisplayData!="undefined"?n.triggerDisplayData:!0,t.setStyle(n),t.setTitle(t.titleText),t.checkIsPC(),t.checkCascade(),t.addListenerAll(),t.cascade&&t.initCascade(),t.initPosition.length<t.slider.length)for(var i=t.slider.length-t.initPosition.length,a=0;a<i;a++)t.initPosition.push(0);t.setCurDistance(t.initPosition),t.cancelBtn.addEventListener("click",function(l){t.hide()}),t.ensureBtn.addEventListener("click",function(l){t.hide(),t.liHeight||(t.liHeight=t.mobileSelect.querySelector("li").offsetHeight);for(var v="",d=0;d<t.wheel.length;d++)d==t.wheel.length-1?v+=t.getInnerHtml(d):v+=t.getInnerHtml(d)+t.connector;t.curIndexArr=t.getIndexArr(),t.curValue=t.getCurValue(),t.callback(t.curIndexArr,t.curValue)}),t.grayLayer.addEventListener("click",function(l){t.hide()}),t.popUp.addEventListener("click",function(l){l.stopPropagation()}),t.fixRowStyle()},setTitle:function(n){var t=this;t.titleText=n,t.mobileSelect.querySelector(".title").innerHTML=t.titleText},setStyle:function(n){var t=this;n.ensureBtnColor&&(t.ensureBtn.style.color=n.ensureBtnColor),n.cancelBtnColor&&(t.cancelBtn.style.color=n.cancelBtnColor),n.titleColor&&(t.title=t.mobileSelect.querySelector(".title"),t.title.style.color=n.titleColor),n.textColor&&(t.panel=t.mobileSelect.querySelector(".panel"),t.panel.style.color=n.textColor),n.titleBgColor&&(t.btnBar=t.mobileSelect.querySelector(".btnBar"),t.btnBar.style.backgroundColor=n.titleBgColor),n.bgColor&&(t.panel=t.mobileSelect.querySelector(".panel"),t.shadowMask=t.mobileSelect.querySelector(".shadowMask"),t.panel.style.backgroundColor=n.bgColor,t.shadowMask.style.background="linear-gradient(to bottom, "+n.bgColor+", rgba(255, 255, 255, 0), "+n.bgColor+")"),isNaN(n.maskOpacity)||(t.grayMask=t.mobileSelect.querySelector(".grayLayer"),t.grayMask.style.background="rgba(0, 0, 0, "+n.maskOpacity+")")},checkIsPC:function(){var n=this,t=navigator.userAgent.toLowerCase(),i=t.match(/ipad/i)=="ipad",a=t.match(/iphone os/i)=="iphone os",l=t.match(/midp/i)=="midp",v=t.match(/rv:*******/i)=="rv:*******",d=t.match(/ucweb/i)=="ucweb",c=t.match(/android/i)=="android",m=t.match(/windows ce/i)=="windows ce",h=t.match(/windows mobile/i)=="windows mobile";(i||a||l||v||d||c||m||h)&&(n.isPC=!1)},show:function(){this.mobileSelect.classList.add("mobileSelect-show"),typeof this.onShow=="function"&&this.onShow(this)},hide:function(){this.mobileSelect.classList.remove("mobileSelect-show"),typeof this.onHide=="function"&&this.onHide(this)},renderWheels:function(n,t,i,a){var l=this,v=t||"\u53D6\u6D88",d=i||"\u786E\u8BA4";l.mobileSelect=document.createElement("div"),l.mobileSelect.className="mobileSelect",l.mobileSelect.innerHTML='<div class="grayLayer"></div><div class="content"><div class="btnBar"><div class="fixWidth"><div class="cancel">'+v+'</div><div class="title"></div><div class="ensure">'+d+'</div></div></div><div class="panel"><div class="fixWidth"><div class="wheels"></div><div class="selectLine"></div><div class="shadowMask"></div></div></div></div>',a?a.appendChild(l.mobileSelect):document.body.appendChild(l.mobileSelect);for(var c="",m=0;m<n.length;m++){if(c+='<div class="wheel"><ul class="selectContainer">',l.jsonType)for(var h=0;h<n[m].data.length;h++)c+='<li data-id="'+n[m].data[h][l.keyMap.id]+'">'+n[m].data[h][l.keyMap.value]+"</li>";else for(var h=0;h<n[m].data.length;h++)c+="<li>"+n[m].data[h]+"</li>";c+="</ul></div>"}l.mobileSelect.querySelector(".wheels").innerHTML=c},addListenerAll:function(){for(var n=this,t=0;t<n.slider.length;t++)(function(i){n.addListenerWheel(n.wheel[i],i)})(t)},addListenerWheel:function(n,t){var i=this;n.addEventListener("touchstart",function(a){i.touch(a,this.firstChild,t)},!1),n.addEventListener("touchend",function(a){i.touch(a,this.firstChild,t)},!1),n.addEventListener("touchmove",function(a){i.touch(a,this.firstChild,t)},!1),i.isPC&&(n.addEventListener("mousedown",function(a){i.dragClick(a,this.firstChild,t)},!1),n.addEventListener("mousemove",function(a){i.dragClick(a,this.firstChild,t)},!1),n.addEventListener("mouseup",function(a){i.dragClick(a,this.firstChild,t)},!0))},checkDataType:function(){var n=this;typeof n.wheelsData[0].data[0]=="object"&&(n.jsonType=!0)},checkCascade:function(){var n=this;if(n.jsonType){for(var t=n.wheelsData[0].data,i=0;i<t.length;i++)if(n.keyMap.childs in t[i]&&t[i][n.keyMap.childs].length>0){n.cascade=!0,n.cascadeJsonData=n.wheelsData[0].data;break}}else n.cascade=!1},generateArrData:function(n){for(var t=[],i=this.keyMap.id,a=this.keyMap.value,l=0;l<n.length;l++){var v={};v[i]=n[l][this.keyMap.id],v[a]=n[l][this.keyMap.value],t.push(v)}return t},initCascade:function(){var n=this;n.displayJson.push(n.generateArrData(n.cascadeJsonData)),n.initPosition.length>0?(n.initDeepCount=0,n.initCheckArrDeep(n.cascadeJsonData[n.initPosition[0]])):n.checkArrDeep(n.cascadeJsonData[0]),n.reRenderWheels()},initCheckArrDeep:function(n){var t=this;if(n&&t.keyMap.childs in n&&n[t.keyMap.childs].length>0){t.displayJson.push(t.generateArrData(n[t.keyMap.childs])),t.initDeepCount++;var i=n[t.keyMap.childs][t.initPosition[t.initDeepCount]];i?t.initCheckArrDeep(i):t.checkArrDeep(n[t.keyMap.childs][0])}},checkArrDeep:function(n){var t=this;n&&t.keyMap.childs in n&&n[t.keyMap.childs].length>0&&(t.displayJson.push(t.generateArrData(n[t.keyMap.childs])),t.checkArrDeep(n[t.keyMap.childs][0]))},checkRange:function(n,t){for(var i=this,a=i.displayJson.length-1-n,l=0;l<a;l++)i.displayJson.pop();for(var v,l=0;l<=n;l++)l==0?v=i.cascadeJsonData[t[0]]:v=v[i.keyMap.childs][t[l]];i.checkArrDeep(v),i.reRenderWheels(),i.fixRowStyle(),i.setCurDistance(i.resetPosition(n,t))},resetPosition:function(n,t){var i=this,a=t,l;if(i.slider.length>t.length){l=i.slider.length-t.length;for(var v=0;v<l;v++)a.push(0)}else if(i.slider.length<t.length){l=t.length-i.slider.length;for(var v=0;v<l;v++)a.pop()}for(var v=n+1;v<a.length;v++)a[v]=0;return a},reRenderWheels:function(){var n=this;if(n.wheel.length>n.displayJson.length)for(var t=n.wheel.length-n.displayJson.length,i=0;i<t;i++)n.wheels.removeChild(n.wheel[n.wheel.length-1]);for(var i=0;i<n.displayJson.length;i++)(function(l){var v="";if(n.wheel[l]){for(var d=0;d<n.displayJson[l].length;d++)v+='<li data-id="'+n.displayJson[l][d][n.keyMap.id]+'">'+n.displayJson[l][d][n.keyMap.value]+"</li>";n.slider[l].innerHTML=v}else{var c=document.createElement("div");c.className="wheel",v='<ul class="selectContainer">';for(var d=0;d<n.displayJson[l].length;d++)v+='<li data-id="'+n.displayJson[l][d][n.keyMap.id]+'">'+n.displayJson[l][d][n.keyMap.value]+"</li>";v+="</ul>",c.innerHTML=v,n.addListenerWheel(c,l),n.wheels.appendChild(c)}})(i)},updateWheels:function(n){var t=this;if(t.cascade){if(t.cascadeJsonData=n,t.displayJson=[],t.initCascade(),t.initPosition.length<t.slider.length)for(var i=t.slider.length-t.initPosition.length,a=0;a<i;a++)t.initPosition.push(0);t.setCurDistance(t.initPosition),t.fixRowStyle()}},updateWheel:function(n,t){var i=this,a="";if(i.cascade)return console.error("\u7EA7\u8054\u683C\u5F0F\u4E0D\u652F\u6301updateWheel(),\u8BF7\u4F7F\u7528updateWheels()\u66F4\u65B0\u6574\u4E2A\u6570\u636E\u6E90"),!1;if(i.jsonType){for(var l=0;l<t.length;l++)a+='<li data-id="'+t[l][i.keyMap.id]+'">'+t[l][i.keyMap.value]+"</li>";i.wheelsData[n]={data:t}}else{for(var l=0;l<t.length;l++)a+="<li>"+t[l]+"</li>";i.wheelsData[n]=t}i.slider[n].innerHTML=a},fixRowStyle:function(){for(var n=this,t=(100/n.wheel.length).toFixed(2)-2,i=0;i<n.wheel.length;i++);},getIndex:function(n){return Math.round((2*this.liHeight-n)/this.liHeight)},getIndexArr:function(){for(var n=this,t=[],i=0;i<n.curDistance.length;i++)t.push(n.getIndex(n.curDistance[i]));return t},getCurValue:function(){var n=this,t=[],i=n.getIndexArr();if(n.cascade)for(var a=0;a<n.wheel.length;a++)t.push(n.displayJson[a][i[a]]);else if(n.jsonType)for(var a=0;a<n.curDistance.length;a++)t.push(n.wheelsData[a].data[n.getIndex(n.curDistance[a])]);else for(var a=0;a<n.curDistance.length;a++)t.push(n.getInnerHtml(a));return t},getValue:function(){return this.curValue},calcDistance:function(n){return 2*this.liHeight-n*this.liHeight},setCurDistance:function(n){for(var t=this,i=[],a=0;a<t.slider.length;a++)i.push(t.calcDistance(n[a])),t.movePosition(t.slider[a],i[a]);t.curDistance=i},fixPosition:function(n){return-(this.getIndex(n)-2)*this.liHeight},movePosition:function(n,t){n.style.webkitTransform="translate3d(0,"+t+"px, 0)",n.style.transform="translate3d(0,"+t+"px, 0)"},locatePosition:function(n,t){var i=this;this.curDistance[n]=this.calcDistance(t),this.movePosition(this.slider[n],this.curDistance[n]),i.cascade&&i.checkRange(n,i.getIndexArr())},updateCurDistance:function(n,t){n.style.transform?this.curDistance[t]=parseInt(n.style.transform.split(",")[1]):this.curDistance[t]=parseInt(n.style.webkitTransform.split(",")[1])},getDistance:function(n){return n.style.transform?parseInt(n.style.transform.split(",")[1]):parseInt(n.style.webkitTransform.split(",")[1])},getInnerHtml:function(n){var t=this,i=t.slider[n].getElementsByTagName("li").length,a=t.getIndex(t.curDistance[n]);return a>=i?a=i-1:a<0&&(a=0),t.slider[n].getElementsByTagName("li")[a].innerHTML},touch:function(n,t,i){var a=this;switch(n=n||window.event,n.type){case"touchstart":a.startY=n.touches[0].clientY,a.startY=parseInt(a.startY),a.oldMoveY=a.startY;break;case"touchend":if(a.moveEndY=parseInt(n.changedTouches[0].clientY),a.offsetSum=a.moveEndY-a.startY,a.oversizeBorder=-(t.getElementsByTagName("li").length-3)*a.liHeight,a.offsetSum==0){var l=parseInt((document.documentElement.clientHeight-a.moveEndY)/40);if(l!=2){var v=l-2,d=a.curDistance[i]+v*a.liHeight;d<=2*a.liHeight&&d>=a.oversizeBorder&&(a.curDistance[i]=d,a.movePosition(t,a.curDistance[i]),a.transitionEnd(a.getIndexArr(),a.getCurValue(),i))}}else a.updateCurDistance(t,i),a.curDistance[i]=a.fixPosition(a.curDistance[i]),a.movePosition(t,a.curDistance[i]),a.curDistance[i]+a.offsetSum>2*a.liHeight?(a.curDistance[i]=2*a.liHeight,setTimeout(function(){a.movePosition(t,a.curDistance[i])},100)):a.curDistance[i]+a.offsetSum<a.oversizeBorder&&(a.curDistance[i]=a.oversizeBorder,setTimeout(function(){a.movePosition(t,a.curDistance[i])},100)),a.transitionEnd(a.getIndexArr(),a.getCurValue(),i);a.cascade&&a.checkRange(i,a.getIndexArr());break;case"touchmove":n.preventDefault(),a.moveY=n.touches[0].clientY,a.offset=a.moveY-a.oldMoveY,a.updateCurDistance(t,i),a.curDistance[i]=a.curDistance[i]+a.offset,a.movePosition(t,a.curDistance[i]),a.oldMoveY=a.moveY;break}},dragClick:function(n,t,i){var a=this;switch(n=n||window.event,n.type){case"mousedown":a.startY=n.clientY,a.oldMoveY=a.startY,a.clickStatus=!0;break;case"mouseup":if(a.moveEndY=n.clientY,a.offsetSum=a.moveEndY-a.startY,a.oversizeBorder=-(t.getElementsByTagName("li").length-3)*a.liHeight,a.offsetSum==0){var l=parseInt((document.documentElement.clientHeight-a.moveEndY)/40);if(l!=2){var v=l-2,d=a.curDistance[i]+v*a.liHeight;d<=2*a.liHeight&&d>=a.oversizeBorder&&(a.curDistance[i]=d,a.movePosition(t,a.curDistance[i]),a.transitionEnd(a.getIndexArr(),a.getCurValue(),i))}}else a.updateCurDistance(t,i),a.curDistance[i]=a.fixPosition(a.curDistance[i]),a.movePosition(t,a.curDistance[i]),a.curDistance[i]+a.offsetSum>2*a.liHeight?(a.curDistance[i]=2*a.liHeight,setTimeout(function(){a.movePosition(t,a.curDistance[i])},100)):a.curDistance[i]+a.offsetSum<a.oversizeBorder&&(a.curDistance[i]=a.oversizeBorder,setTimeout(function(){a.movePosition(t,a.curDistance[i])},100)),a.transitionEnd(a.getIndexArr(),a.getCurValue(),i);a.clickStatus=!1,a.cascade&&a.checkRange(i,a.getIndexArr());break;case"mousemove":n.preventDefault(),a.clickStatus&&(a.moveY=n.clientY,a.offset=a.moveY-a.oldMoveY,a.updateCurDistance(t,i),a.curDistance[i]=a.curDistance[i]+a.offset,a.movePosition(t,a.curDistance[i]),a.oldMoveY=a.moveY);break}}};var H=s(83279),ie=s(16165),re=function(){return _jsx("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",children:_jsx("path",{d:"M237.136842 668.294737c-5.389474-5.389474-10.778947-10.778947-10.778947-16.168421V355.705263v-21.557895-26.947368-32.336842c0-10.778947 0-21.557895 5.389473-26.947369l-5.389473-5.389473-80.842106 32.336842c-26.947368 5.389474-59.284211 16.168421-91.621052 26.947368l5.389474 21.557895 107.789473-16.168421v334.147368c0 10.778947-5.389474 16.168421-10.778947 21.557895-5.389474 5.389474-16.168421 10.778947-32.336842 10.778948-10.778947 0-32.336842 5.389474-53.894737 5.389473v16.168421H323.368421v-21.557894c-21.557895 0-37.726316 0-48.505263-5.389474-21.557895 10.778947-32.336842 5.389474-37.726316 0z m414.989474-43.11579c0-10.778947 5.389474-16.168421 5.389473-26.947368l-16.168421-5.389474c-5.389474 16.168421-10.778947 26.947368-16.168421 32.336842-5.389474 5.389474-10.778947 16.168421-16.168421 16.168421-5.389474 5.389474-10.778947 5.389474-16.168421 5.389474h-16.168421l-172.463158 5.389474 107.789474-102.4c16.168421-16.168421 32.336842-26.947368 48.505263-43.11579 16.168421-16.168421 26.947368-26.947368 37.726316-43.115789 10.778947-16.168421 21.557895-32.336842 26.947368-48.505263 5.389474-16.168421 10.778947-37.726316 10.778948-53.894737 0-21.557895-5.389474-37.726316-10.778948-53.894737-10.778947-16.168421-21.557895-26.947368-32.336842-37.726316-16.168421-10.778947-32.336842-16.168421-48.505263-21.557895-21.557895-5.389474-37.726316-10.778947-59.28421-10.778947-21.557895 0-48.505263 5.389474-70.063158 10.778947-16.168421 10.778947-37.726316 21.557895-59.284211 32.336843 0 16.168421 0 32.336842 5.389474 48.505263v53.894737h21.557895c0-16.168421 5.389474-26.947368 10.778947-37.726316l16.168421-32.336842c5.389474-10.778947 16.168421-16.168421 26.947368-21.557895 10.778947-5.389474 21.557895-5.389474 37.726316-5.389474 10.778947 0 26.947368 0 37.726316 5.389474 10.778947 5.389474 21.557895 10.778947 32.336842 21.557895 10.778947 10.778947 16.168421 16.168421 21.557895 32.336842 5.389474 10.778947 5.389474 26.947368 5.389474 37.726316 0 16.168421-5.389474 32.336842-10.778948 48.505263-5.389474 16.168421-16.168421 32.336842-26.947368 48.505263-5.389474 10.778947-21.557895 26.947368-32.336842 43.115789l-48.505264 48.505264-123.957894 123.957894 5.389473 5.389474h301.810527v-21.557895c0-10.778947 5.389474-16.168421 5.389473-26.947368 5.389474-10.778947 10.778947-21.557895 10.778948-26.947369z m307.2-80.842105c-5.389474-10.778947-16.168421-26.947368-21.557895-32.336842-10.778947-10.778947-21.557895-21.557895-37.726316-26.947368-10.778947-5.389474-26.947368-10.778947-43.115789-16.168421 16.168421-10.778947 32.336842-26.947368 43.115789-37.726316 10.778947-10.778947 21.557895-21.557895 26.947369-32.336842 5.389474-10.778947 10.778947-21.557895 10.778947-26.947369v-16.168421c0-16.168421-5.389474-32.336842-10.778947-48.505263-10.778947-10.778947-21.557895-26.947368-32.336842-32.336842-10.778947-10.778947-26.947368-16.168421-43.11579-21.557895-16.168421-5.389474-32.336842-5.389474-48.505263-5.389474-26.947368 0-48.505263 5.389474-70.063158 10.778948s-43.115789 16.168421-64.673684 32.336842v91.621053h21.557895c0-16.168421 5.389474-26.947368 10.778947-37.726316 5.389474-10.778947 10.778947-21.557895 21.557895-26.947369 10.778947-5.389474 16.168421-16.168421 26.947368-16.168421 10.778947-5.389474 21.557895-5.389474 37.726316-5.389473 10.778947 0 21.557895 0 32.336842 5.389473 10.778947 5.389474 21.557895 10.778947 26.947368 16.168421 5.389474 5.389474 16.168421 16.168421 21.557895 26.947369 5.389474 10.778947 5.389474 21.557895 5.389474 32.336842 0 21.557895-5.389474 37.726316-16.168421 53.894737-10.778947 16.168421-21.557895 26.947368-37.726316 37.726316-16.168421 10.778947-32.336842 16.168421-48.505263 21.557894-16.168421 5.389474-37.726316 10.778947-53.894737 16.168421l5.389474 26.947369c5.389474-5.389474 16.168421-5.389474 26.947368-5.389474h26.947369c16.168421 0 32.336842 0 48.505263 5.389474 16.168421 5.389474 26.947368 10.778947 37.726315 21.557894 10.778947 10.778947 16.168421 21.557895 26.947369 32.336843 5.389474 16.168421 10.778947 32.336842 10.778947 48.505263 0 16.168421 0 32.336842-5.389473 48.505263-5.389474 16.168421-10.778947 26.947368-21.557895 37.726316-10.778947 10.778947-21.557895 21.557895-37.726316 26.947368-16.168421 5.389474-32.336842 10.778947-48.505263 10.778947-21.557895 0-37.726316-5.389474-59.284211-10.778947-21.557895-10.778947-37.726316-21.557895-48.505263-32.336842-10.778947 0-16.168421 5.389474-26.947368 16.168421-5.389474-16.168421-5.389474-5.389474-5.389474 5.389474s5.389474 16.168421 10.778947 21.557894c5.389474 5.389474 10.778947 10.778947 21.557895 16.168422 10.778947 5.389474 16.168421 5.389474 26.947369 5.389473h32.336842c26.947368 0 53.894737-5.389474 86.231579-10.778947 26.947368-10.778947 53.894737-21.557895 75.452631-37.726316 21.557895-16.168421 43.115789-37.726316 53.894737-64.673684 16.168421-26.947368 21.557895-53.894737 21.557895-86.231579-5.389474-10.778947-10.778947-26.947368-16.168421-37.726316z",fill:"#bfbfbf"})})},le=function(n){return _jsx(Icon,_objectSpread({component:re},n))},oe=function(){return(0,e.jsx)("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",children:(0,e.jsx)("path",{d:"M682.666667 213.333333a21.333333 21.333333 0 0 1 20.992 17.493334l0.341333 3.84v42.666666H810.666667a42.666667 42.666667 0 0 1 42.666666 42.666667v469.333333a42.666667 42.666667 0 0 1-42.666666 42.666667H256a42.666667 42.666667 0 0 1-42.666667-42.666667v-469.333333a42.666667 42.666667 0 0 1 42.666667-42.666667h106.666667v-42.666666a21.333333 21.333333 0 0 1 42.325333-3.84l0.341333 3.84v42.666666h256v-42.666666A21.333333 21.333333 0 0 1 682.666667 213.333333z m128 234.666667H256v341.333333h554.666667v-341.333333z m-448-128H256v85.333333h554.666667v-85.333333h-106.666667V341.333333a21.333333 21.333333 0 0 1-42.325333 3.84L661.333333 341.333333v-21.333333h-256V341.333333a21.333333 21.333333 0 0 1-42.325333 3.84L362.666667 341.333333v-21.333333z",fill:"#bfbfbf","p-id":"4605"})})},de=function(n){return(0,e.jsx)(ie.Z,(0,j.Z)({component:oe},n))},ge=function(){return _jsx("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",children:_jsx("path",{d:"M510.61572266 166.37561035A368.95935059 368.95935059 0 0 0 141.70581055 535.33496094a368.95935059 368.95935059 0 0 0 368.95935059 368.95935059 368.95935059 368.95935059 0 0 0 368.95935058-368.95935059A368.95935059 368.95935059 0 0 0 510.61572266 166.42504883z m0 688.77685547a319.86694336 319.86694336 0 0 1 0-639.63500977 319.86694336 319.86694336 0 0 1 0 639.63500977z m159.93347168-295.24658204h-159.93347168V400.02185058a12.35961914 12.35961914 0 0 0-12.26074219-12.31018066h-24.62036133a12.35961914 12.35961914 0 0 0-12.26074219 12.31018067v196.76513671a12.35961914 12.35961914 0 0 0 12.26074219 12.31018067h196.76513672a12.35961914 12.35961914 0 0 0 12.35961914-12.31018066v-24.57092286a12.26074219 12.26074219 0 0 0-12.35961914-12.31018066z","p-id":"9722",fill:"#bfbfbf"})})},Ke=function(n){return _jsx(Icon,_objectSpread({component:ge},n))},Pe=function(){return _jsx("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",children:_jsx("path",{d:"M566.272 217.088c-11.264-7.168-24.576-11.264-41.984-11.264-4.096 0-8.192 0-12.288 1.024-45.056 7.168-74.752 48.128-93.184 87.04-8.192 18.432-15.36 37.888-22.528 56.32-3.072 9.216-7.168 17.408-10.24 26.624 0 1.024-5.12 18.432-6.144 18.432h-46.08c-6.144 0-11.264 5.12-11.264 11.264 0 6.144 5.12 11.264 11.264 11.264h39.936l-22.528 96.256c-22.528 107.52-53.248 230.4-60.416 251.904-7.168 22.528-17.408 33.792-31.744 33.792-3.072 0-5.12-1.024-7.168-2.048-2.048-1.024-3.072-3.072-3.072-5.12s1.024-5.12 4.096-9.216c3.072-4.096 4.096-9.216 4.096-13.312 0-8.192-3.072-15.36-8.192-20.48-6.144-5.12-12.288-7.168-19.456-7.168s-14.336 3.072-20.48 8.192c-1.024 7.168-4.096 14.336-4.096 23.552 0 12.288 5.12 22.528 15.36 31.744 10.24 9.216 23.552 13.312 40.96 13.312 27.648 0 52.224-12.288 70.656-33.792 11.264-13.312 19.456-28.672 26.624-45.056 22.528-50.176 34.816-105.472 48.128-158.72 13.312-54.272 25.6-108.544 36.864-162.816h44.032c6.144 0 11.264-5.12 11.264-11.264 0-6.144-5.12-11.264-11.264-11.264h-40.96c22.528-84.992 48.128-143.36 53.248-151.552 8.192-14.336 17.408-21.504 27.648-21.504 4.096 0 6.144 1.024 7.168 3.072 1.024 2.048 2.048 4.096 2.048 5.12 0 1.024-1.024 4.096-4.096 9.216-3.072 5.12-4.096 10.24-4.096 15.36 0 7.168 3.072 13.312 8.192 18.432s12.288 8.192 19.456 8.192 14.336-3.072 19.456-8.192 8.192-12.288 8.192-21.504c-1.024-17.408-6.144-28.672-17.408-35.84z m204.8 258.048c16.384 0 48.128-13.312 48.128-56.32s-30.72-45.056-40.96-45.056c-18.432 0-37.888 13.312-54.272 41.984-16.384 28.672-34.816 61.44-34.816 61.44h-1.024c-4.096-20.48-7.168-36.864-9.216-45.056-3.072-17.408-23.552-56.32-66.56-56.32s-81.92 24.576-81.92 24.576c-7.168 4.096-12.288 12.288-12.288 21.504 0 14.336 11.264 25.6 25.6 25.6 4.096 0 8.192-1.024 11.264-3.072 0 0 32.768-18.432 38.912 0 2.048 5.12 4.096 11.264 6.144 17.408 8.192 27.648 15.36 60.416 22.528 90.112L596.992 593.92s-31.744-11.264-48.128-11.264-48.128 13.312-48.128 56.32 30.72 45.056 40.96 45.056c18.432 0 37.888-13.312 54.272-41.984 16.384-28.672 34.816-61.44 34.816-61.44 5.12 26.624 10.24 48.128 13.312 56.32 10.24 30.72 34.816 48.128 67.584 48.128 0 0 33.792 0 72.704-22.528 9.216-4.096 16.384-13.312 16.384-23.552 0-14.336-11.264-25.6-25.6-25.6-4.096 0-8.192 1.024-11.264 3.072 0 0-27.648 15.36-37.888 3.072-7.168-13.312-12.288-30.72-17.408-52.224-4.096-19.456-9.216-41.984-13.312-63.488l28.672-40.96c-1.024 1.024 30.72 12.288 47.104 12.288z","p-id":"1938",fill:"#bfbfbf"})})},Le=function(n){return _jsx(Icon,_objectSpread({component:Pe},n))},Ye=s(27484),Se=s.n(Ye),Ue=s(1615),He=s(10285),Mt=s.n(He);Se().extend(Mt());function O(r,n){for(var t=[],i=r;i<=n;i++)t.push("".concat(i).padStart(2,"0"));return t}function ee(r,n){var t=31;return[1,3,5,7,8,10,12].indexOf(n)!==-1?t=31:(t=30,n===2&&r%4==0?t=29:n===2&&r%4!=0&&(t=28)),t}var te=function(n){var t=n.onChange,i=n.value,a=i===void 0?"":i,l=n.suffix,v=n.readOnly,d=n.dateTimeFormat,c=d===void 0?"YYYY-MM-DD":d,m=n.style,h=n.bordered,g=n.className,p=(0,o.useRef)(0),x=(0,o.useState)([]),C=(0,B.Z)(x,2),y=C[0],E=C[1],S=(0,o.useState)(a.split(/-|\s|:/)),b=(0,B.Z)(S,2),D=b[0],k=b[1];(0,o.useEffect)(function(){var N=Se()(Se()(a||void 0).format(c),c),Q=O(1900,2100),J=O(1,12),z=O(1,ee(N.year(),N.month())),fe=O(0,23),je=O(0,59),Oe=O(0,59),be=[],De=0;c.includes("YYYY")&&(y.push(Q),be.push(N.year()+""),De++),c.includes("MM")&&(y.push(J),be.push(String(N.month()+1).padStart(2,"0")),De++),c.includes("DD")&&(y.push(z),be.push(String(N.date()).padStart(2,"0")),De++),p.current=De,c.includes("HH")&&(y.push(fe),be.push(String(N.hour()).padStart(2,"0")),De++),c.includes("mm")&&(y.push(je),be.push(String(N.minute()).padStart(2,"0")),De++),c.includes("ss")&&(y.push(Oe),be.push(String(N.second()).padStart(2,"0")),De++),E((0,H.Z)(y)),k(be)},[]);var T=(0,o.useState)(!1),L=(0,B.Z)(T,2),Y=L[0],X=L[1];return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(R.Z,{prefix:(0,e.jsx)(de,{}),readOnly:!0,value:a,style:m,suffix:l,bordered:h,className:g,onClick:function(){v||X(!0)}}),(0,e.jsx)(Ue.cW,{columns:y.map(function(N){return N.map(function(Q){return{label:Q,value:Q}})}),value:D,title:"\u65E5\u671F\u9009\u62E9",visible:Y,onClose:function(){X(!1)},mouseWheel:!0,onSelect:function(Q){c.includes("MM")&&c.includes("DD")&&(y.splice(2,1,O(1,ee(parseInt(Q[0]),parseInt(Q[1])))),E((0,H.Z)(y)))},onConfirm:function(Q){var J=Se()(Q.join("-"),c.replaceAll(/\s|:/g,"-"));t(J.format(c))}})]})};function ne(){var r=new Date;return["".concat(r.getHours()).padStart(2,"0"),"".concat(r.getMinutes()).padStart(2,"0"),"".concat(r.getSeconds()).padStart(2,"0")]}var ue=function(n){var t=n.onChange,i=n.value,a=i===void 0?"":i,l=n.readOnly,v=useRef(),d=useRef();return useEffect(function(){var c=ne(),m=_slicedToArray(c,3),h=m[0],g=m[1],p=m[2],x=a.split(":"),C=_slicedToArray(x,3),y=C[0],E=C[1],S=C[2],b=generateSerialNums(0,23),D=generateSerialNums(0,59),k=generateSerialNums(0,59);v.current=new MobileSelect({title:"\u65F6\u95F4\u9009\u62E9",position:[b.indexOf(y||h),D.indexOf(E||g),k.indexOf(S||p)],wheels:[{data:b},{data:D},{data:k}],onShow:function(){document.getElementsByTagName("body")[0].style.overflow="hidden"},onHide:function(){document.getElementsByTagName("body")[0].style.overflow=null},callback:function(L,Y){t(Y.join(":"))}})},[]),_jsx("div",{ref:d,children:_jsx(_Input,{prefix:_jsx(TimeIcon,{}),readOnly:!0,value:a,onClick:function(){l||v.current.show()}})})},Ee=s(17672),$=s(94184),ye=s.n($),Ce=s(73935),Be=function(n){var t=n.visible,i=n.title,a=n.children,l=n.okContent,v=n.cancelContent,d=n.height,c=n.onCancel;return Ce.createPortal((0,e.jsxs)("div",{className:ye()("mobileDrawer",{"mobileDrawer-show":t}),children:[(0,e.jsx)("div",{className:"grayLayer",onClick:c}),(0,e.jsxs)("div",{className:"content",children:[(0,e.jsx)("div",{className:"btnBar",children:(0,e.jsxs)("div",{className:"fixWidth",children:[(0,e.jsx)("div",{className:"cancel",children:v}),i&&(0,e.jsx)("div",{className:"title",children:i}),(0,e.jsx)("div",{className:"ensure",style:{width:80,display:"flex",justifyContent:"space-between"},children:l})]})}),(0,e.jsx)("div",{className:"panel",children:(0,e.jsx)("div",{className:"fixWidth",children:(0,e.jsx)("div",{className:"wheels",style:{height:d},children:a})})})]})]}),document.body)},st=function(n){var t=n.onChange,i=(0,o.useRef)(null),a=(0,o.useRef)(),l=(0,o.useRef)(),v=(0,o.useState)(!1),d=(0,B.Z)(v,2),c=d[0],m=d[1];return(0,o.useEffect)(function(){var h=i.current;if(h){var g,p=new Ee.Z(i.current,{});a.current=p;var x=Math.max(window.devicePixelRatio||1,1);h.width=h.offsetWidth*x,h.height=h.offsetHeight*x,(g=h.getContext("2d"))===null||g===void 0||g.scale(x,x)}},[]),(0,e.jsxs)("div",{style:{position:"relative"},children:[(0,e.jsx)("span",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:"#aaa",visibility:l.current?"hidden":"visible"},onClick:function(){return m(!0)},children:"\u70B9\u51FB\u7B7E\u540D"}),(0,e.jsx)("img",{src:l.current||void 0,style:{display:"block",width:"100%",height:100,border:l.current?"1px solid #E3E3E3":0},onClick:function(){return m(!0)}}),(0,e.jsx)(Be,{visible:c,title:"\u7535\u5B50\u7B7E\u540D",okContent:(0,e.jsx)("div",{style:{userSelect:"none"},onClick:function(){var g,p;if((g=a.current)!==null&&g!==void 0&&g.isEmpty()){Ue.FN.show({content:"\u7B7E\u540D\u4E0D\u80FD\u4E3A\u7A7A",position:"top",maskStyle:{zIndex:1e4}});return}l.current=(p=a.current)===null||p===void 0?void 0:p.toDataURL(),m(!1),t(a.current.toDataURL())},children:"\u786E\u8BA4\u7B7E\u540D"}),cancelContent:(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",width:80},children:[(0,e.jsx)("div",{onClick:function(){return m(!1)},children:"\u53D6\u6D88"}),(0,e.jsx)("div",{style:{color:"#1e83d3"},onClick:function(){var g;return(g=a.current)===null||g===void 0?void 0:g.clear()},children:"\u91CD\u7F6E"})]}),children:(0,e.jsx)("canvas",{ref:i,style:{width:"100%",height:400,touchAction:"none",userSelect:"none"}})})]})};function nt(r){return r?r.substring(0,r.lastIndexOf("[")):"\u70B9\u51FB\u9009\u62E9"}var lt=function(n){var t=n.onChange,i=n.value,a=n.mapMove,l=a===void 0?!1:a,v=n.optionId,d=(0,o.useRef)(null),c=(0,o.useRef)(),m=(0,o.useRef)(),h=(0,o.useRef)(),g=(0,o.useRef)(),p=(0,o.useState)(function(){if(!!i){var N=i.match(/\[(\d+\.\d+),\s*(\d+\.\d+)\]/);if(N&&N.length>2)return[parseFloat(N[1]),parseFloat(N[2])]}}),x=(0,B.Z)(p,2),C=x[0],y=x[1],E=(0,o.useState)(function(){return nt(i)}),S=(0,B.Z)(E,2),b=S[0],D=S[1],k=(0,o.useState)(!1),T=(0,B.Z)(k,2),L=T[0],Y=T[1],X=(0,o.useCallback)(function(N){m.current.getLocation(N,function(Q,J){if(Q==="complete"&&J.geocodes.length){var z=J.geocodes[0].location;h.current.start(z)}else console.error("\u6839\u636E\u5730\u5740\u67E5\u8BE2\u4F4D\u7F6E\u5931\u8D25")})},[]);return(0,o.useEffect)(function(){window["localMap".concat(v)]=function(){initAMapUI(),AMapUI.loadUI(["misc/PositionPicker"],function(fe){var je=new AMap.Map("map-".concat(v),{zoom:16,center:C,dragEnable:l,zoomEnable:l});if(c.current=je,l){var Oe=new fe({mode:"dragMarker",map:je});Oe.on("success",function(Je){D(Je.address),y([Je.position.lng,Je.position.lat])}),C?Oe.start(C):Oe.start(),je.panBy(0,1),h.current=Oe}var be=new AMap.Geocoder({city:"010"});m.current=be;var De=new AMap.Geolocation({enableHighAccuracy:!0,timeout:1e4,offset:[10,20],zoomToAccuracy:!0,position:"RB"});g.current=De,De.getCurrentPosition(function(Je,ze){Je=="complete"?l?positionPicker.start(ze.position):(je.setCenter(ze.position),D(ze.formattedAddress),y([ze.position.lng,ze.position.lat])):console.error(ze)})})},window._AMapSecurityConfig={securityJsCode:"c92dc7c089f25dcd7944094d3ee1a959"};var N="https://webapi.amap.com/maps?v=1.4.15&key=fce3217057b3ace0b2128cb5d1696c87&callback=localMap".concat(v,"&plugin=AMap.Geocoder,AMap.Geolocation"),Q=document.createElement("script");Q.src=N,document.head.appendChild(Q);var J="https://webapi.amap.com/ui/1.1/main-async.js",z=document.createElement("script");z.src=J,document.head.appendChild(z)},[]),(0,e.jsxs)("div",{style:{position:"relative"},children:[(0,e.jsx)(R.Z.TextArea,{readOnly:!0,value:nt(i),onClick:function(){return Y(!0)}}),(0,e.jsxs)(Be,{visible:L,height:450,onCancel:function(){Y(!1)},okContent:(0,e.jsx)("div",{style:{userSelect:"none"},onClick:function(){C&&t("".concat(b,"[").concat(C[0],", ").concat(C[1],"]")),Y(!1)},children:"\u786E\u8BA4\u9009\u62E9"}),cancelContent:(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",width:200},children:[!l&&(0,e.jsx)(F.Z,{type:"link",children:"\u83B7\u53D6\u4F4D\u7F6E"}),l&&(0,e.jsx)(R.Z.Search,{placeholder:"\u8BF7\u8F93\u5165\u8981\u641C\u7D22\u7684\u5730\u70B9\u3001\u8857\u9053",size:"small",onSearch:function(Q){return X(Q)}})]}),children:[(0,e.jsx)("div",{ref:d,id:"map-".concat(v),style:{height:390}}),(0,e.jsx)("div",{style:{height:50,borderTop:"1px solid #b0b0b0",background:"#fff",padding:"12px 18px"},children:b})]})]})},Ge=function(n){var t,i=n.optionId,a=n.questionId,l=n.checked,v=n.checkedCount,d=n.quota,c=n.statEnabled,m=(0,u.Al)(),h=m.statistics,g=h==null?void 0:h.questionStatistics[a],p=((g==null?void 0:g.optionStatistics.reduce(function(E,S){return E+S.count},0))||0)+v,x=(g==null||(t=g.optionStatistics.find(function(E){return E.optionId===i}))===null||t===void 0?void 0:t.count)||0,C=l?x+1:x,y="".concat(Math.floor(C/p*1e3)/10,"%");return!c&&d===void 0?(0,e.jsx)(e.Fragment,{}):(0,e.jsxs)("span",{className:"vote-info",children:[d&&(0,e.jsxs)("span",{className:"vote-info-quota",style:{color:d-x>0?"#1890ff":"#949aae"},children:["(\u4F59",d-x,")"]}),c&&v>0&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)("span",{className:"vote-info-cnt",children:[C,"\u7968"]}),(0,e.jsx)("span",{className:"vote-info-percent ",children:y}),(0,e.jsx)("div",{className:"vote-info-bar",children:(0,e.jsx)("div",{className:"vote-info-bar-fill",style:{width:y}})})]})]})},ft=s(22385),Xe=s(94199),qe=s(93224),et=s(49914),at=s(30071);function Qe(r){if(r=Number(r),r<=0)return"0:0:0";var n=Math.floor(r/3600),t=Math.floor(r%3600/60),i=Math.floor(r%3600%60),a="".concat(n).padStart(2,"0"),l="".concat(t).padStart(2,"0"),v="".concat(i).padStart(2,"0");return"".concat(a,":").concat(l,":").concat(v)}function it(r){return r<=60?"#ff4d4f":r<=180?"#faad14":"#52c41a"}var ct=(0,A.Pi)(function(){var r,n,t=(0,et.H)(),i=(0,u.Rk)(),a=t.startTime,l=((r=t.project)===null||r===void 0||(n=r.setting.examSetting)===null||n===void 0?void 0:n.maxSubmitMinutes)||0,v=(0,o.useState)(0),d=(0,B.Z)(v,2),c=d[0],m=d[1],h=a+l*60*1e3,g=(0,o.useRef)();return(0,o.useEffect)(function(){if(l!==0)return g.current=window.setInterval(function(){m((h-new Date().getTime())/1e3)},1e3),function(){clearInterval(g.current)}},[h]),(0,o.useEffect)(function(){c<0&&(i.submit(),clearInterval(g.current))},[c]),l===0?(0,e.jsx)(e.Fragment,{}):(0,e.jsxs)("div",{style:{position:"absolute",right:10,fontSize:13},children:[(0,e.jsx)(at.Z,{style:{marginRight:5,color:"#1890ff"}}),"\u5269\u4F59:",(0,e.jsx)("span",{style:{marginLeft:3,color:it(c)},children:Qe(c)})]})}),Te=["size","marked"],rt=(0,A.Pi)(function(r){var n=r.size,t=r.marked,i=t===void 0?!0:t,a=(0,qe.Z)(r,Te),l=(0,u.Rk)(),v=l.props.answerSheetVisible;return v?(0,e.jsx)("svg",(0,j.Z)((0,j.Z)({viewBox:"0 0 1024 1024",version:"1.1",width:r.size||24,height:r.size||24,className:"marked"},a),{},{children:(0,e.jsx)("path",{d:"M662.04973405 329.56834123l108.40905238-142.19888693c4.22372932-5.63163908 4.22372932-11.26327817 1.40790978-18.30282702-2.81581955-5.63163908-8.44745863-9.8553684-15.48700748-9.85536841H218.55815604c-9.8553684 0-16.89491725 7.03954885-16.89491725 16.89491726V867.38987389c0 9.8553684 7.03954885 16.89491725 16.89491725 16.89491726s16.89491725-7.03954885 16.89491726-16.89491726V487.25423562h522.33452519c7.03954885 0 12.67118794-4.22372932 15.48700749-9.8553684s1.40790977-12.67118794-2.81581955-18.30282703l-108.40905238-129.52769896z",fill:i?"#EA4942":"#d8d8d8","p-id":"1287"})})):(0,e.jsx)(e.Fragment,{})}),mt=(0,A.Pi)(function(r){var n,t,i,a=r.id,l=r.name,v=(0,u.Rk)(),d=v.answerSheet,c=d.find(function(x){return x.id===a}),m=(0,et.H)(),h=m==null||(n=m.project)===null||n===void 0?void 0:n.isAuthenticated,g=(t=m.project)===null||t===void 0||(i=t.setting.examSetting)===null||i===void 0?void 0:i.randomSurveyWrong,p=v.exerciseMode;return(!p||!h||a.length<=10)&&!g?(0,e.jsx)(e.Fragment,{}):c!=null&&c.favorite?(0,e.jsx)(Xe.Z,{title:g?"\u79FB\u51FA\u9519\u9898":"\u79FB\u51FA\u6536\u85CF",children:(0,e.jsx)("svg",{viewBox:"0 0 1024 1024",version:"1.1",width:"24",height:"24",style:{float:"right"},onClick:function(){v.favoriteQuestion({templateId:a,name:l},!1)},children:(0,e.jsx)("path",{d:"M484.266667 272.021333l6.634666 6.72c5.973333 5.973333 13.013333 12.842667 21.098667 20.629334l9.194667-8.917334c7.253333-7.04 13.44-13.184 18.56-18.432a193.28 193.28 0 0 1 277.44 0c75.904 77.525333 76.629333 202.794667 2.133333 281.194667L512 853.333333 204.672 553.237333c-74.474667-78.421333-73.770667-203.690667 2.133333-281.216a193.28 193.28 0 0 1 277.44 0z","p-id":"2829",fill:"#d81e06"})})}):(0,e.jsx)(Xe.Z,{title:g?"\u6DFB\u52A0\u5230\u9519\u9898":"\u6536\u85CF",children:(0,e.jsx)("svg",{viewBox:"0 0 1024 1024",version:"1.1",width:24,height:24,style:{float:"right"},onClick:function(){v.favoriteQuestion({templateId:a,name:l})},children:(0,e.jsx)("path",{d:"M484.267 272.021l6.634 6.72c5.974 5.974 13.014 12.843 21.099 20.63l9.195-8.918c7.253-7.04 13.44-13.184 18.56-18.432a193.28 193.28 0 0 1 277.44 0c75.904 77.526 76.629 202.795 2.133 281.195L512 853.333 204.672 553.237c-74.475-78.421-73.77-203.69 2.133-281.216a193.28 193.28 0 0 1 277.44 0z m293.162 232.15c46.272-53.76 44.182-136.15-5.973-187.371a129.28 129.28 0 0 0-185.984 0l-15.125 15.104a1687.253 1687.253 0 0 1-4.395 4.31L512 388.18l-49.28-47.445-13.227-12.928-10.965-11.008a129.28 129.28 0 0 0-186.005 0c-51.456 52.565-52.31 137.963-2.198 191.573L512 763.883l261.675-255.531 3.754-4.181z","p-id":"2674",fill:"#d8d8d8"})})})}),Ie=(0,A.Pi)(function(r){var n=(0,u.Rk)(),t=r.finished,i=r.marked,a=r.seqNo,l=r.id;return(0,e.jsxs)("div",{className:ye()("answer-sheet-item",{marked:i,finished:t}),onClick:function(){return n.changePageIndex(l)},children:[a,i&&(0,e.jsx)(rt,{style:{position:"absolute",left:32,bottom:32},size:24})]})}),dt=(0,A.Pi)(function(){var r=(0,u.Rk)(),n=r.answerSheet;return(0,e.jsx)(e.Fragment,{children:n.filter(function(t){return t.display!=="none"}).map(function(t,i){return(0,o.createElement)(Ie,(0,j.Z)((0,j.Z)({},t),{},{seqNo:i+1,key:t.id}))})})}),gt=(0,A.Pi)(function(){var r=(0,u.Rk)(),n=r.answerSheet,t=n.filter(function(l){return l.finished}).length,i=n.filter(function(l){return!l.finished}).length,a=n.filter(function(l){return l.marked}).length;return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)("div",{className:"finished",children:["\u5DF2\u7B54",(0,e.jsx)("span",{style:{marginLeft:4},children:t})]}),(0,e.jsxs)("div",{className:"unFinished",children:["\u672A\u7B54",(0,e.jsx)("span",{style:{marginLeft:4},children:i})]}),(0,e.jsxs)("div",{className:"marked",children:[(0,e.jsx)(rt,{size:18}),"\u6807\u8BB0",(0,e.jsx)("span",{style:{marginLeft:4},children:a})]})]})}),Zt=(0,A.Pi)(function(){var r=(0,u.Rk)(),n=r.props.answerSheetVisible,t=r.answerSheetVisible;return n?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",marginRight:12},onClick:function(){r.answerSheetVisible=!0},children:[(0,e.jsx)("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",children:(0,e.jsx)("path",{d:"M800 96c70.4 0 128 57.6 128 128v576c0 70.4-57.6 128-128 128H224c-70.4 0-128-57.6-128-128V224c0-70.4 57.6-128 128-128h576z m0 64H224c-35.2 0-60.8 25.6-64 60.8V800c0 35.2 25.6 60.8 60.8 64H800c35.2 0 60.8-25.6 64-60.8V224c0-35.2-25.6-60.8-60.8-64H800zM416 640c35.2 0 64 28.8 64 64s-28.8 64-64 64H288c-35.2 0-64-28.8-64-64s28.8-64 64-64h128z m320 0c35.2 0 64 28.8 64 64s-28.8 64-64 64h-128c-35.2 0-64-28.8-64-64s28.8-64 64-64h128z m-320-192c35.2 0 64 28.8 64 64s-28.8 64-64 64H288c-35.2 0-64-28.8-64-64s28.8-64 64-64h128z m320 0c35.2 0 64 28.8 64 64s-28.8 64-64 64h-128c-35.2 0-64-28.8-64-64s28.8-64 64-64h128zM416 256c35.2 0 64 28.8 64 64s-28.8 64-64 64H288c-35.2 0-64-28.8-64-64s28.8-64 64-64h128z m320 0c35.2 0 64 28.8 64 64s-28.8 64-64 64h-128c-35.2 0-64-28.8-64-64s28.8-64 64-64h128z",fill:"#8c8c8c"})}),"\u7B54\u9898\u5361"]}),(0,e.jsx)(Ue.GI,{visible:t,onMaskClick:function(){r.answerSheetVisible=!1},className:"adm-answer-sheet",bodyStyle:{borderTopLeftRadius:"8px",borderTopRightRadius:"8px",overflowY:"auto"},children:(0,e.jsxs)("div",{className:"answer-sheet",children:[(0,e.jsxs)("div",{className:"answer-sheet-header",children:["\u7B54\u9898\u5361",(0,e.jsx)(ct,{})]}),(0,e.jsx)("div",{className:"answer-sheet-content",children:(0,e.jsx)(dt,{})}),(0,e.jsx)("div",{className:"answer-sheet-footer",children:(0,e.jsx)(gt,{})})]})})]}):(0,e.jsx)(e.Fragment,{})}),Dt=(0,A.Pi)(function(r){var n=(0,u.Rk)(),t=n.reviewAnswer[r.qId];return t&&!t.visible?(0,e.jsx)(F.Z,{block:!0,ghost:!0,className:"review-answer-btn",style:{marginTop:10},onClick:function(){t.visible=!0},children:"\u67E5\u770B\u7B54\u6848"}):(0,e.jsx)(e.Fragment,{})}),xt=s(88983),yt=s(66253),ut=s(8913),We=s(50675),$e=s(12968),Me=s(26141),Ct=s(91220),pt=s(15020);function Fe(r,n,t){if(!r)return"";var i=RegExp("_{3,}","g"),a=r.matchAll(i),l=[],v=(0,Ct.Z)(a),d;try{for(v.s();!(d=v.n()).done;){var c=d.value,m=c.index,h=c.index+c[0].length;l.push([m,h])}}catch(y){v.e(y)}finally{v.f()}if(l.length===0)return r;for(var g=l[0][0]===0?"":r.substring(0,l[0][0]),p=function(E){var S,b,D=l[E],k=l[E+1],T=r.substring(D[0],D[1]),L=r.substring(D[1],k?k[0]:r.length),Y=n[E];if(!Y)return"break";var X=t?t[Y.id]:"";if(((S=Y.attribute)===null||S===void 0?void 0:S.dataType)==="select"){var N,Q;X=(N=Y.dataSource)===null||N===void 0||(Q=N.find(function(fe){return fe.value===X}))===null||Q===void 0?void 0:Q.label}else if(((b=Y.attribute)===null||b===void 0?void 0:b.dataType)==="selectDict"){var J,z;X=(J=X)===null||J===void 0||(z=J.split("|"))===null||z===void 0?void 0:z[1]}g+='<input data-id="'.concat(n[E].id,`" value='`).concat(X||"",`'  class="horz-blank" style="width:`).concat(12*T.length,'px">'),g+=L},x=0;x<l.length;x++){var C=p(x);if(C==="break")break}return g}function vt(r,n){return r.name===n?!0:r.parent?vt(r.parent,n):!1}var Ve=(0,A.Pi)(function(r){var n=r.content,t=r.children,i=r.value,a=i===void 0?{}:i,l=r.onChange,v=r.onBlur,d=r.schema,c=r.isTitle,m=(0,u.Rk)(),h=m.reviewAnswer[d.id];function g(p,x){var C,y;if(a[p]===x)return a;a[p]=x;var E=t.find(function(k){return k.id===p}),S=E==null||(C=E.attribute)===null||C===void 0?void 0:C.cascaderLevel,b=E==null||(y=E.attribute)===null||y===void 0?void 0:y.dictCode;b&&S&&t.filter(function(k){var T;return((T=k.attribute)===null||T===void 0?void 0:T.dictCode)===b&&k.attribute.cascaderLevel&&k.attribute.cascaderLevel>S}).forEach(function(k){delete a[k.id]}),(x===""||x===void 0)&&delete a[p];var D=(0,j.Z)({},a);if(Object.keys(D).length!==0)return D}return(0,e.jsx)("div",{className:"pe-view",style:{lineHeight:1.5},onClick:function(x){x.preventDefault(),x.stopPropagation()},children:(0,pt.ZP)((0,I.qZ)(n,t.filter(function(p){var x,C;return c?(x=p.attribute)===null||x===void 0?void 0:x.fieldInTitle:!((C=p.attribute)!==null&&C!==void 0&&C.fieldInTitle)})),{replace:function(x){if(x.name==="img"){var C=x.attribs.width;return(0,e.jsx)(Me.Z,{width:C?parseFloat(C):void 0,src:x.attribs.src,style:{cursor:"zoom-in"},fallback:"data:image/png;base64,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",preview:{mask:!1}})}var y=x.attribs;if(!!y){var E=y["data-id"];if(E){var S,b,D,k,T,L=(0,pt.e_)(y),Y=vt(x,"tr");Y&&L.style&&(L.style=(0,j.Z)((0,j.Z)({},L.style),{},{width:"96%"}));var X=t.findIndex(function(Je){return Je.id===E}),N=t[X],Q=(S=t[X-1])===null||S===void 0?void 0:S.id,J=a[Q],z=(N==null||(b=N.attribute)===null||b===void 0?void 0:b.dataType)||"text",fe=N==null||(D=N.attribute)===null||D===void 0?void 0:D.dictCode,je=N==null||(k=N.attribute)===null||k===void 0?void 0:k.dateTimeFormat,Oe=N==null||(T=N.attribute)===null||T===void 0?void 0:T.cascaderLevel,be=h==null?void 0:h[E].isCorrect,De=ye()(L.className,{selected:h&&h.visible,"is-correct":h&&h.visible&&be},"horz-blank");return(0,e.jsx)(he,(0,j.Z)((0,j.Z)({},L),{},{type:z,value:a[E],parentValue:J,cascaderLevel:Oe,onChange:function(ze){return l(g(E,ze))},onBlur:function(ze){return v(g(E,ze))},dictCode:fe,dateTimeFormat:je,schema:N,bordered:!1,icon:!1,className:De,autoSize:!0}))}}}})})}),ot=(0,A.Pi)(function(r){var n=r.qId,t=r.schema,i=r.value,a=r.onChange,l=r.onBlur,v=r.disabled,d=t.id,c=t.title,m=t.attribute,h=m===void 0?{}:m,g=(0,u.Rk)(),p=g.reviewAnswer[n],x=p==null?void 0:p[d].selected,C=p==null?void 0:p[d].isCorrect,y=h.dataType;return p&&p.visible&&(x||C)?(0,e.jsxs)("label",{className:ye()("ant-radio-wrapper ant-radio-wrapper-in-form-item option-item",{selected:x,"is-correct":C}),onClick:function(){v||r.onChange()},children:[(0,e.jsxs)("span",{children:[!C&&(0,e.jsx)(ut.Z,{style:{color:"#ff6d56",marginRight:10}}),C&&(0,e.jsx)(We.Z,{style:{color:"#00bf6f",marginRight:10}})]}),(0,e.jsx)("div",{className:"option-label-wrap",children:(0,e.jsx)(Z.Z,{html:c})})]}):(0,e.jsx)(yt.ZP,{value:d,className:"option-item",onClick:function(){v||r.onChange(i)},disabled:v,children:y==="horzBlank"&&t.children?(0,e.jsx)(Ve,{schema:t,content:c,children:t.children,value:typeof i=="boolean"?{}:i,onBlur:l,onChange:a}):(0,e.jsx)(Z.Z,{html:c})})}),ht=s(63185),Et=s(9676),Bt=s(36862),we=s(46533),Lt=(0,A.Pi)(function(r){var n=r.qId,t=r.schema,i=r.checked,a=r.onChange,l=r.value,v=r.onBlur,d=r.disabled,c=t.id,m=t.title,h=t.attribute,g=h===void 0?{}:h,p=(0,u.Rk)(),x=p.reviewAnswer[n],C=x==null?void 0:x[c].selected,y=x==null?void 0:x[c].isCorrect,E=g.dataType;return x&&x.visible&&(C||y)?(0,e.jsxs)("label",{className:ye()("ant-checkbox-wrapper ant-checkbox-wrapper-in-form-item option-item",{selected:C,"is-correct":y}),onClick:function(){return a(!i)},children:[(0,e.jsxs)("span",{children:[!y&&(0,e.jsx)(Bt.Z,{size:17,style:{color:"#ff6d56",marginRight:10,fontSize:17}}),y&&(0,e.jsx)(we.Z,{size:17,style:{color:"#00bf6f",marginRight:10,fontSize:17}})]}),(0,e.jsx)("div",{className:"option-label-wrap",children:(0,e.jsx)(Z.Z,{html:m})})]}):(0,e.jsx)(Et.Z,{value:c,checked:i,onChange:function(b){return a(b.target.checked)},className:"option-item",disabled:d,children:E==="horzBlank"&&t.children?(0,e.jsx)(Ve,{schema:t,content:m,children:t.children,value:typeof l=="boolean"?{}:l,onBlur:v,onChange:function(b){return a(!0,b)}}):(0,e.jsx)(Z.Z,{html:m})})}),jt=(0,A.Pi)(function(r){var n,t=r.schema,i=r.value,a=r.onChange,l=r.qId,v=r.onBlur,d=(0,u.AK)("-body-question"),c=t.id,m=t.attribute,h=t.title,g=m==null?void 0:m.suffix,p=m==null?void 0:m.dataType,x=m==null?void 0:m.readOnly,C=(0,u.Rk)(),y=C.reviewAnswer[l],E=y==null?void 0:y[c].isCorrect;return y&&y.visible?(0,e.jsxs)("span",{style:{position:"relative",width:"100%"},children:[!E&&(0,e.jsx)(ut.Z,{style:{color:"#ff6d56",marginRight:10,position:"absolute",left:4,top:3,zIndex:1}}),E&&(0,e.jsx)(We.Z,{style:{color:"#00bf6f",marginRight:10,position:"absolute",left:4,top:3,zIndex:1}}),(0,e.jsx)(he,{type:p,value:i[c],className:ye()("selected",{"is-correct":E}),onChange:function(b){return a(b)},onBlur:v,style:{paddingLeft:20},schema:t,suffix:g&&(0,e.jsx)("span",{className:d+"-option-suffix",children:(0,e.jsx)(Z.Z,{html:g})})})]}):(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(he,{type:p,value:i[c],dictCode:(n=t.attribute)===null||n===void 0?void 0:n.dictCode,onChange:function(b){return a(b)},onBlur:v,readOnly:x,schema:t,suffix:g&&(0,e.jsx)("span",{className:d+"-option-suffix",children:(0,e.jsx)(Z.Z,{html:g})})})})}),zt=(0,A.Pi)(function(r){var n,t,i=r.qId,a=r.schema,l=(0,u.Rk)(),v=l.reviewAnswer[i],d=(n=a.children)===null||n===void 0?void 0:n.map(function(g){var p;return(p=g.attribute)===null||p===void 0?void 0:p.examCorrectAnswer}).filter(function(g){return g}).join(";"),c=d&&["FillBlank","MultipleBlank","Textarea"].includes(a.type),m=function(){return c?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{className:"answer-analysis-title",children:"\u6B63\u786E\u7B54\u6848\uFF1A"}),(0,e.jsx)("div",{className:"answer-analysis-content",style:{marginBottom:10},children:d})]}):(0,e.jsx)(e.Fragment,{})},h=function(){var p;if((p=a.attribute)!==null&&p!==void 0&&p.examAnalysis){var x;return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{className:"answer-analysis-title",children:"\u7B54\u6848\u89E3\u6790\uFF1A"}),(0,e.jsx)("div",{className:"answer-analysis-content",children:(0,e.jsx)(Z.Z,{html:(x=a.attribute)===null||x===void 0?void 0:x.examAnalysis})})]})}return(0,e.jsx)(e.Fragment,{})};return v&&v.visible&&((t=a.attribute)!==null&&t!==void 0&&t.examAnalysis||c)?(0,e.jsxs)("div",{className:"answer-analysis",children:[m(),h()]}):(0,e.jsx)(e.Fragment,{})}),Kt=(0,e.jsx)("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",children:(0,e.jsx)("path",{d:"M839 73.9c-1.2-2.4-3.7-9.8-7.4-9.8s-11.1 8.6-13.5 12.3l-13.5 12.3c-7.4-6.1-35.6-23.4-43-23.4-11.1 0-104.6 113.2-118.1 129.1l-60.2 68.8-30.7 38.1-43.1-83.6-50.4-100.8c-4.9-9.8-8.6-22.1-22.1-22.1-7.3 0-9.8 3.6-13.5 9.8-3.7 4.9-3.7 4.9-11.1 8.6-8.6 0-12.3 0-19.7 6.2-6.2-4.9-11.1-11.1-19.7-11.1-3.7 0-6.2 1.2-8.6 3.6l-8.6 7.4c-4.8 6.1-15.9 17.2-15.9 25.8 0 11 9.8 31.9 13.5 41.8l90.9 250.8-14.7 17.3c-66.4 79.8-159.8 190.5-210.2 279l-29.5 50.4c-8.6 14.7-29.5 44.3-29.5 60.2 0 3.7 3.8 6.2 6.2 8.6 8.6 7.3 3.6 14.7 1.2 24.5-2.5 7.4-3.7 12.3-3.7 19.7 0 13.5 11.1 27 18.5 38.1 7.3 12.2 9.9 24.5 25.8 24.5 17.2 0 49.2-3.6 63.9-14.7l243.4-377.5 115.6 178.5c6.1 9.8 30.7 54.1 46.7 54.1 14.8 0 38.1-16.1 40.6-30.8-2.6-4.9-6.2-12.3-6.2-17.2 0-7.5 19.7-14.9 24.6-19.7v-9.9c8.6-3.7 13.5-8.6 17.2-17.2l-95.9-205.3-38.2-76.3 13.6-19.7 174.6-225.9c11-14.8 28.3-38.1 41.8-50.4 4.9-5 13.5-12.3 13.5-19.7 0-8.6-19.7-22.1-24.6-34.4z","p-id":"14396",fill:"#fe6c56"})}),Ut=(0,e.jsx)("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",children:(0,e.jsx)("path",{d:"M389.7 604.3L198.2 447.4c-3.7-3-9.1-3-12.8 0.1l-73.3 61.4c-4.4 3.7-4.8 10.3-0.9 14.5l324.1 344.3c5 5.3 13.8 3.6 16.6-3.1 64.4-160.1 244.3-452.8 463.2-664.7 3-2.9 3.9-7.2 2.3-11l-13.7-33c-2.4-5.8-9.5-8-14.7-4.5-243.2 157.7-419.4 353.5-499.3 452.9z",fill:"#fe6c56","p-id":"4913"})}),Ht=(0,A.Pi)(function(r){var n,t=r.schema,i=r.seqNo,a=i===void 0?0:i,l=(0,u.Rk)(),v=l.props.correctAnswerVisible,d=l.props.questionScore,c=(n=t.attribute)===null||n===void 0?void 0:n.examAnswerMode;return!v||!d||c==="none"?(0,e.jsx)(e.Fragment,{}):(0,e.jsxs)("div",{className:"exam-score-info",children:[a>0&&(0,e.jsxs)("span",{children:["\u7B2C",a,"\u9898"]}),d[t.id]?(0,e.jsxs)("div",{style:{display:"flex"},children:[(0,e.jsxs)("span",{className:"score",children:["\u5F97\u5206:",d[t.id],"\u5206"]}),Ut]}):Kt]})}),Qt=(0,A.Pi)(function(r){var n,t=r.schema,i=r.title,a=r.onBlur,l=r.onChange,v=t.type,d=((n=t.children)===null||n===void 0?void 0:n.filter(function(h){var g;return(g=h.attribute)===null||g===void 0?void 0:g.fieldInTitle}))||[],c=(0,A.U$)(),m=c.value;return v!=="HorzBlank"||d.length===0?(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(Z.Z,{html:i})}):(0,e.jsx)(Ve,{schema:t,children:d,content:i,onChange:l,onBlur:a,value:m})});function Wt(r){var n,t=r.schema,i=r.required,a=r.error,l=r.replaceTitle,v=r.index,d=r.onBlur,c=r.onChange,m=r.onFocus,h=r.seqNo,g=(0,o.useContext)(I.ZX),p=g.questionNumber,x=(0,u.AK)("-body-question"),C=(0,o.useState)(),y=(0,B.Z)(C,2),E=y[0],S=y[1],b=(0,u.Rk)(),D=(n=b.answerSheet.find(function(L){return L.id===t.id}))===null||n===void 0?void 0:n.marked,k=t.description;(0,o.useEffect)(function(){if(E){var L,Y;(L=document.querySelector("[data-id='".concat(t.id,"']")))===null||L===void 0||L.classList.remove("ant-form-item-has-error"),(Y=document.querySelector("[data-id='".concat(t.id,"']")))===null||Y===void 0||Y.querySelectorAll("[data-id]").forEach(function(je){je.classList.remove("ant-form-item-has-error")})}if(a.length>0){var X,N=a[0].split("^"),Q=(0,B.Z)(N,2),J=Q[0],z=Q[1],fe;z?(fe=document.querySelector("[data-id='".concat(J,"']")),S(z)):(fe=document.querySelector("[data-id='".concat(t.id,"']")),S(J)),(X=fe)===null||X===void 0||X.classList.add("ant-form-item-has-error")}else E&&S(void 0)},[a]);var T=function(){return E?(0,e.jsx)("span",{className:"question-tag error animated",children:(0,e.jsx)("span",{children:E})}):(0,e.jsx)(e.Fragment,{})};return(0,e.jsxs)("div",{className:x,children:[(0,e.jsx)(Ht,{schema:t,seqNo:h}),(0,e.jsxs)(w.Z.Item,{label:(0,e.jsxs)("h2",{className:x+"-header",children:[i&&(0,e.jsx)("span",{className:"question-required",children:"*"}),(0,e.jsx)("span",{className:"question-seq",children:p&&h?h:""}),(0,e.jsx)(Qt,{schema:t,title:l||t.title,onBlur:d,onChange:c,onFocus:m}),T(),h&&(0,e.jsx)(rt,{marked:D,onClick:function(){b.markQuestion(t.id,!D)}}),h&&(0,e.jsx)(M.rF,{id:t.id,name:t.title}),k&&(0,e.jsx)("div",{className:"question-desc",children:(0,e.jsx)(Z.Z,{html:k})})]}),labelCol:{span:24},wrapperCol:{span:24},children:[r.children,(0,e.jsx)(Dt,{qId:t.id}),(0,e.jsx)(zt,{qId:t.id,schema:t})]})]})}(0,f.eC)("Question",(0,A.Pi)(Wt),"antdMobile");function $t(r){var n=r.schema,t=r.index,i=r.seqNo,a=(0,u.AK)("-body-questionSet"),l=(0,o.useContext)(I.ZX),v=l.questionNumber;return(0,e.jsxs)("div",{className:a,children:[(0,e.jsxs)("div",{className:a+"-title",children:[v&&(0,e.jsx)("span",{className:a+"-title-seq",children:(0,e.jsx)("b",{children:v&&i?i:""})}),(0,e.jsx)(Z.Z,{html:n.title})]}),r.children,(0,e.jsx)("div",{className:a+"-splitLine"})]})}(0,f.eC)("QuestionSet",$t,"antdMobile");var zn=s(84305),Vt=s(88182),Jt=s(82925),Yt=s(3024),Gt=(0,A.Pi)(function(){var r=(0,o.useContext)(I.ZX),n=r.progressBar,t=(0,u.AK)(),i=(0,u.Rk)(),a=i.progress;return n?(0,e.jsx)(Yt.Z,{enabled:!0,top:0,innerActiveClass:"fixed",children:(0,e.jsx)("div",{className:t+"-progress",children:(0,e.jsx)("div",{className:"progress-bar s-prog-wait",children:(0,e.jsx)("div",{className:"progress-done  s-prog-done",style:{width:"".concat(a*100,"%")}})})})}):(0,e.jsx)(e.Fragment,{})});function Xt(r){var n=r.headerVisible,t=r.footerVisible,i=r.schema,a=(0,u.AK)(),l=(0,u.Al)(),v=function(){var c,m=(c=i.attribute)===null||c===void 0?void 0:c.headerImage;if(m)return(0,e.jsx)("img",{src:"/api/public/preview/".concat(m),style:{maxWidth:"100%"}})};return(0,e.jsxs)("div",{className:ye()(a,{preview:l.isPreview}),children:[(0,e.jsx)(Gt,{}),(0,e.jsx)("div",{className:a+"-banner",children:v()}),n&&(0,e.jsx)(U,{}),(0,e.jsx)(Vt.ZP,{locale:Jt.Z,children:(0,e.jsx)(K,{children:r.children})}),t&&(0,e.jsx)(W,{}),(0,e.jsx)(Zt,{})]})}(0,f.eC)("Survey",Xt,"antdMobile");var Kn=s(13062),ke=s(71230),Un=s(89032),Ze=s(15746),ve=s(32059);function qt(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children;function v(d,c){t((0,ve.Z)({},c,d))}return(0,e.jsx)("div",{children:l==null?void 0:l.map(function(d){var c=d.id,m=d.title,h=d.attribute,g=h===void 0?{}:h,p=g.mapMove;return(0,e.jsxs)(ke.Z,{gutter:[10,0],"data-id":c,children:[m&&(0,e.jsx)(Ze.Z,{span:24},d.id),(0,e.jsx)(Ze.Z,{span:24,children:(0,e.jsx)(lt,{optionId:c,onChange:function(C){return v(C,c)},value:a[c],mapMove:p})})]},c)})})}(0,f.RM)("Address",qt,"antdMobile");var Hn=s(43185),At=s(28525),Qn=s(34792),wt=s(48086),bt=s(39428),kt=s(3182),en=s(81473);function tn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=r.readOnly,v=n.children,d=v===void 0?[]:v,c=(0,u.Al)(),m=function(){var h=(0,kt.Z)((0,bt.Z)().mark(function g(p){var x,C,y,E;return(0,bt.Z)().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:x=p.onSuccess,C=p.onError,y=p.file,E=p.onProgress,c.onUpload&&c.onUpload({file:y,fileType:6},function(D){E&&E({percent:D.loaded/D.total*100})}).then(function(D){if(D.success&&x&&(x(D.data),D.data.content&&t((0,ve.Z)({},d[0].id,D.data.content))),!D.success&&C){var k=new Error("Some error");C({event:k})}});case 2:case"end":return b.stop()}},g)}));return function(p){return h.apply(this,arguments)}}();return(0,e.jsx)("div",{children:d==null?void 0:d.map(function(h){var g=h.id,p=h.attribute,x=p===void 0?{}:p,C=x.cameraOnly;return(0,e.jsx)(R.Z,{placeholder:"\u70B9\u51FB\u8BC6\u522B\u6761\u7801",value:a[g],addonAfter:(0,e.jsx)(At.Z,{capture:C?"environment":void 0,disabled:l,maxCount:1,accept:"image/*",beforeUpload:function(E){return x!=null&&x.maxFileSize&&E.size>x.maxFileSize*1024*1024?(wt.default.error("\u6700\u5927\u53EA\u80FD\u4E0A\u4F20 ".concat(x.maxFileSize,"M \u5927\u5C0F\u7684\u6587\u4EF6")),At.Z.LIST_IGNORE):!0},customRequest:m,showUploadList:!1,children:(0,e.jsx)(en.Z,{})},g)})})})}(0,f.RM)("Barcode",tn,"antdMobile");function Pt(r,n){var t;return((t=r.children)===null||t===void 0?void 0:t.reduce(function(i,a,l){if(l===0)i.push(r.dataSource||[]);else{var v=r.children[l-1].id,d=n[v];if(d){var c;i.push(((c=i.slice(-1)[0].find(function(m){return m.value===d}))===null||c===void 0?void 0:c.children)||[])}}return i},[]))||[]}function nn(r){var n,t=r.schema,i=r.onChange,a=r.value,l=a===void 0?{}:a,v=(0,o.useState)(r.value||{}),d=(0,B.Z)(v,2),c=d[0],m=d[1],h=(0,o.useState)(Pt(t,l)),g=(0,B.Z)(h,2),p=g[0],x=g[1];(0,o.useEffect)(function(){if(Object.keys(c).length>0){var y=Pt(t,c);x(y)}},[c,t]);var C=function(E,S,b){var D,k={};(D=t.children)===null||D===void 0||D.forEach(function(T,L){b>L?k[T.id]=c[T.id]:b===L&&(k[E]=S)}),m(k),Pt(t,k).filter(function(T){return T.length>0}).length===Object.values(k).filter(function(T){return T&&T!=="\u8BF7\u9009\u62E9"}).length?i(k):i(void 0)};return(0,e.jsx)(ke.Z,{gutter:[10,10],children:(n=t.children)===null||n===void 0?void 0:n.map(function(y,E){return(0,e.jsx)(Ze.Z,{span:24,children:(0,e.jsx)(Re,{options:p[E]||[],value:c[y.id]||"",onChange:function(b){return C(y.id,b,E)}},y.id)},y.id)})})}(0,f.RM)("Cascader",nn,"antdMobile");function an(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=r.onBlur,v=n.children,d=n.attribute,c=24/((d==null?void 0:d.columns)||1),m=(0,u.AK)("-body-question"),h=!!(d!=null&&d.statEnabled),g=(0,u.Rk)(),p=(0,u.Al)(),x=g.hiddenOption[n.id]||[];(0,o.useEffect)(function(){var y=Object.keys(a),E=!1;y.forEach(function(S){x.includes(S)&&(E=!0,delete a[S])}),E&&t((0,j.Z)({},a))},[x]);function C(y,E,S){E?a[y]=S||!0:delete a[y],t((0,j.Z)({},a))}return(0,o.useEffect)(function(){v==null||v.forEach(function(y){var E;(E=y.attribute)!==null&&E!==void 0&&E.defaultChecked&&Object.keys(a).length===0&&t((0,ve.Z)({},y.id,!0))})},[]),(0,e.jsx)("div",{children:(0,e.jsx)(ke.Z,{children:v==null?void 0:v.filter(function(y){return!x.includes(y.id)}).map(function(y){var E,S,b,D,k=y.id,T=(E=y.attribute)===null||E===void 0?void 0:E.dataType,L=(S=y.attribute)===null||S===void 0?void 0:S.suffix,Y=(b=y.attribute)===null||b===void 0?void 0:b.readOnly,X=(D=y.attribute)===null||D===void 0?void 0:D.quota,N=!1;if(X!==void 0){var Q,J=p.statistics,z=J==null?void 0:J.questionStatistics[n.id],fe=(z==null||(Q=z.optionStatistics.find(function(Oe){return Oe.optionId===k}))===null||Q===void 0?void 0:Q.count)||0,je=X-fe;je<=0&&(N=!0)}return(0,e.jsxs)(Ze.Z,{span:c,className:m+"-option","data-id":k,children:[(0,e.jsx)(Lt,{qId:n.id,disabled:N,schema:y,checked:a.hasOwnProperty(k),onChange:function(be,De){N||C(k,be,De)},value:typeof a[k]=="boolean"?{}:a[k],onBlur:l}),(0,e.jsx)(Ge,{statEnabled:h,quota:X,checked:!!a[k],optionId:k,questionId:n.id,checkedCount:Object.keys(a).length}),T&&(0,e.jsx)(he,{value:typeof a[k]=="boolean"?"":a[k],style:{maxWidth:200},type:T,readOnly:Y,onChange:function(be){return C(k,!0,be)},suffix:L&&(0,e.jsx)("span",{className:m+"-option-suffix",children:(0,e.jsx)(Z.Z,{html:L})})})]},k)})})})}(0,f.RM)("Checkbox",(0,A.Pi)(an),"antdMobile");var Wn=s(62999),rn=s(54680),St=s(3980),sn=s(60780),ln=s.n(sn);function on(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=l===void 0?[]:l,d=v[0].id,c=(0,o.useState)([]),m=(0,B.Z)(c,2),h=m[0],g=m[1];function p(C){d&&((C==null?void 0:C.length)===0?t(void 0):t((0,ve.Z)({},d,C==null?void 0:C.map(function(y){return y.value}))))}var x=(0,o.useMemo)(function(){return ln()(h.map(function(C){return{title:C.name,key:C.id,value:C.id,parentId:C.parentId}}),{parentProperty:"parentId",customID:"key"})},[h]);return(0,o.useEffect)(function(){St.hi.selectDept({}).then(function(C){C.success&&g(C.data)})},[]),(0,e.jsx)(ke.Z,{children:(0,e.jsx)(Ze.Z,{span:24,children:(0,e.jsx)(rn.Z,{style:{width:"100%"},treeCheckable:!0,value:(a[d]||[]).map(function(C){return{value:C}}),treeCheckStrictly:!0,showSearch:!1,showCheckedStrategy:"SHOW_ALL",dropdownStyle:{maxHeight:400,overflow:"auto"},treeData:x,placeholder:"\u8BF7\u9009\u62E9",treeDefaultExpandAll:!0,onChange:p})})})}(0,f.RM)("Dept",on,"antdMobile");function Ot(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=r.onBlur,v=n.children,d=(0,u.AK)("-body-question");function c(h,g){a[h]=g,g===""&&delete a[h],t((0,j.Z)({},a))}function m(h,g){a[h]=g,g===""&&delete a[h],l((0,j.Z)({},a),h)}return(0,e.jsx)("div",{style:{display:"flex",flexDirection:"column",rowGap:10},children:v==null?void 0:v.map(function(h){var g=h.id,p=h.title;return(0,e.jsxs)(ke.Z,{gutter:[10,10],"data-id":g,children:[p&&(0,e.jsx)(Ze.Z,{span:24,children:h.title&&(0,e.jsx)("span",{className:d+"-option-title",children:(0,e.jsx)(Z.Z,{html:h.title})})},h.id),(0,e.jsx)(Ze.Z,{span:24,children:(0,e.jsx)(jt,{qId:n.id,schema:h,value:a,onChange:function(C){return c(g,C)},onBlur:function(C){return m(g,C)}})})]},g)})})}(0,f.RM)("FillBlank",Ot,"antdMobile"),(0,f.RM)("MultipleBlank",Ot,"antdMobile");function Nt(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=r.onBlur,v=n.children,d=n.attribute,c=d===void 0?{}:d,m=c.content;return!m||!v?(0,e.jsx)(e.Fragment,{}):(0,e.jsx)(Ve,{schema:n,children:v.filter(function(h){var g;return!((g=h.attribute)!==null&&g!==void 0&&g.fieldInTitle)}),content:m,onBlur:l,onChange:t,value:a})}Nt.Preview=function(r){var n=r.schema,t=r.value,i=n.children,a=n.attribute,l=a===void 0?{}:a,v=l.content;return(0,e.jsx)("div",{className:"preview",children:(0,e.jsx)("div",{children:(0,e.jsx)(Z.Z,{html:Fe(v,i,t),replaceP:!1})})})},(0,f.RM)("HorzBlank",Nt,"antdMobile");var cn=s(54121),dn=s(51042);function un(r){var n,t=r.schema,i=r.onChange,a=r.value||[{}],l=t.children,v=l===void 0?[]:l,d=t.row,c=d===void 0?[]:d,m=(0,I.Ck)((n=t.attribute)===null||n===void 0?void 0:n.scope,[1,999]);return(0,e.jsxs)("div",{className:"",children:[a.map(function(h,g){return(0,e.jsxs)("div",{children:[(0,e.jsx)("div",{children:g+1}),(0,e.jsxs)("div",{className:"question-item",style:{background:"#f7f8fa",borderRadius:4,padding:8,position:"relative"},children:[v.map(function(p){return(0,e.jsxs)("div",{className:"option-box",style:{marginBottom:10},children:[(0,e.jsx)("div",{style:{color:"#aaa",fontSize:12,marginBottom:5},children:(0,e.jsx)(Z.Z,{html:p.title})}),(0,e.jsx)(jt,{qId:t.id,schema:p,value:h,onChange:function(C){h[p.id]=C,i((0,H.Z)(a))}})]},p.id)}),a.length>1&&(0,e.jsx)(cn.Z,{onClick:function(){a.splice(g,1),i((0,H.Z)(a))},style:{position:"absolute",top:-5,right:-5,color:"#ff4d4f"}})]})]},g)}),(0,e.jsx)("div",{children:a.length<m[2]&&(0,e.jsx)(F.Z,{block:!0,icon:(0,e.jsx)(dn.Z,{}),type:"dashed",onClick:function(){a.push({}),i((0,H.Z)(a))},children:"\u7EE7\u7EED\u586B\u5199"})})]})}(0,f.RM)("MatrixAuto",un,"antdMobile");function vn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=l===void 0?[]:l,d=n.row,c=d===void 0?[]:d;return(0,e.jsx)("div",{className:"",children:c==null?void 0:c.map(function(m){var h=Object.keys(a[m.id]||{});return(0,e.jsxs)("div",{className:"question-item",children:[(0,e.jsx)("h3",{className:"question-sub-title",children:(0,e.jsx)(Z.Z,{html:m.title})}),(0,e.jsx)("div",{className:"checkbtn-group",children:(0,e.jsx)(Ue.Qf,{multiple:!0,value:h,options:v.map(function(g){return{label:g.title,value:g.id}}),onChange:function(p,x){t((0,j.Z)((0,j.Z)({},a),{},(0,ve.Z)({},m.id,p.reduce(function(C,y){return C[y]=!0,C},{}))))}})})]},m.id)})})}(0,f.RM)("MatrixCheckbox",vn,"antdMobile");function hn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=l===void 0?[]:l,d=n.row,c=d===void 0?[]:d;return console.log(JSON.parse(JSON.stringify(a))),(0,e.jsx)("div",{className:"",children:c==null?void 0:c.map(function(m){return(0,e.jsxs)("div",{className:"question-item",children:[(0,e.jsx)("h3",{className:"question-sub-title",children:(0,e.jsx)(Z.Z,{html:m.title})}),(0,e.jsx)("div",{className:"fillBlank-group",children:v.map(function(h){var g=a[m.id]||{};return(0,e.jsxs)("div",{className:"option-box",style:{marginBottom:10},children:[(0,e.jsx)("div",{style:{color:"#aaa",fontSize:12,marginBottom:5},children:(0,e.jsx)(Z.Z,{html:h.title})}),(0,e.jsx)(jt,{qId:n.id,schema:h,value:g,onChange:function(x){var C=a[m.id]||{};t((0,j.Z)((0,j.Z)({},a),{},(0,ve.Z)({},m.id,(0,j.Z)((0,j.Z)({},C),{},(0,ve.Z)({},h.id,x)))))}})]},h.id)})})]},m.id)})})}function fn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=l===void 0?[]:l,d=n.row,c=d===void 0?[]:d;return(0,e.jsx)("div",{className:"",children:c==null?void 0:c.map(function(m){return(0,e.jsxs)("div",{className:"question-item",children:[(0,e.jsx)("h3",{className:"question-sub-title",children:(0,e.jsx)(Z.Z,{html:m.title})}),(0,e.jsx)("div",{className:"fillBlank-group",children:v.map(function(h){var g=a[m.id]||{};return(0,e.jsxs)("div",{className:"option-box",style:{marginBottom:10},children:[(0,e.jsx)("div",{style:{color:"#aaa",fontSize:12,marginBottom:5},children:(0,e.jsx)(Z.Z,{html:h.title})}),(0,e.jsx)(jt,{qId:n.id,schema:h,value:g,readOnly:!0,onChange:function(x){var C=a[m.id]||{};t((0,j.Z)((0,j.Z)({},a),{},(0,ve.Z)({},m.id,(0,j.Z)((0,j.Z)({},C),{},(0,ve.Z)({},h.id,x)))))}})]},h.id)})})]},m.id)})})}(0,f.RM)("MatrixFillBlank",(0,A.Pi)(hn),"antdMobile"),(0,f.uF)("MatrixFillBlank",fn,"antdMobile");function mn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=l===void 0?[]:l,d=n.row,c=d===void 0?[]:d;return(0,e.jsx)("div",{className:"",children:c==null?void 0:c.map(function(m){var h=Object.keys(a[m.id]||{})[0];return(0,e.jsxs)("div",{className:"question-item",children:[(0,e.jsx)("h3",{className:"question-sub-title",children:(0,e.jsx)(Z.Z,{html:m.title})}),(0,e.jsx)("div",{className:"checkbtn-group",children:(0,e.jsx)(Ue.Qf,{value:h?[h]:void 0,options:v.map(function(g){return{label:g.title,value:g.id}}),onChange:function(p,x){p[0]?t((0,j.Z)((0,j.Z)({},a),{},(0,ve.Z)({},m.id,(0,ve.Z)({},p[0],!0)))):(delete a[m.id],t((0,j.Z)({},a)))}})})]},m.id)})})}(0,f.RM)("MatrixRadio",mn,"antdMobile");function gn(r){for(var n=[],t=2;t<r+2;t++)n.push(t/(r+2));return n}function xn(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:10,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=[],a=r;a<=n;a++)i.push(a);return t&&i.reverse(),i}function yn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=l===void 0?[]:l;function d(m,h){t((0,ve.Z)({},m,h))}var c=function(h){var g=h.id,p=a[g],x=gn(p||0),C=h.attribute||{},y=C.scope,E=y===void 0?"[0,10]":y,S=C.npsInvertSort,b=C.npsStart,D=b===void 0?"\u4E0D\u53EF\u80FD":b,k=C.npsEnd,T=k===void 0?"\u6781\u6709\u53EF\u80FD":k,L=E.replace("[","").replace("]","").split(","),Y=(0,B.Z)(L,2),X=Y[0],N=Y[1];return(0,e.jsxs)("div",{className:"nps",children:[(0,e.jsx)("div",{className:"nps-container",children:xn(parseInt(X),parseInt(N),S).map(function(Q){var J={background:"#2672ff",opacity:x[Q]};return(0,e.jsx)("div",{className:"nps-item",style:p>=Q?J:void 0,onClick:function(){return d(g,Q)},children:Q},Q)})}),(0,e.jsxs)("div",{className:"nps-tip",children:[(0,e.jsx)("div",{children:S?T:D}),(0,e.jsx)("div",{children:S?D:T})]})]},g)};return(0,e.jsx)(ke.Z,{children:v.map(function(m){return c(m)})})}(0,f.RM)("Nps",yn,"antdMobile");var Cn=yt.ZP.Group;function Rt(r){var n,t=r.schema,i=r.onChange,a=r.value,l=a===void 0?{}:a,v=t.children,d=(0,u.AK)("-body-question"),c=!!((n=t.attribute)!==null&&n!==void 0&&n.statEnabled),m=(0,u.Rk)(),h=m.hiddenOption[t.id]||[],g=(0,u.Al)();function p(x,C){i((0,ve.Z)({},x,C||!0))}return(0,o.useEffect)(function(){var x=Object.keys(l)[0];h.includes(x)&&i(void 0)},[h]),(0,o.useEffect)(function(){var x;(x=t.children)===null||x===void 0||x.forEach(function(C){var y,E=C.id,S=C.title,b=C.attribute,D=b==null?void 0:b.dataType;(y=C.attribute)!==null&&y!==void 0&&y.defaultChecked&&Object.keys(l).length===0&&i((0,ve.Z)({},E,D?l[E]:S))})},[]),(0,e.jsx)("div",{children:(0,e.jsx)(Cn,{onChange:function(C){},value:Object.keys(l)[0],style:{width:"100%"},children:(0,e.jsx)(ke.Z,{children:v==null?void 0:v.filter(function(x){return!h.includes(x.id)}).map(function(x){var C,y,E,S,b=x.id,D=(C=x.attribute)===null||C===void 0?void 0:C.dataType,k=(y=x.attribute)===null||y===void 0?void 0:y.suffix,T=(E=x.attribute)===null||E===void 0?void 0:E.readOnly,L=!1,Y=(S=x.attribute)===null||S===void 0?void 0:S.quota;if(Y!==void 0){var X,N=g.statistics,Q=N==null?void 0:N.questionStatistics[t.id],J=(Q==null||(X=Q.optionStatistics.find(function(fe){return fe.optionId===b}))===null||X===void 0?void 0:X.count)||0,z=Y-J;z<=0&&(L=!0)}return(0,e.jsxs)(Ze.Z,{span:24,className:d+"-option","data-id":b,children:[(0,e.jsx)(ot,{qId:t.id,schema:x,value:l[b],disabled:L,onChange:function(je){return p(b,je)},onBlur:function(je){}}),(0,e.jsx)(Ge,{statEnabled:c,quota:Y,checked:!!l[b],optionId:b,questionId:t.id,checkedCount:Object.keys(l).length}),D&&(0,e.jsx)(he,{value:l[b]===!0?"":l[b],type:D,style:{maxWidth:200},onChange:function(je){return p(b,je)},readOnly:T,suffix:k&&(0,e.jsx)("span",{className:d+"-option-suffix",children:(0,e.jsx)(Z.Z,{html:k})})})]},b)})})})})}(0,f.RM)("Radio",(0,A.Pi)(Rt),"antdMobile"),(0,f.RM)("Judge",(0,A.Pi)(Rt),"antdMobile");function pn(r){var n=(0,u.AK)("-body-question");return(0,e.jsx)("div",{className:n,children:(0,e.jsx)(Z.Z,{html:"".concat(r.replaceTitle||r.schema.title||"")})})}(0,f.RM)("Remark",pn,"antdMobile");var $n=s(80116),Vn=s(96433),It=s(18079),Tt=s(41687),En=s(46870),Ft=s(18613),jn=s(57206),An={1:(0,e.jsx)(Tt.Z,{}),2:(0,e.jsx)(Tt.Z,{}),3:(0,e.jsx)(En.Z,{}),4:(0,e.jsx)(Ft.Z,{}),5:(0,e.jsx)(Ft.Z,{})};function bn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=l===void 0?[]:l;function d(m,h){t((0,ve.Z)({},m,h))}var c=function(h){var g=h.attribute||{},p=h.id,x=g.scope,C=x===void 0?"[0,5]":x,y=g.scoreStyle,E=C.replace("[","").replace("]","").split(","),S=(0,B.Z)(E,2),b=S[0],D=S[1];return y==="heart"?(0,e.jsx)(It.Z,{defaultValue:parseInt(b),character:(0,e.jsx)(jn.Z,{}),style:{color:"#ea7463"},count:parseInt(D),onChange:function(T){return d(p,T)}},p):y==="smile"?(0,e.jsx)(It.Z,{defaultValue:parseInt(b),character:function(T){var L=T.index;return An[L+1]},count:parseInt(D),onChange:function(T){return d(p,T)}},p):(0,e.jsx)(It.Z,{defaultValue:parseInt(b),count:parseInt(D),onChange:function(T){return d(p,T)}},p)};return(0,e.jsx)(ke.Z,{children:v.map(function(m){return c(m)})})}(0,f.RM)("Score",bn,"antdMobile");function Sn(r){var n,t=r.schema,i=r.onChange,a=r.value,l=a===void 0?{}:a;function v(d){var c,m=(c=t.children)===null||c===void 0?void 0:c.find(function(h){return h.id===d});m&&i((0,ve.Z)({},d,!0))}return(0,o.useEffect)(function(){var d;(d=t.children)===null||d===void 0||d.forEach(function(c){var m,h=c.id;(m=c.attribute)!==null&&m!==void 0&&m.defaultChecked&&Object.keys(l).length===0&&i((0,ve.Z)({},h,!0))})},[]),(0,e.jsx)(ke.Z,{children:(0,e.jsx)(Ze.Z,{span:24,children:(0,e.jsx)(Re,{value:Object.keys(l)[0],onChange:v,options:((n=t.children)===null||n===void 0?void 0:n.map(function(d){return{label:d.title,value:d.id}}))||[]})})})}(0,f.RM)("Select",Sn,"antdMobile");var Jn=s(34669),Mn=s(82833);function Zn(r){var n=r.schema,t=r.onChange,i=n.children,a=(0,u.Al)(),l=(0,o.useState)(0),v=(0,B.Z)(l,2),d=v[0],c=v[1];function m(h,g){var p="signature-".concat(g,"-").concat((0,I.kb)(6),".png");if(!a.onUpload){t((0,ve.Z)({},g,[p]));return}c(1),fetch(h).then(function(x){return x.blob()}).then(function(x){var C=new File([x],p,{type:"image/png"});return a.onUpload({file:C,questionId:n.id,projectId:a.schema.id},function(y){c(y.loaded/y.total*100)})}).then(function(x){x.success&&x.data.id?(t((0,ve.Z)({},g,[x.data.id])),c(100),setTimeout(function(){return c(0)},3e3)):(console.error("\u7B7E\u540D\u4E0A\u4F20\u5931\u8D25:",x),c(0))}).catch(function(x){console.error("\u7B7E\u540D\u4E0A\u4F20\u51FA\u9519:",x),c(0)})}return(0,e.jsx)("div",{children:i==null?void 0:i.map(function(h){var g=h.id,p=h.title;return(0,e.jsxs)(ke.Z,{gutter:[10,0],"data-id":g,children:[p&&(0,e.jsx)(Ze.Z,{span:24},h.id),(0,e.jsxs)(Ze.Z,{span:24,children:[(0,e.jsx)(st,{onChange:function(C){return m(C,g)}}),d>0&&d!==100&&(0,e.jsx)(Mn.Z,{percent:d,size:"small",showInfo:!1})]})]},g)})})}(0,f.RM)("Signature",Zn,"antdMobile");var Dn=function(n){var t,i=n.value;if(!i)return(0,e.jsx)("div",{className:"preview"});var a=(t=Object.values(i)[0])===null||t===void 0?void 0:t[0];return(0,e.jsx)("div",{className:"preview signature-print",children:a&&(0,e.jsx)("img",{src:"/api/file?id=".concat(a),alt:"\u7B7E\u540D\u9884\u89C8"})})};(0,f.uF)("Signature",Dn,"antdMobile");var Yn=s(48736),_t=s(27049);function Bn(r){var n=(0,u.AK)("-body-question");return(0,e.jsx)("div",{className:n,children:(0,e.jsx)(_t.Z,{plain:!0})})}(0,f.RM)("SplitLine",Bn,"antdMobile");function Pn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=(0,u.AK)("-body-question");function d(c,m){a[c]=m,m===""&&delete a[c],t((0,j.Z)({},a))}return(0,e.jsx)("div",{children:l==null?void 0:l.map(function(c){var m=c.id,h=c.attribute,g=c.title,p=(0,I.Ck)(h==null?void 0:h.autoSize,[2,4]);return(0,e.jsxs)(ke.Z,{style:{marginBottom:10},"data-id":m,children:[g&&(0,e.jsx)(Ze.Z,{className:v+"-option",span:4,children:c.title&&(0,e.jsx)("span",{className:v+"-option-title",children:c.title})},c.id),(0,e.jsx)(Ze.Z,{span:24,children:(0,e.jsx)(R.Z.TextArea,{autoSize:{minRows:p[1],maxRows:p[2]},value:a[m]||"",onChange:function(C){return d(m,C.target.value)}})})]},m)})})}(0,f.RM)("Textarea",Pn,"antdMobile");var In=s(43347),wn=At.Z.Dragger;function kn(r){var n=r.schema,t=r.onChange,i=r.value,a=r.readOnly,l=n.children,v=(0,u.Al)(),d=(0,o.useState)(),c=(0,B.Z)(d,2),m=c[0],h=c[1];(0,o.useEffect)(function(){var x=n.children[0].id,C=i&&i[x]||[];m==null&&C.length>0&&St.hi.loadFiles({ids:C}).then(function(y){h(y==null?void 0:y.map(function(E){return{name:E.originalName,uid:E.id,status:"done"}}))})},[i]);var g=function(){var x=(0,kt.Z)((0,bt.Z)().mark(function C(y){var E,S,b,D;return(0,bt.Z)().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:E=y.onSuccess,S=y.onError,b=y.file,D=y.onProgress,v.onUpload&&v.onUpload({file:b,questionId:n.id,projectId:v.schema.id},function(L){D&&D({percent:L.loaded/L.total*100})}).then(function(L){if(L.success&&E&&E(L.data),!L.success&&S){var Y=new Error("Some error");S({event:Y})}});case 2:case"end":return T.stop()}},C)}));return function(y){return x.apply(this,arguments)}}(),p=function(C,y){if(h(C.fileList),(C.file.status==="removed"||C.file.status==="done")&&!C.fileList.find(function(S){return S.status!=="done"})){var E=C.fileList.filter(function(S){return S.status==="done"}).map(function(S){var b;return((b=S.response)===null||b===void 0?void 0:b.id)||S.uid});E.length===0?t(void 0):t((0,ve.Z)({},y,E))}};return(0,e.jsx)("div",{children:l==null?void 0:l.map(function(x){var C=x.id,y=x.attribute,E=y===void 0?{}:y,S=[],b=E.maxFileCount,D=E.maxFileSize,k=E.fileAccept,T=E.cameraOnly;return b&&S.push("\u6700\u591A\u4E0A\u4F20".concat(b,"\u4E2A")),D&&S.push("\u9650\u5236\u6BCF\u4E2A\u6587\u4EF6".concat(D,"M\u4EE5\u5185")),k&&S.push("\u4EC5\u652F\u6301\u6587\u4EF6\u7C7B\u578B\uFF1A".concat(k)),(0,e.jsxs)(wn,{multiple:!0,capture:T?"environment":void 0,disabled:a,maxCount:(E==null?void 0:E.maxFileCount)||20,accept:T?"image/*":void 0,beforeUpload:function(Y){return E!=null&&E.maxFileSize&&Y.size>E.maxFileSize*1024*1024?(wt.default.error("\u6700\u5927\u53EA\u80FD\u4E0A\u4F20 ".concat(E.maxFileSize,"M \u5927\u5C0F\u7684\u6587\u4EF6")),At.Z.LIST_IGNORE):!0},customRequest:g,onChange:function(Y){return p(Y,C)},fileList:m,children:[(0,e.jsx)("p",{className:"ant-upload-drag-icon",children:(0,e.jsx)(In.Z,{})}),(0,e.jsx)("p",{className:"ant-upload-text",children:"\u9009\u62E9\u6216\u62D6\u62FD\u4E0A\u4F20\u6587\u4EF6"}),(0,e.jsx)("p",{className:"ant-upload-hint",style:{whiteSpace:"pre-line",wordBreak:"break-word",textAlign:"center"},children:S.join(",")})]},C)})})}(0,f.RM)("Upload",kn,"antdMobile");var On=s(55686),Nn=G.Z.Option;function Rn(r){var n=r.schema,t=r.onChange,i=r.value,a=i===void 0?{}:i,l=n.children,v=l===void 0?[]:l,d=v[0].id,c=(0,o.useState)([]),m=(0,B.Z)(c,2),h=m[0],g=m[1];function p(y){d&&t((0,ve.Z)({},d,y))}var x=(0,St.zE)(function(y){St.hi.userSelect({name:y,selected:a[d]||[]}).then(function(E){E.success&&g(E.data)})},5),C=function(E){p(E?h.map(function(S){return S.userId}):void 0)};return(0,o.useEffect)(function(){var y;x(""),(y=n.children)===null||y===void 0||y.forEach(function(E){var S,b=E.id;(S=E.attribute)!==null&&S!==void 0&&S.defaultChecked&&Object.keys(a).length===0&&t((0,ve.Z)({},b,!0))})},[]),(0,e.jsx)(ke.Z,{children:(0,e.jsx)(Ze.Z,{span:24,children:(0,e.jsx)(G.Z,{placeholder:"\u8BF7\u9009\u62E9",allowClear:!0,filterOption:!1,mode:"multiple",onClear:function(){t(void 0)},value:a[d]||[],onSearch:x,showSearch:!0,onChange:p,dropdownRender:function(E){return(0,e.jsxs)("div",{children:[E,(0,e.jsx)(_t.Z,{style:{margin:"4px 0"}}),(0,e.jsx)("div",{style:{display:"flex",flexWrap:"nowrap",paddingLeft:10},children:(0,e.jsx)(Et.Z,{onChange:function(b){C(b.target.checked)},children:"\u5168\u9009"})})]})},children:h.map(function(y){return(0,e.jsx)(Nn,{value:y.userId,label:y.name,children:(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{role:"img",style:{marginRight:10},children:(0,e.jsx)(On.Z,{user:y,size:20})}),y.name," ",y.deptName&&"(".concat(y.deptName,")")]})},y.userId)})})})})}(0,f.RM)("User",Rn,"antdMobile");function Tn(){return null}var Fn=function(){return null};(0,f.RM)("RandomSurvey",Tn,"antd"),(0,f.uF)("RandomSurvey",Fn,"antd");var _n=function(n){return(0,e.jsx)(e.Fragment,{children:n.children})},Ln=_n;(0,I.RM)("RichText",(0,o.lazy)(function(){return Promise.resolve().then(s.bind(s,80116))}),"antdMobile"),(0,I.uF)("Upload",(0,o.lazy)(function(){return Promise.all([s.e(1207),s.e(858)]).then(s.bind(s,90858))}),"antdMobile")},80116:function(ae,_,s){"use strict";s.r(_);var P=s(32059),w=s(94657),B=s(88851),I=s(3980),Z=s(67294),A=s(54707),o=s(85893);function u(f){var M=f.schema,e=f.onChange,U=f.value,K=U===void 0?{}:U,V=f.onBlur,F=M.children,W=(0,A.AK)("-body-question"),j=(0,Z.useState)(K[M.children[0].id]),se=(0,w.Z)(j,2),R=se[0],q=se[1],G=(0,A.Rk)(),me=(0,A.Al)(),xe=(0,Z.useCallback)(function(ce,pe){e((0,P.Z)({},ce,pe))},[]);return(0,o.jsx)("div",{children:F==null?void 0:F.map(function(ce){var pe=ce.id;return(0,o.jsx)(B.Mf,{value:R,onChange:function(he){return xe(pe,he)},onUpload:function(he){return I.hi.upload("/api/public/upload",{fileType:4,basePath:G.schema.id,file:he,questionId:M.id,projectId:me.schema.id})}},ce.id)})})}_.default=u},55686:function(ae,_,s){"use strict";var P=s(11849),w=s(94233),B=s(51890),I=s(93224),Z=s(89366),A=s(85893),o=["user"],u=function(M){var e=M.user,U=(0,I.Z)(M,o);return!e||!e.avatar?(0,A.jsx)(B.C,(0,P.Z)({icon:(0,A.jsx)(Z.Z,{}),size:U.size||"small"},U)):(0,A.jsx)(B.C,(0,P.Z)({src:"/api/public/preview/".concat(e.avatar),size:U.size||"small"},U))};_.Z=u},89032:function(ae,_,s){"use strict";var P=s(38663),w=s.n(P),B=s(6999)},9715:function(ae,_,s){"use strict";var P=s(38663),w=s.n(P),B=s(34442),I=s.n(B),Z=s(6999),A=s(22385)},38614:function(ae,_,s){"use strict";s.d(_,{Z:function(){return Mt}});var P=s(83179),w=s(96156),B=s(90484),I=s(22122),Z=s(28991),A=s(67294),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},u=o,f=s(27713),M=function(ee,te){return A.createElement(f.Z,(0,Z.Z)((0,Z.Z)({},ee),{},{ref:te,icon:u}))},e=A.forwardRef(M),U=e,K=s(94184),V=s.n(K),F=s(53124),W=s(33603),j=4;function se(O){var ee=O.dropPosition,te=O.dropLevelOffset,ne=O.prefixCls,ue=O.indent,Ee=O.direction,$=Ee===void 0?"ltr":Ee,ye=$==="ltr"?"left":"right",Ce=$==="ltr"?"right":"left",Be=(0,w.Z)((0,w.Z)({},ye,-te*ue+j),Ce,0);switch(ee){case-1:Be.top=-3;break;case 1:Be.bottom=-3;break;default:Be.bottom=-3,Be[ye]=ue+j;break}return A.createElement("div",{style:Be,className:"".concat(ne,"-drop-indicator")})}var R=s(16928),q=A.forwardRef(function(O,ee){var te=A.useContext(F.E_),ne=te.getPrefixCls,ue=te.direction,Ee=te.virtual,$=O.prefixCls,ye=O.className,Ce=O.showIcon,Be=Ce===void 0?!1:Ce,st=O.showLine,nt=O.switcherIcon,lt=O.blockNode,Ge=lt===void 0?!1:lt,ft=O.children,Xe=O.checkable,qe=Xe===void 0?!1:Xe,et=O.selectable,at=et===void 0?!0:et,Qe=O.draggable,it=O.motion,ct=it===void 0?(0,I.Z)((0,I.Z)({},W.ZP),{motionAppear:!1}):it,Te=ne("tree",$),rt=(0,I.Z)((0,I.Z)({},O),{checkable:qe,selectable:at,showIcon:Be,motion:ct,blockNode:Ge,showLine:Boolean(st),dropIndicatorRender:se}),mt=A.useMemo(function(){if(!Qe)return!1;var Ie={};switch((0,B.Z)(Qe)){case"function":Ie.nodeDraggable=Qe;break;case"object":Ie=(0,I.Z)({},Qe);break;default:break}return Ie.icon!==!1&&(Ie.icon=Ie.icon||A.createElement(U,null)),Ie},[Qe]);return A.createElement(P.Z,(0,I.Z)({itemHeight:20,ref:ee,virtual:Ee},rt,{prefixCls:Te,className:V()((0,w.Z)((0,w.Z)((0,w.Z)((0,w.Z)({},"".concat(Te,"-icon-hide"),!Be),"".concat(Te,"-block-node"),Ge),"".concat(Te,"-unselectable"),!at),"".concat(Te,"-rtl"),ue==="rtl"),ye),direction:ue,checkable:qe&&A.createElement("span",{className:"".concat(Te,"-checkbox-inner")}),selectable:at,switcherIcon:function(dt){return(0,R.Z)(Te,nt,st,dt)},draggable:mt}),ft)}),G=q,me=s(85061),xe=s(28481),ce=s(41018),pe=s(48898),Ne=function(ee,te){return A.createElement(f.Z,(0,Z.Z)((0,Z.Z)({},ee),{},{ref:te,icon:pe.Z}))},he=A.forwardRef(Ne),Ae=he,Re=s(85118),_e=function(ee,te){return A.createElement(f.Z,(0,Z.Z)((0,Z.Z)({},ee),{},{ref:te,icon:Re.Z}))},tt=A.forwardRef(_e),H=tt,ie=s(10225),re=s(1089),le;(function(O){O[O.None=0]="None",O[O.Start=1]="Start",O[O.End=2]="End"})(le||(le={}));function oe(O,ee){function te(ne){var ue=ne.key,Ee=ne.children;ee(ue,ne)!==!1&&oe(Ee||[],ee)}O.forEach(te)}function de(O){var ee=O.treeData,te=O.expandedKeys,ne=O.startKey,ue=O.endKey,Ee=[],$=le.None;if(ne&&ne===ue)return[ne];if(!ne||!ue)return[];function ye(Ce){return Ce===ne||Ce===ue}return oe(ee,function(Ce){if($===le.End)return!1;if(ye(Ce)){if(Ee.push(Ce),$===le.None)$=le.Start;else if($===le.Start)return $=le.End,!1}else $===le.Start&&Ee.push(Ce);return te.includes(Ce)}),Ee}function ge(O,ee){var te=(0,me.Z)(ee),ne=[];return oe(O,function(ue,Ee){var $=te.indexOf(ue);return $!==-1&&(ne.push(Ee),te.splice($,1)),!!te.length}),ne}var Ke=function(O,ee){var te={};for(var ne in O)Object.prototype.hasOwnProperty.call(O,ne)&&ee.indexOf(ne)<0&&(te[ne]=O[ne]);if(O!=null&&typeof Object.getOwnPropertySymbols=="function")for(var ue=0,ne=Object.getOwnPropertySymbols(O);ue<ne.length;ue++)ee.indexOf(ne[ue])<0&&Object.prototype.propertyIsEnumerable.call(O,ne[ue])&&(te[ne[ue]]=O[ne[ue]]);return te};function Pe(O){var ee=O.isLeaf,te=O.expanded;return ee?A.createElement(ce.Z,null):te?A.createElement(Ae,null):A.createElement(H,null)}function Le(O){var ee=O.treeData,te=O.children;return ee||(0,re.zn)(te)}var Ye=function(ee,te){var ne=ee.defaultExpandAll,ue=ee.defaultExpandParent,Ee=ee.defaultExpandedKeys,$=Ke(ee,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]),ye=A.useRef(),Ce=A.useRef(),Be=function(){var We=(0,re.I8)(Le($)),$e=We.keyEntities,Me;return ne?Me=Object.keys($e):ue?Me=(0,ie.r7)($.expandedKeys||Ee||[],$e):Me=$.expandedKeys||Ee,Me},st=A.useState($.selectedKeys||$.defaultSelectedKeys||[]),nt=(0,xe.Z)(st,2),lt=nt[0],Ge=nt[1],ft=A.useState(function(){return Be()}),Xe=(0,xe.Z)(ft,2),qe=Xe[0],et=Xe[1];A.useEffect(function(){"selectedKeys"in $&&Ge($.selectedKeys)},[$.selectedKeys]),A.useEffect(function(){"expandedKeys"in $&&et($.expandedKeys)},[$.expandedKeys]);var at=function(We,$e){var Me;return"expandedKeys"in $||et(We),(Me=$.onExpand)===null||Me===void 0?void 0:Me.call($,We,$e)},Qe=function(We,$e){var Me,Ct=$.multiple,pt=$e.node,Fe=$e.nativeEvent,vt=pt.key,Ve=vt===void 0?"":vt,ot=Le($),ht=(0,I.Z)((0,I.Z)({},$e),{selected:!0}),Et=(Fe==null?void 0:Fe.ctrlKey)||(Fe==null?void 0:Fe.metaKey),Bt=Fe==null?void 0:Fe.shiftKey,we;Ct&&Et?(we=We,ye.current=Ve,Ce.current=we,ht.selectedNodes=ge(ot,we)):Ct&&Bt?(we=Array.from(new Set([].concat((0,me.Z)(Ce.current||[]),(0,me.Z)(de({treeData:ot,expandedKeys:qe,startKey:Ve,endKey:ye.current}))))),ht.selectedNodes=ge(ot,we)):(we=[Ve],ye.current=Ve,Ce.current=we,ht.selectedNodes=ge(ot,we)),(Me=$.onSelect)===null||Me===void 0||Me.call($,we,ht),"selectedKeys"in $||Ge(we)},it=A.useContext(F.E_),ct=it.getPrefixCls,Te=it.direction,rt=$.prefixCls,mt=$.className,Ie=$.showIcon,dt=Ie===void 0?!0:Ie,gt=$.expandAction,Zt=gt===void 0?"click":gt,Dt=Ke($,["prefixCls","className","showIcon","expandAction"]),xt=ct("tree",rt),yt=V()("".concat(xt,"-directory"),(0,w.Z)({},"".concat(xt,"-directory-rtl"),Te==="rtl"),mt);return A.createElement(G,(0,I.Z)({icon:Pe,ref:te,blockNode:!0},Dt,{showIcon:dt,expandAction:Zt,prefixCls:xt,className:yt,expandedKeys:qe,selectedKeys:lt,onSelect:Qe,onExpand:at}))},Se=A.forwardRef(Ye),Ue=Se,He=G;He.DirectoryTree=Ue,He.TreeNode=P.O;var Mt=He},32157:function(ae,_,s){"use strict";var P=s(38663),w=s.n(P),B=s(16695),I=s.n(B)},18446:function(ae,_,s){var P=s(90939);function w(B,I){return P(B,I)}ae.exports=w},17672:function(ae,_,s){"use strict";s.d(_,{Z:function(){return Z}});/*!
 * Signature Pad v4.0.4 | https://github.com/szimek/signature_pad
 * (c) 2022 Szymon Nowak | Released under the MIT license
 */class P{constructor(o,u,f,M){if(isNaN(o)||isNaN(u))throw new Error(`Point is invalid: (${o}, ${u})`);this.x=+o,this.y=+u,this.pressure=f||0,this.time=M||Date.now()}distanceTo(o){return Math.sqrt(Math.pow(this.x-o.x,2)+Math.pow(this.y-o.y,2))}equals(o){return this.x===o.x&&this.y===o.y&&this.pressure===o.pressure&&this.time===o.time}velocityFrom(o){return this.time!==o.time?this.distanceTo(o)/(this.time-o.time):0}}class w{constructor(o,u,f,M,e,U){this.startPoint=o,this.control2=u,this.control1=f,this.endPoint=M,this.startWidth=e,this.endWidth=U}static fromPoints(o,u){const f=this.calculateControlPoints(o[0],o[1],o[2]).c2,M=this.calculateControlPoints(o[1],o[2],o[3]).c1;return new w(o[1],f,M,o[2],u.start,u.end)}static calculateControlPoints(o,u,f){const M=o.x-u.x,e=o.y-u.y,U=u.x-f.x,K=u.y-f.y,V={x:(o.x+u.x)/2,y:(o.y+u.y)/2},F={x:(u.x+f.x)/2,y:(u.y+f.y)/2},W=Math.sqrt(M*M+e*e),j=Math.sqrt(U*U+K*K),se=V.x-F.x,R=V.y-F.y,q=j/(W+j),G={x:F.x+se*q,y:F.y+R*q},me=u.x-G.x,xe=u.y-G.y;return{c1:new P(V.x+me,V.y+xe),c2:new P(F.x+me,F.y+xe)}}length(){const o=10;let u=0,f,M;for(let e=0;e<=o;e+=1){const U=e/o,K=this.point(U,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),V=this.point(U,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(e>0){const F=K-f,W=V-M;u+=Math.sqrt(F*F+W*W)}f=K,M=V}return u}point(o,u,f,M,e){return u*(1-o)*(1-o)*(1-o)+3*f*(1-o)*(1-o)*o+3*M*(1-o)*o*o+e*o*o*o}}class B{constructor(){try{this._et=new EventTarget}catch(o){this._et=document}}addEventListener(o,u,f){this._et.addEventListener(o,u,f)}dispatchEvent(o){return this._et.dispatchEvent(o)}removeEventListener(o,u,f){this._et.removeEventListener(o,u,f)}}function I(A,o=250){let u=0,f=null,M,e,U;const K=()=>{u=Date.now(),f=null,M=A.apply(e,U),f||(e=null,U=[])};return function(...F){const W=Date.now(),j=o-(W-u);return e=this,U=F,j<=0||j>o?(f&&(clearTimeout(f),f=null),u=W,M=A.apply(e,U),f||(e=null,U=[])):f||(f=window.setTimeout(K,j)),M}}class Z extends B{constructor(o,u={}){super();this.canvas=o,this._handleMouseDown=f=>{f.buttons===1&&(this._drawningStroke=!0,this._strokeBegin(f))},this._handleMouseMove=f=>{this._drawningStroke&&this._strokeMoveUpdate(f)},this._handleMouseUp=f=>{f.buttons===1&&this._drawningStroke&&(this._drawningStroke=!1,this._strokeEnd(f))},this._handleTouchStart=f=>{if(f.preventDefault(),f.targetTouches.length===1){const M=f.changedTouches[0];this._strokeBegin(M)}},this._handleTouchMove=f=>{f.preventDefault();const M=f.targetTouches[0];this._strokeMoveUpdate(M)},this._handleTouchEnd=f=>{if(f.target===this.canvas){f.preventDefault();const e=f.changedTouches[0];this._strokeEnd(e)}},this._handlePointerStart=f=>{this._drawningStroke=!0,f.preventDefault(),this._strokeBegin(f)},this._handlePointerMove=f=>{this._drawningStroke&&(f.preventDefault(),this._strokeMoveUpdate(f))},this._handlePointerEnd=f=>{this._drawningStroke&&(f.preventDefault(),this._drawningStroke=!1,this._strokeEnd(f))},this.velocityFilterWeight=u.velocityFilterWeight||.7,this.minWidth=u.minWidth||.5,this.maxWidth=u.maxWidth||2.5,this.throttle="throttle"in u?u.throttle:16,this.minDistance="minDistance"in u?u.minDistance:5,this.dotSize=u.dotSize||0,this.penColor=u.penColor||"black",this.backgroundColor=u.backgroundColor||"rgba(0,0,0,0)",this._strokeMoveUpdate=this.throttle?I(Z.prototype._strokeUpdate,this.throttle):Z.prototype._strokeUpdate,this._ctx=o.getContext("2d"),this.clear(),this.on()}clear(){const{_ctx:o,canvas:u}=this;o.fillStyle=this.backgroundColor,o.clearRect(0,0,u.width,u.height),o.fillRect(0,0,u.width,u.height),this._data=[],this._reset(),this._isEmpty=!0}fromDataURL(o,u={}){return new Promise((f,M)=>{const e=new Image,U=u.ratio||window.devicePixelRatio||1,K=u.width||this.canvas.width/U,V=u.height||this.canvas.height/U,F=u.xOffset||0,W=u.yOffset||0;this._reset(),e.onload=()=>{this._ctx.drawImage(e,F,W,K,V),f()},e.onerror=j=>{M(j)},e.crossOrigin="anonymous",e.src=o,this._isEmpty=!1})}toDataURL(o="image/png",u){switch(o){case"image/svg+xml":return this._toSVG();default:return this.canvas.toDataURL(o,u)}}on(){this.canvas.style.touchAction="none",this.canvas.style.msTouchAction="none",this.canvas.style.userSelect="none";const o=/Macintosh/.test(navigator.userAgent)&&"ontouchstart"in document;window.PointerEvent&&!o?this._handlePointerEvents():(this._handleMouseEvents(),"ontouchstart"in window&&this._handleTouchEvents())}off(){this.canvas.style.touchAction="auto",this.canvas.style.msTouchAction="auto",this.canvas.style.userSelect="auto",this.canvas.removeEventListener("pointerdown",this._handlePointerStart),this.canvas.removeEventListener("pointermove",this._handlePointerMove),document.removeEventListener("pointerup",this._handlePointerEnd),this.canvas.removeEventListener("mousedown",this._handleMouseDown),this.canvas.removeEventListener("mousemove",this._handleMouseMove),document.removeEventListener("mouseup",this._handleMouseUp),this.canvas.removeEventListener("touchstart",this._handleTouchStart),this.canvas.removeEventListener("touchmove",this._handleTouchMove),this.canvas.removeEventListener("touchend",this._handleTouchEnd)}isEmpty(){return this._isEmpty}fromData(o,{clear:u=!0}={}){u&&this.clear(),this._fromData(o,this._drawCurve.bind(this),this._drawDot.bind(this)),this._data=this._data.concat(o)}toData(){return this._data}_strokeBegin(o){this.dispatchEvent(new CustomEvent("beginStroke",{detail:o}));const u={dotSize:this.dotSize,minWidth:this.minWidth,maxWidth:this.maxWidth,penColor:this.penColor,points:[]};this._data.push(u),this._reset(),this._strokeUpdate(o)}_strokeUpdate(o){if(this._data.length===0){this._strokeBegin(o);return}this.dispatchEvent(new CustomEvent("beforeUpdateStroke",{detail:o}));const u=o.clientX,f=o.clientY,M=o.pressure!==void 0?o.pressure:o.force!==void 0?o.force:0,e=this._createPoint(u,f,M),U=this._data[this._data.length-1],K=U.points,V=K.length>0&&K[K.length-1],F=V?e.distanceTo(V)<=this.minDistance:!1,{penColor:W,dotSize:j,minWidth:se,maxWidth:R}=U;if(!V||!(V&&F)){const q=this._addPoint(e);V?q&&this._drawCurve(q,{penColor:W,dotSize:j,minWidth:se,maxWidth:R}):this._drawDot(e,{penColor:W,dotSize:j,minWidth:se,maxWidth:R}),K.push({time:e.time,x:e.x,y:e.y,pressure:e.pressure})}this.dispatchEvent(new CustomEvent("afterUpdateStroke",{detail:o}))}_strokeEnd(o){this._strokeUpdate(o),this.dispatchEvent(new CustomEvent("endStroke",{detail:o}))}_handlePointerEvents(){this._drawningStroke=!1,this.canvas.addEventListener("pointerdown",this._handlePointerStart),this.canvas.addEventListener("pointermove",this._handlePointerMove),document.addEventListener("pointerup",this._handlePointerEnd)}_handleMouseEvents(){this._drawningStroke=!1,this.canvas.addEventListener("mousedown",this._handleMouseDown),this.canvas.addEventListener("mousemove",this._handleMouseMove),document.addEventListener("mouseup",this._handleMouseUp)}_handleTouchEvents(){this.canvas.addEventListener("touchstart",this._handleTouchStart),this.canvas.addEventListener("touchmove",this._handleTouchMove),this.canvas.addEventListener("touchend",this._handleTouchEnd)}_reset(){this._lastPoints=[],this._lastVelocity=0,this._lastWidth=(this.minWidth+this.maxWidth)/2,this._ctx.fillStyle=this.penColor}_createPoint(o,u,f){const M=this.canvas.getBoundingClientRect();return new P(o-M.left,u-M.top,f,new Date().getTime())}_addPoint(o){const{_lastPoints:u}=this;if(u.push(o),u.length>2){u.length===3&&u.unshift(u[0]);const f=this._calculateCurveWidths(u[1],u[2]),M=w.fromPoints(u,f);return u.shift(),M}return null}_calculateCurveWidths(o,u){const f=this.velocityFilterWeight*u.velocityFrom(o)+(1-this.velocityFilterWeight)*this._lastVelocity,M=this._strokeWidth(f),e={end:M,start:this._lastWidth};return this._lastVelocity=f,this._lastWidth=M,e}_strokeWidth(o){return Math.max(this.maxWidth/(o+1),this.minWidth)}_drawCurveSegment(o,u,f){const M=this._ctx;M.moveTo(o,u),M.arc(o,u,f,0,2*Math.PI,!1),this._isEmpty=!1}_drawCurve(o,u){const f=this._ctx,M=o.endWidth-o.startWidth,e=Math.ceil(o.length())*2;f.beginPath(),f.fillStyle=u.penColor;for(let U=0;U<e;U+=1){const K=U/e,V=K*K,F=V*K,W=1-K,j=W*W,se=j*W;let R=se*o.startPoint.x;R+=3*j*K*o.control1.x,R+=3*W*V*o.control2.x,R+=F*o.endPoint.x;let q=se*o.startPoint.y;q+=3*j*K*o.control1.y,q+=3*W*V*o.control2.y,q+=F*o.endPoint.y;const G=Math.min(o.startWidth+F*M,u.maxWidth);this._drawCurveSegment(R,q,G)}f.closePath(),f.fill()}_drawDot(o,u){const f=this._ctx,M=u.dotSize>0?u.dotSize:(u.minWidth+u.maxWidth)/2;f.beginPath(),this._drawCurveSegment(o.x,o.y,M),f.closePath(),f.fillStyle=u.penColor,f.fill()}_fromData(o,u,f){for(const M of o){const{penColor:e,dotSize:U,minWidth:K,maxWidth:V,points:F}=M;if(F.length>1)for(let W=0;W<F.length;W+=1){const j=F[W],se=new P(j.x,j.y,j.pressure,j.time);this.penColor=e,W===0&&this._reset();const R=this._addPoint(se);R&&u(R,{penColor:e,dotSize:U,minWidth:K,maxWidth:V})}else this._reset(),f(F[0],{penColor:e,dotSize:U,minWidth:K,maxWidth:V})}}_toSVG(){const o=this._data,u=Math.max(window.devicePixelRatio||1,1),f=0,M=0,e=this.canvas.width/u,U=this.canvas.height/u,K=document.createElementNS("http://www.w3.org/2000/svg","svg");K.setAttribute("width",this.canvas.width.toString()),K.setAttribute("height",this.canvas.height.toString()),this._fromData(o,(R,{penColor:q})=>{const G=document.createElement("path");if(!isNaN(R.control1.x)&&!isNaN(R.control1.y)&&!isNaN(R.control2.x)&&!isNaN(R.control2.y)){const me=`M ${R.startPoint.x.toFixed(3)},${R.startPoint.y.toFixed(3)} C ${R.control1.x.toFixed(3)},${R.control1.y.toFixed(3)} ${R.control2.x.toFixed(3)},${R.control2.y.toFixed(3)} ${R.endPoint.x.toFixed(3)},${R.endPoint.y.toFixed(3)}`;G.setAttribute("d",me),G.setAttribute("stroke-width",(R.endWidth*2.25).toFixed(3)),G.setAttribute("stroke",q),G.setAttribute("fill","none"),G.setAttribute("stroke-linecap","round"),K.appendChild(G)}},(R,{penColor:q,dotSize:G,minWidth:me,maxWidth:xe})=>{const ce=document.createElement("circle"),pe=G>0?G:(me+xe)/2;ce.setAttribute("r",pe.toString()),ce.setAttribute("cx",R.x.toString()),ce.setAttribute("cy",R.y.toString()),ce.setAttribute("fill",q),K.appendChild(ce)});const V="data:image/svg+xml;base64,",F=`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="${f} ${M} ${this.canvas.width} ${this.canvas.height}" width="${e}" height="${U}">`;let W=K.innerHTML;if(W===void 0){const R=document.createElement("dummy"),q=K.childNodes;R.innerHTML="";for(let G=0;G<q.length;G+=1)R.appendChild(q[G].cloneNode(!0));W=R.innerHTML}const j="</svg>",se=F+W+j;return V+btoa(se)}}}}]);
