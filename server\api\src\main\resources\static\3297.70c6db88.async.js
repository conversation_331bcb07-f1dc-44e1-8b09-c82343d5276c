(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[3297],{3297:function(X,L,v){"use strict";v.d(L,{Z:function(){return yt}});var A=v(65353),f=v(85893),k=v(28293),G=v(50146),J=v(28525);const W=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,p=n=>{if(typeof n!="string")throw new TypeError("Invalid argument expected string");const o=n.match(W);if(!o)throw new Error(`Invalid argument not valid semver ('${n}' received)`);return o.shift(),o},_=n=>n==="*"||n==="x"||n==="X",P=n=>{const o=parseInt(n,10);return isNaN(o)?n:o},F=(n,o)=>typeof n!=typeof o?[String(n),String(o)]:[n,o],I=(n,o)=>{if(_(n)||_(o))return 0;const[e,t]=F(P(n),P(o));return e>t?1:e<t?-1:0},ue=(n,o)=>{for(let e=0;e<Math.max(n.length,o.length);e++){const t=I(n[e]||"0",o[e]||"0");if(t!==0)return t}return 0},xe=(n,o)=>{const e=p(n),t=p(o),r=e.pop(),i=t.pop(),a=ue(e,t);return a!==0?a:r&&i?ue(r.split("."),i.split(".")):r||i?r?-1:1:0};var h=v(67294),E=v(71577),re=v(99177);/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var D=function(n,o){return D=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},D(n,o)};function b(n,o){D(n,o);function e(){this.constructor=n}n.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}var y=function(){return y=Object.assign||function(o){for(var e,t=1,r=arguments.length;t<r;t++){e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(o[i]=e[i])}return o},y.apply(this,arguments)};function Oe(n,o){var e={};for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&o.indexOf(t)<0&&(e[t]=n[t]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(n);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(n,t[r])&&(e[t[r]]=n[t[r]]);return e}function Me(n,o,e,t){var r=arguments.length,i=r<3?o:t===null?t=Object.getOwnPropertyDescriptor(o,e):t,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(n,o,e,t);else for(var c=n.length-1;c>=0;c--)(a=n[c])&&(i=(r<3?a(i):r>3?a(o,e,i):a(o,e))||i);return r>3&&i&&Object.defineProperty(o,e,i),i}function Et(n,o){return function(e,t){o(e,t,n)}}function Ot(n,o){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(n,o)}function Mt(n,o,e,t){function r(i){return i instanceof e?i:new e(function(a){a(i)})}return new(e||(e=Promise))(function(i,a){function c(l){try{s(t.next(l))}catch(m){a(m)}}function u(l){try{s(t.throw(l))}catch(m){a(m)}}function s(l){l.done?i(l.value):r(l.value).then(c,u)}s((t=t.apply(n,o||[])).next())})}function Dt(n,o){var e={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},t,r,i,a;return a={next:c(0),throw:c(1),return:c(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function c(s){return function(l){return u([s,l])}}function u(s){if(t)throw new TypeError("Generator is already executing.");for(;e;)try{if(t=1,r&&(i=s[0]&2?r.return:s[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,s[1])).done)return i;switch(r=0,i&&(s=[s[0]&2,i.value]),s[0]){case 0:case 1:i=s;break;case 4:return e.label++,{value:s[1],done:!1};case 5:e.label++,r=s[1],s=[0];continue;case 7:s=e.ops.pop(),e.trys.pop();continue;default:if(i=e.trys,!(i=i.length>0&&i[i.length-1])&&(s[0]===6||s[0]===2)){e=0;continue}if(s[0]===3&&(!i||s[1]>i[0]&&s[1]<i[3])){e.label=s[1];break}if(s[0]===6&&e.label<i[1]){e.label=i[1],i=s;break}if(i&&e.label<i[2]){e.label=i[2],e.ops.push(s);break}i[2]&&e.ops.pop(),e.trys.pop();continue}s=o.call(n,e)}catch(l){s=[6,l],r=0}finally{t=i=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}}var Ne=Object.create?function(n,o,e,t){t===void 0&&(t=e),Object.defineProperty(n,t,{enumerable:!0,get:function(){return o[e]}})}:function(n,o,e,t){t===void 0&&(t=e),n[t]=o[e]};function zt(n,o){for(var e in n)e!=="default"&&!Object.prototype.hasOwnProperty.call(o,e)&&Ne(o,n,e)}function Ae(n){var o=typeof Symbol=="function"&&Symbol.iterator,e=o&&n[o],t=0;if(e)return e.call(n);if(n&&typeof n.length=="number")return{next:function(){return n&&t>=n.length&&(n=void 0),{value:n&&n[t++],done:!n}}};throw new TypeError(o?"Object is not iterable.":"Symbol.iterator is not defined.")}function Je(n,o){var e=typeof Symbol=="function"&&n[Symbol.iterator];if(!e)return n;var t=e.call(n),r,i=[],a;try{for(;(o===void 0||o-- >0)&&!(r=t.next()).done;)i.push(r.value)}catch(c){a={error:c}}finally{try{r&&!r.done&&(e=t.return)&&e.call(t)}finally{if(a)throw a.error}}return i}function Tt(){for(var n=[],o=0;o<arguments.length;o++)n=n.concat(Je(arguments[o]));return n}function Nt(){for(var n=0,o=0,e=arguments.length;o<e;o++)n+=arguments[o].length;for(var t=Array(n),r=0,o=0;o<e;o++)for(var i=arguments[o],a=0,c=i.length;a<c;a++,r++)t[r]=i[a];return t}function Ce(n){return this instanceof Ce?(this.v=n,this):new Ce(n)}function At(n,o,e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e.apply(n,o||[]),r,i=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(d){t[d]&&(r[d]=function(S){return new Promise(function(C,g){i.push([d,S,C,g])>1||c(d,S)})})}function c(d,S){try{u(t[d](S))}catch(C){m(i[0][3],C)}}function u(d){d.value instanceof Ce?Promise.resolve(d.value.v).then(s,l):m(i[0][2],d)}function s(d){c("next",d)}function l(d){c("throw",d)}function m(d,S){d(S),i.shift(),i.length&&c(i[0][0],i[0][1])}}function It(n){var o,e;return o={},t("next"),t("throw",function(r){throw r}),t("return"),o[Symbol.iterator]=function(){return this},o;function t(r,i){o[r]=n[r]?function(a){return(e=!e)?{value:Ce(n[r](a)),done:r==="return"}:i?i(a):a}:i}}function jt(n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o=n[Symbol.asyncIterator],e;return o?o.call(n):(n=typeof Ae=="function"?Ae(n):n[Symbol.iterator](),e={},t("next"),t("throw"),t("return"),e[Symbol.asyncIterator]=function(){return this},e);function t(i){e[i]=n[i]&&function(a){return new Promise(function(c,u){a=n[i](a),r(c,u,a.done,a.value)})}}function r(i,a,c,u){Promise.resolve(u).then(function(s){i({value:s,done:c})},a)}}function Wt(n,o){return Object.defineProperty?Object.defineProperty(n,"raw",{value:o}):n.raw=o,n}var qe=Object.create?function(n,o){Object.defineProperty(n,"default",{enumerable:!0,value:o})}:function(n,o){n.default=o};function Ft(n){if(n&&n.__esModule)return n;var o={};if(n!=null)for(var e in n)e!=="default"&&Object.prototype.hasOwnProperty.call(n,e)&&Ne(o,n,e);return qe(o,n),o}function Zt(n){return n&&n.__esModule?n:{default:n}}function Lt(n,o){if(!o.has(n))throw new TypeError("attempted to get private field on non-instance");return o.get(n)}function Gt(n,o,e){if(!o.has(n))throw new TypeError("attempted to set private field on non-instance");return o.set(n,e),e}var Ke=v(52796),Qe=v.n(Ke);function et(n,o,e,t,r,i){i===void 0&&(i=0);var a=le(n,o,i),c=a.width,u=a.height,s=Math.min(c,e),l=Math.min(u,t);return s>l*r?{width:l*r,height:l}:{width:s,height:s/r}}function tt(n){return n.width>n.height?n.width/n.naturalWidth:n.height/n.naturalHeight}function be(n,o,e,t,r){r===void 0&&(r=0);var i=le(o.width,o.height,r),a=i.width,c=i.height;return{x:Ie(n.x,a,e.width,t),y:Ie(n.y,c,e.height,t)}}function Ie(n,o,e,t){var r=o*t/2-e/2;return Se(n,-r,r)}function je(n,o){return Math.sqrt(Math.pow(n.y-o.y,2)+Math.pow(n.x-o.x,2))}function We(n,o){return Math.atan2(o.y-n.y,o.x-n.x)*180/Math.PI}function nt(n,o,e,t,r,i,a){i===void 0&&(i=0),a===void 0&&(a=!0);var c=a?ot:rt,u=le(o.width,o.height,i),s=le(o.naturalWidth,o.naturalHeight,i),l={x:c(100,((u.width-e.width/r)/2-n.x/r)/u.width*100),y:c(100,((u.height-e.height/r)/2-n.y/r)/u.height*100),width:c(100,e.width/u.width*100/r),height:c(100,e.height/u.height*100/r)},m=Math.round(c(s.width,l.width*s.width/100)),d=Math.round(c(s.height,l.height*s.height/100)),S=s.width>=s.height*t,C=S?{width:Math.round(d*t),height:d}:{width:m,height:Math.round(m/t)},g=y(y({},C),{x:Math.round(c(s.width-C.width,l.x*s.width/100)),y:Math.round(c(s.height-C.height,l.y*s.height/100))});return{croppedAreaPercentages:l,croppedAreaPixels:g}}function ot(n,o){return Math.min(n,Math.max(0,o))}function rt(n,o){return o}function it(n,o,e,t,r,i){var a=le(o.width,o.height,e),c=Se(t.width/a.width*(100/n.width),r,i),u={x:c*a.width/2-t.width/2-a.width*c*(n.x/100),y:c*a.height/2-t.height/2-a.height*c*(n.y/100)};return{crop:u,zoom:c}}function at(n,o,e){var t=tt(o);return e.height>e.width?e.height/(n.height*t):e.width/(n.width*t)}function st(n,o,e,t,r,i){e===void 0&&(e=0);var a=le(o.naturalWidth,o.naturalHeight,e),c=Se(at(n,o,t),r,i),u=t.height>t.width?t.height/n.height:t.width/n.width,s={x:((a.width-n.width)/2-n.x)*u,y:((a.height-n.height)/2-n.y)*u};return{crop:s,zoom:c}}function Fe(n,o){return{x:(o.x+n.x)/2,y:(o.y+n.y)/2}}function ct(n){return n*Math.PI/180}function le(n,o,e){var t=ct(e);return{width:Math.abs(Math.cos(t)*n)+Math.abs(Math.sin(t)*o),height:Math.abs(Math.sin(t)*n)+Math.abs(Math.cos(t)*o)}}function Se(n,o,e){return Math.min(Math.max(n,o),e)}function Re(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];return n.filter(function(e){return typeof e=="string"&&e.length>0}).join(" ").trim()}var ut=`.reactEasyCrop_Container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  user-select: none;
  touch-action: none;
  cursor: move;
  display: flex;
  justify-content: center;
  align-items: center;
}

.reactEasyCrop_Image,
.reactEasyCrop_Video {
  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */
}

.reactEasyCrop_Contain {
  max-width: 100%;
  max-height: 100%;
  margin: auto;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.reactEasyCrop_Cover_Horizontal {
  width: 100%;
  height: auto;
}
.reactEasyCrop_Cover_Vertical {
  width: auto;
  height: 100%;
}

.reactEasyCrop_CropArea {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-sizing: border-box;
  box-shadow: 0 0 0 9999em;
  color: rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.reactEasyCrop_CropAreaRound {
  border-radius: 50%;
}

.reactEasyCrop_CropAreaGrid::before {
  content: ' ';
  box-sizing: border-box;
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.5);
  top: 0;
  bottom: 0;
  left: 33.33%;
  right: 33.33%;
  border-top: 0;
  border-bottom: 0;
}

.reactEasyCrop_CropAreaGrid::after {
  content: ' ';
  box-sizing: border-box;
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.5);
  top: 33.33%;
  bottom: 33.33%;
  left: 0;
  right: 0;
  border-left: 0;
  border-right: 0;
}
`,lt=1,dt=3,ht=function(n){b(o,n);function o(){var e=n!==null&&n.apply(this,arguments)||this;return e.imageRef=h.createRef(),e.videoRef=h.createRef(),e.containerPosition={x:0,y:0},e.containerRef=null,e.styleRef=null,e.containerRect=null,e.mediaSize={width:0,height:0,naturalWidth:0,naturalHeight:0},e.dragStartPosition={x:0,y:0},e.dragStartCrop={x:0,y:0},e.gestureZoomStart=0,e.gestureRotationStart=0,e.isTouching=!1,e.lastPinchDistance=0,e.lastPinchRotation=0,e.rafDragTimeout=null,e.rafPinchTimeout=null,e.wheelTimer=null,e.currentDoc=typeof document!="undefined"?document:null,e.currentWindow=typeof window!="undefined"?window:null,e.resizeObserver=null,e.state={cropSize:null,hasWheelJustStarted:!1,mediaObjectFit:void 0},e.initResizeObserver=function(){if(!(typeof window.ResizeObserver=="undefined"||!e.containerRef)){var t=!0;e.resizeObserver=new window.ResizeObserver(function(r){if(t){t=!1;return}e.computeSizes()}),e.resizeObserver.observe(e.containerRef)}},e.preventZoomSafari=function(t){return t.preventDefault()},e.cleanEvents=function(){!e.currentDoc||(e.currentDoc.removeEventListener("mousemove",e.onMouseMove),e.currentDoc.removeEventListener("mouseup",e.onDragStopped),e.currentDoc.removeEventListener("touchmove",e.onTouchMove),e.currentDoc.removeEventListener("touchend",e.onDragStopped),e.currentDoc.removeEventListener("gesturemove",e.onGestureMove),e.currentDoc.removeEventListener("gestureend",e.onGestureEnd),e.currentDoc.removeEventListener("scroll",e.onScroll))},e.clearScrollEvent=function(){e.containerRef&&e.containerRef.removeEventListener("wheel",e.onWheel),e.wheelTimer&&clearTimeout(e.wheelTimer)},e.onMediaLoad=function(){var t=e.computeSizes();t&&(e.emitCropData(),e.setInitialCrop(t)),e.props.onMediaLoaded&&e.props.onMediaLoaded(e.mediaSize)},e.setInitialCrop=function(t){if(e.props.initialCroppedAreaPercentages){var r=it(e.props.initialCroppedAreaPercentages,e.mediaSize,e.props.rotation,t,e.props.minZoom,e.props.maxZoom),i=r.crop,a=r.zoom;e.props.onCropChange(i),e.props.onZoomChange&&e.props.onZoomChange(a)}else if(e.props.initialCroppedAreaPixels){var c=st(e.props.initialCroppedAreaPixels,e.mediaSize,e.props.rotation,t,e.props.minZoom,e.props.maxZoom),i=c.crop,a=c.zoom;e.props.onCropChange(i),e.props.onZoomChange&&e.props.onZoomChange(a)}},e.computeSizes=function(){var t,r,i,a,c,u,s=e.imageRef.current||e.videoRef.current;if(s&&e.containerRef){e.containerRect=e.containerRef.getBoundingClientRect(),e.saveContainerPosition();var l=e.containerRect.width/e.containerRect.height,m=((t=e.imageRef.current)===null||t===void 0?void 0:t.naturalWidth)||((r=e.videoRef.current)===null||r===void 0?void 0:r.videoWidth)||0,d=((i=e.imageRef.current)===null||i===void 0?void 0:i.naturalHeight)||((a=e.videoRef.current)===null||a===void 0?void 0:a.videoHeight)||0,S=s.offsetWidth<m||s.offsetHeight<d,C=m/d,g=void 0;if(S)switch(e.state.mediaObjectFit){default:case"contain":g=l>C?{width:e.containerRect.height*C,height:e.containerRect.height}:{width:e.containerRect.width,height:e.containerRect.width/C};break;case"horizontal-cover":g={width:e.containerRect.width,height:e.containerRect.width/C};break;case"vertical-cover":g={width:e.containerRect.height*C,height:e.containerRect.height};break}else g={width:s.offsetWidth,height:s.offsetHeight};e.mediaSize=y(y({},g),{naturalWidth:m,naturalHeight:d}),e.props.setMediaSize&&e.props.setMediaSize(e.mediaSize);var O=e.props.cropSize?e.props.cropSize:et(e.mediaSize.width,e.mediaSize.height,e.containerRect.width,e.containerRect.height,e.props.aspect,e.props.rotation);return(((c=e.state.cropSize)===null||c===void 0?void 0:c.height)!==O.height||((u=e.state.cropSize)===null||u===void 0?void 0:u.width)!==O.width)&&e.props.onCropSizeChange&&e.props.onCropSizeChange(O),e.setState({cropSize:O},e.recomputeCropPosition),e.props.setCropSize&&e.props.setCropSize(O),O}},e.saveContainerPosition=function(){if(e.containerRef){var t=e.containerRef.getBoundingClientRect();e.containerPosition={x:t.left,y:t.top}}},e.onMouseDown=function(t){!e.currentDoc||(t.preventDefault(),e.currentDoc.addEventListener("mousemove",e.onMouseMove),e.currentDoc.addEventListener("mouseup",e.onDragStopped),e.saveContainerPosition(),e.onDragStart(o.getMousePoint(t)))},e.onMouseMove=function(t){return e.onDrag(o.getMousePoint(t))},e.onScroll=function(t){!e.currentDoc||(t.preventDefault(),e.saveContainerPosition())},e.onTouchStart=function(t){!e.currentDoc||(e.isTouching=!0,!(e.props.onTouchRequest&&!e.props.onTouchRequest(t))&&(e.currentDoc.addEventListener("touchmove",e.onTouchMove,{passive:!1}),e.currentDoc.addEventListener("touchend",e.onDragStopped),e.saveContainerPosition(),t.touches.length===2?e.onPinchStart(t):t.touches.length===1&&e.onDragStart(o.getTouchPoint(t.touches[0]))))},e.onTouchMove=function(t){t.preventDefault(),t.touches.length===2?e.onPinchMove(t):t.touches.length===1&&e.onDrag(o.getTouchPoint(t.touches[0]))},e.onGestureStart=function(t){!e.currentDoc||(t.preventDefault(),e.currentDoc.addEventListener("gesturechange",e.onGestureMove),e.currentDoc.addEventListener("gestureend",e.onGestureEnd),e.gestureZoomStart=e.props.zoom,e.gestureRotationStart=e.props.rotation)},e.onGestureMove=function(t){if(t.preventDefault(),!e.isTouching){var r=o.getMousePoint(t),i=e.gestureZoomStart-1+t.scale;if(e.setNewZoom(i,r,{shouldUpdatePosition:!0}),e.props.onRotationChange){var a=e.gestureRotationStart+t.rotation;e.props.onRotationChange(a)}}},e.onGestureEnd=function(t){e.cleanEvents()},e.onDragStart=function(t){var r,i,a=t.x,c=t.y;e.dragStartPosition={x:a,y:c},e.dragStartCrop=y({},e.props.crop),(i=(r=e.props).onInteractionStart)===null||i===void 0||i.call(r)},e.onDrag=function(t){var r=t.x,i=t.y;!e.currentWindow||(e.rafDragTimeout&&e.currentWindow.cancelAnimationFrame(e.rafDragTimeout),e.rafDragTimeout=e.currentWindow.requestAnimationFrame(function(){if(!!e.state.cropSize&&!(r===void 0||i===void 0)){var a=r-e.dragStartPosition.x,c=i-e.dragStartPosition.y,u={x:e.dragStartCrop.x+a,y:e.dragStartCrop.y+c},s=e.props.restrictPosition?be(u,e.mediaSize,e.state.cropSize,e.props.zoom,e.props.rotation):u;e.props.onCropChange(s)}}))},e.onDragStopped=function(){var t,r;e.isTouching=!1,e.cleanEvents(),e.emitCropData(),(r=(t=e.props).onInteractionEnd)===null||r===void 0||r.call(t)},e.onWheel=function(t){if(!!e.currentWindow&&!(e.props.onWheelRequest&&!e.props.onWheelRequest(t))){t.preventDefault();var r=o.getMousePoint(t),i=Qe()(t).pixelY,a=e.props.zoom-i*e.props.zoomSpeed/200;e.setNewZoom(a,r,{shouldUpdatePosition:!0}),e.state.hasWheelJustStarted||e.setState({hasWheelJustStarted:!0},function(){var c,u;return(u=(c=e.props).onInteractionStart)===null||u===void 0?void 0:u.call(c)}),e.wheelTimer&&clearTimeout(e.wheelTimer),e.wheelTimer=e.currentWindow.setTimeout(function(){return e.setState({hasWheelJustStarted:!1},function(){var c,u;return(u=(c=e.props).onInteractionEnd)===null||u===void 0?void 0:u.call(c)})},250)}},e.getPointOnContainer=function(t,r){var i=t.x,a=t.y;if(!e.containerRect)throw new Error("The Cropper is not mounted");return{x:e.containerRect.width/2-(i-r.x),y:e.containerRect.height/2-(a-r.y)}},e.getPointOnMedia=function(t){var r=t.x,i=t.y,a=e.props,c=a.crop,u=a.zoom;return{x:(r+c.x)/u,y:(i+c.y)/u}},e.setNewZoom=function(t,r,i){var a=i===void 0?{}:i,c=a.shouldUpdatePosition,u=c===void 0?!0:c;if(!(!e.state.cropSize||!e.props.onZoomChange)){var s=Se(t,e.props.minZoom,e.props.maxZoom);if(u){var l=e.getPointOnContainer(r,e.containerPosition),m=e.getPointOnMedia(l),d={x:m.x*s-l.x,y:m.y*s-l.y},S=e.props.restrictPosition?be(d,e.mediaSize,e.state.cropSize,s,e.props.rotation):d;e.props.onCropChange(S)}e.props.onZoomChange(s)}},e.getCropData=function(){if(!e.state.cropSize)return null;var t=e.props.restrictPosition?be(e.props.crop,e.mediaSize,e.state.cropSize,e.props.zoom,e.props.rotation):e.props.crop;return nt(t,e.mediaSize,e.state.cropSize,e.getAspect(),e.props.zoom,e.props.rotation,e.props.restrictPosition)},e.emitCropData=function(){var t=e.getCropData();if(!!t){var r=t.croppedAreaPercentages,i=t.croppedAreaPixels;e.props.onCropComplete&&e.props.onCropComplete(r,i),e.props.onCropAreaChange&&e.props.onCropAreaChange(r,i)}},e.emitCropAreaChange=function(){var t=e.getCropData();if(!!t){var r=t.croppedAreaPercentages,i=t.croppedAreaPixels;e.props.onCropAreaChange&&e.props.onCropAreaChange(r,i)}},e.recomputeCropPosition=function(){if(!!e.state.cropSize){var t=e.props.restrictPosition?be(e.props.crop,e.mediaSize,e.state.cropSize,e.props.zoom,e.props.rotation):e.props.crop;e.props.onCropChange(t),e.emitCropData()}},e}return o.prototype.componentDidMount=function(){!this.currentDoc||!this.currentWindow||(this.containerRef&&(this.containerRef.ownerDocument&&(this.currentDoc=this.containerRef.ownerDocument),this.currentDoc.defaultView&&(this.currentWindow=this.currentDoc.defaultView),this.initResizeObserver(),typeof window.ResizeObserver=="undefined"&&this.currentWindow.addEventListener("resize",this.computeSizes),this.props.zoomWithScroll&&this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}),this.containerRef.addEventListener("gesturestart",this.onGestureStart)),this.currentDoc.addEventListener("scroll",this.onScroll),this.props.disableAutomaticStylesInjection||(this.styleRef=this.currentDoc.createElement("style"),this.styleRef.setAttribute("type","text/css"),this.props.nonce&&this.styleRef.setAttribute("nonce",this.props.nonce),this.styleRef.innerHTML=ut,this.currentDoc.head.appendChild(this.styleRef)),this.imageRef.current&&this.imageRef.current.complete&&this.onMediaLoad(),this.props.setImageRef&&this.props.setImageRef(this.imageRef),this.props.setVideoRef&&this.props.setVideoRef(this.videoRef))},o.prototype.componentWillUnmount=function(){var e,t;!this.currentDoc||!this.currentWindow||(typeof window.ResizeObserver=="undefined"&&this.currentWindow.removeEventListener("resize",this.computeSizes),(e=this.resizeObserver)===null||e===void 0||e.disconnect(),this.containerRef&&this.containerRef.removeEventListener("gesturestart",this.preventZoomSafari),this.styleRef&&((t=this.styleRef.parentNode)===null||t===void 0||t.removeChild(this.styleRef)),this.cleanEvents(),this.props.zoomWithScroll&&this.clearScrollEvent())},o.prototype.componentDidUpdate=function(e){var t,r,i,a,c,u,s,l,m;e.rotation!==this.props.rotation?(this.computeSizes(),this.recomputeCropPosition()):e.aspect!==this.props.aspect?this.computeSizes():e.objectFit!==this.props.objectFit?this.computeSizes():e.zoom!==this.props.zoom?this.recomputeCropPosition():((t=e.cropSize)===null||t===void 0?void 0:t.height)!==((r=this.props.cropSize)===null||r===void 0?void 0:r.height)||((i=e.cropSize)===null||i===void 0?void 0:i.width)!==((a=this.props.cropSize)===null||a===void 0?void 0:a.width)?this.computeSizes():(((c=e.crop)===null||c===void 0?void 0:c.x)!==((u=this.props.crop)===null||u===void 0?void 0:u.x)||((s=e.crop)===null||s===void 0?void 0:s.y)!==((l=this.props.crop)===null||l===void 0?void 0:l.y))&&this.emitCropAreaChange(),e.zoomWithScroll!==this.props.zoomWithScroll&&this.containerRef&&(this.props.zoomWithScroll?this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}):this.clearScrollEvent()),e.video!==this.props.video&&((m=this.videoRef.current)===null||m===void 0||m.load());var d=this.getObjectFit();d!==this.state.mediaObjectFit&&this.setState({mediaObjectFit:d},this.computeSizes)},o.prototype.getAspect=function(){var e=this.props,t=e.cropSize,r=e.aspect;return t?t.width/t.height:r},o.prototype.getObjectFit=function(){var e,t,r,i;if(this.props.objectFit==="cover"){var a=this.imageRef.current||this.videoRef.current;if(a&&this.containerRef){this.containerRect=this.containerRef.getBoundingClientRect();var c=this.containerRect.width/this.containerRect.height,u=((e=this.imageRef.current)===null||e===void 0?void 0:e.naturalWidth)||((t=this.videoRef.current)===null||t===void 0?void 0:t.videoWidth)||0,s=((r=this.imageRef.current)===null||r===void 0?void 0:r.naturalHeight)||((i=this.videoRef.current)===null||i===void 0?void 0:i.videoHeight)||0,l=u/s;return l<c?"horizontal-cover":"vertical-cover"}return"horizontal-cover"}return this.props.objectFit},o.prototype.onPinchStart=function(e){var t=o.getTouchPoint(e.touches[0]),r=o.getTouchPoint(e.touches[1]);this.lastPinchDistance=je(t,r),this.lastPinchRotation=We(t,r),this.onDragStart(Fe(t,r))},o.prototype.onPinchMove=function(e){var t=this;if(!(!this.currentDoc||!this.currentWindow)){var r=o.getTouchPoint(e.touches[0]),i=o.getTouchPoint(e.touches[1]),a=Fe(r,i);this.onDrag(a),this.rafPinchTimeout&&this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout),this.rafPinchTimeout=this.currentWindow.requestAnimationFrame(function(){var c=je(r,i),u=t.props.zoom*(c/t.lastPinchDistance);t.setNewZoom(u,a,{shouldUpdatePosition:!1}),t.lastPinchDistance=c;var s=We(r,i),l=t.props.rotation+(s-t.lastPinchRotation);t.props.onRotationChange&&t.props.onRotationChange(l),t.lastPinchRotation=s})}},o.prototype.render=function(){var e=this,t=this.props,r=t.image,i=t.video,a=t.mediaProps,c=t.transform,u=t.crop,s=u.x,l=u.y,m=t.rotation,d=t.zoom,S=t.cropShape,C=t.showGrid,g=t.style,O=g.containerStyle,j=g.cropAreaStyle,H=g.mediaStyle,z=t.classes,Q=z.containerClassName,me=z.cropAreaClassName,ie=z.mediaClassName,Y=this.state.mediaObjectFit;return h.createElement("div",{onMouseDown:this.onMouseDown,onTouchStart:this.onTouchStart,ref:function(he){return e.containerRef=he},"data-testid":"container",style:O,className:Re("reactEasyCrop_Container",Q)},r?h.createElement("img",y({alt:"",className:Re("reactEasyCrop_Image",Y==="contain"&&"reactEasyCrop_Contain",Y==="horizontal-cover"&&"reactEasyCrop_Cover_Horizontal",Y==="vertical-cover"&&"reactEasyCrop_Cover_Vertical",ie)},a,{src:r,ref:this.imageRef,style:y(y({},H),{transform:c||"translate(".concat(s,"px, ").concat(l,"px) rotate(").concat(m,"deg) scale(").concat(d,")")}),onLoad:this.onMediaLoad})):i&&h.createElement("video",y({autoPlay:!0,loop:!0,muted:!0,className:Re("reactEasyCrop_Video",Y==="contain"&&"reactEasyCrop_Contain",Y==="horizontal-cover"&&"reactEasyCrop_Cover_Horizontal",Y==="vertical-cover"&&"reactEasyCrop_Cover_Vertical",ie)},a,{ref:this.videoRef,onLoadedMetadata:this.onMediaLoad,style:y(y({},H),{transform:c||"translate(".concat(s,"px, ").concat(l,"px) rotate(").concat(m,"deg) scale(").concat(d,")")}),controls:!1}),(Array.isArray(i)?i:[{src:i}]).map(function(ae){return h.createElement("source",y({key:ae.src},ae))})),this.state.cropSize&&h.createElement("div",{style:y(y({},j),{width:this.state.cropSize.width,height:this.state.cropSize.height}),"data-testid":"cropper",className:Re("reactEasyCrop_CropArea",S==="round"&&"reactEasyCrop_CropAreaRound",C&&"reactEasyCrop_CropAreaGrid",me)}))},o.defaultProps={zoom:1,rotation:0,aspect:4/3,maxZoom:dt,minZoom:lt,cropShape:"rect",objectFit:"contain",showGrid:!0,style:{},classes:{},mediaProps:{},zoomSpeed:1,restrictPosition:!0,zoomWithScroll:!0},o.getMousePoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},o.getTouchPoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},o}(h.Component);const $="img-crop",De=1,pe=.1,_e=0,Ze=-180,Le=180,ze=1,Ge=.5,He=2,fe=.01,pt=(0,h.forwardRef)((n,o)=>{const{cropperRef:e,zoomSlider:t,rotationSlider:r,aspectSlider:i,showReset:a,resetBtnText:c,modalImage:u,aspect:s,minZoom:l,maxZoom:m,cropShape:d,showGrid:S,cropperProps:C}=n,[g,O]=(0,h.useState)(De),[j,H]=(0,h.useState)(_e),[z,Q]=(0,h.useState)(s),me=g!==De||j!==_e||z!==s,ie=()=>{O(De),H(_e),Q(s)},[Y,ae]=(0,h.useState)({x:0,y:0}),he=(0,h.useRef)({width:0,height:0,x:0,y:0}),ve=(0,h.useCallback)((se,Pe)=>{he.current=Pe},[]);(0,h.useImperativeHandle)(o,()=>({rotation:j,cropPixelsRef:he,onReset:ie}));const ge="[display:flex] [align-items:center] [width:60%] [margin-inline:auto]",ee="[display:flex] [align-items:center] [justify-content:center] [height:32px] [width:32px] [background:transparent] [border:0] [font-family:inherit] [font-size:18px] [cursor:pointer] disabled:[opacity:20%] disabled:[cursor:default]",B="[flex:1]";return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(ht,Object.assign({},C,{ref:e,image:u,crop:Y,zoom:g,rotation:j,aspect:z,minZoom:l,maxZoom:m,zoomWithScroll:t,cropShape:d,showGrid:S,onCropChange:ae,onZoomChange:O,onRotationChange:H,onCropComplete:ve,classes:{containerClassName:`${$}-container ![position:relative] [width:100%] [height:40vh] [&~section:first-of-type]:[margin-top:16px] [&~section:last-of-type]:[margin-bottom:16px]`,mediaClassName:`${$}-media`}})),t&&(0,f.jsxs)("section",{className:`${$}-control ${$}-control-zoom ${ge}`,children:[(0,f.jsx)("button",{className:ee,onClick:()=>O(g-pe),disabled:g-pe<l,children:"\uFF0D"}),(0,f.jsx)(re.Z,{className:B,min:l,max:m,step:pe,value:g,onChange:O}),(0,f.jsx)("button",{className:ee,onClick:()=>O(g+pe),disabled:g+pe>m,children:"\uFF0B"})]}),r&&(0,f.jsxs)("section",{className:`${$}-control ${$}-control-rotation ${ge}`,children:[(0,f.jsx)("button",{className:`${ee} [font-size:16px]`,onClick:()=>H(j-ze),disabled:j===Ze,children:"\u21BA"}),(0,f.jsx)(re.Z,{className:B,min:Ze,max:Le,step:ze,value:j,onChange:H}),(0,f.jsx)("button",{className:`${ee} [font-size:16px]`,onClick:()=>H(j+ze),disabled:j===Le,children:"\u21BB"})]}),i&&(0,f.jsxs)("section",{className:`${$}-control ${$}-control-aspect ${ge}`,children:[(0,f.jsx)("button",{className:ee,onClick:()=>Q(z-fe),disabled:z-fe<Ge,children:"\u2195\uFE0F"}),(0,f.jsx)(re.Z,{className:B,min:Ge,max:He,step:fe,value:z,onChange:Q}),(0,f.jsx)("button",{className:ee,onClick:()=>Q(z+fe),disabled:z+fe>He,children:"\u2194\uFE0F"})]}),a&&(t||r||i)&&(0,f.jsx)(E.Z,{className:"[bottom:20px] [position:absolute]",style:me?{}:{opacity:.3,pointerEvents:"none"},onClick:ie,children:c})]})});var ft=(0,h.memo)(pt);function mt(n,o){o===void 0&&(o={});var e=o.insertAt;if(!(!n||typeof document=="undefined")){var t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",e==="top"&&t.firstChild?t.insertBefore(r,t.firstChild):t.appendChild(r),r.styleSheet?r.styleSheet.cssText=n:r.appendChild(document.createTextNode(n))}}var vt=".visible{visibility:visible}.grid{display:grid}.\\!\\[position\\:relative\\]{position:relative!important}.\\[align-items\\:center\\]{align-items:center}.\\[background\\:transparent\\]{background:transparent}.\\[border\\:0\\]{border:0}.\\[bottom\\:20px\\]{bottom:20px}.\\[cursor\\:pointer\\]{cursor:pointer}.\\[display\\:flex\\]{display:flex}.\\[flex\\:1\\]{flex:1}.\\[font-family\\:inherit\\]{font-family:inherit}.\\[font-size\\:16px\\]{font-size:16px}.\\[font-size\\:18px\\]{font-size:18px}.\\[height\\:32px\\]{height:32px}.\\[height\\:40vh\\]{height:40vh}.\\[justify-content\\:center\\]{justify-content:center}.\\[margin-inline\\:auto\\]{margin-inline:auto}.\\[position\\:absolute\\]{position:absolute}.\\[width\\:100\\%\\]{width:100%}.\\[width\\:32px\\]{width:32px}.\\[width\\:60\\%\\]{width:60%}.disabled\\:\\[cursor\\:default\\]:disabled{cursor:default}.disabled\\:\\[opacity\\:20\\%\\]:disabled{opacity:20%}.\\[\\&\\~section\\:first-of-type\\]\\:\\[margin-top\\:16px\\]~section:first-of-type{margin-top:16px}.\\[\\&\\~section\\:last-of-type\\]\\:\\[margin-bottom\\:16px\\]~section:last-of-type{margin-bottom:16px}";mt(vt,{insertAt:"top"});const gt=xe(k.Z,"4.23.0")===-1?"visible":"open",de=(n,o,e)=>o in n?(console.error(`\`${o}\` is deprecated, please use \`${e}\` instead`),n[o]):n[e],yt=(0,h.forwardRef)((n,o)=>{const{quality:e=.4,fillColor:t="white",zoomSlider:r=!0,rotationSlider:i=!1,aspectSlider:a=!1,showReset:c=!1,resetText:u,aspect:s=1,minZoom:l=1,maxZoom:m=3,cropShape:d="rect",showGrid:S=!1,cropperProps:C,modalClassName:g,modalTitle:O,modalWidth:j,modalOk:H,modalCancel:z,onModalOk:Q,onModalCancel:me,modalProps:ie,beforeCrop:Y,children:ae}=n,he=de(n,"zoom","zoomSlider")||!0,ve=de(n,"rotate","rotationSlider")||!1,ge=de(n,"shape","cropShape")||"rect",ee=de(n,"grid","showGrid")||!1;"onUploadFail"in n&&console.error("`onUploadFail` is removed, because the only way it is called, is when the file is rejected by beforeUpload"),de(n,"modalMaskTransitionName","modalProps.maskTransitionName"),de(n,"modalTransitionName","modalProps.transitionName");const B=(0,h.useRef)({});B.current.onModalOk=Q,B.current.onModalCancel=me,B.current.beforeCrop=Y;const se=(0,h.useRef)(null),Pe=(0,h.useCallback)(x=>{var M;const R=document.createElement("canvas"),w=R.getContext("2d"),T=(((M=x==null?void 0:x.getRootNode)===null||M===void 0?void 0:M.call(x))||document).querySelector(`.${$}-media`),{width:V,height:N,x:te,y:ne}=se.current.cropPixelsRef.current;if(ve&&se.current.rotation!==_e){const{naturalWidth:K,naturalHeight:ce}=T,ye=se.current.rotation*(Math.PI/180),we=Math.abs(Math.sin(ye)),oe=Math.abs(Math.cos(ye)),U=K*oe+ce*we,Z=ce*oe+K*we;R.width=U,R.height=Z,w.fillStyle=t,w.fillRect(0,0,U,Z);const Ye=U/2,Ve=Z/2;w.translate(Ye,Ve),w.rotate(ye),w.translate(-Ye,-Ve);const Rt=(U-K)/2,_t=(Z-ce)/2;w.drawImage(T,0,0,K,ce,Rt,_t,K,ce);const Pt=w.getImageData(0,0,U,Z);R.width=V,R.height=N,w.putImageData(Pt,-te,-ne)}else R.width=V,R.height=N,w.fillStyle=t,w.fillRect(0,0,V,N),w.drawImage(T,te,ne,V,N,0,0,V,N);return R},[t,ve]),[Be,Te]=(0,h.useState)(""),ke=(0,h.useRef)(),Ue=(0,h.useRef)(),Ee=(0,h.useCallback)(({beforeUpload:x,file:M,resolve:R,reject:w})=>(0,A.mG)(void 0,void 0,void 0,function*(){const q=M;if(typeof x!="function"){R(q);return}try{const T=yield x(M,[M]);R(T===!1?!1:T!==!0&&T||q)}catch(T){w(T)}}),[]),Xe=(0,h.useCallback)(x=>(M,R)=>new Promise((w,q)=>(0,A.mG)(void 0,void 0,void 0,function*(){let T=M;if(typeof B.current.beforeCrop=="function")try{const N=yield B.current.beforeCrop(M,R);if(N===!1)return Ee({beforeUpload:x,file:M,resolve:w,reject:q});N!==!0&&(T=N||M)}catch(N){return Ee({beforeUpload:x,file:M,resolve:w,reject:q})}const V=new FileReader;V.addEventListener("load",()=>{typeof V.result=="string"&&Te(V.result)}),V.readAsDataURL(T),ke.current=()=>{var N,te;Te(""),se.current.onReset();let ne=!1;(te=(N=B.current).onModalCancel)===null||te===void 0||te.call(N,K=>{w(K),ne=!0}),ne||w(J.Z.LIST_IGNORE)},Ue.current=N=>(0,A.mG)(void 0,void 0,void 0,function*(){Te(""),se.current.onReset();const te=Pe(N.target),{type:ne,name:K,uid:ce}=T;te.toBlob(ye=>(0,A.mG)(void 0,void 0,void 0,function*(){const we=new File([ye],K,{type:ne});Object.assign(we,{uid:ce}),Ee({beforeUpload:x,file:we,resolve:oe=>{var U,Z;w(oe),(Z=(U=B.current).onModalOk)===null||Z===void 0||Z.call(U,oe)},reject:oe=>{var U,Z;q(oe),(Z=(U=B.current).onModalOk)===null||Z===void 0||Z.call(U,oe)}})}),ne,e)})})),[Pe,e,Ee]),wt=(0,h.useCallback)(x=>{const M=Array.isArray(x)?x[0]:x,R=M.props,{beforeUpload:w,accept:q}=R,T=(0,A._T)(R,["beforeUpload","accept"]);return Object.assign(Object.assign({},M),{props:Object.assign(Object.assign({},T),{accept:q||"image/*",beforeUpload:Xe(w)})})},[Xe]),xt=(0,h.useMemo)(()=>{const x={};return j!==void 0&&(x.width=j),H!==void 0&&(x.okText=H),z!==void 0&&(x.cancelText=z),x},[z,H,j]),Ct=`${$}-modal${g?` ${g}`:""}`,$e=(typeof window=="undefined"?"":window.navigator.language)==="zh-CN",bt=O||($e?"\u7F16\u8F91\u56FE\u7247":"Edit image"),St=u||($e?"\u91CD\u7F6E":"Reset");return(0,f.jsxs)(f.Fragment,{children:[wt(ae),Be&&(0,f.jsx)(G.Z,Object.assign({},ie,xt,{[gt]:!0,title:bt,onCancel:ke.current,onOk:Ue.current,wrapClassName:Ct,maskClosable:!1,destroyOnClose:!0,children:(0,f.jsx)(ft,{ref:se,cropperRef:o,zoomSlider:he,rotationSlider:ve,aspectSlider:a,showReset:c,resetBtnText:St,modalImage:Be,aspect:s,minZoom:l,maxZoom:m,cropShape:ge,showGrid:ee,cropperProps:C})}))]})})},52796:function(X,L,v){X.exports=v(10643)},13264:function(X){"use strict";var L=!!(typeof window!="undefined"&&window.document&&window.document.createElement),v={canUseDOM:L,canUseWorkers:typeof Worker!="undefined",canUseEventListeners:L&&!!(window.addEventListener||window.attachEvent),canUseViewport:L&&!!window.screen,isInWorker:!L};X.exports=v},84518:function(X){var L=!1,v,A,f,k,G,J,W,p,_,P,F,I,ue,xe,h;function E(){if(!L){L=!0;var D=navigator.userAgent,b=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(D),y=/(Mac OS X)|(Windows)|(Linux)/.exec(D);if(I=/\b(iPhone|iP[ao]d)/.exec(D),ue=/\b(iP[ao]d)/.exec(D),P=/Android/i.exec(D),xe=/FBAN\/\w+;/i.exec(D),h=/Mobile/i.exec(D),F=!!/Win64/.exec(D),b){v=b[1]?parseFloat(b[1]):b[5]?parseFloat(b[5]):NaN,v&&document&&document.documentMode&&(v=document.documentMode);var Oe=/(?:Trident\/(\d+.\d+))/.exec(D);J=Oe?parseFloat(Oe[1])+4:v,A=b[2]?parseFloat(b[2]):NaN,f=b[3]?parseFloat(b[3]):NaN,k=b[4]?parseFloat(b[4]):NaN,k?(b=/(?:Chrome\/(\d+\.\d+))/.exec(D),G=b&&b[1]?parseFloat(b[1]):NaN):G=NaN}else v=A=f=G=k=NaN;if(y){if(y[1]){var Me=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(D);W=Me?parseFloat(Me[1].replace("_",".")):!0}else W=!1;p=!!y[2],_=!!y[3]}else W=p=_=!1}}var re={ie:function(){return E()||v},ieCompatibilityMode:function(){return E()||J>v},ie64:function(){return re.ie()&&F},firefox:function(){return E()||A},opera:function(){return E()||f},webkit:function(){return E()||k},safari:function(){return re.webkit()},chrome:function(){return E()||G},windows:function(){return E()||p},osx:function(){return E()||W},linux:function(){return E()||_},iphone:function(){return E()||I},mobile:function(){return E()||I||ue||P||h},nativeApp:function(){return E()||xe},android:function(){return E()||P},ipad:function(){return E()||ue}};X.exports=re},96534:function(X,L,v){"use strict";var A=v(13264),f;A.canUseDOM&&(f=document.implementation&&document.implementation.hasFeature&&document.implementation.hasFeature("","")!==!0);/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @param {?boolean} capture Check if the capture phase is supported.
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */function k(G,J){if(!A.canUseDOM||J&&!("addEventListener"in document))return!1;var W="on"+G,p=W in document;if(!p){var _=document.createElement("div");_.setAttribute(W,"return;"),p=typeof _[W]=="function"}return!p&&f&&G==="wheel"&&(p=document.implementation.hasFeature("Events.wheel","3.0")),p}X.exports=k},10643:function(X,L,v){"use strict";var A=v(84518),f=v(96534),k=10,G=40,J=800;function W(p){var _=0,P=0,F=0,I=0;return"detail"in p&&(P=p.detail),"wheelDelta"in p&&(P=-p.wheelDelta/120),"wheelDeltaY"in p&&(P=-p.wheelDeltaY/120),"wheelDeltaX"in p&&(_=-p.wheelDeltaX/120),"axis"in p&&p.axis===p.HORIZONTAL_AXIS&&(_=P,P=0),F=_*k,I=P*k,"deltaY"in p&&(I=p.deltaY),"deltaX"in p&&(F=p.deltaX),(F||I)&&p.deltaMode&&(p.deltaMode==1?(F*=G,I*=G):(F*=J,I*=J)),F&&!_&&(_=F<1?-1:1),I&&!P&&(P=I<1?-1:1),{spinX:_,spinY:P,pixelX:F,pixelY:I}}W.getEventType=function(){return A.firefox()?"DOMMouseScroll":f("wheel")?"wheel":"mousewheel"},X.exports=W}}]);
