(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[5349],{66640:function(L,E,e){"use strict";e.r(E),e.d(E,{Report:function(){return z},default:function(){return pt}});var u=e(67294),y=e(80582),M=e(23156),P=e(11628),C=(0,u.createContext)({}),b=C.Provider,St=C.Consumer;function Q(n){var i=["Radio","Checkbox","Cascader","Select","QuestionSet","Nps","Score"];if(i.includes(n.type))return!0;if(n.type==="FillBlank"||n.type==="MultipleBlank"){var a;if((a=n.children)!==null&&a!==void 0&&a.find(function(r){var l;return((l=r.attribute)===null||l===void 0?void 0:l.dataType)==="number"}))return!0}return!1}var U={Radio:"\u5355\u9009\u9898",Checkbox:"\u591A\u9009\u9898",Cascader:"\u7EA7\u8054\u9898",Select:"\u4E0B\u62C9\u9898",FillBlank:"\u5355\u884C\u6587\u672C\u9898",Textarea:"\u591A\u884C\u6587\u672C\u9898",MultipleBlank:"\u591A\u9879\u586B\u7A7A\u9898",Nps:"Nps\u9898",Score:"\u6253\u5206\u9898",Judge:"\u5224\u65AD\u9898"},W=e(69610),k=e(54941),H=function(){function n(){(0,W.Z)(this,n),this._init=this._init.bind(this),this._cleanUp=this._cleanUp.bind(this),this.convertFromInput=this.convertFromInput.bind(this)}return(0,k.Z)(n,[{key:"_init",value:function(){this.canvas=document.createElement("canvas"),this.imgPreview=document.createElement("img"),this.imgPreview.style="position: absolute; top: -9999px",document.body.appendChild(this.imgPreview),this.canvasCtx=this.canvas.getContext("2d")}},{key:"_cleanUp",value:function(){document.body.removeChild(this.imgPreview)}},{key:"convertFromInput",value:function(a,r){this._init();var l=this;this.imgPreview.onload=function(){var x=new Image;l.canvas.width=l.imgPreview.clientWidth,l.canvas.height=l.imgPreview.clientHeight,x.crossOrigin="anonymous",x.src=l.imgPreview.src,x.onload=function(){l.canvasCtx.drawImage(x,0,0);var o=l.canvas.toDataURL("image/png");typeof r=="function"&&r(o),l._cleanUp()}},this.imgPreview.src=a}}]),n}(),t=e(85893),O=function(i){var a;return i?(a=i.children)===null||a===void 0?void 0:a.filter(function(r){return Q(r)}).map(function(r){return r.type==="QuestionSet"?(0,t.jsx)(V,{schema:r},r.id):(0,t.jsx)(lt,{schema:r},r.id)}):(0,t.jsx)(t.Fragment,{})},G=(0,y.Pi)(function(){var n=(0,P.IE)(),i=n.schema;return(0,t.jsx)("div",{children:O(i)})}),V=function(i){return(0,t.jsx)("div",{children:O(i.schema)})},Ct=e(13062),T=e(71230),Ft=e(66456),J=e(94132),It=e(89032),F=e(15746),Rt=e(57663),I=e(71577),K=e(94657),X=e(3375),$=e(83279),Y=e(68023),q=e(81615),_=e(71257),tt=e(82242),et=e(8690),rt=e(17813),nt=e(73761),at=e(93450),it=e(50434);Y.D([et.N,rt.N,nt.N,_.N,tt.N,it.N,at.N]);var R=[{color:"#5398ff"},{color:"#39d2d9"},{color:"#f16b79"},{color:"#b18160"},{color:"#94ed8c"},{color:"#ffb878"},{color:"#99a4ff"},{color:"#808bea"},{color:"#c36cff"}],ot=(0,u.forwardRef)(function(n,i){var a=n.data,r=n.labels,l=n.loading,x=n.name,o=n.type,B=(0,u.useRef)(null),c=(0,u.useRef)();return(0,u.useEffect)(function(){if(B.current){var j=q.S1(B.current);return c.current=j,function(){j.dispose()}}},[]),(0,u.useImperativeHandle)(i,function(){return{saveAsImage:function(){var s;return(s=c.current)===null||s===void 0?void 0:s.getDataURL({type:"png"})}}}),(0,u.useEffect)(function(){if(!l&&c.current){c.current.hideLoading(),c.current.clear();var j=300;o==="horizontalBar"?(c.current.setOption({title:{text:x},grid:{top:15,bottom:15,left:10*Math.max.apply(Math,(0,$.Z)(r.map(function(s){return s.length})))+25,right:50},tooltip:{show:!0,trigger:"item"},xAxis:{show:!1},yAxis:{type:"category",data:r,inverse:!0},series:[{type:"bar",barWidth:12,data:a==null?void 0:a.map(function(s,d){var f=s.value,S=s.percent;return{value:f,percent:S,itemStyle:R[d%R.length]}}),showBackground:!0,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)"},label:{show:!0,position:"right",color:"black",formatter:function(d){var f=d.data;return f.percent||"0%"}}}]}),j=r.length*40-r.length):o==="pie"?c.current.setOption({title:{text:x},tooltip:{show:!0,trigger:"item"},series:[{name:x,type:"pie",radius:"60%",data:a==null?void 0:a.map(function(s,d){var f=s.value,S=s.percent;return{value:f,name:"".concat(r[d]," ").concat(S),itemStyle:R[d%R.length]}}),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},labelLine:{length:10,length2:25}}]}):o==="bar"&&c.current.setOption({title:{text:x},tooltip:{show:!0,trigger:"item"},xAxis:{type:"category",data:r,axisLabel:{interval:0,rotate:30}},yAxis:{show:!1},series:[{type:"bar",barWidth:15,data:a==null?void 0:a.map(function(s,d){var f=s.value,S=s.percent;return{value:f,percent:S,itemStyle:R[d%R.length]}}),showBackground:!0,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)"},label:{show:!0,position:"top",color:"black",formatter:function(d){var f=d.data;return f.percent||"0%"}}}]}),c.current.resize({height:j})}else c.current&&c.current.showLoading()},[l,o,a]),(0,t.jsx)("div",{ref:B})}),D=e(76826),lt=(0,y.Pi)(function(n){var i,a,r=n.schema,l=(0,u.useContext)(C),x=l.loading,o=l.reportData,B=(0,u.useState)("horizontalBar"),c=(0,K.Z)(B,2),j=c[0],s=c[1],d=(o==null||(i=o.statistics[r.id])===null||i===void 0?void 0:i.total)||0,f=(0,u.useRef)(null),S=(0,u.useMemo)(function(){if(r.type==="FillBlank"||r.type==="MultipleBlank"||r.type==="Score"||r.type==="Nps"){var Z=[{title:"\u5408\u8BA1",dataIndex:"sum",width:"20%"},{title:"\u5E73\u5747\u503C",dataIndex:"average",width:"20%"},{title:"\u6700\u9AD8\u503C",dataIndex:"max",width:"20%"},{title:"\u6700\u4F4E\u503C",dataIndex:"min",width:"20%"}];return r.type==="MultipleBlank"?[{title:"\u9009\u9879",dataIndex:"title",width:"20%"}].concat(Z):Z}return[{title:"\u9009\u9879",dataIndex:"title",width:"50%"},{title:"\u6570\u636E\u91CF",dataIndex:"total",width:"25%",sorter:function(m,h){return m.total-h.total},showSorterTooltip:!1},{title:"\u5360\u6BD4",dataIndex:"percent",width:"25%",sorter:function(m,h){return parseFloat(m.percent)-parseFloat(h.percent)},showSorterTooltip:!1}]},[r.type]),xt=function(){var v,m=(v=f.current)===null||v===void 0?void 0:v.saveAsImage();m&&new H().convertFromInput(m,function(h){var p=document.createElement("a");p.href=h,p.download="".concat((0,D.WO)(r.title),".png"),p.click(),p.remove()})},gt=function(){var v;return(v=r.children)===null||v===void 0?void 0:v.map(function(m){var h=m.id,p=m.title,N=(0,D.WO)(p),g=o==null?void 0:o.statistics[h];if(g){var w=g.total||0,A=Math.round(1e4*w/(d||1))/100+"%";if(!(o!=null&&o.search)||N.includes(o.search))return{id:h,title:N,total:w,percent:A,min:g.min,max:g.max,average:g.average,sum:g.sum}}return{id:h,title:N,total:0,percent:"0%"}})},yt=function(){var v;if(r.type==="FillBlank"||r.type==="MatrixFillBlank"||r.type==="Score"||r.type==="Nps")return(0,t.jsx)(t.Fragment,{});var m=[],h=[];return(v=r.children)===null||v===void 0||v.forEach(function(p){var N=(0,D.WO)(p.title);if(m.push(N),!(o!=null&&o.search)||N.includes(o.search)){var g,w=(o==null||(g=o.statistics[p.id])===null||g===void 0?void 0:g.total)||0,A=Math.round(1e4*w/(d||1))/100+"%";h.push({percent:A,value:w})}else h.push({percent:"0%",value:0})}),(0,t.jsxs)(F.Z,{span:24,className:"question-chart",children:[(0,t.jsx)("div",{className:"question-chart-btns",children:(0,t.jsxs)(I.Z.Group,{size:"small",children:[(0,t.jsx)(I.Z,{type:"link",onClick:function(){return s("horizontalBar")},children:"\u6761\u5F62\u56FE"}),(0,t.jsx)(I.Z,{type:"link",onClick:function(){return s("bar")},children:"\u67F1\u5F62\u56FE"}),(0,t.jsx)(I.Z,{type:"link",onClick:function(){return s("pie")},children:"\u6247\u5F62\u56FE"}),(0,t.jsx)(I.Z,{type:"link",icon:(0,t.jsx)("div",{className:"divider"})}),(0,t.jsx)(I.Z,{type:"link",icon:(0,t.jsx)(X.Z,{}),onClick:xt})]})}),(0,t.jsx)("div",{children:(0,t.jsx)(ot,{type:j,loading:x,labels:m,data:h,ref:f})})]})},jt=function(){return(0,t.jsx)(F.Z,{span:24,children:(0,t.jsx)(J.Z,{pagination:!1,size:"small",bordered:!0,columns:S,rowKey:"id",dataSource:gt()})})};return(0,t.jsx)("div",{className:"question-item",children:(0,t.jsxs)(T.Z,{gutter:[12,12],className:"question-content",children:[(0,t.jsx)(F.Z,{xs:24,sm:12,children:(0,t.jsxs)(T.Z,{children:[(0,t.jsx)(F.Z,{span:24,children:(0,t.jsx)("span",{className:"question-label",children:(0,D.WO)(r.title)})}),yt()]})}),(0,t.jsx)(F.Z,{xs:24,sm:12,children:(0,t.jsxs)(T.Z,{children:[(0,t.jsx)(F.Z,{span:24,children:(0,t.jsx)("div",{className:"question-title",children:(0,t.jsxs)("div",{className:"question-data-desc",children:[(0,t.jsxs)("div",{children:["\u7C7B\u578B\uFF1A",U[r.type]]}),(0,t.jsxs)("div",{children:["\u5FC5\u586B\uFF1A",(a=r.attribute)!==null&&a!==void 0&&a.required?"\u662F":"\u5426"]}),(0,t.jsxs)("div",{children:[d,"\u6761\u6570\u636E"]})]})})}),jt()]})})]})})}),Zt=e(59250),st=e(13013),Nt=e(30887),ut=e(28682),Pt=e(47673),ct=e(77808),dt=e(19957),vt=ct.Z.Search,ht=(0,y.Pi)(function(){var n=(0,u.useContext)(C),i=n.reportData,a=(0,t.jsx)(ut.Z,{items:[{label:"\u6309\u6570\u636E\u5185\u5BB9\u7B5B\u9009",key:"content"},{label:"\u6309\u63D0\u4EA4\u65E5\u671F\u7B5B\u9009",key:"date"}]});return(0,t.jsxs)("div",{className:"report-header",children:[(0,t.jsxs)("span",{children:["\u5171",(i==null?void 0:i.total)||0,"\u6761\u6570\u636E"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(vt,{size:"small",placeholder:"\u641C\u7D22",onSearch:function(l){n.loadReportData(l)},style:{width:200,marginRight:10}}),(0,t.jsx)(st.Z,{overlay:a,placement:"bottomRight",children:(0,t.jsx)(dt.Z,{style:{cursor:"pointer"}})})]})]})}),ft=function(){return(0,t.jsx)(ht,{})},mt=function(){return(0,t.jsx)("div",{className:"report-container",children:(0,t.jsx)(G,{})})},z=(0,y.Pi)(function(){var n=(0,P.IE)(),i=(0,u.useMemo)(function(){return new M.Cu(n)},[n]);return(0,u.useEffect)(function(){i.loadReportData()},[]),(0,t.jsx)("div",{className:"survey-report",children:(0,t.jsxs)(b,{value:i,children:[(0,t.jsx)(ft,{}),(0,t.jsx)(mt,{})]})})}),pt=z},11628:function(L,E,e){"use strict";e.d(E,{xI:function(){return y},Ge:function(){return C},IE:function(){return P}});var u=e(67294),y=(0,u.createContext)({}),M=y.Provider;function P(){var b=(0,u.useContext)(y);return b.store}function C(){var b=P();return b.flowStore}}}]);
