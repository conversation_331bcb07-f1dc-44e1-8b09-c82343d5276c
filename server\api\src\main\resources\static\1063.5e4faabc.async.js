(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[1063],{21063:function(et,rt,P){"use strict";P.d(rt,{_$:function(){return xe},tl:function(){return se},hY:function(){return pe}});var _=P(11849),t=P(94657),s=P(67294),I=P(29006),e=function(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:12;return"".concat(b,"px -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji")};function r(B){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},L=b.fontSize,y=e(L);return B==="light"?{gridBackgroundColor:"rgba(240, 240, 240, 1)",cellBorderColor:"#dcdcdc",cellBorderWidth:1,cornerCellBackgroundColor:"#fafafa",cornerCellBorderColor:"#bcbcbc",columnHeaderCellBorderColor:"#bcbcbc",columnHeaderCellCapBackgroundColor:"#fafafa",columnHeaderCellBorderWidth:1,rowHeaderCellBackgroundColor:"#fafafa",rowHeaderCellBorderColor:"#c5c5c5",rowHeaderCellBorderWidth:1,rowHeaderCellSelectedBackgroundColor:"#e6e6e6",activeColumnHeaderCellColor:"#16161",activeRowHeaderCellColor:"#16161",columnHeaderCellColor:"#161616",rowHeaderCellColor:"#161616",cellColor:"#161616",cellFont:y,activeCellFont:y,columnHeaderCellFont:y,rowHeaderCellFont:y,treeArrowMarginLeft:13,treeArrowColor:"black",treeArrowWidth:8,treeArrowHeight:8,treeArrowMarginTop:-8,activeCellOverlayBorderColor:"red",cellInsertBackgroundColor:"#AED6AE",cellUpdateBackgroundColor:"#ffc069",cellRemoveBackgroundColor:"#ff7875"}:{gridBackgroundColor:"##282C34",cellBackgroundColor:"#2B2B2B",cellSelectedBackgroundColor:"#2B2B2B",cellHoverBackgroundColor:"#2B2B2B",editCellBackgroundColor:"#2B2B2B",activeCellBackgroundColor:"#2B2B2B",activeCellHoverBackgroundColor:"#2B2B2B",activeCellSelectedBackgroundColor:"#2B2B2B",cornerCellBackgroundColor:"#383C44",activeHeaderCellBackgroundColor:"#2B2B2B",columnHeaderCellColor:"#BABABA",columnHeaderCellHoverColor:"#BABABA",columnHeaderCellCapBackgroundColor:"#2B2B2B",columnHeaderCellHoverBackgroundColor:"#2B2B2B",columnHeaderCellBackgroundColor:"#383C44",columnHeaderCellBorderColor:"#3B4048",columnHeaderCellSelectedBackgroundColor:"#e6e6e6",activeColumnHeaderCellBackgroundColor:"#2C313C",activeColumnHeaderCellColor:"#BABABA",columnHeaderCellCapBorderColor:"#3B4048",rowHeaderCellColor:"#BABABA",rowHeaderCellHoverColor:"#BABABA",rowHeaderCellSelectedColor:"#BABABA",rowHeaderCellCapBackgroundColor:"#2B2B2B",rowHeaderCellHoverBackgroundColor:"#2B2B2B",rowHeaderCellBackgroundColor:"#383C44",rowHeaderCellBorderColor:"#3B4048",rowHeaderCellSelectedBackgroundColor:"#2B2B2B",activeRowHeaderCellBackgroundColor:"#2C313C",activeRowHeaderCellColor:"#BABABA",scrollBarBackgroundColor:"#2B2B2B",scrollBarBoxColor:"#414448",scrollBarCornerBackgroundColor:"#383C44",cellBorderColor:"#3B4048",gridBorderColor:"#3B4048",cornerCellBorderColor:"#3B4048",cellColor:"#BABABA",cellHoverColor:"#BABABA",cellSelectedColor:"#BABABA",activeCellColor:"#BABABA",activeCellHoverColor:"#BABABA",activeCellSelectedColor:"#BABABA",treeArrowColor:"black",rowHeaderCellBorderWidth:1,columnHeaderCellBorderWidth:1,cellBorderWidth:1,cellFont:y,activeCellFont:y,columnHeaderCellFont:y,rowHeaderCellFont:y,treeArrowMarginLeft:13,treeArrowWidth:8,treeArrowHeight:8,treeArrowMarginTop:-8,cellInsertBackgroundColor:"#2D4134",cellUpdateBackgroundColor:"#2C4957",cellRemoveBackgroundColor:"#404859"}}var c=s.createContext({}),o=s.createContext({}),n=P(85893);function i(B){var b=B.top,L=B.left,y=B.children,D=B.style,k=B.containerStyle,u=B.onClick,m=(0,s.useRef)(null);return(0,s.useLayoutEffect)(function(){if(m.current){var M=m.current.getBoundingClientRect(),z=M.bottom,F=M.right,j=b,Y=L;z>window.innerHeight&&(j=b-(z-window.innerHeight)),F>window.innerWidth&&(Y=L-(F-window.innerWidth)-20),m.current.style.top="".concat(j,"px"),m.current.style.left="".concat(Y,"px")}},[]),(0,n.jsx)("div",{className:"grid-context-container",ref:m,onClick:function(z){u&&u(z)},style:(0,_.Z)({top:b,left:L},D),children:(0,n.jsx)("div",{className:"grid-context-menu",children:(0,n.jsx)("ul",{className:"actions-container flexcroll",style:k,children:y})})})}function a(B){var b=B.title,L=B.name,y=B.hidden,D=(0,s.useState)(y),k=(0,t.Z)(D,2),u=k[0],m=k[1],M=(0,s.useContext)(c),z=M.gridRef,F=(0,s.useContext)(o),j=F.cell,Y=F.allChecked,Q=F.dispatch;return(0,s.useEffect)(function(){Y===!0?m(!1):Y===!1&&j.header&&L!==j.header.name&&m(!0)},[Y]),(0,n.jsx)("li",{onClick:function(){z.current&&(L==="\u9690\u85CF\u5176\u4ED6"?z.current.schema.forEach(function(x){x&&j.header&&j.header.name!==x.name&&(x.hidden=!0),Q({type:"checkAll",payload:{checked:!1}})}):L==="\u663E\u793A\u6240\u6709"?(Q({type:"checkAll",payload:{checked:!0}}),z.current.schema.forEach(function(x){x.hidden=!1})):z.current.schema.find(function(x){return x.name===L}).hidden=!u,m(!u),z.current.self.resize(!0))},children:(0,n.jsxs)("a",{className:"action-menu-item",children:[(0,n.jsx)("span",{className:"menu-item-check codicon codicon-check",style:{visibility:!u&&L!="\u9690\u85CF\u5176\u4ED6"&&L!=="\u663E\u793A\u6240\u6709"?"visible":"hidden"}}),(0,n.jsx)("span",{className:"action-label",children:b||L})]})})}function l(){return(0,n.jsx)("li",{className:"action-item",children:(0,n.jsx)("a",{className:"action-label separator"})})}function g(){var B=(0,s.useState)(!1),b=(0,t.Z)(B,2),L=b[0],y=b[1],D=(0,s.useRef)(null),k=(0,s.useState)({top:0,left:0}),u=(0,t.Z)(k,2),m=u[0],M=u[1],z=(0,s.useContext)(c),F=z.gridRef;return(0,s.useLayoutEffect)(function(){if(D.current){var j=D.current.getBoundingClientRect(),Y=j.top,Q=j.right;M({left:Q,top:Y})}},[]),(0,n.jsxs)("li",{className:"action-item",onMouseEnter:function(){y(!0)},ref:D,children:[(0,n.jsxs)("a",{className:"action-menu-item",role:"menuitem","aria-checked":"false",children:[(0,n.jsx)("span",{className:"menu-item-check codicon codicon-checklist"}),(0,n.jsx)("span",{className:"action-label","aria-label":"Run",children:"\u663E\u793A/\u9690\u85CF\u5217"}),(0,n.jsx)("span",{className:"submenu-indicator codicon codicon-menu-submenu"})]}),L&&(0,n.jsxs)(i,{top:m.top,left:m.left,style:{zIndex:10001,minWidth:120},onClick:function(Y){return Y.stopPropagation()},children:[F.current.schema.map(function(j){return(0,n.jsx)(a,(0,_.Z)({},j),j.name)}),(0,n.jsx)(l,{}),(0,n.jsx)(a,{name:"\u9690\u85CF\u5176\u4ED6",hidden:!0}),(0,n.jsx)(a,{name:"\u663E\u793A\u6240\u6709",hidden:!0})]})]})}function A(B){var b=B.name,L=B.onClick,y=(0,s.useContext)(c),D=y.gridRef;return(0,n.jsx)("li",{onClick:function(){D.current&&L()},children:(0,n.jsxs)("a",{className:"action-menu-item",children:[(0,n.jsx)("span",{className:"menu-item-check codicon codicon-check"}),(0,n.jsx)("span",{className:"action-label",children:b})]})})}function p(){var B=(0,s.useContext)(o),b=B.cell,L=(0,s.useContext)(c),y=L.gridRef,D=(0,s.useState)(!1),k=(0,t.Z)(D,2),u=k[0],m=k[1],M=(0,s.useState)(y.current.self.columnFilters[b.header.name]||""),z=(0,t.Z)(M,2),F=z[0],j=z[1],Y=(0,s.useState)({top:0,left:0}),Q=(0,t.Z)(Y,2),tt=Q[0],x=Q[1],ot=(0,s.useRef)(null),mt=(0,s.useState)([]),st=(0,t.Z)(mt,2),Ut=st[0],Wt=st[1];if((0,s.useLayoutEffect)(function(){if(ot.current){var dt=ot.current.getBoundingClientRect(),pt=dt.left,Mt=dt.bottom;x({left:pt,top:Mt})}},[]),(0,s.useEffect)(function(){if(y.current&&(b.isColumnHeader||b.isNormal)){var dt={},pt=0;y.current.data.forEach(function(Mt){var xt=Mt[b.header.name]===null?Mt[b.header.name]:String(Mt[b.header.name]).trim(),Dt=y.current.self.blankValues.includes(xt)?y.current.self.attributes.blanksText:xt;dt[Dt]||pt>20||(pt+=1,dt[Dt]=y.current.self.formatters[b.header.type||"string"]({cell:{value:Dt}}))}),Wt(Object.keys(dt).map(function(Mt){return{title:Mt,value:dt[Mt]}}))}},[]),(0,s.useEffect)(function(){y.current&&(b.isColumnHeader||b.isNormal)&&y.current.setFilter(b.header.name,F)},[F]),!b.isNormal&&!b.isColumnHeader||b.type==="image")return(0,n.jsx)(n.Fragment,{});var Ot=b.header&&b.header.title;return(0,n.jsxs)("li",{className:"action-item",onMouseEnter:function(){m(!0)},onMouseLeave:function(){return m(!1)},children:[(0,n.jsxs)("a",{className:"action-menu-item",children:[(0,n.jsx)("span",{className:"menu-item-check codicon codicon-filter"}),(0,n.jsxs)("span",{className:"action-label",children:[(0,n.jsx)("span",{style:{whiteSpace:"nowrap"},children:"\u8FC7\u6EE4 ".concat(Ot)}),(0,n.jsx)("input",{ref:ot,style:{marginLeft:10,border:"1px solid #999",flexGrow:1,width:60},value:F,onChange:function(pt){j(pt.target.value)},onClick:function(pt){pt.stopPropagation()}})]})]}),u&&(0,n.jsx)(i,{left:tt.left,top:tt.top,containerStyle:{maxHeight:150},onClick:function(pt){pt.stopPropagation()},children:Ut.map(function(dt){return(0,n.jsx)(A,{name:dt.title,value:dt.value,onClick:function(){j(F?F+","+dt.value:dt.value)}},dt.title)})})]})}function S(){var B=(0,s.useContext)(c),b=B.gridRef,L=function(){if(b.current){var D=b.current.self;D.sizes.rows={},D.sizes.columns={},D.createRowOrders(),D.createColumnOrders(),D.storedSettings=void 0,D.setStorageData(),D.frozenRow=0,D.frozenColumn=0,D.columnFilters={},D.orderings.columns=[],b.current.schema.forEach(function(k){k.hidden=!1}),D.resize(!0)}};return(0,n.jsx)("li",{className:"action-item",onClick:L,children:(0,n.jsxs)("a",{className:"action-menu-item",role:"menuitem","aria-checked":"false",children:[(0,n.jsx)("span",{className:"menu-item-check codicon codicon-clear-all"}),(0,n.jsx)("span",{className:"action-label","aria-label":"Run",children:"\u91CD\u7F6E\u8BBE\u7F6E"})]})})}function v(){var B=(0,s.useContext)(c),b=B.gridRef,L=(0,s.useContext)(o),y=L.cell,D=b.current.singleRecordViewRowIndex,k=D===-1&&(y.isRowHeader||y.isNormal),u=D>-1,m=function(){b.current&&(b.current.singleRecordViewRowIndex=D>-1?-1:y.rowIndex)};return k||u?(0,n.jsx)("li",{className:"action-item",onClick:m,children:(0,n.jsxs)("a",{className:"action-menu-item",role:"menuitem","aria-checked":"false",children:[(0,n.jsx)("span",{className:"menu-item-check codicon"}),(0,n.jsxs)("span",{className:"action-label","aria-label":"Run",children:[k&&"\u5355\u8BB0\u5F55\u89C6\u56FE",u&&"\u591A\u8BB0\u5F55\u89C6\u56FE"]})]})}):(0,n.jsx)(n.Fragment,{})}var h=P(20640),E=P.n(h),H,R,K,N=["svgRef","title"];function $(){return $=Object.assign?Object.assign.bind():function(B){for(var b=1;b<arguments.length;b++){var L=arguments[b];for(var y in L)Object.prototype.hasOwnProperty.call(L,y)&&(B[y]=L[y])}return B},$.apply(this,arguments)}function gt(B,b){if(B==null)return{};var L=ht(B,b),y,D;if(Object.getOwnPropertySymbols){var k=Object.getOwnPropertySymbols(B);for(D=0;D<k.length;D++)y=k[D],!(b.indexOf(y)>=0)&&(!Object.prototype.propertyIsEnumerable.call(B,y)||(L[y]=B[y]))}return L}function ht(B,b){if(B==null)return{};var L={},y=Object.keys(B),D,k;for(k=0;k<y.length;k++)D=y[k],!(b.indexOf(D)>=0)&&(L[D]=B[D]);return L}var T=function(b){var L=b.svgRef,y=b.title,D=gt(b,N);return s.createElement("svg",$({t:1619366329561,className:"icon",viewBox:"0 0 1024 1024","p-id":5969,width:48,height:48,ref:L},D),y?s.createElement("title",null,y):null,H||(H=s.createElement("defs",null,s.createElement("style",{type:"text/css"}))),R||(R=s.createElement("path",{d:"M988.944032 277.100221L728.08548 6.044132A19.71317 19.71317 0 0 0 713.889518 0H324.957351a78.976663 78.976663 0 0 0-78.914672 78.883676v275.581439h-157.457396a59.108515 59.108515 0 0 0-58.891546 58.891546v295.480582a59.108515 59.108515 0 0 0 58.891546 58.891546h157.581379V945.364289a78.914671 78.914671 0 0 0 78.790689 78.635711h590.465236a78.883676 78.883676 0 0 0 79.038653-78.635711V290.769259a19.71317 19.71317 0 0 0-5.517208-13.669038z m-270.188213-223.942852l214.148258 222.517057h-214.148258zM88.585283 728.612404a19.682175 19.682175 0 0 1-19.651179-19.651179V413.480643a19.682175 19.682175 0 0 1 19.651179-19.651179h531.759664a19.682175 19.682175 0 0 1 19.651179 19.651179v295.480582a19.682175 19.682175 0 0 1-19.651179 19.651179zM955.065896 945.364289a39.457336 39.457336 0 0 1-39.519327 39.302357H324.957351a39.488331 39.488331 0 0 1-39.519327-39.364349v-177.325544h334.751945a59.108515 59.108515 0 0 0 58.891546-58.891546V413.480643a59.108515 59.108515 0 0 0-58.891546-58.891546h-334.751945V78.883676a39.550322 39.550322 0 0 1 39.519327-39.488331h354.434119V295.387596a19.682175 19.682175 0 0 0 19.682175 19.682174h255.992251V945.364289z","p-id":5970})),K||(K=s.createElement("path",{d:"M199.146412 654.254079a96.086206 96.086206 0 0 1-33.041257-5.734177 76.311045 76.311045 0 0 1-27.617036-17.295517 82.944093 82.944093 0 0 1-18.814299-29.011835 108.825378 108.825378 0 0 1-6.973999-40.604172 106.407725 106.407725 0 0 1 7.252959-40.604171 89.608136 89.608136 0 0 1 19.372219-29.848715 83.687986 83.687986 0 0 1 27.895996-18.59733 86.787541 86.787541 0 0 1 33.041256-6.416079 71.289766 71.289766 0 0 1 32.359355 7.128977 91.312892 91.312892 0 0 1 23.990556 16.861579l-25.106396 30.685595a72.622575 72.622575 0 0 0-13.79302-9.391652 34.34307 34.34307 0 0 0-16.303659-3.750461 33.010261 33.010261 0 0 0-15.218815 3.626479 38.86842 38.86842 0 0 0-12.398221 10.321518 51.111663 51.111663 0 0 0-8.647758 16.458638 70.421891 70.421891 0 0 0-3.099555 22.037836 62.48703 62.48703 0 0 0 10.72446 39.333354 34.250083 34.250083 0 0 0 28.329933 13.669037 37.907558 37.907558 0 0 0 18.814299-4.587341 55.791991 55.791991 0 0 0 14.07198-11.003421l25.106396 30.127675a76.590005 76.590005 0 0 1-26.904138 20.085117 80.588431 80.588431 0 0 1-33.041256 6.509066z m133.900778 0a109.166329 109.166329 0 0 1-35.582892-6.19911 94.660411 94.660411 0 0 1-33.041257-19.744166l27.338075-32.917275a92.025789 92.025789 0 0 0 21.045979 12.708176 53.901262 53.901262 0 0 0 21.355934 4.866302 28.577898 28.577898 0 0 0 15.776736-3.34752 10.817447 10.817447 0 0 0 4.866301-9.484638 9.050701 9.050701 0 0 0-1.67376-5.579199 17.140539 17.140539 0 0 0-4.897297-4.1844 53.715289 53.715289 0 0 0-7.655901-3.781457q-4.463359-1.797742-10.383509-3.874444l-22.595756-9.484638a67.105367 67.105367 0 0 1-13.947998-7.376941 54.3352 54.3352 0 0 1-11.654327-11.034416 50.08881 50.08881 0 0 1-7.965857-14.381935 52.10352 52.10352 0 0 1-2.944577-17.853438 48.848987 48.848987 0 0 1 5.021279-21.913854 56.070951 56.070951 0 0 1 13.947998-17.853437 67.29134 67.29134 0 0 1 21.355934-12.11926 79.844538 79.844538 0 0 1 27.214094-4.463359 91.870812 91.870812 0 0 1 31.646457 5.858159 77.860823 77.860823 0 0 1 28.577897 18.411357l-23.95956 30.220661a87.066501 87.066501 0 0 0-17.729455-9.763598 50.243787 50.243787 0 0 0-18.597331-3.347519 26.74916 26.74916 0 0 0-13.947997 3.099555 10.073554 10.073554 0 0 0-5.021279 9.298665 10.817447 10.817447 0 0 0 7.128976 9.918576q7.128977 3.502497 19.651179 8.213821l22.037837 8.647758a59.542452 59.542452 0 0 1 26.222235 18.597331 49.592881 49.592881 0 0 1 9.298665 30.99555 53.405333 53.405333 0 0 1-4.742319 22.037836 53.560311 53.560311 0 0 1-13.79302 18.597331 68.624149 68.624149 0 0 1-22.192814 12.708175 88.306323 88.306323 0 0 1-30.15867 4.525351zM463.476466 650.906559l-53.436329-181.32397h50.770711l18.969277 79.224627q3.905439 14.505918 6.695039 28.608893t6.695039 28.856858h1.11584q3.905439-14.784878 6.819021-28.856858t6.540061-28.608893l18.442353-79.224627h48.972969L521.624118 650.906559z","p-id":5971})))},G=s.forwardRef(function(B,b){return s.createElement(T,$({svgRef:b},B))}),nt=P.p+"static/csv.450882d9.svg";function At(B){var b=B.title,L=B.name,y=B.icon,D=B.onClick;return(0,n.jsx)("li",{onClick:D,children:(0,n.jsxs)("a",{className:"action-menu-item",children:[y!=="csv"&&(0,n.jsx)("span",{className:"menu-item-check codicon codicon-".concat(y)}),y==="csv"&&(0,n.jsx)(G,{className:"menu-item-check",style:{width:19,height:19}}),(0,n.jsx)("span",{className:"action-label",children:b||L})]})})}function Lt(){var B=(0,s.useState)(!1),b=(0,t.Z)(B,2),L=b[0],y=b[1],D=(0,s.useRef)(null),k=(0,s.useState)({top:0,left:0}),u=(0,t.Z)(k,2),m=u[0],M=u[1],z=(0,s.useContext)(c),F=z.gridRef,j=(0,s.useState)(""),Y=(0,t.Z)(j,2),Q=Y[0],tt=Y[1];(0,s.useLayoutEffect)(function(){if(D.current){var ot=D.current.getBoundingClientRect(),mt=ot.top,st=ot.right;M({left:st,top:mt})}},[]);var x=function(){var mt=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"text",st=arguments.length>1?arguments[1]:void 0;E()(F.current.getCopyText(mt,Q||st),{format:mt==="csv"?"text/html":"text/plain"})};return(0,n.jsxs)("li",{className:"action-item",onMouseEnter:function(){y(!0)},onMouseLeave:function(){return y(!1)},ref:D,children:[(0,n.jsxs)("a",{className:"action-menu-item",role:"menuitem","aria-checked":"false",onClick:function(){return x("text"," ")},children:[(0,n.jsx)("span",{className:"menu-item-check codicon codicon-files"}),(0,n.jsxs)("span",{className:"action-label","aria-label":"Run",children:["\u590D\u5236",(0,n.jsx)("input",{style:{marginLeft:10,border:"1px solid #999",flexGrow:1,width:80},placeholder:"\u5206\u9694\u7B26",onClick:function(mt){mt.stopPropagation()},value:Q,onChange:function(mt){return tt(mt.target.value)}})]}),(0,n.jsx)("span",{className:"submenu-indicator codicon codicon-menu-submenu"})]}),L&&(0,n.jsxs)(i,{top:m.top,left:m.left,style:{zIndex:10001,minWidth:120},children:[(0,n.jsx)(At,{name:"\u590D\u5236\u4E3A JSON",icon:"json",onClick:function(){return x("json")}}),(0,n.jsx)(At,{name:"\u590D\u5236\u4E3A CSV",icon:"csv",onClick:function(){return x("html")}})]})]})}var zt,It,q,d,C,w,W=["svgRef","title"];function O(){return O=Object.assign?Object.assign.bind():function(B){for(var b=1;b<arguments.length;b++){var L=arguments[b];for(var y in L)Object.prototype.hasOwnProperty.call(L,y)&&(B[y]=L[y])}return B},O.apply(this,arguments)}function V(B,b){if(B==null)return{};var L=f(B,b),y,D;if(Object.getOwnPropertySymbols){var k=Object.getOwnPropertySymbols(B);for(D=0;D<k.length;D++)y=k[D],!(b.indexOf(y)>=0)&&(!Object.prototype.propertyIsEnumerable.call(B,y)||(L[y]=B[y]))}return L}function f(B,b){if(B==null)return{};var L={},y=Object.keys(B),D,k;for(k=0;k<y.length;k++)D=y[k],!(b.indexOf(D)>=0)&&(L[D]=B[D]);return L}var ct=function(b){var L=b.svgRef,y=b.title,D=V(b,W);return s.createElement("svg",O({t:1619365939665,className:"icon",viewBox:"0 0 1024 1024","p-id":1127,width:48,height:48,ref:L},D),y?s.createElement("title",null,y):null,zt||(zt=s.createElement("defs",null,s.createElement("style",{type:"text/css"}))),It||(It=s.createElement("path",{d:"M170.666667 170.666667h341.333333v739.555555H170.666667z",fill:"#D9D9D9","p-id":1128})),q||(q=s.createElement("path",{d:"M910.222222 113.777778v796.444444H113.777778V113.777778h796.444444z m-56.888889 56.888889H170.666667v682.666666h682.666666V170.666667z",fill:"#595959","p-id":1129})),d||(d=s.createElement("path",{d:"M483.555556 170.666667h56.888888v682.666666h-56.888888z",fill:"#595959","p-id":1130})),C||(C=s.createElement("path",{d:"M170.666667 540.444444v-56.888888h682.666666v56.888888z",fill:"#595959","p-id":1131})),w||(w=s.createElement("path",{d:"M170.552889 130.275556l353.28 353.28-40.277333 40.220444-353.28-353.28zM130.275556 554.382222l40.277333-40.277333 353.28 353.28-40.277333 40.220444z",fill:"#595959","p-id":1132})))},U=s.forwardRef(function(B,b){return s.createElement(ct,O({svgRef:b},B))}),bt=P.p+"static/frozen.742fa194.svg";function at(){var B,b=(0,s.useContext)(c),L=b.gridRef,y=(0,s.useContext)(o),D=y.cell,k=D.isNormal,u=(0,s.useState)(((B=L.current)===null||B===void 0?void 0:B.frozenRow)===D.rowIndex&&L.current.frozenColumn===D.columnIndex),m=(0,t.Z)(u,1),M=m[0],z=function(){if(L.current)try{L.current.frozenRow=M?0:D.rowIndex,L.current.frozenColumn=M?0:D.columnIndex}catch(j){console.error("Cannot set a value larger than the number of visible rows or columns.")}};return k?(0,n.jsx)("li",{className:"action-item",onClick:z,children:(0,n.jsxs)("a",{className:"action-menu-item",role:"menuitem","aria-checked":"false",children:[(0,n.jsx)(U,{className:"menu-item-check",style:{width:24,height:24}}),(0,n.jsx)("span",{className:"action-label","aria-label":"Run",children:M?"\u53D6\u6D88\u51BB\u7ED3\u7A97\u683C":"\u51BB\u7ED3\u7A97\u683C"})]})}):(0,n.jsx)(n.Fragment,{})}var X=P(19671),ut=P(24843);function wt(){var B,b,L=(0,s.useContext)(o),y=L.cell,D=(0,s.useContext)(c),k=D.gridRef,u=(0,s.useState)(!1),m=(0,t.Z)(u,2),M=m[0],z=m[1],F=(0,ut.a)(),j=(0,s.useState)(k.current.self.columnFilters[(B=y.header)===null||B===void 0?void 0:B.name]||""),Y=(0,t.Z)(j,2),Q=Y[0],tt=Y[1],x=(0,s.useState)({top:0,left:0}),ot=(0,t.Z)(x,2),mt=ot[0],st=ot[1],Ut=(0,s.useRef)(null),Wt=(0,s.useState)([]),Ot=(0,t.Z)(Wt,2),dt=Ot[0],pt=Ot[1];if((0,s.useLayoutEffect)(function(){if(Ut.current){var xt=Ut.current.getBoundingClientRect(),Dt=xt.left,Bt=xt.bottom;st({left:Dt,top:Bt})}},[]),(0,s.useEffect)(function(){if(k.current&&(y.isColumnHeader||y.isNormal)){var xt={},Dt=0;k.current.data.forEach(function(Bt){var St=Bt[y.header.name]===null?Bt[y.header.name]:String(Bt[y.header.name]).trim(),Qt=k.current.self.blankValues.includes(St)?k.current.self.attributes.blanksText:St;xt[Qt]||Dt>20||(Dt+=1,xt[Qt]=k.current.self.formatters[y.header.type||"string"]({cell:{value:Qt}}))}),pt(Object.keys(xt).map(function(Bt){return{title:Bt,value:xt[Bt]}}))}},[]),(0,s.useEffect)(function(){k.current&&(y.isColumnHeader||y.isNormal)&&k.current.setFilter(y.header.name,Q)},[Q]),y.type!=="image"&&((b=y.header)===null||b===void 0?void 0:b.dataType)!=="image")return(0,n.jsx)(n.Fragment,{});function Mt(){y.type==="image"?F.downloadCurrentAttachment(y.data.id):y.header.dataType==="image"&&F.downloadSurveyAttachment("attachment")}return(0,n.jsx)("li",{className:"action-item",onMouseEnter:function(){z(!0)},onMouseLeave:function(){return z(!1)},children:(0,n.jsxs)("a",{className:"action-menu-item",onClick:function(){return Mt()},children:[(0,n.jsx)(X.Z,{className:"menu-item-check"}),(0,n.jsx)("span",{className:"action-label",children:(0,n.jsxs)("span",{style:{whiteSpace:"nowrap"},children:["\u9644\u4EF6\u4E0B\u8F7D(",y.type==="image"?"\u5F53\u524D\u7B54\u6848":"\u6240\u6709\u7B54\u6848",")"]})})]})})}var Et=P(73935);function vt(B,b){switch(b.type){case"checkAll":var L=b.payload.checked;return(0,_.Z)((0,_.Z)({},B),{},{allChecked:L});default:throw new Error}}var lt=function(b){var L=b.onClose,y=b.contextPosition,D=y.top,k=y.left,u=b.cell;(0,s.useLayoutEffect)(function(){return window.addEventListener("click",L),function(){window.removeEventListener("click",L)}},[]);var m=(0,s.useReducer)(vt,{allChecked:!0}),M=(0,t.Z)(m,2),z=M[0],F=M[1];return Et.createPortal((0,n.jsx)(o.Provider,{value:{cell:u,dispatch:F,allChecked:z.allChecked},children:(0,n.jsxs)(i,{top:D,left:k,style:{width:250},children:[(0,n.jsx)(g,{}),(0,n.jsx)(l,{}),u.header&&u.header.name&&(0,n.jsx)(p,{}),(0,n.jsx)(v,{}),(0,n.jsx)(Lt,{}),(0,n.jsx)(at,{}),(0,n.jsx)(l,{}),(0,n.jsx)(S,{}),(0,n.jsx)(wt,{})]})}),document.body)},Zt=lt;function Ct(B){var b=(0,s.useRef)();return(0,s.useEffect)(function(){b.current=B}),b.current}var yt=P(59250),Xt=P(13013),Ft=P(30887),Yt=P(28682),jt=P(9715),Vt=P(55246),ae=P(77883),ee=P(48592),re=P(71194),Nt=P(50146),Pt=P(14603),ce=P.n(Pt),de=P(83434),ne=P.n(de),Gt=P(53758),ue=P.n(Gt),le=P(41198),oe=P.n(le),he=P(34994),ge=P.n(he),se=function(b){var L=b.total,y=L===void 0?0:L,D=b.current,k=D===void 0?1:D,u=b.pageSize,m=u===void 0?50:u,M=b.onChange,z=(0,s.useRef)(m),F=Math.ceil(y/m);m<=0&&(F=1);var j=(k-1)*m+1,Y=k*m;(Y<=0||Y>y)&&(Y=y);var Q=function(x){typeof x=="number"?M({current:k,pageSize:x}):x==="\u663E\u793A\u6240\u6709"?M({current:k,pageSize:-1}):Nt.Z.confirm({icon:null,content:(0,n.jsx)(Vt.Z.Item,{label:"\u81EA\u5B9A\u4E49\u9875\u7801",children:(0,n.jsx)(ee.Z,{style:{width:200},defaultValue:m,onChange:function(mt){z.current=mt}})}),okText:"\u786E\u5B9A",cancelText:"\u53D6\u6D88",onOk:function(){M({current:k,pageSize:z.current})}})};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"toolbar-item",style:{borderLeft:"1px solid #d3cfcf"},children:(0,n.jsx)("a",{className:k===1?"disabled":"",title:"First page",onClick:function(){k>1&&M({current:1,pageSize:m})},children:(0,n.jsx)(ce(),{size:18,style:{cursor:"pointer"}})})}),(0,n.jsx)("div",{className:"toolbar-item",children:(0,n.jsx)("a",{className:k===1?"disabled":"",onClick:function(){k>1&&M({current:k-1,pageSize:m})},title:"Previous page",children:(0,n.jsx)(oe(),{size:18,style:{cursor:"pointer"}})})}),(0,n.jsx)("div",{className:"toolbar-item",children:(0,n.jsx)("a",{title:"Page set",children:(0,n.jsx)(Xt.Z,{trigger:["click"],placement:"bottom",overlay:(0,n.jsx)(Yt.Z,{style:{minWidth:80,padding:0},items:[20,50,100,500,1e3,"\u663E\u793A\u6240\u6709","\u81EA\u5B9A\u4E49\u9875\u7801"].map(function(tt){return{label:tt,key:tt,onClick:function(){return Q(tt)},style:{padding:"0 1em"}}})}),children:(0,n.jsxs)("span",{style:{display:"flex",width:65,justifyContent:"center",alignItems:"center"},children:[y===0?"0 \u884C":"".concat(j,"-").concat(Y),(0,n.jsx)(ge(),{size:18,style:{cursor:"pointer"}})]})})})}),y>0&&(0,n.jsx)("div",{className:"toolbar-item",children:(0,n.jsx)("a",{onClick:function(){M({current:k,pageSize:m})},title:"Refresh total",children:(0,n.jsxs)("span",{style:{lineHeight:"16px"},children:["of ",y]})})}),(0,n.jsx)("div",{className:"toolbar-item",children:(0,n.jsx)("a",{className:k===F?"disabled":"",onClick:function(){k<F&&M({current:k+1,pageSize:m})},title:"Next page",children:(0,n.jsx)(ue(),{size:18,style:{cursor:"pointer"}})})}),(0,n.jsx)("div",{className:"toolbar-item",style:{borderRight:"1px solid #d3cfcf"},children:(0,n.jsx)("a",{className:k===F?"disabled":"",onClick:function(){k<F&&M({current:m,pageSize:m})},title:"Last page",children:(0,n.jsx)(ne(),{size:18,style:{cursor:"pointer"}})})})]})},me=P(9761),Ae=P(93756),Ce=P.n(Ae),pe=(0,me.Pi)(function(B){return(0,n.jsx)("div",{className:"toolbar-item",style:{width:45,borderRight:"1px solid #c5c5c5"},onClick:function(){return B.onClick()},children:(0,n.jsx)("a",{onClick:function(){},children:(0,n.jsx)(Ce(),{size:18,style:{cursor:"pointer"}})})})}),xe=(0,s.forwardRef)(function(B,b){var L=B.height,y=B.width,D=B.schema,k=B.data,u=B.setting,m=B.editable,M=m===void 0?!0:m,z=B.mode,F=z===void 0?"table":z,j=B.theme,Y=j===void 0?"light":j,Q=B.selectionMode,tt=Q===void 0?"cell":Q,x=B.changesWithBackground,ot=x===void 0?!1:x,mt=B.allowSorting,st=mt===void 0?!1:mt,Ut=B.style,Wt=B.defaultChanges,Ot=B.onSelect,dt=B.onChange,pt=B.onSyncSetting,Mt=B.onReload,xt=B.onDoubleClick,Dt=B.contextMenuVisible,Bt=Dt===void 0?!0:Dt,St=B.onClick,Qt=(0,s.useRef)(null),Z=(0,s.useRef)(),ft=(0,s.useState)(),Rt=(0,t.Z)(ft,2),kt=Rt[0],qt=Rt[1],ie=(0,s.useState)(),_t=(0,t.Z)(ie,2),Jt=_t[0],ve=_t[1],Me=(0,s.useState)(-1),Be=(0,t.Z)(Me,2),$t=Be[0],Ie=Be[1],be=(0,s.useRef)({changes:Wt,initialized:!(Wt&&(Wt.insert.length>0||Wt.update.length>0||Wt.remove.length>0))});(0,s.useEffect)(function(){Z.current&&(Z.current.theme=Y,Z.current.style=r(Y))},[Y]),(0,s.useImperativeHandle)(b,function(){return{getData:function(){if(Z.current)return Z.current.data},scrollTo:function(it,Ht){if(Z.current&&it>0)return Z.current.scrollIntoView(it,Ht)},getSelectedRows:function(){var it={};if(Z.current){var Ht=Z.current.selectedRows;Ht.forEach(function(Tt,te){Tt&&(it["".concat(te)]=Tt)})}return it},getActiveCell:function(){if(Z.current)return Z.current.activeCell},getGridInstance:function(){return Z.current}}}),(0,s.useEffect)(function(){return Z.current=(0,I.Z)({editable:M,parentNode:Qt.current,data:k,schema:D,style:r(Y),selectionFollowsActiveCell:!0,singleRecordViewRowIndex:-1,selectionMode:tt,showPerformance:!1,changesWithBackground:ot,filters:{html:function(){return!0},checkbox:function(){return!0},multiSelect:function(){return!0},select:function(){return!0},image:function(){return!0},user:function(){return!0},dept:function(){return!0}},formatters:{checkbox:function(it){return it.cell.value===!0?'<input type="checkbox" checked="checked" />':'<input type="checkbox" />'},multiSelect:function(it){return it.cell.value!==void 0?it.cell.value:""},select:function(it){return it.cell.value!==void 0?it.cell.value:""},image:function(it){return it.cell.value!==void 0?it.cell.value:""},user:function(it){var Ht=it.cell.value;if(Ht){var Tt,te=(Tt=it.row._raw)===null||Tt===void 0?void 0:Tt.users;return Ht.split(",").map(function(ye){var Kt;return(Kt=te.find(function(we){return we.userId===ye}))===null||Kt===void 0?void 0:Kt.name}).join(",")}return""},dept:function(it){var Ht=it.cell.value;if(Ht){var Tt,te=(Tt=it.row._raw)===null||Tt===void 0?void 0:Tt.depts;return Ht.split(",").map(function(ye){var Kt;return(Kt=te.find(function(we){return we.id===ye}))===null||Kt===void 0?void 0:Kt.name}).join(",")}return""}},allowSorting:st,showFilter:!0,showRowNumbers:!0,mode:F}),Z.current.theme=Y,Z.current.addEventListener("rendercell",function(J){var it=Z.current.theme==="light"}),Z.current.addEventListener("beginedit",function(J){J.cell.inputStyle=J.style,qt(J.cell)}),Z.current.addEventListener("endedit",function(J){qt(void 0)}),Z.current.addEventListener("click",function(J){St&&St(J.cell)}),Z.current.addEventListener("selectionchanged",function(J){Ot&&Ot(J.selectedData,J)}),Z.current.addEventListener("dblclick",function(J){xt&&xt(J.cell)}),Z.current.addEventListener("datachanged",function(J){dt&&dt(J)}),Z.current.addEventListener("activecellchanged",function(J){if(!J.cell&&Ot){Ot([]);return}else if(!J.cell.data){Ot([]);return}}),Z.current.addEventListener("singlerecordview",function(J){var it=J.rowIndex,Ht=J.needRefresh;if(Ie(it),pt&&Z.current&&it>-1){var Tt=Z.current.syncSetting();pt(Tt)}Ht&&Mt&&Mt()}),Z.current.addEventListener("contextmenu",function(J){Bt&&(ve(void 0),ve(J))}),!be.current.initialized&&k&&(be.current.initialized=!0),function(){Z.current&&Z.current.dispose()}},[]),(0,s.useEffect)(function(){Z.current&&(Z.current.style.height=L+"px",Z.current.style.width=y+"px")},[L,y]);var Se=Ct(D),Ee=Ct(k);(0,s.useEffect)(function(){Z.current&&D&&(Z.current.singleRecordViewRowIndex=-1,Se&&Se!==D&&(D.map(function(J){return(0,_.Z)({width:90},J)}),Z.current.schema=D),Ee&&Ee!==k&&(Z.current.data=k))},[D,k]);var De=Ct($t);return(0,s.useEffect)(function(){if(De!==void 0&&$t===-1)Z.current&&(Z.current.schema=D,Z.current.data=k,u&&Z.current.applySetting(u));else if($t>-1&&Z.current&&D&&k){Z.current.resetSetting(),Z.current.schema=[{name:"row",title:"\u5B57\u6BB5\u540D",readOnly:!0},{name:"field",title:"\u5B57\u6BB5\u503C"}];var J=k[$t],it=[];D.forEach(function(Ht,Tt){it.push({row:Ht.title,field:J[Ht.name],_i:J._i,dataType:Ht.dataType})}),Z.current.data=it}},[$t]),(0,n.jsx)(c.Provider,{value:{gridRef:Z},children:(0,n.jsx)("div",{style:(0,_.Z)({width:y,height:L,overflow:"hidden"},Ut),ref:Qt,children:Jt&&(0,n.jsx)(Zt,(0,_.Z)({onClose:function(){ve(void 0)}},Jt),"".concat(Jt.cell.rowIndex,"-").concat(Jt.cell.columnIndex))})})})},37398:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return t}});var _=P(30020);function t(){var s,I={};I.dehyphenateProperty=function(c){c=c.replace("--cdg-","");var o="",n;return Array.prototype.forEach.call(c,function(i){if(n){n=!1,o+=i.toUpperCase();return}if(i==="-"){n=!0;return}o+=i}),o},I.hyphenateProperty=function(c,o){var n="";return Array.prototype.forEach.call(c,function(i){if(i===i.toUpperCase()){n+="-"+i.toLowerCase();return}n+=i}),(o?"--cdg-":"")+n};function e(r,c){var o={},n;return(0,_.Z)(o),n=o.defaults[r].filter(function(i){return i[0].toLowerCase()===c.toLowerCase()||I.hyphenateProperty(i[0])===c.toLowerCase()||I.hyphenateProperty(i[0],!0)===c.toLowerCase()})[0],n}return I.applyComponentStyle=function(r,c){if(!!c.isComponent){var o=window.getComputedStyle(c.tagName==="CANVAS-DATAGRID"?c:c.canvas,null),n={};c.computedStyle=o,(0,_.Z)(n),n=n.defaults.styles,n.forEach(function(i){var a;a=o.getPropertyValue(I.hyphenateProperty(i[0],!0)),a===""&&(a=o.getPropertyValue(I.hyphenateProperty(i[0],!1))),a!==""&&typeof a=="string"&&c.setStyleProperty(i[0],s[typeof i[1]](a.replace(/^\s+/,"").replace(/\s+$/,""),i[1]),!0)}),!r&&c.dispatchEvent&&(requestAnimationFrame(function(){c.resize(!0)}),c.dispatchEvent("stylechanged",c.style))}},s={data:function(c){try{return JSON.parse(c)}catch(o){throw new Error("Cannot read JSON data in canvas-datagrid data.")}},schema:function(c){try{return JSON.parse(c)}catch(o){throw new Error("Cannot read JSON data in canvas-datagrid schema attribute.")}},number:function(c,o){var n=parseInt(c,10);return isNaN(n)?o:n},boolean:function(c){return/true/i.test(c)},string:function(c){return c}},I.getObservableAttributes=function(){var r={},c=["data","schema","style","className","name"];return(0,_.Z)(r),r.defaults.attributes.forEach(function(o){c.push(o[0].toLowerCase())}),c},I.disconnectedCallback=function(){this.connected=!1},I.connectedCallback=function(){var r=this;r.parentDOMNode.innerHTML="",r.parentDOMNode.appendChild(r.canvas),r.connected=!0,I.observe(r),I.applyComponentStyle(!0,r),r.resize(!0)},I.adoptedCallback=function(){this.resize()},I.attributeChangedCallback=function(r,c,o){var n,i=this,a;if(r==="style"){I.applyComponentStyle(!1,i);return}if(r==="data"){i.dataType==="application/x-canvas-datagrid"&&(i.dataType="application/json+x-canvas-datagrid"),i.data=o;return}if(r==="schema"){i.schema=s.schema(o);return}if(r==="name"){i.name=o;return}if(!(r==="class"||r==="className")){if(a=e("attributes",r),a){n=s[typeof a[1]],i.attributes[a[0]]=n(o);return}/^on/.test(r)&&i.addEventListener("on"+r,Function("e",o))}},I.observe=function(r){var c;!window.MutationObserver||(r.applyComponentStyle=function(){I.applyComponentStyle(!1,r),r.resize()},c=new window.MutationObserver(function(o){var n,i;Array.prototype.forEach.call(o,function(a){if(a.attributeName==="class"||a.attributeName==="style"){i=!0;return}if(a.target.nodeName==="STYLE"){i=!0;return}if(a.target.parentNode&&a.target.parentNode.nodeName==="STYLE"){i=!0;return}a.target===r&&(a.addedNodes.length>0||a.type==="characterData")&&(n=!0)}),i&&r.applyComponentStyle(!1,r),n&&(r.dataType==="application/x-canvas-datagrid"&&(r.dataType="application/json+x-canvas-datagrid"),r.data=r.innerHTML)}),c.observe(r,{characterData:!0,childList:!0,attributes:!0,subtree:!0}),Array.prototype.forEach.call(document.querySelectorAll("style"),function(o){c.observe(o,{characterData:!0,childList:!0,attributes:!0,subtree:!0})}),r.observer=c)},I}},22873:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return _}});function _(t){var s,I,e;function r(i){t.createInlineStyle(i,"canvas-datagrid-context-menu-item"+(t.mobile?"-mobile":"")),i.addEventListener("mouseover",function(){t.createInlineStyle(i,"canvas-datagrid-context-menu-item:hover")}),i.addEventListener("mouseout",function(){t.createInlineStyle(i,"canvas-datagrid-context-menu-item")})}function c(i,a,l,g){var A=document.createElement("div"),p=document.createElement("div"),S=document.createElement("div"),v=[],h=-1,E={},H;if(!Array.isArray(l))throw new Error("createContextMenu expects an array.");function R(){l.forEach(function(T){var G=document.createElement("div"),nt;function At(q){q.relatedTarget===A||T.contextMenu.container===q.relatedTarget||nt===q.relatedTarget||G===q.relatedTarget||T.contextMenu.container.contains(q.relatedTarget)||(T.contextMenu.dispose(),v.splice(v.indexOf(T.contextMenu),1),T.contextMenu=void 0,G.removeEventListener("mouseout",At),A.removeEventListener("mouseout",At),G.setAttribute("contextOpen","0"),G.setAttribute("opening","0"))}function Lt(q){if(!(G.getAttribute("opening")!=="1"||G.getAttribute("contextOpen")==="1")){var d=G.getBoundingClientRect();d={left:d.left+t.style.childContextMenuMarginLeft+A.offsetWidth,top:d.top+t.style.childContextMenuMarginTop,bottom:d.bottom,right:d.right},T.contextMenu=c(i,d,q,E),G.setAttribute("contextOpen","1"),G.addEventListener("mouseout",At),A.addEventListener("mouseout",At),v.push(T.contextMenu)}}function zt(){var q;if(G.getAttribute("contextOpen")!=="1"){if(G.setAttribute("opening","1"),typeof T.items=="function"){q=T.items.apply(E,[function(d){Lt(d)}]),q!==void 0&&Array.isArray(q)&&Lt(q);return}Lt(T.items)}}function It(q){function d(C){if(C!==null){if(typeof C=="function")return d(C(i));if(typeof C=="object"){G.appendChild(C);return}r(G),G.innerHTML=C}}d(q.title),q.contextItemContainer=G,(q.items&&q.items.length>0||typeof q.items=="function")&&(nt=document.createElement("div"),t.createInlineStyle(nt,"canvas-datagrid-context-child-arrow"),nt.innerHTML=t.style.childContextMenuArrowHTML,G.appendChild(nt),G.addEventListener("mouseover",zt),G.addEventListener("mouseout",function(){G.setAttribute("opening","0")})),q.click&&G.addEventListener("click",function(C){q.click.apply(t,[C])})}It(T),A.appendChild(G)})}function K(T){l[T].contextItemContainer.dispatchEvent(new Event("click"))}function N(){A.scrollTop>0?t.parentDOMNode.appendChild(p):p.parentNode&&p.parentNode.removeChild(p),A.scrollTop>=A.scrollHeight-A.offsetHeight&&S.parentNode?S.parentNode.removeChild(S):A.scrollHeight-A.offsetHeight>0&&!(A.scrollTop>=A.scrollHeight-A.offsetHeight)&&t.parentDOMNode.appendChild(S)}function $(T){return function G(){var nt=t.attributes.contextHoverScrollAmount;T==="up"&&A.scrollTop===0||T==="down"&&A.scrollTop===A.scrollHeight||(A.scrollTop+=T==="up"?-nt:nt,I=setTimeout(G,t.attributes.contextHoverScrollRateMs,T))}}function gt(T){return function(){clearTimeout(I)}}function ht(){var T={},G=t.scrollOffset(t.canvas);s===void 0&&(s=t.style.contextMenuZIndex),R(),t.createInlineStyle(A,"canvas-datagrid-context-menu"+(t.mobile?"-mobile":"")),T.x=a.left-G.left,T.y=a.top-G.top,T.height=0,s+=1,A.classList.add("grid-context-container"),p.style.color=t.style.contextMenuArrowColor,S.style.color=t.style.contextMenuArrowColor,[p,S].forEach(function(nt){nt.style.textAlign="center",nt.style.position="absolute",nt.style.zIndex=s+1}),A.style.zIndex=s,g&&g.inputDropdown&&(A.style.maxHeight=window.innerHeight-T.y-t.style.autocompleteBottomMargin+"px",A.style.minWidth=a.width+"px",T.y+=a.height),t.mobile&&(A.style.width=a.width+"px"),A.style.left=T.x+"px",A.style.top=T.y+"px",A.addEventListener("scroll",N),A.addEventListener("wheel",function(nt){t.hasFocus&&(A.scrollTop+=nt.deltaY,A.scrollLeft+=nt.deltaX),N()}),p.innerHTML=t.style.contextMenuArrowUpHTML,S.innerHTML=t.style.contextMenuArrowDownHTML,A.appendChild(p),document.body.appendChild(S),document.body.appendChild(A),H=A.getBoundingClientRect(),H.bottom>window.innerHeight&&(g&&g.inputDropdown||(T.y-=H.bottom+t.style.contextMenuWindowMargin-window.innerHeight),T.y<0&&(T.y=t.style.contextMenuWindowMargin),A.offsetHeight>window.innerHeight-t.style.contextMenuWindowMargin&&(A.style.height=window.innerHeight-t.style.contextMenuWindowMargin*2+"px")),H.right>window.innerWidth&&(T.x-=H.right-window.innerWidth+t.style.contextMenuWindowMargin),T.x<0&&(T.x=t.style.contextMenuWindowMargin),T.y<0&&(T.y=t.style.contextMenuWindowMargin),A.style.left=T.x+"px",A.style.top=T.y+"px",H=A.getBoundingClientRect(),p.style.top=H.top+"px",S.style.top=H.top+H.height-S.offsetHeight+"px",p.style.left=H.left+"px",S.style.left=H.left+"px",S.style.width=A.offsetWidth+"px",p.style.width=A.offsetWidth+"px",S.addEventListener("mouseover",$("down")),S.addEventListener("mouseout",gt("down")),p.addEventListener("mouseover",$("up")),p.addEventListener("mouseout",gt("up")),N()}return E.parentGrid=t.intf,E.parentContextMenu=g,E.container=A,ht(),E.clickIndex=K,E.rect=H,E.items=l,E.upArrow=p,E.downArrow=S,E.dispose=function(){clearTimeout(I),v.forEach(function(T){T.dispose()}),[S,p,A].forEach(function(T){T.parentNode&&T.parentNode.removeChild(T)})},Object.defineProperty(E,"selectedIndex",{get:function(){return h},set:function(G){if(typeof G!="number"||isNaN(G||!isFinite(G)))throw new Error("Context menu selected index must be a sane number.");h=G,h>l.length-1&&(h=l.length-1),h<0&&(h=0),l.forEach(function(nt,At){if(At===h)return t.createInlineStyle(nt.contextItemContainer,"canvas-datagrid-context-menu-item:hover");t.createInlineStyle(nt.contextItemContainer,"canvas-datagrid-context-menu-item")})}}),E}function o(i){var a=document.createElement("div"),l=document.createElement("div"),g=document.createElement("button"),A=document.createElement("input"),p=i.cell&&i.cell.header?i.cell.header.title||i.cell.header.name:"",S;function v(){A.style.background=t.style.contextFilterInputBackground,A.style.color=t.style.contextFilterInputColor,t.invalidFilterRegEx&&(A.style.background=t.style.contextFilterInvalidRegExpBackground,A.style.color=t.style.contextFilterInvalidRegExpColor)}function h(){var H=0,R={},K=[];return t.data.forEach(function(N){var $=N[i.cell.header.name]==null?N[i.cell.header.name]:String(N[i.cell.header.name]).trim(),gt=t.blankValues.includes($)?t.attributes.blanksText:$;R[gt]||H>t.attributes.maxAutoCompleteItems||(H+=1,R[gt]={title:t.formatters[i.cell.header.type||"string"]({cell:{value:gt}}),click:function(T){A.value=gt,T.stopPropagation(),A.dispatchEvent(new Event("keyup")),t.disposeAutocomplete()}})}),Object.keys(R).indexOf(t.attributes.blanksText)!==-1&&(K.push(R[t.attributes.blanksText]),delete R[t.attributes.blanksText]),K.concat(Object.keys(R).map(function(N){return R[N]}))}function E(H){if(!(H&&["ArrowDown","ArrowUp","Enter","Tab"].includes(H.key))){var R=h();S=A.getBoundingClientRect(),e&&(e.dispose(),e=void 0),e=c(i,{left:S.left,top:S.top,right:S.right,bottom:S.bottom,height:S.height,width:S.width},R,{inputDropdown:!0}),e.selectedIndex=0}}t.createInlineStyle(l,"canvas-datagrid-context-menu-label"),t.createInlineStyle(g,"canvas-datagrid-context-menu-filter-button"),t.createInlineStyle(A,"canvas-datagrid-context-menu-filter-input"),v(),A.onclick=t.disposeAutocomplete,A.addEventListener("keydown",function(H){H.key==="ArrowDown"&&(e.selectedIndex+=1),H.key==="ArrowUp"&&(e.selectedIndex-=1),H.key==="Enter"&&(e.clickIndex(e.selectedIndex),t.disposeContextMenu()),H.key==="Tab"&&(e.clickIndex(e.selectedIndex),H.preventDefault()),H.key==="Escape"&&t.disposeContextMenu()}),A.addEventListener("keyup",function(){t.setFilter(i.cell.header.name,A.value)}),A.addEventListener("keyup",E),["focus","blur","keydown","keyup","change"].forEach(function(H){A.addEventListener(H,v)}),A.value=i.cell.header&&t.columnFilters[i.cell.header.name]||"",l.innerHTML=t.attributes.filterOptionText.replace(/%s/g,p),g.onclick=function(){if(e)return t.disposeAutocomplete();E()},g.innerHTML=t.style.contextFilterButtonHTML,a.addEventListener("click",function(H){return H.stopPropagation()}),a.appendChild(l),a.appendChild(A),a.appendChild(g),i.items.push({title:a}),Object.keys(t.columnFilters).length&&Object.keys(t.columnFilters).forEach(function(H){var R=t.getHeaderByName(H);i.items.push({title:t.attributes.removeFilterOptionText.replace(/%s/g,R.title||R.name),click:function(N){N.preventDefault(),t.setFilter(H,""),t.controlInput.focus()}})})}function n(i){var a=!(i.cell.isBackground||i.cell.isColumnHeaderCellCap||i.cell.isScrollBar||i.cell.isCorner||i.cell.isRowHeader)&&i.cell.header;t.attributes.showFilter&&a&&o(i),t.attributes.showCopy&&t.selections.reduce(function(l,g){return l+g.length},0)>0&&i.items.push({title:t.attributes.copyText,click:function(){document.execCommand("copy"),t.disposeContextMenu(),t.controlInput.focus()}}),t.attributes.showPaste&&t.clipBoardData&&i.items.push({title:t.attributes.pasteText,click:function(){t.paste(t.clipBoardData,i.cell.columnIndex,i.cell.rowIndex),t.draw()}}),t.attributes.showColumnSelector&&(i.items.push({title:t.attributes.columnSelectorText,items:function(){var g=[];return t.getSchema().forEach(function(A){function p(v){A.hidden=!A.hidden,t.dispatchEvent("togglecolumn",{column:A,hidden:A.hidden}),v.preventDefault(),t.stopPropagation(v),t.disposeContextMenu(),t.resize(!0),t.setStorageData()}var S=document.createElement("div");r(S),S.addEventListener("touchstart",p),S.addEventListener("click",p),S.innerHTML=(A.hidden?t.attributes.columnSelectorHiddenText:t.attributes.columnSelectorVisibleText)+(A.title||A.name),g.push({title:S})}),g}}),i.cell&&i.cell.header&&i.cell.columnIndex>-1&&i.items.push({title:t.attributes.hideColumnText.replace(/%s/gi,i.cell.header.title||i.cell.header.name),click:function(g){t.getSchema()[i.cell.columnIndex].hidden=!0,g.preventDefault(),t.stopPropagation(g),t.disposeContextMenu(),t.setStorageData(),setTimeout(function(){t.resize(!0)},10)}})),t.attributes.saveAppearance&&t.attributes.showClearSettingsOption&&(Object.keys(t.sizes.rows).length>0||Object.keys(t.sizes.columns).length>0)&&i.items.push({title:t.attributes.clearSettingsOptionText,click:function(g){g.preventDefault(),t.sizes.rows={},t.sizes.columns={},t.createRowOrders(),t.createColumnOrders(),t.storedSettings=void 0,t.dispatchEvent("resizecolumn",{columnWidth:t.style.cellWidth}),t.dispatchEvent("resizerow",{cellHeight:t.style.cellHeight}),t.setStorageData(),t.resize(!0),t.disposeContextMenu(),t.controlInput.focus()}}),t.attributes.allowSorting&&t.attributes.showOrderByOption&&a&&(i.items.push({title:t.attributes.showOrderByOptionTextAsc.replace("%s",i.cell.header.title||i.cell.header.name),click:function(g){g.preventDefault(),t.order(i.cell.header.name,"asc"),t.controlInput.focus()}}),i.items.push({title:t.attributes.showOrderByOptionTextDesc.replace("%s",i.cell.header.title||i.cell.header.name),click:function(g){g.preventDefault(),t.order(i.cell.header.name,"desc"),t.disposeContextMenu(),t.controlInput.focus()}}))}t.disposeAutocomplete=function(){e&&(e.dispose(),e=void 0)},t.disposeContextMenu=function(){document.removeEventListener("click",t.disposeContextMenu),s=t.style.contextMenuZIndex,t.disposeAutocomplete(),t.contextMenu&&t.contextMenu.dispose(),t.contextMenu=void 0},t.contextmenuEvent=function(i,a){if(!t.hasFocus&&i.target!==t.canvas)return;function l(){requestAnimationFrame(function(){document.addEventListener("click",t.disposeContextMenu),document.removeEventListener("mouseup",l)})}var g,A=[],p=a||t.getLayerPos(i),S={NativeEvent:i,cell:t.getCellAt(p.x,p.y),items:A};S.cell.isGrid||(g={left:i.clientX,top:i.clientY,right:S.cell.width+S.cell.x,bottom:S.cell.height+S.cell.y,height:S.cell.height,width:S.cell.width},S.contextPosition=g),i.preventDefault(),!t.dispatchEvent("contextmenu",S)}}},30020:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return _}});function _(t){t.defaults={attributes:[["allowColumnReordering",!0],["allowColumnResize",!0],["allowColumnResizeFromCell",!1],["allowFreezingRows",!1],["allowFreezingColumns",!1],["allowMovingSelection",!0],["allowRowHeaderResize",!0],["allowRowReordering",!1],["allowRowResize",!0],["allowRowResizeFromCell",!1],["allowSorting",!1],["autoGenerateSchema",!1],["autoResizeColumns",!1],["autoResizeRows",!1],["blanksText","(Blanks)"],["borderDragBehavior","none"],["borderResizeZone",10],["changesWithBackground",!1],["clearSettingsOptionText","Clear saved settings"],["columnHeaderClickBehavior","sort"],["columnSelectorHiddenText","&nbsp;&nbsp;&nbsp;"],["columnSelectorText","Add/Remove columns"],["columnSelectorVisibleText","\u2713"],["contextHoverScrollAmount",2],["contextHoverScrollRateMs",5],["copyHeadersOnSelectAll",!0],["copyText","Copy"],["debug",!1],["editable",!0],["ellipsisText","..."],["filterOptionText","Filter %s"],["filterTextPrefix","(filtered) "],["globalRowResize",!1],["hideColumnText","Hide %s"],["maxAutoCompleteItems",200],["mode","table"],["multiLine",!1],["name",""],["pageUpDownOverlap",1],["pasteText","Paste"],["persistantSelectionMode",!1],["removeFilterOptionText","Remove filter on %s"],["reorderDeadZone",3],["resizeScrollZone",20],["rowGrabZoneSize",5],["saveAppearance",!0],["scrollAnimationPPSThreshold",.75],["scrollPointerLock",!1],["scrollRepeatRate",75],["selectionFollowsActiveCell",!1],["selectionHandleBehavior","none"],["selectionMode","cell"],["selectionScrollIncrement",20],["selectionScrollZone",20],["showClearSettingsOption",!0],["showColumnHeaders",!0],["showColumnSelector",!0],["showCopy",!1],["showFilter",!0],["showNewRow",!1],["showOrderByOption",!0],["showOrderByOptionTextAsc","Order by %s ascending"],["showOrderByOptionTextDesc","Order by %s descending"],["showPaste",!1],["showPerformance",!1],["showRowHeaders",!0],["showRowNumbers",!0],["singleSelectionMode",!1],["singleRecordViewRowIndex",-1],["snapToRow",!1],["touchContextMenuTimeMs",800],["touchDeadZone",3],["touchEasingMethod","easeOutQuad"],["touchReleaseAcceleration",1e3],["touchReleaseAnimationDurationMs",2e3],["touchScrollZone",20],["touchSelectHandleZone",20],["touchZoomSensitivity",.005],["touchZoomMin",.5],["touchZoomMax",1.75],["maxPixelRatio",2],["tree",!1],["treeHorizontalScroll",!1]],styles:[["activeCellBackgroundColor","rgba(255, 255, 255, 1)"],["activeCellBorderColor","rgba(110, 168, 255, 1)"],["activeCellBorderWidth",1],["activeCellColor","rgba(0, 0, 0, 1)"],["activeCellFont","16px sans-serif"],["activeCellHoverBackgroundColor","rgba(255, 255, 255, 1)"],["activeCellHorizontalAlignment","left"],["activeCellHoverColor","rgba(0, 0, 0, 1)"],["activeCellOverlayBorderColor","rgba(66, 133, 244, 1)"],["activeCellOverlayBorderWidth",1],["activeCellPaddingBottom",5],["activeCellPaddingLeft",5],["activeCellPaddingRight",5],["activeCellPaddingTop",5],["activeCellSelectedBackgroundColor","rgba(236, 243, 255, 1)"],["activeCellSelectedColor","rgba(0, 0, 0, 1)"],["activeCellVerticalAlignment","center"],["activeColumnHeaderCellBackgroundColor","rgba(225, 225, 225, 1)"],["activeColumnHeaderCellColor","rgba(0, 0, 0, 1)"],["activeRowHeaderCellBackgroundColor","rgba(225, 225, 225, 1)"],["activeRowHeaderCellColor","rgba(0, 0, 0, 1)"],["autocompleteBottomMargin",60],["autosizeHeaderCellPadding",8],["autosizePadding",5],["cellAutoResizePadding",13],["cellBackgroundColor","rgba(255, 255, 255, 1)"],["cellBorderColor","rgba(195, 199, 202, 1)"],["cellBorderWidth",1],["cellColor","rgba(0, 0, 0, 1)"],["cellFont","16px sans-serif"],["cellGridHeight",250],["cellHeight",24],["cellHeightWithChildGrid",150],["cellHorizontalAlignment","left"],["cellHoverBackgroundColor","rgba(255, 255, 255, 1)"],["cellHoverColor","rgba(0, 0, 0, 1)"],["cellPaddingBottom",5],["cellPaddingLeft",5],["cellPaddingRight",5],["cellPaddingTop",5],["cellSelectedBackgroundColor","rgba(236, 243, 255, 1)"],["cellSelectedColor","rgba(0, 0, 0, 1)"],["cellVerticalAlignment","center"],["cellWidth",125],["cellWidthWithChildGrid",250],["cellWhiteSpace","nowrap"],["cellLineHeight",1],["cellLineSpacing",3],["childContextMenuArrowColor","rgba(43, 48, 43, 1)"],["childContextMenuArrowHTML","&#x25BA;"],["childContextMenuMarginLeft",-11],["childContextMenuMarginTop",-6],["columnHeaderCellBackgroundColor","rgba(240, 240, 240, 1)"],["columnHeaderCellBorderColor","rgba(172, 172, 172, 1)"],["columnHeaderCellBorderWidth",1],["columnHeaderCellCapBackgroundColor","rgba(240, 240, 240, 1)"],["columnHeaderCellCapBorderColor","rgba(172, 172, 172, 1)"],["columnHeaderCellCapBorderWidth",1],["columnHeaderCellColor","rgba(50, 50, 50, 1)"],["columnHeaderCellFont","16px sans-serif"],["columnHeaderCellHeight",25],["columnHeaderCellHorizontalAlignment","left"],["columnHeaderCellHoverBackgroundColor","rgba(235, 235, 235, 1)"],["columnHeaderCellHoverColor","rgba(0, 0, 0, 1)"],["columnHeaderCellPaddingBottom",5],["columnHeaderCellPaddingLeft",5],["columnHeaderCellPaddingRight",5],["columnHeaderCellPaddingTop",5],["columnHeaderCellVerticalAlignment","center"],["columnHeaderOrderByArrowBorderColor","rgba(195, 199, 202, 1)"],["columnHeaderOrderByArrowBorderWidth",1],["columnHeaderOrderByArrowColor","rgba(155, 155, 155, 1)"],["columnHeaderOrderByArrowHeight",8],["columnHeaderOrderByArrowMarginLeft",0],["columnHeaderOrderByArrowMarginRight",5],["columnHeaderOrderByArrowMarginTop",6],["columnHeaderOrderByArrowWidth",13],["contextFilterButtonBorder","solid 1px rgba(158, 163, 169, 1)"],["contextFilterButtonBorderRadius","3px"],["contextFilterButtonHTML","&#x25BC;"],["contextFilterInputBackground","rgba(255,255,255,1)"],["contextFilterInputBorder","solid 1px rgba(158, 163, 169, 1)"],["contextFilterInputBorderRadius","0"],["contextFilterInputColor","rgba(0,0,0,1)"],["contextFilterInputFontFamily","sans-serif"],["contextFilterInputFontSize","14px"],["contextFilterInvalidRegExpBackground","rgba(180, 6, 1, 1)"],["contextFilterInvalidRegExpColor","rgba(255, 255, 255, 1)"],["contextMenuArrowColor","rgba(43, 48, 43, 1)"],["contextMenuArrowDownHTML","&#x25BC;"],["contextMenuArrowUpHTML","&#x25B2;"],["contextMenuBackground","rgba(240, 240, 240, 1)"],["contextMenuBorder","solid 1px rgba(158, 163, 169, 1)"],["contextMenuBorderRadius","3px"],["contextMenuChildArrowFontSize","12px"],["contextMenuColor","rgba(43, 48, 43, 1)"],["contextMenuCursor","default"],["contextMenuFilterButtonFontFamily","sans-serif"],["contextMenuFilterButtonFontSize","10px"],["contextMenuFilterInvalidExpresion","rgba(237, 155, 156, 1)"],["contextMenuFontFamily","sans-serif"],["contextMenuFontSize","16px"],["contextMenuHoverBackground","rgba(182, 205, 250, 1)"],["contextMenuHoverColor","rgba(43, 48, 153, 1)"],["contextMenuItemBorderRadius","3px"],["contextMenuItemMargin","2px"],["contextMenuLabelDisplay","inline-block"],["contextMenuLabelMargin","0 3px 0 0"],["contextMenuLabelMaxWidth","700px"],["contextMenuLabelMinWidth","75px"],["contextMenuMarginLeft",3],["contextMenuMarginTop",-3],["contextMenuOpacity","0.98"],["contextMenuPadding","2px"],["contextMenuWindowMargin",30],["contextMenuZIndex",1e4],["cornerCellBackgroundColor","rgba(240, 240, 240, 1)"],["cornerCellBorderColor","rgba(202, 202, 202, 1)"],["debugBackgroundColor","rgba(0, 0, 0, .0)"],["debugColor","rgba(255, 15, 24, 1)"],["debugEntitiesColor","rgba(76, 231, 239, 1.00)"],["debugFont","11px sans-serif"],["debugPerfChartBackground","rgba(29, 25, 26, 1.00)"],["debugPerfChartTextColor","rgba(255, 255, 255, 0.8)"],["debugPerformanceColor","rgba(252, 255, 37, 1.00)"],["debugScrollHeightColor","rgba(248, 33, 103, 1.00)"],["debugScrollWidthColor","rgba(66, 255, 27, 1.00)"],["debugTouchPPSXColor","rgba(246, 102, 24, 1.00)"],["debugTouchPPSYColor","rgba(186, 0, 255, 1.00)"],["display","inline-block"],["editCellBackgroundColor","white"],["editCellBorder","solid 1px rgba(110, 168, 255, 1)"],["editCellBoxShadow","0 2px 5px rgba(0,0,0,0.4)"],["editCellColor","black"],["editCellFontFamily","sans-serif"],["editCellFontSize","16px"],["editCellPaddingLeft",4],["editCellZIndex",1e4],["frozenMarkerHoverColor","rgba(236, 243, 255, 1)"],["frozenMarkerHoverBorderColor","rgba(110, 168, 255, 1)"],["frozenMarkerActiveColor","rgba(236, 243, 255, 1)"],["frozenMarkerActiveBorderColor","rgba(110, 168, 255, 1)"],["frozenMarkerColor","rgba(222, 222, 222, 1)"],["frozenMarkerBorderColor","rgba(168, 168, 168, 1)"],["frozenMarkerBorderWidth",1],["frozenMarkerWidth",2],["gridBackgroundColor","rgba(240, 240, 240, 1)"],["gridBorderCollapse","collapse"],["gridBorderColor","rgba(202, 202, 202, 1)"],["gridBorderWidth",1],["height","auto"],["maxHeight","inherit"],["maxWidth","inherit"],["minColumnWidth",57],["minHeight","inherit"],["minRowHeight",24],["minWidth","inherit"],["mobileContextMenuMargin",10],["mobileEditInputHeight",30],["mobileEditFontFamily","sans-serif"],["mobileEditFontSize","16px"],["moveOverlayBorderWidth",1],["moveOverlayBorderColor","rgba(66, 133, 244, 1)"],["moveOverlayBorderSegments","12, 7"],["name","default"],["overflowY","auto"],["overflowX","auto"],["reorderMarkerBackgroundColor","rgba(0, 0, 0, 0.1)"],["reorderMarkerBorderColor","rgba(0, 0, 0, 0.2)"],["reorderMarkerBorderWidth",1.25],["reorderMarkerIndexBorderColor","rgba(66, 133, 244, 1)"],["reorderMarkerIndexBorderWidth",2.75],["rowHeaderCellBackgroundColor","rgba(240, 240, 240, 1)"],["rowHeaderCellBorderColor","rgba(200, 200, 200, 1)"],["rowHeaderCellBorderWidth",1],["rowHeaderCellColor","rgba(50, 50, 50, 1)"],["rowHeaderCellFont","16px sans-serif"],["rowHeaderCellHeight",25],["rowHeaderCellHorizontalAlignment","left"],["rowHeaderCellHoverBackgroundColor","rgba(235, 235, 235, 1)"],["rowHeaderCellHoverColor","rgba(0, 0, 0, 1)"],["rowHeaderCellPaddingBottom",5],["rowHeaderCellPaddingLeft",5],["rowHeaderCellPaddingRight",5],["rowHeaderCellPaddingTop",5],["rowHeaderCellSelectedBackgroundColor","rgba(217, 217, 217, 1)"],["rowHeaderCellSelectedColor","rgba(50, 50, 50, 1)"],["rowHeaderCellVerticalAlignment","center"],["rowHeaderCellWidth",57],["scrollBarActiveColor","rgba(125, 125, 125, 1)"],["scrollBarBackgroundColor","rgba(240, 240, 240, 1)"],["scrollBarBorderColor","rgba(202, 202, 202, 1)"],["scrollBarBorderWidth",.5],["scrollBarBoxBorderRadius",4.125],["scrollBarBoxColor","rgba(192, 192, 192, 1)"],["scrollBarBoxMargin",2],["scrollBarBoxMinSize",15],["scrollBarBoxWidth",3],["scrollBarCornerBackgroundColor","rgba(240, 240, 240, 1)"],["scrollBarCornerBorderColor","rgba(202, 202, 202, 1)"],["scrollBarWidth",7],["selectionHandleBorderColor","rgba(255, 255, 255, 1)"],["selectionHandleBorderWidth",1.5],["selectionHandleColor","rgba(66, 133, 244, 1)"],["selectionHandleSize",8],["selectionHandleType","square"],["selectionOverlayBorderColor","rgba(66, 133, 244, 1)"],["selectionOverlayBorderWidth",1],["treeArrowBorderColor","rgba(195, 199, 202, 1)"],["treeArrowBorderWidth",1],["treeArrowClickRadius",5],["treeArrowColor","rgba(155, 155, 155, 1)"],["treeArrowHeight",8],["treeArrowMarginLeft",0],["treeArrowMarginRight",5],["treeArrowMarginTop",6],["treeArrowWidth",13],["treeGridHeight",250],["width","auto"],["cellInsertBackgroundColor","#AED6AE"],["cellUpdateBackgroundColor","#ffc069"],["cellRemoveBackgroundColor","#ff7875"]]}}},99050:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return _}});function _(t){t.getClippingRect=function(s){var I=t.position(t.parentNode),e=t.position(s),r=t.scrollOffset(t.canvas),c={x:0,y:0,h:0,w:0},o={x:-Infinity,y:-Infinity,h:Infinity,w:Infinity},n=t.getColumnHeaderCellHeight(),i=t.getRowHeaderCellWidth();return I.top-=r.top,I.left-=r.left,e.top-=r.top,e.left-=r.left,c.h=I.top+I.height-s.offsetTop-t.style.scrollBarWidth,c.w=I.left+I.width-s.offsetLeft-t.style.scrollBarWidth,c.x=I.left+e.left*-1+i,c.y=I.top+e.top*-1+n,{x:c.x>o.x?c.x:o.x,y:c.y>o.y?c.y:o.y,h:c.h<o.h?c.h:o.h,w:c.w<o.w?c.w:o.w}},t.clipElement=function(s){var I=t.getClippingRect(s);I.w<0&&(I.w=0),I.h<0&&(I.h=0),s.style.clip="rect("+I.y+"px,"+I.w+"px,"+I.h+"px,"+I.x+"px)"},t.scrollOffset=function(s){for(var I=0,e=0,r=document.scrollingElement||{scrollLeft:0,scrollTop:0};s.parentNode&&s.nodeName!=="CANVAS-DATAGRID"&&s!==t.intf;)s.nodeType!=="canvas-datagrid-tree"&&s.nodeType!=="canvas-datagrid-cell"&&(I-=s.scrollLeft,e-=s.scrollTop),s=s.parentNode;return{left:I-r.scrollLeft,top:e-r.scrollTop}},t.resizeEditInput=function(){if(t.input&&t.input.editCell){var s=t.canvas.getBoundingClientRect(),I=t.scrollOffset(t.intf),e=t.style.gridBorderCollapse==="collapse"?1:2,r=t.style.cellBorderWidth*e,c=t.getVisibleCellByIndex(t.input.editCell.columnIndex,t.input.editCell.rowIndex)||{x:-100,y:-100,height:0,width:0};if(t.mobile){t.input.style.left="0",t.input.style.top=t.height-t.style.mobileEditInputHeight-r-1+"px",t.input.style.height=t.style.mobileEditInputHeight+"px",t.input.style.width=t.width-r-1+"px";return}t.input.style.fixedLeft=s.left+c.x+t.canvasOffsetLeft-I.left,t.input.style.left=c.x+t.canvasOffsetLeft-I.left,t.input.style.top=c.y-t.style.cellBorderWidth+t.canvasOffsetTop-I.top,t.input.style.fixedTop=s.top+c.y-t.style.cellBorderWidth+t.canvasOffsetTop-I.top+c.height,t.input.style.height=c.height-r,t.input.style.width=c.width-t.style.cellPaddingLeft,t.clipElement(t.input)}},t.position=function(s,I){for(var e=0,r=0,c=s,o,n;s.offsetParent&&s.nodeName!=="CANVAS-DATAGRID";)e+=s.offsetLeft,r+=s.offsetTop,o=s.offsetHeight,n=s.offsetWidth,s=s.offsetParent;return I?{left:e,top:r,height:o,width:n}:(s=c,c=t.scrollOffset(s),{left:e+c.left,top:r+c.top,height:o,width:n})},t.getLayerPos=function(s){var I=t.canvas.getBoundingClientRect(),e={x:s.clientX-I.left,y:s.clientY-I.top};return t.isChildGrid&&(e.x-=t.canvasOffsetLeft,e.y-=t.canvasOffsetTop),{x:e.x,y:e.y,rect:I}},t.endEdit=function(s){var I=t.input.editCell,e=I.rowIndex,r=I.value;return s||(r=t.inputValue,I.data[I.header.name]=t.inputValue,t.inputValue=void 0),t.intf.focus(),t.dispatchEvent("endedit",{cell:I,value:t.input.value,aborted:s,input:t.input}),I.value=r,t.updateChanges(I),t.input=void 0,t.draw(!0),!0},t.beginEditAt=function(s,I,e){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!!t.attributes.editable){t.input&&t.endEdit();var c=t.getVisibleCellByIndex(s,I),o=t.getSchema();if(!(!c||!c.header||c.header.readOnly)){if(t.dispatchEvent("beforebeginedit",{cell:c,NativeEvent:e}))return!1;t.scrollIntoView(s,I),t.setActiveCell(s,I),t.input={style:{},editCell:{}};var n=t.singleRecordViewRowIndex;n===-1||n===void 0?c.dataType=c.header.dataType:c.dataType=c.data.dataType,t.updateChanges(c),!t.dispatchEvent("appendeditinput",{cell:c,input:t.input}),t.createInlineStyle(t.input,t.mobile?"canvas-datagrid-edit-mobile-input":"canvas-datagrid-edit-input"),t.input.style.position="absolute",t.input.editCell=c,t.resizeEditInput(),t.input.style.zIndex=t.style.editCellZIndex,t.input.style.fontSize=parseInt(t.style.editCellFontSize,10)*t.scale+"px",t.input.style.display="none";var i=t.input.style,a=i.left,l=i.top,g=i.height,A=i.width,p=i.zIndex,S=i.fixedLeft,v=i.fixedTop,h=[null,void 0].indexOf(c.value)!==-1,E=h||r;e&&e.key!=="Enter"&&(c.value=E?e.key:c.value),t.inputValue=c.value,t.dispatchEvent("beginedit",{cell:c,input:t.input,style:{position:"absolute",left:a,top:l,height:g,width:A,zIndex:p,fixedLeft:S,fixedTop:v}})}}},t.createInlineStyle=function(s,I){var e={"canvas-datagrid-context-menu-filter-input":{height:"19px",verticalAlign:"bottom",marginLeft:"2px",padding:"0",background:t.style.contextFilterInputBackground,color:t.style.contextFilterInputColor,border:t.style.contextFilterInputBorder,borderRadius:t.style.contextFilterInputBorderRadius,lineHeight:"normal",fontWeight:"normal",fontFamily:t.style.contextFilterInputFontFamily,fontSize:t.style.contextFilterInputFontSize},"canvas-datagrid-context-menu-filter-button":{height:"19px",verticalAlign:"bottom",marginLeft:"2px",padding:"0",background:t.style.contextMenuBackground,color:t.style.contextMenuColor,border:t.style.contextFilterButtonBorder,borderRadius:t.style.contextFilterButtonBorderRadius,lineHeight:"normal",fontWeight:"normal",fontFamily:t.style.contextMenuFilterButtonFontFamily,fontSize:t.style.contextMenuFilterButtonFontSize},"canvas-datagrid-context-child-arrow":{cssFloat:"right",color:t.style.childContextMenuArrowColor,fontSize:t.style.contextMenuChildArrowFontSize,fontFamily:t.style.contextMenuFontFamily,verticalAlign:"middle"},"canvas-datagrid-autocomplete":{fontFamily:t.style.contextMenuFontFamily,fontSize:t.style.contextMenuFontSize,background:t.style.contextMenuBackground,color:t.style.contextMenuColor,border:t.style.contextMenuBorder,padding:t.style.contextMenuPadding,borderRadius:t.style.contextMenuBorderRadius,opacity:t.style.contextMenuOpacity,position:"absolute",zIndex:9999,overflow:"hidden"},"canvas-datagrid-autocomplete-item":{background:t.style.contextMenuBackground,color:t.style.contextMenuColor},"canvas-datagrid-autocomplete-item:hover":{background:t.style.contextMenuHoverBackground,color:t.style.contextMenuHoverColor},"canvas-datagrid-canvas":{position:"absolute",zIndex:"-1"},"canvas-datagrid":{display:"block"},"canvas-datagrid-control-input":{position:"fixed",top:"-5px",left:"-5px",border:"none",opacity:"0",cursor:"pointer",width:"1px",height:"1px",lineHeight:"normal",fontWeight:"normal",fontFamily:t.style.contextMenuFontFamily,fontSize:t.style.contextMenuFontSize},"canvas-datagrid-edit-mobile-input":{boxSizing:"content-box",outline:"none",margin:"0",padding:"0 0 0 0",lineHeight:"normal",fontWeight:"normal",fontFamily:t.style.mobileEditFontFamily,fontSize:t.style.mobileEditFontSize,border:t.style.editCellBorder,color:t.style.editCellColor,background:t.style.editCellBackgroundColor,appearance:"none",webkitAppearance:"none",mozAppearance:"none",borderRadius:"0"},"canvas-datagrid-edit-input":{boxSizing:"content-box",outline:"none",margin:"0",padding:"0 0 0 "+t.style.editCellPaddingLeft+"px",lineHeight:"normal",fontWeight:"normal",fontFamily:t.style.editCellFontFamily,fontSize:t.style.editCellFontSize,boxShadow:t.style.editCellBoxShadow,border:t.style.editCellBorder,color:t.style.editCellColor,background:t.style.editCellBackgroundColor,appearance:"none",webkitAppearance:"none",mozAppearance:"none",borderRadius:"0"},"canvas-datagrid-context-menu-item-mobile":{lineHeight:"normal",fontWeight:"normal",fontFamily:t.style.contextMenuFontFamily,fontSize:t.style.contextMenuFontSize,color:"inherit",background:"inherit",margin:t.style.contextMenuItemMargin,borderRadius:t.style.contextMenuItemBorderRadius,verticalAlign:"middle"},"canvas-datagrid-context-menu-item":{lineHeight:"normal",fontWeight:"normal",fontFamily:t.style.contextMenuFontFamily,fontSize:t.style.contextMenuFontSize,color:"inherit",background:"inherit",margin:t.style.contextMenuItemMargin,borderRadius:t.style.contextMenuItemBorderRadius,verticalAlign:"middle"},"canvas-datagrid-context-menu-item:hover":{background:t.style.contextMenuHoverBackground,color:t.style.contextMenuHoverColor},"canvas-datagrid-context-menu-label":{margin:t.style.contextMenuLabelMargin,display:t.style.contextMenuLabelDisplay,minWidth:t.style.contextMenuLabelMinWidth,maxWidth:t.style.contextMenuLabelMaxWidth},"canvas-datagrid-context-menu-mobile":{lineHeight:"normal",fontWeight:"normal",fontFamily:t.style.contextMenuFontFamily,fontSize:t.style.contextMenuFontSize,background:t.style.contextMenuBackground,color:t.style.contextMenuColor,border:t.style.contextMenuBorder,padding:t.style.contextMenuPadding,borderRadius:t.style.contextMenuBorderRadius,opacity:t.style.contextMenuOpacity,overflow:"hidden",whiteSpace:"nowrap"},"canvas-datagrid-context-menu":{},"canvas-datagrid-invalid-search-regExp":{background:t.style.contextMenuFilterInvalidExpresion}};e[I]&&Object.keys(e[I]).map(function(r){s.style[r]=e[I][r]})},t.appendTo=function(s){t.parentNode=s,t.setDom()},t.setDom=function(){t.isChildGrid?(t.parentGrid=t.parentNode.parentGrid,t.ctx=t.parentGrid.context,t.canvas=t.parentGrid.canvas,t.controlInput=t.parentGrid.controlInput,t.eventParent=t.canvas):(t.controlInput=t.controlInput||document.createElement("input"),t.controlInput.onblur=t.intf.blur,t.createInlineStyle(t.controlInput,"canvas-datagrid-control-input"),t.isChildGrid=!1,t.parentDOMNode=t.parentNode,t.parentIsCanvas=/^canvas$/i.test(t.parentDOMNode.tagName),t.parentIsCanvas?t.canvas=t.parentDOMNode:(t.canvas=document.createElement("canvas"),t.parentDOMNode.appendChild(t.canvas)),document.body.appendChild(t.controlInput),t.createInlineStyle(t.canvas,"canvas-datagrid"),t.ctx=t.canvas.getContext("2d"),t.ctx.textBaseline="alphabetic",t.eventParent=t.canvas),t.parentNodeStyle=t.canvas.style,t.controlInput.setAttribute("readonly",!0),t.controlInput.addEventListener("blur",function(s){s.target!==t.canvas&&(t.hasFocus=!1)}),t.eventParent.addEventListener("scroll",t.resize,!1),t.eventParent.addEventListener("touchstart",t.touchstart,!1),t.eventParent.addEventListener("mouseup",t.mouseup,!1),t.eventParent.addEventListener("mousedown",t.mousedown,!1),t.eventParent.addEventListener("dblclick",t.dblclick,!1),t.eventParent.addEventListener("click",t.click,!1),t.eventParent.addEventListener("mousemove",t.mousemove),t[t.isChildGrid?"parentGrid":"eventParent"].addEventListener("wheel",t.scrollWheel,!1),t.canvas.addEventListener("contextmenu",t.contextmenuEvent,!1),t.controlInput.addEventListener("copy",t.copy),t.controlInput.addEventListener("cut",t.cut),t.controlInput.addEventListener("paste",t.paste),t.controlInput.addEventListener("keypress",t.keypress,!1),t.controlInput.addEventListener("keyup",t.keyup,!1),t.controlInput.addEventListener("keydown",t.keydown,!1),window.addEventListener("resize",t.resize)}}},73861:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return _}});function _(t){var s=[],I=!1,e=0,r=300,c=[],o=0,n=[],i=[];t.htmlImageCache={};function a(d,C,w,W){w=w||1;var O=[],V;for(V=d;V<=C;V+=w)O[V]=W===void 0?V:typeof W=="function"?W(V):W;return O}function l(d,C,w,W,O,V,f,ct,U){var bt=d/O.length,at=C/f;w+=t.canvasOffsetLeft,W+=t.canvasOffsetTop,t.ctx.beginPath(),t.ctx.moveTo(w,W+C),O.forEach(function(X){var ut=V===void 0?X:X[V],wt,Et;U&&(ut=Math.abs(ut)),wt=w+bt,Et=W+C-ut*at,t.ctx.lineTo(wt,Et),w+=bt}),t.ctx.moveTo(w+d,W+C),t.ctx.strokeStyle=ct,t.ctx.stroke()}function g(){var d=!0;Object.keys(t.htmlImageCache).forEach(function(C){var w;!((w=t.htmlImageCache[C].img)!==null&&w!==void 0&&w.complete)&&C!=="sort"&&(d=!1)}),d&&!I&&(I=!0,t.draw())}function A(d){var C,w=d.innerHTML||d.formattedValue,W=w.toString()+d.rowIndex.toString()+d.columnIndex.toString(),O=Math.round(d.x+t.canvasOffsetLeft),V=Math.round(d.y+t.canvasOffsetTop);if(t.htmlImageCache[W])if(C=t.htmlImageCache[W].img,t.htmlImageCache[W].height!==d.height||t.htmlImageCache[W].width!==d.width)t.htmlImageCache[W]=void 0;else{if(!C.complete)return;try{return t.ctx.drawImage(C,O,V)}catch(f){}}else I=!1;C=new Image(d.width,d.height),t.htmlImageCache[W]={img:C,width:d.width,height:d.height},C.onload=function(){t.ctx.drawImage(C,O,V),g()},C.src="data:image/svg+xml;base64,"+btoa('<svg xmlns="http://www.w3.org/2000/svg" width="'+d.width+'" height="'+d.height+`">
<foreignObject class="node" x="0" y="0" width="100%" height="100%">
<body xmlns="http://www.w3.org/1999/xhtml" style="margin:0;padding:0;">
`+w+`
</body></foreignObject>
</svg>
`)}function p(d){if(!!d.value)for(var C=Math.min(d.height-6,1024),w=Math.min(d.height-6,1024),W=d.fontHeight*d.lineHeight,O=0;O<d.value.length;O++){var V=d.value[O],f=d.paddingLeft,ct=Math.max(d.height*.5-w*.5,0),U,bt=V.id,at=Math.round(d.x+t.canvasOffsetLeft)+f+O*C+O*5,X=Math.round(d.y+t.canvasOffsetTop)+ct;if(t.htmlImageCache[bt]){if(U=t.htmlImageCache[bt].img,!U.complete)continue;try{t.ctx.drawImage(U,at,X,C,w)}catch(wt){}continue}else I=!1;U=new Image(d.width,d.height),t.htmlImageCache[bt]={img:U,x:at,y:X,width:C,height:w},U.onload=function(){t.ctx.drawImage(U,at,X,C,w),g()};var ut=V.originalName.substr(V.originalName.lastIndexOf(".")+1).toLowerCase();ut==="xls"||ut==="xlsx"?U.src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAAAXNSR0IArs4c6QAADQpJREFUeF7tnQl0FEUax//Vk4RTCIiAAhIuJxF6QOXJISpgCCAoiiLLIgtiiIicCSCngiBLAJHDhSdiBCOQtwqyPliVgHJ4REUUFATWCERdEFfECxGSrn01QyIJ013VPTM9nXTVe/N6Mvnqq6//36/6rK4mcGG5Ie2G2P/VrKOSIs0HSn1UUVRCqQqgnrEcdObRhVtnVCTJSEVamWDr0nh8ShOWaEKIjwIsyeyTaG29JQDWdLOhVsLYzvHEE+fTKGW92QdFUUFpEoD48DUvAQifliF4ajouWS0CVNarL+rRTUNwKVhVAiAoVHjMGozq3DAmLs7fowmoqoF4CagXIJeFpwWzXiQAZhUTsr9qxh1V437+3QeNHZCRVgTEC4B9Ggs5sM1IAhCy1E3G9PAiRlMpRSsQ6gX1J5p9qobsPOIOJADCEjcbn1L3vEZVBaQlIZoXlHhBiJdS2lDYieMMJQCXpKT4nFop0q7VCPESSv2JBlsClRyXw5ACcjkA7JwaGk0iLMGaluhPdOCgrH5Iupabyi4BgJ1TQ4lJBEiivyf7E43A/prAU27yFfZAKyAATdNvu4ZCSQzso6lXA7wkcFB2Rdj1K/cOKyAACendaLnPi1yBoApQoJBQ/AiCHxFYFoCSfE0pyi54atsBVolIANxKT2BrJgFwa/6BwzHnY3wSAPcCAErI/RIAEwCMSRlkwtqZpou3ZF8c2CoJgIk8MQDGdi+/ECx6MxtlAMiTALgbgIMSAFcDQE9IAFwNAM5KANwNgLwQZCL/qIAHgRIACYC8FyDMgNwCCEtVMQ3bN2ODkMXLmO6D0L5Z60sqDMt6XNyJgGVKq47od2N3rmWQ6wByF8BVLQSDdSPmBwUg8dHe+KPwXAieS1ed1z9DAhA2NcPoaN2IBQi21ZAAhFFkJ7uSWwAnZ8eG2CQANojs5CYkAE7Ojg2xSQBsENnJTUgAnJwdG2KTANggspObkAA4OTsWYgvXlcAhz02x0Lp+lV6tb5UXgsKqqI4zeS/ADpUd3IYEwIHJURQFVeMqo2pcFf+yeuWqOH3mF3xz6kTYo5UAhF1ScYc1qlTD8w/OLknyZZWrgf3mUUo/q7ph91ZkrJsn7tiEpQSAI1aH5m1Qs0p1f2Jq+JeBT53LaqFO9fiS5Z5jBzB05XQT0kN4NE5y5oPIP/m1Kd+ixhIAAaXef2wt6tesw7W8ZuLtOF9UyLUrNvjsyY3+zbtR2bx3J0a+OFvYp1lDBkD75mbGBJCgdwPz8veZbZprL3KGYst4gOFd++PRXg9yA05fm4lXP97GtWMGyS074LmhM7m2vRY+jAPf5nPt7DLQux3cJCMlrCGIbplsAaBSbBwOzHkN7ODMqOw5egD3LB0rJMTqtDm4xdvW0Hbr/jwMy3pMyJ9dRq4EgIk7vc9wDL2lL1fnbvNS8eV3BYZ2LRs0x6b0ZVxffZeMwSfHvuDa2WngWgAa1a6PnVNf5Gq98I3VWJq7xtBu6p1pSL31XkObXYc/xt+encxtz24D1wLAhF46aAp6t+lsqDk7WmdH7Xrl8urx2D55Fffgb8CyCcjL32t3frntuRqA6xOuxfpRi7gipWY9hm3784Lasd0I250YlQ+/+gz9/5HBbScaBq4GgAmeM2IB2nGGUq//KBfjc+YHzc/GMUvR+mo2X5V+YTdWdhzcHY38ctt0PQA91E5YPsT4yPynM7+gy9wH8ONvP5cStGOLNlgz3PiK3t6CQ7hr8ShuIqJl4HoAmPBvTcpCkyuMZ4fNWDcfG3bnlsrTjLsfweBOfQxzl/bCDOR+/l608sttVwIAYNBNd+KJviMNxdr06Q6Myn6yxCbWE4MdU1bjynj9qQoPHj+Cngse4iYhmgYSgAvq8y7jnvnjd7Sd0R+/nzvrr9Hn+q5YNHCSYe4YMAwcO4voFTc7YzLTli1XAoMFlNFzCEYm/9Uw1gk5C/DKR1v8NssGT0dP38269ke+/wZd5w41s+5hsZUAWJSR3RHcO3uDYe03P3sXw1fNRL2al+PdaS9dcpv34srj183H+jLHDBZDM1VNAmBKrtLGmfel4752PXQ9nCs8j6RJd2DIzX0wvc/Dunb/PX0SN826P4RIrFeVAFjXDs3rXY3ciSs5+/U5fgBuSGipazf1lcVY+/7mECKxXlUCYF07f82s1NnoknSjrpfX972Dnr5Ouv//4dfTaPv4fSFGYb26BMC6dv6aHZu3wZqH9S/uUEpBiP67LGduXIZVuzaGGIV91fWeC6jQ4wF48rJbu+wWr9ny69kzUKfeZbZaVO3ldYAg8ouc4wfL2txNK/Hs2/+MakLNNi63ADqKffB4DurWqC2sJxs7yMYQlrciAdDJ2LDO92LKHWnC+Xz6jRexJPclYXunGEoADDJx5KnAVT+RIjJySMSP3TYSAB3FzZ5OsRm12MRK5a1IAHQyZqb3F7t48rUVWLnjlXLFgAQgSLrM9v5iF+x5v+um3yMBCEHTqN0NvDhmo97/nxPH0KK+/gvDp61fgjXvbSo3EMgtQJlU8Xo/I9TolSzHT3+PjrMGRg0As+8MYo+RBZsqtsxrW3TXh5p4o6PIq2yivgXg7ftT5g3DlonPGSaYPfnLngCORuEBHI2YzLQZVQB44m35/D089MIM7JqajYa16+mu11cnv8ZtBs8SmBHErC1vHcz6s9s+qgDwev/YNXPxrz1vYXLvYUjr0s9QmxGrZ+H1fbvs1k/4EXXbAxNsMGoA8HrON6e+w22ZQ8EGhXRNaofnU2cZrtL+b79E74UjBFc7fGa89QhfS5HxFDUAeL1/xdsv4++bAvt+NvPH9imrULtaTUMVHlg5Ddu/+DAySul4lQBYkFtEtLsXj8anBQdLvM++dzQGdjC+8rf7yOfo90y6hYisVzF/FtA66AQRrjoL4PX+dw7vwaBnSw8B73TN9ch+aC43U059KLQ4cNePBxDp/Xpj/DaMXozrGicZQrDz0McYvMJ5j4VLAC4owOv9p377Cd0yU8GWZYvoLWP2bCB7RtCJxdVbAJHez0b3si1AsNKgVl28MWEFqlcynhiq+HkCCQD/pda2ngXwej9L2OAVU7DzkP6j3XP6jcWA9vyRQD3mp+HQiaOOY8C1WwCR3r/v68Pos8j4oVE27+Bag1HExRlnF5DYhSSnFdcCINL75/87C8u25XBzxoaRs+HkvHLrnMEo+OE4z8zW/7sSAJHez8b/ex/tJTRRZN+2yXhqwERu4nLyXsfkl5/m2tlp4EoARHp/2fkAeEl5Z1o2GtTSv0FUXP/GmX/B9z+f4rmz7f+uA0Ck9zP12b6fHQOIlkeSB2B8zwe45i/sehVPbFzOtbNqUHo6Vv0nmIr9B14de+nUsuwClvVy6SCBds1aG46jKG4r4mcBIr0//2QBkjNTTa2/QgjyF7wpVMc39W78cvY3IVuzRqKAm/Vrl31EAfAoCmI9sWDTu8TGxCDuou/s9zj/74H/f2BhsuTZ94zGwI78kcHL38rBvM1ZEdFUAhARWcWcXlWrLtSGLRBftQZqVbvwKfleM/Dbhb9bTLwdhSZmIheLQHzKelF/dttFdAtg98pEoz25BYiG6g5qUwLgoGREI5TACyNal2m6+Ki8+KyA/f3n92CjgoO/MMLEEOAgKx+snbJmchdgMzXyuQCbBXdacxIAp2XE5ngkADYL7rTmJABOy4jN8UgAbBbcac1JAJyWEZvjkQDYLLjTmpMAOC0jNscjAbBZcKc1JwFwWkZCjMf8u4MRdIKICv3u4BA1dnR1eTPI0emJfHASgMhr7OgWJACOTk/kg6uAAJwhCendQrsRHXndHdNCBQSgQAJgAq8KCMAnEgB3A5ArAXAzAAQ5EgATAIiMuzPhLiqmefl7S9qlwDMSgKikwSmN0pkSAKfkwv449secj+koAbBf+Oi3SOkh6kHGsQVbN0sAzKSDYrsZcwfZngLBl6A4AtDjShy2f5W51T8zlwTATJYoth99OreLmSpOtyVNxvTwwlPkBSVeEOoF4NVAEwlIHacHb3t8FRGAYCI2HNe9tofQREI1LwjxAgwMtvR/V2wX3ikNugUAPb2bjeveXANNAtG8GoiXlICBuk7JUUTjcDsAwbcWHarEooZKFZoESr0g8IIiERTsuyeiCbHbuQRAXPGG47o2iEGsSol2LfGDUbILqS/uxWGWEoDQE9J0XLJaBKgKSEsGhQbqJQA7+IwN3XuEPUgAIiPw1ZN61VL+OOcjoColpNWF3QeD4srItGjRqwTAonAWqzWa0LOZUljoI4AKglb+3QjbnQCVLLoMrZoEIDT9wlE7YUjnysrlHrVI8/i3GAjAwaBoEA7/hj4kABGX2HIDjUZ2u8pTiapEU3waqEoI2AyNDIzKlp2WrSgBCJuUtjlqND6llYdSFRr1ERCVBsBoZCkACYAl2RxXKWFs53jiifNplKqEUh8URQVbAtXctgv4Pya2EmMqZgxeAAAAAElFTkSuQmCC":ut==="doc"||ut==="docx"?U.src="data:image/png;base64,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":ut==="pdf"?U.src="data:image/png;base64,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":["jpg","jpeg","png","gif","bmp","wbmp"].includes(ut)?U.src="/api/public/preview/".concat(V.id,"@thumbnail"):ut==="zip"?U.src="data:image/png;base64,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":ut==="ppt"?U.src="data:image/png;base64,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":ut==="csv"?U.src="data:image/png;base64,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":ut==="video"?U.src="data:image/png;base64,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":ut==="text"?U.src="data:image/png;base64,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":U.src="data:image/png;base64,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"}}function S(d){var C="";d==="sort"?C=`<?xml version="1.0" encoding="UTF-8"?>
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1"  width="24" height="24" viewBox="0 0 24 24">
       <path fill="#848484" d="M12,6L7,11H17L12,6M7,13L12,18L17,13H7Z" />
    </svg>`:d==="desc"?C=`<?xml version="1.0" encoding="UTF-8"?>
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1"  width="24" height="24" viewBox="0 0 24 24">
         <path fill="#848484" d="M7,10L12,15L17,10H7Z" />
      </svg>`:d==="asc"&&(C=`<?xml version="1.0" encoding="UTF-8"?>
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1"  width="24" height="24" viewBox="0 0 24 24">
         <path fill="#848484" d="M7,15L12,10L17,15H7Z" />
      </svg>`);var w=btoa(C),W="data:image/svg+xml;base64,";return W+w}function v(d,C,w){var W=new Image(20,20);t.htmlImageCache[d]?t.ctx.drawImage(t.htmlImageCache[d],C,w,18,18):(W.onload=function(){t.ctx.drawImage(W,C,w,18,18)},W.src=S(d),t.htmlImageCache[d]=W)}function h(d,C,w){var W=t.style.columnHeaderOrderByArrowMarginTop*t.scale,O=t.style.columnHeaderOrderByArrowMarginLeft*t.scale,V=t.style.columnHeaderOrderByArrowMarginRight*t.scale,f=t.style.columnHeaderOrderByArrowWidth*t.scale,ct=t.style.columnHeaderOrderByArrowHeight*t.scale;C+=t.canvasOffsetTop,C=C+W;var U=t.orderings.columns.find(function(at){return at.orderBy===w})||{orderDirection:void 0},bt=U.orderDirection;return v(bt==="asc"?"asc":bt==="desc"?"desc":"sort",d,C),O+f+V}function E(d,C,w){var W=t.style.treeArrowMarginTop*t.scale,O=t.style.treeArrowMarginRight*t.scale,V=t.style.treeArrowMarginLeft*t.scale,f=t.style.treeArrowWidth*t.scale,ct=t.style.treeArrowHeight*t.scale;return C+=t.canvasOffsetLeft,w+=t.canvasOffsetTop,t.ctx.fillStyle=t.style.treeArrowColor,t.ctx.strokeStyle=t.style.treeArrowBorderColor,t.ctx.beginPath(),C=C+V,w=w+W,t.openChildren[d.rowIndex]?(t.ctx.moveTo(C,w),t.ctx.lineTo(C+f,w),t.ctx.lineTo(C+f*.5,w+ct),t.ctx.moveTo(C,w)):(t.ctx.lineTo(C,w),t.ctx.lineTo(C+ct,w+f*.5),t.ctx.lineTo(C,w+f),t.ctx.lineTo(C,w)),t.ctx.stroke(),t.ctx.fill(),V+f+O}function H(d,C,w,W,O){d+=t.canvasOffsetLeft,C+=t.canvasOffsetTop;var V=d+w,f=C+W;t.ctx.beginPath(),t.ctx.moveTo(d+O,C),t.ctx.lineTo(V-O,C),t.ctx.quadraticCurveTo(V,C,V,C+O),t.ctx.lineTo(V,C+W-O),t.ctx.quadraticCurveTo(V,f,V-O,f),t.ctx.lineTo(d+O,f),t.ctx.quadraticCurveTo(d,f,d,f-O),t.ctx.lineTo(d,C+O),t.ctx.quadraticCurveTo(d,C,d+O,C)}function R(d,C,w,W){d+=t.canvasOffsetLeft,C+=t.canvasOffsetTop,t.ctx.fillRect(d,C,w,W)}function K(d,C,w,W){d+=t.canvasOffsetLeft,C+=t.canvasOffsetTop,t.ctx.strokeRect(d,C,w,W)}function N(d,C,w){C+=t.canvasOffsetLeft,w+=t.canvasOffsetTop,t.ctx.fillText(d,C,w)}function $(d,C,w){d+=t.canvasOffsetLeft,C+=t.canvasOffsetTop,t.ctx.beginPath(),t.ctx.arc(d,C,w,0,2*Math.PI),t.ctx.fill()}function gt(d,C,w){d+=t.canvasOffsetLeft,C+=t.canvasOffsetTop,t.ctx.beginPath(),t.ctx.arc(d,C,w,0,2*Math.PI),t.ctx.stroke()}function ht(d){t.ctx.beginPath(),d===0&&(t.ctx.moveTo(t.lastFrozenColumnPixel,t.lastFrozenRowPixel),t.ctx.lineTo(t.lastFrozenColumnPixel,t.height),t.ctx.lineTo(t.width,t.height),t.ctx.lineTo(t.width,t.lastFrozenRowPixel)),d===1&&(t.ctx.moveTo(0,t.lastFrozenRowPixel),t.ctx.lineTo(0,t.height),t.ctx.lineTo(t.width,t.height),t.ctx.lineTo(t.width,t.lastFrozenRowPixel)),d===2&&(t.ctx.moveTo(t.lastFrozenColumnPixel,0),t.ctx.lineTo(t.width,0),t.ctx.lineTo(t.width,t.height),t.ctx.lineTo(t.lastFrozenColumnPixel,t.height)),t.ctx.clip()}function T(d,C,w){if(t.style.selectionHandleType==="circle")return $(d,C,w*.5);R(d-w*.5,C-w*.5,w,w)}function G(d,C,w){if(t.style.selectionHandleType==="circle")return gt(d,C,w*.5);K(d-w*.5,C-w*.5,w,w)}function nt(d,C){var w=t.style.selectionHandleSize,W={tr:function(){T(d.x+d.width,d.y,w),G(d.x+d.width,d.y,w)},br:function(){T(d.x+d.width,d.y+d.height,w),G(d.x+d.width,d.y+d.height,w)},tl:function(){T(d.x,d.y,w),G(d.x,d.y,w)},bl:function(){T(d.x,d.y+d.height,w),G(d.x,d.y+d.height,w)}};W[C]()}function At(d,C){t.ctx.beginPath();var w={t:function(){t.ctx.moveTo(d.x+t.canvasOffsetLeft,d.y+t.canvasOffsetTop),t.ctx.lineTo(d.x+t.canvasOffsetLeft+d.width,d.y+t.canvasOffsetTop)},r:function(){t.ctx.moveTo(d.x+t.canvasOffsetLeft+d.width,d.y+t.canvasOffsetTop),t.ctx.lineTo(d.x+t.canvasOffsetLeft+d.width,d.y+t.canvasOffsetTop+d.height)},b:function(){t.ctx.moveTo(d.x+t.canvasOffsetLeft,d.y+t.canvasOffsetTop+d.height),t.ctx.lineTo(d.x+t.canvasOffsetLeft+d.width,d.y+t.canvasOffsetTop+d.height)},l:function(){t.ctx.moveTo(d.x+t.canvasOffsetLeft,d.y+t.canvasOffsetTop),t.ctx.lineTo(d.x+t.canvasOffsetLeft,d.y+t.canvasOffsetTop+d.height)}};w[C](),t.ctx.stroke()}function Lt(d,C){var w,W=0;return t.ellipsisCache[d]&&t.ellipsisCache[d][C]?t.ellipsisCache[d][C]:(W=t.ctx.measureText(d).width,t.ellipsisCache[d]=t.ellipsisCache[d]||{},w={value:d,width:W},t.ellipsisCache[d][C]=w,w)}function zt(d,C){if(!d.formattedValue)return{lines:[{width:0,value:""}],width:0,height:d.calculatedLineHeight};var w=0,W=`
`,O,V,f=d.formattedValue.split(C),ct=d.calculatedLineHeight,U=[],bt=[],at=t.style.cellWhiteSpace!=="nowrap",X=t.attributes.autoResizeRows&&at,ut,wt=t.attributes.ellipsisText,Et,vt,lt,Zt,Ct,yt={width:0,value:""},Xt=at?d.paddedHeight:d.calculatedLineHeight;for(U.push(yt),ut=t.ctx.measureText(" "+wt).width,O=0;O<f.length;O+=1){V=f[O];var Ft=t.ctx.measureText(V+C);if(yt.width+Ft.width+ut<d.paddedWidth){yt.value+=V+C,yt.width+=Ft.width;continue}if(/\w-\w/.test(V)&&d.paddedWidth<Ft.width){f.splice(O,1,V.split("-")[0]+"-",V.split("-")[1]),O-=1;continue}if(yt={width:Ft.width,value:V+C},O===0&&(U=[],U.push(yt)),ct+=d.calculatedLineHeight,ct>Xt&&!X){if(U.length===0||(Et=1,Ct=U[U.length-1],Ct.width<d.paddedWidth&&f.length===1))break;lt=Ct.value+V,vt=t.ctx.measureText(lt+wt).width;var Yt=lt;if(vt>d.paddedWidth)for(var jt=parseInt(lt.length/2),Vt=-1;jt>0;)lt=Yt.substr(0,jt*Vt+lt.length),vt=t.ctx.measureText(lt+wt).width,Vt=vt>d.paddedWidth?-1:1,jt=parseInt(jt/2);lt=lt+(Yt.length!=lt.length?wt:""),Ct.value=lt,Ct.width=vt;break}O>0&&U.push(yt)}return{lines:U,width:w,height:d.calculatedLineHeight*U.length}}function It(d){var C=d.text.lines.length,w=d.fontHeight*d.lineHeight,W,O,V=t.style.cellWhiteSpace!=="nowrap",f=0;for(W=0;W<d.text.lines.length;W+=1){O=d.text.lines[W];var ct=Math.max((d.height-(V?d.text.height:d.calculatedLineHeight))*.5,0)+w,U=d.paddingLeft+d.treeArrowWidth;d.horizontalAlignment==="right"?U=d.paddingLeft+d.paddedWidth-O.width:d.horizontalAlignment==="center"&&(U=d.paddingLeft+(d.paddedWidth+d.paddingRight)/2-O.width/2),d.verticalAlignment==="top"?ct=d.calculatedLineHeight:d.verticalAlignment==="bottom"&&(ct=d.height-d.paddingBottom-d.text.height),O.height=w+d.lineSpacing,O.offsetLeft=U,O.offsetTop=ct,O.x=d.x+U,O.y=d.y+f+ct,f+=O.height,N(O.value,O.x,O.y)}return t.attributes.debug&&d.active&&requestAnimationFrame(function(){t.ctx.font=t.style.debugFont,t.ctx.fillStyle=t.style.debugColor,N(JSON.stringify({x:d.x,y:d.y,h:d.height,w:d.width,pw:d.paddedWidth,idx:d.columnIndex,idx_ord:d.sortColumnIndex},null,"	"),d.x+14,d.y+14),N(JSON.stringify(d.text.lines.map(function(bt){return{w:bt.width,v:bt.value.length}}),null,"	"),d.x+14,d.y+30)}),O}function q(){var d=0,C=t.getSchema(),w=0,W=Math.min(t.frozenColumn,C.length),O;for(o=0;w<W;)O=C[w],O.hidden?o+=1:d+=t.getColummnWidth(w),w+=1;return d}t.draw=function(d){if(t.dispatchEvent("beforedraw",{})||!t.isChildGrid&&(!t.height||!t.width))return;if(t.isChildGrid&&d){requestAnimationFrame(t.parentGrid.draw);return}if(t.intf.visible===!1)return;var C,w,W,O,V,f,ct,U,bt,at,X,ut,wt,Et,vt,lt,Zt,Ct,yt=t.data||[],Xt=t.style.gridBorderCollapse==="collapse",Ft=[],Yt=[],jt=[],Vt=[],ae=yt.length,ee=t.currentCell||{},re=t.getColumnHeaderCellHeight(),Nt=t.getRowHeaderCellWidth(),Pt=t.style.cellHeight;e+=1,W=performance.now(),t.visibleRowHeights=[],yt.length>t.orders.rows.length&&t.createRowOrders();function ce(){var u,m=t.scrollBox.entities,M=t.style.scrollBarBoxMargin*2;t.ctx.strokeStyle=t.style.scrollBarBorderColor,t.ctx.lineWidth=t.style.scrollBarBorderWidth,m.horizontalBox.x=Nt+t.style.scrollBarBoxMargin+(m.horizontalBar.width-t.scrollBox.scrollBoxWidth)*(t.scrollBox.scrollLeft/t.scrollBox.scrollWidth),m.verticalBox.y=re+t.style.scrollBarBoxMargin+(m.verticalBar.height-t.scrollBox.scrollBoxHeight)*(t.scrollBox.scrollTop/t.scrollBox.scrollHeight),t.scrollBox.horizontalBarVisible&&(t.ctx.fillStyle=t.style.scrollBarBackgroundColor,R(m.horizontalBar.x,m.horizontalBar.y,m.horizontalBar.width+M,m.horizontalBar.height),K(m.horizontalBar.x,m.horizontalBar.y,m.horizontalBar.width+M,m.horizontalBar.height),t.ctx.fillStyle=t.style.scrollBarBoxColor,t.scrollBox.horizontalBoxVisible&&(/horizontal/.test(ee.context)&&(t.ctx.fillStyle=t.style.scrollBarActiveColor),H(m.horizontalBox.x,m.horizontalBox.y,m.horizontalBox.width,m.horizontalBox.height,t.style.scrollBarBoxBorderRadius),t.ctx.stroke(),t.ctx.fill()),u=!0,t.visibleCells.unshift(m.horizontalBar),t.visibleCells.unshift(m.horizontalBox)),t.scrollBox.verticalBarVisible&&(t.ctx.fillStyle=t.style.scrollBarBackgroundColor,R(m.verticalBar.x,m.verticalBar.y,m.verticalBar.width,m.verticalBar.height+M),K(m.verticalBar.x,m.verticalBar.y,m.verticalBar.width,m.verticalBar.height+M),t.scrollBox.verticalBoxVisible&&(t.ctx.fillStyle=t.style.scrollBarBoxColor,/vertical/.test(ee.context)&&(t.ctx.fillStyle=t.style.scrollBarActiveColor),H(m.verticalBox.x,m.verticalBox.y,m.verticalBox.width,m.verticalBox.height,t.style.scrollBarBoxBorderRadius),t.ctx.stroke(),t.ctx.fill()),u=!0,t.visibleCells.unshift(m.verticalBar),t.visibleCells.unshift(m.verticalBox)),u&&(t.ctx.strokeStyle=t.style.scrollBarCornerBorderColor,t.ctx.fillStyle=t.style.scrollBarCornerBackgroundColor,H(m.corner.x,m.corner.y,m.corner.width,m.corner.height,0),t.ctx.stroke(),t.ctx.fill(),t.visibleCells.unshift(m.corner))}function de(u){(t.attributes.allowMovingSelection||t.mobile)&&(u.selectionBorderTop&&u.selectionBorderRight&&t.mobile&&(jt.push([u,"tr"]),u.selectionHandle="tr"),u.selectionBorderTop&&u.selectionBorderLeft&&t.mobile&&(jt.push([u,"tl"]),u.selectionHandle="tl"),u.selectionBorderBottom&&u.selectionBorderLeft&&t.mobile&&(jt.push([u,"bl"]),u.selectionHandle="bl"),u.selectionBorderBottom&&u.selectionBorderRight&&(t.attributes.selectionHandleBehavior!=="none"||t.mobile)&&(jt.push([u,"br"]),u.selectionHandle="br"))}function ne(u,m,M,z){z=z||{x:0,y:0},u.selectionBorder="",!u.isRowHeader&&t.selections[u.rowIndex+-z.y]&&t.selections[u.rowIndex+-z.y].indexOf(u.columnIndex+-z.x)!==-1&&((!t.selections[u.rowIndex-1+-z.y]||t.selections[u.rowIndex-1+-z.y].indexOf(u.columnIndex+-z.x)===-1||u.rowIndex===0)&&!u.isHeader&&(m.push([u,"t"]),u[M+"BorderTop"]=!0,u[M+"Border"]+="t"),(!t.selections[u.rowIndex+1+-z.y]||t.selections[u.rowIndex+1+-z.y].indexOf(u.columnIndex+-z.x)===-1)&&(m.push([u,"b"]),u[M+"BorderBottom"]=!0,u[M+"Border"]+="b"),(!t.selections[u.rowIndex+-z.y]||u.columnIndex===0||t.selections[u.rowIndex+-z.y].indexOf(u.columnIndex-1+-z.x)===-1)&&(m.push([u,"l"]),u[M+"BorderLeft"]=!0,u[M+"Border"]+="l"),(!t.selections[u.rowIndex+-z.y]||u.columnIndex===vt.length||t.selections[u.rowIndex+-z.y].indexOf(u.columnIndex+1+-z.x)===-1)&&(m.push([u,"r"]),u[M+"BorderRight"]=!0,u[M+"Border"]+="r"))}function Gt(u,m,M){return function(F,j,Y){if(F.hidden)return 0;var Q=F.style||"cell",tt,x,ot=/HeaderCell/.test(Q),mt=/cornerCell/.test(Q),st=Q==="rowHeaderCell",Ut=Q==="columnHeaderCell",Wt=t.style.cellWhiteSpace!=="nowrap",Ot=t.selections[m]&&t.selections[m].indexOf(Y)!==-1,dt=t.hovers.rowIndex===m&&t.hovers.columnIndex===Y,pt=t.activeCell.rowIndex===m&&t.activeCell.columnIndex===Y,Mt=Q==="columnHeaderCellCap",xt=u?u[F.name]:void 0,Dt=F.type==="canvas-datagrid",Bt=(t.orders.rows[t.activeCell.rowIndex]===m||t.orders.columns[t.activeCell.columnIndex]===j)&&(Y===-1||m===-1)?st?"activeRowHeaderCell":"activeColumnHeaderCell":!1,St,Qt=t.formatters[F.type||"string"],Z=0,ft=0,Rt=t.sizes.columns[j]||F.width,kt={value:xt,row:u,header:F};if(Mt&&(Rt=Et-X),Rt===void 0&&(Rt=t.style.cellWidth),Rt=Rt*t.scale,X+Rt+t.style.cellBorderWidth<0&&(X+=Rt+t.style.cellBorderWidth),pt&&Q!=="cornerCell"&&(Q="activeCell"),t.visibleRows.indexOf(M)===-1&&!ot&&t.visibleRows.push(M),St=t.dispatchEvent("formatcellvalue",kt),O=X,V=at,Q==="cornerCell"?(O=0,V=0):st?O=0:ot&&(V=0),x={type:Dt?"canvas-datagrid-cell":F.type,style:Q,nodeType:"canvas-datagrid-cell",x:O,y:V,fontHeight:(t.style[Q+"FontHeight"]||0)*t.scale,horizontalAlignment:t.style[Q+"HorizontalAlignment"],verticalAlignment:t.style[Q+"VerticalAlignment"],paddingLeft:(t.style[Q+"PaddingLeft"]||0)*t.scale,paddingTop:(t.style[Q+"PaddingTop"]||0)*t.scale,paddingRight:(t.style[Q+"PaddingRight"]||0)*t.scale,paddingBottom:(t.style[Q+"PaddingBottom"]||0)*t.scale,whiteSpace:t.style.cellWhiteSpace,lineHeight:t.style.cellLineHeight,lineSpacing:t.style.cellLineSpacing,offsetTop:t.canvasOffsetTop+V,offsetLeft:t.canvasOffsetLeft+O,scrollTop:t.scrollBox.scrollTop,scrollLeft:t.scrollBox.scrollLeft,active:pt||Bt,hovered:dt,selected:Ot,width:Rt,height:Pt,offsetWidth:Rt,offsetHeight:Pt,parentNode:t.intf.parentNode,offsetParent:t.intf.parentNode,data:u,isCorner:mt,isHeader:ot,isColumnHeader:Ut,isColumnHeaderCellCap:Mt,isRowHeader:st,rowOpen:ct,header:F,columnIndex:Y,rowIndex:m,sortColumnIndex:j,sortRowIndex:M,isGrid:Dt,isNormal:!Dt&&!mt&&!ot,gridId:(t.attributes.name||"")+M+":"+j,parentGrid:t.intf,innerHTML:"",activeHeader:Bt,value:ot&&!st?F.title||F.name:xt},x.calculatedLineHeight=x.fontHeight*x.lineHeight+x.lineSpacing,x.paddedWidth=x.width-x.paddingRight-x.paddingLeft,x.paddedHeight=x.height-x.paddingTop-x.paddingBottom,kt.cell=x,x.userHeight=x.isHeader?t.sizes.rows[-1]:U,x.userWidth=x.isHeader?t.sizes.columns.cornerCell:t.sizes.columns[j],t.visibleCells.unshift(x),!t.dispatchEvent("beforerendercell",kt)){if(t.ctx.fillStyle=t.style[Q+"BackgroundColor"],t.ctx.strokeStyle=t.style[Q+"BorderColor"],t.ctx.lineWidth=t.style[Q+"BorderWidth"],dt&&(t.ctx.fillStyle=t.style[Q+"HoverBackgroundColor"],t.ctx.strokeStyle=t.style[Q+"HoverBorderColor"]),Ot&&(t.ctx.fillStyle=t.style[Q+"SelectedBackgroundColor"],t.ctx.strokeStyle=t.style[Q+"SelectedBorderColor"]),Bt&&(t.ctx.fillStyle=t.style[Bt+"BackgroundColor"]),t.dispatchEvent("rendercell",kt),x.isNormal&&t.attributes.changesWithBackground){var qt=x.data._i,ie=x.header.name;t.singleRecordViewRowIndex>-1&&(ie=x.data.row),t.changes.update.find(function(Jt){return Jt.rowKey===qt&&Jt.columnName===ie})&&(t.ctx.fillStyle=t.style.cellUpdateBackgroundColor),t.changes.insert.indexOf(qt)!==-1&&(t.ctx.fillStyle=t.style.cellInsertBackgroundColor),t.changes.remove.indexOf(qt)!==-1&&(t.ctx.fillStyle=t.style.cellRemoveBackgroundColor)}x.isGrid&&(x.height!==U&&(x.height=U||t.style.cellHeightWithChildGrid,C=!0),x.width=t.sizes.columns[j]||t.style.cellWidthWithChildGrid),ct&&!x.isRowHeader&&(x.height=t.sizes.rows[M]||t.style.cellHeight),x.isGrid||(R(O,V,x.width,x.height),K(O,V,x.width,x.height),x.rowIndex===0&&(t.ctx.strokeStyle="white",At(x,"t")),x.columnIndex===0&&(t.ctx.strokeStyle="white",At(x,"l"))),t.ctx.save(),H(x.x,x.y,x.width,x.height,0),t.ctx.clip(),t.dispatchEvent("afterrendercell",kt),x.height!==Pt&&!(ct&&!x.isRowHeader)&&(t.sizes.rows[ot?-1:M]=x.height,C=!0),x.width!==Rt&&(t.sizes.columns[j]=x.width,C=!0),st&&t.attributes.tree&&(t.dispatchEvent("rendertreearrow",kt)||(ft=E(x,t.style[Q+"PaddingLeft"],V,0)));var _t;if(t.attributes.showRowNumbers&&st||!st)if(x.isGrid&&!t.dispatchEvent("beforerendercellgrid",kt)){if(!t.childGrids[x.gridId]){if(tt=t.cellGridAttributes,tt.name=t.attributes.saveAppearance?x.gridId:void 0,tt.component=!1,tt.parentNode=x,tt.data=xt,kt.cellGridAttributes=tt,t.dispatchEvent("beforecreatecellgrid",kt))return;t.childGrids[x.gridId]=t.createGrid(tt),t.sizes.rows[M]=t.sizes.rows[M]||t.style.cellGridHeight,C=!0}x.grid=t.childGrids[x.gridId],x.grid.parentNode=x,x.grid.visible=!0,x.grid.draw(),t.dispatchEvent("rendercellgrid",kt)}else x.isGrid||(t.childGrids[x.gridId]&&(t.childGrids[x.gridId].parentNode.offsetHeight=0),ot&&F.name&&F.style==="columnHeaderCell"&&t.attributes.allowSorting&&h(O+Rt-20,0,F.name),t.ctx.fillStyle=t.style[Q+"Color"],dt&&(t.ctx.fillStyle=t.style[Q+"HoverColor"]),Ot&&(t.ctx.fillStyle=t.style[Q+"SelectedColor"]),Bt&&(t.ctx.fillStyle=t.style[Bt+"Color"]),x.treeArrowWidth=ft,x.orderByArrowWidth=Z,St=St!==void 0?St:Qt?Qt(kt):"",St===void 0&&!Qt&&(St="",console.warn("canvas-datagrid: Unknown format "+F.type+" add a cellFormater")),x.formattedValue=(St!=null?St:"").toString(),t.columnFilters&&t.columnFilters[St]!==void 0&&ot&&(x.formattedValue=t.attributes.filterTextPrefix+St),t.ctx.font=t.style[Q+"FontHeight"]*t.scale+"px "+t.style[Q+"FontName"],t.dispatchEvent("formattext",kt)||(x.text=zt(x," ")),t.dispatchEvent("rendertext",kt)||(x.innerHTML||F.type==="html"||F.type==="checkbox"?A(x):F.type==="image"?p(x):_t=It(x),Wt&&x.text&&x.text.height>U&&(t.sizes.rows[ot?-1:M]=x.text.height,C=!0)));return st&&Bt&&(ft=E(x,Rt-25,_t.y||V,0)),pt&&(Ct=x),ne(x,Ft,"selection"),de(x),t.movingSelection&&ne(x,Yt,"move",t.moveOffset),t.ctx.restore(),X+=x.width+(Xt?0:t.style.cellBorderWidth),x.width}}}function ue(u,m,M){var z,F;t.attributes.showRowHeaders&&(X=0,F=m+1,w={rowHeaderCell:F},z={name:"rowHeaderCell",width:t.style.rowHeaderCellWidth||t.sizes.columns[-1],style:"rowHeaderCell",type:"string",data:F,index:-1},ct=t.openChildren[m],Gt(w,m,M)(z,-1,-1))}function le(){var u,m=vt.length,M,z,F,j,Y;function Q(tt,x){for(x=Math.min(x,m),z=tt;z<x&&(M=t.orders.columns[z],j=vt[M],!(!j.hidden&&(u={title:j.title,name:j.name,width:j.width||t.style.cellWidth,style:"columnHeaderCell",type:"string",dataType:j.type,index:z,order:M},F={columnHeaderCell:j.title||j.name},X+=Gt(F,-1,-1)(u,M,z),X>t.width+t.scrollBox.scrollLeft)));z+=1);}Vt.forEach(function(tt,x){at=tt[3],Pt=tt[4],x===t.frozenRow&&(t.ctx.save(),H(0,t.lastFrozenRowPixel,t.width,t.height-t.lastFrozenRowPixel,0),t.ctx.clip()),ue(tt[0],tt[1],tt[2])}),t.ctx.restore(),t.attributes.showColumnHeaders&&(X=-t.scrollBox.scrollLeft+t.scrollPixelLeft+t.style.columnHeaderCellBorderWidth,t.attributes.showRowHeaders&&(X+=Nt),at=0,Pt=t.getColumnHeaderCellHeight(),Q(t.scrollIndexLeft,m),Y=X,X=t.style.columnHeaderCellBorderWidth,t.attributes.showRowHeaders&&(X+=Nt),Q(0,t.frozenColumn),X=Y,X<Et&&(ut={name:"",width:t.style.scrollBarWidth,style:"columnHeaderCellCap",isColumnHeaderCell:!0,isColumnHeaderCellCap:!0,type:"string",index:vt.length},Gt({endCap:""},-1,-1)(ut,-1,-1)),t.attributes.showRowHeaders&&(bt={cornerCell:""},X=0,ut={name:"cornerCell",width:t.style.rowHeaderCellWidth,style:"cornerCell",type:"string",index:-1},Gt(bt,-1,-1)(ut,-1,-1)))}function oe(u,m){var M,z,F,j,Y=vt.length;if(at-Pt*2>wt||(Zt=yt[u],ct=t.openChildren[u],F=(t.sizes.rows[u]||t.style.cellHeight)*t.scale,z=(ct?t.sizes.trees[u]:0)*t.scale,U=F+z,at<-U))return!1;for(t.attributes.showRowHeaders&&(X+=Nt),Pt=U,j=t.scrollIndexLeft;j<Y;j+=1)if(M=t.orders.columns[j],X+=Gt(Zt,u,m)(vt[M],M,j),X>t.width){t.scrollIndexRight=j,t.scrollPixelRight=X;break}for(X=0,t.attributes.showRowHeaders&&(X+=Nt),j=0;j<t.frozenColumn&&(M=t.orders.columns[j],X+=Gt(Zt,u,m)(vt[M],M,j),!(X>t.width));j+=1);return t.lastFrozenColumnPixel=X,Pt=U,X=-t.scrollBox.scrollLeft+t.scrollPixelLeft+t.style.cellBorderWidth,f=t.childGrids[u],u!==yt.length&&ct?(f.visible=!0,f.parentNode={offsetTop:at+F+t.canvasOffsetTop,offsetLeft:Nt-1+t.canvasOffsetLeft,offsetHeight:z,offsetWidth:t.width-Nt-t.style.scrollBarWidth-1,offsetParent:t.intf.parentNode,parentNode:t.intf.parentNode,style:t.style,nodeType:"canvas-datagrid-tree",scrollTop:t.scrollBox.scrollTop,scrollLeft:t.scrollBox.scrollLeft,rowIndex:u},t.visibleCells.unshift({rowIndex:u,columnIndex:0,y:f.parentNode.offsetTop,x:f.parentNode.offsetLeft,height:f.height,width:f.width,style:"tree-grid",type:f.parentNode.nodeType}),f.draw()):f&&(f.parentNode.offsetHeight=0,delete t.sizes.trees[u]),Vt.push([Zt,u,m,at,U]),t.visibleRowHeights[u]=U,at+=Pt+(Xt?0:t.style.cellBorderWidth),!0}function he(){t.visibleRows=[],vt=t.getSchema(),t.visibleCells=[],t.canvasOffsetTop=t.isChildGrid?t.parentNode.offsetTop:.5,t.canvasOffsetLeft=t.isChildGrid?t.parentNode.offsetLeft:-.5,wt=t.height,Et=t.width}function ge(){H(0,0,Et,wt,0),t.ctx.clip(),t.ctx.fillStyle=t.style.gridBackgroundColor,R(0,0,Et,wt)}function se(){var u,m=Math.min(yt.length,t.frozenRow);for(X=-t.scrollBox.scrollLeft+t.scrollPixelLeft+t.style.cellBorderWidth,at=re,lt=0;lt<m&&(u=t.orders.rows[lt],!!oe(u,lt));lt+=1);t.attributes.allowFreezingRows&&(at+=t.style.frozenMarkerBorderWidth+t.style.frozenMarkerWidth-.4999999999),t.lastFrozenRowPixel=at}function me(){t.ctx.save(),t.frozenRow>0&&(H(0,t.lastFrozenRowPixel,t.width,t.height-t.lastFrozenRowPixel,0),t.ctx.clip());var u,m,M,z=vt.length;for(X=-t.scrollBox.scrollLeft+t.scrollPixelLeft+t.style.cellBorderWidth,t.attributes.snapToRow||(at+=-t.scrollBox.scrollTop+t.scrollPixelTop+t.style.cellBorderWidth),lt=t.frozenRow+t.scrollIndexTop;lt<ae;lt+=1){m=t.orders.rows[lt];var F=yt[m],j=t.changes.remove.indexOf(F._i);if(!(j!==-1&&t.attributes.mode==="form")&&(t.scrollIndexBottom=lt,t.scrollPixelBottom=at,!oe(m,lt)))break}if(t.attributes.showNewRow){for(t.attributes.showRowHeaders&&(X+=Nt),U=Pt=t.style.cellHeight,ct=!1,u=t.scrollIndexLeft;u<z&&(M=t.orders.columns[u],X+=Gt(t.newRow,yt.length,yt.length)(vt[M],M,u),!(X>t.width+t.scrollBox.scrollLeft));u+=1);Vt.push([t.newRow,yt.length,yt.length,at,U])}t.ctx.restore()}function Ae(){!t.movingSelection||(t.ctx.lineWidth=t.style.moveOverlayBorderWidth,t.ctx.strokeStyle=t.style.moveOverlayBorderColor,t.ctx.setLineDash(t.style.moveOverlayBorderSegments),Yt.forEach(function(u){At(u[0],u[1])}),t.ctx.setLineDash([]))}function Ce(){if(!!t.reorderObject){var u={height:t.reorderObject.height,width:t.reorderObject.width,x:t.reorderObject.x+t.reorderObject.dragOffset.x,y:t.reorderObject.y+t.reorderObject.dragOffset.y},m={width:Et,height:wt,x:0,y:0};t.ctx.fillStyle=t.style.reorderMarkerBackgroundColor,t.ctx.lineWidth=t.style.reorderMarkerBorderWidth,t.ctx.strokeStyle=t.style.reorderMarkerBorderColor,t.dragMode==="row-reorder"?(u.width=Et,u.x=0,m.width=Et,m.height=t.currentCell.height,m.y=t.currentCell.y,R(u.x,u.y,u.width,u.height),K(u.x,u.y,u.width,u.height),t.ctx.lineWidth=t.style.reorderMarkerIndexBorderWidth,t.ctx.strokeStyle=t.style.reorderMarkerIndexBorderColor,t.currentCell.rowIndex!==t.reorderObject.rowIndex&&t.currentCell.rowIndex>-1&&t.currentCell.rowIndex<ae&&At(m,t.reorderTarget.sortRowIndex>t.reorderObject.sortRowIndex?"b":"t")):t.dragMode==="column-reorder"&&t.reorderObject&&(u.height=wt,u.y=0,m.height=wt,m.width=t.currentCell.width,m.y=0,m.x=t.currentCell.x,R(u.x,u.y,u.width,u.height),K(u.x,u.y,u.width,u.height),t.ctx.lineWidth=t.style.reorderMarkerIndexBorderWidth,t.ctx.strokeStyle=t.style.reorderMarkerIndexBorderColor,t.currentCell.sortColumnIndex!==t.reorderObject.sortColumnIndex&&t.currentCell.sortColumnIndex>-1&&t.currentCell.sortColumnIndex<vt.length&&At(m,t.reorderTarget.columnIndex>t.reorderObject.columnIndex?"r":"l"))}}function pe(){t.ctx.lineWidth=t.style.gridBorderWidth,t.ctx.strokeStyle=t.style.gridBorderColor,K(0,0,t.width,t.height)}function xe(){t.ctx.lineWidth=t.style.selectionOverlayBorderWidth,t.ctx.strokeStyle=t.style.selectionOverlayBorderColor;function u(m){At(m[0],m[1])}Ft.filter(function(m){return m[0].rowIndex<t.frozenRow&&m[0].columnIndex<t.frozenColumn}).forEach(u),t.ctx.save(),ht(0),Ft.filter(function(m){return m[0].rowIndex>=t.frozenRow&&m[0].columnIndex>=t.frozenColumn}).forEach(u),t.ctx.restore(),t.ctx.save(),ht(1),Ft.filter(function(m){return m[0].rowIndex>=t.frozenRow&&m[0].columnIndex<t.frozenColumn}).forEach(u),t.ctx.restore(),t.ctx.save(),ht(2),Ft.filter(function(m){return m[0].rowIndex<t.frozenRow&&m[0].columnIndex>=t.frozenColumn}).forEach(u),t.ctx.restore()}function B(){(t.mobile||t.attributes.allowMovingSelection)&&(t.ctx.lineWidth=t.style.selectionHandleBorderWidth,t.ctx.strokeStyle=t.style.selectionHandleBorderColor,t.ctx.fillStyle=t.style.selectionHandleColor,jt.forEach(function(u){nt(u[0],u[1]);var m=t.attributes.touchSelectHandleZone/2,M=u[0].x+(u[1]==="tl"||u[1]==="bl"?0:u[0].width)-m,z=u[0].y+(u[1]==="bl"||u[1]==="br"?u[0].height:0)-m;t.visibleCells.unshift({x:M,y:z,height:t.style.selectionHandleSize+m,width:t.style.selectionHandleSize+m,style:"selection-handle-"+u[1]})}))}function b(){if(!!Ct){t.ctx.save();var u=t.activeCell.columnIndex+1>t.frozenColumn||t.activeCell.rowIndex+1>t.frozenRow,m=u?t.lastFrozenColumnPixel:0,M=u?t.lastFrozenRowPixel:0,z=u?t.width-t.lastFrozenColumnPixel:t.width,F=u?t.height-t.lastFrozenRowPixel:t.height;H(m,M,z,F,0),t.ctx.clip(),t.attributes.selectionMode==="row"?t.activeCell&&t.activeCell.rowIndex===Ct.rowIndex&&(t.ctx.lineWidth=t.style.activeCellOverlayBorderWidth,t.ctx.strokeStyle="red",K(Ct.x,Ct.y,Ct.width,Ct.height)):(t.ctx.lineWidth=t.style.activeCellOverlayBorderWidth,t.ctx.strokeStyle=t.style.activeCellOverlayBorderColor,K(Ct.x,Ct.y,Ct.width,Ct.height)),t.ctx.restore()}}function L(){}function y(){var u=t.lastFrozenRowPixel-t.style.frozenMarkerWidth,m=t.lastFrozenColumnPixel-t.style.frozenMarkerBorderWidth,M=t.currentCell&&t.currentCell.style==="frozen-row-marker",z=t.currentCell&&t.currentCell.style==="frozen-column-marker";t.ctx.lineWidth=t.style.frozenMarkerBorderWidth,t.attributes.allowFreezingColumns&&(t.ctx.fillStyle=z?t.style.frozenMarkerHoverColor:t.style.frozenMarkerColor,t.ctx.strokeStyle=z?t.style.frozenMarkerHoverBorderColor:t.style.frozenMarkerBorderColor,R(m,0,t.style.frozenMarkerWidth,t.height),K(m,0,t.style.frozenMarkerWidth,t.height),t.visibleCells.unshift({x:m,y:0,height:t.height,width:t.style.frozenMarkerWidth+t.style.frozenMarkerBorderWidth,style:"frozen-column-marker"})),t.attributes.allowFreezingRows&&(t.ctx.fillStyle=M?t.style.frozenMarkerHoverColor:t.style.frozenMarkerColor,t.ctx.strokeStyle=M?t.style.frozenMarkerHoverBorderColor:t.style.frozenMarkerBorderColor,R(0,u,t.width,t.style.frozenMarkerWidth),K(0,u,t.width,t.style.frozenMarkerWidth),t.visibleCells.unshift({x:0,y:u,height:t.style.frozenMarkerWidth+t.style.frozenMarkerBorderWidth,width:t.width,style:"frozen-row-marker"})),t.freezeMarkerPosition&&(t.ctx.fillStyle=t.style.frozenMarkerActiveColor,t.ctx.strokeStyle=t.style.frozenMarkerActiveBorderColor,t.dragMode==="frozen-column-marker"?(R(t.freezeMarkerPosition.x,0,t.style.frozenMarkerWidth,t.height),K(t.freezeMarkerPosition.x,0,t.style.frozenMarkerWidth,t.height)):(R(0,t.freezeMarkerPosition.y,t.width,t.style.frozenMarkerWidth),K(0,t.freezeMarkerPosition.y,t.width,t.style.frozenMarkerWidth)))}function D(){if(!t.attributes.showPerformance)return;var u=250,m=t.width-u-t.style.scrollBarWidth-t.style.scrollBarBorderWidth*2,M=re,z=100;n.length===0&&(n=a(0,r,1,function(){return[0,0]})),i.length===0&&(i=a(0,r,1,function(){return[0,0]})),c.length===0&&(c=a(0,r,1,0)),t.ctx.lineWidth=.5;function F(j,Y,Q,tt,x,ot,mt){var st;l(u,z,m,M,Y,Q,tt,x,ot),t.ctx.fillStyle=x,R(3+m,M+9+mt*11,8,8),t.ctx.fillStyle=t.style.debugPerfChartTextColor,st=Q!==void 0?Y[0][Q]:Y[0],N(j+" "+(isNaN(st)?0:st).toFixed(3),14+m,M+16+mt*11)}t.ctx.textAlign="left",t.ctx.font=t.style.debugFont,t.ctx.fillStyle=t.style.debugPerfChartBackground,R(m,M,u,z),[["Scroll Height",n,0,t.scrollBox.scrollHeight,t.style.debugScrollHeightColor,!1],["Scroll Width",n,1,t.scrollBox.scrollWidth,t.style.debugScrollWidthColor,!1],["Performance",s,void 0,200,t.style.debugPerformanceColor,!1],["Entities",c,void 0,1500,t.style.debugEntitiesColor,!1],["TouchPPSX",i,0,1e3,t.style.debugTouchPPSXColor,!0],["TouchPPSY",i,1,1e3,t.style.debugTouchPPSYColor,!0]].forEach(function(j,Y){j.push(Y),F.apply(null,j)}),t.ctx.fillStyle=t.style.debugPerfChartBackground,c.pop(),c.unshift(t.visibleCells.length),n.pop(),n.unshift([t.scrollBox.scrollTop,t.scrollBox.scrollLeft]),i.pop(),i.unshift([t.yPPS,t.xPPS])}function k(){t.ctx.save();var u;if((t.attributes.showPerformance||t.attributes.debug)&&(s.length===0&&(s=a(0,r,1,0)),s.pop(),s.unshift(performance.now()-W)),!t.attributes.debug){t.ctx.restore();return}t.ctx.font=t.style.debugFont,u={},u.perf=(s.reduce(function(m,M){return m+M},0)/Math.min(e,s.length)).toFixed(1),u.perfDelta=s[0].toFixed(1),u.frozenColumnsWidth=q(),u.htmlImages=Object.keys(t.htmlImageCache).length,u.reorderObject="x: "+(t.reorderObject||{columnIndex:0}).columnIndex+", y: "+(t.reorderObject||{rowIndex:0}).rowIndex,u.reorderTarget="x: "+(t.reorderTarget||{columnIndex:0}).columnIndex+", y: "+(t.reorderTarget||{rowIndex:0}).rowIndex,u.scale=t.scale,u.startScale=t.startScale,u.scaleDelta=t.scaleDelta,u.zoomDeltaStart=t.zoomDeltaStart,u.touchLength=t.touchLength,u.touches="y0: "+(t.touchPosition||{y:0}).y+" y1: "+(t.touchPosition1||{y:0}).y,u.scrollBox=t.scrollBox.toString(),u.scrollIndex="x: "+t.scrollIndexLeft+", y: "+t.scrollIndexTop,u.scrollPixel="x: "+t.scrollPixelLeft+", y: "+t.scrollPixelTop,u.canvasOffset="x: "+t.canvasOffsetLeft+", y: "+t.canvasOffsetTop,u.touchDelta="x: "+t.touchDelta.x+", y: "+t.touchDelta.y,u.touchAnimateTo="x: "+t.touchAnimateTo.x+", y: "+t.touchAnimateTo.y,u.scrollAnimation="x: "+t.scrollAnimation.x+", y: "+t.scrollAnimation.y,u.touchPPS="x: "+t.xPPS+", y: "+t.yPPS,u.touchPPST="x: "+t.xPPST+", y: "+t.yPPST,u.touchDuration=t.touchDuration,u.pointerLockPosition=t.pointerLockPosition?t.pointerLockPosition.x+", "+t.pointerLockPosition.y:"",u.size="w: "+t.width+", h: "+t.height,u.mouse="x: "+t.mouse.x+", y: "+t.mouse.y,u.touch=t.touchStart?"x: "+t.touchStart.x+", y: "+t.touchStart.y:"",u.entities=t.visibleCells.length,u.hasFocus=t.hasFocus,u.dragMode=t.dragMode,t.currentCell&&(u.columnIndex=t.currentCell.columnIndex,u.rowIndex=t.currentCell.rowIndex,u.sortColumnIndex=t.currentCell.sortColumnIndex,u.sortRowIndex=t.currentCell.sortRowIndex,u.context=t.currentCell.context,u.dragContext=t.currentCell.dragContext,u.style=t.currentCell.style,u.type=t.currentCell.type),t.ctx.textAlign="right",t.ctx.fillStyle=t.style.debugBackgroundColor,R(0,0,t.width,t.height),Object.keys(u).forEach(function(m,M){var z=m+": "+u[m],F=14;t.ctx.fillStyle=t.style.debugColor,N(z,Et-20,(t.attributes.showPerformance?140:24)+M*F)}),t.ctx.restore()}t.ctx.save(),he(),ge(),se(),me(),le(),y(),B(),Ce(),Ae(),pe(),xe(),b(),L(),ce(),C&&t.resize(!0),k(),D(),!t.dispatchEvent("afterdraw",{})&&t.ctx.restore()}}},10505:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return e}});var _=P(91220),t=P(19273),s=P(155),I=P.n(s);function e(r){var c;r.stopPropagation=function(o){o.stopPropagation()},r.addEventListener=function(o,n){r.events[o]=r.events[o]||[],r.events[o].unshift(n)},r.removeEventListener=function(o,n){(r.events[o]||[]).forEach(function(a,l){n===a&&r.events[o].splice(l,1)})},r.dispatchEvent=function(o,n){n=o.type?o:n||{},o=o.type||o;var i;function a(){i=!0}if(!!r.events[o])return r.events[o].forEach(function(g){n.ctx=r.ctx,n.preventDefault=a,g.apply(r.intf,[n])}),i},r.getRatio=function(){return Math.min(r.attributes.maxPixelRatio,(window.devicePixelRatio||1)/(r.ctx.webkitBackingStorePixelRatio||r.ctx.mozBackingStorePixelRatio||r.ctx.msBackingStorePixelRatio||r.ctx.oBackingStorePixelRatio||r.ctx.backingStorePixelRatio||1))},r.resize=function(o){if(!r.canvas)return;var n,i={x:0,y:0,height:0,width:0,style:"vertical-scroll-bar"},a={x:0,y:0,height:0,width:0,style:"horizontal-scroll-bar"},l={x:0,y:0,height:0,width:0,style:"vertical-scroll-box"},g={x:0,y:0,height:0,width:0,style:"horizontal-scroll-box"},A={x:0,y:0,height:0,width:0,isCorner:!0,isScrollBoxCorner:!0,style:"scroll-box-corner"},p=r.style.scrollBarBoxMargin*2,S=r.style.scrollBarBorderWidth*2,v=r.style.scrollBarBoxMargin*.5,h=r.style.scrollBarWidth+r.style.scrollBarBorderWidth*2,E=r.getRatio(),H=r.style.gridBorderCollapse==="collapse"?1:2,R=r.style.cellBorderWidth*H,K=r.style.columnHeaderCellBorderWidth*H,N=0,$=0,gt,ht=(r.data||[]).length,T=r.getColumnHeaderCellHeight(),G=r.getRowHeaderCellWidth(),nt=r.style.cellHeight,At=r.getSchema();function Lt(){r.scrollBox.horizontalBarVisible=r.style.width!=="auto"&&$>r.scrollBox.width&&r.style.overflowX!=="hidden"||r.style.overflowX==="scroll",r.scrollBox.horizontalBoxVisible=$>r.scrollBox.width,r.scrollBox.verticalBarVisible=r.style.height!=="auto"&&N>r.scrollBox.height&&r.style.overflowY!=="hidden"||r.style.overflowY==="scroll",r.scrollBox.verticalBoxVisible=N>r.scrollBox.height}function zt(){r.scrollBox.width=r.width-G,r.scrollBox.height=r.height-T}function It(){r.isChildGrid||(gt={height:T+N+R+1,width:$+G+R},["width","height"].forEach(function(q){["auto",void 0].indexOf(r.style[q])!==-1&&["auto",void 0].indexOf(r.appliedInlineStyles[q])!==-1?r.parentNodeStyle[q]=gt[q]+"px":["auto",void 0].indexOf(r.style[q])==-1&&["auto",void 0].indexOf(r.appliedInlineStyles[q])==-1&&(r.parentNodeStyle[q]=r.style[q],r.isComponet&&(r.canvas.style[q]=r.style[q]))}))}for(r.scrollCache.x=[],r.scrollCache.y=[],n=0;n<ht;n+=1)r.scrollCache.y[n]=N,N+=((r.sizes.rows[n]||nt)+(r.sizes.trees[n]||0))*r.scale+(r.frozenRow>n&&r.sizes.trees[n]||0);return ht>1&&(r.scrollCache.y[n]=N),$=At.reduce(function(d,C,w){if(C=At[r.orders.columns[w]],C.hidden)return r.scrollCache.x[w]=d,d;var W=d+r.getColummnWidth(r.orders.columns[w]);return r.scrollCache.x[w]=W,W},0)||0,r.attributes.showNewRow&&(N+=nt),r.attributes.snapToRow&&(N+=r.style.cellHeight),It(),r.isChildGrid?(r.width=r.parentNode.offsetWidth,r.height=r.parentNode.offsetHeight):(r.height!==r.canvas.offsetHeight||r.width!==r.canvas.offsetWidth)&&(r.height=r.canvas.offsetHeight,r.width=r.canvas.offsetWidth,r.canvasOffsetLeft=r.args.canvasOffsetLeft||0,r.canvasOffsetTop=r.args.canvasOffsetTop||0),r.scrollBox.top=T+K,r.scrollBox.left=G,zt(),Lt(),r.scrollBox.horizontalBarVisible&&(r.style.height==="auto"&&!r.isChildGrid&&(r.height+=h),N+=h,It(),zt(),Lt()),r.scrollBox.verticalBarVisible&&(r.style.width==="auto"&&!r.isChildGrid&&(r.width+=h),$+=h,It(),zt(),Lt()),zt(),r.scrollBox.scrollWidth=$-r.scrollBox.width,r.scrollBox.scrollHeight=N-r.scrollBox.height,r.scrollBox.widthBoxRatio=r.scrollBox.width/$,r.scrollBox.scrollBoxWidth=r.scrollBox.width*r.scrollBox.widthBoxRatio-r.style.scrollBarWidth-S-v,r.scrollBox.heightBoxRatio=(r.scrollBox.height-T)/N,r.scrollBox.scrollBoxHeight=r.scrollBox.height*r.scrollBox.heightBoxRatio-r.style.scrollBarWidth-S-v,r.scrollBox.scrollBoxWidth=Math.max(r.scrollBox.scrollBoxWidth,r.style.scrollBarBoxMinSize),r.scrollBox.scrollBoxHeight=Math.max(r.scrollBox.scrollBoxHeight,r.style.scrollBarBoxMinSize),a.x+=G,a.y+=r.height-r.style.scrollBarWidth-v,a.width=r.width-r.style.scrollBarWidth-G-v-p,a.height=r.style.scrollBarWidth+r.style.scrollBarBorderWidth+v,g.y=a.y+r.style.scrollBarBoxMargin,g.width=r.scrollBox.scrollBoxWidth,g.height=r.style.scrollBarBoxWidth,i.x+=r.width-r.style.scrollBarWidth-r.style.scrollBarBorderWidth-v,i.y+=T,i.width=r.style.scrollBarWidth+r.style.scrollBarBorderWidth+v,i.height=r.height-T-r.style.scrollBarWidth-v-p,l.x=i.x+r.style.scrollBarBoxMargin,l.width=r.style.scrollBarBoxWidth,l.height=r.scrollBox.scrollBoxHeight,A.x=a.x+a.width+p,A.y=i.y+i.height+p,A.width=r.style.scrollBarWidth+r.style.scrollBarBorderWidth,A.height=r.style.scrollBarWidth+r.style.scrollBarBorderWidth,r.scrollBox.entities={horizontalBar:a,horizontalBox:g,verticalBar:i,verticalBox:l,corner:A},r.scrollBox.bar={v:i,h:a},r.scrollBox.box={v:l,h:g},r.page=Math.max(1,r.visibleRows.length-3-r.attributes.pageUpDownOverlap),r.isChildGrid||(r.canvas.width=r.width*E,r.canvas.height=r.height*E,r.ctx.scale(E,E)),r.resizeEditInput(),r.scroll(!0),o&&r.draw(!0),r.dispatchEvent("resize",{}),!0},r.scroll=function(o){var n=r.getSchema(),i=(r.data||[]).length,a=r.style.cellHeight;for(r.scrollIndexTop=Math.floor(i*(r.scrollBox.scrollTop/r.scrollBox.scrollHeight)-100),r.scrollIndexTop=Math.max(r.scrollIndexTop,0),r.scrollPixelTop=r.scrollCache.y[r.scrollIndexTop],r.scrollBox.scrollHeight===0&&(r.scrollIndexTop=0),r.scrollPixelTop=0,r.scrollIndexLeft=r.frozenColumn,r.scrollPixelLeft=0;r.scrollPixelTop<r.scrollBox.scrollTop&&r.scrollIndexTop<r.data.length;)r.scrollIndexTop+=1,r.scrollPixelTop=r.scrollCache.y[r.scrollIndexTop];for(;r.scrollPixelLeft<r.scrollBox.scrollLeft+1&&r.scrollIndexLeft<n.length;)r.scrollPixelLeft=r.scrollCache.x[r.scrollIndexLeft],r.scrollIndexLeft+=1;n.length>0&&(r.scrollIndexLeft=Math.max(r.scrollIndexLeft-1,0),r.scrollPixelLeft-=r.getColummnWidth(r.orders.columns[r.scrollIndexLeft])),(r.data||[]).length>0&&(r.scrollIndexTop=Math.max(r.scrollIndexTop-1,0),r.scrollPixelTop=Math.max(r.scrollPixelTop-(r.data[r.scrollIndexTop]?(r.sizes.rows[r.scrollIndexTop]||a)+(r.sizes.trees[r.scrollIndexTop]||0):a)*r.scale,0)),r.ellipsisCache={},o||r.draw(!0),requestAnimationFrame(r.resizeEditInput),r.dispatchEvent("scroll",{top:r.scrollBox.scrollTop,left:r.scrollBox.scrollLeft})},r.mousemove=function(o,n){if(!(r.contextMenu||r.input)){r.mouse=n||r.getLayerPos(o);var i=(o.ctrlKey||o.metaKey||r.attributes.persistantSelectionMode)&&!r.attributes.singleSelectionMode,a,l=r.getSchema(),g,A,p=r.mouse.x,S=r.mouse.y,v=r.getCellAt(p,S),h,E={NativeEvent:o,cell:v,x:p,y:S},H=r.currentCell;if(clearTimeout(r.scrollTimer),r.isInGrid({x:p,y:S})||(r.hasFocus=!1),!r.dispatchEvent("mousemove",E)){if(v&&r.currentCell&&(r.rowBoundaryCrossed=r.currentCell.rowIndex!==v.rowIndex,r.columnBoundaryCrossed=r.currentCell.columnIndex!==v.columnIndex,r.cellBoundaryCrossed=r.rowBoundaryCrossed||r.columnBoundaryCrossed,["row","column","cell"].forEach(function($){r[$+"BoundaryCrossed"]&&(E.cell=H,r.dispatchEvent($+"mouseout",E),E.cell=v,r.dispatchEvent($+"mouseover",E))})),r.currentCell=v,r.hovers={},!r.draggingItem&&v&&r.scrollModes.indexOf(v.context)===-1&&(r.dragItem=v,r.dragMode=v.dragContext,r.cursor=v.context,v.context==="cell"&&(r.cursor="default",r.hovers={rowIndex:v.rowIndex,columnIndex:v.columnIndex}),(r.selecting||r.reorderObject)&&v.context==="cell")){if(h={x:Math.abs(r.dragStart.x-p),y:Math.abs(r.dragStart.y-S)},r.dragStartObject.columnIndex!==-1&&o.shiftKey&&(r.dragStartObject={rowIndex:r.activeCell.rowIndex,columnIndex:r.activeCell.columnIndex}),g={top:Math.min(r.dragStartObject.rowIndex,v.rowIndex),left:Math.min(r.dragStartObject.columnIndex,v.columnIndex),bottom:Math.max(r.dragStartObject.rowIndex,v.rowIndex),right:Math.max(r.dragStartObject.columnIndex,v.columnIndex)},r.dragStartObject.columnIndex===-1&&(A=r.getSelectionBounds(),g.left=-1,g.right=l.length-1,g.top=Math.min(A.top,v.rowIndex),g.bottom=Math.max(A.bottom,v.rowIndex)),(r.dragStartObject.rowIndex!==v.rowIndex||r.dragStartObject.columnIndex!==v.columnIndex)&&(r.ignoreNextClick=!0),(r.cellBoundaryCrossed||h.x===0&&h.y===0||r.attributes.selectionMode==="row")&&((r.attributes.selectionMode==="row"||r.dragStartObject.columnIndex===-1)&&r.rowBoundaryCrossed?r.selectRow(v.rowIndex,i,null,!0):r.attributes.selectionMode!=="row"&&(!r.dragAddToSelection&&v.rowIndex!==void 0?r.selections[v.rowIndex]&&r.selections[v.rowIndex].indexOf(v.columnIndex)!==-1&&r.selections[v.rowIndex].splice(r.selections[v.rowIndex].indexOf(v.columnIndex),1):(r.selections[v.rowIndex]=r.selections[v.rowIndex]||[],r.selections[v.rowIndex].indexOf(v.columnIndex)===-1&&r.selections[v.rowIndex].push(v.columnIndex)))),(!r.selectionBounds||g.top!==r.selectionBounds.top||g.left!==r.selectionBounds.left||g.bottom!==r.selectionBounds.bottom||g.right!==r.selectionBounds.right)&&!i)if(r.selections=[],A=g,r.attributes.selectionMode==="row")for(a=A.top;a<=A.bottom;a+=1)r.selectRow(a,!0,null,!0);else g.top!==-1&&r.selectArea(A,!0);r.autoScrollZone(o,p,S,i)}r.cellBoundaryCrossed=!1,r.rowBoundaryCrossed=!1,r.columnBoundaryCrossed=!1;var R=/-move/.test(r.dragMode),K=/frozen-row-marker|frozen-column-marker/.test(r.dragMode),N=/-resize/.test(r.dragMode);(r.selecting||R||K||N||r.reorderObject||r.currentCell.type==="image")&&r.draw(!0)}}},r.click=function(o,n){var i,a=JSON.stringify(r.getSelectionBounds()),l=(o.ctrlKey||o.metaKey||r.attributes.persistantSelectionMode)&&!r.attributes.singleSelectionMode,g=n||r.getLayerPos(o);if(r.currentCell=r.getCellAt(g.x,g.y),r.currentCell.grid!==void 0)return;function A(){var R,K=r.getSelectionBounds();R={selections:r.selections,selectionBounds:r.getSelectionBounds()},Object.defineProperty(R,"selectedData",{get:function(){return r.getSelectedData()}}),r.dispatchEvent("selectionchanged",R)}if(r.input&&r.endEdit(),r.ignoreNextClick){r.ignoreNextClick=!1;return}if(i=r.currentCell,!r.dispatchEvent("click",{NativeEvent:o,cell:r.currentCell})&&!!r.hasFocus){if(l||(r.lastClickCell=r.currentCell),["rowHeaderCell","columnHeaderCell"].indexOf(r.currentCell.style)===-1&&!l&&(r.setActiveCell(i.columnIndex,i.rowIndex),r.dispatchEvent("activecellchanged",{cell:i})),r.currentCell.context==="cell"){if(r.currentCell.style==="cornerCell"){r.selectAll(),r.draw(),A();return}if(r.currentCell.style==="columnHeaderCell"){if(r.attributes.columnHeaderClickBehavior==="sort"&&r.attributes.allowSorting){var p=r.orderings.columns.find(function(R){return R.orderBy===i.header.name}),S=[void 0,"asc","desc"];p?r.orderDirection=S[(S.indexOf(p.orderDirection)+1)%3]:r.orderDirection="asc",r.order(i.header.name,r.orderDirection),A();return}if(r.attributes.columnHeaderClickBehavior==="select"){r.selectColumn(i.header.index,l,o.shiftKey),r.draw();return}}if(r.selections[i.rowIndex]=r.selections[i.rowIndex]||[],(r.attributes.selectionMode==="row"||r.currentCell.style==="rowHeaderCell")&&r.currentCell.style==="rowHeaderCell"&&r.attributes.tree&&g.x>0&&g.x-r.currentCell.x<r.style.treeArrowWidth+r.style.treeArrowMarginLeft+r.style.treeArrowMarginRight+r.style.treeArrowClickRadius&&g.y-r.currentCell.y<r.style.treeArrowHeight+r.style.treeArrowMarginTop+r.style.treeArrowClickRadius&&g.y>0){r.toggleTree(i.rowIndex);return}o.shiftKey&&!l&&(r.selectionBounds=r.getSelectionBounds(),r.selectArea(void 0,!1))}if(A(),r.intf.attributes.mode==="form"){if(i.type==="checkbox"){r.updateChanges(i);var v=r.data[i.rowIndex][i.header.name]=!i.value;(0,t.Z)(v),i.value=!i.value,r.updateChanges(i)}else if(i.isNormal){var h=i,E=h.rowIndex,H=h.columnIndex;r.beginEditAt(H,E,!0)}}r.draw(!0)}},r.dragResizeColumn=function(o){var n,i,a;if(n=r.getLayerPos(o),i=r.resizingStartingWidth+n.x-r.dragStart.x,a=r.resizingStartingHeight+n.y-r.dragStart.y,i<r.style.minColumnWidth&&(i=r.style.minColumnWidth),a<r.style.minRowHeight&&(a=r.style.minRowHeight),r.dispatchEvent("resizecolumn",{x:i,y:a,draggingItem:r.draggingItem}))return!1;if(r.scrollBox.scrollLeft>r.scrollBox.scrollWidth-r.attributes.resizeScrollZone&&r.dragMode==="ew-resize"&&(r.resize(!0),r.scrollBox.scrollLeft+=i),r.dragMode==="ew-resize"){r.sizes.columns[r.draggingItem.header.style==="rowHeaderCell"?"cornerCell":r.draggingItem.sortColumnIndex]=i,["rowHeaderCell","cornerCell"].indexOf(r.draggingItem.header.style)!==-1&&r.resize(!0),r.resizeChildGrids();return}if(r.dragMode==="ns-resize"){r.draggingItem.rowOpen?r.sizes.trees[r.draggingItem.rowIndex]=a:r.attributes.globalRowResize?r.style.cellHeight=a:r.sizes.rows[r.draggingItem.rowIndex]=a,r.dispatchEvent("resizerow",{row:a}),r.resizeChildGrids();return}r.ellipsisCache={}},r.stopDragResize=function(){r.resize(),document.body.removeEventListener("mousemove",r.dragResizeColumn,!1),document.body.removeEventListener("mouseup",r.stopDragResize,!1),r.setStorageData(),r.draw(!0),r.ignoreNextClick=!0},r.scrollGrid=function(o){var n=r.getLayerPos(o);if(r.attributes.scrollPointerLock&&r.pointerLockPosition&&["horizontal-scroll-box","vertical-scroll-box"].indexOf(r.scrollStartMode)!==-1&&(r.pointerLockPosition.x+=o.movementX,r.pointerLockPosition.y+=o.movementY,r.pointerLockPosition.x=Math.min(r.width-r.style.scrollBarWidth,Math.max(0,r.pointerLockPosition.x)),r.pointerLockPosition.y=Math.min(r.height-r.style.scrollBarWidth,Math.max(0,r.pointerLockPosition.y)),n=r.pointerLockPosition),r.scrollMode=r.getCellAt(n.x,n.y).context,r.scrollMode==="horizontal-scroll-box"&&r.scrollStartMode!=="horizontal-scroll-box"){r.scrollStartMode="horizontal-scroll-box",r.dragStart=n,r.scrollStart.left=r.scrollBox.scrollLeft,clearTimeout(r.scrollTimer);return}if(r.scrollMode==="vertical-scroll-box"&&r.scrollStartMode!=="vertical-scroll-box"){r.scrollStartMode="vertical-scroll-box",r.dragStart=n,r.scrollStart.top=r.scrollBox.scrollTop,clearTimeout(r.scrollTimer);return}r.scrollStartMode==="vertical-scroll-box"&&r.scrollMode!=="vertical-scroll-box"&&(r.scrollMode="vertical-scroll-box"),r.scrollStartMode==="horizontal-scroll-box"&&r.scrollMode!=="horizontal-scroll-box"&&(r.scrollMode="horizontal-scroll-box"),clearTimeout(r.scrollTimer),r.scrollModes.indexOf(r.scrollMode)!==-1&&(r.scrollMode==="vertical-scroll-box"?r.scrollBox.scrollTop=r.scrollStart.top+(n.y-r.dragStart.y)/r.scrollBox.heightBoxRatio:r.scrollMode==="vertical-scroll-top"?(r.scrollBox.scrollTop-=r.page*r.style.cellHeight,r.scrollTimer=setTimeout(r.scrollGrid,r.attributes.scrollRepeatRate,o)):r.scrollMode==="vertical-scroll-bottom"&&(r.scrollBox.scrollTop+=r.page*r.style.cellHeight,r.scrollTimer=setTimeout(r.scrollGrid,r.attributes.scrollRepeatRate,o)),r.scrollMode==="horizontal-scroll-box"?r.scrollBox.scrollLeft=r.scrollStart.left+(n.x-r.dragStart.x)/r.scrollBox.widthBoxRatio:r.scrollMode==="horizontal-scroll-right"?(r.scrollBox.scrollLeft+=r.attributes.selectionScrollIncrement,r.scrollTimer=setTimeout(r.scrollGrid,r.attributes.scrollRepeatRate,o)):r.scrollMode==="horizontal-scroll-left"&&(r.scrollBox.scrollLeft-=r.attributes.selectionScrollIncrement,r.scrollTimer=setTimeout(r.scrollGrid,r.attributes.scrollRepeatRate,o)))},r.stopScrollGrid=function(){clearTimeout(r.scrollTimer),document.exitPointerLock&&document.exitPointerLock(),document.removeEventListener("mousemove",r.scrollGrid,!1)},r.dragReorder=function(o){var n,i,a,l=r.dragMode==="column-reorder",g=r.dragMode==="row-reorder";n=r.getLayerPos(o),i=n.x-r.dragStart.x,a=n.y-r.dragStart.y,!(!r.attributes.allowColumnReordering&&l)&&(!r.attributes.allowRowReordering&&g||r.dispatchEvent("reordering",{NativeEvent:o,source:r.dragStartObject,target:r.currentCell,dragMode:r.dragMode})||(Math.abs(i)>r.attributes.reorderDeadZone||Math.abs(a)>r.attributes.reorderDeadZone)&&(r.reorderObject=r.draggingItem,r.reorderTarget=r.currentCell,r.reorderObject.dragOffset={x:i,y:a},r.autoScrollZone(o,l?n.x:-1,g?n.y:-1,!1)))},r.stopDragReorder=function(o){var n,i,a={"row-reorder":r.orders.rows,"column-reorder":r.orders.columns},l={"row-reorder":"rowIndex","column-reorder":"sortColumnIndex"}[r.dragMode];document.body.removeEventListener("mousemove",r.dragReorder,!1),document.body.removeEventListener("mouseup",r.stopDragReorder,!1),r.reorderObject&&r.reorderTarget&&(r.dragMode==="column-reorder"&&r.reorderTarget.sortColumnIndex>-1&&r.reorderTarget.sortColumnIndex<r.getSchema().length||r.dragMode==="row-reorder"&&r.reorderTarget.rowIndex>-1&&r.reorderTarget.rowIndex<r.data.length)&&r.reorderObject[l]!==r.reorderTarget[l]&&!r.dispatchEvent("reorder",{NativeEvent:o,source:r.reorderObject,target:r.reorderTarget,dragMode:r.dragMode})&&(r.ignoreNextClick=!0,n=a[r.dragMode].indexOf(r.reorderObject[l]),i=a[r.dragMode].indexOf(r.reorderTarget[l]),a[r.dragMode].splice(n,1),a[r.dragMode].splice(i,0,r.reorderObject[l]),r.dragMode==="column-reorder"?r.orders.columns=a[r.dragMode]:r.orders.rows=a[r.dragMode],r.resize(),r.setStorageData()),r.reorderObject=void 0,r.reorderTarget=void 0,r.draw(!0)},r.dragMove=function(o){if(!r.dispatchEvent("moving",{NativeEvent:o,cell:r.currentCell})){var n=r.getLayerPos(o);r.moveOffset={x:r.currentCell.columnIndex-r.dragStartObject.columnIndex,y:r.currentCell.rowIndex-r.dragStartObject.rowIndex},(Math.abs(n.x)>r.attributes.reorderDeadZone||Math.abs(n.y)>r.attributes.reorderDeadZone)&&setTimeout(function(){r.autoScrollZone(o,n.x,n.y,!1)},1)}},r.stopDragMove=function(o){document.body.removeEventListener("mousemove",r.dragMove,!1),document.body.removeEventListener("mouseup",r.stopDragMove,!1);var n=r.getSelectionBounds();if(r.dispatchEvent("endmove",{NativeEvent:o,cell:r.currentCell})){r.movingSelection=void 0,r.moveOffset=void 0,r.draw(!0);return}r.moveOffset&&(r.moveTo(r.movingSelection,n.left+r.moveOffset.x,n.top+r.moveOffset.y),r.moveSelection(r.moveOffset.x,r.moveOffset.y)),r.movingSelection=void 0,r.moveOffset=void 0,r.draw(!0)},r.freezeMove=function(o){if(!r.dispatchEvent("freezemoving",{NativeEvent:o,cell:r.currentCell})){var n=r.getLayerPos(o);r.ignoreNextClick=!0,r.freezeMarkerPosition=n,r.currentCell&&r.currentCell.rowIndex!==void 0&&r.dragMode==="frozen-row-marker"&&(r.scrollBox.scrollTop=0,r.frozenRow=r.currentCell.rowIndex+1),r.currentCell&&r.currentCell.columnIndex!==void 0&&r.dragMode==="frozen-column-marker"&&(r.scrollBox.scrollLeft=0,r.frozenColumn=r.currentCell.columnIndex+1),(Math.abs(n.x)>r.attributes.reorderDeadZone||Math.abs(n.y)>r.attributes.reorderDeadZone)&&setTimeout(function(){r.autoScrollZone(o,n.x,n.y,!1)},1)}},r.stopFreezeMove=function(o){if(document.body.removeEventListener("mousemove",r.freezeMove,!1),document.body.removeEventListener("mouseup",r.stopFreezeMove,!1),r.freezeMarkerPosition=void 0,r.dispatchEvent("endfreezemove",{NativeEvent:o,cell:r.currentCell})){r.frozenRow=r.startFreezeMove.x,r.frozenColumn=r.startFreezeMove.y,r.draw(!0);return}r.draw(!0)},r.mousedown=function(o,n){if(!r.dispatchEvent("mousedown",{NativeEvent:o,cell:r.currentCell})&&!!r.hasFocus&&!(o.button===2||r.input)){var i=o.ctrlKey||o.metaKey,a=/-move/.test(r.dragMode),l=/frozen-row-marker|frozen-column-marker/.test(r.dragMode),g=/-resize/.test(r.dragMode);if(r.dragStart=n||r.getLayerPos(o),r.scrollStart={left:r.scrollBox.scrollLeft,top:r.scrollBox.scrollTop},r.dragStartObject=r.getCellAt(r.dragStart.x,r.dragStart.y),r.dragAddToSelection=!r.dragStartObject.selected,!i&&!o.shiftKey&&!/(vertical|horizontal)-scroll-(bar|box)/.test(r.dragStartObject.context)&&r.currentCell&&!r.currentCell.isColumnHeader&&!a&&!l&&!g&&(r.selections=[]),!r.dragStartObject.isGrid){if(r.scrollModes.indexOf(r.dragStartObject.context)!==-1){r.scrollMode=r.dragStartObject.context,r.scrollStartMode=r.dragStartObject.context,r.scrollGrid(o),r.attributes.scrollPointerLock&&["horizontal-scroll-box","vertical-scroll-box"].indexOf(r.scrollStartMode)!==-1&&(r.pointerLockPosition={x:r.dragStart.x,y:r.dragStart.y},r.canvas.requestPointerLock()),document.addEventListener("mousemove",r.scrollGrid,!1),document.addEventListener("mouseup",r.stopScrollGrid,!1),r.ignoreNextClick=!0;return}if(r.dragMode==="cell"){r.selecting=!0,(r.attributes.selectionMode==="row"||r.dragStartObject.columnIndex===-1)&&r.dragStartObject.rowIndex>-1?r.selectRow(r.dragStartObject.rowIndex,i,null):r.attributes.selectionMode!=="row"&&r.mousemove(o);return}if(a)return r.draggingItem=r.dragItem,r.movingSelection=r.selections.concat([]),r.dragging=r.dragStartObject,r.dispatchEvent("beginmove",{NativeEvent:o,cell:r.currentCell})?void 0:(document.body.addEventListener("mousemove",r.dragMove,!1),document.body.addEventListener("mouseup",r.stopDragMove,!1),r.mousemove(o));if(l)return r.draggingItem=r.dragItem,r.startFreezeMove={x:r.frozenRow,y:r.frozenColumn},r.dispatchEvent("beginfreezemove",{NativeEvent:o})?void 0:(document.body.addEventListener("mousemove",r.freezeMove,!1),document.body.addEventListener("mouseup",r.stopFreezeMove,!1),r.mousemove(o));if(g){r.draggingItem=r.dragItem,r.draggingItem.rowOpen?r.resizingStartingHeight=r.sizes.trees[r.draggingItem.rowIndex]:r.resizingStartingHeight=r.sizes.rows[r.draggingItem.rowIndex]||r.style.cellHeight,r.resizingStartingWidth=r.sizes.columns[r.draggingItem.header.style==="rowHeaderCell"?"cornerCell":r.draggingItem.sortColumnIndex]||r.draggingItem.width,document.body.addEventListener("mousemove",r.dragResizeColumn,!1),document.body.addEventListener("mouseup",r.stopDragResize,!1);return}if(["row-reorder","column-reorder"].indexOf(r.dragMode)!==-1){r.draggingItem=r.dragStartObject,document.body.addEventListener("mousemove",r.dragReorder,!1),document.body.addEventListener("mouseup",r.stopDragReorder,!1);return}}}},r.mouseup=function(o){clearTimeout(r.scrollTimer),r.cellBoundaryCrossed=!0,r.rowBoundaryCrossed=!0,r.columnBoundaryCrossed=!0,r.selecting=void 0,r.draggingItem=void 0,r.dragStartObject=void 0,!r.dispatchEvent("mouseup",{NativeEvent:o,cell:r.currentCell})&&(!r.hasFocus&&o.target!==r.canvas||r.currentCell&&r.currentCell.grid!==void 0||r.contextMenu||r.input||(r.dragStart&&r.isInGrid(r.dragStart)&&r.controlInput.focus(),o.preventDefault()))},r.getAdjacentCells=function(){var o,n,i=r.getSchema(),a={};for(o=0;o<i.length;o+=1)n=r.orders.columns[o],i[n].hidden||(a.first===void 0&&(a.first=o,a.left=o),a.last=o,o>r.activeCell.columnIndex&&a.right===void 0&&(a.right=o),o<r.activeCell.columnIndex&&(a.left=o));return a.right===void 0&&(a.right=a.last),a},r.keydown=function(o){var n,i,a=r.getAdjacentCells(),l=r.activeCell.columnIndex,g=r.activeCell.rowIndex,A=o.ctrlKey||o.metaKey,p=r.data.length-1,S=r.getSchema(),v=S.length-1,h=r.dispatchEvent("keydown",{NativeEvent:o,cell:r.currentCell});if(!h&&!!r.hasFocus){if(I()(o)&&!A)return r.beginEditAt(l,g,o,!0);if(r.attributes.showNewRow&&(p+=1),o.key==="Tab"&&o.preventDefault(),o.key==="Escape")r.selectNone();else if(A&&o.key==="a")r.selectAll();else if(o.key==="ArrowDown")g+=1;else if(o.key==="ArrowUp")g-=1;else if(o.key==="ArrowLeft"&&!A||o.shiftKey&&o.key==="Tab")l=a.left;else if(o.key==="ArrowRight"&&!A||!o.shiftKey&&o.key==="Tab")l=a.right;else if(o.key==="PageUp")g-=r.page,o.preventDefault();else if(o.key==="PageDown")g+=r.page,o.preventDefault();else if(o.key==="Home"||A&&o.key==="ArrowUp")g=0;else if(o.key==="End"||A&&o.key==="ArrowDown")g=r.data.length-1;else if(A&&o.key==="ArrowRight")l=a.last;else if(A&&o.key==="ArrowLeft")l=a.first;else if(o.key==="Backspace")return r.deleteSelection();if(o.key==="Enter")return o.preventDefault(),r.beginEditAt(l,g,o);(l<0||Number.isNaN(l))&&(l=a.first),g>p&&(g=p),(g<0||Number.isNaN(g))&&(g=0),l>v&&(l=a.last);var E=["ArrowLeft","ArrowUp","ArrowRight","ArrowDown"].includes(o.key);o.shiftKey&&E&&(r.selections[Math.max(g,0)]=r.selections[Math.max(g,0)]||[],r.selections[Math.max(g,0)].push(l),r.selectionBounds=r.getSelectionBounds(),r.selectArea(void 0,A),r.draw(!0)),(l!==r.activeCell.columnIndex||g!==r.activeCell.rowIndex)&&(r.scrollIntoView(l!==r.activeCell.columnIndex?l:void 0,g!==r.activeCell.rowIndex&&!Number.isNaN(g)?g:void 0),r.setActiveCell(l,g),r.dispatchEvent("activecellchanged",{cell:r.getVisibleCellByIndex(l,g)}),!o.shiftKey&&r.attributes.selectionFollowsActiveCell&&(A||(r.selections=[]),r.selections[g]=r.selections[g]||[],r.selections[g].push(l),i={selectedData:r.getSelectedData(),selections:r.selections,selectionBounds:r.getSelectionBounds()},Object.defineProperty(i,"selectedData",{get:function(){return r.getSelectedData()}}),r.dispatchEvent("selectionchanged",i)),r.draw(!0))}},r.keyup=function(o){r.dispatchEvent("keyup",{NativeEvent:o,cell:r.currentCell})||!!r.hasFocus},r.keypress=function(o){!r.hasFocus||!r.dispatchEvent("keypress",{NativeEvent:o,cell:r.currentCell})},r.dblclick=function(o){r.dispatchEvent("dblclick",{NativeEvent:o,cell:r.currentCell})||!r.hasFocus||(r.currentCell.context==="ew-resize"&&r.currentCell.style==="columnHeaderCell"?r.fitColumnToValues(r.currentCell.header.name):r.currentCell.context==="ew-resize"&&r.currentCell.style==="cornerCell"?r.autosize():["cell","activeCell"].indexOf(r.currentCell.style)!==-1&&r.currentCell.type!=="checkbox"&&r.beginEditAt(r.currentCell.columnIndex,r.currentCell.rowIndex))},r.scrollWheel=function(o){o.preventDefault();var n,i,a=o,l=o.deltaX===void 0?o.NativeEvent.deltaX:o.deltaX,g=o.deltaY===void 0?o.NativeEvent.deltaY:o.deltaY,A=o.deltaMode===void 0?o.NativeEvent.deltaMode:o.deltaMode,o=o.NativeEvent||o;if(c){a.preventDefault(o);return}r.dispatchEvent("wheel",{NativeEvent:o})||(r.touchHaltAnimation=!0,n=r.scrollBox.scrollLeft,i=r.scrollBox.scrollTop,r.hasFocus&&(A===1&&(g=g*17),(r.scrollBox.scrollTop<r.scrollBox.scrollHeight&&g>0||r.scrollBox.scrollLeft<r.scrollBox.scrollWidth&&l>0||r.scrollBox.scrollTop>0&&g<0||r.scrollBox.scrollLeft>0&&l<0)&&a.preventDefault(o),c=setTimeout(function(){c=void 0,r.scrollBox.scrollTo(l+n,g+i)},1)))},r.pasteData=function(o,n,i,a){var l=r.getVisibleSchema();function g(T,G){if(G==="text/html"){var nt=new DOMParser,At=nt.parseFromString(T,"text/html"),Lt=At.getElementsByTagName("tr"),zt=[],It=(0,_.Z)(Lt),q;try{var d=function(){var w=q.value,W=w.querySelectorAll("td"),O=[];W.forEach(function(V){O.push(V.textContent)}),O.length>0&&zt.push(O)};for(It.s();!(q=It.n()).done;)d()}catch(C){It.e(C)}finally{It.f()}return zt}return T.split(`
`).map(function(C){return[C]})}var A=g(o,n),p=!1;if(A.length===1&&A[0].length===1){var S=A[0][0];r.forEachSelectedCell(function(T,G,nt){r.updateChanges({data:T[G],header:{name:nt},initValue:T[G][nt],value:T[G][nt]}),T[G][nt]=S})}else{for(var v=[],h=0;h<A.length;h++){var E=r.orders.rows[i+h],H=A[h],R=r.data[E];R||(p=!0);var K=Object.assign({},R);v[E]=[];for(var N=0;N<H.length;N++){var $=l[a+N];if(!$){console.warn("Paste data exceeded grid bounds. Skipping.");continue}var gt=$.name,S=H[N];if(S==null){K[gt]=R[gt];continue}v[E].push(a+N),K[gt]=S}r.data[E]=K,p?r.addRow(K,!1):r.updateRow(K)}r.selections=v}var ht=[];return r.selections.forEach(function(T,G){G!==void 0&&T.forEach(function(nt){ht.push([G,nt])})}),r.dispatchEvent("afterpaste",{cells:ht}),p&&(r.refreshFromOriginalData(),r.draw(!0)),A.length},r.getNextVisibleColumnIndex=function(o){var n,i=r.getVisibleSchema();for(n=0;n<i.length;n+=1)if(i[n].columnIndex===o)return i[n+1].columnIndex;return-1},r.getVisibleColumnIndexOf=function(o){var n,i=r.getVisibleSchema();for(n=0;n<i.length;n+=1)if(i[n].columnIndex===o)return n;return-1},r.paste=function(o){if(!!r.attributes.editable){var n=r.dispatchEvent("beforepaste",{NativeEvent:o});if(!n){var i=new Map(Array.from(o.clipboardData.items).map(function(p){return[p.type,p]})),a=["text/html","text/csv","text/plain"],l=a.map(function(p){return i.get(p)}).filter(function(p){return!!p});if(l.length===0){console.warn("Cannot find supported clipboard data type. Supported types are:",a.join(", "));return}var g=l[0],A=g.type;g.getAsString(function(p){r.pasteData(p,A,r.activeCell.rowIndex,r.getVisibleColumnIndexOf(r.activeCell.columnIndex)),r.draw()})}}},r.cut=function(o){r.copy(o),r.forEachSelectedCell(function(n,i,a){n[i][a]=""})},r.copy=function(o){r.dispatchEvent("copy",{NativeEvent:o})||!r.hasFocus||!o.clipboardData||(o.clipboardData.setData("text/html",r.getCopyText("html")),o.clipboardData.setData("text/plain",r.getCopyText("text"," ")),o.preventDefault())}}},39334:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return I}});var _=P(94657),t=P(80717),s=P.n(t);function I(e,r){e.scale=1,e.orders={rows:[],columns:[]},e.appliedInlineStyles={},e.cellGridAttributes={},e.treeGridAttributes={},e.visibleRowHeights=[],e.hasFocus=!1,e.activeCell={columnIndex:0,rowIndex:0},e.innerHTML="",e.storageName="canvasDataGrid",e.invalidSearchExpClass="canvas-datagrid-invalid-search-regExp",e.localStyleLibraryStorageKey="canvas-datagrid-user-style-library",e.dataType="application/x-canvas-datagrid",e.orderBy=null,e.orderings={columns:[],add:function(n,i,a){if(e.orderings.columns=e.orderings.columns.filter(function(g){return g.orderBy!==n}),i){e.orderings.columns.push({orderBy:n,orderDirection:i,sortFunction:a});var l=[];e.schema.filter(function(g){return!!e.orderings.columns.find(function(A){return A.orderBy===g.name})}).forEach(function(g){l.push(e.orderings.columns.find(function(A){return A.orderBy===g.name}))}),e.orderings.columns=l}},sort:function(){var n;e.orderings.columns.forEach(function(i,a){a===0?n=(0,t.firstBy)(i.sortFunction(i.orderBy,i.orderDirection)):n=n.thenBy(i.sortFunction(i.orderBy,i.orderDirection))}),n&&e.data.sort(n)}},e.columnFilters={},e.filters={},e.frozenRow=0,e.frozenColumn=0,e.ellipsisCache={},e.scrollCache={x:[],y:[]},e.scrollBox={},e.visibleRows=[],e.visibleCells=[],e.sizes={rows:{},columns:{},trees:{}},e.currentFilter=function(){return!0},e.selections=[],e.hovers={},e.attributes={},e.style={},e.formatters={},e.sorters={},e.parsers={},e.schemaHashes={},e.events={},e.changes={insert:[],update:[],remove:[]},e.scrollIndexTop=0,e.scrollPixelTop=0,e.scrollIndexLeft=0,e.scrollPixelLeft=0,e.childGrids={},e.openChildren={},e.scrollModes=["vertical-scroll-box","vertical-scroll-top","vertical-scroll-bottom","horizontal-scroll-box","horizontal-scroll-right","horizontal-scroll-left"],e.componentL1Events={},e.eventNames=["activecellchanged","afterdraw","afterrendercell","attributechanged","beforebeginedit","beforecreatecellgrid","beforedraw","beforeendedit","beforerendercell","beforerendercellgrid","beginedit","cellmouseout","cellmouseover","click","collapsetree","contextmenu","copy","datachanged","dblclick","endedit","expandtree","formatcellvalue","keydown","keypress","keyup","mousedown","mousemove","mouseup","newrow","ordercolumn","rendercell","rendercellgrid","renderorderbyarrow","rendertext","rendertreearrow","reorder","reordering","resize","resizecolumn","resizerow","schemachanged","scroll","singlerecordview","selectionchanged","stylechanged","touchcancel","touchend","touchmove","touchstart","wheel"],e.mouse={x:0,y:0},e.getSelectedData=function(o){var n=[],i=e.getSchema(),a=e.data.length;return a===0?[]:(e.selections.forEach(function(l,g){if(!!l&&g!==a){if(l.length===0){n[g]=null;return}n[g]={},l.forEach(function(A){var p;A===-1||!i[A]||(p=e.orders.columns[A],!(!o&&i[p].hidden)&&e.data[g]&&(n[g][i[p].name]=e.data[g][i[p].name],n[g]._i=e.data[g]._i))})}}),n)},e.getColumnHeaderCellHeight=function(){return e.attributes.showColumnHeaders?(e.sizes.rows[-1]||e.style.columnHeaderCellHeight)*e.scale:0},e.getRowHeaderCellWidth=function(){return e.attributes.showRowHeaders?(e.sizes.columns[-1]||e.style.rowHeaderCellWidth)*e.scale:0},e.setStorageData=function(){if(!(!e.attributes.saveAppearance||!e.attributes.name)){var o={};e.getSchema().forEach(function(n){o[n.name]=!n.hidden}),localStorage.setItem(e.storageName+"-"+e.attributes.name,JSON.stringify({sizes:{rows:e.sizes.rows,columns:e.sizes.columns},orders:{rows:e.orders.rows,columns:e.orders.columns},orderBy:e.orderBy,orderDirection:e.orderDirection,visibility:o}))}},e.getSchema=function(){return e.schema||e.tempSchema||[]};function c(o,n){var i=[],a;for(a=o;a<=n;a+=1)i[a]=a;return i}e.createColumnOrders=function(){var o=e.getSchema();e.orders.columns=c(0,o.length-1)},e.createRowOrders=function(){e.orders.rows=c(0,e.data.length-1)},e.getVisibleSchema=function(){return e.getSchema().filter(function(o){return!o.hidden})},e.applyDefaultValue=function(o,n){var i=n.defaultValue||"";typeof i=="function"&&(i=i.apply(e.intf,[n])),o[n.name]=i},e.createNewRowData=function(){e.newRow={},e.getSchema().forEach(function(n){e.applyDefaultValue(e.newRow,n)})},e.getSchemaNameHash=function(o){for(var n=0;e.schemaHashes[o];)n+=1,o=o+n;return o},e.filter=function(o){var n=e.filters[o];return!n&&o!==void 0&&(console.warn("Cannot find filter for type %s, falling back to substring match.",o),n=e.filters.string),n},e.applyFilter=function(){e.refreshFromOriginalData(),Object.keys(e.columnFilters).forEach(function(o){var n=e.getHeaderByName(o);!n||(e.currentFilter=n.filter||e.filter(n.type||"string"),e.data=e.data.filter(function(i){var a=e.columnFilters[o];return a?a.split(",").filter(function(l){return!!l}).some(function(l){return e.currentFilter(i[o],l)}):!0}))}),e.resize()},e.applyDataTransforms=function(o){e.applyFilter(),e.orderings.sort(),o&&e.draw(!0)},e.getBestGuessDataType=function(o,n){var i,a,l=n.length;for(a=0;a<l;a+=1)if(n[a]!==void 0&&n[a]!==null&&[null,void 0].indexOf(n[a][o])!==-1)return i=typeof n[a],i==="object"?"string":i;return"string"},e.drawChildGrids=function(){Object.keys(e.childGrids).forEach(function(o){e.childGrids[o].draw()})},e.resizeChildGrids=function(){Object.keys(e.childGrids).forEach(function(o){e.childGrids[o].resize()})},e.autoScrollZone=function(o,n,i,a){var l,g=e.getRowHeaderCellWidth(),A=e.getColumnHeaderCellHeight();i!==-1&&(n>e.width-e.attributes.selectionScrollZone&&n<e.width&&(e.scrollBox.scrollLeft+=e.attributes.selectionScrollIncrement,l=!0),n-e.attributes.selectionScrollZone-g<0&&(e.scrollBox.scrollLeft-=e.attributes.selectionScrollIncrement,l=!0)),i!==-1&&(i>e.height-e.attributes.selectionScrollZone&&i<e.height&&(e.scrollBox.scrollTop+=e.attributes.selectionScrollIncrement,l=!0),i-e.attributes.selectionScrollZone-A<0&&(e.scrollBox.scrollTop-=e.attributes.selectionScrollIncrement,l=!0)),l&&!a&&e.currentCell&&e.currentCell.columnIndex!==-1&&(e.scrollTimer=setTimeout(e.mousemove,e.attributes.scrollRepeatRate,o))},e.refreshFromOriginalData=function(){e.data=e.originalData.filter(function(o){return!0})},e.validateColumn=function(o,n){if(!o.name)throw new Error("A column must contain at least a name.");if(n.filter(function(i){return i.name===o.name}).length>0)throw new Error("A column with the name "+o.name+" already exists and cannot be added again.");return!0},e.setDefaults=function(o,n,i,a){o[i]=n[i]===void 0?a:n[i]},e.setAttributes=function(){e.defaults.attributes.forEach(function(n){e.setDefaults(e.attributes,e.args,n[0],n[1])})},e.setStyle=function(){e.defaults.styles.forEach(function(n){e.setDefaults(e.style,e.args.style||{},n[0],n[1])})},e.autosize=function(o){e.getVisibleSchema().forEach(function(n,i){(n.name===o||o===void 0)&&(e.sizes.columns[i]=Math.max(e.findColumnMaxTextLength(n.name),e.style.minColumnWidth))}),e.sizes.columns[-1]=e.findColumnMaxTextLength("cornerCell")},e.dispose=function(){!e.isChildGrid&&e.canvas&&e.canvas.parentNode&&e.canvas.parentNode.removeChild(e.canvas),e.isChildGrid||document.body.removeChild(e.controlInput),e.eventParent.removeEventListener("mouseup",e.mouseup,!1),e.eventParent.removeEventListener("mousedown",e.mousedown,!1),e.eventParent.removeEventListener("dblclick",e.dblclick,!1),e.eventParent.removeEventListener("click",e.click,!1),e.eventParent.removeEventListener("mousemove",e.mousemove),e.eventParent.removeEventListener("wheel",e.scrollWheel,!1),e.canvas.removeEventListener("contextmenu",e.contextmenu,!1),e.canvas.removeEventListener("copy",e.copy),e.controlInput.removeEventListener("copy",e.copy),e.controlInput.removeEventListener("cut",e.cut),e.controlInput.removeEventListener("paste",e.paste),e.controlInput.removeEventListener("keypress",e.keypress,!1),e.controlInput.removeEventListener("keyup",e.keyup,!1),e.controlInput.removeEventListener("keydown",e.keydown,!1),window.removeEventListener("resize",e.resize),e.intf.observer&&e.intf.observer.disconnect&&e.intf.observer.disconnect()},e.tryLoadStoredSettings=function(){var o;e.reloadStoredValues(),e.storedSettings&&typeof e.storedSettings.orders=="object"&&e.storedSettings.orders!==null&&(e.storedSettings.orders.rows.length>=(e.data||[]).length&&(e.orders.rows=e.storedSettings.orders.rows),o=e.getSchema(),e.storedSettings.orders.columns.length===o.length&&(e.orders.columns=e.storedSettings.orders.columns),e.orderBy=e.storedSettings.orderBy===void 0?o[0].name:e.storedSettings.orderBy,e.orderDirection=e.storedSettings.orderDirection===void 0?"asc":e.storedSettings.orderDirection,e.storedSettings.orderBy!==void 0&&e.getHeaderByName(e.orderBy)&&e.orderDirection&&e.order(e.orderBy,e.orderDirection))},e.getDomRoot=function(){return e.shadowRoot?e.shadowRoot.host:e.parentNode},e.getFontName=function(o){return o.replace(/\d+\.?\d*px/,"")},e.getFontHeight=function(o){return parseFloat(o,10)},e.parseStyleValue=function(o){if(/Font/.test(o)){e.style[o+"Height"]=e.getFontHeight(e.style[o]),e.style[o+"Name"]=e.getFontName(e.style[o]);return}o==="moveOverlayBorderSegments"&&typeof e.style[o]=="string"&&(e.style[o]=e.style[o].split(",").map(function(n){return parseInt(n,10)}))},e.initProp=function(o){!e.args[o]||Object.keys(e.args[o]).forEach(function(n){e[o][n]=e.args[o][n]})},e.getStyleProperty=function(o){return e.styleKeys.indexOf(o)===-1?e.parentNodeStyle[o]:e.style[o]},e.setStyleProperty=function(o,n,i){var a=["height","width","minHeight","minWidth","maxHeight","maxWidth"].indexOf(o)!==-1;e.styleKeys.indexOf(o)===-1?e.parentNodeStyle[o]=n:(/-/.test(o)&&(o=e.dehyphenateProperty(o)),e.style[o]=n,e.parseStyleValue(o)),a&&e.resize(),i||(e.draw(!0),e.dispatchEvent("stylechanged",{name:"style",value:n}))},e.reloadStoredValues=function(){if(e.attributes.name&&e.attributes.saveAppearance){try{e.storedSettings=localStorage.getItem(e.storageName+"-"+e.attributes.name)}catch(o){console.warn("Error loading stored values. "+o.message),e.storedSettings=void 0}if(e.storedSettings)try{e.storedSettings=JSON.parse(e.storedSettings)}catch(o){console.warn("could not read settings from localStore",o),e.storedSettings=void 0}e.storedSettings&&(typeof e.storedSettings.sizes=="object"&&e.storedSettings.sizes!==null&&(e.sizes.rows=e.storedSettings.sizes.rows,e.sizes.columns=e.storedSettings.sizes.columns,["trees","columns","rows"].forEach(function(o){e.sizes[o]||(e.sizes[o]={})})),typeof e.storedSettings.visibility=="object"&&e.getSchema().forEach(function(o){e.storedSettings.visibility&&e.storedSettings.visibility[o.name]!==void 0&&(o.hidden=!e.storedSettings.visibility[o.name])}))}},e.init=function(){if(e.initialized)return;function o(i){e.styleKeys.indexOf(i)===-1&&e.styleKeys.push(i)}var n={};return e.setAttributes(),e.setStyle(),e.initScrollBox(),e.setDom(),e.nodeType="canvas-datagrid",e.ie=/Trident/.test(window.navigator.userAgent),e.edge=/Edge/.test(window.navigator.userAgent),e.webKit=/WebKit/.test(window.navigator.userAgent),e.moz=/Gecko/.test(window.navigator.userAgent),e.mobile=/Mobile/i.test(window.navigator.userAgent),e.blankValues=[void 0,null,""],e.cursorGrab="grab",e.cursorGrabing="grabbing",e.cursorGrab=e.webKit?"-webkit-grab":e.cursorGrab,e.cursorGrabing=e.moz?"-webkit-grabbing":e.cursorGrabbing,e.pointerLockPosition={x:0,y:0},Object.keys(e.style).forEach(e.parseStyleValue),e.intf.self=e,e.intf.moveSelection=e.moveSelection,e.intf.moveTo=e.moveTo,e.intf.addEventListener=e.addEventListener,e.intf.removeEventListener=e.removeEventListener,e.intf.dispatchEvent=e.dispatchEvent,e.intf.dispose=e.dispose,e.intf.appendTo=e.appendTo,e.intf.getVisibleCellByIndex=e.getVisibleCellByIndex,e.intf.filters=e.filters,e.intf.sorters=e.sorters,e.intf.autosize=e.autosize,e.intf.beginEditAt=e.beginEditAt,e.intf.endEdit=e.endEdit,e.intf.setActiveCell=e.setActiveCell,e.intf.forEachSelectedCell=e.forEachSelectedCell,e.intf.scrollIntoView=e.scrollIntoView,e.intf.clearChangeLog=e.clearChangeLog,e.intf.gotoCell=e.gotoCell,e.intf.gotoRow=e.gotoRow,e.intf.getHeaderByName=e.getHeaderByName,e.intf.findColumnScrollLeft=e.findColumnScrollLeft,e.intf.findRowScrollTop=e.findRowScrollTop,e.intf.fitColumnToValues=e.fitColumnToValues,e.intf.findColumnMaxTextLength=e.findColumnMaxTextLength,e.intf.disposeContextMenu=e.disposeContextMenu,e.intf.getCellAt=e.getCellAt,e.intf.isCellVisible=e.isCellVisible,e.intf.isRowVisible=e.isRowVisible,e.intf.isColumnVisible=e.isColumnVisible,e.intf.order=e.order,e.intf.draw=e.draw,e.intf.isComponent=e.isComponent,e.intf.selectArea=e.selectArea,e.intf.clipElement=e.clipElement,e.intf.getSchemaFromData=e.getSchemaFromData,e.intf.setFilter=e.setFilter,e.intf.selectRow=e.selectRow,e.intf.parentGrid=e.parentGrid,e.intf.toggleTree=e.toggleTree,e.intf.expandTree=e.expandTree,e.intf.collapseTree=e.collapseTree,e.intf.canvas=e.canvas,e.intf.context=e.ctx,e.intf.insertRow=e.insertRow,e.intf.deleteRow=e.deleteRow,e.intf.addRow=e.addRow,e.intf.insertColumn=e.insertColumn,e.intf.deleteColumn=e.deleteColumn,e.intf.addColumn=e.addColumn,e.intf.getClippingRect=e.getClippingRect,e.intf.setRowHeight=e.setRowHeight,e.intf.setColumnWidth=e.setColumnWidth,e.intf.resetColumnWidths=e.resetColumnWidths,e.intf.resetRowHeights=e.resetRowHeights,e.intf.resize=e.resize,e.intf.selectColumn=e.selectColumn,e.intf.selectRow=e.selectRow,e.intf.selectAll=e.selectAll,e.intf.selectNone=e.selectNone,e.intf.drawChildGrids=e.drawChildGrids,e.intf.assertPxColor=e.assertPxColor,e.intf.clearPxColorAssertions=e.clearPxColorAssertions,e.intf.integerToAlpha=e.integerToAlpha,e.intf.copy=e.copy,e.intf.paste=e.paste,e.intf.setStyleProperty=e.setStyleProperty,e.intf.singleRecordViewRowIndex=e.singleRecordViewRowIndex,e.intf.activeCell=e.activeCell,e.intf.columnFilters=e.columnFilters,e.intf.applySetting=e.applySetting,e.intf.resetSetting=e.resetSetting,e.intf.syncSetting=e.syncSetting,e.intf.resetChanges=e.resetChanges,e.intf.getCopyText=e.getCopyText,Object.defineProperty(e.intf,"defaults",{get:function(){return{styles:e.defaults.styles.reduce(function(a,l){return a[l[0]]=l[1],a},{}),attributes:e.defaults.attributes.reduce(function(a,l){return a[l[0]]=l[1],a},{})}}}),e.styleKeys=Object.keys(e.intf.defaults.styles),e.styleKeys.map(function(i){return e.hyphenateProperty(i,!1)}).forEach(o),e.styleKeys.map(function(i){return e.hyphenateProperty(i,!0)}).forEach(o),e.DOMStyles=window.getComputedStyle(document.body,null),e.styleKeys.concat(Object.keys(e.DOMStyles)).forEach(function(i){n[i]=void 0,Object.defineProperty(n,i,{get:function(){return e.getStyleProperty(i)},set:function(l){e.initialized&&(e.appliedInlineStyles[i]=l),e.setStyleProperty(i,l)}})}),Object.defineProperty(e.intf,"shadowRoot",{get:function(){return e.shadowRoot}}),Object.defineProperty(e.intf,"activeCell",{get:function(){return e.activeCell},set:function(a){a&&(e.activeCell=a)}}),Object.defineProperty(e.intf,"hasFocus",{get:function(){return e.hasFocus}}),Object.defineProperty(e.intf,"style",{get:function(){return n},set:function(a){e.defaults.styles.forEach(function(l){var g=(0,_.Z)(l,2),A=g[0],p=g[1];e.setStyleProperty(A,p,!0)}),Object.keys(a).forEach(function(l){e.setStyleProperty(l,a[l],!0)}),e.draw(!0),e.dispatchEvent("stylechanged",{name:"style",value:a})}}),Object.defineProperty(e.intf,"attributes",{value:{}}),Object.keys(e.attributes).forEach(function(i){Object.defineProperty(e.intf.attributes,i,{get:function(){return e.attributes[i]},set:function(l){e.attributes[i]=l,i==="name"&&e.tryLoadStoredSettings(),e.draw(!0),e.dispatchEvent("attributechanged",{name:i,value:l[i]})}})}),e.filters.string=function(i,a){if(a===e.attributes.blanksText)return e.blankValues.includes(i==null?i:String(i).trim());i=String(i);var l,g=/\/(i|g|m)*$/,A=g.exec(a),p=A?A[0].substring(1):"",S=p.length;if(e.invalidFilterRegEx=void 0,a.substring(0,1)==="/"&&A){try{l=new RegExp(a.substring(1,a.length-(S+1)),p)}catch(v){e.invalidFilterRegEx=v;return}return l.test(i)}return i.toString?i.toString().toLocaleUpperCase().indexOf(a.toLocaleUpperCase())!==-1:!1},e.filters.number=function(i,a){return a===e.attributes.blanksText?e.blankValues.includes(i==null?i:String(i).trim()):a?i==a:!0},e.filters.date=e.filters.string,e.filters.time=e.filters.string,e.filters.timestamp=e.filters.string,e.filters.checkbox=e.filters.string,e.filters.select=e.filters.string,e.filters.multiSelect=e.filters.string,e.filters.image=e.filters.string,e.filters.user=e.filters.string,e.filters.dept=e.filters.string,["formatters","filters","sorters"].forEach(e.initProp),e.applyComponentStyle(!1,e.intf),e.reloadStoredValues(),e.args.data&&(e.intf.data=e.args.data),(e.intf.innerText||e.intf.textContent)&&(e.intf.dataType==="application/x-canvas-datagrid"&&(e.intf.dataType="application/json+x-canvas-datagrid"),e.intf.data=e.intf.innerText||e.intf.textContent),e.args.schema&&(e.intf.schema=e.args.schema),e.isChildGrid||!e.isComponent?requestAnimationFrame(function(){e.resize(!0)}):e.resize(!0),e.initialized=!0,e},e.intf.blur=function(o){e.hasFocus=!1},e.intf.focus=function(){e.hasFocus=!0,e.controlInput.focus()},(e.shadowRoot||e.isChildGrid)&&(Object.defineProperty(e.intf,"height",{get:function(){return e.shadowRoot?e.shadowRoot.height:e.parentNode.height},set:function(n){e.shadowRoot?e.shadowRoot.height=n:e.parentNode.height=n,e.resize(!0)}}),Object.defineProperty(e.intf,"width",{get:function(){return e.shadowRoot?e.shadowRoot.width:e.parentNode.width},set:function(n){e.shadowRoot?e.shadowRoot.width=n:e.parentNode.width=n,e.resize(!0)}}),Object.defineProperty(e.intf,"parentNode",{get:function(){return e.parentNode},set:function(n){if(!e.isChildGrid)throw new TypeError("Cannot set property parentNode which has only a getter");e.parentNode=n}})),Object.defineProperty(e.intf,"visibleRowHeights",{get:function(){return e.visibleRowHeights}}),Object.defineProperty(e.intf,"openChildren",{get:function(){return e.openChildren}}),Object.defineProperty(e.intf,"childGrids",{get:function(){return Object.keys(e.childGrids).map(function(n){return e.childGrids[n]})}}),Object.defineProperty(e.intf,"isChildGrid",{get:function(){return e.isChildGrid}}),Object.defineProperty(e,"cursor",{get:function(){return e.parentNodeStyle.cursor},set:function(n){n==="cell"&&(n="default"),e.currentCursor!==n&&(e.parentNodeStyle.cursor=n,e.currentCursor=n)}}),Object.defineProperty(e.intf,"orderDirection",{get:function(){return e.orderDirection},set:function(n){n!=="desc"&&(n="asc"),e.orderDirection=n,e.order(e.orderBy,e.orderDirection)}}),Object.defineProperty(e.intf,"orderBy",{get:function(){return e.orderBy},set:function(n){if(e.getSchema().find(function(i){return i.name===n})===void 0)throw new Error("Cannot sort by unknown column name.");e.orderBy=n,e.order(e.orderBy,e.orderDirection)}}),e.isComponent&&(Object.defineProperty(e.intf,"offsetHeight",{get:function(){return e.canvas.offsetHeight}}),Object.defineProperty(e.intf,"offsetWidth",{get:function(){return e.canvas.offsetWidth}})),Object.defineProperty(e.intf,"scrollHeight",{get:function(){return e.scrollBox.scrollHeight}}),Object.defineProperty(e.intf,"scrollWidth",{get:function(){return e.scrollBox.scrollWidth}}),Object.defineProperty(e.intf,"scrollTop",{get:function(){return e.scrollBox.scrollTop},set:function(n){e.scrollBox.scrollTop=n}}),Object.defineProperty(e.intf,"scrollLeft",{get:function(){return e.scrollBox.scrollLeft},set:function(n){e.scrollBox.scrollLeft=n}}),Object.defineProperty(e.intf,"sizes",{get:function(){return e.sizes}}),Object.defineProperty(e.intf,"parentDOMNode",{get:function(){return e.parentDOMNode}}),Object.defineProperty(e.intf,"input",{get:function(){return e.input}}),Object.defineProperty(e.intf,"controlInput",{get:function(){return e.controlInput}}),Object.defineProperty(e.intf,"currentCell",{get:function(){return e.currentCell}}),Object.defineProperty(e.intf,"visibleCells",{get:function(){return e.visibleCells}}),Object.defineProperty(e.intf,"visibleRows",{get:function(){return e.visibleRows}}),Object.defineProperty(e.intf,"selections",{get:function(){return e.selections},set:function(n){n&&n.length>0&&(e.selections=n)}}),Object.defineProperty(e.intf,"dragMode",{get:function(){return e.dragMode}}),e.intf.formatters=e.formatters,Object.defineProperty(e.intf,"dataType",{get:function(){return e.dataType},set:function(n){if(!e.parsers[n])throw new Error("No parser for MIME type "+n);e.dataType=n}}),e.eventNames.forEach(function(o){Object.defineProperty(e.intf,"on"+o,{get:function(){return e.componentL1Events[o]},set:function(i){e.events[o]=[],e.componentL1Events[o]=i,!!i&&e.addEventListener(o,i)}})}),Object.defineProperty(e.intf,"frozenRow",{get:function(){return e.frozenRow},set:function(n){if(isNaN(n))throw new TypeError("Expected value for frozenRow to be a number.");if(e.visibleRows.length<n)throw new RangeError("Cannot set a value larger than the number of visible rows.");e.frozenRow=n}}),Object.defineProperty(e.intf,"frozenColumn",{get:function(){return e.frozenColumn},set:function(n){if(isNaN(n))throw new TypeError("Expected value for frozenRow to be a number.");if(e.getVisibleSchema().length<n)throw new RangeError("Cannot set a value larger than the number of visible columns.");e.frozenColumn=n}}),Object.defineProperty(e.intf,"scrollIndexRect",{get:function(){return{top:e.scrollIndexTop,right:e.scrollIndexRight,bottom:e.scrollIndexBottom,left:e.scrollIndexLeft}}}),Object.defineProperty(e.intf,"scrollPixelRect",{get:function(){return{top:e.scrollPixelTop,right:e.scrollPixelRight,bottom:e.scrollPixelBottom,left:e.scrollPixelLeft}}}),Object.defineProperty(e.intf,"rowOrder",{get:function(){return e.orders.rows},set:function(n){if(!Array.isArray(n))throw new TypeError("Value must be an array.");if(!e.data||n.length<e.data.length)throw new RangeError("Array length must be equal to or greater than number of rows.");e.orders.rows=n}}),Object.defineProperty(e.intf,"columnOrder",{get:function(){return e.orders.columns},set:function(n){if(!Array.isArray(n))throw new TypeError("Value must be an array.");if(n.length<e.getSchema().length)throw new RangeError("Array length must be equal to or greater than number of columns.");e.orders.columns=n}}),Object.defineProperty(e.intf,"selectionBounds",{get:function(){return e.getSelectionBounds()}}),Object.defineProperty(e.intf,"selectedRows",{get:function(){return e.getSelectedData(!0)}}),Object.defineProperty(e.intf,"selectedCells",{get:function(){return e.getSelectedData()}}),Object.defineProperty(e.intf,"visibleSchema",{get:function(){return e.getVisibleSchema().map(function(i){return i})}}),Object.defineProperty(e.intf,"treeGridAttributes",{get:function(){return e.treeGridAttributes},set:function(n){e.treeGridAttributes=n}}),Object.defineProperty(e.intf,"cellGridAttributes",{get:function(){return e.cellGridAttributes},set:function(n){e.cellGridAttributes=n}}),Object.defineProperty(e.intf,"ctx",{get:function(){return e.ctx}}),Object.defineProperty(e.intf,"schema",{get:function(){return e.getSchema()},set:function(n){if(n===void 0){e.schema=void 0,e.tempSchema=void 0,e.dispatchEvent("schemachanged",{schema:void 0});return}if(!Array.isArray(n)||typeof n[0]!="object")throw new Error("Schema must be an array of objects.");if(n[0].name===void 0)throw new Error("Expected schema to contain an object with at least a name property.");e.schema=n.map(function(a,l){return a.width=a.width||e.style.cellWidth,a.filter=a.filter||e.filter(a.type),a.type=a.type||"string",a.index=l,a.columnIndex=l,a.rowIndex=-1,a}),e.tempSchema=void 0,e.createNewRowData(),e.createColumnOrders(),e.tryLoadStoredSettings(),e.storedSettings&&typeof e.storedSettings.visibility=="object"&&e.schema.forEach(function(a,l){e.storedSettings&&e.storedSettings.visibility[a.name]!==void 0&&(a.hidden=!e.storedSettings.visibility[a.name])}),e.resize(!0),e.dispatchEvent("schemachanged",{schema:e.schema})}}),e.intf.getTypes=function(){return Object.keys(e.parsers)},e.parseInnerHtml=function(o){if(!o||/^ +$/.test(o))return[];try{o=JSON.parse(o)}catch(n){console.warn(Error("Cannot parse application/json+x-canvas-datagrid formated data. "+n.message+`  
Note: canvas-datagrid.innerHTML is for string data only.  Use the canvas-datagrid.data property to set object data.`))}return o},e.parsers["application/json+x-canvas-datagrid"]=function(o,n){e.parsers["application/x-canvas-datagrid"](e.parseInnerHtml(o),function(i,a){return n(i,a)})},e.parsers["application/x-canvas-datagrid"]=function(o,n){return n(o)},e.intf.parsers=e.parsers,e.etl=function(o,n){if(!e.intf.parsers[e.dataType])throw new Error("Unsupported data type.");e.intf.parsers[e.dataType](o,function(i,a){Array.isArray(a)&&(e.schema=a),e.attributes.autoGenerateSchema&&(e.schema=e.getSchemaFromData(i)),e.schema||(e.tempSchema=e.getSchemaFromData(i)),e.getSchema()&&e.createColumnOrders(),e.originalData=i,i&&i.forEach(function(l,g){l._i===void 0&&(l._i=g)}),e.applyDataTransforms(),!e.schema&&(e.data||[]).length===0&&(e.tempSchema=[{name:""}]),e.fitColumnToValues("cornerCell",!0),(e.tempSchema&&!e.schema||e.attributes.autoGenerateSchema)&&(e.createColumnOrders(),e.dispatchEvent("schemachanged",{schema:e.tempSchema})),n()})},Object.defineProperty(e.intf,"data",{get:function(){return e.data},set:function(n){e.etl(n,function(){e.changes={insert:[],remove:[],update:[]},e.createNewRowData(),e.attributes.autoResizeColumns&&e.data.length>0&&e.storedSettings===void 0&&e.autosize(),e.fitColumnToValues("cornerCell",!0),e.createRowOrders(),e.tryLoadStoredSettings(),e.resize(!0)})}}),Object.defineProperty(e.intf,"changes",{get:function(){return e.changes},set:function(n){e.changes=n,e.resize(!0)}}),Object.defineProperty(e.intf,"singleRecordViewRowIndex",{get:function(){return e.singleRecordViewRowIndex},set:function(n){e.singleRecordViewRowIndex=n,e.dispatchEvent("singlerecordview",{rowIndex:n,needRefresh:!!e.needRefresh}),e.needRefresh=!1}}),e.initScrollBox=function(){var o=0,n=0,i=0,a=0,l=0,g=0,A=20,p=20;function S(h,E){if(isNaN(h))throw new Error("ScrollTop value must be a number");h<0&&(h=0),h>l&&(h=l),l<0&&(h=0),i=h,E||e.scroll()}function v(h,E){if(isNaN(h))throw new Error("ScrollLeft value must be a number");h<0&&(h=0),h>g&&(h=g),g<0&&(h=0),a=h,E||e.scroll()}e.scrollBox.toString=function(){return'{"width": '+g.toFixed(2)+', "height": '+l.toFixed(2)+', "left": '+a.toFixed(2)+', "top": '+i.toFixed(2)+', "widthRatio": '+e.scrollBox.widthBoxRatio.toFixed(5)+', "heightRatio": '+e.scrollBox.heightBoxRatio.toFixed(5)+"}"},e.scrollBox.scrollTo=function(h,E,H){v(h,!0),S(E,H)},Object.defineProperty(e.scrollBox,"scrollBoxHeight",{get:function(){return A},set:function(E){A=E}}),Object.defineProperty(e.scrollBox,"scrollBoxWidth",{get:function(){return p},set:function(E){p=E}}),Object.defineProperty(e.scrollBox,"height",{get:function(){return o},set:function(E){o=E}}),Object.defineProperty(e.scrollBox,"width",{get:function(){return n},set:function(E){n=E}}),Object.defineProperty(e.scrollBox,"scrollTop",{get:function(){return i},set:S}),Object.defineProperty(e.scrollBox,"scrollLeft",{get:function(){return a},set:v}),Object.defineProperty(e.scrollBox,"scrollHeight",{get:function(){return l},set:function(E){i>E&&(i=Math.max(E,0)),l=E}}),Object.defineProperty(e.scrollBox,"scrollWidth",{get:function(){return g},set:function(E){a>E&&(a=Math.max(E,0)),g=E}})}}},29006:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,{Z:function(){return canvasDatagrid}});var _component__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(37398),_defaults__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(30020),_draw__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(73861),_events__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(10505),_touch__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(5771),_intf__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(39334),_contextMenu__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(22873),_dom__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(99050),_publicMethods__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__(85432),webComponent=(0,_component__WEBPACK_IMPORTED_MODULE_2__.Z)(),modules=[_defaults__WEBPACK_IMPORTED_MODULE_3__.Z,_draw__WEBPACK_IMPORTED_MODULE_4__.Z,_events__WEBPACK_IMPORTED_MODULE_0__.Z,_touch__WEBPACK_IMPORTED_MODULE_5__.Z,_intf__WEBPACK_IMPORTED_MODULE_1__.Z,_contextMenu__WEBPACK_IMPORTED_MODULE_6__.Z,_dom__WEBPACK_IMPORTED_MODULE_7__.Z,_publicMethods__WEBPACK_IMPORTED_MODULE_8__.Z];function Grid(args){args=args||{};var self={};return self.isComponent=args.component===void 0,self.isChildGrid=args.parentNode&&/canvas-sqlzero-(cell|tree)/.test(args.parentNode.nodeType),self.isChildGrid?self.intf={}:self.intf=self.isComponent?eval("Reflect.construct(HTMLElement, [], new.target)"):document.createElement("canvas"),self.args=args,self.intf.args=args,self.applyComponentStyle=webComponent.applyComponentStyle,self.hyphenateProperty=webComponent.hyphenateProperty,self.dehyphenateProperty=webComponent.dehyphenateProperty,self.observer=webComponent.observe,self.createGrid=function(rt){return rt.component=!1,new Grid(rt)},modules.forEach(function(et){et(self)}),self.isChildGrid?(self.shadowRoot=args.parentNode.shadowRoot,self.parentNode=args.parentNode):(self.shadowRoot=self.intf.attachShadow({mode:"open"}),self.parentNode=self.shadowRoot),self.init(),self.intf}window.HTMLElement&&(Grid.prototype=Object.create(window.HTMLElement.prototype)),window.customElements&&(Grid.observedAttributes=webComponent.getObservableAttributes(),Grid.prototype.disconnectedCallback=webComponent.disconnectedCallback,Grid.prototype.attributeChangedCallback=webComponent.attributeChangedCallback,Grid.prototype.connectedCallback=webComponent.connectedCallback,Grid.prototype.adoptedCallback=webComponent.adoptedCallback,window.customElements.define("canvas-sqlzero",Grid)),window&&!window.canvasDatagrid&&!window.require&&!window.EXCLUDE_GLOBAL&&(window.canvasDatagrid=function(et){return new Grid(et)});function canvasDatagrid(et){et=et||{};var rt,P=["style","formatters","sorters","filters","treeGridAttributes","cellGridAttributes","data","schema","singleRecordViewRowIndex"];return window.customElements?(rt=document.createElement("canvas-sqlzero"),Object.keys(et).forEach(function(_){if(_!=="data"&&_!=="parentNode"){if(P.indexOf(_)!==-1){P.forEach(function(t){et[t]===void 0||t!==_||(["formatters","sorters","filters"].indexOf(_)!==-1?typeof et[t]=="object"&&et[t]!==null&&Object.keys(et[t]).forEach(function(s){rt[t][s]=et[t][s]}):rt[t]=et[t])});return}rt.attributes[_]=et[_]}}),et.data&&(rt.data=et.data),et.parentNode&&et.parentNode.appendChild(rt),rt):(et.component=!1,rt=new Grid(et),et.parentNode&&et.parentNode.appendChild&&et.parentNode.appendChild(rt),rt)}},85432:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return I}});var _=P(93224),t=P(83279),s=["_i"];function I(e){e.integerToAlpha=function(r){for(var c="a".charCodeAt(0),o="z".charCodeAt(0),n=o-c+1,i="";r>=0;)i=String.fromCharCode(r%n+c)+i,r=Math.floor(r/n)-1;return i},e.insertColumn=function(r,c){var o=e.getSchema();if(o.length<c)throw new Error("Index is beyond the length of the schema.");e.validateColumn(r,o),o.splice(c,0,r),e.data.forEach(function(n){e.applyDefaultValue(n,r)}),e.intf.schema=o},e.deleteColumn=function(r){var c=e.getSchema();e.data.forEach(function(o){delete o[c[r].name]}),c.splice(r,1),e.intf.schema=c},e.addColumn=function(r){var c=e.getSchema();e.validateColumn(r,c),c.push(r),e.data.forEach(function(o){e.applyDefaultValue(o,r)}),e.intf.schema=c},e.deleteSelection=function(){var r=e.getSelectedData();r.forEach(function(c){c&&c._i!==void 0&&e.deleteRow(!0,c._i)}),e.selections=[]},e.deleteRow=function(r,c){function o(n){var i=e.changes.insert.findIndex(function(l){return l===n}),a=e.changes.remove.findIndex(function(l){return l===n});r&&i===-1?a===-1?e.changes.remove.push(n):e.changes.remove.splice(a,1):(i!==-1?e.changes.insert.splice(i,1):e.changes.remove.push(n),i=e.originalData.findIndex(function(l){return l._i===n}),e.originalData.splice(i,1))}e.singleRecordViewRowIndex>-1||(c===void 0?e.getSelectedData().filter(function(n){return!!n}).forEach(function(n){c=n._i,o(c)}):o(c),e.dispatchEvent("datachanged",e.changes),e.setFilter(),e.resize(!0))},e.insertRow=function(r,c){if(e.originalData.length<c)throw new Error("Index is beyond the length of the dataset.");e.originalData.splice(c,0,r),e.getSchema().forEach(function(o){r[o.name]===void 0&&e.applyDefaultValue(e.originalData[c],o)}),e.setFilter(),e.resize(!0)},e.addRow=function(r){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!(e.singleRecordViewRowIndex>-1)){r||(r={});var o;return e.originalData.length===0?o=0:o=Math.max.apply(Math,(0,t.Z)(e.originalData.map(function(n){return n._i})))+1,r._i=o,e.changes.insert.push(o),e.dispatchEvent("datachanged",e.changes),e.originalData.push(r),e.getSchema().forEach(function(n){r[n.name]===void 0&&e.applyDefaultValue(e.originalData[e.originalData.length-1],n)}),c&&(e.setFilter(),e.resize(!0)),o}},e.updateRow=function(r){var c=e.originalData.findIndex(function(n){return n._i===r._i}),o=e.originalData.find(function(n){return n._i===r._i});Object.keys(r).forEach(function(n){var i=o[n],a=r[n];i!=a&&e.updateChanges({data:r,header:{name:n},initValue:i,value:a})}),e.originalData.splice(c,1,r)},e.updateCell=function(){},e.setRowHeight=function(r,c){e.sizes.rows[r]=c,e.draw(!0)},e.setColumnWidth=function(r,c){e.sizes.columns[r]=c,e.draw(!0)},e.resetColumnWidths=function(){e.sizes.columns={},e.draw(!0)},e.resetRowHeights=function(){e.sizes.rows={},e.draw(!0)},e.setFilter=function(r,c){r===void 0&&c===void 0?e.columnFilters={}:r&&(c===""||c===void 0)?delete e.columnFilters[r]:e.columnFilters[r]=c,e.applyDataTransforms(!0)},e.findRowScrollTop=function(r){if(e.scrollCache.y[r]===void 0)throw new RangeError("Row index out of range.");return e.scrollCache.y[r]},e.findColumnScrollLeft=function(r){var c=Math.max(r-1,0);if(e.scrollCache.x[c]===void 0)throw new Error("Column index out of range.");return e.scrollCache.x[c]-e.getColummnWidth(e.orders.columns[r])},e.gotoCell=function(r,c,o,n){var i=r===void 0?void 0:e.findColumnScrollLeft(r),a=c===void 0?void 0:e.findRowScrollTop(c),l,g=e.scrollBox.width-(e.scrollBox.verticalBarVisible?e.style.scrollBarWidth:0),A=e.scrollBox.height-(e.scrollBox.horizontalBarVisible?e.style.scrollBarWidth:0);o=o===void 0?0:o,n=n===void 0?0:n,i-=g*o,a-=A*n,r!==void 0&&c!==void 0?(e.scrollBox.scrollTo(i,a),requestAnimationFrame(function(){l=e.getVisibleCellByIndex(r,c),!!l&&(i+=l.width*o,a+=l.height*n,e.scrollBox.scrollTo(i,a))})):r!==void 0?e.scrollBox.scrollLeft=i:c!==void 0&&(e.scrollBox.scrollTop=a)},e.gotoRow=function(r){e.gotoCell(0,r)},e.scrollIntoView=function(r,c,o,n){e.visibleCells.filter(function(i){return(i.rowIndex===c||c===void 0)&&(i.columnIndex===r||r===void 0)&&i.x>0&&i.y>0&&i.x+i.width<e.width&&i.y+i.height<e.height}).length===0&&e.gotoCell(r,c,o,n)},e.setActiveCell=function(r,c){r<0&&(r=0),c<0&&(c=0),e.activeCell={rowIndex:c,columnIndex:r},e.dispatchEvent("activecellchanged",{cell:e.getVisibleCellByIndex(r,c)})},e.selectNone=function(r){e.selections=[],e.dispatchEvent("selectionchanged",{selectedData:e.getSelectedData(),selections:e.selections,selectionBounds:e.selectionBounds}),!r&&e.draw()},e.selectAll=function(r){e.selectArea({top:0,left:-1,right:e.getSchema().length-1,bottom:e.data.length-1}),!r&&e.draw()},e.isColumnSelected=function(r){var c=!0;return e.data.forEach(function(o,n){(!e.selections[n]||e.selections[n].indexOf(e.orders.columns[r])===-1)&&(c=!1)}),c},e.forEachSelectedCell=function(r,c){var o=[],n=c?e.getSchema():e.getVisibleSchema(),i=e.data.length;e.selections.forEach(function(a,l){if(l!==i){if(a.length===0){o[l]=null;return}o[l]={},a.forEach(function(g){g===-1||!n[g]||r(e.data,l,n[g].name)})}})},e.selectColumn=function(r,c,o,n){var i,a,l;function g(p){e.data.forEach(function(S,v){e.selections[v]=e.selections[v]||[],e.selections[v].indexOf(p)===-1&&e.selections[v].push(p)})}function A(p){e.data.forEach(function(S,v){e.selections[v]=e.selections[v]||[],e.selections[v].indexOf(p)!==-1&&e.selections[v].splice(e.selections[v].indexOf(p),1)})}if(o){if(!e.activeCell)return;for(i=Math.min(e.activeCell.columnIndex,r),a=Math.max(e.activeCell.columnIndex,r),l=i;a>l;l+=1)g(l)}!c&&!o&&(e.selections=[],e.activeCell.columnIndex=r,e.activeCell.rowIndex=e.scrollIndexTop),c&&e.isColumnSelected(r)?A(r):g(r),!n&&e.dispatchEvent("selectionchanged",{selectedData:e.getSelectedData(),selections:e.selections,selectionBounds:e.getSelectionBounds()})},e.selectRow=function(r,c,o,n){var i,a,l,g=e.getVisibleSchema();function A(){n||e.dispatchEvent("selectionchanged",{selectedData:e.getSelectedData(),selections:e.selections,selectionBounds:e.selectionBounds})}function p(S){e.selections[S]=[],e.selections[S].push(-1),g.forEach(function(v,h){e.selections[S].push(e.orders.columns.indexOf(v.index))})}if((e.dragAddToSelection===!1||e.dragObject===void 0)&&e.selections[r]&&e.selections[r].length-1===g.length&&c){e.selections[r]=[],A();return}if(e.dragAddToSelection===!0||e.dragObject===void 0)if(o&&e.dragObject===void 0){if(!e.activeCell)return;for(a=Math.min(e.activeCell.rowIndex,r),l=Math.max(e.activeCell.rowIndex,r),i=a;l>=i;i+=1)p(i)}else p(r);A()},e.collapseTree=function(r){e.dispatchEvent("collapsetree",{childGrid:e.childGrids[r],data:e.data[r],rowIndex:r}),e.openChildren[r].blur(),e.openChildren[r].dispose(),delete e.openChildren[r],delete e.sizes.trees[r],delete e.childGrids[r],e.dispatchEvent("resizerow",{cellHeight:e.style.cellHeight}),e.resize(!0),e.draw(!0)},e.expandTree=function(r){var c=e.args.treeGridAttributes||{},o=e.getColumnHeaderCellHeight(),n=e.sizes.columns.cornerCell||e.style.rowHeaderCellWidth,i=e.sizes.trees[r]||e.style.treeGridHeight,a;e.childGrids[r]||(c.debug=e.attributes.debug,c.name=e.attributes.saveAppearance?e.attributes.name+"tree"+r:void 0,c.style=c.style||e.style,c.parentNode={parentGrid:e.intf,nodeType:"canvas-datagrid-tree",offsetHeight:i,offsetWidth:e.width-n,header:{width:e.width-n},offsetLeft:n,offsetTop:o,offsetParent:e.intf.parentNode,parentNode:e.intf.parentNode,style:"tree",data:e.data[r]},a=e.createGrid(c),e.childGrids[r]=a),a=e.childGrids[r],a.visible=!0,e.dispatchEvent("expandtree",{treeGrid:a,data:e.data[r],rowIndex:r}),e.openChildren[r]=a,e.sizes.trees[r]=i,e.dispatchEvent("resizerow",{height:e.style.cellHeight}),e.resize(!0)},e.toggleTree=function(r){var c=e.openChildren[r];if(c)return e.collapseTree(r);e.expandTree(r)},e.getHeaderByName=function(r){var c,o=e.getSchema();for(c=0;c<o.length;c+=1)if(o[c].name===r)return o[c]},e.fitColumnToValues=function(r,c){!e.canvas||(e.sizes.columns[r==="cornerCell"?-1:e.getHeaderByName(r).index]=Math.max(e.findColumnMaxTextLength(r),e.style.minColumnWidth),c||(e.resize(),e.draw(!0)))},e.isCellVisible=function(r,c){if(c!==void 0)return e.visibleCells.filter(function(i){return i.columnIndex===r&&i.rowIndex===c}).length>0;var o,n=e.visibleCells.length;for(o=0;o<n;o+=1)if(r.x===e.visibleCells[o].x&&r.y===e.visibleCells[o].y)return!0;return!1},e.order=function(r,c,o,n){var i,a=e.getSchema().filter(function(l){return l.name===r});if(!e.dispatchEvent("beforesortcolumn",{name:r,direction:c})&&(e.orderBy=r,e.orderDirection=c,!(!e.data||e.data.length===0))){if(a.length===0)throw new Error("Cannot sort.  No such column name");i=o||a[0].sorter||e.sorters[a[0].type],!i&&a[0].type!==void 0&&console.warn('Cannot sort type "%s" falling back to string sort.',a[0].type),e.orderings.add(r,c,typeof i=="function"?i:e.sorters.string),e.orderings.sort(),e.dispatchEvent("sortcolumn",{name:r,direction:c}),e.draw(!0),!n&&e.setStorageData()}},e.isInGrid=function(r){return!(r.x<0||r.x>e.width||r.y<0||r.y>e.height)},e.moveSelection=function(r,c){var o=[];e.selections.forEach(function(n,i){o[i+c]=[],n.forEach(function(a){o[i+c].push(a+r)})}),e.selections=o},e.moveTo=function(r,c,o){var n=e.getSelectedData(),i=e.getVisibleSchema(),a=r.length,l,g=-Infinity,A=Infinity,p=o-1;r.forEach(function(S,v){v!==a&&S.length!==0&&(A=Math.min(e.getVisibleColumnIndexOf(c),A),g=Math.max(g,S.length),S.forEach(function(h){h=e.getVisibleColumnIndexOf(h),!!i[h]&&(e.data[v]||(e.data[v]={}),e.data[v][i[h].name]=null)}))}),r.forEach(function(S,v){var h;p+=1,l=e.getVisibleColumnIndexOf(c),S.forEach(function(E,H){E=e.getVisibleColumnIndexOf(E),H>0&&(l+=E-h),h=E,!(E===-1||!i[l]||!i[E]||e.data.length-1<p||p<0)&&(e.data[p]||(e.data[p]={}),e.data[p][i[l].name]=n[v][i[E].name])})})},e.isColumnVisible=function(r){return e.visibleCells.filter(function(c){return c.columnIndex===r}).length>0},e.isRowVisible=function(r){return e.visibleCells.filter(function(c){return c.rowIndex===r}).length>0},e.getVisibleCellByIndex=function(r,c){return e.visibleCells.filter(function(o){return o.columnIndex===r&&o.rowIndex===c})[0]},e.getCellAt=function(r,c,o){function n(H){if(H.x+H.width-e.attributes.borderResizeZone*.4<r&&H.x+H.width+e.attributes.borderResizeZone*.6>r)return"r";if(H.x-e.attributes.borderResizeZone*.4<r&&H.x+e.attributes.borderResizeZone*.6>r)return"l";if(H.y+H.height-e.attributes.borderResizeZone*.4<c&&H.y+H.height+e.attributes.borderResizeZone*.6>c)return"b";if(H.y-e.attributes.borderResizeZone*.4<c&&H.y+e.attributes.borderResizeZone*.6>c)return"t"}if(!!e.visibleCells){var i,a=o?e.attributes.touchScrollZone:0,l=e.attributes.borderDragBehavior==="move",g,A=e.visibleCells.length,p,S=l?e.cursorGrab:"ew-resize",v=l?e.cursorGrab:"ns-resize",h,E;if(!(!e.visibleCells||!e.visibleCells.length)){if(e.hasFocus=!0,!(c<e.height&&c>0&&r<e.width&&r>0))return e.hasFocus=!1,{dragContext:"inherit",context:"inherit"};for(g=0;g<A;g+=1)if(h=e.visibleCells[g],E={x:h.x,y:h.y,height:h.height,width:h.width},o&&/(vertical|horizontal)-scroll-/.test(h.style)&&(E.x-=a,E.y-=a,E.height+=a,E.width+=a),E.x-e.style.cellBorderWidth<r&&E.x+E.width+e.style.cellBorderWidth>r&&E.y-e.style.cellBorderWidth<c&&E.y+E.height+e.style.cellBorderWidth>c){if(/frozen-row-marker/.test(h.style))return h.dragContext=h.style,h.context="row-resize",h;if(/frozen-column-marker/.test(h.style))return h.dragContext=h.style,h.context="col-resize",h;if(/selection-handle-/.test(h.style))return h.dragContext=h.style,h.context="crosshair",h;if(/vertical-scroll-(bar|box)/.test(h.style))return h.dragContext="vertical-scroll-box",h.context="vertical-scroll-box",h.isScrollBar=!0,h.isVerticalScrollBar=!0,c>e.scrollBox.box.v.y+e.scrollBox.scrollBoxHeight?(h.dragContext="vertical-scroll-bottom",h.context="vertical-scroll-bottom"):c<e.scrollBox.box.v.y&&(h.dragContext="vertical-scroll-top",h.context="vertical-scroll-top"),e.cursor="default",h;if(/horizontal-scroll-(bar|box)/.test(h.style))return h.dragContext="horizontal-scroll-box",h.context="horizontal-scroll-box",h.isScrollBar=!0,h.isHorizontalScrollBar=!0,r>e.scrollBox.box.h.x+e.scrollBox.scrollBoxWidth?(h.dragContext="horizontal-scroll-right",h.context="horizontal-scroll-right"):r<e.scrollBox.box.h.x&&(h.dragContext="horizontal-scroll-left",h.context="horizontal-scroll-left"),e.cursor="default",h;if(i=n(E),p=l&&h.selectionBorder&&h.selectionBorder.indexOf(i)!==-1,["l","r"].indexOf(i)!==-1&&(e.attributes.allowColumnResize||p)&&(e.attributes.allowColumnResizeFromCell&&h.isNormal||!h.isNormal||p)&&(e.attributes.allowRowHeaderResize&&(h.isRowHeader||h.isCorner)||!(h.isRowHeader&&h.isCorner))){if((h.isColumnHeader||h.isCorner||e.attributes.allowColumnResizeFromCell&&h.isNormal)&&i==="r")return h.context="ew-resize",h.dragContext="ew-resize",h;if(!(h.isColumnHeader||h.isCorner)&&p)return h.context=S,h.dragContext=i+"-move",h}if(["t","b"].indexOf(i)!==-1&&h.rowIndex>-1&&(e.attributes.allowRowResize||p)&&(e.attributes.allowRowResizeFromCell&&h.isNormal||!h.isNormal||p)&&!h.isColumnHeader){if((h.isRowHeader||h.isCorner||e.attributes.allowRowResizeFromCell&&h.isNormal)&&i==="b")return h.context="ns-resize",h.dragContext="ns-resize",h;if(!(h.isRowHeader||h.isCorner)&&p)return h.context=v,h.dragContext=i+"-move",h}return h.style==="columnHeaderCell"?(h.context="cell",h.dragContext="column-reorder",h):h.style==="rowHeaderCell"?(e.attributes.rowGrabZoneSize+(h.y-e.style.cellBorderWidth)<c||!e.attributes.allowRowReordering?(h.dragContext="cell",h.context="cell"):(h.context=e.cursorGrab,h.dragContext="row-reorder"),h):h.isGrid?(e.hasFocus=!1,h.dragContext="cell-grid",h.context="cell-grid",h):h.style==="tree-grid"?(e.hasFocus=!1,h.dragContext="tree",h.context="tree",h):(h.dragContext="cell",h.context="cell",h)}return e.hasFocus=!0,e.cursor="default",{dragContext:"background",context:"background",style:"background",isBackground:!0}}}},e.getSelectionBounds=function(){var r={x:Infinity,y:Infinity},c={x:-Infinity,y:-Infinity};return e.selections.forEach(function(o,n){var i,a;r.y=n<r.y?n:r.y,c.y=n>c.y?n:c.y,i=Math.max.apply(null,o),a=Math.min.apply(null,o),r.x=a<r.x?a:r.x,c.x=i>c.x?i:c.x}),{top:r.y,left:r.x,bottom:c.y,right:c.x}},e.getSchemaFromData=function(r){return r=r||e.data,Object.keys(r[0]||{" ":""}).map(function(o,n){var i=e.getBestGuessDataType(o,r),a={name:o,title:isNaN(parseInt(o,10))?o:e.integerToAlpha(o).toUpperCase(),index:n,columnIndex:n,type:i,filter:e.filter(i)};return e.storedSettings&&e.storedSettings.visibility&&e.storedSettings.visibility[a.name]!==void 0&&(a.hidden=!e.storedSettings.visibility[a.name]),a})},e.clearChangeLog=function(){e.changes=[]},e.selectArea=function(r,c){e.selectionBounds=r||e.selectionBounds;var o,n,i,a=e.getSchema();if(c||(e.selections=[]),e.selectionBounds.top<-1||e.selectionBounds.bottom>e.data.length||e.selectionBounds.left<-1||e.selectionBounds.right>a.length)throw new Error("Impossible selection area");for(n=e.selectionBounds.top;n<=e.selectionBounds.bottom;n+=1)for(e.selections[n]=[],i=e.selectionBounds.left;i<=e.selectionBounds.right;i+=1)e.selections[n].indexOf(i)===-1&&e.selections[n].push(i);o={selections:e.selections,selectionBounds:e.selectionBounds},Object.defineProperty(o,"selectedData",{get:function(){return e.getSelectedData()}}),e.dispatchEvent("selectionchanged",o)},e.findColumnMaxTextLength=function(r){var c=-Infinity;if(r==="cornerCell")return e.ctx.font=e.style.rowHeaderCellFont,e.ctx.measureText((e.data.length+(e.attributes.showNewRow?1:0)).toString()).width+e.style.autosizePadding+e.style.autosizeHeaderCellPadding+e.style.rowHeaderCellPaddingRight+e.style.rowHeaderCellPaddingLeft+(e.attributes.tree?e.style.treeArrowWidth+e.style.treeArrowMarginLeft+e.style.treeArrowMarginRight:0);var o=null;return e.getSchema().forEach(function(n){if(n.name===r){e.ctx.font=e.style.columnHeaderCellFont;var i=e.ctx.measureText(n.title||n.name).width+e.style.headerCellPaddingRight+e.style.headerCellPaddingLeft;c=i>c?i:c,o=e.formatters[n.type]}}),e.data.forEach(function(n){var i=n[r];o&&(i=o({cell:{value:i}})),e.ctx.font=e.style.cellFont;var a=e.ctx.measureText(i).width+e.style.cellPaddingRight+e.style.cellPaddingLeft+e.style.cellAutoResizePadding;c=a>c?a:c}),c},e.getHeaderWidth=function(){return e.getVisibleSchema().reduce(function(r,c){return r+parseInt(c.width||e.style.cellWidth,10)},0)},e.getRowHeight=function(r){return(e.sizes.rows[r]||e.style.cellHeight)*e.scale},e.getColummnWidth=function(r){return(e.sizes.columns[r]||e.getSchema()[r].width||e.style.cellWidth)*e.scale},e.formatters.string=function(c){return c.cell.value!==void 0?c.cell.value:""},e.formatters.rowHeaderCell=e.formatters.string,e.formatters.headerCell=e.formatters.string,e.formatters.number=e.formatters.string,e.formatters.int=e.formatters.string,e.formatters.html=e.formatters.string,e.formatters.number=e.formatters.string,e.formatters.date=e.formatters.string,e.formatters.timestamp=e.formatters.string,e.formatters.time=e.formatters.string,e.sorters.string=function(r,c){var o=c==="asc";return function(n,i){return n[r]===void 0||n[r]===null?1:i[r]===void 0||i[r]===null?0:o?n[r].localeCompare?n[r].localeCompare(i[r]):n-i:i[r].localeCompare?i[r].localeCompare(n[r]):1}},e.sorters.number=function(r,c){var o=c==="asc";return function(n,i){return o?n[r]-i[r]:i[r]-n[r]}},e.sorters.date=function(r,c){var o=c==="asc";return function(n,i){return o?new Date(n[r]).getTime()-new Date(i[r]).getTime():new Date(i[r]).getTime()-new Date(n[r]).getTime()}},e.applySetting=function(r){r.selections&&(e.selections=r.selections),r.activeCell&&(e.activeCell=r.activeCell),r.columnFilters&&Object.keys(r.columnFilters).forEach(function(c){e.setFilter(c,r.columnFilters[c])}),r.changes&&(e.changes=r.changes),e.resize(!0)},e.resetSetting=function(){e.selections=[],e.activeCell={columnIndex:0,rowIndex:0},e.columnFilters={},e.changes={insert:[],update:[],remove:[]}},e.resetChanges=function(){e.changes={insert:[],update:[],remove:[]},e.needRefresh=!0,e.resize(!0)},e.syncSetting=function(){return{activeCell:e.activeCell,changes:e.changes,columnFilters:e.columnFilters,selections:e.selections}},e.updateChanges=function(r){var c=e.changes.insert,o=e.changes.update,n=r.data._i,i=e.singleRecordViewRowIndex,a=r.value;if(!c.find(function(p){return p===n})){var l=i===void 0||i===-1?r.header.name:r.data.row,g=o.find(function(p){return p.columnName===l&&p.rowKey===n});if(!g)o.push({rowKey:n,initValue:r.initValue!==void 0?r.initValue:r.value,columnName:l});else if(g.initValue==a){var A=o.findIndex(function(p){return p.columnName===l&&p.rowKey===n});o.splice(A,1)}else g.currValue=a}e.dispatchEvent("datachanged",e.changes)},e.getCopyText=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"text",c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:",",o,n,i=[],a=[],l={},g,A=e.getSelectedData(),p=e.getSchema();function S(R){return R.replace(/</g,"&lt;").replace(/>/g,"&gt;")}function v(R){return R=R==null?"":R,"<td>"+(typeof R=="string"?S(R):R)+"</td>"}function h(R,K){if(!p.length||R.length<2)return"";var N=[];return K&&N.push("<tr>"),p.forEach(function($,gt){if($=p[e.orders.columns[gt]],!$.hidden&&R.indexOf($.name)!==-1){var ht=$.name||$.title||"";K?N.push("<th>"+S(ht)+"</th>"):r==="csv"?N.push('"'+ht.replace(/"/g,'""')+'"'):N.push(ht)}}),N.push(K?"</tr>":`
`),N.join(K?"":c)}function E(R,K,N,$){if(R!==null&&R!==!1&&R!==void 0&&R.replace){K.push(v(R)),r==="csv"?N.push('"'+R.replace(/"/g,'""')+'"'):N.push(R);return}if(R!==void 0){N.push(R),K.push(v(R));return}N.push(""),K.push("<td>&nbsp;</td>")}if(A.length>0){if(A.filter(function(R){return!!R}).forEach(function(R){var K=Object.keys(R);if(R){var N=[],$=[],gt=[];p.forEach(function(ht,T){gt.push(p[e.orders.columns[T]])}),gt.forEach(function(ht,T){K.indexOf(ht.name)!==-1&&(l[ht.name]=!0,E(R[ht.name],N,$,ht))}),i.push(N.join("")),a.push($.join(c))}}),g=Object.keys(l),r==="csv"?o=h(g)+a.join(`
`):o=a.join(`
`),n="<table>"+h(g,!0)+"<tr>"+i.join("</tr><tr>")+"</tr></table>",g.length===1&&o.startsWith('"')&&(o=o.substring(1,o.length-1)),r==="text"||r==="csv")return o;if(r==="html")return n;if(r==="json"){var H=A.filter(function(R){return!!R}).map(function(R){var K=R._i,N=(0,_.Z)(R,s);return N});return JSON.stringify(H)}}}}},5771:function(et,rt,P){"use strict";P.d(rt,{Z:function(){return _}});function _(t){var s=50,I,e,r;t.scrollAnimation={},t.touchDelta={},t.touchAnimateTo={},t.animationFrames=0,t.getTouchPos=function(o,n){var i=n?o.touches[n]:o.touches[0],a=t.canvas.getBoundingClientRect(),l;if(!!i)return l={x:i.clientX-a.left,y:i.clientY-a.top},t.isChildGrid&&(l.x-=t.canvasOffsetLeft,l.y-=t.canvasOffsetTop),{x:l.x,y:l.y,rect:a}},t.easingFunctions={linear:function(n){return n},easeInQuad:function(n){return n*n},easeOutQuad:function(n){return n*(2-n)},easeInOutQuad:function(n){return n<.5?2*n*n:-1+(4-2*n)*n},easeInCubic:function(n){return n*n*n},easeOutCubic:function(n){return--n*n*n+1},easeInOutCubic:function(n){return n<.5?4*n*n*n:(n-1)*(2*n-2)*(2*n-2)+1},easeInQuart:function(n){return n*n*n*n},easeOutQuart:function(n){return 1- --n*n*n*n},easeInOutQuart:function(n){return n<.5?8*n*n*n*n:1-8*--n*n*n*n},easeInQuint:function(n){return n*n*n*n*n},easeOutQuint:function(n){return 1+--n*n*n*n*n},easeInOutQuint:function(n){return n<.5?16*n*n*n*n*n:1+16*--n*n*n*n*n}},t.easing=function(o,n,i,a){return i*t.easingFunctions[t.attributes.touchEasingMethod](o/a)+n},t.calculatePPSTimed=function(){t.xPPST=-((t.touchDelta.x-t.touchSigmaTimed.x)/(t.touchDelta.t-t.touchSigmaTimed.t)),t.yPPST=-((t.touchDelta.y-t.touchSigmaTimed.y)/(t.touchDelta.t-t.touchSigmaTimed.t)),t.touchSigmaTimed={x:t.touchDelta.x,y:t.touchDelta.y,t:performance.now()}},t.calculatePPS=function(){t.xPPS=-((t.touchDelta.x-t.touchSigma.x)/(t.touchDelta.t-t.touchSigma.t)),t.yPPS=-((t.touchDelta.y-t.touchSigma.y)/(t.touchDelta.t-t.touchSigma.t)),t.touchSigma={x:t.touchDelta.x,y:t.touchDelta.y,t:performance.now()}},t.touchEndAnimation=function(){if(!t.canvas||!t.scrollBox.scrollTo)return requestAnimationFrame(t.touchEndAnimation);var o=performance.now(),n=t.attributes.touchReleaseAnimationDurationMs,i;i=o-t.touchDelta.t,t.animationFrames+=1,t.scrollAnimation.x=t.easing(i,t.touchDelta.scrollLeft,t.touchAnimateTo.x,n),t.scrollAnimation.y=t.easing(i,t.touchDelta.scrollTop,t.touchAnimateTo.y,n),!(i>n||t.scrollAnimation.y===t.scrollBox.scrollTop&&t.scrollAnimation.x===t.scrollBox.scrollLeft||t.stopAnimation)&&(t.scrollBox.scrollTo(t.scrollAnimation.x,t.scrollAnimation.y),requestAnimationFrame(t.touchEndAnimation))},t.touchEditCell=function(o){t.beginEditAt(o.columnIndex,o.rowIndex)},t.touchCell=function(o){return function(){clearInterval(t.calculatePPSTimer);var n,i=t.getTouchPos(o);if(Math.abs(t.touchDelta.x)+Math.abs(t.touchDelta.y)<t.attributes.touchDeadZone){if(n=t.getCellAt(i.x,i.y),!n)return;if(t.touchingCell&&t.touchingCell.rowIndex===n.rowIndex&&t.touchingCell.columnIndex===n.columnIndex){t.touchEditCell(n);return}t.input&&t.input.editCell&&t.endEdit(),t.touchingCell=n,t.selectArea({top:n.rowIndex,bottom:n.rowIndex,left:n.columnIndex,right:n.columnIndex}),t.draw(!0)}}},t.touchstart=function(o){if(o.changedTouches[0]&&(t.touchStart=t.getTouchPos(o),t.startingCell=t.getCellAt(t.touchStart.x,t.touchStart.y,!0)),!t.dispatchEvent("touchstart",{NativeEvent:o,cell:t.startingCell})){if(t.disposeContextMenu(),clearInterval(t.calculatePPSTimer),clearTimeout(t.touchContextTimeout),t.touchStartEvent=o,t.stopAnimation=!0,t.animationFrames=0,t.stopPropagation(o),o.preventDefault(),o.touches.length===1&&o.changedTouches[0]&&!t.zoomAltered){if(t.touchLength=1,t.touchStart=t.touchStart||t.touchStart1,t.touchScrollStart={x:t.scrollBox.scrollLeft,y:t.scrollBox.scrollTop,t:performance.now()},t.touchDelta={x:0,y:0,scrollLeft:t.scrollBox.scrollLeft,scrollTop:t.scrollBox.scrollTop,t:t.touchScrollStart.t},t.touchSigma={x:t.touchDelta.x,y:t.touchDelta.y,t:t.touchDelta.t},t.touchSigmaTimed={x:t.touchDelta.x,y:t.touchDelta.y,t:t.touchDelta.t},t.touchContextTimeout=setTimeout(function(){t.contextmenuEvent(o,t.touchStart)},t.attributes.touchContextMenuTimeMs),t.calculatePPSTimer=setInterval(t.calculatePPSTimed,s),t.startingCell&&(t.startingCell.isGrid||["tree","inherit"].indexOf(t.startingCell.context)!==-1)){t.hasFocus=!1;return}if(t.hasFocus=!0,t.startingCell.isHeader){t.startingCell.isRowHeader?(t.selectArea({top:t.startingCell.rowIndex,bottom:t.startingCell.rowIndex,left:0,right:t.getVisibleSchema().length-1}),t.draw(!0)):t.startingCell.isColumnHeader&&(t.attributes.columnHeaderClickBehavior==="sort"&&(t.orderBy===t.startingCell.header.name?t.orderDirection=t.orderDirection==="asc"?"desc":"asc":t.orderDirection="asc",t.order(t.startingCell.header.name,t.orderDirection)),t.attributes.columnHeaderClickBehavior==="select"&&(t.selectArea({top:0,bottom:t.data.length-1,left:t.startingCell.columnIndex,right:t.startingCell.columnIndex}),t.draw(!0))),t.touchEndEvents(o);return}}t.zoomAltered||(document.body.addEventListener("touchmove",t.touchmove,{passive:!1}),document.body.addEventListener("touchend",t.touchend,!1),document.body.addEventListener("touchcancel",t.touchcancel,!1),t.draw(!0))}},t.touchSelect=function(o,n){if(!(o.rowIndex===void 0||o.columnIndex===void 0)){t.touchSelecting=!0;var i=t.getSelectionBounds();n==="selection-handle-bl"&&o.rowIndex>=i.top&&o.columnIndex<=i.right?(i.bottom=o.rowIndex,i.left=o.columnIndex):n==="selection-handle-tl"&&o.rowIndex<=i.bottom&&o.columnIndex<=i.right?(i.top=o.rowIndex,i.left=o.columnIndex):n==="selection-handle-tr"&&o.rowIndex<=i.bottom&&o.columnIndex>=i.left?(i.top=o.rowIndex,i.right=o.columnIndex):n==="selection-handle-br"&&o.rowIndex>=i.top&&o.columnIndex>=i.left&&(i.bottom=o.rowIndex,i.right=o.columnIndex),t.attributes.selectionMode==="row"||o.rowIndex===-1?(i.left=0,i.right=t.getSchema().length-1):i.left=Math.max(0,i.left),t.selectArea(i),t.draw(!0)}};function c(o){var n,i,a,l,g,A,p,S,v;if(t.dispatchEvent("beforetouchmove",{NativeEvent:o}))return;if(clearTimeout(r),o.changedTouches[0]&&(t.touchPosition=t.getTouchPos(o)),o.changedTouches[1]&&(t.touchPosition1=t.getTouchPos(o,1)),Math.abs(t.touchDelta.x)+Math.abs(t.touchDelta.y)>t.attributes.touchDeadZone&&clearTimeout(t.touchContextTimeout),o.touches.length===2&&t.touchPosition&&t.touchPosition1){S=t.touchPosition.y,v=t.touchPosition1.y,t.zoomDeltaStart||(t.zoomDeltaStart=Math.abs(S-v),t.startScale=t.scale),t.touchLength=2,t.scaleDelta=t.zoomDeltaStart-Math.abs(S-v),t.scale=t.startScale-t.scaleDelta*t.attributes.touchZoomSensitivity,t.scale=Math.min(Math.max(t.scale,t.attributes.touchZoomMin),t.attributes.touchZoomMax),t.zoomAltered=!0,t.resize(!0),t.resizeChildGrids();return}if(t.zoomAltered)return;t.touchLength=1,t.touchPosition=t.touchPosition||t.touchPosition1,n=t.getColumnHeaderCellHeight(),i=t.getRowHeaderCellWidth(),a=t.width-t.style.scrollBarWidth-t.touchPosition.x<t.attributes.selectionScrollZone,l=t.touchPosition.x-i<t.attributes.selectionScrollZone,g=t.height-t.style.scrollBarWidth-t.touchPosition.y<t.attributes.selectionScrollZone,A=t.touchPosition.y-n<t.attributes.selectionScrollZone,p=t.style.scrollBarWidth;function h(){var E=t.scrollBox.scrollLeft,H=t.scrollBox.scrollTop;E+=a?t.attributes.selectionScrollIncrement:0,H+=g?t.attributes.selectionScrollIncrement:0,H-=A?t.attributes.selectionScrollIncrement:0,E-=l?t.attributes.selectionScrollIncrement:0,t.scrollBox.scrollTo(E,H),r=setTimeout(h,t.attributes.scrollRepeatRate)}if(o.stopPropagation(),t.touchDelta={x:t.touchPosition.x-t.touchStart.x,y:t.touchPosition.y-t.touchStart.y,scrollLeft:t.scrollBox.scrollLeft,scrollTop:t.scrollBox.scrollTop,t:performance.now()},t.currentCell=t.getCellAt(t.touchPosition.x,t.touchPosition.y),t.dispatchEvent("touchmove",{NativeEvent:o,cell:t.currentCell}),t.calculatePPS(),t.touchDuration=performance.now()-t.touchScrollStart.t,t.stopAnimation=!0,t.animationFrames=0,t.touchSelecting&&(a||l||A||g)&&h(),/vertical-scroll-/.test(t.startingCell.style)){t.scrollBox.scrollTop=t.scrollBox.scrollHeight*((t.touchPosition.y-n-p)/(t.scrollBox.height-p-n));return}if(/horizontal-scroll-/.test(t.startingCell.style)){t.scrollBox.scrollLeft=t.scrollBox.scrollWidth*((t.touchPosition.x-i-p)/(t.scrollBox.width-p-i));return}if(/selection-handle-/.test(t.startingCell.style)){t.touchSelect(t.currentCell,t.startingCell.style);return}t.scrollBox.scrollTo(t.touchScrollStart.x-t.touchDelta.x,t.touchScrollStart.y-t.touchDelta.y)}t.touchmove=function(o){e||requestAnimationFrame(function(){e=!0,c(o),e=!1})},t.touchEndEvents=function(o){t.zoomDeltaStart=void 0,t.touchSelecting=!1,clearInterval(t.touchScrollTimeout),clearInterval(t.touchContextTimeout),clearInterval(t.calculatePPSTimer),o.stopPropagation(),document.body.removeEventListener("touchmove",t.touchmove,{passive:!1}),document.body.removeEventListener("touchend",t.touchend,!1),document.body.removeEventListener("touchcancel",t.touchcancel,!1)},t.touchend=function(o){if(!t.dispatchEvent("touchend",{NativeEvent:o,cell:t.currentCell})){if(t.zoomDeltaStart=void 0,o.changedTouches[0]&&(t.touchPosition=void 0),o.changedTouches[1]&&(t.touchPosition1=void 0),t.zoomAltered){o.touches.length===0&&(t.zoomAltered=!1);return}var n=Math.abs(t.touchDelta.x)+Math.abs(t.touchDelta.y)<t.attributes.touchDeadZone;isNaN(t.xPPS)&&(t.xPPS=0),isNaN(t.yPPS)&&(t.yPPS=0),isNaN(t.xPPST)&&(t.xPPST=0),isNaN(t.yPPST)&&(t.yPPST=0),t.touchAnimateTo.x=t.xPPS*t.attributes.touchReleaseAcceleration,t.touchAnimateTo.y=t.yPPS*t.attributes.touchReleaseAcceleration,t.calculatePPSTimed(),n&&!t.contextMenu?t.touchCell(t.touchStartEvent)():t.animationFrames===0&&(Math.abs(t.xPPST)>t.attributes.scrollAnimationPPSThreshold||Math.abs(t.yPPST)>t.attributes.scrollAnimationPPSThreshold)&&!/-scroll-/.test(t.startingCell.style)&&!n&&(t.stopAnimation=!1,t.touchEndAnimation()),t.touchEndEvents(o)}},t.touchcancel=function(o){t.dispatchEvent("touchcancel",{NativeEvent:o,cell:t.currentCell})||t.touchEndEvents(o)}}},24843:function(et,rt,P){"use strict";P.d(rt,{a:function(){return I},A:function(){return s}});var _=P(67294),t=P(18111);function s(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=(0,_.useContext)(t.xi),c=r.prefixCls;return c+e}function I(){var e=(0,_.useContext)(t.xi);return e.dataStore}}}]);
