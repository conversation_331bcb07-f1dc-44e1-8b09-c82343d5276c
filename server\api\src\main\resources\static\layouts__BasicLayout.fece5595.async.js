(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[1034],{44010:function(y,l,n){"use strict";n.r(l);var E=n(11849),u=n(92725),d=n(27400),v=n(9761),s=n(67294),t=n(85893),M=s.lazy(function(){return n.e(6147).then(n.bind(n,86147))}),P=s.lazy(function(){return Promise.all([n.e(2225),n.e(9223),n.e(7098),n.e(7422),n.e(5246),n.e(5785),n.e(3844),n.e(398),n.e(2622),n.e(4186),n.e(1600)]).then(n.bind(n,25862))}),f=(0,v.Pi)(function(a){var o=(0,d.a)(),h=o.isMobile;return(0,s.useEffect)(function(){o.currentUser()},[o]),(0,t.jsx)(s.Suspense,{fallback:(0,t.jsx)(u.Z,{}),children:h?(0,t.jsx)(M,(0,E.Z)({},a)):(0,t.jsx)(P,(0,E.Z)({},a))})});l.default=f}}]);
