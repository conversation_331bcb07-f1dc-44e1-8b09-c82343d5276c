(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[2622],{47356:function(Ct,ve){"use strict";Object.defineProperty(ve,"__esModule",{value:!0});var m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};ve.default=m},44149:function(Ct,ve){"use strict";Object.defineProperty(ve,"__esModule",{value:!0});var m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"};ve.default=m},77404:function(Ct,ve,m){"use strict";Object.defineProperty(ve,"__esModule",{value:!0}),ve.default=void 0;var P=M(m(85317));function M(Y){return Y&&Y.__esModule?Y:{default:Y}}var G=P;ve.default=G,Ct.exports=G},86056:function(Ct,ve,m){"use strict";Object.defineProperty(ve,"__esModule",{value:!0}),ve.default=void 0;var P=M(m(91724));function M(Y){return Y&&Y.__esModule?Y:{default:Y}}var G=P;ve.default=G,Ct.exports=G},16165:function(Ct,ve,m){"use strict";var P=m(28991),M=m(96156),G=m(81253),Y=m(67294),H=m(94184),oe=m.n(H),X=m(63017),B=m(41755),J=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],we=Y.forwardRef(function(be,qe){var se=be.className,Qe=be.component,He=be.viewBox,At=be.spin,ot=be.rotate,Et=be.tabIndex,p=be.onClick,fe=be.children,u=(0,G.Z)(be,J);(0,B.Kp)(Boolean(Qe||fe),"Should have `component` prop or `children`."),(0,B.C3)();var U=Y.useContext(X.Z),We=U.prefixCls,Nt=We===void 0?"anticon":We,Se=oe()(Nt,se),Ke=oe()((0,M.Z)({},"".concat(Nt,"-spin"),!!At)),O=ot?{msTransform:"rotate(".concat(ot,"deg)"),transform:"rotate(".concat(ot,"deg)")}:void 0,wt=(0,P.Z)((0,P.Z)({},B.vD),{},{className:Ke,style:O,viewBox:He});He||delete wt.viewBox;var w=function(){return Qe?Y.createElement(Qe,(0,P.Z)({},wt),fe):fe?((0,B.Kp)(Boolean(He)||Y.Children.count(fe)===1&&Y.isValidElement(fe)&&Y.Children.only(fe).type==="use","Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),Y.createElement("svg",(0,P.Z)((0,P.Z)({},wt),{},{viewBox:He}),fe)):null},gt=Et;return gt===void 0&&p&&(gt=-1),Y.createElement("span",(0,P.Z)((0,P.Z)({role:"img"},u),{},{ref:qe,tabIndex:gt,onClick:p,className:Se}),w())});we.displayName="AntdIcon",ve.Z=we},88284:function(Ct,ve,m){"use strict";var P=m(28991),M=m(67294),G=m(32857),Y=m(27029),H=function(X,B){return M.createElement(Y.Z,(0,P.Z)((0,P.Z)({},X),{},{ref:B,icon:G.Z}))};H.displayName="CheckOutlined",ve.Z=M.forwardRef(H)},85175:function(Ct,ve,m){"use strict";var P=m(28991),M=m(67294),G=m(48820),Y=m(27029),H=function(X,B){return M.createElement(Y.Z,(0,P.Z)((0,P.Z)({},X),{},{ref:B,icon:G.Z}))};H.displayName="CopyOutlined",ve.Z=M.forwardRef(H)},56717:function(Ct,ve,m){"use strict";var P=m(28991),M=m(67294),G=m(93696),Y=m(27029),H=function(X,B){return M.createElement(Y.Z,(0,P.Z)((0,P.Z)({},X),{},{ref:B,icon:G.Z}))};H.displayName="InfoCircleOutlined",ve.Z=M.forwardRef(H)},79090:function(Ct,ve,m){"use strict";var P=m(28991),M=m(67294),G=m(15294),Y=m(27029),H=function(X,B){return M.createElement(Y.Z,(0,P.Z)((0,P.Z)({},X),{},{ref:B,icon:G.Z}))};H.displayName="LoadingOutlined",ve.Z=M.forwardRef(H)},76629:function(Ct,ve,m){"use strict";m.d(ve,{Z:function(){return X}});var P=m(28991),M=m(67294),G={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"},Y=G,H=m(27029),oe=function(J,we){return M.createElement(H.Z,(0,P.Z)((0,P.Z)({},J),{},{ref:we,icon:Y}))};oe.displayName="MenuFoldOutlined";var X=M.forwardRef(oe)},24616:function(Ct,ve,m){"use strict";m.d(ve,{Z:function(){return X}});var P=m(28991),M=m(67294),G={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"}}]},name:"setting",theme:"outlined"},Y=G,H=m(27029),oe=function(J,we){return M.createElement(H.Z,(0,P.Z)((0,P.Z)({},J),{},{ref:we,icon:Y}))};oe.displayName="SettingOutlined";var X=M.forwardRef(oe)},85317:function(Ct,ve,m){"use strict";var P=m(20862),M=m(95318);Object.defineProperty(ve,"__esModule",{value:!0}),ve.default=void 0;var G=M(m(81109)),Y=P(m(67294)),H=M(m(47356)),oe=M(m(92074)),X=function(we,be){return Y.createElement(oe.default,(0,G.default)((0,G.default)({},we),{},{ref:be,icon:H.default}))};X.displayName="ArrowLeftOutlined";var B=Y.forwardRef(X);ve.default=B},91724:function(Ct,ve,m){"use strict";var P=m(20862),M=m(95318);Object.defineProperty(ve,"__esModule",{value:!0}),ve.default=void 0;var G=M(m(81109)),Y=P(m(67294)),H=M(m(44149)),oe=M(m(92074)),X=function(we,be){return Y.createElement(oe.default,(0,G.default)((0,G.default)({},we),{},{ref:be,icon:H.default}))};X.displayName="ArrowRightOutlined";var B=Y.forwardRef(X);ve.default=B},36450:function(Ct,ve,m){"use strict";m.d(ve,{qc:function(){return oa},fT:function(){return jr},_z:function(){return Pr},Xn:function(){return Ft},WB:function(){return Oo},ZP:function(){return jl}});var P=m(38663),M=m(77935),G=m(70883),Y=m(94233),H=m(36017),oe=m(48736),X=m(57338),B=m(12001),J=m(54421),we=m(30887),be=m(34792),qe=m(20136),se=m(43358),Qe=m(49111),He=m(20228),At=m(77576),ot=m(18106),Et=m(22385),p=m(28991),fe=m(81253),u=m(85893),U=m(62582),We=m(88182),Nt=m(94184),Se=m.n(Nt),Ke=m(97435),O=m(67294),wt=m(73935),w=m(96156),gt=function(e){return(0,w.Z)({},e.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:"rgba(255, 255, 255, 0.58)",borderBlockStart:"1px solid ".concat(e.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",transition:"all 0.2s ease 0s","&-left":{flex:1},"&-right":{"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};function Mt(s){return(0,U.Xj)("footer-toolbar",function(e){var n=(0,p.Z)((0,p.Z)({},e),{},{componentCls:".".concat(s)});return[gt(n)]})}var xr=["children","className","extra","style","renderContent"],wr=function(e){var n=e.children,a=e.className,d=e.extra,v=e.style,b=e.renderContent,T=(0,fe.Z)(e,xr),A=(0,O.useContext)(We.ZP.ConfigContext),D=A.getPrefixCls,j=A.getTargetContainer,I=e.prefixCls||D("pro"),W="".concat(I,"-footer-bar"),ee=Mt(W),ne=ee.wrapSSR,ie=ee.hashId,le=(0,O.useContext)(Ft),je=(0,O.useMemo)(function(){var Ve=le.hasSiderMenu,at=le.isMobile,Rt=le.siderWidth;if(!!Ve)return Rt?at?"100%":"calc(100% - ".concat(Rt,"px)"):"100%"},[le.collapsed,le.hasSiderMenu,le.isMobile,le.siderWidth]),Oe=(0,O.useMemo)(function(){return(j==null?void 0:j())||document.querySelector(".ant-pro")||document.body},[]),Ye=(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"".concat(W,"-left ").concat(ie),children:d}),(0,u.jsx)("div",{className:"".concat(W,"-right ").concat(ie),children:n})]});(0,O.useEffect)(function(){return!le||!(le==null?void 0:le.setHasFooterToolbar)?function(){}:(le==null||le.setHasFooterToolbar(!0),function(){var Ve;le==null||(Ve=le.setHasFooterToolbar)===null||Ve===void 0||Ve.call(le,!1)})},[]);var ye=(0,u.jsx)("div",(0,p.Z)((0,p.Z)({className:Se()(a,ie,W),style:(0,p.Z)({width:je},v)},(0,Ke.Z)(T,["prefixCls"])),{},{children:b?b((0,p.Z)((0,p.Z)((0,p.Z)({},e),le),{},{leftWidth:je}),Ye):Ye})),Le=(0,U.jU)()?(0,wt.createPortal)(ye,Oe,W):ye;return ne(Le)},Ft=(0,O.createContext)({}),ke=function(e){return(0,w.Z)({},e.componentCls,{width:"100%","&-wide":{maxWidth:1152,margin:"0 auto"}})};function cr(s){return(0,U.Xj)("pro-layout-grid-content",function(e){var n=(0,p.Z)((0,p.Z)({},e),{},{componentCls:".".concat(s)});return[ke(n)]})}var jr=function(e){var n=(0,O.useContext)(Ft),a=e.children,d=e.contentWidth,v=e.className,b=e.style,T=(0,O.useContext)(We.ZP.ConfigContext),A=T.getPrefixCls,D=e.prefixCls||A("pro"),j=d||n.contentWidth,I="".concat(D,"-grid-content"),W=cr(I),ee=W.wrapSSR,ne=W.hashId,ie=j==="Fixed";return ee((0,u.jsx)("div",{className:Se()(I,ne,v,(0,w.Z)({},"".concat(I,"-wide"),ie)),style:b,children:(0,u.jsx)("div",{className:"".concat(D,"-grid-content-children ").concat(ne),children:a})}))},Vt=m(90484),er=m(78775),hr=m(72488),It=m(22122),ge=m(6610),st=m(5991),kt=m(46070),Xt=m(51814),ct=m(77608),ut=m(10379),mt=m(48717),Yt=m(98423),Zt=m(53124),Ht=m(85061),rr=m(75164);function br(s){var e,n=function(v){return function(){e=null,s.apply(void 0,(0,Ht.Z)(v))}},a=function(){if(e==null){for(var v=arguments.length,b=new Array(v),T=0;T<v;T++)b[T]=arguments[T];e=(0,rr.Z)(n(b))}};return a.cancel=function(){rr.Z.cancel(e),e=null},a}function Ar(){return function(e,n,a){var d=a.value,v=!1;return{configurable:!0,get:function(){if(v||this===e.prototype||this.hasOwnProperty(n))return d;var T=br(d.bind(this));return v=!0,Object.defineProperty(this,n,{value:T,configurable:!0,writable:!0}),v=!1,T}}}}var Kr=m(64019);function Mr(s){return s!==window?s.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function cn(s,e,n){if(n!==void 0&&e.top>s.top-n)return n+e.top}function ur(s,e,n){if(n!==void 0&&e.bottom<s.bottom+n){var a=window.innerHeight-e.bottom;return n+a}}var pt=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"],ar=[];function Or(){return ar}function Pn(s,e){if(!!s){var n=ar.find(function(a){return a.target===s});n?n.affixList.push(e):(n={target:s,affixList:[e],eventHandlers:{}},ar.push(n),pt.forEach(function(a){n.eventHandlers[a]=(0,Kr.Z)(s,a,function(){n.affixList.forEach(function(d){d.lazyUpdatePosition()})})}))}}function un(s){var e=ar.find(function(n){var a=n.affixList.some(function(d){return d===s});return a&&(n.affixList=n.affixList.filter(function(d){return d!==s})),a});e&&e.affixList.length===0&&(ar=ar.filter(function(n){return n!==e}),pt.forEach(function(n){var a=e.eventHandlers[n];a&&a.remove&&a.remove()}))}function Q(s,e,n){return e=(0,ct.Z)(e),(0,kt.Z)(s,(0,Xt.Z)()?Reflect.construct(e,n||[],(0,ct.Z)(s).constructor):e.apply(s,n))}var $e=function(s,e,n,a){var d=arguments.length,v=d<3?e:a===null?a=Object.getOwnPropertyDescriptor(e,n):a,b;if((typeof Reflect=="undefined"?"undefined":(0,Vt.Z)(Reflect))==="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(s,e,n,a);else for(var T=s.length-1;T>=0;T--)(b=s[T])&&(v=(d<3?b(v):d>3?b(e,n,v):b(e,n))||v);return d>3&&v&&Object.defineProperty(e,n,v),v};function V(){return typeof window!="undefined"?window:null}var Ae;(function(s){s[s.None=0]="None",s[s.Prepare=1]="Prepare"})(Ae||(Ae={}));var Re=function(s){(0,ut.Z)(e,s);function e(){var n;return(0,ge.Z)(this,e),n=Q(this,e,arguments),n.state={status:Ae.None,lastAffix:!1,prevTarget:null},n.getOffsetTop=function(){var a=n.props,d=a.offsetBottom,v=a.offsetTop;return d===void 0&&v===void 0?0:v},n.getOffsetBottom=function(){return n.props.offsetBottom},n.savePlaceholderNode=function(a){n.placeholderNode=a},n.saveFixedNode=function(a){n.fixedNode=a},n.measure=function(){var a=n.state,d=a.status,v=a.lastAffix,b=n.props.onChange,T=n.getTargetFunc();if(!(d!==Ae.Prepare||!n.fixedNode||!n.placeholderNode||!T)){var A=n.getOffsetTop(),D=n.getOffsetBottom(),j=T();if(!!j){var I={status:Ae.None},W=Mr(j),ee=Mr(n.placeholderNode),ne=cn(ee,W,A),ie=ur(ee,W,D);ee.top===0&&ee.left===0&&ee.width===0&&ee.height===0||(ne!==void 0?(I.affixStyle={position:"fixed",top:ne,width:ee.width,height:ee.height},I.placeholderStyle={width:ee.width,height:ee.height}):ie!==void 0&&(I.affixStyle={position:"fixed",bottom:ie,width:ee.width,height:ee.height},I.placeholderStyle={width:ee.width,height:ee.height}),I.lastAffix=!!I.affixStyle,b&&v!==I.lastAffix&&b(I.lastAffix),n.setState(I))}}},n.prepareMeasure=function(){if(n.setState({status:Ae.Prepare,affixStyle:void 0,placeholderStyle:void 0}),!1)var a},n}return(0,st.Z)(e,[{key:"getTargetFunc",value:function(){var a=this.context.getTargetContainer,d=this.props.target;return d!==void 0?d:a!=null?a:V}},{key:"componentDidMount",value:function(){var a=this,d=this.getTargetFunc();d&&(this.timeout=setTimeout(function(){Pn(d(),a),a.updatePosition()}))}},{key:"componentDidUpdate",value:function(a){var d=this.state.prevTarget,v=this.getTargetFunc(),b=(v==null?void 0:v())||null;d!==b&&(un(this),b&&(Pn(b,this),this.updatePosition()),this.setState({prevTarget:b})),(a.offsetTop!==this.props.offsetTop||a.offsetBottom!==this.props.offsetBottom)&&this.updatePosition(),this.measure()}},{key:"componentWillUnmount",value:function(){clearTimeout(this.timeout),un(this),this.updatePosition.cancel(),this.lazyUpdatePosition.cancel()}},{key:"updatePosition",value:function(){this.prepareMeasure()}},{key:"lazyUpdatePosition",value:function(){var a=this.getTargetFunc(),d=this.state.affixStyle;if(a&&d){var v=this.getOffsetTop(),b=this.getOffsetBottom(),T=a();if(T&&this.placeholderNode){var A=Mr(T),D=Mr(this.placeholderNode),j=cn(D,A,v),I=ur(D,A,b);if(j!==void 0&&d.top===j||I!==void 0&&d.bottom===I)return}}this.prepareMeasure()}},{key:"render",value:function(){var a=this,d=this.state,v=d.affixStyle,b=d.placeholderStyle,T=this.props,A=T.affixPrefixCls,D=T.children,j=Se()((0,w.Z)({},A,!!v)),I=(0,Yt.Z)(this.props,["prefixCls","offsetTop","offsetBottom","target","onChange","affixPrefixCls"]);return O.createElement(mt.Z,{onResize:function(){a.updatePosition()}},O.createElement("div",(0,It.Z)({},I,{ref:this.savePlaceholderNode}),v&&O.createElement("div",{style:b,"aria-hidden":"true"}),O.createElement("div",{className:j,ref:this.saveFixedNode,style:v},O.createElement(mt.Z,{onResize:function(){a.updatePosition()}},D))))}}]),e}(O.Component);Re.contextType=Zt.E_,$e([Ar()],Re.prototype,"updatePosition",null),$e([Ar()],Re.prototype,"lazyUpdatePosition",null);var Ce=O.forwardRef(function(s,e){var n=s.prefixCls,a=O.useContext(Zt.E_),d=a.getPrefixCls,v=d("affix",n),b=(0,It.Z)((0,It.Z)({},s),{affixPrefixCls:v});return O.createElement(Re,(0,It.Z)({},b,{ref:e}))}),_e=Ce,rt=function(e,n){var a,d=(0,p.Z)({},e);return(0,p.Z)((0,p.Z)({bgLayout:"linear-gradient(#fff, #f7f8fa 28%)",colorTextAppListIcon:"#666",appListIconHoverBgColor:d==null||(a=d.sider)===null||a===void 0?void 0:a.colorBgMenuItemSelected,colorBgAppListIconHover:"rgba(0, 0, 0, 0.04)",colorTextAppListIconHover:n.colorTextBase},d),{},{header:(0,p.Z)({colorBgHeader:"rgba(240, 242, 245, 0.4)",colorHeaderTitle:n.colorText,colorBgMenuItemHover:(0,U.uK)(n.colorTextBase,.03),colorBgMenuItemSelected:"transparent",colorTextMenuSelected:(0,U.uK)(n.colorTextBase,.95),colorBgRightActionsItemHover:(0,U.uK)(n.colorTextBase,.03),colorTextRightActionsItem:n.colorTextTertiary,heightLayoutHeader:56,colorTextMenu:(0,U.uK)(n.colorTextBase,.65),colorTextMenuSecondary:n.colorTextTertiary,colorTextMenuTitle:n.colorText,colorTextMenuActive:n.colorText},d.header),sider:(0,p.Z)({paddingInlineLayoutMenu:8,paddingBlockLayoutMenu:8,colorBgCollapsedButton:"#fff",colorTextCollapsedButtonHover:n.colorTextSecondary,colorTextCollapsedButton:(0,U.uK)(n.colorTextBase,.25),colorMenuBackground:"transparent",colorBgMenuItemCollapsedHover:"rgba(90, 75, 75, 0.03)",colorBgMenuItemCollapsedSelected:(0,U.uK)(n.colorTextBase,.04),colorMenuItemDivider:(0,U.uK)(n.colorTextBase,.06),colorBgMenuItemHover:(0,U.uK)(n.colorTextBase,.03),colorBgMenuItemSelected:(0,U.uK)(n.colorTextBase,.04),colorTextMenuSelected:(0,U.uK)(n.colorTextBase,.95),colorTextMenuActive:n.colorText,colorTextMenu:(0,U.uK)(n.colorTextBase,.65),colorTextMenuSecondary:n.colorTextTertiary,colorTextMenuTitle:n.colorText,colorTextSubMenuSelected:(0,U.uK)(n.colorTextBase,.95)},d.sider),pageContainer:(0,p.Z)({colorBgPageContainer:"transparent",marginBlockPageContainerContent:24,marginInlinePageContainerContent:40,colorBgPageContainerFixed:"#fff"},d.pageContainer)})},Lt=rt({},{}),Ee=O.createContext(Lt),yt=function(e){var n=(0,U.dQ)(),a=n.token,d=n.hashId;return(0,u.jsx)(Ee.Provider,{value:(0,p.Z)((0,p.Z)({},rt((e==null?void 0:e.token)||{},a)),{},{hashId:d}),children:e.children})},tt=m(28481),Dt=m(77404),Sr=m.n(Dt),ir=m(86056),Lr=m.n(ir),dr=m(71577),et=m(35247),Yr=m(51890),_r=m(19650),S=function(){return{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}},_=function(e){var n,a;return(0,w.Z)({},e.componentCls,(0,p.Z)((0,p.Z)({},U.Wf===null||U.Wf===void 0?void 0:(0,U.Wf)(e)),{},(a={position:"relative",backgroundColor:e.colorBgContainer,paddingBlock:e.pageHeaderPaddingVertical,paddingInline:e.pageHeaderPadding,"&-ghost":{backgroundColor:e.pageHeaderBgGhost},"&-has-breadcrumb":{paddingBlockStart:e.pageHeaderPaddingBreadCrumb},"&-has-footer":{paddingBlockEnd:0},"&-back":(0,w.Z)({marginInlineEnd:e.margin,fontSize:16,lineHeight:1,"&-button":(0,p.Z)((0,p.Z)({},U.Nd===null||U.Nd===void 0?void 0:(0,U.Nd)(e)),{},{color:e.pageHeaderColorBack,cursor:"pointer"})},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:0})},(0,w.Z)(a,"ant".concat("-divider-vertical"),{height:14,marginBlock:0,marginInline:e.marginSM,verticalAlign:"middle"}),(0,w.Z)(a,"&-breadcrumb + &-heading",{marginBlockStart:e.marginXS}),(0,w.Z)(a,"&-heading",{display:"flex",justifyContent:"space-between","&-left":{display:"flex",alignItems:"center",marginBlock:e.marginXS/2,marginInlineEnd:0,marginInlineStart:0,overflow:"hidden"},"&-title":(0,p.Z)((0,p.Z)({marginInlineEnd:e.marginSM,marginBlockEnd:0,color:e.colorTextHeading,fontWeight:600,fontSize:e.pageHeaderFontSizeHeaderTitle,lineHeight:e.controlHeight+"px"},S()),{},(0,w.Z)({},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:0,marginInlineStart:e.marginSM})),"&-avatar":(0,w.Z)({marginInlineEnd:e.marginSM},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:e.marginSM}),"&-tags":(0,w.Z)({},"".concat(e.componentCls,"-rlt &"),{float:"right"}),"&-sub-title":(0,p.Z)((0,p.Z)({marginInlineEnd:e.marginSM,color:e.colorTextSecondary,fontSize:e.pageHeaderFontSizeHeaderSubTitle,lineHeight:e.lineHeight},S()),{},(0,w.Z)({},"".concat(e.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:12})),"&-extra":(n={marginBlock:e.marginXS/2,marginInlineEnd:0,marginInlineStart:0,whiteSpace:"nowrap","> *":(0,w.Z)({"white-space":"unset"},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:e.marginSM,marginInlineStart:0})},(0,w.Z)(n,"".concat(e.componentCls,"-rlt &"),{float:"left"}),(0,w.Z)(n,"*:first-child",(0,w.Z)({},"".concat(e.componentCls,"-rlt &"),{marginInlineEnd:0})),n)}),(0,w.Z)(a,"&-content",{paddingBlockStart:e.pageHeaderPaddingContentPadding}),(0,w.Z)(a,"&-footer",{marginBlockStart:e.margin}),(0,w.Z)(a,"&-compact &-heading",{flexWrap:"wrap"}),(0,w.Z)(a,"&-rtl",{direction:"rtl"}),a)))};function z(s){return(0,U.Xj)("page-header",function(e){var n=(0,p.Z)((0,p.Z)({},e),{},{componentCls:".".concat(s),pageHeaderBgGhost:"transparent",pageHeaderPadding:16,pageHeaderPaddingVertical:8,pageHeaderPaddingBreadCrumb:e.paddingSM,pageHeaderColorBack:e.colorIcon,pageHeaderFontSizeHeaderTitle:e.fontSizeHeading4,pageHeaderFontSizeHeaderSubTitle:14,pageHeaderPaddingContentPadding:e.paddingSM});return[_(n)]})}var Ne=function(e,n,a){return!n||!a?null:(0,u.jsx)("div",{className:"".concat(e,"-back"),children:(0,u.jsx)(dr.Z,{type:"text",onClick:function(v){a==null||a(v)},className:"".concat(e,"-back-button"),"aria-label":"back",children:n})})},it=function(e,n){return(0,u.jsx)(et.Z,(0,p.Z)((0,p.Z)({},e),{},{className:Se()("".concat(n,"-breadcrumb"),e.className)}))},Xe=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"ltr";return e.backIcon!==void 0?e.backIcon:n==="rtl"?(0,u.jsx)(Lr(),{}):(0,u.jsx)(Sr(),{})},dt=function(e,n){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"ltr",d=arguments.length>3?arguments[3]:void 0,v=n.title,b=n.avatar,T=n.subTitle,A=n.tags,D=n.extra,j=n.onBack,I="".concat(e,"-heading"),W=v||T||A||D;if(!W)return null;var ee=Xe(n,a),ne=Ne(e,ee,j),ie=ne||b||W;return(0,u.jsxs)("div",{className:I+" "+d,children:[ie&&(0,u.jsxs)("div",{className:"".concat(I,"-left ").concat(d),children:[ne,b&&(0,u.jsx)(Yr.C,(0,p.Z)({className:Se()("".concat(I,"-avatar"),d,b.className)},b)),v&&(0,u.jsx)("span",{className:"".concat(I,"-title ").concat(d),title:typeof v=="string"?v:void 0,children:v}),T&&(0,u.jsx)("span",{className:"".concat(I,"-sub-title ").concat(d),title:typeof T=="string"?T:void 0,children:T}),A&&(0,u.jsx)("span",{className:"".concat(I,"-tags ").concat(d),children:A})]}),D&&(0,u.jsx)("span",{className:"".concat(I,"-extra ").concat(d),children:(0,u.jsx)(_r.Z,{children:D})})]})},xt=function(e,n,a){return n?(0,u.jsx)("div",{className:"".concat(e,"-footer ").concat(a),children:n}):null},yn=function(e,n,a){return(0,u.jsx)("div",{className:"".concat(e,"-content ").concat(a),children:n})},tn=function(e){var n,a,d=(0,U.FH)(!1),v=(0,tt.Z)(d,2),b=v[0],T=v[1],A=function(or){var en=or.width;T(en<768,!0)},D=O.useContext(We.ZP.ConfigContext),j=D.getPrefixCls,I=D.pageHeader,W=D.direction,ee=e.prefixCls,ne=e.style,ie=e.footer,le=e.children,je=e.breadcrumb,Oe=e.breadcrumbRender,Ye=e.className,ye=!0;"ghost"in e?ye=e.ghost:I&&"ghost"in I&&(ye=I.ghost);var Le=j("page-header",ee),Ve=z(Le),at=Ve.wrapSSR,Rt=Ve.hashId,Bt=function(){return(je==null?void 0:je.routes)?it(je,Le):null},Wt=Bt(),pr=je&&"props"in je,Er=(n=Oe==null?void 0:Oe((0,p.Z)((0,p.Z)({},e),{},{prefixCls:Le}),Wt))!==null&&n!==void 0?n:Wt,$t=pr?je:Er,Hr=Se()(Le,e.className,Ye,(a={hashId:Rt},(0,w.Z)(a,"".concat(Le,"-has-breadcrumb"),!!$t),(0,w.Z)(a,"".concat(Le,"-has-footer"),!!ie),(0,w.Z)(a,"".concat(Le,"-ghost"),ye),(0,w.Z)(a,"".concat(Le,"-rtl"),W==="rtl"),(0,w.Z)(a,"".concat(Le,"-compact"),b),a)),Ge=dt(Le,e,W,Rt),Zr=le&&yn(Le,le,Rt),Mn=xt(Le,ie,Rt);return!$t&&!Ge&&!Mn&&!Zr?null:at((0,u.jsx)(mt.Z,{onResize:A,children:(0,u.jsxs)("div",{className:Hr,style:ne,children:[$t,Ge,Zr,Mn]})}))},rn=m(11382),Xa=["isLoading","pastDelay","timedOut","error","retry"],Wn=function(e){var n=e.isLoading,a=e.pastDelay,d=e.timedOut,v=e.error,b=e.retry,T=(0,fe.Z)(e,Xa);return(0,u.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,u.jsx)(rn.Z,(0,p.Z)({size:"large"},T))})},Bi=function(e){if(!e)return 1;var n=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n},Qa=function(e){var n=e.children,a=e.style,d=e.className,v=e.markStyle,b=e.markClassName,T=e.zIndex,A=T===void 0?9:T,D=e.gapX,j=D===void 0?212:D,I=e.gapY,W=I===void 0?222:I,ee=e.width,ne=ee===void 0?120:ee,ie=e.height,le=ie===void 0?64:ie,je=e.rotate,Oe=je===void 0?-22:je,Ye=e.image,ye=e.content,Le=e.offsetLeft,Ve=e.offsetTop,at=e.fontStyle,Rt=at===void 0?"normal":at,Bt=e.fontWeight,Wt=Bt===void 0?"normal":Bt,pr=e.fontColor,Er=pr===void 0?"rgba(0,0,0,.15)":pr,$t=e.fontSize,Hr=$t===void 0?16:$t,Ge=e.fontFamily,Zr=Ge===void 0?"sans-serif":Ge,Mn=e.prefixCls,Dn=(0,O.useContext)(We.ZP.ConfigContext),or=Dn.getPrefixCls,en=or("pro-layout-watermark",Mn),zn=Se()("".concat(en,"-wrapper"),d),ta=Se()(en,b),ha=(0,O.useState)(""),Ra=(0,tt.Z)(ha,2),Na=Ra[0],Di=Ra[1];return(0,O.useEffect)(function(){var mn=document.createElement("canvas"),lr=mn.getContext("2d"),sr=Bi(lr),Un="".concat((j+ne)*sr,"px"),Ya="".concat((W+le)*sr,"px"),Vr=Le||j/2,ji=Ve||W/2;if(mn.setAttribute("width",Un),mn.setAttribute("height",Ya),lr){lr.translate(Vr*sr,ji*sr),lr.rotate(Math.PI/180*Number(Oe));var vr=ne*sr,Gt=le*sr;if(Ye){var yr=new Image;yr.crossOrigin="anonymous",yr.referrerPolicy="no-referrer",yr.src=Ye,yr.onload=function(){lr.drawImage(yr,0,0,vr,Gt),Di(mn.toDataURL())}}else if(ye){var sn=Number(Hr)*sr;lr.font="".concat(Rt," normal ").concat(Wt," ").concat(sn,"px/").concat(Gt,"px ").concat(Zr),lr.fillStyle=Er,Array.isArray(ye)?ye==null||ye.forEach(function(pn,Ia){return lr.fillText(pn,0,Ia*50)}):lr.fillText(ye,0,0),Di(mn.toDataURL())}}else console.error("\u5F53\u524D\u73AF\u5883\u4E0D\u652F\u6301Canvas")},[j,W,Le,Ve,Oe,Rt,Wt,ne,le,Zr,Er,Ye,ye,Hr]),(0,u.jsxs)("div",{style:(0,p.Z)({position:"relative"},a),className:zn,children:[n,(0,u.jsx)("div",{className:ta,style:(0,p.Z)((0,p.Z)({zIndex:A,position:"absolute",left:0,top:0,width:"100%",height:"100%",backgroundSize:"".concat(j+ne,"px"),pointerEvents:"none",backgroundRepeat:"repeat"},Na?{backgroundImage:"url('".concat(Na,"')")}:{}),v)})]})},Ni=[576,768,992,1200].map(function(s){return"@media (min-width: ".concat(s,"px)")}),ra=(0,tt.Z)(Ni,4),ka=ra[0],Fr=ra[1],Fn=ra[2],Vn=ra[3],dn=function(e){var n,a;return(0,w.Z)({},e.componentCls,(a={position:"relative","&-layout-has-margin":{marginBlock:e.marginBlockPageContainerContent,marginInline:-e.marginInlinePageContainerContent},"&-children-content":{marginBlock:e.marginBlockPageContainerContent,marginInline:e.marginInlinePageContainerContent},"&-affix":(0,w.Z)({},"".concat(e.antCls,"-affix"),(0,w.Z)({},"".concat(e.componentCls,"-warp"),{backgroundColor:e.colorBgPageContainerFixed,boxShadow:"0 2px 8px #f0f1f2"}))},(0,w.Z)(a,"& &-warp-page-header",(0,w.Z)({marginBlockEnd:e.marginBlockPageContainerContent/2,paddingInlineStart:e.marginInlinePageContainerContent,paddingInlineEnd:e.marginInlinePageContainerContent},"& ~ ".concat(e.proComponentsCls,"-grid-content"),(0,w.Z)({},"".concat(e.proComponentsCls,"-page-container-children-content"),{marginBlock:e.marginBlockPageContainerContent/3}))),(0,w.Z)(a,"&-detail",(0,w.Z)({display:"flex"},ka,{display:"block"})),(0,w.Z)(a,"&-main",{width:"100%"}),(0,w.Z)(a,"&-row",(0,w.Z)({display:"flex",width:"100%"},Fr,{display:"block"})),(0,w.Z)(a,"&-content",{flex:"auto",width:"100%"}),(0,w.Z)(a,"&-extraContent",(n={flex:"0 1 auto",minWidth:"242px",marginInlineStart:88,textAlign:"end"},(0,w.Z)(n,ka,{marginInlineStart:0}),(0,w.Z)(n,Fr,{marginInlineStart:0,textAlign:"start"}),(0,w.Z)(n,Fn,{marginInlineStart:20}),(0,w.Z)(n,Vn,{marginInlineStart:44}),n)),a))};function lo(s){var e=(0,O.useContext)(Ee),n=e.pageContainer;return(0,U.Xj)("page-container",function(a){var d=(0,p.Z)((0,p.Z)({},a),{},{componentCls:".".concat(s)},n);return[dn(d)]})}var fn=["title","content","pageHeaderRender","header","prefixedClassName","extraContent","style","prefixCls","hashId","value","breadcrumbRender"],vn=["children","loading","className","style","footer","affixProps","fixedHeader","breadcrumbRender"];function Br(s){return(0,Vt.Z)(s)==="object"?s:{spinning:s}}var $r=function(e){var n=e.tabList,a=e.tabActiveKey,d=e.onTabChange,v=e.hashId,b=e.tabBarExtraContent,T=e.tabProps,A=e.prefixedClassName;return Array.isArray(n)||b?(0,u.jsx)(hr.Z,(0,p.Z)((0,p.Z)({className:"".concat(A,"-tabs ").concat(v),activeKey:a,onChange:function(j){d&&d(j)},tabBarExtraContent:b,items:n==null?void 0:n.map(function(D,j){var I;return(0,p.Z)((0,p.Z)({label:D.tab},D),{},{key:((I=D.key)===null||I===void 0?void 0:I.toString())||(j==null?void 0:j.toString())})})},T),{},{children:n==null?void 0:n.map(function(D,j){return(0,u.jsx)(hr.Z.TabPane,(0,p.Z)({tab:D.tab},D),D.key||j)})})):null},xn=function(e,n,a,d){return!e&&!n?null:(0,u.jsx)("div",{className:"".concat(a,"-detail ").concat(d),children:(0,u.jsx)("div",{className:"".concat(a,"-main ").concat(d),children:(0,u.jsxs)("div",{className:"".concat(a,"-row ").concat(d),children:[e&&(0,u.jsx)("div",{className:"".concat(a,"-content ").concat(d),children:e}),n&&(0,u.jsx)("div",{className:"".concat(a,"-extraContent ").concat(d),children:n})]})})})},na=function(e){var n=useContext(RouteContext);return _jsx("div",{style:{height:"100%",display:"flex",alignItems:"center"},children:_jsx(Breadcrumb,_objectSpread(_objectSpread(_objectSpread({},n==null?void 0:n.breadcrumb),n==null?void 0:n.breadcrumbProps),e))})},Xr=function(e){var n,a=e.title,d=e.content,v=e.pageHeaderRender,b=e.header,T=e.prefixedClassName,A=e.extraContent,D=e.style,j=e.prefixCls,I=e.hashId,W=e.value,ee=e.breadcrumbRender,ne=(0,fe.Z)(e,fn),ie=function(){if(!!ee)return ee};if(v===!1)return null;if(v)return(0,u.jsxs)(u.Fragment,{children:[" ",v((0,p.Z)((0,p.Z)({},e),W))]});var le=a;!a&&a!==!1&&(le=W.title);var je=(0,p.Z)((0,p.Z)((0,p.Z)({},W),{},{title:le},ne),{},{footer:$r((0,p.Z)((0,p.Z)({},ne),{},{hashId:I,breadcrumbRender:ee,prefixedClassName:T}))},b),Oe=je.breadcrumb,Ye=(!Oe||!(Oe==null?void 0:Oe.itemRender)&&!(Oe==null||(n=Oe.routes)===null||n===void 0?void 0:n.length))&&!ee;return["title","subTitle","extra","tags","footer","avatar","backIcon"].every(function(ye){return!je[ye]})&&Ye&&!d&&!A?null:(0,u.jsx)(tn,(0,p.Z)((0,p.Z)({},je),{},{className:"".concat(T,"-warp-page-header ").concat(I),breadcrumb:ee===!1?void 0:(0,p.Z)((0,p.Z)({},je.breadcrumb),W.breadcrumbProps),breadcrumbRender:ie(),prefixCls:j,children:(b==null?void 0:b.children)||xn(d,A,T,I)}))},Pr=function(e){var n,a,d=e.children,v=e.loading,b=v===void 0?!1:v,T=e.className,A=e.style,D=e.footer,j=e.affixProps,I=e.fixedHeader,W=e.breadcrumbRender,ee=(0,fe.Z)(e,vn),ne=(0,O.useContext)(Ft);(0,O.useEffect)(function(){var Ge;return!ne||!(ne==null?void 0:ne.setHasPageContainer)?function(){}:(ne==null||(Ge=ne.setHasPageContainer)===null||Ge===void 0||Ge.call(ne,!0),function(){var Zr;ne==null||(Zr=ne.setHasPageContainer)===null||Zr===void 0||Zr.call(ne,!1)})},[]);var ie=(0,O.useContext)(Ee),le=ie.pageContainer,je=(0,O.useContext)(We.ZP.ConfigContext),Oe=je.getPrefixCls,Ye=e.prefixCls||Oe("pro"),ye="".concat(Ye,"-page-container"),Le=lo(ye),Ve=Le.wrapSSR,at=Le.hashId,Rt=(0,O.useMemo)(function(){var Ge;return W==!1?!1:W||(ee==null||(Ge=ee.header)===null||Ge===void 0?void 0:Ge.breadcrumbRender)},[W,ee==null||(n=ee.header)===null||n===void 0?void 0:n.breadcrumbRender]),Bt=Xr((0,p.Z)((0,p.Z)({},ee),{},{breadcrumbRender:Rt,ghost:!0,hashId:at,prefixCls:void 0,prefixedClassName:ye,value:ne})),Wt=(0,O.useMemo)(function(){if(O.isValidElement(b))return b;if(typeof b=="boolean"&&!b)return null;var Ge=Br(b);return Ge.spinning?(0,u.jsx)(Wn,(0,p.Z)({},Ge)):null},[b]),pr=(0,O.useMemo)(function(){return d?(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:Se()("".concat(ye,"-children-content ").concat(at)),children:d}),ne.hasFooterToolbar&&(0,u.jsx)("div",{style:{height:64,marginBlockStart:le.marginBlockPageContainerContent}})]}):null},[d,ye,at,ne.hasFooterToolbar,le.marginBlockPageContainerContent]),Er=(0,O.useMemo)(function(){var Ge=Wt||pr;if(e.waterMarkProps||ne.waterMarkProps){var Zr=(0,p.Z)((0,p.Z)({},ne.waterMarkProps),e.waterMarkProps);return(0,u.jsx)(Qa,(0,p.Z)((0,p.Z)({},Zr),{},{children:Ge}))}return Ge},[e.waterMarkProps,ne.waterMarkProps,Wt,pr]),$t=Se()(ye,at,T,(a={},(0,w.Z)(a,"".concat(ye,"-ghost"),!0),(0,w.Z)(a,"".concat(ye,"-with-footer"),D),(0,w.Z)(a,"".concat(ye,"-with-affix"),I&&Bt),a)),Hr=(0,O.useContext)(Ee);return Ve((0,u.jsxs)(er.oK,{children:[(0,u.jsxs)("div",{style:A,className:$t,children:[I&&Bt?(0,u.jsx)(_e,(0,p.Z)((0,p.Z)({offsetTop:ne.hasHeader&&ne.fixedHeader?Hr.header.heightLayoutHeader:0},j),{},{className:"".concat(ye,"-affix ").concat(at),children:Bt})):Bt,Er&&(0,u.jsx)(jr,{children:Er})]}),D&&(0,u.jsx)(wr,{prefixCls:Ye,children:D})]}))},Kn=function(e){var n=useContext(RouteContext);return Xr(_objectSpread(_objectSpread({},e),{},{hashId:"",value:n}))},bn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"}}]},name:"copyright",theme:"outlined"},Ja=bn,aa=m(27029),ia=function(e,n){return O.createElement(aa.Z,(0,p.Z)((0,p.Z)({},e),{},{ref:n,icon:Ja}))};ia.displayName="CopyrightOutlined";var Bn=O.forwardRef(ia),$n=m(2897),qa=m(90999),ga=$n.ZP;ga.Header=$n.h4,ga.Footer=$n.$_,ga.Content=$n.VY,ga.Sider=qa.Z,ga._InternalSiderContext=qa.D;var Ha=ga,so=function(e){return(0,w.Z)({},e.componentCls,{marginBlock:0,marginBlockStart:48,marginBlockEnd:24,marginInline:0,paddingBlock:0,paddingInline:16,textAlign:"center","&-list":{marginBlockEnd:8,color:e.colorTextSecondary,"&-link":{color:e.colorTextSecondary},"*:not(:last-child)":{marginInlineEnd:8,"&:hover":{color:e.colorText}}},"&-copyright":{fontSize:"14px",color:e.colorText}})};function vl(s){return(0,U.Xj)("pro-layout-footer",function(e){var n=(0,p.Z)((0,p.Z)({},e),{},{componentCls:".".concat(s)});return[so(n)]})}var Qr=function(e){var n=e.className,a=e.prefixCls,d=e.links,v=e.copyright,b=e.style,T=(0,O.useContext)(We.ZP.ConfigContext),A=T.getPrefixCls(a||"pro-global-footer"),D=vl(A),j=D.wrapSSR,I=D.hashId;return(d==null||d===!1||Array.isArray(d)&&d.length===0)&&(v==null||v===!1)?null:j((0,u.jsxs)("div",{className:Se()(A,I,n),style:b,children:[d&&(0,u.jsx)("div",{className:"".concat(A,"-list ").concat(I),children:d.map(function(W){return(0,u.jsx)("a",{className:"".concat(A,"-list-link ").concat(I),title:W.key,target:W.blankTarget?"_blank":"_self",href:W.href,rel:"noreferrer",children:W.title},W.key)})}),v&&(0,u.jsx)("div",{className:"".concat(A,"-copyright ").concat(I),children:v})]}))},gi=Ha.Footer,oa=function(e){var n=e.links,a=e.copyright,d=e.style,v=e.className,b=e.prefixCls;return(0,u.jsx)(gi,{className:v,style:(0,p.Z)({padding:0},d),children:(0,u.jsx)(Qr,{links:n,prefixCls:b,copyright:a===!1?null:(0,u.jsxs)(O.Fragment,{children:[(0,u.jsx)(Bn,{})," ",a]})})})},Wo=function(e){return(0,w.Z)({},e.proLayoutCls,(0,w.Z)({},".ant-layout-header".concat(e.componentCls),{height:e.ProLayoutHeaderHeaderHeight,lineHeight:"".concat(e.ProLayoutHeaderHeaderHeight,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:8,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:e.colorBgHeader||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}))};function Gn(s,e){var n=(0,O.useContext)(Ee),a=n.header;return(0,U.Xj)("pro-layout-header",function(d){var v=(0,p.Z)((0,p.Z)({},d),{},{componentCls:".".concat(s),proLayoutCls:".".concat(e.proLayoutCls),ProLayoutHeaderHeaderHeight:a.heightLayoutHeader},a);return[Wo(v)]})}var co=function s(e){return(e||[]).reduce(function(n,a){if(a.key&&n.push(a.key),a.routes){var d=n.concat(s(a.routes)||[]);return d}return n},[])},ei={techBlue:"#1677FF",daybreak:"#1890ff",dust:"#F5222D",volcano:"#FA541C",sunset:"#FAAD14",cyan:"#13C2C2",green:"#52C41A",geekblue:"#2F54EB",purple:"#722ED1"};function uo(s){return s&&ei[s]?ei[s]:s}function ma(s){return s.map(function(e){var n=e.children||[],a=(0,p.Z)({},e);if(!a.children&&a.routes&&(a.children=a.routes),!a.name||a.hideInMenu)return null;if(a&&(a==null?void 0:a.children)){if(!a.hideChildrenInMenu&&n.some(function(d){return d&&d.name&&!d.hideInMenu}))return(0,p.Z)((0,p.Z)({},e),{},{children:ma(n)});delete a.children}return delete a.routes,a}).filter(function(e){return e})}var hl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"},fo=hl,ti=function(e,n){return O.createElement(aa.Z,(0,p.Z)((0,p.Z)({},e),{},{ref:n,icon:fo}))};ti.displayName="MenuUnfoldOutlined";var gl=O.forwardRef(ti),Vo=m(76629),ri=m(28293),_a=m(55241),mi=function(){return(0,u.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,u.jsx)("path",{d:"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"})})},vo=function(e){var n=e.appList,a=e.baseClassName,d=e.hashId;return(0,u.jsx)("div",{className:"".concat(a,"-content ").concat(d),children:(0,u.jsx)("ul",{className:"".concat(a,"-content-list ").concat(d),children:n==null?void 0:n.map(function(v,b){return(0,u.jsx)("li",{className:"".concat(a,"-content-list-item ").concat(d),children:(0,u.jsxs)("a",{href:v.url,target:v.target,rel:"noreferrer",children:[pa(v.icon),(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{children:v.title}),v.desc?(0,u.jsx)("span",{children:v.desc}):null]})]})},b)})})})},Ko=function(e,n){if(e&&typeof e=="string"&&(0,U.CB)(e))return(0,u.jsx)("img",{src:e,alt:"logo"});if(typeof e=="function")return e();if(e&&typeof e=="string")return(0,u.jsx)("div",{id:"avatarLogo",children:e});if(!e&&n&&typeof n=="string"){var a=n.substring(0,1);return(0,u.jsx)("div",{id:"avatarLogo",children:a})}return e},ki=function(e){var n=e.appList,a=e.baseClassName,d=e.hashId;return(0,u.jsx)("div",{className:"".concat(a,"-content ").concat(d),children:(0,u.jsx)("ul",{className:"".concat(a,"-content-list ").concat(d),children:n==null?void 0:n.map(function(v,b){return(0,u.jsx)("li",{className:"".concat(a,"-content-list-item ").concat(d),children:(0,u.jsxs)("a",{href:v.url,target:v.target,rel:"noreferrer",children:[Ko(v.icon,v.title),(0,u.jsx)("div",{children:(0,u.jsx)("div",{children:v.title})})]})},b)})})})},$o=function(e){return{"&-content":{"&-list":{boxSizing:"content-box",maxWidth:656,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","> *":{boxSizing:"border-box",fontFamily:e.fontFamily},"&-item":{position:"relative",display:"inline-block",width:328,height:72,paddingInline:16,paddingBlock:16,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.radiusBase,"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.03)"},"*":{boxSizing:"border-box",fontFamily:e.fontFamily},"* div":U.Wf===null||U.Wf===void 0?void 0:(0,U.Wf)(e),a:{display:"flex",height:"100%",fontSize:12,textDecoration:"none","& > img":{width:40,height:40},"& > div":{marginInlineStart:14,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},ml=function(e){return{"&-content":{"&-list":{boxSizing:"border-box",maxWidth:376,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","*":{boxSizing:"border-box",fontFamily:e.fontFamily},"&-item":{position:"relative",display:"inline-block",width:104,height:104,marginBlock:8,marginInline:8,paddingInline:24,paddingBlock:24,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.radiusBase,"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.03)"},a:{display:"flex",flexDirection:"column",alignItems:"center",height:"100%",fontSize:12,textDecoration:"none","& > #avatarLogo":{width:40,height:40,margin:"0 auto",color:e.colorPrimary,fontSize:22,lineHeight:"40px",textAlign:"center",backgroundImage:"linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",borderRadius:e.radiusBase},"& > img":{width:40,height:40},"& > div":{marginBlockStart:5,marginInlineStart:0,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},Go=function(e){var n;return(0,w.Z)({},e.componentCls,{"&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInline:4,paddingBlock:0,fontSize:14,lineHeight:"14px",height:28,width:28,cursor:"pointer",color:e.colorTextAppListIcon,"&:hover":{color:e.colorTextAppListIconHover,backgroundColor:e.colorBgAppListIconHover},"&-active":{color:e.colorTextAppListIconHover,backgroundColor:e.colorBgAppListIconHover}},"&-popover":(n={},(0,w.Z)(n,"".concat(e.antCls,"-popover-arrow"),{display:"none"}),(0,w.Z)(n,"*",{boxSizing:"border-box",fontFamily:e.fontFamily}),n),"&-simple":ml(e),"&-default":$o(e)})};function pi(s){var e=(0,O.useContext)(Ee);return(0,U.Xj)("apps-logo-components",function(n){var a=(0,p.Z)((0,p.Z)({},n),{},{componentCls:".".concat(s)},e);return[Go(a)]})}var pa=function(e){return typeof e=="string"?(0,u.jsx)("img",{src:e,alt:"logo"}):typeof e=="function"?e():e},ya=function(e){var n,a=e.appList,d=e.prefixCls,v=d===void 0?"ant-pro":d,b=O.useRef(null),T="".concat(v,"-layout-apps"),A=pi(T),D=A.wrapSSR,j=A.hashId,I=(0,O.useState)(!1),W=(0,tt.Z)(I,2),ee=W[0],ne=W[1],ie=(0,O.useMemo)(function(){var je=a==null?void 0:a.some(function(Oe){return!(Oe==null?void 0:Oe.desc)});return je?(0,u.jsx)(ki,{hashId:j,appList:a,baseClassName:"".concat(T,"-simple")}):(0,u.jsx)(vo,{hashId:j,appList:a,baseClassName:"".concat(T,"-default")})},[a,T,j]);if(!(e==null||(n=e.appList)===null||n===void 0?void 0:n.length))return null;var le=(0,U.n4)(ri.Z,"4.23.0")>-1?{onOpenChange:ne}:{onVisibleChange:function(){return ne}};return D((0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{ref:b}),(0,u.jsx)(_a.Z,(0,p.Z)((0,p.Z)({placement:"bottomRight",trigger:["click"],zIndex:9999,arrowPointAtCenter:!0},le),{},{overlayClassName:"".concat(T,"-popover ").concat(j),content:ie,getPopupContainer:function(){return b.current||document.body},children:(0,u.jsx)("span",{onClick:function(Oe){Oe.stopPropagation()},className:Se()("".concat(T,"-icon"),j,(0,w.Z)({},"".concat(T,"-icon-active"),ee)),children:(0,u.jsx)(mi,{})})}))]}))},Za=m(28682);function Yo(){return(0,u.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,u.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})})}var Xo=function(e){return(0,w.Z)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:e.colorTextCollapsedButton,backgroundColor:e.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:e.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}})};function Qo(s){var e=(0,O.useContext)(Ee),n=e.sider;return(0,U.Xj)("sider-menu-collapsed-icon",function(a){var d=(0,p.Z)((0,p.Z)({},a),{},{componentCls:".".concat(s)},n);return[Xo(d)]})}var Hi=["isMobile","collapsed"],Jo=function(e){var n,a=e.isMobile,d=e.collapsed,v=(0,fe.Z)(e,Hi),b=Qo(e.className),T=b.wrapSSR,A=b.hashId;return a&&d?null:T((0,u.jsx)("div",(0,p.Z)((0,p.Z)({},v),{},{className:Se()(e.className,A,(n={},(0,w.Z)(n,"".concat(e.className,"-collapsed"),d),(0,w.Z)(n,"".concat(e.className,"-is-mobile"),a),n)),children:(0,u.jsx)(Yo,{})})))},ni=m(16165),ho=["type","children"],go=new Set;function mo(s){return Boolean(typeof s=="string"&&s.length&&!go.has(s))}function yi(s){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=s[e];if(mo(n)){var a=document.createElement("script");a.setAttribute("src",n),a.setAttribute("data-namespace",n),s.length>e+1&&(a.onload=function(){yi(s,e+1)},a.onerror=function(){yi(s,e+1)}),go.add(n),document.body.appendChild(a)}}function po(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=s.scriptUrl,n=s.extraCommonProps,a=n===void 0?{}:n;e&&typeof document!="undefined"&&typeof window!="undefined"&&typeof document.createElement=="function"&&(Array.isArray(e)?yi(e.reverse()):yi([e]));var d=O.forwardRef(function(v,b){var T=v.type,A=v.children,D=(0,fe.Z)(v,ho),j=null;return v.type&&(j=O.createElement("use",{xlinkHref:"#".concat(T)})),A&&(j=A),O.createElement(ni.Z,(0,p.Z)((0,p.Z)((0,p.Z)({},a),D),{},{ref:b}),j)});return d.displayName="Iconfont",d}var _i=m(43574),yo=m(45520),Sn={navTheme:"light",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,iconfontUrl:"",colorPrimary:"#1677FF",splitMenus:!1},xo=m(57186);function xa(){var s=(0,O.useState)([]),e=(0,tt.Z)(s,2),n=e[0],a=e[1];return{flatMenuKeys:n,setFlatMenuKeys:a}}var ai=(0,xo.f)(xa),bo=function(e){var n,a,d,v;return(0,w.Z)({},"".concat(e.componentCls),(v={background:"transparent",border:"none"},(0,w.Z)(v,"& &-collapsed",(n={},(0,w.Z)(n,"".concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",height:"auto !important",marginBlock:"8px !important"}),(0,w.Z)(n,"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:e.colorBgMenuItemSelected,borderRadius:e.radiusBase}),(0,w.Z)(n,"".concat(e.componentCls,"-group"),(0,w.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0})),n)),(0,w.Z)(v,"".concat(e.componentCls,"-item-icon"),{height:"14px",width:"14px",opacity:"0.85",".anticon":{lineHeight:"14px",height:"14px"}}),(0,w.Z)(v,"& &-item-title",(0,w.Z)({display:"flex",flexDirection:"row",alignItems:"center","&-collapsed":(a={flexDirection:"column",justifyContent:"center"},(0,w.Z)(a,"".concat(e.componentCls,"-item-text"),{maxWidth:"100%"}),(0,w.Z)(a,"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"}),a)},"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,w.Z)({lineHeight:"16px",height:"48px"},"&".concat(e.componentCls,"-item-title-collapsed"),(d={display:"flex"},(0,w.Z)(d,"".concat(e.componentCls,"-item-icon"),{height:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px",height:"16px"}}),(0,w.Z)(d,"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4}),d)))),(0,w.Z)(v,"&-group",(0,w.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),(0,w.Z)(v,"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20}),v))};function xi(s){var e=(0,O.useContext)(Ee),n=e.sider;return(0,U.Xj)("pro-layout-base-menu",function(a){var d=(0,p.Z)((0,p.Z)({},a),{},{componentCls:".".concat(s)},n);return[bo(d)]})}var Yn=po({scriptUrl:Sn.iconfontUrl}),zi=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"icon-",a=arguments.length>2?arguments[2]:void 0;if(typeof e=="string"&&e!==""){if((0,U.CB)(e)||(0,U.ev)(e))return(0,u.jsx)("img",{width:16,src:e,alt:"icon",className:a},e);if(e.startsWith(n))return(0,u.jsx)(Yn,{type:e})}return e},bi=function(e){if(e&&typeof e=="string"){var n=e.substring(0,1).toUpperCase();return n}return null},Si=(0,st.Z)(function s(e){var n=this;(0,ge.Z)(this,s),this.props=void 0,this.getNavMenuItems=function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],d=arguments.length>1?arguments[1]:void 0;return a.map(function(v){return n.getSubMenuOrItem(v,d)}).filter(function(v){return v}).flat(1)},this.getSubMenuOrItem=function(a,d){var v=n.props,b=v.subMenuItemRender,T=v.baseClassName,A=v.prefixCls,D=v.collapsed,j=v.menu,I=v.iconPrefixes,W=v.layout,ee=(j==null?void 0:j.type)==="group"&&W!=="top",ne=n.props.token,ie=n.getIntlName(a),le=(a==null?void 0:a.children)||(a==null?void 0:a.routes);(0,yo.default)(!(a==null?void 0:a.routes),"routes \u5C06\u4F1A\u5E9F\u5F03\uFF0C\u4E3A\u4E86\u4FDD\u8BC1\u517C\u5BB9\u8BF7\u4F7F\u7528 children \u4F5C\u4E3A\u5B50\u8282\u70B9\u5B9A\u4E49\u65B9\u5F0F");var je=ee&&d===0?"group":void 0;if(Array.isArray(le)&&le.length>0){var Oe,Ye,ye,Le,Ve,at,Rt=d===0||ee&&d===1,Bt=zi(a.icon,I,"action ".concat(T,"-icon ").concat((Oe=n.props)===null||Oe===void 0?void 0:Oe.hashId)),Wt=D&&Rt?bi(ie):null,pr=(0,u.jsxs)("div",{title:ie,className:Se()("".concat(T,"-item-title"),(Ye=n.props)===null||Ye===void 0?void 0:Ye.hashId,(ye={},(0,w.Z)(ye,"".concat(T,"-item-title-collapsed"),D),(0,w.Z)(ye,"".concat(T,"-item-collapsed-show-title"),(j==null?void 0:j.collapsedShowTitle)&&D),ye)),children:[je==="group"&&D?null:Rt&&Bt?(0,u.jsx)("span",{className:"anticon ".concat(T,"-item-icon ").concat((Le=n.props)===null||Le===void 0?void 0:Le.hashId),children:Bt}):Wt,(0,u.jsx)("span",{className:Se()("".concat(T,"-item-text"),(Ve=n.props)===null||Ve===void 0?void 0:Ve.hashId,(0,w.Z)({},"".concat(T,"-item-text-has-icon"),je!=="group"&&Rt&&(Bt||Wt))),children:ie})]}),Er=b?b((0,p.Z)((0,p.Z)({},a),{},{isUrl:!1}),pr,n.props):pr,$t=n.getNavMenuItems(le,d+1);return ee&&d===0&&n.props.collapsed&&!j.collapsedShowGroupTitle?$t:[{type:je,key:a.key||a.path,title:a.tooltip||Er,label:Er,onClick:ee?void 0:a.onTitleClick,children:$t,className:je=="group"?"".concat(T,"-group"):"".concat(T,"-submenu")},ee&&d===0?{type:"divider",prefixCls:A,className:"".concat(T,"-divider"),key:(a.key||a.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:n.props.collapsed?"4px":"6px 16px",marginBlockStart:n.props.collapsed?4:8,borderColor:ne==null||(at=ne.sider)===null||at===void 0?void 0:at.colorMenuItemDivider}}:void 0].filter(Boolean)}return{className:"".concat(T,"-menu-item"),title:a.tooltip||ie,disabled:a.disabled,key:a.key||a.path,onClick:a.onTitleClick,label:n.getMenuItemPath(a,d)}},this.getIntlName=function(a){var d=a.name,v=a.locale,b=n.props,T=b.menu,A=b.formatMessage;return v&&(T==null?void 0:T.locale)!==!1?A==null?void 0:A({id:v,defaultMessage:d}):d},this.getMenuItemPath=function(a,d){var v,b,T,A,D,j=n.conversionPath(a.path||"/"),I=n.props,W=I.location,ee=W===void 0?{pathname:"/"}:W,ne=I.isMobile,ie=I.onCollapse,le=I.menuItemRender,je=I.iconPrefixes,Oe=n.getIntlName(a),Ye=n.props,ye=Ye.baseClassName,Le=Ye.menu,Ve=Ye.collapsed,at=(Le==null?void 0:Le.type)==="group",Rt=d===0||at&&d===1,Bt=Rt?zi(a.icon,je,"".concat(ye,"-icon ").concat((v=n.props)===null||v===void 0?void 0:v.hashId)):null,Wt=Ve&&Rt?bi(Oe):null,pr=(0,u.jsxs)("div",{className:Se()("".concat(ye,"-item-title"),(b=n.props)===null||b===void 0?void 0:b.hashId,(T={},(0,w.Z)(T,"".concat(ye,"-item-title-collapsed"),Ve),(0,w.Z)(T,"".concat(ye,"-item-collapsed-show-title"),(Le==null?void 0:Le.collapsedShowTitle)&&Ve),T)),children:[Bt?(0,u.jsx)("span",{className:"anticon ".concat(ye,"-item-icon ").concat((A=n.props)===null||A===void 0?void 0:A.hashId),children:Bt}):Wt,(0,u.jsx)("span",{className:Se()("".concat(ye,"-item-text"),(D=n.props)===null||D===void 0?void 0:D.hashId,(0,w.Z)({},"".concat(ye,"-item-text-has-icon"),Rt&&(Bt||Wt))),children:Oe})]},j),Er=(0,U.CB)(j);if(Er){var $t,Hr,Ge,Zr;pr=(0,u.jsxs)("span",{title:Oe,onClick:function(){var or,en;(or=window)===null||or===void 0||(en=or.open)===null||en===void 0||en.call(or,j,"_blank")},className:Se()("".concat(ye,"-item-title"),($t=n.props)===null||$t===void 0?void 0:$t.hashId,(Hr={},(0,w.Z)(Hr,"".concat(ye,"-item-title-collapsed"),Ve),(0,w.Z)(Hr,"".concat(ye,"-item-link"),!0),(0,w.Z)(Hr,"".concat(ye,"-item-collapsed-show-title"),(Le==null?void 0:Le.collapsedShowTitle)&&Ve),Hr)),children:[Bt?(0,u.jsx)("span",{className:"anticon ".concat(ye,"-item-icon ").concat((Ge=n.props)===null||Ge===void 0?void 0:Ge.hashId),children:Bt}):Wt,(0,u.jsx)("span",{className:Se()("".concat(ye,"-item-text"),(Zr=n.props)===null||Zr===void 0?void 0:Zr.hashId,(0,w.Z)({},"".concat(ye,"-item-text-has-icon"),Rt&&(Bt||Wt))),children:Oe})]},j)}if(le){var Mn=(0,p.Z)((0,p.Z)({},a),{},{isUrl:Er,itemPath:j,isMobile:ne,replace:j===ee.pathname,onClick:function(){return ie&&ie(!0)},children:void 0});return le(Mn,pr,n.props)}return pr},this.conversionPath=function(a){return a&&a.indexOf("http")===0?a:"/".concat(a||"").replace(/\/+/g,"/")},this.props=e}),qo=function(e,n){var a=n.layout,d=n.collapsed,v={};return e&&!d&&["side","mix"].includes(a||"mix")&&(v={openKeys:e}),v},So=function(e){var n,a=e.mode,d=e.className,v=e.handleOpenChange,b=e.style,T=e.menuData,A=e.prefixCls,D=e.menu,j=e.matchMenuKeys,I=e.iconfontUrl,W=e.selectedKeys,ee=e.onSelect,ne=e.menuRenderType,ie=e.openKeys,le=(0,O.useContext)(Ee),je="".concat(A,"-base-menu"),Oe=(0,O.useRef)([]),Ye=ai.useContainer(),ye=Ye.flatMenuKeys,Le=(0,U.i9)(D==null?void 0:D.defaultOpenAll),Ve=(0,tt.Z)(Le,2),at=Ve[0],Rt=Ve[1],Bt=(0,U.i9)(function(){return(D==null?void 0:D.defaultOpenAll)?co(T)||[]:ie===!1?!1:[]},{value:ie===!1?void 0:ie,onChange:v}),Wt=(0,tt.Z)(Bt,2),pr=Wt[0],Er=Wt[1],$t=(0,U.i9)([],{value:W,onChange:ee?function(ha){ee&&ha&&ee(ha)}:void 0}),Hr=(0,tt.Z)($t,2),Ge=Hr[0],Zr=Hr[1];(0,O.useEffect)(function(){(D==null?void 0:D.defaultOpenAll)||ie===!1||ye.length||j&&(Er(j),Zr(j))},[j.join("-")]),(0,O.useEffect)(function(){I&&(Yn=po({scriptUrl:I}))},[I]),(0,O.useEffect)(function(){if(j.join("-")!==(Ge||[]).join("-")&&Zr(j),!at&&ie!==!1&&j.join("-")!==(pr||[]).join("-")){var ha=j;(D==null?void 0:D.autoClose)===!1&&(ha=Array.from(new Set([].concat((0,Ht.Z)(j),(0,Ht.Z)(pr||[]))))),Er(ha)}else(D==null?void 0:D.ignoreFlatMenu)&&at?Er(co(T)):ye.length>0&&Rt(!1)},[j.join("-")]);var Mn=(0,O.useMemo)(function(){return qo(pr,e)},[pr&&pr.join(","),e.layout,e.collapsed]),Dn=xi(je),or=Dn.wrapSSR,en=Dn.hashId,zn=(0,O.useMemo)(function(){return new Si((0,p.Z)((0,p.Z)({},e),{},{token:le,menuRenderType:ne,baseClassName:je,hashId:en}))},[e,le,ne,je,en]);if(D==null?void 0:D.loading)return(0,u.jsx)("div",{style:(a==null?void 0:a.includes("inline"))?{padding:24}:{marginBlockStart:16},children:(0,u.jsx)(_i.Z,{active:!0,title:!1,paragraph:{rows:(a==null?void 0:a.includes("inline"))?6:1}})});e.openKeys===!1&&!e.handleOpenChange&&(Oe.current=j);var ta=e.postMenuData?e.postMenuData(T):T;return ta&&(ta==null?void 0:ta.length)<1?null:or((0,O.createElement)(Za.Z,(0,p.Z)((0,p.Z)({},Mn),{},{key:"Menu",mode:a,inlineIndent:16,defaultOpenKeys:Oe.current,theme:"light",selectedKeys:Ge,style:(0,p.Z)({backgroundColor:"transparent",border:"none"},b),className:Se()(d,en,je,(n={},(0,w.Z)(n,"".concat(je,"-horizontal"),a==="horizontal"),(0,w.Z)(n,"".concat(je,"-collapsed"),e.collapsed),n)),items:zn.getNavMenuItems(ta,0),onOpenChange:Er},e.menuProps)))},pl=Ha.Sider,Ui=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"menuHeaderRender",a=e.logo,d=e.title,v=e.layout,b=e[n||""];if(b===!1)return null;var T=pa(a),A=(0,u.jsx)("h1",{children:d!=null?d:"Ant Design Pro"});return b?b(T,e.collapsed?null:A,e):e.isMobile?null:v==="mix"&&n==="menuHeaderRender"?!1:e.collapsed?(0,u.jsx)("a",{children:T},"title"):(0,u.jsxs)("a",{children:[T,A]},"title")},Jr=function(e){var n,a,d=e.collapsed,v=e.originCollapsed,b=e.fixSiderbar,T=e.menuFooterRender,A=e.onCollapse,D=e.theme,j=e.siderWidth,I=e.isMobile,W=e.onMenuHeaderClick,ee=e.breakpoint,ne=ee===void 0?"lg":ee,ie=e.style,le=e.layout,je=e.menuExtraRender,Oe=je===void 0?!1:je,Ye=e.links,ye=e.menuContentRender,Le=e.collapsedButtonRender,Ve=e.prefixCls,at=e.avatarProps,Rt=e.rightContentRender,Bt=e.actionsRender,Wt=e.onOpenChange,pr=e.logoStyle,Er=(0,U.dQ)(),$t=Er.hashId,Hr=(0,O.useMemo)(function(){return!(I||le==="mix")},[I,le]),Ge="".concat(Ve,"-sider"),Zr=ai.useContainer(),Mn=Zr.flatMenuKeys,Dn=Se()("".concat(Ge),$t,(n={},(0,w.Z)(n,"".concat(Ge,"-fixed"),b),(0,w.Z)(n,"".concat(Ge,"-collapsed"),e.collapsed),(0,w.Z)(n,"".concat(Ge,"-layout-").concat(le),le&&!I),(0,w.Z)(n,"".concat(Ge,"-light"),D!=="dark"),(0,w.Z)(n,"".concat(Ge,"-mix"),le==="mix"&&!I),n)),or=Ui(e),en=Oe&&Oe(e),zn=(0,O.useMemo)(function(){return ye!==!1&&Mn&&(0,O.createElement)(So,(0,p.Z)((0,p.Z)({},e),{},{key:"base-menu",mode:"inline",handleOpenChange:Wt,style:{width:"100%"},className:"".concat(Ge,"-menu ").concat($t)}))},[Ge,Mn,$t,ye,Wt,e]),ta=(Ye||[]).map(function(Gt,yr){return{className:"".concat(Ge,"-link"),label:Gt,key:yr}}),ha=(0,O.useMemo)(function(){return ye?ye(e,zn):zn},[ye,zn,e]),Ra=(0,O.useMemo)(function(){return at&&(0,u.jsxs)(_r.Z,{align:"center",className:"".concat(Ge,"-actions-avatar"),children:[(0,u.jsx)(Yr.C,(0,p.Z)({size:28},at)),at.title&&!d&&(0,u.jsx)("span",{children:at.title})]})},[at,Ge,d]),Na=(0,O.useMemo)(function(){return Bt?(0,u.jsx)(_r.Z,{align:"center",size:4,direction:d?"vertical":"horizontal",className:Se()(["".concat(Ge,"-actions-list"),d&&"".concat(Ge,"-actions-list-collapsed"),$t]),children:Bt==null?void 0:Bt(e).map(function(Gt,yr){return(0,u.jsx)("div",{className:"".concat(Ge,"-actions-list-item ").concat($t),children:Gt},yr)})}):null},[Bt,Ge,d]),Di=(0,O.useMemo)(function(){return(0,u.jsx)(ya,{appList:e.appList,prefixCls:e.prefixCls})},[e.appList,e.prefixCls]),mn=(0,O.useMemo)(function(){if(Le===!1)return null;var Gt=(0,u.jsx)(Jo,{isMobile:I,collapsed:v,className:"".concat(Ge,"-collapsed-button"),onClick:function(){A==null||A(!v)}});return Le?Le(d,Gt):Gt},[Le,I,v,Ge,d,A]),lr=(0,O.useMemo)(function(){return!Ra&&!Na?null:(0,u.jsxs)("div",{className:Se()("".concat(Ge,"-actions"),$t,d&&"".concat(Ge,"-actions-collapsed")),children:[Ra,Na]})},[Na,Ra,Ge,d,$t]),sr=60,Un=(0,O.useMemo)(function(){var Gt;return(e==null||(Gt=e.menu)===null||Gt===void 0?void 0:Gt.hideMenuWhenCollapsed)&&d?"".concat(Ge,"-hide-menu-collapsed"):null},[Ge,d,e==null||(a=e.menu)===null||a===void 0?void 0:a.hideMenuWhenCollapsed]),Ya=T&&(T==null?void 0:T(e)),Vr=(0,u.jsxs)(u.Fragment,{children:[or&&(0,u.jsxs)("div",{className:Se()([Se()("".concat(Ge,"-logo"),$t,(0,w.Z)({},"".concat(Ge,"-logo-collapsed"),d))]),onClick:Hr?W:void 0,id:"logo",style:pr,children:[or,Di]}),en&&(0,u.jsx)("div",{className:Se()(["".concat(Ge,"-extra"),!or&&"".concat(Ge,"-extra-no-logo"),$t]),children:en}),(0,u.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:ha}),Ye?(0,u.jsx)("div",{className:"".concat(Ge,"-links ").concat($t),children:(0,u.jsx)(Za.Z,{inlineIndent:16,className:"".concat(Ge,"-link-menu ").concat($t),selectedKeys:[],openKeys:[],theme:"light",mode:"inline",items:ta})}):null,Hr&&(0,u.jsxs)(u.Fragment,{children:[lr,Rt?(0,u.jsx)("div",{className:Se()("".concat(Ge,"-actions"),$t,(0,w.Z)({},"".concat(Ge,"-actions-collapsed"),d)),children:Rt==null?void 0:Rt(e)}):null]}),Ya&&(0,u.jsx)("div",{className:Se()(["".concat(Ge,"-footer"),$t,(0,w.Z)({},"".concat(Ge,"-footer-collapsed"),d)]),children:Ya})]}),ji=(0,O.useContext)(Ee),vr=ji.sider;return(0,u.jsxs)(u.Fragment,{children:[b&&!I&&!Un&&(0,u.jsx)("div",{style:(0,p.Z)({width:d?sr:j,overflow:"hidden",flex:"0 0 ".concat(d?sr:j,"px"),maxWidth:d?sr:j,minWidth:d?sr:j,transition:"all 0.2s ease 0s"},ie)}),(0,u.jsxs)(pl,{collapsible:!0,trigger:null,collapsed:d,breakpoint:ne===!1?void 0:ne,onCollapse:function(yr){I||A==null||A(yr)},collapsedWidth:sr,style:ie,theme:"light",width:j,className:Se()(Dn,$t,Un),children:[(0,u.jsx)(We.ZP,{theme:{hashed:!1,override:{Menu:{radiusItem:4,colorItemBgSelected:vr.colorBgMenuItemSelected||"rgba(0, 0, 0, 0.04)",colorItemBgActive:vr.colorBgMenuItemHover||"rgba(0, 0, 0, 0.04)",colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:vr.colorTextMenu||"rgba(0, 0, 0, 0.65)",colorItemTextHover:vr.colorTextMenuActive||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:vr.colorTextMenuSelected||"rgba(0, 0, 0, 1)",colorItemBg:"transparent",colorSubItemBg:"transparent"}}},children:Un?(0,u.jsx)("div",{className:"".concat(Ge,"-hide-when-collapsed ").concat($t),style:{height:"100%",width:"100%",opacity:Un?0:1},children:Vr}):Vr}),mn]})]})},nn=m(7353),la=m(92137),ba=function(e){return(0,w.Z)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:e.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.radiusBase,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.radiusBase,"&:hover":{backgroundColor:e.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:"16px",paddingInlineEnd:"16px",color:(0,U.uK)(e.colorTextBase,.65),"> div":{height:"44px",color:(0,U.uK)(e.colorTextBase,.65),paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.radiusBase,"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.03)"}}}}})};function el(s){var e=(0,O.useContext)(Ee),n=e.header;return(0,U.Xj)("RightContent",function(a){var d=(0,p.Z)((0,p.Z)({},a),{},{componentCls:".".concat(s)},n);return[ba(d)]})}var hn=["rightContentRender","avatarProps","actionsRender","headerContentRender"],tl=["title"],Co=function(e){var n=e.rightContentRender,a=e.avatarProps,d=e.actionsRender,v=e.headerContentRender,b=(0,fe.Z)(e,hn),T=(0,O.useContext)(We.ZP.ConfigContext),A=T.getPrefixCls,D="".concat(A(),"-pro-global-header"),j=el(D),I=j.wrapSSR,W=j.hashId,ee=(0,O.useState)("auto"),ne=(0,tt.Z)(ee,2),ie=ne[0],le=ne[1],je=(0,O.useMemo)(function(){if(!a)return null;var ye=a.title,Le=(0,fe.Z)(a,tl);return[(0,O.createElement)(Yr.C,(0,p.Z)((0,p.Z)({},Le),{},{size:28,key:"avatar"})),ye?(0,u.jsx)("span",{style:{marginInlineStart:8},children:ye},"name"):void 0]},[a]),Oe=function(Le){var Ve=d&&(d==null?void 0:d(Le));return!Ve&&!je?null:(Array.isArray(Ve)||(Ve=[Ve]),I((0,u.jsxs)("div",{className:"".concat(D,"-header-actions ").concat(W),children:[Ve.filter(Boolean).map(function(at,Rt){var Bt=!1;if(O.isValidElement(at)){var Wt;Bt=!!(at==null||(Wt=at.props)===null||Wt===void 0?void 0:Wt["aria-hidden"])}return(0,u.jsx)("div",{className:Se()("".concat(D,"-header-actions-item ").concat(W),(0,w.Z)({},"".concat(D,"-header-actions-hover"),!Bt)),children:at},Rt)}),je&&(0,u.jsx)("span",{className:"".concat(D,"-header-actions-avatar ").concat(W),children:(0,u.jsx)("div",{children:je})})]})))},Ye=(0,U.DI)(function(){var ye=(0,la.Z)((0,nn.Z)().mark(function Le(Ve){return(0,nn.Z)().wrap(function(Rt){for(;;)switch(Rt.prev=Rt.next){case 0:le(Ve);case 1:case"end":return Rt.stop()}},Le)}));return function(Le){return ye.apply(this,arguments)}}(),160);return(0,u.jsx)("div",{className:"".concat(D,"-right-content ").concat(W),style:{minWidth:ie,height:"100%"},children:(0,u.jsx)("div",{style:{height:"100%"},children:(0,u.jsx)(mt.Z,{onResize:function(Le){var Ve=Le.width;Ye.run(Ve)},children:(n||Oe)&&(0,u.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:(n||Oe)((0,p.Z)((0,p.Z)({},b),{},{rightContentSize:ie}))})})})})},Wi=function(e){return(0,w.Z)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,w.Z)({display:"flex",alignItems:"center",minWidth:"192px"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-logo":{position:"relative",minWidth:"165px",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:e==null?void 0:e.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(e.heightLayoutHeader-12,"px")}})};function wo(s){var e=(0,O.useContext)(Ee),n=e.header;return(0,U.Xj)("top-nav-header",function(a){var d=(0,p.Z)((0,p.Z)({},a),{},{componentCls:".".concat(s)},n);return[Wi(d)]})}var sa=function(e){var n=(0,O.useRef)(null),a=e.onMenuHeaderClick,d=e.contentWidth,v=e.rightContentRender,b=e.className,T=e.style,A=e.headerContentRender,D=e.layout,j=e.actionsRender,I=(0,O.useContext)(We.ZP.ConfigContext),W=I.getPrefixCls,ee=(0,O.useContext)(Ee),ne=ee.header,ie="".concat(e.prefixCls||W("pro"),"-top-nav-header"),le=wo(ie),je=le.wrapSSR,Oe=le.hashId,Ye=Ui((0,p.Z)((0,p.Z)({},e),{},{collapsed:!1}),D==="mix"?"headerTitleRender":void 0),ye=(0,O.useMemo)(function(){var Le,Ve,at=(0,u.jsx)(We.ZP,{theme:{hashed:((Le="production")===null||Le===void 0?void 0:Le.toLowerCase())!=="test",override:{Menu:{colorItemBg:ne.colorBgHeader||"transparent",colorSubItemBg:ne.colorBgHeader||"transparent",radiusItem:4,colorItemBgSelected:ne.colorBgMenuItemSelected||"rgba(0, 0, 0, 0.04)",colorItemBgActive:ne.colorBgMenuItemHover||"rgba(0, 0, 0, 0.04)",colorItemBgSelectedHorizontal:ne.colorBgMenuItemSelected||"rgba(0, 0, 0, 0.04)",colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:ne.colorTextMenu||"rgba(0, 0, 0, 0.65)",colorItemTextHover:ne.colorTextMenuActive||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:ne.colorTextMenuSelected||"rgba(0, 0, 0, 1)"}}},children:(0,u.jsx)(So,(0,p.Z)((0,p.Z)((0,p.Z)({theme:"light"},e),{},{className:"".concat(ie,"-base-menu ").concat(Oe)},e.menuProps),{},{style:(0,p.Z)({width:"100%"},(Ve=e.menuProps)===null||Ve===void 0?void 0:Ve.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return A?A(e,at):at},[Oe,ne.colorBgHeader,ne.colorBgMenuItemHover,ne.colorBgMenuItemSelected,ne.colorTextMenu,ne.colorTextMenuActive,ne.colorTextMenuSelected,A,ie,e]);return je((0,u.jsx)("div",{className:Se()(ie,Oe,b,(0,w.Z)({},"".concat(ie,"-light"),!0)),style:T,children:(0,u.jsxs)("div",{ref:n,className:Se()("".concat(ie,"-main"),Oe,(0,w.Z)({},"".concat(ie,"-wide"),d==="Fixed")),children:[Ye&&(0,u.jsxs)("div",{className:Se()("".concat(ie,"-main-left ").concat(Oe)),onClick:a,children:[(0,u.jsx)(ya,(0,p.Z)({},e)),(0,u.jsx)("div",{className:"".concat(ie,"-logo ").concat(Oe),id:"logo",children:Ye},"logo")]}),(0,u.jsx)("div",{style:{flex:1},className:"".concat(ie,"-menu ").concat(Oe),children:ye}),(v||j||e.avatarProps)&&(0,u.jsx)(Co,(0,p.Z)((0,p.Z)({rightContentRender:v},e),{},{prefixCls:ie}))]})}))},rl=function(e){var n;return(0,w.Z)({},e.componentCls,(n={position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:e.heightLayoutHeader,boxSizing:"border-box","> a":{height:"100%"}},(0,w.Z)(n,"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),(0,w.Z)(n,"&-collapsed-button",{minHeight:"22px",color:e.colorHeaderTitle,fontSize:"22px",marginInlineStart:"16px"}),(0,w.Z)(n,"&-logo",{position:"relative",minWidth:"154px",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),(0,w.Z)(n,"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}),n))};function ii(s){var e=(0,O.useContext)(Ee),n=e.header;return(0,U.Xj)("pro-layout-global-header",function(a){var d=(0,p.Z)((0,p.Z)({},a),{},{componentCls:".".concat(s),heightLayoutHeader:n.heightLayoutHeader,colorHeaderTitle:n.colorHeaderTitle});return[rl(d)]})}var Vi=function(e,n){return e===!1?null:e?e(n,null):n},Ki=function(e){var n,a=e.isMobile,d=e.logo,v=e.collapsed,b=e.onCollapse,T=e.rightContentRender,A=e.menuHeaderRender,D=e.onMenuHeaderClick,j=e.className,I=e.style,W=e.layout,ee=e.children,ne=e.splitMenus,ie=e.menuData,le=e.prefixCls,je=(0,O.useContext)(We.ZP.ConfigContext),Oe=je.direction,Ye="".concat(le,"-global-header"),ye=ii(Ye),Le=ye.wrapSSR,Ve=ye.hashId,at=Se()(j,Ye,Ve);if(W==="mix"&&!a&&ne){var Rt=(ie||[]).map(function(Er){return(0,p.Z)((0,p.Z)({},Er),{},{children:void 0,routes:void 0})}),Bt=ma(Rt);return(0,u.jsx)(sa,(0,p.Z)((0,p.Z)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:Bt}))}var Wt=Se()("".concat(Ye,"-logo"),Ve,(n={},(0,w.Z)(n,"".concat(Ye,"-logo-rtl"),Oe==="rtl"),(0,w.Z)(n,"".concat(Ye,"-logo-mix"),W==="mix"),(0,w.Z)(n,"".concat(Ye,"-logo-mobile"),a),n)),pr=(0,u.jsx)("span",{className:Wt,children:(0,u.jsx)("a",{children:pa(d)})},"logo");return Le((0,u.jsxs)("div",{className:at,style:(0,p.Z)({},I),children:[a&&Vi(A,pr),a&&(0,u.jsx)("span",{className:"".concat(Ye,"-collapsed-button ").concat(Ve),onClick:function(){b==null||b(!v)},children:v?(0,u.jsx)(gl,{}):(0,u.jsx)(Vo.Z,{})}),W==="mix"&&!a&&(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(ya,(0,p.Z)({},e)),(0,u.jsx)("div",{className:Wt,onClick:D,children:Ui((0,p.Z)((0,p.Z)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,u.jsx)("div",{style:{flex:1},children:ee}),(T||e.actionsRender||e.avatarProps)&&(0,u.jsx)(Co,(0,p.Z)({rightContentRender:T},e))]}))},$i=Ha.Header,Ci=function(e){var n,a,d=e.isMobile,v=e.fixedHeader,b=e.className,T=e.style,A=e.collapsed,D=e.prefixCls,j=e.onCollapse,I=e.layout,W=e.headerRender,ee=e.headerContentRender,ne=(0,O.useContext)(Ee),ie=ne.header,le=(0,O.useCallback)(function(){var Rt=I==="top",Bt=ma(e.menuData||[]),Wt=(0,u.jsx)(Ki,(0,p.Z)((0,p.Z)({onCollapse:j},e),{},{menuData:Bt,children:ee&&ee(e,null)}));return Rt&&!d&&(Wt=(0,u.jsx)(sa,(0,p.Z)((0,p.Z)({mode:"horizontal",onCollapse:j},e),{},{menuData:Bt}))),W&&typeof W=="function"?W(e,Wt):Wt},[ee,W,d,I,j,e]),je=v||I==="mix",Oe=I==="top",Ye="".concat(D,"-layout-header"),ye=Gn(Ye,{proLayoutCls:D||"ant-pro-layout"}),Le=ye.wrapSSR,Ve=ye.hashId,at=Se()(b,Ve,Ye,(n={},(0,w.Z)(n,"".concat(Ye,"-fixed-header"),je),(0,w.Z)(n,"".concat(Ye,"-mix"),I==="mix"),(0,w.Z)(n,"".concat(Ye,"-fixed-header-action"),!A),(0,w.Z)(n,"".concat(Ye,"-top-menu"),Oe),(0,w.Z)(n,"".concat(Ye,"-header"),!0),n));return I==="side"&&!d?null:Le((0,u.jsx)(u.Fragment,{children:(0,u.jsxs)(We.ZP,{theme:{hashed:((a="production")===null||a===void 0?void 0:a.toLowerCase())!=="test",override:{Layout:{colorBgHeader:"transparent",colorBgBody:"transparent"}}},children:[je&&(0,u.jsx)($i,{style:(0,p.Z)({height:ie.heightLayoutHeader,lineHeight:"".concat(ie.heightLayoutHeader,"px"),backgroundColor:"transparent",zIndex:19},T)}),(0,u.jsx)($i,{className:at,style:T,children:le()})]})}))},ca=m(28508),To=m(24616),Xn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112c-3.8 0-7.7.7-11.6 2.3L292 345.9H128c-8.8 0-16 7.4-16 16.6v299c0 9.2 7.2 16.6 16 16.6h101.7c-3.7 11.6-5.7 23.9-5.7 36.4 0 65.9 53.8 119.5 120 119.5 55.4 0 102.1-37.6 115.9-88.4l408.6 164.2c3.9 1.5 7.8 2.3 11.6 2.3 16.9 0 32-14.2 32-33.2V145.2C912 126.2 897 112 880 112zM344 762.3c-26.5 0-48-21.4-48-47.8 0-11.2 3.9-21.9 11-30.4l84.9 34.1c-2 24.6-22.7 44.1-47.9 44.1zm496 58.4L318.8 611.3l-12.9-5.2H184V417.9h121.9l12.9-5.2L840 203.3v617.4z"}}]},name:"notification",theme:"outlined"},Aa=Xn,ua=function(e,n){return O.createElement(aa.Z,(0,p.Z)((0,p.Z)({},e),{},{ref:n,icon:Aa}))};ua.displayName="NotificationOutlined";var za=O.forwardRef(ua),wi=m(85175),Oa=m(17474),Ti=m(45095),La=m(273),Sa=m(27049),Ua=m(38272),Ca=m(12028),Wa=m(76772),Eo=m(48086),Gi=m(60869),oi={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blue (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.sidermenutype":"SideMenu Type","app.setting.sidermenutype-sub":"Classic","app.setting.sidermenutype-group":"Grouping","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"},Ei=(0,p.Z)({},oi),Mo={"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Blu cielo mattutino","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xE0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xE8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"},Yi=(0,p.Z)({},Mo),nr={"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."},qr=(0,p.Z)({},nr),nl={"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98CE\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8BA4","app.setting.themecolor.daybreak":"\u62C2\u6653","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.sidermenutype":"\u4FA7\u8FB9\u83DC\u5355\u7C7B\u578B","app.setting.sidermenutype-sub":"\u7ECF\u5178\u6A21\u5F0F","app.setting.sidermenutype-group":"\u5206\u7EC4\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},Rn=(0,p.Z)({},nl),Xi={"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98A8\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},Mi=(0,p.Z)({},Xi),Qi={"zh-CN":Rn,"zh-TW":Mi,"en-US":Ei,"it-IT":Yi,"ko-KR":qr},Da=function(){if(!(0,U.jU)())return"zh-CN";var e=window.localStorage.getItem("umi_locale");return e||window.g_locale||navigator.language},Qn=function(){var e=Da();return Qi[e]||Qi["zh-CN"]},Jn=m(88284),li=m(94199),Ji=function(e){var n=e.value,a=e.configType,d=e.onChange,v=e.list,b=e.prefixCls,T=e.hashId,A="".concat(b,"-block-checkbox"),D=(0,O.useMemo)(function(){var j=(v||[]).map(function(I){return(0,u.jsx)(li.Z,{title:I.title,children:(0,u.jsxs)("div",{className:Se()(T,"".concat(A,"-item"),"".concat(A,"-item-").concat(I.key),"".concat(A,"-").concat(a,"-item")),onClick:function(){return d(I.key)},children:[(0,u.jsx)(Jn.Z,{className:"".concat(A,"-selectIcon ").concat(T),style:{display:n===I.key?"block":"none"}}),(I==null?void 0:I.icon)?(0,u.jsx)("div",{className:"".concat(A,"-icon ").concat(T),children:I.icon}):null]})},I.key)});return j},[n,v==null?void 0:v.length,d]);return(0,u.jsx)("div",{className:Se()(A,T),children:D})};function Po(){return(0,u.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",width:"1em",height:"1em",viewBox:"0 0 104 104",children:[(0,u.jsxs)("defs",{children:[(0,u.jsx)("rect",{id:"path-1",width:"90",height:"72",x:"0",y:"0",rx:"10"}),(0,u.jsxs)("filter",{id:"filter-2",width:"152.2%",height:"165.3%",x:"-26.1%",y:"-27.1%",filterUnits:"objectBoundingBox",children:[(0,u.jsx)("feMorphology",{in:"SourceAlpha",radius:"0.25",result:"shadowSpreadOuter1"}),(0,u.jsx)("feOffset",{dy:"1",in:"shadowSpreadOuter1",result:"shadowOffsetOuter1"}),(0,u.jsx)("feGaussianBlur",{in:"shadowOffsetOuter1",result:"shadowBlurOuter1",stdDeviation:"1"}),(0,u.jsx)("feColorMatrix",{in:"shadowBlurOuter1",result:"shadowMatrixOuter1",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"}),(0,u.jsx)("feMorphology",{in:"SourceAlpha",radius:"1",result:"shadowSpreadOuter2"}),(0,u.jsx)("feOffset",{dy:"2",in:"shadowSpreadOuter2",result:"shadowOffsetOuter2"}),(0,u.jsx)("feGaussianBlur",{in:"shadowOffsetOuter2",result:"shadowBlurOuter2",stdDeviation:"4"}),(0,u.jsx)("feColorMatrix",{in:"shadowBlurOuter2",result:"shadowMatrixOuter2",values:"0 0 0 0 0.098466735 0 0 0 0 0.0599695403 0 0 0 0 0.0599695403 0 0 0 0.07 0"}),(0,u.jsx)("feMorphology",{in:"SourceAlpha",radius:"2",result:"shadowSpreadOuter3"}),(0,u.jsx)("feOffset",{dy:"4",in:"shadowSpreadOuter3",result:"shadowOffsetOuter3"}),(0,u.jsx)("feGaussianBlur",{in:"shadowOffsetOuter3",result:"shadowBlurOuter3",stdDeviation:"8"}),(0,u.jsx)("feColorMatrix",{in:"shadowBlurOuter3",result:"shadowMatrixOuter3",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"}),(0,u.jsxs)("feMerge",{children:[(0,u.jsx)("feMergeNode",{in:"shadowMatrixOuter1"}),(0,u.jsx)("feMergeNode",{in:"shadowMatrixOuter2"}),(0,u.jsx)("feMergeNode",{in:"shadowMatrixOuter3"})]})]})]}),(0,u.jsxs)("g",{fill:"none",fillRule:"evenodd",stroke:"none",strokeWidth:"1",children:[(0,u.jsxs)("g",{children:[(0,u.jsx)("use",{fill:"#000",filter:"url(#filter-2)",xlinkHref:"#path-1"}),(0,u.jsx)("use",{fill:"#F0F2F5",xlinkHref:"#path-1"})]}),(0,u.jsx)("path",{fill:"#FFF",d:"M25 15h65v47c0 5.523-4.477 10-10 10H25V15z"}),(0,u.jsx)("path",{stroke:"#E6EAF0",strokeLinecap:"square",d:"M0.5 15.5L90.5 15.5"}),(0,u.jsx)("rect",{width:"14",height:"3",x:"4",y:"26",fill:"#D7DDE6",rx:"1.5"}),(0,u.jsx)("rect",{width:"9",height:"3",x:"4",y:"32",fill:"#D7DDE6",rx:"1.5"}),(0,u.jsx)("rect",{width:"9",height:"3",x:"4",y:"42",fill:"#E6EAF0",rx:"1.5"}),(0,u.jsx)("rect",{width:"9",height:"3",x:"4",y:"21",fill:"#E6EAF0",rx:"1.5"}),(0,u.jsx)("rect",{width:"9",height:"3",x:"4",y:"53",fill:"#D7DDE6",rx:"1.5"}),(0,u.jsx)("rect",{width:"14",height:"3",x:"4",y:"47",fill:"#D7DDE6",rx:"1.5"}),(0,u.jsx)("path",{stroke:"#E6EAF0",strokeLinecap:"square",d:"M25.5 15.5L25.5 72.5"})]})]})}function qi(){return(0,u.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",width:"1em",height:"1em",viewBox:"0 0 104 104",children:[(0,u.jsxs)("defs",{children:[(0,u.jsx)("rect",{id:"path-1",width:"90",height:"72",x:"0",y:"0",rx:"10"}),(0,u.jsxs)("filter",{id:"filter-2",width:"152.2%",height:"165.3%",x:"-26.1%",y:"-27.1%",filterUnits:"objectBoundingBox",children:[(0,u.jsx)("feMorphology",{in:"SourceAlpha",radius:"0.25",result:"shadowSpreadOuter1"}),(0,u.jsx)("feOffset",{dy:"1",in:"shadowSpreadOuter1",result:"shadowOffsetOuter1"}),(0,u.jsx)("feGaussianBlur",{in:"shadowOffsetOuter1",result:"shadowBlurOuter1",stdDeviation:"1"}),(0,u.jsx)("feColorMatrix",{in:"shadowBlurOuter1",result:"shadowMatrixOuter1",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"}),(0,u.jsx)("feMorphology",{in:"SourceAlpha",radius:"1",result:"shadowSpreadOuter2"}),(0,u.jsx)("feOffset",{dy:"2",in:"shadowSpreadOuter2",result:"shadowOffsetOuter2"}),(0,u.jsx)("feGaussianBlur",{in:"shadowOffsetOuter2",result:"shadowBlurOuter2",stdDeviation:"4"}),(0,u.jsx)("feColorMatrix",{in:"shadowBlurOuter2",result:"shadowMatrixOuter2",values:"0 0 0 0 0.098466735 0 0 0 0 0.0599695403 0 0 0 0 0.0599695403 0 0 0 0.07 0"}),(0,u.jsx)("feMorphology",{in:"SourceAlpha",radius:"2",result:"shadowSpreadOuter3"}),(0,u.jsx)("feOffset",{dy:"4",in:"shadowSpreadOuter3",result:"shadowOffsetOuter3"}),(0,u.jsx)("feGaussianBlur",{in:"shadowOffsetOuter3",result:"shadowBlurOuter3",stdDeviation:"8"}),(0,u.jsx)("feColorMatrix",{in:"shadowBlurOuter3",result:"shadowMatrixOuter3",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"}),(0,u.jsxs)("feMerge",{children:[(0,u.jsx)("feMergeNode",{in:"shadowMatrixOuter1"}),(0,u.jsx)("feMergeNode",{in:"shadowMatrixOuter2"}),(0,u.jsx)("feMergeNode",{in:"shadowMatrixOuter3"})]})]})]}),(0,u.jsxs)("g",{fill:"none",fillRule:"evenodd",stroke:"none",strokeWidth:"1",children:[(0,u.jsxs)("g",{children:[(0,u.jsx)("use",{fill:"#000",filter:"url(#filter-2)",xlinkHref:"#path-1"}),(0,u.jsx)("use",{fill:"#F0F2F5",xlinkHref:"#path-1"})]}),(0,u.jsx)("path",{fill:"#FFF",d:"M26 0h55c5.523 0 10 4.477 10 10v8H26V0z"}),(0,u.jsx)("path",{fill:"#001529",d:"M10 0h19v72H10C4.477 72 0 67.523 0 62V10C0 4.477 4.477 0 10 0z"}),(0,u.jsx)("rect",{width:"14",height:"3",x:"5",y:"18",fill:"#D7DDE6",opacity:"0.2",rx:"1.5"}),(0,u.jsx)("rect",{width:"14",height:"3",x:"5",y:"42",fill:"#D7DDE6",opacity:"0.2",rx:"1.5"}),(0,u.jsx)("rect",{width:"9",height:"3",x:"9",y:"24",fill:"#D7DDE6",opacity:"0.2",rx:"1.5"}),(0,u.jsx)("rect",{width:"9",height:"3",x:"9",y:"48",fill:"#D7DDE6",opacity:"0.2",rx:"1.5"}),(0,u.jsx)("rect",{width:"9",height:"3",x:"9",y:"36",fill:"#D7DDE6",opacity:"0.2",rx:"1.5"}),(0,u.jsx)("rect",{width:"14",height:"3",x:"9",y:"30",fill:"#D7DDE6",opacity:"0.2",rx:"1.5"}),(0,u.jsx)("rect",{width:"14",height:"3",x:"9",y:"54",fill:"#D7DDE6",opacity:"0.2",rx:"1.5"})]})]})}var eo=m(34041),Pi=function(e){var n=O.cloneElement(e.action,{disabled:e.disabled});return(0,u.jsx)(li.Z,{title:e.disabled?e.disabledReason:"",placement:"left",children:(0,u.jsx)(Ua.ZP.Item,{actions:[n],children:(0,u.jsx)("span",{style:{opacity:e.disabled?.5:1},children:e.title})})})},Ro=function(e){var n=e.settings,a=n===void 0?{}:n,d=e.changeSetting,v=e.hashId,b=Zi(),T=a||Sn,A=T.contentWidth,D=T.splitMenus,j=T.fixedHeader,I=T.layout,W=T.fixSiderbar;return(0,u.jsx)(Ua.ZP,{split:!1,dataSource:[{title:b({id:"app.setting.content-width",defaultMessage:"Content Width"}),action:(0,u.jsxs)(eo.Z,{value:A||"Fixed",size:"small",className:"content-width ".concat(v),onSelect:function(ne){d("contentWidth",ne)},style:{width:80},children:[I==="side"?null:(0,u.jsx)(eo.Z.Option,{value:"Fixed",children:b({id:"app.setting.content-width.fixed",defaultMessage:"Fixed"})}),(0,u.jsx)(eo.Z.Option,{value:"Fluid",children:b({id:"app.setting.content-width.fluid",defaultMessage:"Fluid"})})]})},{title:b({id:"app.setting.fixedheader",defaultMessage:"Fixed Header"}),action:(0,u.jsx)(Ca.Z,{size:"small",className:"fixed-header",checked:!!j,onChange:function(ne){d("fixedHeader",ne)}})},{title:b({id:"app.setting.fixedsidebar",defaultMessage:"Fixed Sidebar"}),disabled:I==="top",disabledReason:b({id:"app.setting.fixedsidebar.hint",defaultMessage:"Works on Side Menu Layout"}),action:(0,u.jsx)(Ca.Z,{size:"small",className:"fix-siderbar",checked:!!W,onChange:function(ne){return d("fixSiderbar",ne)}})},{title:b({id:"app.setting.splitMenus"}),disabled:I!=="mix",action:(0,u.jsx)(Ca.Z,{size:"small",checked:!!D,className:"split-menus",onChange:function(ne){d("splitMenus",ne)}})}],renderItem:Pi})},al=function(e){var n=e.settings,a=n===void 0?{}:n,d=e.changeSetting,v=e.hashId,b=Zi(),T=["header","footer","menu","menuHeader"];return(0,u.jsx)(Ua.ZP,{split:!1,renderItem:Pi,dataSource:T.map(function(A){return{title:b({id:"app.setting.regionalsettings.".concat(A)}),action:(0,u.jsx)(Ca.Z,{size:"small",className:"regional-".concat(A," ").concat(v),checked:a["".concat(A,"Render")]||a["".concat(A,"Render")]===void 0,onChange:function(j){return d("".concat(A,"Render"),j===!0?void 0:!1)}})}})})},Ri=function(e){var n;return n={},(0,w.Z)(n,"".concat(e.componentCls,"-handle"),{position:"fixed",insetBlockStart:"240px",insetInlineEnd:"0px",zIndex:0,display:"flex",alignItems:"center",justifyContent:"center",width:"48px",height:"48px",fontSize:"16px",textAlign:"center",backgroundColor:e.colorPrimary,borderEndStartRadius:e.radiusLG,borderStartStartRadius:e.radiusLG,"-webkit-backdropilter":"saturate(180%) blur(20px)",backdropFilter:"saturate(180%) blur(20px)",cursor:"pointer",pointerEvents:"auto"}),(0,w.Z)(n,e.componentCls,{"&-content":{position:"relative",minHeight:"100%",color:e.colorText},"&-body-title":{marginBlockEnd:"12px",fontSize:"14px",lineHeight:"22px",color:e.colorTextHeading},"&-block-checkbox":{display:"flex",minHeight:42,"&-item":{position:"relative",width:"44px",height:"36px",marginInlineEnd:"16px",overflow:"hidden",borderRadius:"4px",boxShadow:"0 1px 2.5px 0 rgba(0, 0, 0, 0.18)",cursor:"pointer",fontSize:56,lineHeight:"56px","&::before":{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"33%",height:"100%",content:"''"},"&::after":{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"100%",height:"25%",content:"''"},"&-realDark":{backgroundColor:"rgba(0, 21, 41, 0.85)","&::before":{backgroundColor:e.colorTextSecondary},"&::after":{backgroundColor:e.colorText}},"&-light":{backgroundColor:e.colorBgContainer,"&::before":{backgroundColor:e.colorBgContainer},"&::after":{backgroundColor:e.colorBgContainer}},"&-dark,&-side":{backgroundColor:"#f7f8fa","&::before":{zIndex:"1",backgroundColor:"#001529"},"&::after":{backgroundColor:e.colorBgContainer}},"&-top":{backgroundColor:"#f7f8fa","&::before":{backgroundColor:"transparent"},"&::after":{backgroundColor:"#001529"}},"&-mix":{backgroundColor:"#f7f8fa","&::before":{backgroundColor:e.colorBgContainer},"&::after":{backgroundColor:"#001529"}}},"& &-selectIcon":{position:"absolute",insetInlineEnd:"6px",bottom:"4px",color:e.colorPrimary,fontWeight:"bold",fontSize:"14px",pointerEvents:"none",".action":{color:e.colorPrimary}}},"&-theme-color":{marginBlockStart:"16px",overflow:"hidden","& &-block":{float:"left",width:"20px",height:"20px",marginBlockStart:8,marginInlineEnd:8,color:"#fff",fontWeight:"bold",textAlign:"center",borderRadius:"2px",cursor:"pointer"}}}),n};function wa(s){return(0,U.Xj)("pro-layout-setting-drawer",function(e){var n=(0,p.Z)((0,p.Z)({},e),{},{componentCls:".".concat(s)});return[Ri(n)]})}var to=["color","check"],Io=O.forwardRef(function(s,e){var n=s.color,a=s.check,d=(0,fe.Z)(s,to);return(0,u.jsx)("div",(0,p.Z)((0,p.Z)({},d),{},{style:{backgroundColor:n},ref:e,children:a?(0,u.jsx)(Jn.Z,{}):""}))}),Ii=function(e){var n=e.value,a=e.colorList,d=e.onChange,v=e.prefixCls,b=e.formatMessage,T=e.hashId;if(!a||(a==null?void 0:a.length)<1)return null;var A="".concat(v,"-theme-color");return(0,u.jsx)("div",{className:"".concat(A," ").concat(T),children:a==null?void 0:a.map(function(D){var j=D.key,I=D.color;return j?(0,u.jsx)(li.Z,{title:b({id:"app.setting.themecolor.".concat(j)}),children:(0,u.jsx)(Io,{className:"".concat(A,"-block ").concat(T),color:I,check:n===I,onClick:function(){return d&&d(I)}})},I):null})})},Va=function(e){var n=e.children,a=e.hashId,d=e.prefixCls,v=e.title;return(0,u.jsxs)("div",{style:{marginBlockEnd:24},children:[(0,u.jsx)("h3",{className:"".concat(d,"-body-title ").concat(a),children:v}),n]})},Ta=function(e){var n={};return Object.keys(e).forEach(function(a){e[a]!==Sn[a]&&a!=="collapse"?n[a]=e[a]:n[a]=void 0,a.includes("Render")&&(n[a]=e[a]===!1?!1:void 0)}),n.menu=void 0,n},Zi=function(){var e=function(a){var d=a.id,v=Qn();return v[d]};return e},ro=function(){var s=(0,la.Z)((0,nn.Z)().mark(function e(n){var a,d;return(0,nn.Z)().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:if(typeof window!="undefined"){b.next=2;break}return b.abrupt("return");case 2:if(typeof window.MutationObserver!="undefined"){b.next=4;break}return b.abrupt("return");case 4:n?(a={brightness:100,contrast:90,sepia:10},d={invert:[],css:"",ignoreInlineStyle:[".react-switch-handle"],ignoreImageAnalysis:[],disableStyleSheetsProxy:!0},window.MutationObserver&&window.fetch&&((0,Oa.setFetchMethod)(window.fetch),(0,Oa.enable)(a,d))):window.MutationObserver&&(0,Oa.disable)();case 5:case"end":return b.stop()}},e)}));return function(n){return s.apply(this,arguments)}}(),il=function(e,n,a){if(!!(0,U.jU)()){var d={};Object.keys(e).forEach(function(b){if(Sn[b]||Sn[b]===void 0){if(b==="colorPrimary"){d[b]=uo(e[b]);return}d[b]=e[b]}});var v=(0,U.TS)({},n,d);delete v.menu,delete v.title,delete v.iconfontUrl,a==null||a(v),Sn.navTheme!==e.navTheme&&e.navTheme&&ro(n.navTheme==="realDark")}},Zo=function(e,n){return(0,U.jU)()?(0,p.Z)((0,p.Z)((0,p.Z)({},Sn),n||{}),e):Sn},Ao=function(e){return JSON.stringify((0,Ke.Z)((0,p.Z)((0,p.Z)({},e),{},{colorPrimary:e.colorPrimary}),["colorWeak"]),null,2)},Oo=function(e){var n=e.defaultSettings,a=n===void 0?void 0:n,d=e.settings,v=d===void 0?void 0:d,b=e.hideHintAlert,T=e.hideCopyButton,A=e.colorList,D=A===void 0?[{key:"techBlue",color:"#1677FF"},{key:"daybreak",color:"#1890ff"},{key:"dust",color:"#F5222D"},{key:"volcano",color:"#FA541C"},{key:"sunset",color:"#FAAD14"},{key:"cyan",color:"#13C2C2"},{key:"green",color:"#52C41A"},{key:"geekblue",color:"#2F54EB"},{key:"purple",color:"#722ED1"}]:A,j=e.getContainer,I=e.onSettingChange,W=e.enableDarkTheme,ee=e.prefixCls,ne=ee===void 0?"ant-pro":ee,ie=e.pathname,le=ie===void 0?window.location.pathname:ie,je=e.disableUrlParams,Oe=je===void 0?!0:je,Ye=e.themeOnly,ye=(0,O.useRef)(!0),Le=(0,Gi.default)(!1,{value:e.collapse,onChange:e.onCollapseChange}),Ve=(0,tt.Z)(Le,2),at=Ve[0],Rt=Ve[1],Bt=(0,O.useState)(Da()),Wt=(0,tt.Z)(Bt,2),pr=Wt[0],Er=Wt[1],$t=(0,Ti.l)({},{disabled:Oe}),Hr=(0,tt.Z)($t,2),Ge=Hr[0],Zr=Hr[1],Mn=(0,Gi.default)(function(){return Zo(Ge,v||a)},{value:v,onChange:I}),Dn=(0,tt.Z)(Mn,2),or=Dn[0],en=Dn[1],zn=or||{},ta=zn.navTheme,ha=zn.colorPrimary,Ra=zn.siderMenuType,Na=zn.layout,Di=zn.colorWeak;(0,O.useEffect)(function(){var vr=function(){pr!==Da()&&Er(Da())};return(0,U.jU)()?(il(Zo(Ge,v),or,en),window.document.addEventListener("languagechange",vr,{passive:!0}),function(){return window.document.removeEventListener("languagechange",vr)}):function(){return null}},[]),(0,O.useEffect)(function(){ro(or.navTheme==="realDark"),(0,U.n4)(ri.Z,"5.0.0")<0&&We.ZP.config({theme:{primaryColor:or.colorPrimary}})},[or.colorPrimary,or.navTheme]);var mn=function(Gt,yr){var sn={};if(sn[Gt]=yr,Gt==="layout"&&(sn.contentWidth=yr==="top"?"Fixed":"Fluid"),Gt==="layout"&&yr!=="mix"&&(sn.splitMenus=!1),Gt==="layout"&&yr==="mix"&&(sn.navTheme="light"),Gt==="colorWeak"&&yr===!0){var pn=document.querySelector("body");pn&&(pn.dataset.prosettingdrawer=pn.style.filter,pn.style.filter="invert(80%)")}if(Gt==="colorWeak"&&yr===!1){var Ia=document.querySelector("body");Ia&&(Ia.style.filter=Ia.dataset.prosettingdrawer||"none",delete Ia.dataset.prosettingdrawer)}delete sn.menu,delete sn.title,delete sn.iconfontUrl,delete sn.logo,delete sn.pwa,en((0,p.Z)((0,p.Z)({},or),sn))},lr=Zi();(0,O.useEffect)(function(){if(!!(0,U.jU)()&&!Oe){if(ye.current){ye.current=!1;return}var vr=new URLSearchParams(window.location.search),Gt=Object.fromEntries(vr.entries()),yr=Ta((0,p.Z)((0,p.Z)({},Gt),or));delete yr.logo,delete yr.menu,delete yr.title,delete yr.iconfontUrl,delete yr.pwa,Zr(yr)}},[Zr,or,Ge,le,Oe]);var sr="".concat(ne,"-setting-drawer"),Un=wa(sr),Ya=Un.wrapSSR,Vr=Un.hashId,ji=(0,U.n4)(ri.Z,"4.23.0")>-1?{open:at,onClose:function(){return Rt(!1)}}:{visible:at,onClose:function(){return Rt(!1)}};return Ya((0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"".concat(sr,"-handle ").concat(Vr),onClick:function(){return Rt(!at)},style:{width:48,height:48},children:at?(0,u.jsx)(ca.Z,{style:{color:"#fff",fontSize:20}}):(0,u.jsx)(To.Z,{style:{color:"#fff",fontSize:20}})}),(0,u.jsx)(La.Z,(0,p.Z)((0,p.Z)({},ji),{},{width:300,closable:!1,placement:"right",getContainer:j,style:{zIndex:999},children:(0,u.jsxs)("div",{className:"".concat(sr,"-drawer-content ").concat(Vr),children:[(0,u.jsx)(Va,{title:lr({id:"app.setting.pagestyle",defaultMessage:"Page style setting"}),hashId:Vr,prefixCls:sr,children:(0,u.jsx)(Ji,{hashId:Vr,prefixCls:sr,list:[{key:"light",title:lr({id:"app.setting.pagestyle.light",defaultMessage:"\u4EAE\u8272\u83DC\u5355\u98CE\u683C"})},{key:"realDark",title:lr({id:"app.setting.pagestyle.realdark",defaultMessage:"\u6697\u8272\u83DC\u5355\u98CE\u683C"})}].filter(function(vr){return!(vr.key==="dark"&&or.layout==="mix"||vr.key==="realDark"&&!W)}),value:ta,configType:"theme",onChange:function(Gt){return mn("navTheme",Gt)}},"navTheme")}),D!==!1&&(0,u.jsx)(Va,{hashId:Vr,title:lr({id:"app.setting.themecolor",defaultMessage:"Theme color"}),prefixCls:sr,children:(0,u.jsx)(Ii,{hashId:Vr,prefixCls:sr,colorList:D,value:uo(ha),formatMessage:lr,onChange:function(Gt){return mn("colorPrimary",Gt)}})}),!Ye&&(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(Sa.Z,{}),(0,u.jsx)(Va,{hashId:Vr,prefixCls:sr,title:lr({id:"app.setting.navigationmode"}),children:(0,u.jsx)(Ji,{prefixCls:sr,value:Na,hashId:Vr,configType:"layout",list:[{key:"side",title:lr({id:"app.setting.sidemenu"})},{key:"top",title:lr({id:"app.setting.topmenu"})},{key:"mix",title:lr({id:"app.setting.mixmenu"})}],onChange:function(Gt){return mn("layout",Gt)}},"layout")}),or.layout=="side"||or.layout=="mix"?(0,u.jsx)(Va,{hashId:Vr,prefixCls:sr,title:lr({id:"app.setting.sidermenutype"}),children:(0,u.jsx)(Ji,{prefixCls:sr,value:Ra,hashId:Vr,configType:"siderMenuType",list:[{key:"sub",icon:(0,u.jsx)(qi,{}),title:lr({id:"app.setting.sidermenutype-sub"})},{key:"group",icon:(0,u.jsx)(Po,{}),title:lr({id:"app.setting.sidermenutype-group"})}],onChange:function(Gt){return mn("siderMenuType",Gt)}},"siderMenuType")}):null,(0,u.jsx)(Ro,{hashId:Vr,settings:or,changeSetting:mn}),(0,u.jsx)(Sa.Z,{}),(0,u.jsx)(Va,{hashId:Vr,prefixCls:sr,title:lr({id:"app.setting.regionalsettings"}),children:(0,u.jsx)(al,{hashId:Vr,settings:or,changeSetting:mn})}),(0,u.jsx)(Sa.Z,{}),(0,u.jsx)(Va,{hashId:Vr,prefixCls:sr,title:lr({id:"app.setting.othersettings"}),children:(0,u.jsx)(Ua.ZP,{split:!1,renderItem:Pi,dataSource:[{title:lr({id:"app.setting.weakmode"}),action:(0,u.jsx)(Ca.Z,{size:"small",className:"color-weak",checked:!!Di,onChange:function(Gt){mn("colorWeak",Gt)}})}]})}),b&&T?null:(0,u.jsx)(Sa.Z,{}),b?null:(0,u.jsx)(Wa.Z,{type:"warning",message:lr({id:"app.setting.production.hint"}),icon:(0,u.jsx)(za,{}),showIcon:!0,style:{marginBlockEnd:16}}),T?null:(0,u.jsx)(dr.Z,{block:!0,icon:(0,u.jsx)(wi.Z,{}),style:{marginBlockEnd:24},onClick:function(){var vr=(0,la.Z)((0,nn.Z)().mark(function yr(){return(0,nn.Z)().wrap(function(pn){for(;;)switch(pn.prev=pn.next){case 0:return pn.prev=0,pn.next=3,navigator.clipboard.writeText(Ao(or));case 3:Eo.default.success(lr({id:"app.setting.copyinfo"})),pn.next=8;break;case 6:pn.prev=6,pn.t0=pn.catch(0);case 8:case"end":return pn.stop()}},yr,null,[[0,6]])}));function Gt(){return vr.apply(this,arguments)}return Gt}(),children:lr({id:"app.setting.copy"})})]})]})}))]}))},Lo=m(14779),Ka=m.n(Lo),no=function(e,n,a){if(a){var d=(0,Ht.Z)(a.keys()).find(function(b){return Ka()(b).test(e)});if(d)return a.get(d)}if(n){var v=Object.keys(n).find(function(b){return Ka()(b).test(e)});if(v)return n[v]}return{path:""}},Ea=function(e,n){var a=e.pathname,d=a===void 0?"/":a,v=e.breadcrumb,b=e.breadcrumbMap,T=e.formatMessage,A=e.title,D=e.menu,j=D===void 0?{locale:!1}:D,I=n?"":A||"",W=no(d,v,b);if(!W)return{title:I,id:"",pageName:I};var ee=W.name;return j.locale!==!1&&W.locale&&T&&(ee=T({id:W.locale||"",defaultMessage:W.name})),ee?n||!A?{title:ee,id:W.locale||"",pageName:ee}:{title:"".concat(ee," - ").concat(A),id:W.locale||"",pageName:ee}:{title:I,id:W.locale||"",pageName:I}},ao=function(e,n){return Ea(e,n).title},Ai=m(6234),Oi=m.n(Ai),da=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function Do(s,e){return!!(s===e||da(s)&&da(e))}function ol(s,e){if(s.length!==e.length)return!1;for(var n=0;n<s.length;n++)if(!Do(s[n],e[n]))return!1;return!0}function ll(s,e){e===void 0&&(e=ol);var n,a=[],d,v=!1;function b(){for(var T=[],A=0;A<arguments.length;A++)T[A]=arguments[A];return v&&n===this&&e(T,a)||(d=s.apply(this,T),v=!0,n=this,a=T),d}return b}var si=ll;function sl(s){for(var e=[],n=0;n<s.length;){var a=s[n];if(a==="*"||a==="+"||a==="?"){e.push({type:"MODIFIER",index:n,value:s[n++]});continue}if(a==="\\"){e.push({type:"ESCAPED_CHAR",index:n++,value:s[n++]});continue}if(a==="{"){e.push({type:"OPEN",index:n,value:s[n++]});continue}if(a==="}"){e.push({type:"CLOSE",index:n,value:s[n++]});continue}if(a===":"){for(var d="",v=n+1;v<s.length;){var b=s.charCodeAt(v);if(b>=48&&b<=57||b>=65&&b<=90||b>=97&&b<=122||b===95){d+=s[v++];continue}break}if(!d)throw new TypeError("Missing parameter name at "+n);e.push({type:"NAME",index:n,value:d}),n=v;continue}if(a==="("){var T=1,A="",v=n+1;if(s[v]==="?")throw new TypeError('Pattern cannot start with "?" at '+v);for(;v<s.length;){if(s[v]==="\\"){A+=s[v++]+s[v++];continue}if(s[v]===")"){if(T--,T===0){v++;break}}else if(s[v]==="("&&(T++,s[v+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+v);A+=s[v++]}if(T)throw new TypeError("Unbalanced pattern at "+n);if(!A)throw new TypeError("Missing pattern at "+n);e.push({type:"PATTERN",index:n,value:A}),n=v;continue}e.push({type:"CHAR",index:n,value:s[n++]})}return e.push({type:"END",index:n,value:""}),e}function jo(s,e){e===void 0&&(e={});for(var n=sl(s),a=e.prefixes,d=a===void 0?"./":a,v="[^"+fa(e.delimiter||"/#?")+"]+?",b=[],T=0,A=0,D="",j=function(Ve){if(A<n.length&&n[A].type===Ve)return n[A++].value},I=function(Ve){var at=j(Ve);if(at!==void 0)return at;var Rt=n[A],Bt=Rt.type,Wt=Rt.index;throw new TypeError("Unexpected "+Bt+" at "+Wt+", expected "+Ve)},W=function(){for(var Ve="",at;at=j("CHAR")||j("ESCAPED_CHAR");)Ve+=at;return Ve};A<n.length;){var ee=j("CHAR"),ne=j("NAME"),ie=j("PATTERN");if(ne||ie){var le=ee||"";d.indexOf(le)===-1&&(D+=le,le=""),D&&(b.push(D),D=""),b.push({name:ne||T++,prefix:le,suffix:"",pattern:ie||v,modifier:j("MODIFIER")||""});continue}var je=ee||j("ESCAPED_CHAR");if(je){D+=je;continue}D&&(b.push(D),D="");var Oe=j("OPEN");if(Oe){var le=W(),Ye=j("NAME")||"",ye=j("PATTERN")||"",Le=W();I("CLOSE"),b.push({name:Ye||(ye?T++:""),pattern:Ye&&!ye?v:ye,prefix:le,suffix:Le,modifier:j("MODIFIER")||""});continue}I("END")}return b}function ci(s,e){return cl(jo(s,e),e)}function cl(s,e){e===void 0&&(e={});var n=ui(e),a=e.encode,d=a===void 0?function(A){return A}:a,v=e.validate,b=v===void 0?!0:v,T=s.map(function(A){if(typeof A=="object")return new RegExp("^(?:"+A.pattern+")$",n)});return function(A){for(var D="",j=0;j<s.length;j++){var I=s[j];if(typeof I=="string"){D+=I;continue}var W=A?A[I.name]:void 0,ee=I.modifier==="?"||I.modifier==="*",ne=I.modifier==="*"||I.modifier==="+";if(Array.isArray(W)){if(!ne)throw new TypeError('Expected "'+I.name+'" to not repeat, but got an array');if(W.length===0){if(ee)continue;throw new TypeError('Expected "'+I.name+'" to not be empty')}for(var ie=0;ie<W.length;ie++){var le=d(W[ie],I);if(b&&!T[j].test(le))throw new TypeError('Expected all "'+I.name+'" to match "'+I.pattern+'", but got "'+le+'"');D+=I.prefix+le+I.suffix}continue}if(typeof W=="string"||typeof W=="number"){var le=d(String(W),I);if(b&&!T[j].test(le))throw new TypeError('Expected "'+I.name+'" to match "'+I.pattern+'", but got "'+le+'"');D+=I.prefix+le+I.suffix;continue}if(!ee){var je=ne?"an array":"a string";throw new TypeError('Expected "'+I.name+'" to be '+je)}}return D}}function yl(s,e){var n=[],a=Tt(s,n,e);return Fo(a,n,e)}function Fo(s,e,n){n===void 0&&(n={});var a=n.decode,d=a===void 0?function(v){return v}:a;return function(v){var b=s.exec(v);if(!b)return!1;for(var T=b[0],A=b.index,D=Object.create(null),j=function(W){if(b[W]===void 0)return"continue";var ee=e[W-1];ee.modifier==="*"||ee.modifier==="+"?D[ee.name]=b[W].split(ee.prefix+ee.suffix).map(function(ne){return d(ne,ee)}):D[ee.name]=d(b[W],ee)},I=1;I<b.length;I++)j(I);return{path:T,index:A,params:D}}}function fa(s){return s.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function ui(s){return s&&s.sensitive?"":"i"}function Cn(s,e){if(!e)return s;var n=s.source.match(/\((?!\?)/g);if(n)for(var a=0;a<n.length;a++)e.push({name:a,prefix:"",suffix:"",modifier:"",pattern:""});return s}function $a(s,e,n){var a=s.map(function(d){return Tt(d,e,n).source});return new RegExp("(?:"+a.join("|")+")",ui(n))}function In(s,e,n){return Li(jo(s,n),e,n)}function Li(s,e,n){n===void 0&&(n={});for(var a=n.strict,d=a===void 0?!1:a,v=n.start,b=v===void 0?!0:v,T=n.end,A=T===void 0?!0:T,D=n.encode,j=D===void 0?function(Ve){return Ve}:D,I="["+fa(n.endsWith||"")+"]|$",W="["+fa(n.delimiter||"/#?")+"]",ee=b?"^":"",ne=0,ie=s;ne<ie.length;ne++){var le=ie[ne];if(typeof le=="string")ee+=fa(j(le));else{var je=fa(j(le.prefix)),Oe=fa(j(le.suffix));if(le.pattern)if(e&&e.push(le),je||Oe)if(le.modifier==="+"||le.modifier==="*"){var Ye=le.modifier==="*"?"?":"";ee+="(?:"+je+"((?:"+le.pattern+")(?:"+Oe+je+"(?:"+le.pattern+"))*)"+Oe+")"+Ye}else ee+="(?:"+je+"("+le.pattern+")"+Oe+")"+le.modifier;else ee+="("+le.pattern+")"+le.modifier;else ee+="(?:"+je+Oe+")"+le.modifier}}if(A)d||(ee+=W+"?"),ee+=n.endsWith?"(?="+I+")":"$";else{var ye=s[s.length-1],Le=typeof ye=="string"?W.indexOf(ye[ye.length-1])>-1:ye===void 0;d||(ee+="(?:"+W+"(?="+I+"))?"),Le||(ee+="(?="+W+"|"+I+")")}return new RegExp(ee,ui(n))}function Tt(s,e,n){return s instanceof RegExp?Cn(s,e):Array.isArray(s)?$a(s,e,n):In(s,e,n)}function Qt(s,e){return e>>>s|e<<32-s}function Bo(s,e,n){return s&e^~s&n}function Nn(s,e,n){return s&e^s&n^e&n}function di(s){return Qt(2,s)^Qt(13,s)^Qt(22,s)}function Zn(s){return Qt(6,s)^Qt(11,s)^Qt(25,s)}function ul(s){return Qt(7,s)^Qt(18,s)^s>>>3}function l(s){return Qt(17,s)^Qt(19,s)^s>>>10}function o(s,e){return s[e&15]+=l(s[e+14&15])+s[e+9&15]+ul(s[e+1&15])}var f=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],y,E,Z,L="0123456789abcdef";function k(s,e){var n=(s&65535)+(e&65535),a=(s>>16)+(e>>16)+(n>>16);return a<<16|n&65535}function te(){y=new Array(8),E=new Array(2),Z=new Array(64),E[0]=E[1]=0,y[0]=1779033703,y[1]=3144134277,y[2]=1013904242,y[3]=2773480762,y[4]=1359893119,y[5]=2600822924,y[6]=528734635,y[7]=1541459225}function ce(){var s,e,n,a,d,v,b,T,A,D,j=new Array(16);s=y[0],e=y[1],n=y[2],a=y[3],d=y[4],v=y[5],b=y[6],T=y[7];for(var I=0;I<16;I++)j[I]=Z[(I<<2)+3]|Z[(I<<2)+2]<<8|Z[(I<<2)+1]<<16|Z[I<<2]<<24;for(var W=0;W<64;W++)A=T+Zn(d)+Bo(d,v,b)+f[W],W<16?A+=j[W]:A+=o(j,W),D=di(s)+Nn(s,e,n),T=b,b=v,v=d,d=k(a,A),a=n,n=e,e=s,s=k(A,D);y[0]+=s,y[1]+=e,y[2]+=n,y[3]+=a,y[4]+=d,y[5]+=v,y[6]+=b,y[7]+=T}function re(s,e){var n,a,d=0;a=E[0]>>3&63;var v=e&63;for((E[0]+=e<<3)<e<<3&&E[1]++,E[1]+=e>>29,n=0;n+63<e;n+=64){for(var b=a;b<64;b++)Z[b]=s.charCodeAt(d++);ce(),a=0}for(var T=0;T<v;T++)Z[T]=s.charCodeAt(d++)}function de(){var s=E[0]>>3&63;if(Z[s++]=128,s<=56)for(var e=s;e<56;e++)Z[e]=0;else{for(var n=s;n<64;n++)Z[n]=0;ce();for(var a=0;a<56;a++)Z[a]=0}Z[56]=E[1]>>>24&255,Z[57]=E[1]>>>16&255,Z[58]=E[1]>>>8&255,Z[59]=E[1]&255,Z[60]=E[0]>>>24&255,Z[61]=E[0]>>>16&255,Z[62]=E[0]>>>8&255,Z[63]=E[0]&255,ce()}function me(){for(var s=0,e=new Array(32),n=0;n<8;n++)e[s++]=y[n]>>>24&255,e[s++]=y[n]>>>16&255,e[s++]=y[n]>>>8&255,e[s++]=y[n]&255;return e}function ue(){for(var s=new String,e=0;e<8;e++)for(var n=28;n>=0;n-=4)s+=L.charAt(y[e]>>>n&15);return s}function he(s){return te(),re(s,s.length),de(),ue()}var Ie=he;function Ze(s){return Ze=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ze(s)}var Fe=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function xe(s,e){return ze(s)||De(s,e)||_t(s,e)||Pe()}function Pe(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function De(s,e){var n=s==null?null:typeof Symbol!="undefined"&&s[Symbol.iterator]||s["@@iterator"];if(n!=null){var a=[],d=!0,v=!1,b,T;try{for(n=n.call(s);!(d=(b=n.next()).done)&&(a.push(b.value),!(e&&a.length===e));d=!0);}catch(A){v=!0,T=A}finally{try{!d&&n.return!=null&&n.return()}finally{if(v)throw T}}return a}}function ze(s){if(Array.isArray(s))return s}function jt(s,e){var n=typeof Symbol!="undefined"&&s[Symbol.iterator]||s["@@iterator"];if(!n){if(Array.isArray(s)||(n=_t(s))||e&&s&&typeof s.length=="number"){n&&(s=n);var a=0,d=function(){};return{s:d,n:function(){return a>=s.length?{done:!0}:{done:!1,value:s[a++]}},e:function(D){throw D},f:d}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var v=!0,b=!1,T;return{s:function(){n=n.call(s)},n:function(){var D=n.next();return v=D.done,D},e:function(D){b=!0,T=D},f:function(){try{!v&&n.return!=null&&n.return()}finally{if(b)throw T}}}}function Be(s,e){if(!(s instanceof e))throw new TypeError("Cannot call a class as a function")}function Nr(s,e){for(var n=0;n<e.length;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(s,a.key,a)}}function An(s,e,n){return e&&Nr(s.prototype,e),n&&Nr(s,n),Object.defineProperty(s,"prototype",{writable:!1}),s}function Jt(s,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");s.prototype=Object.create(e&&e.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),e&&vt(s,e)}function ja(s){var e=Ue();return function(){var a=lt(s),d;if(e){var v=lt(this).constructor;d=Reflect.construct(a,arguments,v)}else d=a.apply(this,arguments);return zr(this,d)}}function zr(s,e){if(e&&(Ze(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ma(s)}function Ma(s){if(s===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s}function an(s){var e=typeof Map=="function"?new Map:void 0;return an=function(a){if(a===null||!ft(a))return a;if(typeof a!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(a))return e.get(a);e.set(a,d)}function d(){return bt(a,arguments,lt(this).constructor)}return d.prototype=Object.create(a.prototype,{constructor:{value:d,enumerable:!1,writable:!0,configurable:!0}}),vt(d,a)},an(s)}function bt(s,e,n){return Ue()?bt=Reflect.construct.bind():bt=function(d,v,b){var T=[null];T.push.apply(T,v);var A=Function.bind.apply(d,T),D=new A;return b&&vt(D,b.prototype),D},bt.apply(null,arguments)}function Ue(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(s){return!1}}function ft(s){return Function.toString.call(s).indexOf("[native code]")!==-1}function vt(s,e){return vt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,d){return a.__proto__=d,a},vt(s,e)}function lt(s){return lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},lt(s)}function tr(s){return Rr(s)||Kt(s)||_t(s)||Cr()}function Cr(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _t(s,e){if(!!s){if(typeof s=="string")return Ut(s,e);var n=Object.prototype.toString.call(s).slice(8,-1);if(n==="Object"&&s.constructor&&(n=s.constructor.name),n==="Map"||n==="Set")return Array.from(s);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ut(s,e)}}function Kt(s){if(typeof Symbol!="undefined"&&s[Symbol.iterator]!=null||s["@@iterator"]!=null)return Array.from(s)}function Rr(s){if(Array.isArray(s))return Ut(s)}function Ut(s,e){(e==null||e>s.length)&&(e=s.length);for(var n=0,a=new Array(e);n<e;n++)a[n]=s[n];return a}function Pa(s,e){if(s==null)return{};var n=No(s,e),a,d;if(Object.getOwnPropertySymbols){var v=Object.getOwnPropertySymbols(s);for(d=0;d<v.length;d++)a=v[d],!(e.indexOf(a)>=0)&&(!Object.prototype.propertyIsEnumerable.call(s,a)||(n[a]=s[a]))}return n}function No(s,e){if(s==null)return{};var n={},a=Object.keys(s),d,v;for(v=0;v<a.length;v++)d=a[v],!(e.indexOf(d)>=0)&&(n[d]=s[d]);return n}function t(s,e){var n=Object.keys(s);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(s);e&&(a=a.filter(function(d){return Object.getOwnPropertyDescriptor(s,d).enumerable})),n.push.apply(n,a)}return n}function r(s){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?t(Object(n),!0).forEach(function(a){i(s,a,n[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach(function(a){Object.defineProperty(s,a,Object.getOwnPropertyDescriptor(n,a))})}return s}function i(s,e,n){return e in s?Object.defineProperty(s,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[e]=n,s}var c="routes";function g(s){return s.split("?")[0].split("#")[0]}var h=function(e){if(!e.startsWith("http"))return!1;try{var n=new URL(e);return!!n}catch(a){return!1}},x=function(e){var n=e.path;if(!n||n==="/")try{return"/".concat(Ie(JSON.stringify(e)))}catch(a){}return n&&g(n)},C=function(e,n){var a=e.name,d=e.locale;return"locale"in e&&d===!1||!a?!1:e.locale||"".concat(n,".").concat(a)},F=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||n).startsWith("/")||h(e)?e:"/".concat(n,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},R=function(e,n){var a=e.menu,d=a===void 0?{}:a,v=e.indexRoute,b=e.path,T=b===void 0?"":b,A=e.children||e[c],D=d.name,j=D===void 0?e.name:D,I=d.icon,W=I===void 0?e.icon:I,ee=d.hideChildren,ne=ee===void 0?e.hideChildren:ee,ie=d.flatMenu,le=ie===void 0?e.flatMenu:ie,je=v&&Object.keys(v).join(",")!=="redirect"?[r({path:T,menu:d},v)].concat(A||[]):A,Oe=r({},e);if(j&&(Oe.name=j),W&&(Oe.icon=W),je&&je.length){if(ne)return delete Oe[c],delete Oe.children,Oe;var Ye=K(r(r({},n),{},{data:je}),e);if(le)return Ye;Oe[c]=Ye}return Oe},N=function(e){return Array.isArray(e)&&e.length>0};function K(s){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},n=s.data,a=s.formatMessage,d=s.parentName,v=s.locale;return!n||!Array.isArray(n)?[]:n.filter(function(b){return b?N(b[c])||N(b.children)||b.path||b.originPath||b.layout?!0:(b.redirect||b.unaccessible,!1):!1}).filter(function(b){var T,A;return(b==null||(T=b.menu)===null||T===void 0?void 0:T.name)||(b==null?void 0:b.flatMenu)||(b==null||(A=b.menu)===null||A===void 0?void 0:A.flatMenu)?!0:b.menu!==!1}).map(function(b){var T=r({},b);return T.unaccessible&&delete T.name,T.path==="*"&&(T.path="."),T.path==="/*"&&(T.path="."),!T.path&&T.originPath&&(T.path=T.originPath),T}).map(function(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"},T=b.children||b[c],A=F(b.path,e?e.path:"/"),D=b.name,j=C(b,d||"menu"),I=j!==!1&&v!==!1&&a&&j?a({id:j,defaultMessage:D}):D,W=e.pro_layout_parentKeys,ee=W===void 0?[]:W,ne=e.children,ie=e.icon,le=e.flatMenu,je=e.indexRoute,Oe=e.routes,Ye=Pa(e,Fe),ye=new Set([].concat(tr(ee),tr(b.parentKeys||[])));e.key&&ye.add(e.key);var Le=r(r(r({},Ye),{},{menu:void 0},b),{},{path:A,locale:j,key:b.key||x(r(r({},b),{},{path:A})),pro_layout_parentKeys:Array.from(ye).filter(function(at){return at&&at!=="/"})});if(I?Le.name=I:delete Le.name,Le.menu===void 0&&delete Le.menu,N(T)){var Ve=K(r(r({},s),{},{data:T,parentName:j||""}),Le);N(Ve)&&(Le[c]=Ve,Le.children=Ve)}return R(Le,s)}).flat(1)}var Te=si(K,Oi()),Me=function s(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.filter(function(n){return n&&(n.name||N(n[c])||N(n.children))&&!n.hideInMenu&&!n.redirect}).map(function(n){var a=r({},n),d=a.children||a[c];if(N(d)&&!a.hideChildrenInMenu&&d.some(function(T){return T&&!!T.name})){var v,b=s(d);if(b.length)return r(r({},a),{},(v={},i(v,c,b),i(v,"children",b),v))}return r(r({},n),{},i({},c,void 0))}).filter(function(n){return n})},ae=function(s){Jt(n,s);var e=ja(n);function n(){return Be(this,n),e.apply(this,arguments)}return An(n,[{key:"get",value:function(d){var v;try{var b=jt(this.entries()),T;try{for(b.s();!(T=b.n()).done;){var A=xe(T.value,2),D=A[0],j=A[1],I=g(D);if(!h(D)&&Tt(I,[]).test(d)){v=j;break}}}catch(W){b.e(W)}finally{b.f()}}catch(W){v=void 0}return v}}]),n}(an(Map)),$=function(e){var n=new ae,a=function d(v,b){v.forEach(function(T){var A=T.children||T[c];N(A)&&d(A,T);var D=F(T.path,b?b.path:"/");n.set(g(D),T)})};return a(e),n},q=si($,Oi()),pe=function s(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(function(n){var a=n.children||n[c];if(N(a)){var d=s(a);if(d.length)return r(r({},n),{},i({},c,d))}var v=r({},n);return delete v[c],delete v.children,v}).filter(function(n){return n})},Je=function(e,n,a,d){var v=Te({data:e,formatMessage:a,locale:n}),b=d?pe(v):Me(v),T=q(v);return{breadcrumb:T,menuData:b}},nt=Je;function Ot(s,e){var n=Object.keys(s);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(s);e&&(a=a.filter(function(d){return Object.getOwnPropertyDescriptor(s,d).enumerable})),n.push.apply(n,a)}return n}function Pt(s){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Ot(Object(n),!0).forEach(function(a){St(s,a,n[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(n)):Ot(Object(n)).forEach(function(a){Object.defineProperty(s,a,Object.getOwnPropertyDescriptor(n,a))})}return s}function St(s,e,n){return e in s?Object.defineProperty(s,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[e]=n,s}var qt=function s(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n={};return e.forEach(function(a){if(!(!a||!a.key)){var d=a.children||a[c];n[g(a.path||a.key||"/")]=Pt({},a),n[a.key||a.path||"/"]=Pt({},a),d&&(n=Pt(Pt({},n),s(d)))}}),n},Dr=qt,kr=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,a=arguments.length>2?arguments[2]:void 0;return e.filter(function(d){if(d==="/"&&n==="/")return!0;if(d!=="/"&&d!=="/*"&&d&&!h(d)){var v=g(d);try{if(a&&Tt("".concat(v)).test(n)||Tt("".concat(v),[]).test(n)||Tt("".concat(v,"/(.*)")).test(n))return!0}catch(b){}}return!1}).sort(function(d,v){return d===n?10:v===n?-10:d.substr(1).split("/").length-v.substr(1).split("/").length})},wn=function(e,n,a,d){var v=Dr(n),b=Object.keys(v),T=kr(b,e||"/",d);return!T||T.length<1?[]:(a||(T=[T[T.length-1]]),T.map(function(A){var D=v[A]||{pro_layout_parentKeys:"",key:""},j=new Map,I=(D.pro_layout_parentKeys||[]).map(function(W){return j.has(W)?null:(j.set(W,!0),v[W])}).filter(function(W){return W});return D.key&&I.push(D),I}).flat(1))},fi=wn,Fa=m(29405),io=m(38069),ko=function(){return(0,u.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,u.jsxs)("defs",{children:[(0,u.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,u.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,u.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,u.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,u.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,u.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,u.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,u.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,u.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,u.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,u.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,u.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,u.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,u.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,u.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,u.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,u.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,u.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,u.jsxs)("g",{children:[(0,u.jsxs)("g",{fillRule:"nonzero",children:[(0,u.jsxs)("g",{children:[(0,u.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,u.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,u.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,u.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})},dl=function(e){var n;return(0,w.Z)({},e.proComponentsCls,(0,w.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,w.Z)({},e.componentCls,(n={position:"relative",background:e.colorMenuBackground||"transparent",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"},"&-fixed":{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%"}},(0,w.Z)(n,"".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:e.paddingInlineLayoutMenu,paddingBlock:e.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit)}),(0,w.Z)(n,"".concat(e.antCls,"-menu"),(0,w.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4})),(0,w.Z)(n,"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:e.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat(e.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:e.colorTextMenuTitle,fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,w.Z)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),(0,w.Z)(n,"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:e.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:e.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animation:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.radiusBase,"&:hover":{background:"rgba(0, 0, 0, 0.018)"}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,borderRadius:e.radiusBase,"& *":{cursor:"pointer"},"&:hover":{background:"rgba(0, 0, 0, 0.018)"}}}),(0,w.Z)(n,"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),(0,w.Z)(n,"&-mix",{height:"calc(100% - ".concat(e.heightLayoutHeader,"px)"),insetBlockStart:"".concat(e.heightLayoutHeader,"px")}),(0,w.Z)(n,"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),(0,w.Z)(n,"&-links",{width:"100%",ul:{height:"auto"}}),(0,w.Z)(n,"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),(0,w.Z)(n,"&-footer",{color:e.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize}),n))))};function oo(s,e){var n=e.proLayoutCollapsedWidth,a=(0,O.useContext)(Ee),d=a.sider,v=a.header;return(0,U.Xj)("sider-menu",function(b){var T=(0,p.Z)((0,p.Z)({},b),{},{componentCls:".".concat(s),proLayoutCollapsedWidth:n,heightLayoutHeader:v.heightLayoutHeader},d);return[dl(T)]})}var zt=function(e){var n=e.isMobile,a=e.menuData,d=e.siderWidth,v=e.collapsed,b=e.onCollapse,T=e.style,A=e.className,D=e.hide,j=e.getContainer,I=e.prefixCls,W=e.matchMenuKeys,ee=ai.useContainer(),ne=ee.setFlatMenuKeys;(0,O.useEffect)(function(){if(!(!a||a.length<1)){var Le=Dr(a);ne(Object.keys(Le))}},[W.join("-")]),(0,O.useEffect)(function(){n===!0&&(b==null||b(!0))},[n]);var ie=(0,Ke.Z)(e,["className","style"]),le=oo("".concat(I,"-sider"),{proLayoutCollapsedWidth:64}),je=le.wrapSSR,Oe=le.hashId,Ye=Se()("".concat(I,"-sider"),A,Oe);if(D)return null;var ye=(0,U.n4)(ri.Z,"4.23.0")>-1?{open:!v,onClose:function(){return b==null?void 0:b(!0)}}:{visible:!v,onClose:function(){return b==null?void 0:b(!0)}};return je(n?(0,u.jsx)(La.Z,(0,p.Z)((0,p.Z)({placement:"left",className:Se()("".concat(I,"-drawer-sider"),A)},ye),{},{style:(0,p.Z)({padding:0,height:"100vh"},T),closable:!1,getContainer:j,width:d,bodyStyle:{height:"100vh",padding:0,display:"flex",flexDirection:"row"},children:(0,u.jsx)(Jr,(0,p.Z)((0,p.Z)({},ie),{},{isMobile:!0,className:Ye,collapsed:n?!1:v,splitMenus:!1,originCollapsed:v}))})):(0,u.jsx)(Jr,(0,p.Z)((0,p.Z)({className:Ye,originCollapsed:v},ie),{},{style:T})))},fr=function(e){var n,a,d,v,b,T,A,D,j;return ri.Z.startsWith("5")?{}:(j={},(0,w.Z)(j,e.proComponentsCls,{width:"100%",height:"100%"}),(0,w.Z)(j,e.componentCls,(T={width:"100%",height:"100%"},(0,w.Z)(T,"".concat(e.proComponentsCls,"-base-menu"),(d={color:e.sider.colorTextMenu},(0,w.Z)(d,"".concat(e.antCls,"-menu-sub"),{color:e.sider.colorTextMenu}),(0,w.Z)(d,"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),(0,w.Z)(d,"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),(0,w.Z)(d,"&".concat(e.antCls,"-menu"),(0,w.Z)({color:e.sider.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),(0,w.Z)(d,"&".concat(e.antCls,"-menu-inline"),(0,w.Z)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),(0,w.Z)(d,"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),(0,w.Z)(d,"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),(0,w.Z)(d,"&".concat(e.antCls,"-menu-light"),(0,w.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,w.Z)({color:e.sider.colorTextMenuActive,borderRadius:e.radiusBase},"".concat(e.antCls,"-menu-submenu-arrow"),{color:e.sider.colorTextMenuActive}))),(0,w.Z)(d,"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(n={},(0,w.Z)(n,"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:e.sider.colorBgMenuItemSelected,borderRadius:e.radiusBase}),(0,w.Z)(n,"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,w.Z)({color:e.sider.colorTextMenuActive,borderRadius:e.radiusBase},"".concat(e.antCls,"-menu-submenu-arrow"),{color:e.sider.colorTextMenuActive})),n)),(0,w.Z)(d,"".concat(e.antCls,"-menu-item-selected"),{color:e.sider.colorTextMenuSelected}),(0,w.Z)(d,"".concat(e.antCls,"-menu-submenu-selected"),{color:e.sider.colorTextMenuSelected}),(0,w.Z)(d,"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:e.sider.colorTextMenuSelected}),(0,w.Z)(d,"&".concat(e.antCls,"-menu-vertical"),(0,w.Z)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.radiusBase,color:e.sider.colorTextMenuSelected})),(0,w.Z)(d,"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:e.sider.colorTextMenuActive}),(0,w.Z)(d,"&".concat(e.antCls,"-menu-horizontal"),(a={},(0,w.Z)(a,"".concat(e.antCls,`-menu-item:hover,
          `).concat(e.antCls,`-menu-submenu:hover,
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,color:e.header.colorTextMenuActive}),(0,w.Z)(a,"".concat(e.antCls,`-menu-item-open,
          `).concat(e.antCls,`-menu-submenu-open,
          `).concat(e.antCls,`-menu-item-selected,
          `).concat(e.antCls,"-menu-submenu-selected"),(0,w.Z)({backgroundColor:e.header.colorBgMenuItemSelected,borderRadius:e.radiusBase,color:e.header.colorTextMenuSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:e.header.colorTextMenuSelected})),(0,w.Z)(a,"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),(0,w.Z)(a,"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"}),a)),d)),(0,w.Z)(T,"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(b={},(0,w.Z)(b,"&".concat(e.antCls,"-menu"),(0,w.Z)({color:e.header.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),(0,w.Z)(b,"&".concat(e.antCls,"-menu-light"),(v={},(0,w.Z)(v,"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,w.Z)({color:e.header.colorTextMenuActive,borderRadius:e.radiusBase},"".concat(e.antCls,"-menu-submenu-arrow"),{color:e.header.colorTextMenuActive})),(0,w.Z)(v,"".concat(e.antCls,"-menu-item-selected"),{color:e.header.colorTextMenuActive,fontWeight:"bold",borderRadius:e.radiusBase}),v)),b)),T)),(0,w.Z)(j,"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),(0,w.Z)(j,"".concat(e.antCls,"-menu-submenu-popup"),(D={backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},(0,w.Z)(D,"".concat(e.antCls,"-menu"),(0,w.Z)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),(0,w.Z)(D,"".concat(e.antCls,"-menu-item-selected"),{color:e.sider.colorTextMenuSelected,fontWeight:"bold"}),(0,w.Z)(D,"".concat(e.antCls,"-menu-submenu-selected"),{color:e.sider.colorTextMenuSelected,fontWeight:"bold"}),(0,w.Z)(D,"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(A={},(0,w.Z)(A,"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.radiusBase,color:e.sider.colorTextMenuSelected,fontWeight:"bold"}),(0,w.Z)(A,"".concat(e.antCls,`-menu-item:hover, 
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-title:hover"),(0,w.Z)({color:e.sider.colorTextMenuActive,borderRadius:e.radiusBase},"".concat(e.antCls,"-menu-submenu-arrow"),{color:e.sider.colorTextMenuActive})),A)),D)),j)},Ir=function(e){var n,a;return(0,w.Z)({body:{paddingBlock:0,paddingInline:0,marginBlock:0,marginInline:0,fontFamily:e.fontFamily}},e.proComponentsCls,(a={},(0,w.Z)(a,"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),(0,w.Z)(a,"& ".concat(e.componentCls),(n={},(0,w.Z)(n,"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),(0,w.Z)(n,"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:e.pageContainer.colorBgPageContainer||"transparent",position:"relative","*":{boxSizing:"border-box"},marginBlock:e.pageContainer.marginBlockPageContainerContent,marginInline:e.pageContainer.marginInlinePageContainerContent,"&-has-page-container":{margin:0}}),(0,w.Z)(n,"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0}),(0,w.Z)(n,"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:e.bgLayout}),n)),(0,w.Z)(a,"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}),a))};function gr(s){var e=(0,O.useContext)(Ee);return(0,U.Xj)("pro-layout",function(n){var a=(0,p.Z)((0,p.Z)({},n),{},{componentCls:".".concat(s)},e);return[Ir(a),fr(a)]})}function mr(s){if(!s||s==="/")return["/"];var e=s.split("/").filter(function(n){return n});return e.map(function(n,a){return"/".concat(e.slice(0,a+1).join("/"))})}var Ur=function(e,n,a){var d=e.breadcrumbName,v=e.path,b=a.indexOf(e)===a.length-1;return b?(0,u.jsx)("span",{children:d}):(0,u.jsx)("a",{href:v,children:d})},Tr=function(e,n){var a=n.formatMessage,d=n.menu;return e.locale&&a&&(d==null?void 0:d.locale)!==!1?a({id:e.locale,defaultMessage:e.name}):e.name},va=function(e,n){var a=e.get(n);if(!a){var d=Array.from(e.keys())||[],v=d.find(function(b){return Ka()(b.replace("?","")).test(n)});v&&(a=e.get(v))}return a||{path:""}},kn=function(e){var n=e.location,a=e.breadcrumbMap;return{location:n,breadcrumbMap:a}},qn=function(e,n,a){var d=mr(e==null?void 0:e.pathname),v=d.map(function(b){var T=va(n,b),A=Tr(T,a),D=T.hideInBreadcrumb;return A&&!D?{path:b,breadcrumbName:A,component:T.component}:{path:"",breadcrumbName:""}}).filter(function(b){return b&&b.path});return v},Tn=function(e){var n=kn(e),a=n.location,d=n.breadcrumbMap;return a&&a.pathname&&d?qn(a,d,e):[]},On=function(e,n){var a=e.breadcrumbRender,d=e.itemRender,v=n.breadcrumbProps||{},b=v.minLength,T=b===void 0?2:b,A=Tn(e),D=d||Ur,j=A;return a&&(j=a(j)||[]),(j&&j.length<T||a===!1)&&(j=void 0),{routes:j,itemRender:D}};function gn(s){return(0,Ht.Z)(s).reduce(function(e,n){var a=(0,tt.Z)(n,2),d=a[0],v=a[1];return e[d]=v,e},{})}var ea=function s(e,n,a,d){var v=nt(e,(n==null?void 0:n.locale)||!1,a,!0),b=v.menuData,T=v.breadcrumb;return d?s(d(b),n,a,void 0):{breadcrumb:gn(T),breadcrumbMap:T,menuData:b}},Hn=function(e){var n=(0,O.useState)({}),a=(0,tt.Z)(n,2),d=a[0],v=a[1];return(0,O.useEffect)(function(){v((0,U.Yc)({layout:(0,Vt.Z)(e.layout)!=="object"?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}))},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),d},vi=function(e){var n,a=(0,U.dQ)(),d=a.hashId,v=e.style,b=e.prefixCls,T=e.children,A=e.hasPageContainer,D=Se()("".concat(b,"-content"),d,(n={},(0,w.Z)(n,"".concat(b,"-has-header"),e.hasHeader),(0,w.Z)(n,"".concat(b,"-content-has-page-container"),A),n)),j=e.ErrorBoundary||U.SV;return e.ErrorBoundary===!1?(0,u.jsx)(Ha.Content,{className:D,style:v,children:T}):(0,u.jsx)(j,{children:(0,u.jsx)(Ha.Content,{className:D,style:v,children:T})})},ht=["id","defaultMessage"],Ln=["fixSiderbar","navTheme","layout"],Wr=0,Gr=function(e,n){return e.headerRender===!1||e.pure?null:(0,u.jsx)(Ci,(0,p.Z)({matchMenuKeys:n},e))},on=function(e){return e.footerRender===!1||e.pure?null:e.footerRender?e.footerRender((0,p.Z)({},e),(0,u.jsx)(oa,{})):null},En=function(e,n){var a=e.layout,d=e.navTheme,v=e.isMobile,b=e.selectedKeys,T=e.openKeys,A=e.splitMenus,D=e.menuRender;if(e.menuRender===!1||e.pure)return null;var j=e.menuData;if(A&&(T!==!1||a==="mix")&&!v){var I=b||n,W=(0,tt.Z)(I,1),ee=W[0];if(ee){var ne,ie;j=((ne=e.menuData)===null||ne===void 0||(ie=ne.find(function(Oe){return Oe.key===ee}))===null||ie===void 0?void 0:ie.children)||[]}else j=[]}var le=ma(j||[]);if(le&&(le==null?void 0:le.length)<1&&A)return null;if(a==="top"&&!v)return(0,u.jsx)(zt,(0,p.Z)((0,p.Z)({matchMenuKeys:n},e),{},{hide:!0}));var je=(0,u.jsx)(zt,(0,p.Z)((0,p.Z)({matchMenuKeys:n},e),{},{style:d==="realDark"?{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"}:{},menuData:le}));return D?D(e,je):je},ln=function(e,n){var a=n.pageTitleRender,d=Ea(e);if(a===!1)return{title:n.title||"",id:"",pageName:""};if(a){var v=a(e,d.title,d);if(typeof v=="string")return(0,p.Z)((0,p.Z)({},d),{},{title:v});(0,yo.default)(typeof v=="string","pro-layout: renderPageTitle return value should be a string")}return d},Ga=function(e,n,a){return e?n?60:a:0},Ba=function(e){var n,a,d,v=e||{},b=v.children,T=v.onCollapse,A=v.location,D=A===void 0?{pathname:"/"}:A,j=v.contentStyle,I=v.route,W=v.defaultCollapsed,ee=v.style,ne=v.siderWidth,ie=v.menu,le=v.siderMenuType,je=v.isChildrenLayout,Oe=v.menuDataRender,Ye=v.actionRef,ye=v.bgLayoutImgList,Le=v.formatMessage,Ve=v.loading,at=(0,O.useMemo)(function(){return ne||(e.layout==="mix"?215:256)},[e.layout,ne]),Rt=(0,O.useContext)(We.ZP.ConfigContext),Bt=(n=e.prefixCls)!==null&&n!==void 0?n:Rt.getPrefixCls("pro"),Wt=(0,U.i9)(!1,{value:ie==null?void 0:ie.loading,onChange:ie==null?void 0:ie.onLoadingChange}),pr=(0,tt.Z)(Wt,2),Er=pr[0],$t=pr[1],Hr=(0,O.useState)(function(){return Wr+=1,"pro-layout-".concat(Wr)}),Ge=(0,tt.Z)(Hr,1),Zr=Ge[0],Mn=(0,O.useCallback)(function(jn){var Fi=jn.id,xl=jn.defaultMessage,El=(0,fe.Z)(jn,ht);if(Le)return Le((0,p.Z)({id:Fi,defaultMessage:xl},El));var zo=Qn();return zo[Fi]?zo[Fi]:xl},[Le]),Dn=(0,Fa.ZP)(function(){return(ie==null?void 0:ie.params)?[Zr,ie==null?void 0:ie.params]:[Zr,{}]},function(){var jn=(0,la.Z)((0,nn.Z)().mark(function Fi(xl,El){var zo,Dl;return(0,nn.Z)().wrap(function(Uo){for(;;)switch(Uo.prev=Uo.next){case 0:return $t(!0),Uo.next=3,ie==null||(zo=ie.request)===null||zo===void 0?void 0:zo.call(ie,El,(I==null?void 0:I.children)||(I==null?void 0:I.routes)||[]);case 3:return Dl=Uo.sent,$t(!1),Uo.abrupt("return",Dl);case 6:case"end":return Uo.stop()}},Fi)}));return function(Fi,xl){return jn.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),or=Dn.data,en=Dn.mutate,zn=(0,Fa.kY)(),ta=zn.cache;(0,O.useEffect)(function(){return function(){ta instanceof Map&&ta.clear()}},[]);var ha=(0,O.useMemo)(function(){return ea(or||(I==null?void 0:I.children)||(I==null?void 0:I.routes)||[],ie,Mn,Oe)},[Mn,ie,Oe,or,I==null?void 0:I.children,I==null?void 0:I.routes]),Ra=ha||{},Na=Ra.breadcrumb,Di=Na===void 0?{}:Na,mn=Ra.breadcrumbMap,lr=Ra.menuData,sr=lr===void 0?[]:lr;Ye&&(ie==null?void 0:ie.request)&&(Ye.current={reload:function(){en()}});var Un=(0,O.useMemo)(function(){return fi(D.pathname||"/",sr||[],!0)},[D.pathname,sr]),Ya=(0,O.useMemo)(function(){return Array.from(new Set(Un.map(function(jn){return jn.key||jn.path||""})))},[Un]),Vr=Un[Un.length-1]||{},ji=Hn(Vr),vr=(0,p.Z)((0,p.Z)({},e),ji),Gt=vr.fixSiderbar,yr=vr.navTheme,sn=vr.layout,pn=(0,fe.Z)(vr,Ln),Ia=(0,io.ZP)(),Ho=(Ia==="sm"||Ia==="xs")&&!e.disableMobile,Fl=sn!=="top"&&!Ho,Bl=(0,Gi.default)(function(){return W!==void 0?W:!!(Ho||Ia==="md")},{value:e.collapsed,onChange:T}),Ml=(0,tt.Z)(Bl,2),fl=Ml[0],Pl=Ml[1],_o=(0,Ke.Z)((0,p.Z)((0,p.Z)((0,p.Z)({prefixCls:Bt},e),{},{siderWidth:at},ji),{},{formatMessage:Mn,breadcrumb:Di,menu:(0,p.Z)((0,p.Z)({},ie),{},{type:le||(ie==null?void 0:ie.type),loading:Er}),layout:sn}),["className","style","breadcrumbRender"]),bl=ln((0,p.Z)((0,p.Z)({pathname:D.pathname},_o),{},{breadcrumbMap:mn}),e),Nl=On((0,p.Z)((0,p.Z)({},_o),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:mn}),e),Sl=En((0,p.Z)((0,p.Z)({},_o),{},{menuData:sr,onCollapse:Pl,isMobile:Ho,collapsed:fl}),Ya),Cl=Gr((0,p.Z)((0,p.Z)({},_o),{},{children:null,hasSiderMenu:!!Sl,menuData:sr,isMobile:Ho,collapsed:fl,onCollapse:Pl}),Ya),Rl=on((0,p.Z)({isMobile:Ho,collapsed:fl},_o)),kl=(0,O.useContext)(Ft),Hl=kl.isChildrenLayout,wl=je!==void 0?je:Hl,hi="".concat(Bt,"-layout"),Il=gr(hi),_l=Il.wrapSSR,Tl=Il.hashId,zl=Se()(e.className,Tl,"ant-design-pro",hi,(a={},(0,w.Z)(a,"screen-".concat(Ia),Ia),(0,w.Z)(a,"".concat(hi,"-top-menu"),sn==="top"),(0,w.Z)(a,"".concat(hi,"-is-children"),wl),(0,w.Z)(a,"".concat(hi,"-fix-siderbar"),Gt),(0,w.Z)(a,"".concat(hi,"-").concat(sn),sn),a)),Ul=Ga(!!Fl,fl,at),Zl={position:"relative"};(wl||j&&j.minHeight)&&(Zl.minHeight=0),(0,O.useEffect)(function(){var jn;(jn=e.onPageChange)===null||jn===void 0||jn.call(e,e.location)},[D.pathname,(d=D.pathname)===null||d===void 0?void 0:d.search]);var Wl=(0,O.useState)(!1),Al=(0,tt.Z)(Wl,2),Vl=Al[0],Kl=Al[1],$l=(0,O.useState)(!1),Ol=(0,tt.Z)($l,2),Ll=Ol[0],Gl=Ol[1];(0,U.jr)(bl,e.title||!1);var Yl=(0,O.useMemo)(function(){return ye&&ye.length>0?ye.map(function(jn,Fi){return(0,u.jsx)("img",{src:jn.src,style:(0,p.Z)({position:"absolute"},jn)},Fi)}):null},[ye]);return _l((0,u.jsx)(ai.Provider,{children:(0,u.jsx)(Ft.Provider,{value:(0,p.Z)((0,p.Z)({},_o),{},{breadcrumb:Nl,menuData:sr,isMobile:Ho,collapsed:fl,hasPageContainer:Ll,setHasPageContainer:Gl,isChildrenLayout:!0,title:bl.pageName,hasSiderMenu:!!Sl,hasHeader:!!Cl,siderWidth:Ul,hasFooter:!!Rl,hasFooterToolbar:Vl,setHasFooterToolbar:Kl,pageTitleInfo:bl,matchMenus:Un,matchMenuKeys:Ya,currentMenu:Vr}),children:e.pure?(0,u.jsx)(u.Fragment,{children:b}):(0,u.jsxs)("div",{className:zl,children:[(0,u.jsx)("div",{className:"".concat(hi,"-bg-list ").concat(Tl),children:Yl}),(0,u.jsxs)(Ha,{style:(0,p.Z)({minHeight:"100%"},ee),children:[Sl,(0,u.jsxs)("div",{style:Zl,className:"".concat(hi,"-container ").concat(Tl),children:[Cl,(0,u.jsx)(vi,(0,p.Z)((0,p.Z)({hasPageContainer:Ll,isChildrenLayout:wl},pn),{},{hasHeader:!!Cl,prefixCls:hi,style:j,children:Ve?(0,u.jsx)(Wn,{}):b})),Rl]})]})]})})}))};Ba.defaultProps=(0,p.Z)((0,p.Z)({logo:(0,u.jsx)(ko,{})},Sn),{},{location:(0,U.jU)()?window.location:void 0});var _n=function(e){var n,a=e.colorPrimary;return(0,u.jsx)(We.ZP,{theme:{hashed:((n="production")===null||n===void 0?void 0:n.toLowerCase())!=="test",token:{radiusBase:4,colorPrimary:a||"#1677FF",colorError:"#ff4d4f",colorInfo:"#1677FF"}},children:(0,u.jsx)(er.oK,{autoClearCache:!0,children:(0,u.jsx)(yt,{token:e.token,children:(0,u.jsx)(Ba,(0,p.Z)({},e))})})})},jl=_n},78775:function(Ct,ve,m){"use strict";m.d(ve,{oK:function(){return V},ZP:function(){return Ce},Go:function(){return pt},YB:function(){return Ae}});var P=m(28991),M=m(11965),G=m(85893),Y=m(68370),H=m(88182),oe=m(84378),X=m(67294),B=m(29405),J={moneySymbol:"$",form:{lightFilter:{more:"\u0627\u0644\u0645\u0632\u064A\u062F",clear:"\u0646\u0638\u0641",confirm:"\u062A\u0623\u0643\u064A\u062F",itemUnit:"\u0639\u0646\u0627\u0635\u0631"}},tableForm:{search:"\u0627\u0628\u062D\u062B",reset:"\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",submit:"\u0627\u0631\u0633\u0627\u0644",collapsed:"\u0645\u064F\u0642\u0644\u0635",expand:"\u0645\u064F\u0648\u0633\u0639",inputPlaceholder:"\u0627\u0644\u0631\u062C\u0627\u0621 \u0627\u0644\u0625\u062F\u062E\u0627\u0644",selectPlaceholder:"\u0627\u0644\u0631\u062C\u0627\u0621 \u0627\u0644\u0625\u062E\u062A\u064A\u0627\u0631"},alert:{clear:"\u0646\u0638\u0641",selected:"\u0645\u062D\u062F\u062F",item:"\u0639\u0646\u0635\u0631"},pagination:{total:{range:" ",total:"\u0645\u0646",item:"\u0639\u0646\u0627\u0635\u0631"}},tableToolBar:{leftPin:"\u062B\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",rightPin:"\u062B\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",noPin:"\u0627\u0644\u063A\u0627\u0621 \u0627\u0644\u062A\u062B\u0628\u064A\u062A",leftFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",rightFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",noFixedTitle:"\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0625\u0644\u0635\u0627\u0642",reset:"\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",columnDisplay:"\u0627\u0644\u0623\u0639\u0645\u062F\u0629 \u0627\u0644\u0645\u0639\u0631\u0648\u0636\u0629",columnSetting:"\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A",fullScreen:"\u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",exitFullScreen:"\u0627\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",reload:"\u062A\u062D\u062F\u064A\u062B",density:"\u0627\u0644\u0643\u062B\u0627\u0641\u0629",densityDefault:"\u0627\u0641\u062A\u0631\u0627\u0636\u064A",densityLarger:"\u0623\u0643\u0628\u0631",densityMiddle:"\u0648\u0633\u0637",densitySmall:"\u0645\u062F\u0645\u062C"},stepsForm:{next:"\u0627\u0644\u062A\u0627\u0644\u064A",prev:"\u0627\u0644\u0633\u0627\u0628\u0642",submit:"\u0623\u0646\u0647\u0649"},loginForm:{submitText:"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"},editableTable:{action:{save:"\u0623\u0646\u0642\u0630",cancel:"\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0623\u0645\u0631",delete:"\u062D\u0630\u0641",add:"\u0625\u0636\u0627\u0641\u0629 \u0635\u0641 \u0645\u0646 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A"}},switch:{open:"\u0645\u0641\u062A\u0648\u062D",close:"\u063A\u0644\u0642"}},we={moneySymbol:"\u20AC",form:{lightFilter:{more:"M\xE1is",clear:"Limpar",confirm:"Confirmar",itemUnit:"Elementos"}},tableForm:{search:"Cercar",reset:"Netejar",submit:"Enviar",collapsed:"Expandir",expand:"Col\xB7lapsar",inputPlaceholder:"Introdu\xEFu valor",selectPlaceholder:"Seleccioneu valor"},alert:{clear:"Netejar",selected:"Seleccionat",item:"Article"},pagination:{total:{range:" ",total:"de",item:"articles"}},tableToolBar:{leftPin:"Pin a l'esquerra",rightPin:"Pin a la dreta",noPin:"Sense Pin",leftFixedTitle:"Fixat a l'esquerra",rightFixedTitle:"Fixat a la dreta",noFixedTitle:"Sense fixar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xF3",fullScreen:"Pantalla Completa",exitFullScreen:"Sortir Pantalla Completa",reload:"Refrescar",density:"Densitat",densityDefault:"Per Defecte",densityLarger:"Llarg",densityMiddle:"Mitj\xE0",densitySmall:"Compacte"},stepsForm:{next:"Seg\xFCent",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Gardar",cancel:"Cancelar",delete:"Eliminar",add:"engadir unha fila de datos"}},switch:{open:"aberto",close:"pechar"}},be={moneySymbol:"\u20AC",form:{lightFilter:{more:"Mehr",clear:"Zur\xFCcksetzen",confirm:"Best\xE4tigen",itemUnit:"Eintr\xE4ge"}},tableForm:{search:"Suchen",reset:"Zur\xFCcksetzen",submit:"Absenden",collapsed:"Zeige mehr",expand:"Zeige weniger",inputPlaceholder:"Bitte eingeben",selectPlaceholder:"Bitte ausw\xE4hlen"},alert:{clear:"Zur\xFCcksetzen",selected:"Ausgew\xE4hlt",item:"Eintrag"},pagination:{total:{range:" ",total:"von",item:"Eintr\xE4gen"}},tableToolBar:{leftPin:"Links anheften",rightPin:"Rechts anheften",noPin:"Nicht angeheftet",leftFixedTitle:"Links fixiert",rightFixedTitle:"Rechts fixiert",noFixedTitle:"Nicht fixiert",reset:"Zur\xFCcksetzen",columnDisplay:"Angezeigte Reihen",columnSetting:"Einstellungen",fullScreen:"Vollbild",exitFullScreen:"Vollbild verlassen",reload:"Aktualisieren",density:"Abstand",densityDefault:"Standard",densityLarger:"Gr\xF6\xDFer",densityMiddle:"Mittel",densitySmall:"Kompakt"},stepsForm:{next:"Weiter",prev:"Zur\xFCck",submit:"Abschlie\xDFen"},loginForm:{submitText:"Anmelden"},editableTable:{action:{save:"Retten",cancel:"Abbrechen",delete:"L\xF6schen",add:"Hinzuf\xFCgen einer Datenzeile"}},switch:{open:"offen",close:"schlie\xDFen"}},qe={moneySymbol:"\xA3",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed the left",rightFixedTitle:"Fixed the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},se={moneySymbol:"$",deleteThisLine:"Delete this line",copyThisLine:"Copy this line",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed the left",rightFixedTitle:"Fixed the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},Qe={moneySymbol:"\u20AC",form:{lightFilter:{more:"M\xE1s",clear:"Limpiar",confirm:"Confirmar",itemUnit:"art\xEDculos"}},tableForm:{search:"Buscar",reset:"Limpiar",submit:"Submit",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Ingrese valor",selectPlaceholder:"Seleccione valor"},alert:{clear:"Limpiar",selected:"Seleccionado",item:"Articulo"},pagination:{total:{range:" ",total:"de",item:"art\xEDculos"}},tableToolBar:{leftPin:"Pin a la izquierda",rightPin:"Pin a la derecha",noPin:"Sin Pin",leftFixedTitle:"Fijado a la izquierda",rightFixedTitle:"Fijado a la derecha",noFixedTitle:"Sin Fijar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xF3n",fullScreen:"Pantalla Completa",exitFullScreen:"Salir Pantalla Completa",reload:"Refrescar",density:"Densidad",densityDefault:"Por Defecto",densityLarger:"Largo",densityMiddle:"Medio",densitySmall:"Compacto"},stepsForm:{next:"Siguiente",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Descartar",delete:"Borrar",add:"a\xF1adir una fila de datos"}},switch:{open:"abrir",close:"cerrar"}},He={moneySymbol:"\u062A\u0648\u0645\u0627\u0646",form:{lightFilter:{more:"\u0628\u06CC\u0634\u062A\u0631",clear:"\u067E\u0627\u06A9 \u06A9\u0631\u062F\u0646",confirm:"\u062A\u0627\u06CC\u06CC\u062F",itemUnit:"\u0645\u0648\u0631\u062F"}},tableForm:{search:"\u062C\u0633\u062A\u062C\u0648",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06CC",submit:"\u062A\u0627\u06CC\u06CC\u062F",collapsed:"\u0646\u0645\u0627\u06CC\u0634 \u0628\u06CC\u0634\u062A\u0631",expand:"\u0646\u0645\u0627\u06CC\u0634 \u06A9\u0645\u062A\u0631",inputPlaceholder:"\u067E\u06CC\u062F\u0627 \u06A9\u0646\u06CC\u062F",selectPlaceholder:"\u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F"},alert:{clear:"\u067E\u0627\u06A9 \u0633\u0627\u0632\u06CC",selected:"\u0627\u0646\u062A\u062E\u0627\u0628",item:"\u0645\u0648\u0631\u062F"},pagination:{total:{range:" ",total:"\u0627\u0632",item:"\u0645\u0648\u0631\u062F"}},tableToolBar:{leftPin:"\u0633\u0646\u062C\u0627\u0642 \u0628\u0647 \u0686\u067E",rightPin:"\u0633\u0646\u062C\u0627\u0642 \u0628\u0647 \u0631\u0627\u0633\u062A",noPin:"\u0633\u0646\u062C\u0627\u0642 \u0646\u0634\u062F\u0647",leftFixedTitle:"\u062B\u0627\u0628\u062A \u0634\u062F\u0647 \u062F\u0631 \u0686\u067E",rightFixedTitle:"\u062B\u0627\u0628\u062A \u0634\u062F\u0647 \u062F\u0631 \u0631\u0627\u0633\u062A",noFixedTitle:"\u0634\u0646\u0627\u0648\u0631",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06CC",columnDisplay:"\u0646\u0645\u0627\u06CC\u0634 \u0647\u0645\u0647",columnSetting:"\u062A\u0646\u0638\u06CC\u0645\u0627\u062A",fullScreen:"\u062A\u0645\u0627\u0645 \u0635\u0641\u062D\u0647",exitFullScreen:"\u062E\u0631\u0648\u062C \u0627\u0632 \u062D\u0627\u0644\u062A \u062A\u0645\u0627\u0645 \u0635\u0641\u062D\u0647",reload:"\u062A\u0627\u0632\u0647 \u0633\u0627\u0632\u06CC",density:"\u062A\u0631\u0627\u06A9\u0645",densityDefault:"\u067E\u06CC\u0634 \u0641\u0631\u0636",densityLarger:"\u0628\u0632\u0631\u06AF",densityMiddle:"\u0645\u062A\u0648\u0633\u0637",densitySmall:"\u06A9\u0648\u0686\u06A9"},stepsForm:{next:"\u0628\u0639\u062F\u06CC",prev:"\u0642\u0628\u0644\u06CC",submit:"\u0627\u062A\u0645\u0627\u0645"},loginForm:{submitText:"\u0648\u0631\u0648\u062F"},editableTable:{action:{save:"\u0630\u062E\u06CC\u0631\u0647",cancel:"\u0644\u063A\u0648",delete:"\u062D\u0630\u0641",add:"\u06CC\u06A9 \u0631\u062F\u06CC\u0641 \u062F\u0627\u062F\u0647 \u0627\u0636\u0627\u0641\u0647 \u06A9\u0646\u06CC\u062F"}},switch:{open:"\u0628\u0627\u0632",close:"\u0646\u0632\u062F\u06CC\u06A9"}},At={moneySymbol:"\u20AC",form:{lightFilter:{more:"Plus",clear:"Effacer",confirm:"Confirmer",itemUnit:"Items"}},tableForm:{search:"Rechercher",reset:"R\xE9initialiser",submit:"Envoyer",collapsed:"Agrandir",expand:"R\xE9duire",inputPlaceholder:"Entrer une valeur",selectPlaceholder:"S\xE9lectionner une valeur"},alert:{clear:"R\xE9initialiser",selected:"S\xE9lectionn\xE9",item:"Item"},pagination:{total:{range:" ",total:"sur",item:"\xE9l\xE9ments"}},tableToolBar:{leftPin:"\xC9pingler \xE0 gauche",rightPin:"\xC9pingler \xE0 gauche",noPin:"Sans \xE9pingle",leftFixedTitle:"Fixer \xE0 gauche",rightFixedTitle:"Fixer \xE0 droite",noFixedTitle:"Non fix\xE9",reset:"R\xE9initialiser",columnDisplay:"Affichage colonne",columnSetting:"R\xE9glages",fullScreen:"Plein \xE9cran",exitFullScreen:"Quitter Plein \xE9cran",reload:"Rafraichir",density:"Densit\xE9",densityDefault:"Par d\xE9faut",densityLarger:"Larger",densityMiddle:"Moyenne",densitySmall:"Compacte"},stepsForm:{next:"Suivante",prev:"Pr\xE9c\xE9dente",submit:"Finaliser"},loginForm:{submitText:"Se connecter"},editableTable:{action:{save:"Sauvegarder",cancel:"Annuler",delete:"Supprimer",add:"ajouter une ligne de donn\xE9es"}},switch:{open:"ouvert",close:"pr\xE8s"}},ot={moneySymbol:"kn",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010Disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pretra\u017Ei",reset:"Poni\u0161ti",submit:"Potvrdi",collapsed:"Ra\u0161iri",expand:"Skupi",inputPlaceholder:"Unesite",selectPlaceholder:"Odaberite"},alert:{clear:"O\u010Disti",selected:"Odaberi",item:"stavke"},pagination:{total:{range:" ",total:"od",item:"stavke"}},tableToolBar:{leftPin:"Prika\u010Di lijevo",rightPin:"Prika\u010Di desno",noPin:"Bez prika\u010Denja",leftFixedTitle:"Fiksiraj lijevo",rightFixedTitle:"Fiksiraj desno",noFixedTitle:"Bez fiksiranja",reset:"Resetiraj",columnDisplay:"Prikaz stupaca",columnSetting:"Postavke",fullScreen:"Puni zaslon",exitFullScreen:"Iza\u0111i iz punog zaslona",reload:"Ponovno u\u010Ditaj",density:"Veli\u010Dina",densityDefault:"Zadano",densityLarger:"Veliko",densityMiddle:"Srednje",densitySmall:"Malo"},stepsForm:{next:"Sljede\u0107i",prev:"Prethodni",submit:"Kraj"},loginForm:{submitText:"Prijava"},editableTable:{action:{save:"Spremi",cancel:"Odustani",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"otvori",close:"zatvori"}},Et={moneySymbol:"RP",form:{lightFilter:{more:"Lebih",clear:"Hapus",confirm:"Konfirmasi",itemUnit:"Unit"}},tableForm:{search:"Cari",reset:"Atur ulang",submit:"Kirim",collapsed:"Lebih sedikit",expand:"Lebih banyak",inputPlaceholder:"Masukkan pencarian",selectPlaceholder:"Pilih"},alert:{clear:"Hapus",selected:"Dipilih",item:"Butir"},pagination:{total:{range:" ",total:"Dari",item:"Butir"}},tableToolBar:{leftPin:"Pin kiri",rightPin:"Pin kanan",noPin:"Tidak ada pin",leftFixedTitle:"Rata kiri",rightFixedTitle:"Rata kanan",noFixedTitle:"Tidak tetap",reset:"Atur ulang",columnDisplay:"Tampilan kolom",columnSetting:"Pengaturan",fullScreen:"Layar penuh",exitFullScreen:"Keluar layar penuh",reload:"Atur ulang",density:"Kerapatan",densityDefault:"Standar",densityLarger:"Lebih besar",densityMiddle:"Sedang",densitySmall:"Rapat"},stepsForm:{next:"Selanjutnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Login"},editableTable:{action:{save:"simpan",cancel:"batal",delete:"hapus",add:"Tambahkan baris data"}},switch:{open:"buka",close:"tutup"}},p={moneySymbol:"\u20AC",form:{lightFilter:{more:"pi\xF9",clear:"pulisci",confirm:"conferma",itemUnit:"elementi"}},tableForm:{search:"Filtra",reset:"Pulisci",submit:"Invia",collapsed:"Espandi",expand:"Contrai",inputPlaceholder:"Digita",selectPlaceholder:"Seleziona"},alert:{clear:"Rimuovi",selected:"Selezionati",item:"elementi"},pagination:{total:{range:" ",total:"di",item:"elementi"}},tableToolBar:{leftPin:"Fissa a sinistra",rightPin:"Fissa a destra",noPin:"Ripristina posizione",leftFixedTitle:"Fissato a sinistra",rightFixedTitle:"Fissato a destra",noFixedTitle:"Non fissato",reset:"Ripristina",columnDisplay:"Disposizione colonne",columnSetting:"Impostazioni",fullScreen:"Modalit\xE0 schermo intero",exitFullScreen:"Esci da modalit\xE0 schermo intero",reload:"Ricarica",density:"Grandezza tabella",densityDefault:"predefinito",densityLarger:"Grande",densityMiddle:"Media",densitySmall:"Compatta"},stepsForm:{next:"successivo",prev:"precedente",submit:"finisci"},loginForm:{submitText:"Accedi"},editableTable:{action:{save:"salva",cancel:"annulla",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"chiudi"}},fe={moneySymbol:"\xA5",form:{lightFilter:{more:"\u3082\u3063\u3068",clear:"\u660E\u78BA",confirm:"\u78BA\u8A8D",itemUnit:"\u9805\u76EE"}},tableForm:{search:"\u691C\u7D22",reset:"\u30EA\u30BB\u30C3\u30C8",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u958B",expand:"\u53CE\u7D0D",inputPlaceholder:"\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",selectPlaceholder:"\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044"},alert:{clear:"\u30AF\u30EA\u30A2",selected:"\u9078\u629E\u3057\u305F",item:"\u9805\u76EE"},pagination:{total:{range:"\u8A18\u4E8B",total:"/\u5408\u8A08",item:" "}},tableToolBar:{leftPin:"\u5DE6\u306B\u56FA\u5B9A",rightPin:"\u53F3\u306B\u56FA\u5B9A",noPin:"\u30AD\u30E3\u30F3\u30BB\u30EB",leftFixedTitle:"\u5DE6\u306B\u56FA\u5B9A\u3055\u308C\u305F\u9805\u76EE",rightFixedTitle:"\u53F3\u306B\u56FA\u5B9A\u3055\u308C\u305F\u9805\u76EE",noFixedTitle:"\u56FA\u5B9A\u3055\u308C\u3066\u306A\u3044\u9805\u76EE",reset:"\u30EA\u30BB\u30C3\u30C8",columnDisplay:"\u8868\u793A\u5217",columnSetting:"\u5217\u8868\u793A\u8A2D\u5B9A",fullScreen:"\u30D5\u30EB\u30B9\u30AF\u30EA\u30FC\u30F3",exitFullScreen:"\u7D42\u4E86",reload:"\u66F4\u65B0",density:"\u884C\u9AD8",densityDefault:"\u30C7\u30D5\u30A9\u30EB\u30C8",densityLarger:"\u9ED8\u8BA4",densityMiddle:"\u4E2D",densitySmall:"\u5C0F"},stepsForm:{next:"\u6B21\u306E\u30B9\u30C6\u30C3\u30D7",prev:"\u524D",submit:"\u9001\u4FE1"},loginForm:{submitText:"\u30ED\u30B0\u30A4\u30F3"},editableTable:{action:{save:"\u6551\u3046",cancel:"\u30AD\u30E3\u30F3\u30BB\u30EB",delete:"\u524A\u9664",add:"1\u884C\u306E\u30C7\u30FC\u30BF\u3092\u8FFD\u52A0\u3057\u307E\u3059"}},switch:{open:"\u30AA\u30FC\u30D7\u30F3",close:"\u8FD1\u3044"}},u={moneySymbol:"\u20A9",form:{lightFilter:{more:"\uB354\uBCF4\uAE30",clear:"\uCDE8\uC18C",confirm:"\uD655\uC778",itemUnit:"\uAC74\uC218"}},tableForm:{search:"\uC870\uD68C",reset:"\uCD08\uAE30\uD654",submit:"\uC81C\uCD9C",collapsed:"\uD655\uC7A5",expand:"\uB2EB\uAE30",inputPlaceholder:"\uC785\uB825\uD574 \uC8FC\uC138\uC694",selectPlaceholder:"\uC120\uD0DD\uD574 \uC8FC\uC138\uC694"},alert:{clear:"\uCDE8\uC18C",selected:"\uC120\uD0DD",item:"\uAC74"},pagination:{total:{range:" ",total:"/ \uCD1D",item:"\uAC74"}},tableToolBar:{leftPin:"\uC67C\uCABD\uC73C\uB85C \uD540",rightPin:"\uC624\uB978\uCABD\uC73C\uB85C \uD540",noPin:"\uD540 \uC81C\uAC70",leftFixedTitle:"\uC67C\uCABD\uC73C\uB85C \uACE0\uC815",rightFixedTitle:"\uC624\uB978\uCABD\uC73C\uB85C \uACE0\uC815",noFixedTitle:"\uBE44\uACE0\uC815",reset:"\uCD08\uAE30\uD654",columnDisplay:"\uCEEC\uB7FC \uD45C\uC2DC",columnSetting:"\uC124\uC815",fullScreen:"\uC804\uCCB4 \uD654\uBA74",exitFullScreen:"\uC804\uCCB4 \uD654\uBA74 \uCDE8\uC18C",reload:"\uB2E4\uC2DC \uC77D\uAE30",density:"\uC5EC\uBC31",densityDefault:"\uAE30\uBCF8",densityLarger:"\uB9CE\uC740 \uC5EC\uBC31",densityMiddle:"\uC911\uAC04 \uC5EC\uBC31",densitySmall:"\uC881\uC740 \uC5EC\uBC31"},stepsForm:{next:"\uB2E4\uC74C",prev:"\uC774\uC804",submit:"\uC885\uB8CC"},loginForm:{submitText:"\uB85C\uADF8\uC778"},editableTable:{action:{save:"\uC800\uC7A5",cancel:"\uCDE8\uC18C",delete:"\uC0AD\uC81C",add:"\uB370\uC774\uD130 \uD589 \uCD94\uAC00"}},switch:{open:"\uC5F4",close:"\uAC00\uAE4C \uC6B4"}},U={moneySymbol:"\u20AE",form:{lightFilter:{more:"\u0418\u043B\u04AF\u04AF",clear:"\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",confirm:"\u0411\u0430\u0442\u0430\u043B\u0433\u0430\u0430\u0436\u0443\u0443\u043B\u0430\u0445",itemUnit:"\u041D\u044D\u0433\u0436\u04AF\u04AF\u0434"}},tableForm:{search:"\u0425\u0430\u0439\u0445",reset:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",submit:"\u0418\u043B\u0433\u044D\u044D\u0445",collapsed:"\u04E8\u0440\u0433\u04E9\u0442\u0433\u04E9\u0445",expand:"\u0425\u0443\u0440\u0430\u0430\u0445",inputPlaceholder:"\u0423\u0442\u0433\u0430 \u043E\u0440\u0443\u0443\u043B\u043D\u0430 \u0443\u0443",selectPlaceholder:"\u0423\u0442\u0433\u0430 \u0441\u043E\u043D\u0433\u043E\u043D\u043E \u0443\u0443"},alert:{clear:"\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",selected:"\u0421\u043E\u043D\u0433\u043E\u0433\u0434\u0441\u043E\u043D",item:"\u041D\u044D\u0433\u0436"},pagination:{total:{range:" ",total:"\u041D\u0438\u0439\u0442",item:"\u043C\u04E9\u0440"}},tableToolBar:{leftPin:"\u0417\u04AF\u04AF\u043D \u0442\u0438\u0439\u0448 \u0431\u044D\u0445\u043B\u044D\u0445",rightPin:"\u0411\u0430\u0440\u0443\u0443\u043D \u0442\u0438\u0439\u0448 \u0431\u044D\u0445\u043B\u044D\u0445",noPin:"\u0411\u044D\u0445\u043B\u044D\u0445\u0433\u04AF\u0439",leftFixedTitle:"\u0417\u04AF\u04AF\u043D \u0437\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445",rightFixedTitle:"\u0411\u0430\u0440\u0443\u0443\u043D \u0437\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445",noFixedTitle:"\u0417\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445\u0433\u04AF\u0439",reset:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",columnDisplay:"\u0411\u0430\u0433\u0430\u043D\u0430\u0430\u0440 \u0445\u0430\u0440\u0443\u0443\u043B\u0430\u0445",columnSetting:"\u0422\u043E\u0445\u0438\u0440\u0433\u043E\u043E",fullScreen:"\u0411\u04AF\u0442\u044D\u043D \u0434\u044D\u043B\u0433\u044D\u0446\u044D\u044D\u0440",exitFullScreen:"\u0411\u04AF\u0442\u044D\u043D \u0434\u044D\u043B\u0433\u044D\u0446 \u0446\u0443\u0446\u043B\u0430\u0445",reload:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",density:"\u0425\u044D\u043C\u0436\u044D\u044D",densityDefault:"\u0425\u044D\u0432\u0438\u0439\u043D",densityLarger:"\u0422\u043E\u043C",densityMiddle:"\u0414\u0443\u043D\u0434",densitySmall:"\u0416\u0438\u0436\u0438\u0433"},stepsForm:{next:"\u0414\u0430\u0440\u0430\u0430\u0445",prev:"\u04E8\u043C\u043D\u04E9\u0445",submit:"\u0414\u0443\u0443\u0441\u0433\u0430\u0445"},loginForm:{submitText:"\u041D\u044D\u0432\u0442\u0440\u044D\u0445"},editableTable:{action:{save:"\u0425\u0430\u0434\u0433\u0430\u043B\u0430\u0445",cancel:"\u0426\u0443\u0446\u043B\u0430\u0445",delete:"\u0423\u0441\u0442\u0433\u0430\u0445",add:"\u041C\u04E9\u0440 \u043D\u044D\u043C\u044D\u0445"}},switch:{open:"\u041D\u044D\u044D\u0445",close:"\u0425\u0430\u0430\u0445"}},We={moneySymbol:"RM",form:{lightFilter:{more:"Lebih banyak",clear:"Jelas",confirm:"Mengesahkan",itemUnit:"Item"}},tableForm:{search:"Cari",reset:"Menetapkan semula",submit:"Hantar",collapsed:"Kembang",expand:"Kuncup",inputPlaceholder:"Sila masuk",selectPlaceholder:"Sila pilih"},alert:{clear:"Padam",selected:"Dipilih",item:"Item"},pagination:{total:{range:" ",total:"daripada",item:"item"}},tableToolBar:{leftPin:"Pin ke kiri",rightPin:"Pin ke kanan",noPin:"Tidak pin",leftFixedTitle:"Tetap ke kiri",rightFixedTitle:"Tetap ke kanan",noFixedTitle:"Tidak Tetap",reset:"Menetapkan semula",columnDisplay:"Lajur",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Keluar Full Screen",reload:"Muat Semula",density:"Densiti",densityDefault:"Biasa",densityLarger:"Besar",densityMiddle:"Tengah",densitySmall:"Kecil"},stepsForm:{next:"Seterusnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Log Masuk"},editableTable:{action:{save:"Simpan",cancel:"Membatalkan",delete:"Menghapuskan",add:"tambah baris data"}},switch:{open:"Terbuka",close:"Tutup"}},Nt={moneySymbol:"z\u0142",form:{lightFilter:{more:"Wi\u0119cej",clear:"Wyczy\u015B\u0107",confirm:"Potwierd\u017A",itemUnit:"Ilo\u015B\u0107"}},tableForm:{search:"Szukaj",reset:"Reset",submit:"Zatwierd\u017A",collapsed:"Poka\u017C wiecej",expand:"Poka\u017C mniej",inputPlaceholder:"Prosz\u0119 poda\u0107",selectPlaceholder:"Prosz\u0119 wybra\u0107"},alert:{clear:"Wyczy\u015B\u0107",selected:"Wybrane",item:"Wpis"},pagination:{total:{range:" ",total:"z",item:"Wpis\xF3w"}},tableToolBar:{leftPin:"Przypnij do lewej",rightPin:"Przypnij do prawej",noPin:"Odepnij",leftFixedTitle:"Przypi\u0119te do lewej",rightFixedTitle:"Przypi\u0119te do prawej",noFixedTitle:"Nieprzypi\u0119te",reset:"Reset",columnDisplay:"Wy\u015Bwietlane wiersze",columnSetting:"Ustawienia",fullScreen:"Pe\u0142en ekran",exitFullScreen:"Zamknij pe\u0142en ekran",reload:"Od\u015Bwie\u017C",density:"Odst\u0119p",densityDefault:"Standard",densityLarger:"Wiekszy",densityMiddle:"Sredni",densitySmall:"Kompaktowy"},stepsForm:{next:"Weiter",prev:"Zur\xFCck",submit:"Abschlie\xDFen"},loginForm:{submitText:"Zaloguj si\u0119"},editableTable:{action:{save:"Zapisa\u0107",cancel:"Anuluj",delete:"Usun\u0105\u0107",add:"dodawanie wiersza danych"}},switch:{open:"otwiera\u0107",close:"zamyka\u0107"}},Se={moneySymbol:"R$",form:{lightFilter:{more:"Mais",clear:"Limpar",confirm:"Confirmar",itemUnit:"Itens"}},tableForm:{search:"Filtrar",reset:"Limpar",submit:"Confirmar",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Por favor insira",selectPlaceholder:"Por favor selecione"},alert:{clear:"Limpar",selected:"Selecionado(s)",item:"Item(s)"},pagination:{total:{range:" ",total:"de",item:"itens"}},tableToolBar:{leftPin:"Fixar \xE0 esquerda",rightPin:"Fixar \xE0 direita",noPin:"Desfixado",leftFixedTitle:"Fixado \xE0 esquerda",rightFixedTitle:"Fixado \xE0 direita",noFixedTitle:"N\xE3o fixado",reset:"Limpar",columnDisplay:"Mostrar Coluna",columnSetting:"Configura\xE7\xF5es",fullScreen:"Tela Cheia",exitFullScreen:"Sair da Tela Cheia",reload:"Atualizar",density:"Densidade",densityDefault:"Padr\xE3o",densityLarger:"Largo",densityMiddle:"M\xE9dio",densitySmall:"Compacto"},stepsForm:{next:"Pr\xF3ximo",prev:"Anterior",submit:"Enviar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Salvar",cancel:"Cancelar",delete:"Apagar",add:"adicionar uma linha de dados"}},switch:{open:"abrir",close:"fechar"}},Ke={moneySymbol:"\u20BD",form:{lightFilter:{more:"\u0415\u0449\u0435",clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",confirm:"\u041E\u041A",itemUnit:"\u041F\u043E\u0437\u0438\u0446\u0438\u0438"}},tableForm:{search:"\u041D\u0430\u0439\u0442\u0438",reset:"\u0421\u0431\u0440\u043E\u0441",submit:"\u041E\u0442\u043F\u0440\u0430\u0432\u0438\u0442\u044C",collapsed:"\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C",expand:"\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C",inputPlaceholder:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435",selectPlaceholder:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435"},alert:{clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",selected:"\u0412\u044B\u0431\u0440\u0430\u043D\u043E",item:"\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432"},pagination:{total:{range:" ",total:"\u0438\u0437",item:"\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432"}},tableToolBar:{leftPin:"\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0441\u043B\u0435\u0432\u0430",rightPin:"\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0441\u043F\u0440\u0430\u0432\u0430",noPin:"\u041E\u0442\u043A\u0440\u0435\u043F\u0438\u0442\u044C",leftFixedTitle:"\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E \u0441\u043B\u0435\u0432\u0430",rightFixedTitle:"\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E \u0441\u043F\u0440\u0430\u0432\u0430",noFixedTitle:"\u041D\u0435 \u0437\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E",reset:"\u0421\u0431\u0440\u043E\u0441",columnDisplay:"\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u0441\u0442\u043E\u043B\u0431\u0446\u0430",columnSetting:"\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",fullScreen:"\u041F\u043E\u043B\u043D\u044B\u0439 \u044D\u043A\u0440\u0430\u043D",exitFullScreen:"\u0412\u044B\u0439\u0442\u0438 \u0438\u0437 \u043F\u043E\u043B\u043D\u043E\u044D\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0430",reload:"\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C",density:"\u0420\u0430\u0437\u043C\u0435\u0440",densityDefault:"\u041F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",densityLarger:"\u0411\u043E\u043B\u044C\u0448\u043E\u0439",densityMiddle:"\u0421\u0440\u0435\u0434\u043D\u0438\u0439",densitySmall:"\u0421\u0436\u0430\u0442\u044B\u0439"},stepsForm:{next:"\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439",prev:"\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0438\u0439",submit:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044C"},loginForm:{submitText:"\u0412\u0445\u043E\u0434"},editableTable:{action:{save:"\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C",delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",add:"\u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0440\u044F\u0434 \u0434\u0430\u043D\u043D\u044B\u0445"}},switch:{open:"\u041E\u0442\u043A\u0440\u044B\u0442\u044B\u0439 \u0447\u0435\u043C\u043F\u0438\u043E\u043D\u0430\u0442 \u043C\u0438\u0440\u0430 \u043F\u043E \u0442\u0435\u043D\u043D\u0438\u0441\u0443",close:"\u041F\u043E \u0430\u0434\u0440\u0435\u0441\u0443:"}},O={moneySymbol:"RSD",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010Disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Prona\u0111i",reset:"Resetuj",submit:"Po\u0161alji",collapsed:"Pro\u0161iri",expand:"Skupi",inputPlaceholder:"Molimo unesite",selectPlaceholder:"Molimo odaberite"},alert:{clear:"O\u010Disti",selected:"Odabrano",item:"Stavka"},pagination:{total:{range:" ",total:"od",item:"stavki"}},tableToolBar:{leftPin:"Zaka\u010Di levo",rightPin:"Zaka\u010Di desno",noPin:"Nije zaka\u010Deno",leftFixedTitle:"Fiksirano levo",rightFixedTitle:"Fiksirano desno",noFixedTitle:"Nije fiksirano",reset:"Resetuj",columnDisplay:"Prikaz kolona",columnSetting:"Pode\u0161avanja",fullScreen:"Pun ekran",exitFullScreen:"Zatvori pun ekran",reload:"Osve\u017Ei",density:"Veli\u010Dina",densityDefault:"Podrazumevana",densityLarger:"Ve\u0107a",densityMiddle:"Srednja",densitySmall:"Kompaktna"},stepsForm:{next:"Dalje",prev:"Nazad",submit:"Gotovo"},loginForm:{submitText:"Prijavi se"},editableTable:{action:{save:"Sa\u010Duvaj",cancel:"Poni\u0161ti",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"\u041E\u0442\u0432\u043E\u0440\u0438\u0442\u0435",close:"\u0417\u0430\u0442\u0432\u043E\u0440\u0438\u0442\u0435"}},wt={moneySymbol:"\u20BA",form:{lightFilter:{more:"Daha Fazla",clear:"Temizle",confirm:"Onayla",itemUnit:"\xD6\u011Feler"}},tableForm:{search:"Filtrele",reset:"S\u0131f\u0131rla",submit:"G\xF6nder",collapsed:"Daha fazla",expand:"Daha az",inputPlaceholder:"Filtrelemek i\xE7in bir de\u011Fer girin",selectPlaceholder:"Filtrelemek i\xE7in bir de\u011Fer se\xE7in"},alert:{clear:"Temizle",selected:"Se\xE7ili",item:"\xD6\u011Fe"},pagination:{total:{range:" ",total:"Toplam",item:"\xD6\u011Fe"}},tableToolBar:{leftPin:"Sola sabitle",rightPin:"Sa\u011Fa sabitle",noPin:"Sabitlemeyi kald\u0131r",leftFixedTitle:"Sola sabitlendi",rightFixedTitle:"Sa\u011Fa sabitlendi",noFixedTitle:"Sabitlenmedi",reset:"S\u0131f\u0131rla",columnDisplay:"Kolon G\xF6r\xFCn\xFCm\xFC",columnSetting:"Ayarlar",fullScreen:"Tam Ekran",exitFullScreen:"Tam Ekrandan \xC7\u0131k",reload:"Yenile",density:"Kal\u0131nl\u0131k",densityDefault:"Varsay\u0131lan",densityLarger:"B\xFCy\xFCk",densityMiddle:"Orta",densitySmall:"K\xFC\xE7\xFCk"},stepsForm:{next:"S\u0131radaki",prev:"\xD6nceki",submit:"G\xF6nder"},loginForm:{submitText:"Giri\u015F Yap"},editableTable:{action:{save:"Kaydet",cancel:"Vazge\xE7",delete:"Sil",add:"foegje in rige gegevens ta"}},switch:{open:"a\xE7\u0131k",close:"kapatmak"}},w={moneySymbol:"\u20AB",form:{lightFilter:{more:"Nhi\u1EC1u h\u01A1n",clear:"Trong",confirm:"X\xE1c nh\u1EADn",itemUnit:"M\u1EE5c"}},tableForm:{search:"T\xECm ki\u1EBFm",reset:"L\xE0m l\u1EA1i",submit:"G\u1EEDi \u0111i",collapsed:"M\u1EDF r\u1ED9ng",expand:"Thu g\u1ECDn",inputPlaceholder:"nh\u1EADp d\u1EEF li\u1EC7u",selectPlaceholder:"Vui l\xF2ng ch\u1ECDn"},alert:{clear:"X\xF3a",selected:"\u0111\xE3 ch\u1ECDn",item:"m\u1EE5c"},pagination:{total:{range:" ",total:"tr\xEAn",item:"m\u1EB7t h\xE0ng"}},tableToolBar:{leftPin:"Ghim tr\xE1i",rightPin:"Ghim ph\u1EA3i",noPin:"B\u1ECF ghim",leftFixedTitle:"C\u1ED1 \u0111\u1ECBnh tr\xE1i",rightFixedTitle:"C\u1ED1 \u0111\u1ECBnh ph\u1EA3i",noFixedTitle:"Ch\u01B0a c\u1ED1 \u0111\u1ECBnh",reset:"L\xE0m l\u1EA1i",columnDisplay:"C\u1ED9t hi\u1EC3n th\u1ECB",columnSetting:"C\u1EA5u h\xECnh",fullScreen:"Ch\u1EBF \u0111\u1ED9 to\xE0n m\xE0n h\xECnh",exitFullScreen:"Tho\xE1t ch\u1EBF \u0111\u1ED9 to\xE0n m\xE0n h\xECnh",reload:"L\xE0m m\u1EDBi",density:"M\u1EADt \u0111\u1ED9 hi\u1EC3n th\u1ECB",densityDefault:"M\u1EB7c \u0111\u1ECBnh",densityLarger:"M\u1EB7c \u0111\u1ECBnh",densityMiddle:"Trung b\xECnh",densitySmall:"Ch\u1EADt"},stepsForm:{next:"Sau",prev:"Tr\u01B0\u1EDBc",submit:"K\u1EBFt th\xFAc"},loginForm:{submitText:"\u0110\u0103ng nh\u1EADp"},editableTable:{action:{save:"C\u1EE9u",cancel:"H\u1EE7y",delete:"X\xF3a",add:"th\xEAm m\u1ED9t h\xE0ng d\u1EEF li\u1EC7u"}},switch:{open:"m\u1EDF",close:"\u0111\xF3ng"}},gt={moneySymbol:"\uFFE5",deleteThisLine:"\u5220\u9664\u6B64\u884C",copyThisLine:"\u590D\u5236\u6B64\u884C",form:{lightFilter:{more:"\u66F4\u591A\u7B5B\u9009",clear:"\u6E05\u9664",confirm:"\u786E\u8BA4",itemUnit:"\u9879"}},tableForm:{search:"\u67E5\u8BE2",reset:"\u91CD\u7F6E",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u5F00",expand:"\u6536\u8D77",inputPlaceholder:"\u8BF7\u8F93\u5165",selectPlaceholder:"\u8BF7\u9009\u62E9"},alert:{clear:"\u53D6\u6D88\u9009\u62E9",selected:"\u5DF2\u9009\u62E9",item:"\u9879"},pagination:{total:{range:"\u7B2C",total:"\u6761/\u603B\u5171",item:"\u6761"}},tableToolBar:{leftPin:"\u56FA\u5B9A\u5728\u5217\u9996",rightPin:"\u56FA\u5B9A\u5728\u5217\u5C3E",noPin:"\u4E0D\u56FA\u5B9A",leftFixedTitle:"\u56FA\u5B9A\u5728\u5DE6\u4FA7",rightFixedTitle:"\u56FA\u5B9A\u5728\u53F3\u4FA7",noFixedTitle:"\u4E0D\u56FA\u5B9A",reset:"\u91CD\u7F6E",columnDisplay:"\u5217\u5C55\u793A",columnSetting:"\u5217\u8BBE\u7F6E",fullScreen:"\u5168\u5C4F",exitFullScreen:"\u9000\u51FA\u5168\u5C4F",reload:"\u5237\u65B0",density:"\u5BC6\u5EA6",densityDefault:"\u6B63\u5E38",densityLarger:"\u9ED8\u8BA4",densityMiddle:"\u4E2D\u7B49",densitySmall:"\u7D27\u51D1"},stepsForm:{next:"\u4E0B\u4E00\u6B65",prev:"\u4E0A\u4E00\u6B65",submit:"\u63D0\u4EA4"},loginForm:{submitText:"\u767B\u5F55"},editableTable:{onlyOneLineEditor:"\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C",action:{save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",delete:"\u5220\u9664",add:"\u6DFB\u52A0\u4E00\u884C\u6570\u636E"}},switch:{open:"\u6253\u5F00",close:"\u5173\u95ED"}},Mt={moneySymbol:"NT$",form:{lightFilter:{more:"\u66F4\u591A\u7BE9\u9078",clear:"\u6E05\u9664",confirm:"\u78BA\u8A8D",itemUnit:"\u9805"}},tableForm:{search:"\u67E5\u8A62",reset:"\u91CD\u7F6E",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u958B",expand:"\u6536\u8D77",inputPlaceholder:"\u8ACB\u8F38\u5165",selectPlaceholder:"\u8ACB\u9078\u64C7"},alert:{clear:"\u53D6\u6D88\u9078\u64C7",selected:"\u5DF2\u9078\u64C7",item:"\u9805"},pagination:{total:{range:"\u7B2C",total:"\u689D/\u7E3D\u5171",item:"\u689D"}},tableToolBar:{leftPin:"\u56FA\u5B9A\u5230\u5DE6\u908A",rightPin:"\u56FA\u5B9A\u5230\u53F3\u908A",noPin:"\u4E0D\u56FA\u5B9A",leftFixedTitle:"\u56FA\u5B9A\u5728\u5DE6\u5074",rightFixedTitle:"\u56FA\u5B9A\u5728\u53F3\u5074",noFixedTitle:"\u4E0D\u56FA\u5B9A",reset:"\u91CD\u7F6E",columnDisplay:"\u5217\u5C55\u793A",columnSetting:"\u5217\u8A2D\u7F6E",fullScreen:"\u5168\u5C4F",exitFullScreen:"\u9000\u51FA\u5168\u5C4F",reload:"\u5237\u65B0",density:"\u5BC6\u5EA6",densityDefault:"\u6B63\u5E38",densityLarger:"\u9ED8\u8A8D",densityMiddle:"\u4E2D\u7B49",densitySmall:"\u7DCA\u6E4A"},stepsForm:{next:"\u4E0B\u4E00\u500B",prev:"\u4EE5\u524D\u7684",submit:"\u5B8C\u6210"},loginForm:{submitText:"\u767B\u5165"},editableTable:{action:{save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",delete:"\u522A\u9664",add:"\u6DFB\u52A0\u4E00\u884C\u6578\u64DA"}},switch:{open:"\u6253\u958B",close:"\u95DC\u9589"}},xr=Y.rS||{useToken:function(){return{hashId:""}}},wr=xr.useToken;function Ft(_e,rt,Lt){var Ee=rt.replace(/\[(\d+)\]/g,".$1").split("."),yt=_e,tt=Lt,Dt=(0,M.Z)(Ee),Sr;try{for(Dt.s();!(Sr=Dt.n()).done;){var ir=Sr.value;if(tt=Object(yt)[ir],yt=Object(yt)[ir],tt===void 0)return Lt}}catch(Lr){Dt.e(Lr)}finally{Dt.f()}return tt}var ke=function(rt,Lt){return{getMessage:function(yt,tt){return Ft(Lt,yt,tt)||tt},locale:rt}},cr=ke("mn_MN",U),jr=ke("ar_EG",J),Vt=ke("zh_CN",gt),er=ke("en_US",se),hr=ke("en_GB",qe),It=ke("vi_VN",w),ge=ke("it_IT",p),st=ke("ja_JP",fe),kt=ke("es_ES",Qe),Xt=ke("ca_ES",we),ct=ke("ru_RU",Ke),ut=ke("sr_RS",O),mt=ke("ms_MY",We),Yt=ke("zh_TW",Mt),Zt=ke("fr_FR",At),Ht=ke("pt_BR",Se),rr=ke("ko_KR",u),br=ke("id_ID",Et),Ar=ke("de_DE",be),Kr=ke("fa_IR",He),Mr=ke("tr_TR",wt),cn=ke("pl_PL",Nt),ur=ke("hr_",ot),pt={"mn-MN":cr,"ar-EG":jr,"zh-CN":Vt,"en-US":er,"en-GB":hr,"vi-VN":It,"it-IT":ge,"ja-JP":st,"es-ES":kt,"ca-ES":Xt,"ru-RU":ct,"sr-RS":ut,"ms-MY":mt,"zh-TW":Yt,"fr-FR":Zt,"pt-BR":Ht,"ko-KR":rr,"id-ID":br,"de-DE":Ar,"fa-IR":Kr,"tr-TR":Mr,"pl-PL":cn,"hr-HR":ur},ar=Object.keys(pt),Or=X.createContext({intl:(0,P.Z)((0,P.Z)({},Vt),{},{locale:"default"}),isDeps:!1,valueTypeMap:{}}),Pn=Or.Consumer,un=Or.Provider,Q=function(rt){if(!rt)return"zh-CN";var Lt=rt.toLocaleLowerCase();return ar.find(function(Ee){var yt=Ee.toLocaleLowerCase();return yt.includes(Lt)})},$e=function(){var rt=(0,B.kY)(),Lt=rt.cache;return(0,X.useEffect)(function(){return function(){Lt.clear()}},[]),null},V=function(rt){var Lt=rt.children,Ee=rt.autoClearCache,yt=Ee===void 0?!1:Ee,tt=(0,X.useContext)(H.ZP.ConfigContext),Dt=tt.locale,Sr=tt.getPrefixCls,ir=wr==null?void 0:wr(),Lr=Dt===void 0?H.ZP:X.Fragment,dr=(0,X.useContext)(Or),et=(0,G.jsx)(Pn,{children:function(_r){var S,_,z=Dt==null?void 0:Dt.locale,Ne=Q(z),it=z&&((S=_r.intl)===null||S===void 0?void 0:S.locale)==="default"?pt[Ne]:_r.intl||pt[Ne],Xe=Dt===void 0?{locale:oe.Z,theme:{hashed:((_="production")===null||_===void 0?void 0:_.toLowerCase())!=="test"}}:{},dt=(0,G.jsx)(Lr,(0,P.Z)((0,P.Z)({},Xe),{},{children:(0,G.jsx)(un,{value:(0,P.Z)((0,P.Z)({},_r),{},{isDeps:!0,intl:it||Vt}),children:(0,G.jsxs)(G.Fragment,{children:[yt&&(0,G.jsx)($e,{}),Lt]})})}));return dr.isDeps?dt:(0,G.jsx)("div",{className:"".concat((Sr==null?void 0:Sr("pro"))||"ant-pro"," ").concat(ir.hashId),children:dt})}});return yt?(0,G.jsx)(B.J$,{value:{provider:function(){return new Map}},children:et}):et};function Ae(){var _e=(0,X.useContext)(H.ZP.ConfigContext),rt=_e.locale,Lt=(0,X.useContext)(Or),Ee=Lt.intl;return Ee&&Ee.locale!=="default"?Ee:(rt==null?void 0:rt.locale)?pt[Q(rt.locale)]:Vt}var Re=null,Ce=Or},62582:function(Ct,ve,m){"use strict";m.d(ve,{SV:function(){return ho},Qy:function(){return yo},ML:function(){return Ui},UA:function(){return rl},Gx:function(){return Ci},Jp:function(){return ca},n4:function(){return Si},lp:function(){return Ca},c1:function(){return Eo},Cl:function(){return Oa},cx:function(){return $a},X8:function(){return Ei},wf:function(){return Yi},jU:function(){return Pi},Ad:function(){return Qn},BU:function(){return Io},ev:function(){return Ii},kK:function(){return ua},CB:function(){return Va},TS:function(){return Ta},x0:function(){return il},vF:function(){return Zo},Yc:function(){return Ao},eQ:function(){return Oo},Nd:function(){return ya},iV:function(){return Ka},vw:function(){return Ea},j8:function(){return Oi},sN:function(){return Cn},Wf:function(){return pa},hm:function(){return Mo},uK:function(){return $o},My:function(){return ol},DI:function(){return Xi},nj:function(){return Qi},KW:function(){return Ji},Au:function(){return Po},jr:function(){return Ro},e0:function(){return Bo},jL:function(){return Zn},$5:function(){return wa},dU:function(){return Mi},i9:function(){return ci.default},D9:function(){return to},Jg:function(){return Rn},FH:function(){return ul},Xj:function(){return Za},dQ:function(){return pi}});var P={};m.r(P),m.d(P,{darkAlgorithm:function(){return Ko},defaultAlgorithm:function(){return ki},defaultTheme:function(){return _a},token:function(){return mi},useToken:function(){return vo}});var M=m(85893),G=m(78775),Y=m(88182),H=m(71577),oe=m(94184),X=m.n(oe),B=m(67294),J=m(28991),we=m(96156),be=m(28481),qe=m(85061),se=m(90484),Qe=m(44958),He=m(98924),At=m(62506),ot=m(40351),Et="-ms-",p="-moz-",fe="-webkit-",u="comm",U="rule",We="decl",Nt="@page",Se="@media",Ke="@import",O="@charset",wt="@viewport",w="@supports",gt="@document",Mt="@namespace",xr="@keyframes",wr="@font-face",Ft="@counter-style",ke="@font-feature-values",cr="@layer",jr="@scope",Vt=Math.abs,er=String.fromCharCode,hr=Object.assign;function It(l,o){return ct(l,0)^45?(((o<<2^ct(l,0))<<2^ct(l,1))<<2^ct(l,2))<<2^ct(l,3):0}function ge(l){return l.trim()}function st(l,o){return(l=o.exec(l))?l[0]:l}function kt(l,o,f){return l.replace(o,f)}function Xt(l,o,f){return l.indexOf(o,f)}function ct(l,o){return l.charCodeAt(o)|0}function ut(l,o,f){return l.slice(o,f)}function mt(l){return l.length}function Yt(l){return l.length}function Zt(l,o){return o.push(l),l}function Ht(l,o){return l.map(o).join("")}function rr(l,o){return l.filter(function(f){return!st(f,o)})}function br(l,o){for(var f="",y=0;y<l.length;y++)f+=o(l[y],y,l,o)||"";return f}function Ar(l,o,f,y){switch(l.type){case cr:if(l.children.length)break;case Ke:case Mt:case We:return l.return=l.return||l.value;case u:return"";case xr:return l.return=l.value+"{"+br(l.children,y)+"}";case U:if(!mt(l.value=l.props.join(",")))return""}return mt(f=br(l.children,y))?l.return=l.value+"{"+f+"}":""}var Kr=1,Mr=1,cn=0,ur=0,pt=0,ar="";function Or(l,o,f,y,E,Z,L,k){return{value:l,root:o,parent:f,type:y,props:E,children:Z,line:Kr,column:Mr,length:L,return:"",siblings:k}}function Pn(l,o){return assign(Or("",null,null,"",null,null,0,l.siblings),l,{length:-l.length},o)}function un(l){for(;l.root;)l=Pn(l.root,{children:[l]});append(l,l.siblings)}function Q(){return pt}function $e(){return pt=ur>0?ct(ar,--ur):0,Mr--,pt===10&&(Mr=1,Kr--),pt}function V(){return pt=ur<cn?ct(ar,ur++):0,Mr++,pt===10&&(Mr=1,Kr++),pt}function Ae(){return ct(ar,ur)}function Re(){return ur}function Ce(l,o){return ut(ar,l,o)}function _e(l){switch(l){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function rt(l){return Kr=Mr=1,cn=mt(ar=l),ur=0,[]}function Lt(l){return ar="",l}function Ee(l){return ge(Ce(ur-1,ir(l===91?l+2:l===40?l+1:l)))}function yt(l){return Lt(Dt(rt(l)))}function tt(l){for(;(pt=Ae())&&pt<33;)V();return _e(l)>2||_e(pt)>3?"":" "}function Dt(l){for(;V();)switch(_e(pt)){case 0:append(dr(ur-1),l);break;case 2:append(Ee(pt),l);break;default:append(from(pt),l)}return l}function Sr(l,o){for(;--o&&V()&&!(pt<48||pt>102||pt>57&&pt<65||pt>70&&pt<97););return Ce(l,Re()+(o<6&&Ae()==32&&V()==32))}function ir(l){for(;V();)switch(pt){case l:return ur;case 34:case 39:l!==34&&l!==39&&ir(pt);break;case 40:l===41&&ir(l);break;case 92:V();break}return ur}function Lr(l,o){for(;V()&&l+pt!==47+10;)if(l+pt===42+42&&Ae()===47)break;return"/*"+Ce(o,ur-1)+"*"+er(l===47?l:V())}function dr(l){for(;!_e(Ae());)V();return Ce(l,ur)}function et(l){return Lt(Yr("",null,null,null,[""],l=rt(l),0,[0],l))}function Yr(l,o,f,y,E,Z,L,k,te){for(var ce=0,re=0,de=L,me=0,ue=0,he=0,Ie=1,Ze=1,Fe=1,xe=0,Pe="",De=E,ze=Z,jt=y,Be=Pe;Ze;)switch(he=xe,xe=V()){case 40:if(he!=108&&ct(Be,de-1)==58){Xt(Be+=kt(Ee(xe),"&","&\f"),"&\f",Vt(ce?k[ce-1]:0))!=-1&&(Fe=-1);break}case 34:case 39:case 91:Be+=Ee(xe);break;case 9:case 10:case 13:case 32:Be+=tt(he);break;case 92:Be+=Sr(Re()-1,7);continue;case 47:switch(Ae()){case 42:case 47:Zt(S(Lr(V(),Re()),o,f,te),te),(_e(he||1)==5||_e(Ae()||1)==5)&&mt(Be)&&ut(Be,-1,void 0)!==" "&&(Be+=" ");break;default:Be+="/"}break;case 123*Ie:k[ce++]=mt(Be)*Fe;case 125*Ie:case 59:case 0:switch(xe){case 0:case 125:Ze=0;case 59+re:Fe==-1&&(Be=kt(Be,/\f/g,"")),ue>0&&(mt(Be)-de||Ie===0&&he===47)&&Zt(ue>32?_(Be+";",y,f,de-1,te):_(kt(Be," ","")+";",y,f,de-2,te),te);break;case 59:Be+=";";default:if(Zt(jt=_r(Be,o,f,ce,re,E,k,Pe,De=[],ze=[],de,Z),Z),xe===123)if(re===0)Yr(Be,o,jt,jt,De,Z,de,k,ze);else{switch(me){case 99:if(ct(Be,3)===110)break;case 108:if(ct(Be,2)===97)break;default:re=0;case 100:case 109:case 115:}re?Yr(l,jt,jt,y&&Zt(_r(l,jt,jt,0,0,E,k,Pe,E,De=[],de,ze),ze),E,ze,de,k,y?De:ze):Yr(Be,jt,jt,jt,[""],ze,0,k,ze)}}ce=re=ue=0,Ie=Fe=1,Pe=Be="",de=L;break;case 58:de=1+mt(Be),ue=he;default:if(Ie<1){if(xe==123)--Ie;else if(xe==125&&Ie++==0&&$e()==125)continue}switch(Be+=er(xe),xe*Ie){case 38:Fe=re>0?1:(Be+="\f",-1);break;case 44:k[ce++]=(mt(Be)-1)*Fe,Fe=1;break;case 64:Ae()===45&&(Be+=Ee(V())),me=Ae(),re=de=mt(Pe=Be+=dr(Re())),xe++;break;case 45:he===45&&mt(Be)==2&&(Ie=0)}}return Z}function _r(l,o,f,y,E,Z,L,k,te,ce,re,de){for(var me=E-1,ue=E===0?Z:[""],he=Yt(ue),Ie=0,Ze=0,Fe=0;Ie<y;++Ie)for(var xe=0,Pe=ut(l,me+1,me=Vt(Ze=L[Ie])),De=l;xe<he;++xe)(De=ge(Ze>0?ue[xe]+" "+Pe:kt(Pe,/&\f/g,ue[xe])))&&(te[Fe++]=De);return Or(l,o,f,E===0?U:k,te,ce,re,de)}function S(l,o,f,y){return Or(l,o,f,u,er(Q()),ut(l,2,-2),0,y)}function _(l,o,f,y,E){return Or(l,o,f,We,ut(l,0,y),ut(l,y+1,-1),y,E)}var z=m(6610),Ne=m(5991),it=function(){function l(){(0,z.Z)(this,l),this.cache=new Map}return(0,Ne.Z)(l,[{key:"get",value:function(f){return this.cache.get(f.join("%"))||null}},{key:"update",value:function(f,y){var E=f.join("%"),Z=this.cache.get(E),L=y(Z);L===null?this.cache.delete(E):this.cache.set(E,L)}}]),l}(),Xe=it,dt="data-token-hash",xt="data-css-hash",yn="data-dev-cache-path",tn="__cssinjs_instance__",rn=Math.random().toString(12).slice(2);function Xa(){if(typeof document!="undefined"){var l=document.body.querySelectorAll("style[".concat(xt,"]")),o=document.head.firstChild;Array.from(l).forEach(function(y){y[tn]=y[tn]||rn,document.head.insertBefore(y,o)});var f={};Array.from(document.querySelectorAll("style[".concat(xt,"]"))).forEach(function(y){var E=y.getAttribute(xt);if(f[E]){if(y[tn]===rn){var Z;(Z=y.parentNode)===null||Z===void 0||Z.removeChild(y)}}else f[E]=!0})}return new Xe}var Wn=B.createContext({hashPriority:"low",cache:Xa(),defaultCache:!0}),Bi=function(o){var f=o.autoClear,y=o.mock,E=o.cache,Z=o.hashPriority,L=o.children,k=React.useContext(Wn),te=k.cache,ce=k.autoClear,re=k.mock,de=k.defaultCache,me=k.hashPriority,ue=React.useMemo(function(){return{autoClear:f!=null?f:ce,mock:y!=null?y:re,cache:E||te||Xa(),defaultCache:!E&&de,hashPriority:Z!=null?Z:me}},[f,ce,re,te,y,E,de,Z,me]);return React.createElement(Wn.Provider,{value:ue},L)},Qa=Wn;function Ni(){return!1}var ra=!1;function ka(){return ra}var Fr=Ni;if(!1)var Fn,Vn;function dn(l,o,f,y){var E=B.useContext(Qa),Z=E.cache,L=[l].concat((0,qe.Z)(o)),k=Fr();return B.useMemo(function(){Z.update(L,function(te){var ce=te||[],re=(0,be.Z)(ce,2),de=re[0],me=de===void 0?0:de,ue=re[1],he=ue,Ie=he||f();return[me+1,Ie]})},[L.join("_")]),B.useEffect(function(){return function(){Z.update(L,function(te){var ce=te||[],re=(0,be.Z)(ce,2),de=re[0],me=de===void 0?0:de,ue=re[1],he=me-1;return he===0?(y==null||y(ue,!1),null):[me-1,ue]})}},L),Z.get(L)[1]}var lo=m(80334);function fn(l){var o="";return Object.keys(l).forEach(function(f){var y=l[f];o+=f,y&&_typeof(y)==="object"?o+=fn(y):o+=y}),o}function vn(l,o){return hash("".concat(o,"_").concat(fn(l)))}function Br(l,o){devWarning(!1,"[Ant Design CSS-in-JS] ".concat(o?"Error in '".concat(o,"': "):"").concat(l))}var $r=function(o,f){var y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},E=y.path,Z=y.hashId;switch(o){case"content":var L=/(attr|counters?|url|(((repeating-)?(linear|radial))|conic)-gradient)\(|(no-)?(open|close)-quote/,k=["normal","none","initial","inherit","unset"];(typeof f!="string"||k.indexOf(f)===-1&&!L.test(f)&&(f.charAt(0)!==f.charAt(f.length-1)||f.charAt(0)!=='"'&&f.charAt(0)!=="'"))&&Br("You seem to be using a value for 'content' without quotes, try replacing it with `content: '\"".concat(f,"\"'`"),E);return;case"marginLeft":case"marginRight":case"paddingLeft":case"paddingRight":case"left":case"right":case"borderLeft":case"borderLeftWidth":case"borderLeftStyle":case"borderLeftColor":case"borderRight":case"borderRightWidth":case"borderRightStyle":case"borderRightColor":case"borderTopLeftRadius":case"borderTopRightRadius":case"borderBottomLeftRadius":case"borderBottomRightRadius":Br("You seem to be using non-logical property '".concat(o,"' which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties."),E);return;case"margin":case"padding":case"borderWidth":case"borderStyle":if(typeof f=="string"){var te=f.split(" ").map(function(de){return de.trim()});te.length===4&&te[1]!==te[3]&&Br("You seem to be using '".concat(o,"' property with different left ").concat(o," and right ").concat(o,", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties."),E)}return;case"clear":case"textAlign":(f==="left"||f==="right")&&Br("You seem to be using non-logical value '".concat(f,"' of ").concat(o,", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties."),E);return;case"borderRadius":if(typeof f=="string"){var ce=f.split("/").map(function(de){return de.trim()}),re=ce.reduce(function(de,me){if(de)return de;var ue=me.split(" ").map(function(he){return he.trim()});return ue.length>=2&&ue[0]!==ue[1]||ue.length===3&&ue[1]!==ue[2]||ue.length===4&&ue[2]!==ue[3]?!0:de},!1);re&&Br("You seem to be using non-logical value '".concat(f,"' of ").concat(o,", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties."),E)}return;case"animation":Z&&f!=="none"&&Br("You seem to be using hashed animation '".concat(f,"', in which case 'animationName' with Keyframe as value is recommended."),E);default:return}},xn="layer-".concat(Date.now(),"-").concat(Math.random()).replace(/\./g,""),na="903px";function Xr(l,o){if((0,He.Z)()){var f;(0,Qe.hq)(l,xn);var y=document.createElement("div");y.style.position="fixed",y.style.left="0",y.style.top="0",o==null||o(y),document.body.appendChild(y);var E=getComputedStyle(y).width===na;return(f=y.parentNode)===null||f===void 0||f.removeChild(y),(0,Qe.jL)(xn),E}return!1}var Pr=void 0;function Kn(){return Pr===void 0&&(Pr=Xr("@layer ".concat(xn," { .").concat(xn," { width: ").concat(na,"!important; } }"),function(l){l.className=xn})),Pr}var bn=(0,He.Z)(),Ja="_skip_check_";function aa(l){var o=br(et(l),Ar);return o.replace(/\{%%%\:[^;];}/g,";")}function ia(l){return(0,se.Z)(l)==="object"&&l&&Ja in l}var Bn={};function $n(l,o,f){if(!o)return l;var y=".".concat(o),E=f==="low"?":where(".concat(y,")"):y,Z=l.split(",").map(function(L){var k,te=L.trim().split(/\s+/),ce=te[0]||"",re=((k=ce.match(/^\w+/))===null||k===void 0?void 0:k[0])||"";return ce="".concat(re).concat(E).concat(ce.slice(re.length)),[ce].concat((0,qe.Z)(te.slice(1))).join(" ")});return Z.join(",")}var qa=function l(o){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{root:!0},E=y.root,Z=y.injectHash,L=f.hashId,k=f.layer,te=f.path,ce=f.hashPriority,re="";function de(Ze){return Bn[Ze.getName(L)]?"":(Bn[Ze.getName(L)]=!0,"@keyframes ".concat(Ze.getName(L)).concat(l(Ze.style,f,{root:!1})))}function me(Ze){var Fe=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return Ze.forEach(function(xe){Array.isArray(xe)?me(xe,Fe):xe&&Fe.push(xe)}),Fe}var ue=me(Array.isArray(o)?o:[o]);if(ue.forEach(function(Ze){var Fe=typeof Ze=="string"&&!E?{}:Ze;typeof Fe=="string"?re+="".concat(Fe,`
`):Fe._keyframe?re+=de(Fe):Object.keys(Fe).forEach(function(xe){var Pe=Fe[xe];if((0,se.Z)(Pe)==="object"&&Pe&&(xe!=="animationName"||!Pe._keyframe)&&!ia(Pe)){var De=!1,ze=xe.trim(),jt=!1;(E||Z)&&L?ze.startsWith("@")?De=!0:ze=$n(xe,L,ce):E&&!L&&(ze==="&"||ze==="")&&(ze="",jt=!0),re+="".concat(ze).concat(l(Pe,(0,J.Z)((0,J.Z)({},f),{},{path:"".concat(te," -> ").concat(ze)}),{root:jt,injectHash:De}))}else{var Be,Nr=(Be=Pe==null?void 0:Pe.value)!==null&&Be!==void 0?Be:Pe,An=xe.replace(/[A-Z]/g,function(ja){return"-".concat(ja.toLowerCase())}),Jt=Nr;!ot.Z[xe]&&typeof Jt=="number"&&Jt!==0&&(Jt="".concat(Jt,"px")),xe==="animationName"&&(Pe==null?void 0:Pe._keyframe)&&(re+=de(Pe),Jt=Pe.getName(L)),re+="".concat(An,":").concat(Jt,";")}})}),!E)re="{".concat(re,"}");else if(k&&Kn()){var he=k.split(","),Ie=he[he.length-1].trim();re="@layer ".concat(Ie," {").concat(re,"}"),he.length>1&&(re="@layer ".concat(k,"{%%%:%}").concat(re))}return re};function ga(l,o){return(0,At.Z)("".concat(l.join("%")).concat(o))}function Ha(){return null}function so(l,o){var f=l.token,y=l.path,E=l.hashId,Z=l.layer,L=B.useContext(Qa),k=L.autoClear,te=L.mock,ce=L.defaultCache,re=L.hashPriority,de=f._tokenKey,me=[de].concat((0,qe.Z)(y)),ue=bn,he=dn("style",me,function(){var Pe=o(),De=aa(qa(Pe,{hashId:E,hashPriority:re,layer:Z,path:y.join("-")})),ze=ga(me,De);if(Bn={},ue){var jt=(0,Qe.hq)(De,ze,{mark:xt,prepend:"queue"});jt[tn]=rn,jt.setAttribute(dt,de)}return[De,de,ze]},function(Pe,De){var ze=(0,be.Z)(Pe,3),jt=ze[2];(De||k)&&bn&&(0,Qe.jL)(jt,{mark:xt})}),Ie=(0,be.Z)(he,3),Ze=Ie[0],Fe=Ie[1],xe=Ie[2];return function(Pe){var De;if(ue||!ce)De=B.createElement(Ha,null);else{var ze;De=B.createElement("style",(0,J.Z)((0,J.Z)({},(ze={},(0,we.Z)(ze,dt,Fe),(0,we.Z)(ze,xt,xe),ze)),{},{dangerouslySetInnerHTML:{__html:Ze}}))}return B.createElement(B.Fragment,null,De,Pe)}}function vl(l){var o=Array.from(l.cache.keys()).filter(function(y){return y.startsWith("style%")}),f="";return o.forEach(function(y){var E=_slicedToArray(l.cache.get(y)[1],3),Z=E[0],L=E[1],k=E[2];f+="<style ".concat(ATTR_TOKEN,'="').concat(L,'" ').concat(ATTR_MARK,'="').concat(k,'">').concat(Z,"</style>")}),f}var Qr={},gi=null,oa=new Map;function Wo(l){oa.set(l,(oa.get(l)||0)+1)}function Gn(l){if(typeof document!="undefined"){var o=document.querySelectorAll("style[".concat(ATTR_TOKEN,'="').concat(l,'"]'));o.forEach(function(f){if(f[CSS_IN_JS_INSTANCE]===CSS_IN_JS_INSTANCE_ID){var y;(y=f.parentNode)===null||y===void 0||y.removeChild(f)}})}}function co(l){oa.set(l,(oa.get(l)||0)-1);var o=Array.from(oa.keys()),f=o.filter(function(y){var E=oa.get(y)||0;return E<=0});f.length<o.length&&f.forEach(function(y){Gn(y),oa.delete(y)})}function ei(l,o){var f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},y=f.salt,E=y===void 0?"":y,Z=f.override,L=Z===void 0?Qr:Z,k=f.formatToken,te=React.useMemo(function(){return Object.assign.apply(Object,[{}].concat(_toConsumableArray(o)))},[o]),ce=React.useMemo(function(){return flattenToken(te)},[te]),re=React.useMemo(function(){return flattenToken(L)},[L]),de=useGlobalCache("token",[E,l.id,ce,re],function(){var me=l.getDerivativeToken(te),ue=_objectSpread(_objectSpread({},me),L);k&&(ue=k(ue));var he=token2key(ue,E);ue._tokenKey=he,Wo(he);var Ie="".concat(gi,"-").concat(hash(he));return ue._hashId=Ie,[ue,Ie]},function(me){co(me[0]._tokenKey)});return de}function uo(l,o){if(l.length!==o.length)return!1;for(var f=0;f<l.length;f++)if(l[f]!==o[f])return!1;return!0}var ma=function(){function l(){(0,z.Z)(this,l),this.cache=void 0,this.keys=void 0,this.cacheCallTimes=void 0,this.cache=new Map,this.keys=[],this.cacheCallTimes=0}return(0,Ne.Z)(l,[{key:"size",value:function(){return this.keys.length}},{key:"internalGet",value:function(f){var y,E,Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,L={map:this.cache};return f.forEach(function(k){if(!L)L=void 0;else{var te,ce;L=(te=L)===null||te===void 0||(ce=te.map)===null||ce===void 0?void 0:ce.get(k)}}),((y=L)===null||y===void 0?void 0:y.value)&&Z&&(L.value[1]=this.cacheCallTimes++),(E=L)===null||E===void 0?void 0:E.value}},{key:"get",value:function(f){var y;return(y=this.internalGet(f,!0))===null||y===void 0?void 0:y[0]}},{key:"has",value:function(f){return!!this.internalGet(f)}},{key:"set",value:function(f,y){var E=this;if(!this.has(f)){if(this.size()+1>l.MAX_CACHE_SIZE+l.MAX_CACHE_OFFSET){var Z=this.keys.reduce(function(ce,re){var de=(0,be.Z)(ce,2),me=de[1];return E.internalGet(re)[1]<me?[re,E.internalGet(re)[1]]:ce},[this.keys[0],this.cacheCallTimes]),L=(0,be.Z)(Z,1),k=L[0];this.delete(k)}this.keys.push(f)}var te=this.cache;f.forEach(function(ce,re){if(re===f.length-1)te.set(ce,{value:[y,E.cacheCallTimes++]});else{var de=te.get(ce);de?de.map||(de.map=new Map):te.set(ce,{map:new Map}),te=te.get(ce).map}})}},{key:"deleteByPath",value:function(f,y){var E=f.get(y[0]);if(y.length===1){var Z;return E.map?f.set(y[0],{map:E.map}):f.delete(y[0]),(Z=E.value)===null||Z===void 0?void 0:Z[0]}var L=this.deleteByPath(E.map,y.slice(1));return(!E.map||E.map.size===0)&&!E.value&&f.delete(y[0]),L}},{key:"delete",value:function(f){if(this.has(f))return this.keys=this.keys.filter(function(y){return!uo(y,f)}),this.deleteByPath(this.cache,f)}}]),l}();ma.MAX_CACHE_SIZE=20,ma.MAX_CACHE_OFFSET=5;var hl=0,fo=null,ti=new ma;function gl(l){var o=Array.isArray(l)?l:[l];return ti.has(o)||ti.set(o,new Theme(o)),ti.get(o)}var Vo=m(10274),ri=m(68370),_a={blue:"#1890FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#eb2f96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911",colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677FF",colorTextBase:"#000",colorTextLightSolid:"#fff",colorBgBase:"#fff",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\\n'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\\n'Noto Color Emoji'",fontSizeBase:14,gridUnit:4,gridBaseStep:2,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInQuint:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",radiusBase:4,sizeUnit:4,sizeBaseStep:4,sizePopupArrow:11.313708498984761,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,"blue-1":"#e6f7ff","blue-2":"#bae7ff","blue-3":"#91d5ff","blue-4":"#69c0ff","blue-5":"#40a9ff","blue-6":"#1890ff","blue-7":"#096dd9","blue-8":"#0050b3","blue-9":"#003a8c","blue-10":"#002766","purple-1":"#f9f0ff","purple-2":"#efdbff","purple-3":"#d3adf7","purple-4":"#b37feb","purple-5":"#9254de","purple-6":"#722ed1","purple-7":"#531dab","purple-8":"#391085","purple-9":"#22075e","purple-10":"#120338","cyan-1":"#e6fffb","cyan-2":"#b5f5ec","cyan-3":"#87e8de","cyan-4":"#5cdbd3","cyan-5":"#36cfc9","cyan-6":"#13c2c2","cyan-7":"#08979c","cyan-8":"#006d75","cyan-9":"#00474f","cyan-10":"#002329","green-1":"#f6ffed","green-2":"#d9f7be","green-3":"#b7eb8f","green-4":"#95de64","green-5":"#73d13d","green-6":"#52c41a","green-7":"#389e0d","green-8":"#237804","green-9":"#135200","green-10":"#092b00","magenta-1":"#fff0f6","magenta-2":"#ffd6e7","magenta-3":"#ffadd2","magenta-4":"#ff85c0","magenta-5":"#f759ab","magenta-6":"#eb2f96","magenta-7":"#c41d7f","magenta-8":"#9e1068","magenta-9":"#780650","magenta-10":"#520339","pink-1":"#fff0f6","pink-2":"#ffd6e7","pink-3":"#ffadd2","pink-4":"#ff85c0","pink-5":"#f759ab","pink-6":"#eb2f96","pink-7":"#c41d7f","pink-8":"#9e1068","pink-9":"#780650","pink-10":"#520339","red-1":"#fff1f0","red-2":"#ffccc7","red-3":"#ffa39e","red-4":"#ff7875","red-5":"#ff4d4f","red-6":"#f5222d","red-7":"#cf1322","red-8":"#a8071a","red-9":"#820014","red-10":"#5c0011","orange-1":"#fff7e6","orange-2":"#ffe7ba","orange-3":"#ffd591","orange-4":"#ffc069","orange-5":"#ffa940","orange-6":"#fa8c16","orange-7":"#d46b08","orange-8":"#ad4e00","orange-9":"#873800","orange-10":"#612500","yellow-1":"#feffe6","yellow-2":"#ffffb8","yellow-3":"#fffb8f","yellow-4":"#fff566","yellow-5":"#ffec3d","yellow-6":"#fadb14","yellow-7":"#d4b106","yellow-8":"#ad8b00","yellow-9":"#876800","yellow-10":"#614700","volcano-1":"#fff2e8","volcano-2":"#ffd8bf","volcano-3":"#ffbb96","volcano-4":"#ff9c6e","volcano-5":"#ff7a45","volcano-6":"#fa541c","volcano-7":"#d4380d","volcano-8":"#ad2102","volcano-9":"#871400","volcano-10":"#610b00","geekblue-1":"#f0f5ff","geekblue-2":"#d6e4ff","geekblue-3":"#adc6ff","geekblue-4":"#85a5ff","geekblue-5":"#597ef7","geekblue-6":"#2f54eb","geekblue-7":"#1d39c4","geekblue-8":"#10239e","geekblue-9":"#061178","geekblue-10":"#030852","gold-1":"#fffbe6","gold-2":"#fff1b8","gold-3":"#ffe58f","gold-4":"#ffd666","gold-5":"#ffc53d","gold-6":"#faad14","gold-7":"#d48806","gold-8":"#ad6800","gold-9":"#874d00","gold-10":"#613400","lime-1":"#fcffe6","lime-2":"#f4ffb8","lime-3":"#eaff8f","lime-4":"#d3f261","lime-5":"#bae637","lime-6":"#a0d911","lime-7":"#7cb305","lime-8":"#5b8c00","lime-9":"#3f6600","lime-10":"#254000",colorFill:"rgba(0, 0, 0, 0.06)",colorFillSecondary:"rgba(0, 0, 0, 0.04)",colorFillTertiary:"rgba(0, 0, 0, 0.03)",colorFillQuaternary:"rgba(0, 0, 0, 0.02)",bgLayout:"#f5f5f5",colorBgContainer:"#ffffff",colorBgElevated:"#ffffff",colorBorder:"#d9d9d9",colorBorderSecondary:"#f0f0f0",colorSplit:"rgba(0, 0, 0, 0.06)",colorText:"rgba(0, 0, 0, 0.85)",colorTextSecondary:"rgba(0, 0, 0, 0.45)",colorTextTertiary:"rgba(0, 0, 0, 0.45)",colorTextQuaternary:"rgba(0, 0, 0, 0.25)",colorPrimaryBg:"#e6f4ff",colorPrimaryBgHover:"#bae0ff",colorPrimaryBorder:"#91caff",colorPrimaryBorderHover:"#69b1ff",colorPrimaryHover:"#4096ff",colorPrimaryActive:"#0958d9",colorPrimaryTextHover:"#4096ff",colorPrimaryText:"#1677ff",colorPrimaryTextActive:"#0958d9",colorSuccessBg:"#f6ffed",colorSuccessBgHover:"#d9f7be",colorSuccessBorder:"#b7eb8f",colorSuccessBorderHover:"#95de64",colorSuccessHover:"#73d13d",colorSuccessActive:"#389e0d",colorSuccessTextHover:"#73d13d",colorSuccessText:"#52c41a",colorSuccessTextActive:"#389e0d",colorErrorBg:"#fff2f0",colorErrorBgHover:"#fff1f0",colorErrorBorder:"#ffccc7",colorErrorBorderHover:"#ffa39e",colorErrorHover:"#ff7875",colorErrorActive:"#d9363e",colorErrorTextHover:"#ff7875",colorErrorText:"#ff4d4f",colorErrorTextActive:"#d9363e",colorWarningBg:"#fffbe6",colorWarningBgHover:"#fff1b8",colorWarningBorder:"#ffe58f",colorWarningBorderHover:"#ffd666",colorWarningHover:"#ffc53d",colorWarningActive:"#d48806",colorWarningTextHover:"#ffc53d",colorWarningText:"#faad14",colorWarningTextActive:"#d48806",colorInfoBg:"#e6f7ff",colorInfoBgHover:"#bae7ff",colorInfoBorder:"#91d5ff",colorInfoBorderHover:"#69c0ff",colorInfoHover:"#40a9ff",colorInfoActive:"#096dd9",colorInfoTextHover:"#40a9ff",colorInfoText:"#1890ff",colorInfoTextActive:"#096dd9",colorBgMask:"rgba(0, 0, 0, 0.45)",colorBgSpotlight:"rgba(0, 0, 0, 0.85)",motionDurationFast:"0.1s",motionDurationMid:"0.2s",motionDurationSlow:"0.3s",fontSizes:[12,14,16,20,24,30,38,46,56,68],lineHeights:[1.6666666666666667,1.5714285714285714,1.5,1.4,1.3333333333333333,1.2666666666666666,1.2105263157894737,1.173913043478261,1.1428571428571428,1.1176470588235294],sizeSpaceSM:12,sizeSpace:16,sizeSpaceXS:8,sizeSpaceXXS:4,gridSpaceSM:4,gridSpaceBase:8,gridSpaceLG:12,gridSpaceXL:16,gridSpaceXXL:28,lineWidthBold:2,radiusSM:2,radiusLG:8,radiusXL:16,controlHeightSM:24,controlHeightXS:16,controlHeightLG:40,Layout:{colorBgHeader:"transparent",colorBgBody:"transparent"},colorLink:"#1677ff",colorLinkHover:"#4096ff",colorLinkActive:"#0958d9",colorFillContent:"rgba(0, 0, 0, 0.04)",colorFillContentHover:"rgba(0, 0, 0, 0.06)",colorFillAlter:"rgba(0, 0, 0, 0.02)",colorBgContainerDisabled:"rgba(0, 0, 0, 0.03)",colorBorderBg:"#ffffff",colorTextPlaceholder:"rgba(0, 0, 0, 0.25)",colorTextDisabled:"rgba(0, 0, 0, 0.25)",colorTextHeading:"rgba(0, 0, 0, 0.85)",colorTextLabel:"rgba(0, 0, 0, 0.65)",colorTextDescription:"rgba(0, 0, 0, 0.65)",colorHighlight:"#ff4d4f",colorIcon:"rgba(0, 0, 0, 0.45)",colorIconHover:"rgba(0, 0, 0, 0.85)",colorErrorOutline:"#fff2f0",colorWarningOutline:"#fffbe6",fontSizeSM:12,fontSize:14,fontSizeLG:16,fontSizeXL:20,fontSizeHeading1:38,fontSizeHeading2:30,fontSizeHeading3:24,fontSizeHeading4:20,fontSizeHeading5:16,fontSizeIcon:12,lineHeight:1.5714285714285714,lineHeightLG:1.5,lineHeightSM:1.6666666666666667,lineHeightHeading1:1.2105263157894737,lineHeightHeading2:1.2666666666666666,lineHeightHeading3:1.3333333333333333,lineHeightHeading4:1.4,lineHeightHeading5:1.5,controlLineWidth:1,controlOutlineWidth:2,controlInteractiveSize:16,controlItemBgHover:"rgba(0, 0, 0, 0.03)",controlItemBgActive:"#e6f4ff",controlItemBgActiveHover:"#bae0ff",controlItemBgActiveDisabled:"rgba(0, 0, 0, 0.25)",controlTmpOutline:"rgba(0, 0, 0, 0.02)",controlOutline:"#e6f4ff",controlLineType:"solid",controlRadius:4,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,padding:16,margin:16,paddingXXS:4,paddingXS:8,paddingSM:12,paddingLG:24,paddingXL:32,marginXXS:4,marginXS:8,marginSM:12,marginLG:24,marginXL:32,marginXXL:48,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)",screenXS:480,screenXSMin:480,screenXSMax:479,screenSM:576,screenSMMin:576,screenSMMax:575,screenMD:768,screenMDMin:768,screenMDMax:767,screenLG:992,screenLGMin:992,screenLGMax:991,screenXL:1200,screenXLMin:1200,screenXLMax:1199,screenXXL:1600,screenXXLMin:1600,screenXXLMax:1599,boxShadowPopoverArrow:"3px 3px 7px rgba(0, 0, 0, 0.1)",boxShadowPopoverArrowBottom:"2px 2px 5px rgba(0, 0, 0, 0.1)",boxShadowSegmentedSelectedItem:"0 2px 8px -2px rgba(0, 0, 0, 0.05),0 1px 4px -1px rgba(0, 0, 0, 0.07),0 0 1px 0 rgba(0, 0, 0, 0.08)",boxShadowCard:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)",boxShadowDrawerRight:"6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05),12px 0 48px 16px rgba(0, 0, 0, 0.03)",boxShadowDrawerLeft:"-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)",boxShadowDrawerUp:"0 -6px 16px -8px rgba(0, 0, 0, 0.32), 0 -9px 28px 0 rgba(0, 0, 0, 0.2),0 -12px 48px 16px rgba(0, 0, 0, 0.12)",boxShadowDrawerDown:"0 6px 16px -8px rgba(0, 0, 0, 0.32), 0 9px 28px 0 rgba(0, 0, 0, 0.2), 0 12px 48px 16px rgba(0, 0, 0, 0.12)",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)",_tokenKey:"15a20jx",_hashId:""},mi={theme:{id:0},token:_a,hashId:""},vo=function(){var o=(0,B.useState)(mi),f=(0,be.Z)(o,1),y=f[0];return y},Ko=function(){return _a},ki=function(){return _a},$o=function(o,f){return new Vo.C(o).setAlpha(f).toRgbString()},ml=function(o,f){var y=new TinyColor(o);return y.lighten(f).toHexString()},Go=(0,J.Z)((0,J.Z)({},P),ri.rS||{}),pi=Go.useToken,pa=function(o){return{boxSizing:"border-box",margin:0,padding:0,color:o.colorText,fontSize:o.fontSize,lineHeight:o.lineHeight,listStyle:"none"}},ya=function(o){return{color:o.colorLink,outline:"none",cursor:"pointer",transition:"color ".concat(o.motionDurationSlow),"&:focus, &:hover":{color:o.colorLinkHover},"&:active":{color:o.colorLinkActive}}};function Za(l,o){var f=pi(),y=f.token,E=f.hashId,Z=f.theme,L=(0,B.useContext)(Y.ZP.ConfigContext),k=L.getPrefixCls,te=".".concat(k(),"-pro");return{wrapSSR:so({theme:Z,token:y,hashId:E,path:[l]},function(){return o((0,J.Z)((0,J.Z)({},y),{},{antCls:"."+k(),proComponentsCls:te}))}),hashId:E}}var Yo=function(o){return(0,we.Z)({},o.componentCls,{display:"flex",justifyContent:"space-between",paddingBlock:16,paddingInlineStart:8,paddingInlineEnd:16,borderBlockStart:"1px solid ".concat(o.colorSplit)})};function Xo(l){return Za("DropdownFooter",function(o){var f=(0,J.Z)((0,J.Z)({},o),{},{componentCls:".".concat(l)});return[Yo(f)]})}var Qo=function(o){var f=(0,G.YB)(),y=o.onClear,E=o.onConfirm,Z=o.disabled,L=o.footerRender,k=(0,B.useContext)(Y.ZP.ConfigContext),te=k.getPrefixCls,ce=te("pro-core-dropdown-footer"),re=Xo(ce),de=re.wrapSSR,me=re.hashId,ue=[(0,M.jsx)(H.Z,{style:{visibility:y?"visible":"hidden"},type:"link",size:"small",disabled:Z,onClick:function(Ze){y&&y(Ze),Ze.stopPropagation()},children:f.getMessage("form.lightFilter.clear","\u6E05\u9664")},"clear"),(0,M.jsx)(H.Z,{"data-type":"confirm",type:"primary",size:"small",onClick:E,disabled:Z,children:f.getMessage("form.lightFilter.confirm","\u786E\u8BA4")},"confirm")];if(L===!1||(L==null?void 0:L(E,y))===!1)return null;var he=(L==null?void 0:L(E,y))||ue;return de((0,M.jsx)("div",{className:X()(ce,me),onClick:function(Ze){return Ze.target.getAttribute("data-type")!=="confirm"&&Ze.stopPropagation()},children:he}))},Hi=m(10379),Jo=m(60446),ni=m(83844),ho=function(l){(0,Hi.Z)(f,l);var o=(0,Jo.Z)(f);function f(){var y;(0,z.Z)(this,f);for(var E=arguments.length,Z=new Array(E),L=0;L<E;L++)Z[L]=arguments[L];return y=o.call.apply(o,[this].concat(Z)),y.state={hasError:!1,errorInfo:""},y}return(0,Ne.Z)(f,[{key:"componentDidCatch",value:function(E,Z){console.log(E,Z)}},{key:"render",value:function(){return this.state.hasError?(0,M.jsx)(ni.ZP,{status:"error",title:"Something went wrong.",extra:this.state.errorInfo}):this.props.children}}],[{key:"getDerivedStateFromError",value:function(E){return{hasError:!0,errorInfo:E.message}}}]),f}(B.Component),go=m(28508),mo=m(34804),yi=function(o){var f,y;return(0,we.Z)({},o.componentCls,{display:"inline-flex",gap:"4px",alignItems:"center",height:"30px",paddingBlock:0,paddingInline:8,fontSize:o.fontSizeBase,lineHeight:"30px",borderRadius:"2px",cursor:"pointer","&:hover":{backgroundColor:"rgba(0,0,0,0.1)"},"*":{boxSizing:"border-box"},"&-active":(0,we.Z)({paddingBlock:0,paddingInline:12,backgroundColor:"rgba(0,0,0,0.04)"},"&".concat(o.componentCls,"-allow-clear:hover:not(").concat(o.componentCls,"-disabled)"),(f={},(0,we.Z)(f,"".concat(o.componentCls,"-arrow"),{display:"none"}),(0,we.Z)(f,"".concat(o.componentCls,"-close"),{display:"inline-flex"}),f)),"&-icon":(0,we.Z)({height:"12px",paddingBlock:1,paddingInline:1,color:o.colorIcon,fontSize:"12px",verticalAlign:"middle"},"&".concat(o.componentCls,"-close"),{display:"none",height:14,alignItems:"center",justifyContent:"center",width:14,color:o.colorBgBase,fontSize:8,backgroundColor:o.colorTextPlaceholder,borderRadius:"50%","&:hover":{color:o.colorIcon}}),"&-disabled":(0,we.Z)({color:o.colorTextPlaceholder,cursor:"not-allowed"},"".concat(o.componentCls,"-icon"),{color:o.colorTextPlaceholder}),"&-small":(y={height:"24px",paddingBlock:0,paddingInline:4,fontSize:o.fontSizeSM,lineHeight:"24px"},(0,we.Z)(y,"&".concat(o.componentCls,"-active"),{paddingBlock:0,paddingInline:8}),(0,we.Z)(y,"".concat(o.componentCls,"-icon"),{paddingBlock:0,paddingInline:0}),(0,we.Z)(y,"".concat(o.componentCls,"-close"),{marginBlockStart:"-2px",paddingBlock:4,paddingInline:4,fontSize:"6px"}),y),"&-bordered":{height:"32px",paddingBlock:0,paddingInline:12,border:"".concat(o.controlLineWidth," solid ").concat(o.colorBorder),borderRadius:"@border-radius-base"},"&-bordered&-small":{height:"24px",paddingBlock:0,paddingInline:8},"&-bordered&-active":{backgroundColor:o.colorBgBase}})};function po(l){return Za("FieldLabel",function(o){var f=(0,J.Z)((0,J.Z)({},o),{},{componentCls:".".concat(l)});return[yi(f)]})}var _i=function(o,f){var y,E=o.label,Z=o.onClear,L=o.value,k=o.size,te=k===void 0?"middle":k,ce=o.disabled,re=o.onLabelClick,de=o.ellipsis,me=o.placeholder,ue=o.className,he=o.formatter,Ie=o.bordered,Ze=o.allowClear,Fe=Ze===void 0?!0:Ze,xe=(0,B.useContext)(Y.ZP.ConfigContext),Pe=xe.getPrefixCls,De=Pe("pro-core-field-label"),ze=po(De),jt=ze.wrapSSR,Be=ze.hashId,Nr=(0,G.YB)(),An=(0,B.useRef)(null),Jt=(0,B.useRef)(null);(0,B.useImperativeHandle)(f,function(){return{labelRef:Jt,clearRef:An}});var ja=function(an){return he?he(an):Array.isArray(an)?an.join(","):an},zr=function(an,bt){if(bt!=null&&bt!==""&&(!Array.isArray(bt)||bt.length)){var Ue,ft,vt=an?(0,M.jsxs)("span",{onClick:re,className:"".concat(De,"-text"),children:[an,": "]}):"",lt=ja(bt);if(!de)return(0,M.jsxs)("span",{style:{display:"inline-flex",alignItems:"center"},children:[vt,ja(bt)]});var tr=41,Cr=function(){var Rr=Array.isArray(bt)&&bt.length>1,Ut=Nr.getMessage("form.lightFilter.itemUnit","\u9879");return typeof lt=="string"&&lt.length>tr&&Rr?"...".concat(bt.length).concat(Ut):""},_t=Cr();return(0,M.jsxs)("span",{title:typeof lt=="string"?lt:void 0,style:{display:"inline-flex",alignItems:"center"},children:[vt,(0,M.jsx)("span",{style:{paddingInlineStart:4},children:typeof lt=="string"?lt==null||(Ue=lt.toString())===null||Ue===void 0||(ft=Ue.substr)===null||ft===void 0?void 0:ft.call(Ue,0,tr):lt}),_t]})}return an||me};return jt((0,M.jsxs)("span",{className:X()(De,Be,"".concat(De,"-").concat(te),(y={},(0,we.Z)(y,"".concat(De,"-active"),!!L||L===0),(0,we.Z)(y,"".concat(De,"-disabled"),ce),(0,we.Z)(y,"".concat(De,"-bordered"),Ie),(0,we.Z)(y,"".concat(De,"-allow-clear"),Fe),y),ue),ref:Jt,children:[zr(E,L),(L||L===0)&&Fe&&(0,M.jsx)(go.Z,{role:"button",title:"\u6E05\u9664",className:X()("".concat(De,"-icon"),Be,"".concat(De,"-close")),onClick:function(an){Z&&!ce&&Z(),an.stopPropagation()},ref:An}),(0,M.jsx)(mo.Z,{className:X()("".concat(De,"-icon"),"".concat(De,"-arrow"))})]}))},yo=B.forwardRef(_i),Sn=m(28293),xo=m(13013),xa=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,ai=function(o){return o==="*"||o==="x"||o==="X"},bo=function(o){var f=parseInt(o,10);return isNaN(f)?o:f},xi=function(o,f){return(0,se.Z)(o)!==(0,se.Z)(f)?[String(o),String(f)]:[o,f]},Yn=function(o,f){if(ai(o)||ai(f))return 0;var y=xi(bo(o),bo(f)),E=(0,be.Z)(y,2),Z=E[0],L=E[1];return Z>L?1:Z<L?-1:0},zi=function(o,f){for(var y=0;y<Math.max(o.length,f.length);y++){var E=Yn(o[y]||"0",f[y]||"0");if(E!==0)return E}return 0},bi=function(o){if(typeof o!="string")throw new TypeError("Invalid argument expected string");var f=o.match(xa);if(!f)throw new Error("Invalid argument not valid semver ('".concat(o,"' received)"));return f.shift(),f},Si=function(o,f){var y=bi(o),E=bi(f),Z=y.pop(),L=E.pop(),k=zi(y,E);return k!==0?k:Z&&L?zi(Z.split("."),L.split(".")):Z||L?Z?-1:1:0},qo=function(o){var f;return f={},(0,we.Z)(f,"".concat(o.componentCls,"-label"),{cursor:"pointer"}),(0,we.Z)(f,"".concat(o.componentCls,"-overlay"),{minWidth:"200px",marginBlockStart:"4px",backgroundColor:o.colorBgContainer,boxShadow:o.boxShadowCard,"*":{fontFamily:o.fontFamily,boxSizing:"border-box"}}),(0,we.Z)(f,"".concat(o.componentCls,"-content"),{paddingBlock:16,paddingInline:16}),f};function So(l){return Za("FilterDropdown",function(o){var f=(0,J.Z)((0,J.Z)({},o),{},{componentCls:".".concat(l)});return[qo(f)]})}var pl=m(59250),Ui=function(o){var f=o.children,y=o.label,E=o.footer,Z=o.open,L=o.onOpenChange,k=o.disabled,te=o.onVisibleChange,ce=o.visible,re=o.footerRender,de=o.placement,me=(0,B.useContext)(Y.ZP.ConfigContext),ue=me.getPrefixCls,he=ue("pro-core-field-dropdown"),Ie=So(he),Ze=Ie.wrapSSR,Fe=Ie.hashId,xe=Si(Sn.Z,"4.23.0")>-1?{visible:Z||ce,onVisibleChange:L||te}:{open:Z||ce,onOpenChange:L||te};return Ze((0,M.jsx)(xo.Z,(0,J.Z)((0,J.Z)({disabled:k,placement:de,trigger:["click"]},xe),{},{overlay:(0,M.jsxs)("div",{className:"".concat(he,"-overlay ").concat(Fe),children:[(0,M.jsx)("div",{className:"".concat(he,"-content ").concat(Fe),children:f}),E&&(0,M.jsx)(Qo,(0,J.Z)({disabled:k,footerRender:re},E))]}),children:(0,M.jsx)("span",{className:"".concat(he,"-label ").concat(Fe),children:y})})))},Jr=m(81253),nn=m(79090),la=m(55241),ba=m(55246),el=function(o){var f="".concat(o.antCls,"-progress-bg");return(0,we.Z)({},o.componentCls,{"&-multiple":{paddingBlockStart:6,paddingBlockEnd:12,paddingInline:8},"&-progress":{"&-success":(0,we.Z)({},f,{backgroundColor:o.colorSuccess}),"&-error":(0,we.Z)({},f,{backgroundColor:o.colorError}),"&-warning":(0,we.Z)({},f,{backgroundColor:o.colorWarning})},"&-rule":{display:"flex",alignItems:"center","&-icon":{"&-default":{display:"flex",alignItems:"center",justifyContent:"center",width:"14px",height:"22px","&-circle":{width:"6px",height:"6px",backgroundColor:o.colorTextSecondary,borderRadius:"4px"}},"&-loading":{color:o.colorPrimary},"&-error":{color:o.colorError},"&-success":{color:o.colorSuccess}},"&-text":{color:o.colorText}}})};function hn(l){return Za("InlineErrorFormItem",function(o){var f=(0,J.Z)((0,J.Z)({},o),{},{componentCls:".".concat(l)});return[el(f)]})}var tl=["label","rules","name","children","popoverProps"],Co=["errorType","rules","name","popoverProps","children"],Wi={marginBlockStart:-5,marginBlockEnd:-5,marginInlineStart:0,marginInlineEnd:0},wo=function(o){var f=o.inputProps,y=o.input,E=o.extra,Z=o.errorList,L=o.popoverProps,k=(0,B.useState)(!1),te=(0,be.Z)(k,2),ce=te[0],re=te[1],de=(0,B.useState)([]),me=(0,be.Z)(de,2),ue=me[0],he=me[1],Ie=(0,B.useContext)(Y.ZP.ConfigContext),Ze=Ie.getPrefixCls,Fe=Ze(),xe=hn("".concat(Fe,"-form-item-with-help")),Pe=xe.wrapSSR,De=xe.hashId;(0,B.useEffect)(function(){f.validateStatus!=="validating"&&he(f.errors)},[f.errors,f.validateStatus]);var ze=Si(Sn.Z,"4.23.0")>-1?{open:ue.length<1?!1:ce,onOpenChange:function(Be){Be!==ce&&re(Be)}}:{visible:ue.length<1?!1:ce,onVisibleChange:function(Be){Be!==ce&&re(Be)}};return(0,M.jsx)(la.Z,(0,J.Z)((0,J.Z)((0,J.Z)({trigger:(L==null?void 0:L.trigger)||"focus",placement:(L==null?void 0:L.placement)||"topRight"},ze),{},{getPopupContainer:L==null?void 0:L.getPopupContainer,getTooltipContainer:L==null?void 0:L.getTooltipContainer,content:Pe((0,M.jsxs)("div",{className:"".concat(Fe,"-form-item-with-help ").concat(De),children:[f.validateStatus==="validating"?(0,M.jsx)(nn.Z,{}):null,Z]}))},L),{},{children:(0,M.jsxs)("div",{children:[y,E]})}),"popover")},sa=function(o){var f=o.label,y=o.rules,E=o.name,Z=o.children,L=o.popoverProps,k=(0,Jr.Z)(o,tl);return(0,M.jsx)(ba.Z.Item,(0,J.Z)((0,J.Z)({preserve:!1,name:E,rules:y,hasFeedback:!0,_internalItemRender:{mark:"pro_table_render",render:function(ce,re){return(0,M.jsx)(wo,(0,J.Z)({inputProps:ce,popoverProps:L},re))}}},k),{},{style:(0,J.Z)((0,J.Z)({},Wi),k==null?void 0:k.style),children:Z}))},rl=function(o){var f=o.errorType,y=o.rules,E=o.name,Z=o.popoverProps,L=o.children,k=(0,Jr.Z)(o,Co);return E&&(y==null?void 0:y.length)&&f==="popover"?(0,M.jsx)(sa,(0,J.Z)((0,J.Z)({name:E,rules:y,popoverProps:Z},k),{},{children:L})):(0,M.jsx)(ba.Z.Item,(0,J.Z)((0,J.Z)({rules:y},k),{},{style:(0,J.Z)((0,J.Z)({},Wi),k.style),name:E,children:L}))},ii=m(56717),Vi=m(94199),Ki=function(o){return(0,we.Z)({},o.componentCls,{display:"inline-flex",alignItems:"center",maxWidth:"100%","&-icon":{display:"block",marginInlineStart:"4px",cursor:"pointer","&:hover":{color:o.colorPrimary}},"&-title":{display:"inline-flex",flex:"1"},"&-subtitle ":{marginInlineStart:8,color:o.colorTextSecondary,fontWeight:"normal",fontSize:o.fontSize,whiteSpace:"nowrap"},"&-title-ellipsis":{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",wordBreak:"keep-all"}})};function $i(l){return Za("LabelIconTip",function(o){var f=(0,J.Z)((0,J.Z)({},o),{},{componentCls:".".concat(l)});return[Ki(f)]})}var Ci=B.memo(function(l){var o=l.label,f=l.tooltip,y=l.ellipsis,E=l.subTitle,Z=(0,B.useContext)(Y.ZP.ConfigContext),L=Z.getPrefixCls,k=L("pro-core-label-tip"),te=$i(k),ce=te.wrapSSR,re=te.hashId;if(!f&&!E)return(0,M.jsx)(M.Fragment,{children:o});var de=typeof f=="string"||B.isValidElement(f)?{title:f}:f,me=(de==null?void 0:de.icon)||(0,M.jsx)(ii.Z,{});return ce((0,M.jsxs)("div",{className:X()(k,re),onMouseDown:function(he){return he.stopPropagation()},onMouseLeave:function(he){return he.stopPropagation()},onMouseMove:function(he){return he.stopPropagation()},children:[(0,M.jsx)("div",{className:X()("".concat(k,"-title"),re,(0,we.Z)({},"".concat(k,"-title-ellipsis"),y)),children:o}),E&&(0,M.jsx)("div",{className:"".concat(k,"-subtitle ").concat(re),children:E}),f&&(0,M.jsx)(Vi.Z,(0,J.Z)((0,J.Z)({},de),{},{children:(0,M.jsx)("span",{className:"".concat(k,"-icon ").concat(re),children:me})}))]}))}),ca=B.createContext({}),To=m(27484),Xn=m.n(To),Aa=m(94787),ua=function(o){return o==null},za=m(96671),wi=m.n(za);Xn().extend(wi());var Oa={time:"HH:mm:ss",timeRange:"HH:mm:ss",date:"YYYY-MM-DD",dateWeek:"YYYY-wo",dateMonth:"YYYY-MM",dateQuarter:"YYYY-[Q]Q",dateYear:"YYYY",dateRange:"YYYY-MM-DD",dateTime:"YYYY-MM-DD HH:mm:ss",dateTimeRange:"YYYY-MM-DD HH:mm:ss"};function Ti(l){return Object.prototype.toString.call(l)==="[object Object]"}function La(l){if(Ti(l)===!1)return!1;var o=l.constructor;if(o===void 0)return!0;var f=o.prototype;return!(Ti(f)===!1||f.hasOwnProperty("isPrototypeOf")===!1)}var Sa=function(o){return!!(o==null?void 0:o._isAMomentObject)},Ua=function(o,f,y){if(!f)return o;if(Xn().isDayjs(o)||Sa(o)){if(f==="number")return o.valueOf();if(f==="string")return o.format(Oa[y]||"YYYY-MM-DD HH:mm:ss");if(typeof f=="string"&&f!=="string")return o.format(f);if(typeof f=="function")return f(o,y)}return o},Ca=function l(o,f,y,E,Z){var L={};return typeof window=="undefined"||(0,se.Z)(o)!=="object"||ua(o)||o instanceof Blob||Array.isArray(o)?o:(Object.keys(o).forEach(function(k){var te=Z?[Z,k].flat(1):[k],ce=(0,Aa.default)(y,te)||"text",re="text",de;typeof ce=="string"?re=ce:ce&&(re=ce.valueType,de=ce.dateFormat);var me=o[k];if(!(ua(me)&&E)){if(La(me)&&!Array.isArray(me)&&!Xn().isDayjs(me)&&!Sa(me)){L[k]=l(me,f,y,E,[k]);return}if(Array.isArray(me)){L[k]=me.map(function(ue,he){return Xn().isDayjs(ue)||Sa(ue)?Ua(ue,de||f,re):l(ue,f,y,E,[k,"".concat(he)])});return}L[k]=Ua(me,de||f,re)}}),L)},Wa=function(o,f){return typeof f=="function"?f(Xn()(o)):Xn()(o).format(f)},Eo=function(o,f){var y=Array.isArray(o)?o:[],E=(0,be.Z)(y,2),Z=E[0],L=E[1],k,te;Array.isArray(f)?(k=f[0],te=f[1]):(k=f,te=f);var ce=Z?Wa(Z,k):"",re=L?Wa(L,te):"",de=ce&&re?"".concat(ce," ~ ").concat(re):"";return de},Gi=m(56118),oi=function(o){var f;return!!((o==null||(f=o.valueType)===null||f===void 0?void 0:f.toString().startsWith("date"))||(o==null?void 0:o.valueType)==="select"||(o==null?void 0:o.valueEnum))},Ei=function(o,f,y){if(f.copyable||f.ellipsis){var E=f.copyable&&y?{text:y,tooltips:["",""]}:void 0,Z=oi(f),L=f.ellipsis&&y?{tooltip:Z?(0,M.jsx)("div",{className:"pro-table-tooltip-text",children:o}):y}:!1;return(0,M.jsx)(Gi.Z.Text,{style:{width:"100%",margin:0,padding:0},title:"",copyable:E,ellipsis:L,children:o})}return o};function Mo(l){if(typeof l=="function"){for(var o=arguments.length,f=new Array(o>1?o-1:0),y=1;y<o;y++)f[y-1]=arguments[y];return l.apply(void 0,f)}return l}var Yi=function(o,f,y){return f===void 0?o:Mo(o,f,y)},nr=m(7353),qr=m(92137),nl=m(8267),Rn=function(o){var f=(0,B.useRef)(null);return f.current=o,(0,B.useCallback)(function(){for(var y,E=arguments.length,Z=new Array(E),L=0;L<E;L++)Z[L]=arguments[L];return(y=f.current)===null||y===void 0?void 0:y.call.apply(y,[f].concat(Z))},[])};function Xi(l,o){var f=Rn(l),y=(0,B.useRef)(),E=(0,B.useCallback)(function(){y.current&&(clearTimeout(y.current),y.current=null)},[]),Z=(0,B.useCallback)((0,qr.Z)((0,nr.Z)().mark(function L(){var k,te,ce,re=arguments;return(0,nr.Z)().wrap(function(me){for(;;)switch(me.prev=me.next){case 0:for(k=re.length,te=new Array(k),ce=0;ce<k;ce++)te[ce]=re[ce];if(!(o===0||o===void 0)){me.next=3;break}return me.abrupt("return",f.apply(void 0,te));case 3:return E(),me.abrupt("return",new Promise(function(ue){y.current=setTimeout((0,qr.Z)((0,nr.Z)().mark(function he(){return(0,nr.Z)().wrap(function(Ze){for(;;)switch(Ze.prev=Ze.next){case 0:Ze.next=4;break;case 3:return Ze.abrupt("return");case 4:return Ze.t0=ue,Ze.next=7,f.apply(void 0,te);case 7:Ze.t1=Ze.sent,(0,Ze.t0)(Ze.t1);case 9:case"end":return Ze.stop()}},he)})),o)}));case 5:case"end":return me.stop()}},L)})),[f,E,o]);return(0,B.useEffect)(function(){return E},[E]),{run:Z,cancel:E}}var Mi=function(o){var f=(0,B.useRef)(o);return f.current=o,f};function Qi(l){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100,f=arguments.length>2?arguments[2]:void 0,y=(0,B.useState)(l),E=(0,be.Z)(y,2),Z=E[0],L=E[1],k=Mi(l);return(0,B.useEffect)(function(){var te=setTimeout(function(){L(k.current)},o);return function(){return clearTimeout(te)}},f?[o].concat((0,qe.Z)(f)):void 0),Z}var Da=m(11965);function Qn(l,o,f,y){if(l===o)return!0;if(l&&o&&(0,se.Z)(l)==="object"&&(0,se.Z)(o)==="object"){if(l.constructor!==o.constructor)return!1;var E,Z,L;if(Array.isArray(l)){if(E=l.length,E!=o.length)return!1;for(Z=E;Z--!=0;)if(!Qn(l[Z],o[Z],f,y))return!1;return!0}if(l instanceof Map&&o instanceof Map){if(l.size!==o.size)return!1;var k=(0,Da.Z)(l.entries()),te;try{for(k.s();!(te=k.n()).done;)if(Z=te.value,!o.has(Z[0]))return!1}catch(he){k.e(he)}finally{k.f()}var ce=(0,Da.Z)(l.entries()),re;try{for(ce.s();!(re=ce.n()).done;)if(Z=re.value,!Qn(Z[1],o.get(Z[0]),f,y))return!1}catch(he){ce.e(he)}finally{ce.f()}return!0}if(l instanceof Set&&o instanceof Set){if(l.size!==o.size)return!1;var de=(0,Da.Z)(l.entries()),me;try{for(de.s();!(me=de.n()).done;)if(Z=me.value,!o.has(Z[0]))return!1}catch(he){de.e(he)}finally{de.f()}return!0}if(ArrayBuffer.isView(l)&&ArrayBuffer.isView(o)){if(E=l.length,E!=o.length)return!1;for(Z=E;Z--!=0;)if(l[Z]!==o[Z])return!1;return!0}if(l.constructor===RegExp)return l.source===o.source&&l.flags===o.flags;if(l.valueOf!==Object.prototype.valueOf&&l.valueOf)return l.valueOf()===o.valueOf();if(l.toString!==Object.prototype.toString&&l.toString)return l.toString()===o.toString();if(L=Object.keys(l),E=L.length,E!==Object.keys(o).length)return!1;for(Z=E;Z--!=0;)if(!Object.prototype.hasOwnProperty.call(o,L[Z]))return!1;for(Z=E;Z--!=0;){var ue=L[Z];if(!(f==null?void 0:f.includes(ue))&&!(ue==="_owner"&&l.$$typeof)&&!Qn(l[ue],o[ue],f,y))return y&&console.log(ue),!1}return!0}return l!==l&&o!==o}var Jn=function(o,f,y){return Qn(o,f,y)};function li(l,o){var f=(0,B.useRef)();return Jn(l,f.current,o)||(f.current=l),f.current}function Ji(l,o,f){(0,B.useEffect)(l,li(o||[],f))}function Po(l,o,f,y){var E=Xi((0,qr.Z)((0,nr.Z)().mark(function Z(){return(0,nr.Z)().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:l();case 1:case"end":return k.stop()}},Z)})),y||16);(0,B.useEffect)(function(){E.run()},li(o||[],f))}var qi=m(34155),eo=typeof qi!="undefined"&&qi.versions!=null&&qi.versions.node!=null,Pi=function(){return typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.matchMedia!="undefined"&&!eo};function Ro(l,o){var f=typeof l.pageName=="string"?l.title:o;(0,B.useEffect)(function(){Pi()&&f&&(document.title=f)},[l.title,f])}var al=m(29405),Ri=0;function wa(l){var o=(0,B.useState)(function(){return l.proFieldKey?l.proFieldKey.toString():(Ri+=1,Ri.toString())}),f=(0,be.Z)(o,1),y=f[0],E=(0,B.useRef)(y),Z=function(){var ce=(0,qr.Z)((0,nr.Z)().mark(function re(){var de,me;return(0,nr.Z)().wrap(function(he){for(;;)switch(he.prev=he.next){case 0:return he.next=2,(de=l.request)===null||de===void 0?void 0:de.call(l,l.params,l);case 2:return me=he.sent,he.abrupt("return",me);case 4:case"end":return he.stop()}},re)}));return function(){return ce.apply(this,arguments)}}();(0,B.useEffect)(function(){return function(){Ri+=1}},[]);var L=(0,al.ZP)([E.current,l.params],Z,{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),k=L.data,te=L.error;return[k||te]}var to=function(o){var f=(0,B.useRef)();return(0,B.useEffect)(function(){f.current=o}),f.current},Io=function(o){var f=!1;return(typeof o=="string"&&o.startsWith("date")&&!o.endsWith("Range")||o==="select"||o==="time")&&(f=!0),f};function Ii(l){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(l)}var Va=function(o){if(!o||!o.startsWith("http"))return!1;try{var f=new URL(o);return!!f}catch(y){return!1}},Ta=function(){for(var o={},f=arguments.length,y=new Array(f),E=0;E<f;E++)y[E]=arguments[E];for(var Z=y.length,L,k=0;k<Z;k+=1)for(L in y[k])y[k].hasOwnProperty(L)&&((0,se.Z)(o[L])==="object"&&(0,se.Z)(y[k][L])==="object"&&o[L]!==void 0&&o[L]!==null&&!Array.isArray(o[L])&&!Array.isArray(y[k][L])?o[L]=(0,J.Z)((0,J.Z)({},o[L]),y[k][L]):o[L]=y[k][L]);return o},Zi=0,ro=function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:21;if(typeof window=="undefined"||!window.crypto)return(Zi+=1).toFixed(0);for(var f="",y=crypto.getRandomValues(new Uint8Array(o));o--;){var E=63&y[o];f+=E<36?E.toString(36):E<62?(E-26).toString(36).toUpperCase():E<63?"_":"-"}return f},il=function(){return typeof window=="undefined"?ro():window.crypto&&window.crypto.randomUUID&&typeof crypto.randomUUID=="function"?crypto.randomUUID():ro()},Zo=function(o){if(o&&o!==!0)return o},Ao=function(o){var f={};if(Object.keys(o||{}).forEach(function(y){o[y]!==void 0&&(f[y]=o[y])}),!(Object.keys(f).length<1))return f},Oo=function(o){var f={};return Object.keys(o||{}).forEach(function(y){var E;Array.isArray(o[y])&&((E=o[y])===null||E===void 0?void 0:E.length)===0||o[y]!==void 0&&(f[y]=o[y])}),f},Lo=function(o){return!!(o==null?void 0:o._isAMomentObject)},Ka=function l(o,f){return ua(o)||Xn().isDayjs(o)||Lo(o)?Lo(o)?Xn()(o):o:Array.isArray(o)?o.map(function(y){return l(y,f)}):typeof o=="number"?Xn()(o):Xn()(o,f)},no=["colon","dependencies","extra","getValueFromEvent","getValueProps","hasFeedback","help","htmlFor","initialValue","noStyle","label","labelAlign","labelCol","name","preserve","normalize","required","rules","shouldUpdate","trigger","validateFirst","validateStatus","validateTrigger","valuePropName","wrapperCol","hidden","addonBefore","addonAfter"];function Ea(l){var o={};return no.forEach(function(f){l[f]!==void 0&&(o[f]=l[f])}),o}var ao="valueType request plain renderFormItem render text formItemProps valueEnum",Ai="fieldProps isDefaultDom groupProps contentRender submitterProps submitter";function Oi(l){var o="".concat(ao," ").concat(Ai).split(/[\s\n]+/),f={};return Object.keys(l||{}).forEach(function(y){o.includes(y)||(f[y]=l[y])}),f}var da=m(20059);function Do(l){return(0,se.Z)(l)!=="object"?!1:l===null?!0:!(B.isValidElement(l)||l.constructor===RegExp||l instanceof Map||l instanceof Set||l instanceof HTMLElement||l instanceof Blob||l instanceof File||Array.isArray(l))}var ol=function(o,f){var y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,E=Object.keys(f).reduce(function(k,te){var ce=f[te];return ua(ce)||(k[te]=ce),k},{});if(Object.keys(E).length<1||typeof window=="undefined"||(0,se.Z)(o)!=="object"||ua(o)||o instanceof Blob)return o;var Z=Array.isArray(o)?[]:{},L=function k(te,ce){var re=Array.isArray(te),de=re?[]:{};return te==null||te===void 0?de:(Object.keys(te).forEach(function(me){var ue=ce?[ce,me].flat(1):[me].flat(1),he=te[me],Ie=(0,Aa.default)(E,ue),Ze=function Pe(De){return Array.isArray(De)&&De.forEach(function(ze,jt){!ze||(typeof ze=="function"&&(he[jt]=ze(he,me,te)),(0,se.Z)(ze)==="object"&&!Array.isArray(ze)&&Object.keys(ze).forEach(function(Be){if(typeof ze[Be]=="function"){var Nr=ze[Be](te[me][jt][Be],me,te);he[jt][Be]=(0,se.Z)(Nr)==="object"?Nr[Be]:Nr}}),(0,se.Z)(ze)==="object"&&Array.isArray(ze)&&Pe(ze))}),me},Fe=function(){var De=typeof Ie=="function"?Ie==null?void 0:Ie(he,me,te):Ze(Ie);if(Array.isArray(De)){de=(0,da.default)(de,De,he);return}(0,se.Z)(De)==="object"&&!Array.isArray(Z)?Z=(0,J.Z)((0,J.Z)({},Z),De):(0,se.Z)(De)==="object"&&Array.isArray(Z)?de=(0,J.Z)((0,J.Z)({},de),De):De&&(de=(0,da.default)(de,[De],he))};if(Ie&&typeof Ie=="function"&&Fe(),typeof window!="undefined"){if(Do(he)){var xe=k(he,ue);if(Object.keys(xe).length<1)return;de=(0,da.default)(de,[me],xe);return}Fe()}}),y?de:te)};return Z=Array.isArray(o)&&Array.isArray(Z)?(0,qe.Z)(L(o)):Ta({},L(o),Z),Z},ll=m(99809),si=m(48086),sl=m(24565),jo=m(84164),ci=m(60869),cl=m(45520),yl=["map_row_parentKey"],Fo=["map_row_parentKey","map_row_key"],fa=["map_row_key"],ui=function(o){return(si.default.warn||si.default.warning)(o)},Cn=function(o){return Array.isArray(o)?o.join(","):o};function $a(l,o){var f,y=l.getRowKey,E=l.row,Z=l.data,L=l.childrenColumnName,k=(f=Cn(l.key))===null||f===void 0?void 0:f.toString(),te=new Map;function ce(de,me,ue){de.forEach(function(he,Ie){var Ze=(ue||0)*10+Ie,Fe=y(he,Ze).toString();he&&(0,se.Z)(he)==="object"&&L in he&&ce(he[L]||[],Fe,Ze);var xe=(0,J.Z)((0,J.Z)({},he),{},{map_row_key:Fe,children:void 0,map_row_parentKey:me});delete xe.children,me||delete xe.map_row_parentKey,te.set(Fe,xe)})}o==="top"&&te.set(k,(0,J.Z)((0,J.Z)({},te.get(k)),E)),ce(Z),o==="update"&&te.set(k,(0,J.Z)((0,J.Z)({},te.get(k)),E)),o==="delete"&&te.delete(k);var re=function(me){var ue=new Map,he=[],Ie=function(){var Fe=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;me.forEach(function(xe){if(xe.map_row_parentKey&&!xe.map_row_key){var Pe=xe.map_row_parentKey,De=(0,Jr.Z)(xe,yl);if(ue.has(Pe)||ue.set(Pe,[]),Fe){var ze;(ze=ue.get(Pe))===null||ze===void 0||ze.push(De)}}})};return Ie(o==="top"),me.forEach(function(Ze){if(Ze.map_row_parentKey&&Ze.map_row_key){var Fe,xe=Ze.map_row_parentKey,Pe=Ze.map_row_key,De=(0,Jr.Z)(Ze,Fo);ue.has(Pe)&&(De[L]=ue.get(Pe)),ue.has(xe)||ue.set(xe,[]),(Fe=ue.get(xe))===null||Fe===void 0||Fe.push(De)}}),Ie(o==="update"),me.forEach(function(Ze){if(!Ze.map_row_parentKey){var Fe=Ze.map_row_key,xe=(0,Jr.Z)(Ze,fa);if(Fe&&ue.has(Fe)){var Pe=(0,J.Z)((0,J.Z)({},xe),{},(0,we.Z)({},L,ue.get(Fe)));he.push(Pe);return}he.push(xe)}}),he};return re(te)}function In(l){var o=l.recordKey,f=l.onSave,y=l.row,E=l.children,Z=l.newLineConfig,L=l.editorType,k=l.tableName,te=(0,B.useContext)(ca),ce=ba.Z.useFormInstance(),re=(0,ci.default)(!1),de=(0,be.Z)(re,2),me=de[0],ue=de[1];return(0,M.jsxs)("a",{onClick:function(){var he=(0,qr.Z)((0,nr.Z)().mark(function Ze(Fe){var xe,Pe,De,ze,jt,Be,Nr,An,Jt;return(0,nr.Z)().wrap(function(zr){for(;;)switch(zr.prev=zr.next){case 0:return Fe.stopPropagation(),Fe.preventDefault(),zr.prev=2,Pe=L==="Map",De=[k,Array.isArray(o)?o[0]:o].map(function(Ma){return Ma==null?void 0:Ma.toString()}).flat(1).filter(Boolean),ue(!0),zr.next=8,ce.validateFields(De,{recursive:!0});case 8:return ze=((xe=te.getFieldFormatValue)===null||xe===void 0?void 0:xe.call(te,De))||ce.getFieldValue(De),Array.isArray(o)&&o.length>1&&(jt=(0,ll.Z)(o),Be=jt.slice(1),Nr=(0,Aa.default)(ze,Be),(0,da.default)(ze,Be,Nr)),An=Pe?(0,da.default)({},De,ze,!0):ze,zr.next=13,f==null?void 0:f(o,Ta({},y,An),y,Z);case 13:return Jt=zr.sent,ue(!1),zr.abrupt("return",Jt);case 18:return zr.prev=18,zr.t0=zr.catch(2),console.log(zr.t0),ue(!1),zr.abrupt("return",null);case 23:case"end":return zr.stop()}},Ze,null,[[2,18]])}));function Ie(Ze){return he.apply(this,arguments)}return Ie}(),children:[me?(0,M.jsx)(nn.Z,{style:{marginInlineEnd:8}}):null,E||"\u4FDD\u5B58"]},"save")}var Li=function(o){var f=o.recordKey,y=o.onDelete,E=o.row,Z=o.children,L=o.deletePopconfirmMessage,k=(0,ci.default)(function(){return!1}),te=(0,be.Z)(k,2),ce=te[0],re=te[1],de=Rn((0,qr.Z)((0,nr.Z)().mark(function me(){var ue;return(0,nr.Z)().wrap(function(Ie){for(;;)switch(Ie.prev=Ie.next){case 0:return Ie.prev=0,re(!0),Ie.next=4,y==null?void 0:y(f,E);case 4:return ue=Ie.sent,re(!1),Ie.abrupt("return",ue);case 9:return Ie.prev=9,Ie.t0=Ie.catch(0),console.log(Ie.t0),re(!1),Ie.abrupt("return",null);case 14:case"end":return Ie.stop()}},me,null,[[0,9]])})));return Z!==!1?(0,M.jsx)(sl.Z,{title:L,onConfirm:function(){return de()},children:(0,M.jsxs)("a",{children:[ce?(0,M.jsx)(nn.Z,{style:{marginInlineEnd:8}}):null,Z||"\u5220\u9664"]})},"delete"):null},Tt=function(o){var f=o.recordKey,y=o.tableName,E=o.newLineConfig,Z=o.editorType,L=o.onCancel,k=o.cancelEditable,te=o.row,ce=o.cancelText,re=(0,B.useContext)(ca),de=ba.Z.useFormInstance();return(0,M.jsx)("a",{onClick:function(){var me=(0,qr.Z)((0,nr.Z)().mark(function he(Ie){var Ze,Fe,xe,Pe,De,ze;return(0,nr.Z)().wrap(function(Be){for(;;)switch(Be.prev=Be.next){case 0:return Ie.stopPropagation(),Ie.preventDefault(),Fe=Z==="Map",xe=[y,f].flat(1).filter(Boolean),Pe=((Ze=re.getFieldFormatValue)===null||Ze===void 0?void 0:Ze.call(re,xe))||de.getFieldValue(xe),De=Fe?(0,da.default)({},xe,Pe):Pe,Be.next=8,L==null?void 0:L(f,De,te,E);case 8:return ze=Be.sent,k(f),de.setFieldsValue((0,we.Z)({},f,Fe?(0,Aa.default)(te,xe):te)),Be.abrupt("return",ze);case 12:case"end":return Be.stop()}},he)}));function ue(he){return me.apply(this,arguments)}return ue}(),children:ce||"\u53D6\u6D88"},"cancel")};function Qt(l,o){var f=o.recordKey,y=o.newLineConfig,E=o.saveText,Z=o.deleteText;return[(0,M.jsx)(In,(0,J.Z)((0,J.Z)({},o),{},{row:l,children:E}),"save"+f),(y==null?void 0:y.options.recordKey)!==f?(0,M.jsx)(Li,(0,J.Z)((0,J.Z)({},o),{},{row:l,children:Z}),"delete"+f):null,(0,M.jsx)(Tt,(0,J.Z)((0,J.Z)({},o),{},{row:l}),"cancel"+f)]}function Bo(l){var o=(0,B.useState)(void 0),f=(0,be.Z)(o,2),y=f[0],E=f[1],Z=(0,B.useRef)(new Map),L=(0,B.useRef)(void 0);Po(function(){var bt=new Map,Ue=function ft(vt,lt){vt==null||vt.forEach(function(tr,Cr){var _t,Kt=lt==null?Cr.toString():lt+"_"+Cr.toString();bt.set(Kt,Cn(l.getRowKey(tr,-1))),bt.set((_t=Cn(l.getRowKey(tr,-1)))===null||_t===void 0?void 0:_t.toString(),Kt),l.childrenColumnName&&tr[l.childrenColumnName]&&ft(tr[l.childrenColumnName],Kt)})};Ue(l.dataSource),Z.current=bt},[l.dataSource]),L.current=y;var k=l.type||"single",te=(0,jo.Z)(l.dataSource,"children",l.getRowKey),ce=(0,be.Z)(te,1),re=ce[0],de=(0,ci.default)([],{value:l.editableKeys,onChange:l.onChange?function(bt){var Ue;l==null||(Ue=l.onChange)===null||Ue===void 0||Ue.call(l,bt.filter(function(ft){return ft!==void 0}),bt.map(function(ft){return re(ft)}).filter(function(ft){return ft!==void 0}))}:void 0}),me=(0,be.Z)(de,2),ue=me[0],he=me[1],Ie=(0,B.useMemo)(function(){var bt=k==="single"?ue==null?void 0:ue.slice(0,1):ue;return new Set(bt)},[(ue||[]).join(","),k]),Ze=to(ue),Fe=Rn(function(bt){var Ue,ft,vt,lt,tr=(Ue=l.getRowKey(bt,bt.index))===null||Ue===void 0||(ft=Ue.toString)===null||ft===void 0?void 0:ft.call(Ue),Cr=(vt=l.getRowKey(bt,-1))===null||vt===void 0||(lt=vt.toString)===null||lt===void 0?void 0:lt.call(vt),_t=ue.map(function(Ut){return Ut==null?void 0:Ut.toString()}),Kt=(Ze==null?void 0:Ze.map(function(Ut){return Ut==null?void 0:Ut.toString()}))||[],Rr=l.tableName&&!!(Kt==null?void 0:Kt.includes(Cr))||!!(Kt==null?void 0:Kt.includes(tr));return{recordKey:Cr,isEditable:l.tableName&&(_t==null?void 0:_t.includes(Cr))||(_t==null?void 0:_t.includes(tr)),preIsEditable:Rr}}),xe=Rn(function(bt){return Ie.size>0&&k==="single"&&l.onlyOneLineEditorAlertMessage!==!1?(ui(l.onlyOneLineEditorAlertMessage||"\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C"),!1):(Ie.add(bt),he(Array.from(Ie)),!0)}),Pe=Rn(function(){var bt=(0,qr.Z)((0,nr.Z)().mark(function Ue(ft,vt){var lt,tr;return(0,nr.Z)().wrap(function(_t){for(;;)switch(_t.prev=_t.next){case 0:if(lt=Cn(ft).toString(),tr=Z.current.get(lt),!(!Ie.has(lt)&&tr&&(vt!=null?vt:!0)&&l.tableName)){_t.next=5;break}return Pe(tr,!1),_t.abrupt("return");case 5:return y&&y.options.recordKey===ft&&E(void 0),Ie.delete(lt),Ie.delete(Cn(ft)),he(Array.from(Ie)),_t.abrupt("return",!0);case 10:case"end":return _t.stop()}},Ue)}));return function(Ue,ft){return bt.apply(this,arguments)}}()),De=Xi((0,qr.Z)((0,nr.Z)().mark(function bt(){var Ue,ft,vt,lt,tr=arguments;return(0,nr.Z)().wrap(function(_t){for(;;)switch(_t.prev=_t.next){case 0:for(ft=tr.length,vt=new Array(ft),lt=0;lt<ft;lt++)vt[lt]=tr[lt];(Ue=l.onValuesChange)===null||Ue===void 0||Ue.call.apply(Ue,[l].concat(vt));case 2:case"end":return _t.stop()}},bt)})),64),ze=Rn(function(bt,Ue){var ft;if(!!l.onValuesChange){var vt=l.dataSource;ue.forEach(function(Kt){if((y==null?void 0:y.options.recordKey)!==Kt){var Rr=Kt.toString(),Ut=(0,Aa.default)(Ue,[l.tableName||"",Rr].flat(1).filter(function(Pa){return Pa||Pa===0}));!Ut||(vt=$a({data:vt,getRowKey:l.getRowKey,row:Ut,key:Rr,childrenColumnName:l.childrenColumnName||"children"},"update"))}});var lt=l.tableName?(0,Aa.default)(bt,[l.tableName||""].flat(1)):bt,tr=(ft=Object.keys(lt||{}).pop())===null||ft===void 0?void 0:ft.toString(),Cr=(0,J.Z)((0,J.Z)({},y==null?void 0:y.defaultValue),(0,Aa.default)(Ue,[l.tableName||"",tr.toString()].flat(1).filter(function(Kt){return Kt||Kt===0}))),_t=Z.current.has(Cn(tr))?vt.find(function(Kt,Rr){var Ut,Pa=(Ut=l.getRowKey(Kt,Rr))===null||Ut===void 0?void 0:Ut.toString();return Pa===tr}):Cr;De.run(_t||Cr,vt)}}),jt=Rn(function(bt,Ue){if((Ue==null?void 0:Ue.parentKey)&&!Z.current.has(Cn(Ue==null?void 0:Ue.parentKey).toString()))return console.warn("can't find record by key",Ue==null?void 0:Ue.parentKey),!1;if(L.current&&l.onlyAddOneLineAlertMessage!==!1)return ui(l.onlyAddOneLineAlertMessage||"\u53EA\u80FD\u65B0\u589E\u4E00\u884C"),!1;if(Ie.size>0&&k==="single"&&l.onlyOneLineEditorAlertMessage!==!1)return ui(l.onlyOneLineEditorAlertMessage||"\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C"),!1;var ft=l.getRowKey(bt,-1);if(!ft)throw(0,cl.noteOnce)(!!ft,`\u8BF7\u8BBE\u7F6E recordCreatorProps.record \u5E76\u8FD4\u56DE\u4E00\u4E2A\u552F\u4E00\u7684key  
  https://procomponents.ant.design/components/editable-table#editable-%E6%96%B0%E5%BB%BA%E8%A1%8C`),new Error("\u8BF7\u8BBE\u7F6E recordCreatorProps.record \u5E76\u8FD4\u56DE\u4E00\u4E2A\u552F\u4E00\u7684key");if(Ie.add(ft),he(Array.from(Ie)),(Ue==null?void 0:Ue.newRecordType)==="dataSource"||l.tableName){var vt,lt={data:l.dataSource,getRowKey:l.getRowKey,row:(0,J.Z)((0,J.Z)({},bt),{},{map_row_parentKey:(Ue==null?void 0:Ue.parentKey)?(vt=Cn(Ue==null?void 0:Ue.parentKey))===null||vt===void 0?void 0:vt.toString():void 0}),key:ft,childrenColumnName:l.childrenColumnName||"children"};l.setDataSource($a(lt,(Ue==null?void 0:Ue.position)==="top"?"top":"update"))}else E({defaultValue:bt,options:(0,J.Z)((0,J.Z)({},Ue),{},{recordKey:ft})});return!0}),Be=(0,G.YB)(),Nr=(l==null?void 0:l.saveText)||Be.getMessage("editableTable.action.save","\u4FDD\u5B58"),An=(l==null?void 0:l.deleteText)||Be.getMessage("editableTable.action.delete","\u5220\u9664"),Jt=(l==null?void 0:l.cancelText)||Be.getMessage("editableTable.action.cancel","\u53D6\u6D88"),ja=Rn(function(){var bt=(0,qr.Z)((0,nr.Z)().mark(function Ue(ft,vt,lt,tr){var Cr,_t,Kt,Rr,Ut,Pa,No;return(0,nr.Z)().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return Rr=tr||L.current||{},Ut=Rr.options,r.next=3,l==null||(Cr=l.onSave)===null||Cr===void 0?void 0:Cr.call(l,ft,vt,lt,tr);case 3:if(Pa=r.sent,Pe(ft),!(!(Ut==null?void 0:Ut.parentKey)&&(Ut==null?void 0:Ut.recordKey)===ft)){r.next=8;break}return(Ut==null?void 0:Ut.position)==="top"?l.setDataSource([vt].concat((0,qe.Z)(l.dataSource))):l.setDataSource([].concat((0,qe.Z)(l.dataSource),[vt])),r.abrupt("return",Pa);case 8:return No={data:l.dataSource,getRowKey:l.getRowKey,row:Ut?(0,J.Z)((0,J.Z)({},vt),{},{map_row_parentKey:(_t=Cn((Kt=Ut==null?void 0:Ut.parentKey)!==null&&Kt!==void 0?Kt:""))===null||_t===void 0?void 0:_t.toString()}):vt,key:ft,childrenColumnName:l.childrenColumnName||"children"},l.setDataSource($a(No,(Ut==null?void 0:Ut.position)==="top"?"top":"update")),r.abrupt("return",Pa);case 11:case"end":return r.stop()}},Ue)}));return function(Ue,ft,vt,lt){return bt.apply(this,arguments)}}()),zr=Rn(function(){var bt=(0,qr.Z)((0,nr.Z)().mark(function Ue(ft,vt){var lt,tr,Cr;return(0,nr.Z)().wrap(function(Kt){for(;;)switch(Kt.prev=Kt.next){case 0:return tr={data:l.dataSource,getRowKey:l.getRowKey,row:vt,key:ft,childrenColumnName:l.childrenColumnName||"children"},Kt.next=3,l==null||(lt=l.onDelete)===null||lt===void 0?void 0:lt.call(l,ft,vt);case 3:return Cr=Kt.sent,Kt.next=6,Pe(ft);case 6:return l.setDataSource($a(tr,"delete")),Kt.abrupt("return",Cr);case 8:case"end":return Kt.stop()}},Ue)}));return function(Ue,ft){return bt.apply(this,arguments)}}()),Ma=Rn(function(){var bt=(0,qr.Z)((0,nr.Z)().mark(function Ue(ft,vt,lt,tr){var Cr,_t;return(0,nr.Z)().wrap(function(Rr){for(;;)switch(Rr.prev=Rr.next){case 0:return Rr.next=2,l==null||(Cr=l.onCancel)===null||Cr===void 0?void 0:Cr.call(l,ft,vt,lt,tr);case 2:return _t=Rr.sent,Rr.abrupt("return",_t);case 4:case"end":return Rr.stop()}},Ue)}));return function(Ue,ft,vt,lt){return bt.apply(this,arguments)}}()),an=function(Ue){var ft=l.getRowKey(Ue,Ue.index),vt={saveText:Nr,cancelText:Jt,deleteText:An,addEditRecord:jt,recordKey:ft,cancelEditable:Pe,index:Ue.index,tableName:l.tableName,newLineConfig:y,onCancel:Ma,onDelete:zr,onSave:ja,editableKeys:ue,setEditableRowKeys:he,deletePopconfirmMessage:l.deletePopconfirmMessage||"".concat(Be.getMessage("deleteThisLine","\u5220\u9664\u6B64\u884C"),"?")},lt=Qt(Ue,vt);return l.actionRender?l.actionRender(Ue,vt,{save:lt[0],delete:lt[1],cancel:lt[2]}):lt};return{editableKeys:ue,setEditableRowKeys:he,isEditable:Fe,actionRender:an,startEditable:xe,cancelEditable:Pe,addEditRecord:jt,newLineRecord:y,preEditableKeys:Ze,onValuesChange:ze}}var Nn=function(o){return(si.default.warn||si.default.warning)(o)};function di(l){var o=l.data,f=l.row;return(0,J.Z)((0,J.Z)({},o),f)}function Zn(l){var o=l.type||"single",f=(0,G.YB)(),y=(0,ci.default)([],{value:l.editableKeys,onChange:l.onChange?function(Fe){var xe;l==null||(xe=l.onChange)===null||xe===void 0||xe.call(l,Fe,l.dataSource)}:void 0}),E=(0,be.Z)(y,2),Z=E[0],L=E[1],k=(0,B.useMemo)(function(){var Fe=o==="single"?Z==null?void 0:Z.slice(0,1):Z;return new Set(Fe)},[(Z||[]).join(","),o]),te=(0,B.useCallback)(function(Fe){return!!(Z==null?void 0:Z.includes(Cn(Fe)))},[(Z||[]).join(",")]),ce=function(xe){return k.size>0&&o==="single"?(Nn(l.onlyOneLineEditorAlertMessage||f.getMessage("editableTable.onlyOneLineEditor","\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C")),!1):(k.add(Cn(xe)),L(Array.from(k)),!0)},re=function(xe){return k.delete(Cn(xe)),L(Array.from(k)),!0},de=function(){var Fe=(0,qr.Z)((0,nr.Z)().mark(function xe(Pe,De,ze,jt){var Be,Nr;return(0,nr.Z)().wrap(function(Jt){for(;;)switch(Jt.prev=Jt.next){case 0:return Jt.next=2,l==null||(Be=l.onCancel)===null||Be===void 0?void 0:Be.call(l,Pe,De,ze,jt);case 2:if(Nr=Jt.sent,Nr!==!1){Jt.next=5;break}return Jt.abrupt("return",!1);case 5:return Jt.abrupt("return",!0);case 6:case"end":return Jt.stop()}},xe)}));return function(Pe,De,ze,jt){return Fe.apply(this,arguments)}}(),me=function(){var Fe=(0,qr.Z)((0,nr.Z)().mark(function xe(Pe,De,ze){var jt,Be,Nr;return(0,nr.Z)().wrap(function(Jt){for(;;)switch(Jt.prev=Jt.next){case 0:return Jt.next=2,l==null||(jt=l.onSave)===null||jt===void 0?void 0:jt.call(l,Pe,De,ze);case 2:if(Be=Jt.sent,Be!==!1){Jt.next=5;break}return Jt.abrupt("return",!1);case 5:return re(Pe),Nr={data:l.dataSource,row:De,key:Pe,childrenColumnName:l.childrenColumnName||"children"},l.setDataSource(di(Nr)),Jt.abrupt("return",!0);case 9:case"end":return Jt.stop()}},xe)}));return function(Pe,De,ze){return Fe.apply(this,arguments)}}(),ue=f.getMessage("editableTable.action.save","\u4FDD\u5B58"),he=f.getMessage("editableTable.action.delete","\u5220\u9664"),Ie=f.getMessage("editableTable.action.cancel","\u53D6\u6D88"),Ze=(0,B.useCallback)(function(Fe,xe){var Pe=(0,J.Z)({recordKey:Fe,cancelEditable:re,onCancel:de,onSave:me,editableKeys:Z,setEditableRowKeys:L,saveText:ue,cancelText:Ie,deleteText:he,deletePopconfirmMessage:"".concat(f.getMessage("deleteThisLine","\u5220\u9664\u6B64\u884C"),"?"),editorType:"Map"},xe),De=Qt(l.dataSource,Pe);return l.actionRender?l.actionRender(l.dataSource,Pe,{save:De[0],delete:De[1],cancel:De[2]}):De},[Z&&Z.join(","),l.dataSource]);return{editableKeys:Z,setEditableRowKeys:L,isEditable:te,actionRender:Ze,startEditable:ce,cancelEditable:re}}function ul(l){var o=B.useRef(!1),f=B.useState(l),y=(0,be.Z)(f,2),E=y[0],Z=y[1];B.useEffect(function(){return o.current=!1,function(){o.current=!0}},[]);function L(k,te){te&&o.current||Z(k)}return[E,L]}},11965:function(Ct,ve,m){"use strict";m.d(ve,{Z:function(){return M}});var P=m(82961);function M(G,Y){var H=typeof Symbol!="undefined"&&G[Symbol.iterator]||G["@@iterator"];if(!H){if(Array.isArray(G)||(H=(0,P.Z)(G))||Y&&G&&typeof G.length=="number"){H&&(G=H);var oe=0,X=function(){};return{s:X,n:function(){return oe>=G.length?{done:!0}:{done:!1,value:G[oe++]}},e:function(qe){throw qe},f:X}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var B,J=!0,we=!1;return{s:function(){H=H.call(G)},n:function(){var qe=H.next();return J=qe.done,qe},e:function(qe){we=!0,B=qe},f:function(){try{J||H.return==null||H.return()}finally{if(we)throw B}}}}},62506:function(Ct,ve){"use strict";function m(P){for(var M=0,G,Y=0,H=P.length;H>=4;++Y,H-=4)G=P.charCodeAt(Y)&255|(P.charCodeAt(++Y)&255)<<8|(P.charCodeAt(++Y)&255)<<16|(P.charCodeAt(++Y)&255)<<24,G=(G&65535)*1540483477+((G>>>16)*59797<<16),G^=G>>>24,M=(G&65535)*1540483477+((G>>>16)*59797<<16)^(M&65535)*1540483477+((M>>>16)*59797<<16);switch(H){case 3:M^=(P.charCodeAt(Y+2)&255)<<16;case 2:M^=(P.charCodeAt(Y+1)&255)<<8;case 1:M^=P.charCodeAt(Y)&255,M=(M&65535)*1540483477+((M>>>16)*59797<<16)}return M^=M>>>13,M=(M&65535)*1540483477+((M>>>16)*59797<<16),((M^M>>>15)>>>0).toString(36)}ve.Z=m},70883:function(){},77935:function(){},81262:function(){},12001:function(){},6234:function(Ct){"use strict";var ve=typeof BigInt64Array!="undefined";Ct.exports=function m(P,M){if(P===M)return!0;if(P&&M&&typeof P=="object"&&typeof M=="object"){if(P.constructor!==M.constructor)return!1;var G,Y,H;if(Array.isArray(P)){if(G=P.length,G!=M.length)return!1;for(Y=G;Y--!=0;)if(!m(P[Y],M[Y]))return!1;return!0}if(P instanceof Map&&M instanceof Map){if(P.size!==M.size)return!1;for(Y of P.entries())if(!M.has(Y[0]))return!1;for(Y of P.entries())if(!m(Y[1],M.get(Y[0])))return!1;return!0}if(P instanceof Set&&M instanceof Set){if(P.size!==M.size)return!1;for(Y of P.entries())if(!M.has(Y[0]))return!1;return!0}if(ArrayBuffer.isView(P)&&ArrayBuffer.isView(M)){if(G=P.length,G!=M.length)return!1;for(Y=G;Y--!=0;)if(P[Y]!==M[Y])return!1;return!0}if(P.constructor===RegExp)return P.source===M.source&&P.flags===M.flags;if(P.valueOf!==Object.prototype.valueOf)return P.valueOf()===M.valueOf();if(P.toString!==Object.prototype.toString)return P.toString()===M.toString();if(H=Object.keys(P),G=H.length,G!==Object.keys(M).length)return!1;for(Y=G;Y--!=0;)if(!Object.prototype.hasOwnProperty.call(M,H[Y]))return!1;for(Y=G;Y--!=0;){var oe=H[Y];if(!(oe==="_owner"&&P.$$typeof)&&!m(P[oe],M[oe]))return!1}return!0}return P!==P&&M!==M}},17474:function(Ct,ve){(function(m,P){P(ve)})(this,function(m){"use strict";/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var P=function(){return P=Object.assign||function(r){for(var i,c=1,g=arguments.length;c<g;c++){i=arguments[c];for(var h in i)Object.prototype.hasOwnProperty.call(i,h)&&(r[h]=i[h])}return r},P.apply(this,arguments)};function M(t,r,i,c){function g(h){return h instanceof i?h:new i(function(x){x(h)})}return new(i||(i=Promise))(function(h,x){function C(N){try{R(c.next(N))}catch(K){x(K)}}function F(N){try{R(c.throw(N))}catch(K){x(K)}}function R(N){N.done?h(N.value):g(N.value).then(C,F)}R((c=c.apply(t,r||[])).next())})}function G(t,r){var i={label:0,sent:function(){if(h[0]&1)throw h[1];return h[1]},trys:[],ops:[]},c,g,h,x;return x={next:C(0),throw:C(1),return:C(2)},typeof Symbol=="function"&&(x[Symbol.iterator]=function(){return this}),x;function C(R){return function(N){return F([R,N])}}function F(R){if(c)throw new TypeError("Generator is already executing.");for(;i;)try{if(c=1,g&&(h=R[0]&2?g.return:R[0]?g.throw||((h=g.return)&&h.call(g),0):g.next)&&!(h=h.call(g,R[1])).done)return h;switch(g=0,h&&(R=[R[0]&2,h.value]),R[0]){case 0:case 1:h=R;break;case 4:return i.label++,{value:R[1],done:!1};case 5:i.label++,g=R[1],R=[0];continue;case 7:R=i.ops.pop(),i.trys.pop();continue;default:if(h=i.trys,!(h=h.length>0&&h[h.length-1])&&(R[0]===6||R[0]===2)){i=0;continue}if(R[0]===3&&(!h||R[1]>h[0]&&R[1]<h[3])){i.label=R[1];break}if(R[0]===6&&i.label<h[1]){i.label=h[1],h=R;break}if(h&&i.label<h[2]){i.label=h[2],i.ops.push(R);break}h[2]&&i.ops.pop(),i.trys.pop();continue}R=r.call(t,i)}catch(N){R=[6,N],g=0}finally{c=h=0}if(R[0]&5)throw R[1];return{value:R[0]?R[1]:void 0,done:!0}}}function Y(t){var r=typeof Symbol=="function"&&Symbol.iterator,i=r&&t[r],c=0;if(i)return i.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&c>=t.length&&(t=void 0),{value:t&&t[c++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function H(t,r){var i=typeof Symbol=="function"&&t[Symbol.iterator];if(!i)return t;var c=i.call(t),g,h=[],x;try{for(;(r===void 0||r-- >0)&&!(g=c.next()).done;)h.push(g.value)}catch(C){x={error:C}}finally{try{g&&!g.done&&(i=c.return)&&i.call(c)}finally{if(x)throw x.error}}return h}function oe(t,r,i){if(i||arguments.length===2)for(var c=0,g=r.length,h;c<g;c++)(h||!(c in r))&&(h||(h=Array.prototype.slice.call(r,0,c)),h[c]=r[c]);return t.concat(h||Array.prototype.slice.call(r))}var X={UI_GET_DATA:"ui-get-data",UI_GET_ACTIVE_TAB_INFO:"ui-get-active-tab-info",UI_SUBSCRIBE_TO_CHANGES:"ui-subscribe-to-changes",UI_UNSUBSCRIBE_FROM_CHANGES:"ui-unsubscribe-from-changes",UI_CHANGE_SETTINGS:"ui-change-settings",UI_SET_THEME:"ui-set-theme",UI_SET_SHORTCUT:"ui-set-shortcut",UI_TOGGLE_URL:"ui-toggle-url",UI_MARK_NEWS_AS_READ:"ui-mark-news-as-read",UI_LOAD_CONFIG:"ui-load-config",UI_APPLY_DEV_DYNAMIC_THEME_FIXES:"ui-apply-dev-dynamic-theme-fixes",UI_RESET_DEV_DYNAMIC_THEME_FIXES:"ui-reset-dev-dynamic-theme-fixes",UI_APPLY_DEV_INVERSION_FIXES:"ui-apply-dev-inversion-fixes",UI_RESET_DEV_INVERSION_FIXES:"ui-reset-dev-inversion-fixes",UI_APPLY_DEV_STATIC_THEMES:"ui-apply-dev-static-themes",UI_RESET_DEV_STATIC_THEMES:"ui-reset-dev-static-themes",UI_SAVE_FILE:"ui-save-file",UI_REQUEST_EXPORT_CSS:"ui-request-export-css",BG_CHANGES:"bg-changes",BG_ADD_CSS_FILTER:"bg-add-css-filter",BG_ADD_STATIC_THEME:"bg-add-static-theme",BG_ADD_SVG_FILTER:"bg-add-svg-filter",BG_ADD_DYNAMIC_THEME:"bg-add-dynamic-theme",BG_EXPORT_CSS:"bg-export-css",BG_UNSUPPORTED_SENDER:"bg-unsupported-sender",BG_CLEAN_UP:"bg-clean-up",BG_RELOAD:"bg-reload",BG_FETCH_RESPONSE:"bg-fetch-response",BG_UI_UPDATE:"bg-ui-update",BG_CSS_UPDATE:"bg-css-update",CS_COLOR_SCHEME_CHANGE:"cs-color-scheme-change",CS_FRAME_CONNECT:"cs-frame-connect",CS_FRAME_FORGET:"cs-frame-forget",CS_FRAME_FREEZE:"cs-frame-freeze",CS_FRAME_RESUME:"cs-frame-resume",CS_EXPORT_CSS_RESPONSE:"cs-export-css-response",CS_FETCH:"cs-fetch"},B=typeof navigator=="undefined"?"some useragent":navigator.userAgent.toLowerCase(),J=typeof navigator=="undefined"?"some platform":navigator.platform.toLowerCase(),we=B.includes("chrome")||B.includes("chromium"),be=B.includes("thunderbird"),qe=B.includes("firefox")||B.includes("librewolf")||be;B.includes("vivaldi"),B.includes("yabrowser"),B.includes("opr")||B.includes("opera"),B.includes("edg");var se=B.includes("safari")&&!we,Qe=J.startsWith("win"),He=J.startsWith("mac");B.includes("mobile");var At=typeof ShadowRoot=="function",ot=typeof MediaQueryList=="function"&&typeof MediaQueryList.prototype.addEventListener=="function";(function(){var t=B.match(/chrom[e|ium]\/([^ ]+)/);return t&&t[1]?t[1]:""})();var Et=function(){try{return document.querySelector(":defined"),!0}catch(t){return!1}}();typeof globalThis!="undefined"&&globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.getManifest&&globalThis.chrome.runtime.getManifest().manifest_version;function p(t,r,i){return M(this,void 0,void 0,function(){var c;return G(this,function(g){switch(g.label){case 0:return[4,fetch(t,{cache:"force-cache",credentials:"omit",referrer:i})];case 1:if(c=g.sent(),qe&&r==="text/css"&&t.startsWith("moz-extension://")&&t.endsWith(".css"))return[2,c];if(r&&!c.headers.get("Content-Type").startsWith(r))throw new Error("Mime type mismatch when loading "+t);if(!c.ok)throw new Error("Unable to load "+t+" "+c.status+" "+c.statusText);return[2,c]}})})}function fe(t,r){return M(this,void 0,void 0,function(){var i;return G(this,function(c){switch(c.label){case 0:return[4,p(t,r)];case 1:return i=c.sent(),[4,u(i)];case 2:return[2,c.sent()]}})})}function u(t){return M(this,void 0,void 0,function(){var r,i;return G(this,function(c){switch(c.label){case 0:return[4,t.blob()];case 1:return r=c.sent(),[4,new Promise(function(g){var h=new FileReader;h.onloadend=function(){return g(h.result)},h.readAsDataURL(r)})];case 2:return i=c.sent(),[2,i]}})})}var U=function(t){return M(void 0,void 0,void 0,function(){return G(this,function(r){return[2,Promise.reject(new Error(["Embedded Dark Reader cannot access a cross-origin resource",t,"Overview your URLs and CORS policies or use","`DarkReader.setFetchMethod(fetch: (url) => Promise<Response>))`.","See if using `DarkReader.setFetchMethod(window.fetch)`","before `DarkReader.enable()` works."].join(" ")))]})})},We=U;function Nt(t){t?We=t:We=U}function Se(t){return M(this,void 0,void 0,function(){return G(this,function(r){switch(r.label){case 0:return[4,We(t)];case 1:return[2,r.sent()]}})})}function Ke(){if(typeof chrome=="undefined")return;typeof window!="undefined"&&!window.chrome&&(window.chrome={}),typeof chrome!="undefined"&&!chrome.runtime&&(chrome.runtime={});var t=new Set;function r(){for(var h=[],x=0;x<arguments.length;x++)h[x]=arguments[x];return M(this,void 0,void 0,function(){var C,F,R,N,K,Te,Me;return G(this,function(ae){switch(ae.label){case 0:if(!(h[0]&&h[0].type===X.CS_FETCH))return[3,8];C=h[0].id,ae.label=1;case 1:return ae.trys.push([1,7,,8]),F=h[0].data,R=F.url,N=F.responseType,[4,Se(R)];case 2:return K=ae.sent(),N!=="data-url"?[3,4]:[4,u(K)];case 3:return Te=ae.sent(),[3,6];case 4:return[4,K.text()];case 5:Te=ae.sent(),ae.label=6;case 6:return t.forEach(function($){return $({type:X.BG_FETCH_RESPONSE,data:Te,error:null,id:C})}),[3,8];case 7:return Me=ae.sent(),console.error(Me),t.forEach(function($){return $({type:X.BG_FETCH_RESPONSE,data:null,error:Me,id:C})}),[3,8];case 8:return[2]}})})}function i(h){t.add(h)}if(typeof chrome.runtime.sendMessage=="function"){var c=chrome.runtime.sendMessage;chrome.runtime.sendMessage=function(){for(var h=[],x=0;x<arguments.length;x++)h[x]=arguments[x];r.apply(void 0,oe([],H(h),!1)),c.apply(chrome.runtime,h)}}else chrome.runtime.sendMessage=r;if(chrome.runtime.onMessage||(chrome.runtime.onMessage={}),typeof chrome.runtime.onMessage.addListener=="function"){var g=chrome.runtime.onMessage.addListener;chrome.runtime.onMessage.addListener=function(){for(var h=[],x=0;x<arguments.length;x++)h[x]=arguments[x];i(h[0]),g.apply(chrome.runtime.onMessage,h)}}else chrome.runtime.onMessage.addListener=function(){for(var h=[],x=0;x<arguments.length;x++)h[x]=arguments[x];return i(h[0])}}Ke();var O={cssFilter:"cssFilter",svgFilter:"svgFilter",staticTheme:"staticTheme",dynamicTheme:"dynamicTheme"},wt={darkScheme:{background:"#181a1b",text:"#e8e6e3"},lightScheme:{background:"#dcdad7",text:"#181a1b"}},w={mode:1,brightness:100,contrast:100,grayscale:0,sepia:0,useFont:!1,fontFamily:He?"Helvetica Neue":Qe?"Segoe UI":"Open Sans",textStroke:0,engine:O.dynamicTheme,stylesheet:"",darkSchemeBackgroundColor:wt.darkScheme.background,darkSchemeTextColor:wt.darkScheme.text,lightSchemeBackgroundColor:wt.lightScheme.background,lightSchemeTextColor:wt.lightScheme.text,scrollbarColor:He?"":"auto",selectionColor:"auto",styleSystemControls:!0,lightColorScheme:"Default",darkColorScheme:"Default"};function gt(t){return t.length!=null}function Mt(t,r){var i,c;if(gt(t))for(var g=0,h=t.length;g<h;g++)r(t[g]);else try{for(var x=Y(t),C=x.next();!C.done;C=x.next()){var F=C.value;r(F)}}catch(R){i={error:R}}finally{try{C&&!C.done&&(c=x.return)&&c.call(x)}finally{if(i)throw i.error}}}function xr(t,r){Mt(r,function(i){return t.push(i)})}function wr(t){for(var r=[],i=0,c=t.length;i<c;i++)r.push(t[i]);return r}function Ft(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r]}function ke(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r]}function cr(t){var r=!1,i=null,c,g=function(){for(var x=[],C=0;C<arguments.length;C++)x[C]=arguments[C];c=x,i?r=!0:(t.apply(void 0,oe([],H(c),!1)),i=requestAnimationFrame(function(){i=null,r&&(t.apply(void 0,oe([],H(c),!1)),r=!1)}))},h=function(){cancelAnimationFrame(i),r=!1,i=null};return Object.assign(g,{cancel:h})}function jr(){var t=[],r=null;function i(){for(var h;h=t.shift();)h();r=null}function c(h){t.push(h),r||(r=requestAnimationFrame(i))}function g(){t.splice(0),cancelAnimationFrame(r),r=null}return{add:c,cancel:g}}function Vt(t){var r=0;return t.seconds&&(r+=t.seconds*1e3),t.minutes&&(r+=t.minutes*60*1e3),t.hours&&(r+=t.hours*60*60*1e3),t.days&&(r+=t.days*24*60*60*1e3),r}function er(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function hr(t,r,i){i===void 0&&(i=Function.prototype);var c=10,g=Vt({seconds:2}),h=Vt({seconds:10}),x=t.previousSibling,C=t.parentNode;if(!C)throw new Error("Unable to watch for node position: parent element not found");if(r==="prev-sibling"&&!x)throw new Error("Unable to watch for node position: there is no previous sibling");var F=0,R=null,N=null,K=cr(function(){if(!N){F++;var pe=Date.now();if(R==null)R=pe;else if(F>=c){if(pe-R<h){ke("Node position watcher paused: retry in "+g+"ms",t,x),N=setTimeout(function(){R=null,F=0,N=null,K()},g);return}R=pe,F=1}if(r==="parent"&&x&&x.parentNode!==C){ke("Unable to restore node position: sibling parent changed",t,x,C),ae();return}if(r==="prev-sibling"){if(x.parentNode==null){ke("Unable to restore node position: sibling was removed",t,x,C),ae();return}x.parentNode!==C&&(ke("Style was moved to another parent",t,x,C),q(x.parentNode))}ke("Restoring node position",t,x,C),C.insertBefore(t,x?x.nextSibling:C.firstChild),Te.takeRecords(),i&&i()}}),Te=new MutationObserver(function(){(r==="parent"&&t.parentNode!==C||r==="prev-sibling"&&t.previousSibling!==x)&&K()}),Me=function(){Te.observe(C,{childList:!0})},ae=function(){clearTimeout(N),Te.disconnect(),K.cancel()},$=function(){Te.takeRecords()},q=function(pe){C=pe,ae(),Me()};return Me(),{run:Me,stop:ae,skip:$}}function It(t,r){if(t!=null)for(var i=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:function(g){return g.shadowRoot==null?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT}}),c=t.shadowRoot?i.currentNode:i.nextNode();c!=null;c=i.nextNode())r(c),It(c.shadowRoot,r)}function ge(){return typeof document=="undefined"?!1:document.readyState==="complete"||document.readyState==="interactive"}var st=new Set;function kt(t){st.add(t)}function Xt(t){st.delete(t)}function ct(){return document.readyState==="complete"}var ut=new Set;function mt(t){ut.add(t)}function Yt(){ut.clear()}if(!ge()){var Zt=function(){ge()&&(st.forEach(function(t){return t()}),st.clear(),ct()&&(document.removeEventListener("readystatechange",Zt),ut.forEach(function(t){return t()}),ut.clear()))};typeof document!="undefined"&&document.addEventListener("readystatechange",Zt)}var Ht=1e3;function rr(t){if(t.length>Ht)return!0;for(var r=0,i=0;i<t.length;i++)if(r+=t[i].addedNodes.length,r>Ht)return!0;return!1}function br(t){var r=new Set,i=new Set,c=new Set;t.forEach(function(x){Mt(x.addedNodes,function(C){C instanceof Element&&C.isConnected&&r.add(C)}),Mt(x.removedNodes,function(C){C instanceof Element&&(C.isConnected?(c.add(C),r.delete(C)):i.add(C))})});var g=[],h=[];return r.forEach(function(x){r.has(x.parentElement)&&g.push(x)}),i.forEach(function(x){i.has(x.parentElement)&&h.push(x)}),g.forEach(function(x){return r.delete(x)}),h.forEach(function(x){return i.delete(x)}),{additions:r,moves:c,deletions:i}}var Ar=new Map,Kr=new WeakMap;function Mr(t,r){var i,c,g;if(Ar.has(t))i=Ar.get(t),c=Kr.get(i);else{var h=!1,x=!1;i=new MutationObserver(function(C){if(rr(C))!h||ge()?c.forEach(function(R){var N=R.onHugeMutations;return N(t)}):x||(g=function(){return c.forEach(function(R){var N=R.onHugeMutations;return N(t)})},kt(g),x=!0),h=!0;else{var F=br(C);c.forEach(function(R){var N=R.onMinorMutations;return N(F)})}}),i.observe(t,{childList:!0,subtree:!0}),Ar.set(t,i),c=new Set,Kr.set(i,c)}return c.add(r),{disconnect:function(){c.delete(r),g&&Xt(g),c.size===0&&(i.disconnect(),Kr.delete(i),Ar.delete(t))}}}var cn,ur=new Map;function pt(t){return cn||(cn=document.createElement("a")),cn.href=t,cn.href}function ar(t,r){r===void 0&&(r=null);var i=""+t+(r?";"+r:"");if(ur.has(i))return ur.get(i);if(r){var c=new URL(t,pt(r));return ur.set(i,c),c}var g=new URL(pt(t));return ur.set(t,g),g}function Or(t,r){if(r.match(/^data\\?\:/))return r;if(/^\/\//.test(r))return""+location.protocol+r;var i=ar(t),c=ar(r,i.href);return c.href}function Pn(t){if(t.startsWith("data:"))return!0;var r=ar(t);return r.protocol!==location.protocol||r.hostname!==location.hostname||r.port!==location.port?!1:r.pathname===location.pathname}function un(t,r,i){Mt(t,function(c){if(c.selectorText)r(c);else if(c.href)try{un(c.styleSheet.cssRules,r,i)}catch(C){Ft("Found a non-loaded link."),i&&i()}else if(c.media){var g=Array.from(c.media),h=g.some(function(C){return C.startsWith("screen")||C.startsWith("all")||C.startsWith("(")}),x=g.some(function(C){return C.startsWith("print")||C.startsWith("speech")});(h||!x)&&un(c.cssRules,r,i)}else c.conditionText?CSS.supports(c.conditionText)&&un(c.cssRules,r,i):ke("CSSRule type not supported",c)})}var Q=["background","border","border-color","border-bottom","border-left","border-right","border-top","outline","outline-color"],$e=se?Q.map(function(t){var r=new RegExp(t+":\\s*(.*?)\\s*;");return[t,r]}):null;function V(t,r){Mt(t,function(c){var g=t.getPropertyValue(c).trim();!g||r(c,g)});var i=t.cssText;i.includes("var(")&&(se?$e.forEach(function(c){var g=H(c,2),h=g[0],x=g[1],C=i.match(x);if(C&&C[1]){var F=C[1].trim();r(h,F)}}):Q.forEach(function(c){var g=t.getPropertyValue(c);g&&g.includes("var(")&&r(c,g)}))}var Ae=/url\((('.+?')|(".+?")|([^\)]*?))\)/g,Re=/@import\s*(url\()?(('.+?')|(".+?")|([^\)]*?))\)? ?(screen)?;?/g;function Ce(t){return t.replace(/^url\((.*)\)$/,"$1").trim().replace(/^"(.*)"$/,"$1").replace(/^'(.*)'$/,"$1")}function _e(t){var r=ar(t);return""+r.origin+r.pathname.replace(/\?.*$/,"").replace(/(\/)([^\/]+)$/i,"$1")}function rt(t,r){return t.replace(Ae,function(i){var c=Ce(i);try{return'url("'+Or(r,c)+'")'}catch(g){return ke("Not able to replace relative URL with Absolute URL, skipping"),i}})}var Lt=/\/\*[\s\S]*?\*\//g;function Ee(t){return t.replace(Lt,"")}var yt=/@font-face\s*{[^}]*}/g;function tt(t){return t.replace(yt,"")}function Dt(t){var r=t.h,i=t.s,c=t.l,g=t.a,h=g===void 0?1:g;if(i===0){var x=H([c,c,c].map(function(pe){return Math.round(pe*255)}),3),C=x[0],F=x[1],R=x[2];return{r:C,g:R,b:F,a:h}}var N=(1-Math.abs(2*c-1))*i,K=N*(1-Math.abs(r/60%2-1)),Te=c-N/2,Me=H((r<60?[N,K,0]:r<120?[K,N,0]:r<180?[0,N,K]:r<240?[0,K,N]:r<300?[K,0,N]:[N,0,K]).map(function(pe){return Math.round((pe+Te)*255)}),3),ae=Me[0],$=Me[1],q=Me[2];return{r:ae,g:$,b:q,a:h}}function Sr(t){var r=t.r,i=t.g,c=t.b,g=t.a,h=g===void 0?1:g,x=r/255,C=i/255,F=c/255,R=Math.max(x,C,F),N=Math.min(x,C,F),K=R-N,Te=(R+N)/2;if(K===0)return{h:0,s:0,l:Te,a:h};var Me=(R===x?(C-F)/K%6:R===C?(F-x)/K+2:(x-C)/K+4)*60;Me<0&&(Me+=360);var ae=K/(1-Math.abs(2*Te-1));return{h:Me,s:ae,l:Te,a:h}}function ir(t,r){r===void 0&&(r=0);var i=t.toFixed(r);if(r===0)return i;var c=i.indexOf(".");if(c>=0){var g=i.match(/0+$/);if(g)return g.index===c+1?i.substring(0,c):i.substring(0,g.index)}return i}function Lr(t){var r=t.r,i=t.g,c=t.b,g=t.a;return g!=null&&g<1?"rgba("+ir(r)+", "+ir(i)+", "+ir(c)+", "+ir(g,2)+")":"rgb("+ir(r)+", "+ir(i)+", "+ir(c)+")"}function dr(t){var r=t.r,i=t.g,c=t.b,g=t.a;return"#"+(g!=null&&g<1?[r,i,c,Math.round(g*255)]:[r,i,c]).map(function(h){return""+(h<16?"0":"")+h.toString(16)}).join("")}function et(t){var r=t.h,i=t.s,c=t.l,g=t.a;return g!=null&&g<1?"hsla("+ir(r)+", "+ir(i*100)+"%, "+ir(c*100)+"%, "+ir(g,2)+")":"hsl("+ir(r)+", "+ir(i*100)+"%, "+ir(c*100)+"%)"}var Yr=/^rgba?\([^\(\)]+\)$/,_r=/^hsla?\([^\(\)]+\)$/,S=/^#[0-9a-f]+$/i;function _(t){var r=t.trim().toLowerCase();if(r.match(Yr))return dt(r);if(r.match(_r))return tn(r);if(r.match(S))return rn(r);if(ra.has(r))return Xa(r);if(ka.has(r))return Wn(r);if(t==="transparent")return{r:0,g:0,b:0,a:0};throw new Error("Unable to parse "+t)}function z(t){var r=[],i=0,c=!1,g=t.indexOf("(");t=t.substring(g+1,t.length-1);for(var h=0;h<t.length;h++){var x=t[h];x>="0"&&x<="9"||x==="."||x==="+"||x==="-"?c=!0:c&&(x===" "||x===",")?(r.push(t.substring(i,h)),c=!1,i=h+1):c||(i=h+1)}return c&&r.push(t.substring(i,t.length)),r}function Ne(t,r,i){var c=z(t),g=Object.entries(i),h=c.map(function(x){return x.trim()}).map(function(x,C){var F,R=g.find(function(N){var K=H(N,1),Te=K[0];return x.endsWith(Te)});return R?F=parseFloat(x.substring(0,x.length-R[0].length))/R[1]*r[C]:F=parseFloat(x),r[C]>1?Math.round(F):F});return h}var it=[255,255,255,1],Xe={"%":100};function dt(t){var r=H(Ne(t,it,Xe),4),i=r[0],c=r[1],g=r[2],h=r[3],x=h===void 0?1:h;return{r:i,g:c,b:g,a:x}}var xt=[360,1,1,1],yn={"%":100,deg:360,rad:2*Math.PI,turn:1};function tn(t){var r=H(Ne(t,xt,yn),4),i=r[0],c=r[1],g=r[2],h=r[3],x=h===void 0?1:h;return Dt({h:i,s:c,l:g,a:x})}function rn(t){var r=t.substring(1);switch(r.length){case 3:case 4:{var i=H([0,1,2].map(function(F){return parseInt(""+r[F]+r[F],16)}),3),c=i[0],g=i[1],h=i[2],x=r.length===3?1:parseInt(""+r[3]+r[3],16)/255;return{r:c,g,b:h,a:x}}case 6:case 8:{var C=H([0,2,4].map(function(Te){return parseInt(r.substring(Te,Te+2),16)}),3),c=C[0],g=C[1],h=C[2],x=r.length===6?1:parseInt(r.substring(6,8),16)/255;return{r:c,g,b:h,a:x}}}throw new Error("Unable to parse "+t)}function Xa(t){var r=ra.get(t);return{r:r>>16&255,g:r>>8&255,b:r>>0&255,a:1}}function Wn(t){var r=ka.get(t);return{r:r>>16&255,g:r>>8&255,b:r>>0&255,a:1}}var Bi=function(t){return t>="0"&&t<="9"},Qa=function(t){return Math.floor(Math.log10(t))+1};function Ni(t){for(var r=0,i=function(R,N,K){t=t.substring(0,R)+K+t.substring(N)},c=function(){for(var R=0,N=1;N<4;N++){var K=t[r+N];if(K===" ")break;if(Bi(K))R*=10,R+=Number(K);else break}var Te=Qa(R);r+=Te;var Me=t[r+1];if(Me==="%")return r++,R};(r=t.indexOf("calc("))!==0;){var g=r;r+=4;var h=c();if(!h||t[r+1]!==" ")break;r++;var x=t[r+1];if(x!=="+"&&x!=="-"||(r++,t[r+1]!==" "))break;r++;var C=c();if(!C)break;var F=void 0;x==="+"?F=h+C+"%":F=h-C+"%",i(g,r+2,F)}return t}var ra=new Map(Object.entries({aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgrey:11119017,darkgreen:25600,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,grey:8421504,green:32768,greenyellow:11403055,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgrey:13882323,lightgreen:9498256,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074})),ka=new Map(Object.entries({ActiveBorder:3906044,ActiveCaption:0,AppWorkspace:11184810,Background:6513614,ButtonFace:16777215,ButtonHighlight:15329769,ButtonShadow:10461343,ButtonText:0,CaptionText:0,GrayText:8355711,Highlight:11720703,HighlightText:0,InactiveBorder:16777215,InactiveCaption:16777215,InactiveCaptionText:0,InfoBackground:16514245,InfoText:0,Menu:16185078,MenuText:16777215,Scrollbar:11184810,ThreeDDarkShadow:0,ThreeDFace:12632256,ThreeDHighlight:16777215,ThreeDLightShadow:16777215,ThreeDShadow:0,Window:15527148,WindowFrame:11184810,WindowText:0,"-webkit-focus-ring-color":15046400}).map(function(t){var r=H(t,2),i=r[0],c=r[1];return[i.toLowerCase(),c]}));function Fr(t,r,i,c,g){return(t-r)*(g-c)/(i-r)+c}function Fn(t,r,i){return Math.min(i,Math.max(r,t))}function Vn(t,r){for(var i=[],c=0,g=t.length;c<g;c++){i[c]=[];for(var h=0,x=r[0].length;h<x;h++){for(var C=0,F=0,R=t[0].length;F<R;F++)C+=t[c][F]*r[F][h];i[c][h]=C}}return i}function dn(t,r,i){i===void 0&&(i=0);for(var c=[],g;g=t.exec(r);)c.push(g[i]);return c}function lo(t){function r(N){return N.replace(/^\s+/,"")}function i(N){return N===0?"":" ".repeat(4*N)}if(t.length<5e4)for(var c=/[^{}]+{\s*}/;c.test(t);)t=t.replace(c,"");for(var g=t.replace(/\s{2,}/g," ").replace(/\{/g,`{
`).replace(/\}/g,`
}
`).replace(/\;(?![^\(|\"]*(\)|\"))/g,`;
`).replace(/\,(?![^\(|\"]*(\)|\"))/g,`,
`).replace(/\n\s*\n/g,`
`).split(`
`),h=0,x=[],C=0,F=g.length;C<F;C++){var R=g[C]+`
`;R.includes("{")?x.push(i(h++)+r(R)):R.includes("}")?x.push(i(--h)+r(R)):x.push(i(h)+r(R))}return x.join("").trim()}function fn(t,r){r===void 0&&(r=0);for(var i=t.length,c=0,g=-1,h=r;h<i;h++)if(c===0){var x=t.indexOf("(",h);if(x<0)break;g=x,c++,h=x}else{var C=t.indexOf(")",h);if(C<0)break;var x=t.indexOf("(",h);if(x<0||C<x){if(c--,c===0)return{start:g,end:C+1};h=C}else c++,h=x}return null}function vn(t){var r=$r.identity();return t.sepia!==0&&(r=Vn(r,$r.sepia(t.sepia/100))),t.grayscale!==0&&(r=Vn(r,$r.grayscale(t.grayscale/100))),t.contrast!==100&&(r=Vn(r,$r.contrast(t.contrast/100))),t.brightness!==100&&(r=Vn(r,$r.brightness(t.brightness/100))),t.mode===1&&(r=Vn(r,$r.invertNHue())),r}function Br(t,r){var i=H(t,3),c=i[0],g=i[1],h=i[2],x=[[c/255],[g/255],[h/255],[1],[1]],C=Vn(r,x);return[0,1,2].map(function(F){return Fn(Math.round(C[F][0]*255),0,255)})}var $r={identity:function(){return[[1,0,0,0,0],[0,1,0,0,0],[0,0,1,0,0],[0,0,0,1,0],[0,0,0,0,1]]},invertNHue:function(){return[[.333,-.667,-.667,0,1],[-.667,.333,-.667,0,1],[-.667,-.667,.333,0,1],[0,0,0,1,0],[0,0,0,0,1]]},brightness:function(t){return[[t,0,0,0,0],[0,t,0,0,0],[0,0,t,0,0],[0,0,0,1,0],[0,0,0,0,1]]},contrast:function(t){var r=(1-t)/2;return[[t,0,0,0,r],[0,t,0,0,r],[0,0,t,0,r],[0,0,0,1,0],[0,0,0,0,1]]},sepia:function(t){return[[.393+.607*(1-t),.769-.769*(1-t),.189-.189*(1-t),0,0],[.349-.349*(1-t),.686+.314*(1-t),.168-.168*(1-t),0,0],[.272-.272*(1-t),.534-.534*(1-t),.131+.869*(1-t),0,0],[0,0,0,1,0],[0,0,0,0,1]]},grayscale:function(t){return[[.2126+.7874*(1-t),.7152-.7152*(1-t),.0722-.0722*(1-t),0,0],[.2126-.2126*(1-t),.7152+.2848*(1-t),.0722-.0722*(1-t),0,0],[.2126-.2126*(1-t),.7152-.7152*(1-t),.0722+.9278*(1-t),0,0],[0,0,0,1,0],[0,0,0,0,1]]}};function xn(t){var r=t.mode===1,i=r?"darkSchemeBackgroundColor":"lightSchemeBackgroundColor";return t[i]}function na(t){var r=t.mode===1,i=r?"darkSchemeTextColor":"lightSchemeTextColor";return t[i]}var Xr=new Map,Pr=new Map;function Kn(t){if(Pr.has(t))return Pr.get(t);var r=_(t),i=Sr(r);return Pr.set(t,i),i}function bn(){Xr.clear(),Pr.clear()}var Ja=["r","g","b","a"],aa=["mode","brightness","contrast","grayscale","sepia","darkSchemeBackgroundColor","darkSchemeTextColor","lightSchemeBackgroundColor","lightSchemeTextColor"];function ia(t,r){var i="";return Ja.forEach(function(c){i+=t[c]+";"}),aa.forEach(function(c){i+=r[c]+";"}),i}function Bn(t,r,i,c,g){var h;Xr.has(i)?h=Xr.get(i):(h=new Map,Xr.set(i,h));var x=ia(t,r);if(h.has(x))return h.get(x);var C=Sr(t),F=c==null?null:Kn(c),R=g==null?null:Kn(g),N=i(C,F,R),K=Dt(N),Te=K.r,Me=K.g,ae=K.b,$=K.a,q=vn(r),pe=H(Br([Te,Me,ae],q),3),Je=pe[0],nt=pe[1],Ot=pe[2],Pt=$===1?dr({r:Je,g:nt,b:Ot}):Lr({r:Je,g:nt,b:Ot,a:$});return h.set(x,Pt),Pt}function $n(t){return t}function qa(t,r){return Bn(t,r,$n)}function ga(t,r){var i=xn(r),c=na(r);return Bn(t,r,Ha,c,i)}function Ha(t,r,i){var c=t.h,g=t.s,h=t.l,x=t.a,C=h<.5,F;if(C)F=h<.2||g<.12;else{var R=c>200&&c<280;F=g<.24||h>.8&&R}var N=c,K=h;F&&(C?(N=r.h,K=r.s):(N=i.h,K=i.s));var Te=Fr(h,0,1,r.l,i.l);return{h:N,s:K,l:Te,a:x}}var so=.4;function vl(t,r){var i=t.h,c=t.s,g=t.l,h=t.a,x=g<.5,C=i>200&&i<280,F=c<.12||g>.8&&C;if(x){var R=Fr(g,0,.5,0,so);if(F){var N=r.h,K=r.s;return{h:N,s:K,l:R,a:h}}return{h:i,s:c,l:R,a:h}}var Te=Fr(g,.5,1,so,r.l);if(F){var Me=r.h,K=r.s;return{h:Me,s:K,l:Te,a:h}}var ae=i,$=i>60&&i<180;if($){var q=i>120;q?ae=Fr(i,120,180,135,180):ae=Fr(i,60,120,60,105)}return{h:ae,s:c,l:Te,a:h}}function Qr(t,r){if(r.mode===0)return ga(t,r);var i=xn(r);return Bn(t,P(P({},r),{mode:0}),vl,i)}var gi=.55;function oa(t){return Fr(t,205,245,205,220)}function Wo(t,r){var i=t.h,c=t.s,g=t.l,h=t.a,x=g>.5,C=g<.2||c<.24,F=!C&&i>205&&i<245;if(x){var R=Fr(g,.5,1,gi,r.l);if(C){var N=r.h,K=r.s;return{h:N,s:K,l:R,a:h}}var Te=i;return F&&(Te=oa(i)),{h:Te,s:c,l:R,a:h}}if(C){var Me=r.h,K=r.s,ae=Fr(g,0,.5,r.l,gi);return{h:Me,s:K,l:ae,a:h}}var $=i,q;return F?($=oa(i),q=Fr(g,0,.5,r.l,Math.min(1,gi+.05))):q=Fr(g,0,.5,r.l,gi),{h:$,s:c,l:q,a:h}}function Gn(t,r){if(r.mode===0)return ga(t,r);var i=na(r);return Bn(t,P(P({},r),{mode:0}),Wo,i)}function co(t,r,i){var c=t.h,g=t.s,h=t.l,x=t.a,C=h<.5,F=h<.2||g<.24,R=c,N=g;F&&(C?(R=r.h,N=r.s):(R=i.h,N=i.s));var K=Fr(h,0,1,.5,.2);return{h:R,s:N,l:K,a:x}}function ei(t,r){if(r.mode===0)return ga(t,r);var i=na(r),c=xn(r);return Bn(t,P(P({},r),{mode:0}),co,i,c)}function uo(t,r){return Qr(t,r)}function ma(t,r){return Qr(t,r)}function hl(t){var r=[];return r.push('*:not(pre, pre *, code, .far, .fa, .glyphicon, [class*="vjs-"], .fab, .fa-github, .fas, .material-icons, .icofont, .typcn, mu, [class*="mu-"], .glyphicon, .icon) {'),t.useFont&&t.fontFamily&&r.push("  font-family: "+t.fontFamily+" !important;"),t.textStroke>0&&(r.push("  -webkit-text-stroke: "+t.textStroke+"px !important;"),r.push("  text-stroke: "+t.textStroke+"px !important;")),r.push("}"),r.join(`
`)}var fo;(function(t){t[t.light=0]="light",t[t.dark=1]="dark"})(fo||(fo={}));function ti(t){var r=[];return t.mode===fo.dark&&r.push("invert(100%) hue-rotate(180deg)"),t.brightness!==100&&r.push("brightness("+t.brightness+"%)"),t.contrast!==100&&r.push("contrast("+t.contrast+"%)"),t.grayscale!==0&&r.push("grayscale("+t.grayscale+"%)"),t.sepia!==0&&r.push("sepia("+t.sepia+"%)"),r.length===0?null:r.join(" ")}function gl(t){return t.slice(0,4).map(function(r){return r.map(function(i){return i.toFixed(3)}).join(" ")}).join(" ")}function Vo(t){return gl(vn(t))}var ri=0,_a=new Map,mi=new Map;function vo(t){return M(this,void 0,void 0,function(){return G(this,function(r){return[2,new Promise(function(i,c){var g=++ri;_a.set(g,i),mi.set(g,c),typeof chrome!="undefined"&&chrome.runtime.sendMessage({type:X.CS_FETCH,data:t,id:g})})]})})}typeof chrome!="undefined"&&chrome.runtime.onMessage.addListener(function(t){var r=t.type,i=t.data,c=t.error,g=t.id;if(r===X.BG_FETCH_RESPONSE){var h=_a.get(g),x=mi.get(g);_a.delete(g),mi.delete(g),c?x&&x(c):h&&h(i)}});var Ko=function(){function t(){this.queue=[],this.timerId=null,this.frameDuration=1e3/60}return t.prototype.addToQueue=function(r){this.queue.push(r),this.startQueue()},t.prototype.stopQueue=function(){this.timerId!==null&&(cancelAnimationFrame(this.timerId),this.timerId=null),this.queue=[]},t.prototype.startQueue=function(){var r=this;this.timerId||(this.timerId=requestAnimationFrame(function(){r.timerId=null;for(var i=Date.now(),c;c=r.queue.shift();)if(c(),Date.now()-i>=r.frameDuration){r.startQueue();break}}))},t}(),ki=new Ko;function $o(t){return M(this,void 0,void 0,function(){var r=this;return G(this,function(i){return[2,new Promise(function(c,g){return M(r,void 0,void 0,function(){var h,x,C,F;return G(this,function(R){switch(R.label){case 0:return t.startsWith("data:")?(h=t,[3,4]):[3,1];case 1:return R.trys.push([1,3,,4]),[4,ml(t)];case 2:return h=R.sent(),[3,4];case 3:return x=R.sent(),g(x),[2];case 4:return R.trys.push([4,6,,7]),[4,Go(h)];case 5:return C=R.sent(),ki.addToQueue(function(){c(P({src:t,dataURL:h,width:C.naturalWidth,height:C.naturalHeight},Qo(C)))}),[3,7];case 6:return F=R.sent(),g(F),[3,7];case 7:return[2]}})})})]})})}function ml(t){return M(this,void 0,void 0,function(){var r;return G(this,function(i){switch(i.label){case 0:return r=new URL(t),r.origin!==location.origin?[3,2]:[4,fe(t)];case 1:return[2,i.sent()];case 2:return[4,vo({url:t,responseType:"data-url"})];case 3:return[2,i.sent()]}})})}function Go(t){return M(this,void 0,void 0,function(){return G(this,function(r){return[2,new Promise(function(i,c){var g=new Image;g.onload=function(){return i(g)},g.onerror=function(){return c("Unable to load image "+t)},g.src=t})]})})}var pi=32*32,pa,ya;function Za(){var t=pi,r=pi;pa=document.createElement("canvas"),pa.width=t,pa.height=r,ya=pa.getContext("2d"),ya.imageSmoothingEnabled=!1}function Yo(){pa=null,ya=null}var Xo=5*1024*1024;function Qo(t){pa||Za();var r=t.naturalWidth,i=t.naturalHeight;if(i===0||r===0)return ke("logWarn(Image is empty "+t.currentSrc+")"),null;var c=r*i*4;if(c>Xo)return Ft("Skipped large image analyzing(Larger than 5mb in memory)"),{isDark:!1,isLight:!1,isTransparent:!1,isLarge:!1,isTooLarge:!0};var g=r*i,h=Math.min(1,Math.sqrt(pi/g)),x=Math.ceil(r*h),C=Math.ceil(i*h);ya.clearRect(0,0,x,C),ya.drawImage(t,0,0,r,i,0,0,x,C);var F=ya.getImageData(0,0,x,C),R=F.data,N=.05,K=.4,Te=.7,Me=0,ae=0,$=0,q,pe,Je,nt,Ot,Pt,St,qt;for(Je=0;Je<C;Je++)for(pe=0;pe<x;pe++)q=4*(Je*x+pe),nt=R[q+0]/255,Ot=R[q+1]/255,Pt=R[q+2]/255,St=R[q+3]/255,St<N?Me++:(qt=.2126*nt+.7152*Ot+.0722*Pt,qt<K&&ae++,qt>Te&&$++);var Dr=x*C,kr=Dr-Me,wn=.7,fi=.7,Fa=.1,io=800*600;return{isDark:ae/kr>=wn,isLight:$/kr>=fi,isTransparent:Me/Dr>=Fa,isLarge:g>=io,isTooLarge:!1}}function Hi(t,r){var i=t.dataURL,c=t.width,g=t.height,h=Vo(r),x=['<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="'+c+'" height="'+g+'">',"<defs>",'<filter id="darkreader-image-filter">','<feColorMatrix type="matrix" values="'+h+'" />',"</filter>","</defs>",'<image width="'+c+'" height="'+g+'" filter="url(#darkreader-image-filter)" xlink:href="'+i+'" />',"</svg>"].join("");return"data:image/svg+xml;base64,"+btoa(x)}function Jo(){ki&&ki.stopQueue(),Yo()}function ni(t,r){return Boolean(t&&t.getPropertyPriority(r))}function ho(t,r,i,c,g,h){if(t.startsWith("--")){var x=So(c,t,r,i,g,h);if(x)return{property:t,value:x,important:ni(i.style,t),sourceValue:r}}else if(r.includes("var(")){var x=pl(c,t,r);if(x)return{property:t,value:x,important:ni(i.style,t),sourceValue:r}}else if(t.includes("color")&&t!=="-webkit-print-color-adjust"||t==="fill"||t==="stroke"||t==="stop-color"){var x=ai(t,r);if(x)return{property:t,value:x,important:ni(i.style,t),sourceValue:r}}else if(t==="background-image"||t==="list-style-image"){var x=bi(r,i,g,h);if(x)return{property:t,value:x,important:ni(i.style,t),sourceValue:r}}else if(t.includes("shadow")){var x=qo(r);if(x)return{property:t,value:x,important:ni(i.style,t),sourceValue:r}}return null}function go(t,r,i){var c=[];return r||(c.push("html {"),c.push("    background-color: "+Qr({r:255,g:255,b:255},t)+" !important;"),c.push("}")),c.push(""+(r?"":"html, body, ")+(i?"input, textarea, select, button":"")+" {"),c.push("    background-color: "+Qr({r:255,g:255,b:255},t)+";"),c.push("}"),c.push("html, body, "+(i?"input, textarea, select, button":"")+" {"),c.push("    border-color: "+ei({r:76,g:76,b:76},t)+";"),c.push("    color: "+Gn({r:0,g:0,b:0},t)+";"),c.push("}"),c.push("a {"),c.push("    color: "+Gn({r:0,g:64,b:255},t)+";"),c.push("}"),c.push("table {"),c.push("    border-color: "+ei({r:128,g:128,b:128},t)+";"),c.push("}"),c.push("::placeholder {"),c.push("    color: "+Gn({r:169,g:169,b:169},t)+";"),c.push("}"),c.push("input:-webkit-autofill,"),c.push("textarea:-webkit-autofill,"),c.push("select:-webkit-autofill {"),c.push("    background-color: "+Qr({r:250,g:255,b:189},t)+" !important;"),c.push("    color: "+Gn({r:0,g:0,b:0},t)+" !important;"),c.push("}"),t.scrollbarColor&&c.push(po(t)),t.selectionColor&&c.push(yi(t)),c.join(`
`)}function mo(t){var r,i;if(t.selectionColor==="auto")r=Qr({r:0,g:96,b:212},P(P({},t),{grayscale:0})),i=Gn({r:255,g:255,b:255},P(P({},t),{grayscale:0}));else{var c=_(t.selectionColor),g=Sr(c);r=t.selectionColor,g.l<.5?i="#FFF":i="#000"}return{backgroundColorSelection:r,foregroundColorSelection:i}}function yi(t){var r=[],i=mo(t),c=i.backgroundColorSelection,g=i.foregroundColorSelection;return["::selection","::-moz-selection"].forEach(function(h){r.push(h+" {"),r.push("    background-color: "+c+" !important;"),r.push("    color: "+g+" !important;"),r.push("}")}),r.join(`
`)}function po(t){var r=[],i,c,g,h,x,C;if(t.scrollbarColor==="auto")i=Qr({r:241,g:241,b:241},t),c=Gn({r:96,g:96,b:96},t),g=Qr({r:176,g:176,b:176},t),h=Qr({r:144,g:144,b:144},t),x=Qr({r:96,g:96,b:96},t),C=Qr({r:255,g:255,b:255},t);else{var F=_(t.scrollbarColor),R=Sr(F),N=R.l>.5,K=function(Me){return P(P({},R),{l:Fn(R.l+Me,0,1)})},Te=function(Me){return P(P({},R),{l:Fn(R.l-Me,0,1)})};i=et(Te(.4)),c=et(N?Te(.4):K(.4)),g=et(R),h=et(K(.1)),x=et(K(.2))}return r.push("::-webkit-scrollbar {"),r.push("    background-color: "+i+";"),r.push("    color: "+c+";"),r.push("}"),r.push("::-webkit-scrollbar-thumb {"),r.push("    background-color: "+g+";"),r.push("}"),r.push("::-webkit-scrollbar-thumb:hover {"),r.push("    background-color: "+h+";"),r.push("}"),r.push("::-webkit-scrollbar-thumb:active {"),r.push("    background-color: "+x+";"),r.push("}"),r.push("::-webkit-scrollbar-corner {"),r.push("    background-color: "+C+";"),r.push("}"),qe&&(r.push("* {"),r.push("    scrollbar-color: "+g+" "+i+";"),r.push("}")),r.join(`
`)}function _i(t,r){var i=r.strict,c=[],g=location.hostname.endsWith("microsoft.com");return c.push("html, body, "+(i?"body :not(iframe)"+(g?':not(div[style^="position:absolute;top:0;left:-"]':""):"body > :not(iframe)")+" {"),c.push("    background-color: "+Qr({r:255,g:255,b:255},t)+" !important;"),c.push("    border-color: "+ei({r:64,g:64,b:64},t)+" !important;"),c.push("    color: "+Gn({r:0,g:0,b:0},t)+" !important;"),c.push("}"),c.join(`
`)}var yo=new Set(["inherit","transparent","initial","currentcolor","none","unset"]),Sn=new Map;function xo(t){if(t=t.trim(),Sn.has(t))return Sn.get(t);t.includes("calc(")&&(t=Ni(t));var r=_(t);return Sn.set(t,r),r}function xa(t){try{return xo(t)}catch(r){return null}}function ai(t,r){if(yo.has(r.toLowerCase()))return r;try{var i=xo(r);return t.includes("background")?function(c){return Qr(i,c)}:t.includes("border")||t.includes("outline")?function(c){return ei(i,c)}:function(c){return Gn(i,c)}}catch(c){return ke("Color parse error",c),null}}var bo=/[\-a-z]+gradient\(([^\(\)]*(\(([^\(\)]*(\(.*?\)))*[^\(\)]*\))){0,15}[^\(\)]*\)/g,xi=new Map,Yn=new Map;function zi(t,r){if(!t||r.length===0)return!1;if(r.some(function(x){return x==="*"}))return!0;for(var i=t.split(/,\s*/g),c=function(x){var C=r[x];if(i.some(function(F){return F===C}))return{value:!0}},g=0;g<r.length;g++){var h=c(g);if(typeof h=="object")return h.value}return!1}function bi(t,r,i,c){var g=this;try{var h=dn(bo,t),x=dn(Ae,t);if(x.length===0&&h.length===0)return t;var C=function(ae){var $=0;return ae.map(function(q){var pe=t.indexOf(q,$);return $=pe+q.length,{match:q,index:pe}})},F=C(x).map(function(ae){return P({type:"url"},ae)}).concat(C(h).map(function(ae){return P({type:"gradient"},ae)})).sort(function(ae,$){return ae.index-$.index}),R=function(ae){var $=ae.match(/^(.*-gradient)\((.*)\)$/),q=$[1],pe=$[2],Je=/([^\(\),]+(\([^\(\)]*(\([^\(\)]*\)*[^\(\)]*)?\))?[^\(\),]*),?/g,nt=/^(from|color-stop|to)\(([^\(\)]*?,\s*)?(.*?)\)$/,Ot=dn(Je,pe,1).map(function(Pt){Pt=Pt.trim();var St=xa(Pt);if(St)return function(kr){return ma(St,kr)};var qt=Pt.lastIndexOf(" ");if(St=xa(Pt.substring(0,qt)),St)return function(kr){return ma(St,kr)+" "+Pt.substring(qt+1)};var Dr=Pt.match(nt);return Dr&&(St=xa(Dr[3]),St)?function(kr){return Dr[1]+"("+(Dr[2]?Dr[2]+", ":"")+ma(St,kr)+")"}:function(){return Pt}});return function(Pt){return q+"("+Ot.map(function(St){return St(Pt)}).join(", ")+")"}},N=function(ae){var $;if(zi(r.selectorText,i))return null;var q=Ce(ae),pe=r.parentStyleSheet,Je=pe&&pe.href?_e(pe.href):(($=pe.ownerNode)===null||$===void 0?void 0:$.baseURI)||location.origin;q=Or(Je,q);var nt='url("'+q+'")';return function(Ot){return M(g,void 0,void 0,function(){var Pt,St,qt,Dr;return G(this,function(kr){switch(kr.label){case 0:return xi.has(q)?(Pt=xi.get(q),[3,7]):[3,1];case 1:return kr.trys.push([1,6,,7]),Yn.has(q)?(St=Yn.get(q),[4,new Promise(function(wn){return St.push(wn)})]):[3,3];case 2:return Pt=kr.sent(),Pt?[3,5]:[2,null];case 3:return Yn.set(q,[]),[4,$o(q)];case 4:Pt=kr.sent(),xi.set(q,Pt),Yn.get(q).forEach(function(wn){return wn(Pt)}),Yn.delete(q),kr.label=5;case 5:return c()?[2,null]:[3,7];case 6:return qt=kr.sent(),ke(qt),Yn.has(q)&&(Yn.get(q).forEach(function(wn){return wn(null)}),Yn.delete(q)),[2,nt];case 7:return Dr=K(Pt,Ot)||nt,[2,Dr]}})})}},K=function(ae,$){var q=ae.isDark,pe=ae.isLight,Je=ae.isTransparent,nt=ae.isLarge,Ot=ae.isTooLarge,Pt=ae.width,St;if(Ot)St='url("'+ae.src+'")';else if(q&&Je&&$.mode===1&&!nt&&Pt>2){Ft("Inverting dark image "+ae.src);var qt=Hi(ae,P(P({},$),{sepia:Fn($.sepia+10,0,100)}));St='url("'+qt+'")'}else if(pe&&!Je&&$.mode===1)if(nt)St="none";else{Ft("Dimming light image "+ae.src);var Dr=Hi(ae,$);St='url("'+Dr+'")'}else if($.mode===0&&pe&&!nt){Ft("Applying filter to image "+ae.src);var kr=Hi(ae,P(P({},$),{brightness:Fn($.brightness-10,5,200),sepia:Fn($.sepia+10,0,100)}));St='url("'+kr+'")'}else St=null;return St},Te=[],Me=0;return F.forEach(function(ae,$){var q=ae.match,pe=ae.type,Je=ae.index,nt=Me,Ot=Je+q.length;Me=Ot,Te.push(function(){return t.substring(nt,Je)}),Te.push(pe==="url"?N(q):R(q)),$===F.length-1&&Te.push(function(){return t.substring(Ot)})}),function(ae){var $=Te.filter(Boolean).map(function(q){return q(ae)});return $.some(function(q){return q instanceof Promise})?Promise.all($).then(function(q){return q.join("")}):$.join("")}}catch(ae){return ke("Unable to parse gradient "+t,ae),null}}function Si(t){try{var r=0,i=dn(/(^|\s)(?!calc)([a-z]+\(.+?\)|#[0-9a-f]+|[a-z]+)(.*?(inset|outset)?($|,))/ig,t,2),c=0,g=i.map(function(h,x){var C=r,F=t.indexOf(h,r),R=F+h.length;r=R;var N=xa(h);return N?function(K){return""+t.substring(C,F)+uo(N,K)+(x===i.length-1?t.substring(R):"")}:(c++,function(){return t.substring(C,R)})});return function(h){var x=g.map(function(C){return C(h)}).join("");return{matchesLength:i.length,unparseableMatchesLength:c,result:x}}}catch(h){return ke("Unable to parse shadow "+t,h),null}}function qo(t){var r=Si(t);return r?function(i){return r(i).result}:null}function So(t,r,i,c,g,h){return t.getModifierForVariable({varName:r,sourceValue:i,rule:c,ignoredImgSelectors:g,isCancelled:h})}function pl(t,r,i){return t.getModifierForVarDependant(r,i)}function Ui(){Sn.clear(),bn(),xi.clear(),Jo(),Yn.clear()}var Jr=1<<0,nn=1<<1,la=1<<2,ba=1<<3,el=function(){function t(){this.varTypes=new Map,this.rulesQueue=[],this.definedVars=new Set,this.varRefs=new Map,this.unknownColorVars=new Set,this.unknownBgVars=new Set,this.undefinedVars=new Set,this.initialVarTypes=new Map,this.changedTypeVars=new Set,this.typeChangeSubscriptions=new Map,this.unstableVarValues=new Map}return t.prototype.clear=function(){this.varTypes.clear(),this.rulesQueue.splice(0),this.definedVars.clear(),this.varRefs.clear(),this.unknownColorVars.clear(),this.unknownBgVars.clear(),this.undefinedVars.clear(),this.initialVarTypes.clear(),this.changedTypeVars.clear(),this.typeChangeSubscriptions.clear(),this.unstableVarValues.clear()},t.prototype.isVarType=function(r,i){return this.varTypes.has(r)&&(this.varTypes.get(r)&i)>0},t.prototype.addRulesForMatching=function(r){this.rulesQueue.push(r)},t.prototype.matchVariablesAndDependants=function(){var r=this;this.changedTypeVars.clear(),this.initialVarTypes=new Map(this.varTypes),this.collectRootVariables(),this.collectVariablesAndVarDep(this.rulesQueue),this.rulesQueue.splice(0),this.collectRootVarDependants(),this.varRefs.forEach(function(i,c){i.forEach(function(g){r.varTypes.has(c)&&r.resolveVariableType(g,r.varTypes.get(c))})}),this.unknownColorVars.forEach(function(i){r.unknownBgVars.has(i)?(r.unknownColorVars.delete(i),r.unknownBgVars.delete(i),r.resolveVariableType(i,Jr)):r.isVarType(i,Jr|nn|la)?r.unknownColorVars.delete(i):r.undefinedVars.add(i)}),this.unknownBgVars.forEach(function(i){var c=r.findVarRef(i,function(g){return r.unknownColorVars.has(g)||r.isVarType(g,nn|la)})!=null;c?r.itarateVarRefs(i,function(g){r.resolveVariableType(g,Jr)}):r.isVarType(i,Jr|ba)?r.unknownBgVars.delete(i):r.undefinedVars.add(i)}),this.changedTypeVars.forEach(function(i){r.typeChangeSubscriptions.has(i)&&r.typeChangeSubscriptions.get(i).forEach(function(c){c()})}),this.changedTypeVars.clear()},t.prototype.getModifierForVariable=function(r){var i=this;return function(c){var g=r.varName,h=r.sourceValue,x=r.rule,C=r.ignoredImgSelectors,F=r.isCancelled,R=function(){var Me=[],ae=function(Je,nt,Ot){if(!!i.isVarType(g,Je)){var Pt=nt(g),St;if(ca(h))if(To(h)){var qt=Ti(h,i.unstableVarValues);qt||(qt=Je===Jr?"#ffffff":"#000000"),St=Ot(qt,c)}else St=sa(h,function(Dr){return nt(Dr)},function(Dr){return Ot(Dr,c)});else St=Ot(h,c);Me.push({property:Pt,value:St})}};if(ae(Jr,ii,za),ae(nn,Vi,wi),ae(la,Ki,Oa),i.isVarType(g,ba)){var $=$i(g),q=h;ca(h)&&(q=sa(h,function(Je){return ii(Je)},function(Je){return za(Je,c)}));var pe=bi(q,x,C,F);q=typeof pe=="function"?pe(c):pe,Me.push({property:$,value:q})}return Me},N=new Set,K=function(Me){var ae=function(){var $=R();Me($)};N.add(ae),i.subscribeForVarTypeChange(g,ae)},Te=function(){N.forEach(function(Me){i.unsubscribeFromVariableTypeChanges(g,Me)})};return{declarations:R(),onTypeChange:{addListener:K,removeListeners:Te}}}},t.prototype.getModifierForVarDependant=function(r,i){var c=this;if(i.match(/^\s*(rgb|hsl)a?\(/)){var g=r.startsWith("background"),h=r==="color"||r==="caret-color";return function(x){var C=Ti(i,c.unstableVarValues);C||(C=g?"#ffffff":"#000000");var F=g?za:h?wi:Oa;return F(C,x)}}return r==="background-color"?function(x){return sa(i,function(C){return ii(C)},function(C){return za(C,x)})}:r==="color"||r==="caret-color"?function(x){return sa(i,function(C){return Vi(C)},function(C){return wi(C,x)})}:r==="background"||r==="background-image"||r==="box-shadow"?function(x){var C=new Set,F=function(){var N=sa(i,function(Me){return c.isVarType(Me,Jr)?ii(Me):c.isVarType(Me,ba)?$i(Me):(C.add(Me),Me)},function(Me){return za(Me,x)});if(r==="box-shadow"){var K=Si(N),Te=K(x);if(Te.unparseableMatchesLength!==Te.matchesLength)return Te.result}return N},R=F();return C.size>0?new Promise(function(N){var K=C.values().next().value,Te=function(){c.unsubscribeFromVariableTypeChanges(K,Te);var Me=F();N(Me)};c.subscribeForVarTypeChange(K,Te)}):R}:r.startsWith("border")||r.startsWith("outline")?function(x){return sa(i,function(C){return Ki(C)},function(C){return Oa(C,x)})}:null},t.prototype.subscribeForVarTypeChange=function(r,i){this.typeChangeSubscriptions.has(r)||this.typeChangeSubscriptions.set(r,new Set);var c=this.typeChangeSubscriptions.get(r);c.has(i)||c.add(i)},t.prototype.unsubscribeFromVariableTypeChanges=function(r,i){this.typeChangeSubscriptions.has(r)&&this.typeChangeSubscriptions.get(r).delete(i)},t.prototype.collectVariablesAndVarDep=function(r){var i=this;r.forEach(function(c){un(c,function(g){g.style&&V(g.style,function(h,x){Ci(h)&&i.inspectVariable(h,x),ca(x)&&i.inspectVarDependant(h,x)})})})},t.prototype.collectRootVariables=function(){var r=this;V(document.documentElement.style,function(i,c){Ci(i)&&r.inspectVariable(i,c)})},t.prototype.inspectVariable=function(r,i){if(this.unstableVarValues.set(r,i),ca(i)&&To(i)&&(this.unknownColorVars.add(r),this.definedVars.add(r)),!this.definedVars.has(r)){this.definedVars.add(r);var c=xa(i);c?this.unknownColorVars.add(r):(i.includes("url(")||i.includes("linear-gradient(")||i.includes("radial-gradient("))&&this.resolveVariableType(r,ba)}},t.prototype.resolveVariableType=function(r,i){var c=this.initialVarTypes.get(r)||0,g=this.varTypes.get(r)||0,h=g|i;this.varTypes.set(r,h),(h!==c||this.undefinedVars.has(r))&&(this.changedTypeVars.add(r),this.undefinedVars.delete(r)),this.unknownColorVars.delete(r),this.unknownBgVars.delete(r)},t.prototype.collectRootVarDependants=function(){var r=this;V(document.documentElement.style,function(i,c){ca(c)&&r.inspectVarDependant(i,c)})},t.prototype.inspectVarDependant=function(r,i){var c=this;Ci(r)?this.iterateVarDeps(i,function(g){c.varRefs.has(r)||c.varRefs.set(r,new Set),c.varRefs.get(r).add(g)}):r==="background-color"||r==="box-shadow"?this.iterateVarDeps(i,function(g){return c.resolveVariableType(g,Jr)}):r==="color"||r==="caret-color"?this.iterateVarDeps(i,function(g){return c.resolveVariableType(g,nn)}):r.startsWith("border")||r.startsWith("outline")?this.iterateVarDeps(i,function(g){return c.resolveVariableType(g,la)}):(r==="background"||r==="background-image")&&this.iterateVarDeps(i,function(g){if(!c.isVarType(g,Jr|ba)){var h=c.findVarRef(g,function(x){return c.unknownColorVars.has(x)||c.isVarType(x,nn|la)})!=null;c.itarateVarRefs(g,function(x){h?c.resolveVariableType(x,Jr):c.unknownBgVars.add(x)})}})},t.prototype.iterateVarDeps=function(r,i){var c=new Set;rl(r,function(g){return c.add(g)}),c.forEach(function(g){return i(g)})},t.prototype.findVarRef=function(r,i,c){var g,h;if(c===void 0&&(c=new Set),c.has(r))return null;c.add(r);var x=i(r);if(x)return r;var C=this.varRefs.get(r);if(!C||C.size===0)return null;try{for(var F=Y(C),R=F.next();!R.done;R=F.next()){var N=R.value,K=this.findVarRef(N,i,c);if(K)return K}}catch(Te){g={error:Te}}finally{try{R&&!R.done&&(h=F.return)&&h.call(F)}finally{if(g)throw g.error}}return null},t.prototype.itarateVarRefs=function(r,i){this.findVarRef(r,function(c){return i(c),!1})},t.prototype.setOnRootVariableChange=function(r){this.onRootVariableDefined=r},t.prototype.putRootVars=function(r,i){var c,g,h=this,x=r.sheet;x.cssRules.length>0&&x.deleteRule(0);var C=new Map;V(document.documentElement.style,function($,q){Ci($)&&(h.isVarType($,Jr)&&C.set(ii($),za(q,i)),h.isVarType($,nn)&&C.set(Vi($),wi(q,i)),h.isVarType($,la)&&C.set(Ki($),Oa(q,i)),h.subscribeForVarTypeChange($,h.onRootVariableDefined))});var F=[];F.push(":root {");try{for(var R=Y(C),N=R.next();!N.done;N=R.next()){var K=H(N.value,2),Te=K[0],Me=K[1];F.push("    "+Te+": "+Me+";")}}catch($){c={error:$}}finally{try{N&&!N.done&&(g=R.return)&&g.call(R)}finally{if(c)throw c.error}}F.push("}");var ae=F.join(`
`);x.insertRule(ae)},t}(),hn=new el;function tl(t,r){r===void 0&&(r=0);var i=t.indexOf("var(",r);if(i>=0){var c=fn(t,i+3);return c?{start:i,end:c.end}:null}}function Co(t){for(var r=[],i=0,c;c=tl(t,i);){var g=c.start,h=c.end;r.push({start:g,end:h,value:t.substring(g,h)}),i=c.end+1}return r}function Wi(t,r){var i=Co(t),c=i.length;if(c===0)return t;var g=t.length,h=i.map(function(N){return r(N.value)}),x=[];x.push(t.substring(0,i[0].start));for(var C=0;C<c;C++){x.push(h[C]);var F=i[C].end,R=C<c-1?i[C+1].start:g;x.push(t.substring(F,R))}return x.join("")}function wo(t){var r=t.indexOf(","),i,c;return r>=0?(i=t.substring(4,r).trim(),c=t.substring(r+1,t.length-1).trim()):(i=t.substring(4,t.length-1).trim(),c=""),{name:i,fallback:c}}function sa(t,r,i){var c=function(g){var h=wo(g),x=h.name,C=h.fallback,F=r(x);if(!C)return"var("+F+")";var R;return ca(C)?R=sa(C,r,i):i?R=i(C):R=C,"var("+F+", "+R+")"};return Wi(t,c)}function rl(t,r){sa(t,function(i){return r(i),i})}function ii(t){return"--darkreader-bg"+t}function Vi(t){return"--darkreader-text"+t}function Ki(t){return"--darkreader-border"+t}function $i(t){return"--darkreader-bgimg"+t}function Ci(t){return t.startsWith("--")}function ca(t){return t.includes("var(")}function To(t){return t.match(/^\s*(rgb|hsl)a?\(/)}var Xn=/^\d{1,3}, ?\d{1,3}, ?\d{1,3}$/;function Aa(t){if(Xn.test(t)){var r=t.split(","),i="rgb(";return r.forEach(function(c){i+=c.trim()+", "}),i=i.substr(0,i.length-2),i+=")",{isRaw:!0,color:i}}return{isRaw:!1,color:t}}function ua(t,r,i){var c=Aa(t),g=c.isRaw,h=c.color,x=xa(h);if(x){var C=i(x,r);if(g){var F=xa(C);return F?F.r+", "+F.g+", "+F.b:C}return C}return h}function za(t,r){return ua(t,r,Qr)}function wi(t,r){return ua(t,r,Gn)}function Oa(t,r){return ua(t,r,ei)}function Ti(t,r,i){i===void 0&&(i=new Set);var c=!1,g=function(x){var C=wo(x),F=C.name,R=C.fallback;if(i.has(F))return c=!0,null;i.add(F);var N=r.get(F)||R,K=null;return N&&(ca(N)?K=Ti(N,r,i):K=N),K||(c=!0,null)},h=Wi(t,g);return c?null:h}var La={"background-color":{customProp:"--darkreader-inline-bgcolor",cssProp:"background-color",dataAttr:"data-darkreader-inline-bgcolor"},"background-image":{customProp:"--darkreader-inline-bgimage",cssProp:"background-image",dataAttr:"data-darkreader-inline-bgimage"},"border-color":{customProp:"--darkreader-inline-border",cssProp:"border-color",dataAttr:"data-darkreader-inline-border"},"border-bottom-color":{customProp:"--darkreader-inline-border-bottom",cssProp:"border-bottom-color",dataAttr:"data-darkreader-inline-border-bottom"},"border-left-color":{customProp:"--darkreader-inline-border-left",cssProp:"border-left-color",dataAttr:"data-darkreader-inline-border-left"},"border-right-color":{customProp:"--darkreader-inline-border-right",cssProp:"border-right-color",dataAttr:"data-darkreader-inline-border-right"},"border-top-color":{customProp:"--darkreader-inline-border-top",cssProp:"border-top-color",dataAttr:"data-darkreader-inline-border-top"},"box-shadow":{customProp:"--darkreader-inline-boxshadow",cssProp:"box-shadow",dataAttr:"data-darkreader-inline-boxshadow"},color:{customProp:"--darkreader-inline-color",cssProp:"color",dataAttr:"data-darkreader-inline-color"},fill:{customProp:"--darkreader-inline-fill",cssProp:"fill",dataAttr:"data-darkreader-inline-fill"},stroke:{customProp:"--darkreader-inline-stroke",cssProp:"stroke",dataAttr:"data-darkreader-inline-stroke"},"outline-color":{customProp:"--darkreader-inline-outline",cssProp:"outline-color",dataAttr:"data-darkreader-inline-outline"},"stop-color":{customProp:"--darkreader-inline-stopcolor",cssProp:"stop-color",dataAttr:"data-darkreader-inline-stopcolor"}},Sa=Object.values(La),Ua={};Sa.forEach(function(t){var r=t.cssProp,i=t.customProp;return Ua[i]=r});var Ca=["style","fill","stop-color","stroke","bgcolor","color"],Wa=Ca.map(function(t){return"["+t+"]"}).join(", ");function Eo(){return Sa.map(function(t){var r=t.dataAttr,i=t.customProp,c=t.cssProp;return["["+r+"] {","  "+c+": var("+i+") !important;","}"].join(`
`)}).join(`
`)}function Gi(t){var r=[];return t instanceof Element&&t.matches(Wa)&&r.push(t),(t instanceof Element||At&&t instanceof ShadowRoot||t instanceof Document)&&xr(r,t.querySelectorAll(Wa)),r}var oi=new Map,Ei=new Map;function Mo(t,r){Yi(document,t,r),It(document.documentElement,function(i){Yi(i.shadowRoot,t,r)})}function Yi(t,r,i){oi.has(t)&&(oi.get(t).disconnect(),Ei.get(t).disconnect());var c=new WeakSet;function g($){Gi($).forEach(function(q){c.has(q)||(c.add(q),r(q))}),It($,function(q){c.has($)||(c.add($),i(q.shadowRoot),Yi(q.shadowRoot,r,i))})}var h=Mr(t,{onMinorMutations:function($){var q=$.additions;q.forEach(function(pe){return g(pe)})},onHugeMutations:function(){g(t)}});oi.set(t,h);var x=0,C=null,F=Vt({seconds:10}),R=Vt({seconds:2}),N=50,K=[],Te=null,Me=cr(function($){$.forEach(function(q){Ca.includes(q.attributeName)&&r(q.target)})}),ae=new MutationObserver(function($){if(Te){K.push.apply(K,oe([],H($),!1));return}x++;var q=Date.now();if(C==null)C=q;else if(x>=N){if(q-C<F){Te=setTimeout(function(){C=null,x=0,Te=null;var pe=K;K=[],Me(pe)},R),K.push.apply(K,oe([],H($),!1));return}C=q,x=1}Me($)});ae.observe(t,{attributes:!0,attributeFilter:Ca.concat(Sa.map(function($){var q=$.dataAttr;return q})),subtree:!0}),Ei.set(t,ae)}function nr(){oi.forEach(function(t){return t.disconnect()}),Ei.forEach(function(t){return t.disconnect()}),oi.clear(),Ei.clear()}var qr=new WeakMap,nl=["brightness","contrast","grayscale","sepia","mode"];function Rn(t,r){return Ca.map(function(i){return i+'="'+t.getAttribute(i)+'"'}).concat(nl.map(function(i){return i+'="'+r[i]+'"'})).join(" ")}function Xi(t,r){for(var i=0,c=r.length;i<c;i++){var g=r[i];if(t.matches(g))return!0}return!1}function Mi(t,r,i,c){var g=Rn(t,r);if(g===qr.get(t))return;var h=new Set(Object.keys(La));function x(K,Te,Me){var ae=La[K],$=ae.customProp,q=ae.dataAttr,pe=ho(Te,Me,{},hn,c,null);if(!!pe){var Je=pe.value;typeof Je=="function"&&(Je=Je(r)),t.style.setProperty($,Je),t.hasAttribute(q)||t.setAttribute(q,""),h.delete(K)}}if(i.length>0&&Xi(t,i)){h.forEach(function(K){t.removeAttribute(La[K].dataAttr)});return}if(t.hasAttribute("bgcolor")){var C=t.getAttribute("bgcolor");(C.match(/^[0-9a-f]{3}$/i)||C.match(/^[0-9a-f]{6}$/i))&&(C="#"+C),x("background-color","background-color",C)}if(t.hasAttribute("color")&&t.rel!=="mask-icon"){var C=t.getAttribute("color");(C.match(/^[0-9a-f]{3}$/i)||C.match(/^[0-9a-f]{6}$/i))&&(C="#"+C),x("color","color",C)}if(t instanceof SVGElement){if(t.hasAttribute("fill")){var F=32,R=t.getAttribute("fill");if(R!=="none")if(t instanceof SVGTextElement)x("fill","color",R);else{var N=function(){var K=t.getBoundingClientRect(),Te=K.width,Me=K.height,ae=Te>F||Me>F;x("fill",ae?"background-color":"color",R)};ct()?N():mt(N)}}t.hasAttribute("stop-color")&&x("stop-color","background-color",t.getAttribute("stop-color"))}if(t.hasAttribute("stroke")){var C=t.getAttribute("stroke");x("stroke",t instanceof SVGLineElement||t instanceof SVGTextElement?"border-color":"color",C)}t.style&&V(t.style,function(K,Te){if(!(K==="background-image"&&Te.includes("url")))if(La.hasOwnProperty(K))x(K,K,Te);else{var Me=Ua[K];if(Me&&!t.style.getPropertyValue(Me)&&!t.hasAttribute(Me)){if(Me==="background-color"&&t.hasAttribute("bgcolor"))return;t.style.setProperty(K,"")}}}),t.style&&t instanceof SVGTextElement&&t.style.fill&&x("fill","color",t.style.getPropertyValue("fill")),Mt(h,function(K){t.removeAttribute(La[K].dataAttr)}),qr.set(t,Rn(t,r))}var Qi="theme-color",Da='meta[name="'+Qi+'"]',Qn=null,Jn=null;function li(t,r){Qn=Qn||t.content;try{var i=_(Qn);t.content=Qr(i,r)}catch(c){ke(c)}}function Ji(t){var r=document.querySelector(Da);r?li(r,t):(Jn&&Jn.disconnect(),Jn=new MutationObserver(function(i){e:for(var c=0;c<i.length;c++)for(var g=i[c].addedNodes,h=0;h<g.length;h++){var x=g[h];if(x instanceof HTMLMetaElement&&x.name===Qi){Jn.disconnect(),Jn=null,li(x,t);break e}}}),Jn.observe(document.head,{childList:!0}))}function Po(){Jn&&(Jn.disconnect(),Jn=null);var t=document.querySelector(Da);t&&Qn&&(t.content=Qn)}var qi=["mode","brightness","contrast","grayscale","sepia","darkSchemeBackgroundColor","darkSchemeTextColor","lightSchemeBackgroundColor","lightSchemeTextColor"];function eo(t){return qi.map(function(r){return r+":"+t[r]}).join(";")}var Pi=jr();function Ro(){var t=0,r=new Set,i=new Map,c=new Set,g=null,h=!1,x=!1;function C(){return h&&!x}function F(R){var N=R.sourceCSSRules,K=R.theme,Te=R.ignoreImageAnalysis,Me=R.force,ae=R.prepareSheet,$=R.isAsyncCancelled,q=i.size===0,pe=new Set(i.keys()),Je=eo(K),nt=Je!==g;h&&(x=!0);var Ot=[];if(un(N,function(zt){var fr=zt.cssText,Ir=!1;if(pe.delete(fr),zt.parentRule instanceof CSSMediaRule&&(fr+=";"+zt.parentRule.media.mediaText),r.has(fr)||(r.add(fr),Ir=!0),Ir)q=!0;else{Ot.push(i.get(fr));return}var gr=[];zt.style&&V(zt.style,function(Tr,va){var kn=ho(Tr,va,zt,hn,Te,$);kn&&gr.push(kn)});var mr=null;if(gr.length>0){var Ur=zt.parentRule;mr={selector:zt.selectorText,declarations:gr,parentRule:Ur},Ot.push(mr)}i.set(fr,mr)},function(){h=!0}),pe.forEach(function(zt){r.delete(zt),i.delete(zt)}),g=Je,!Me&&!q&&!nt)return;t++;function Pt(zt,fr,Ir){var gr=Ir.selector,mr=Ir.declarations,Ur=function(kn){var qn=kn.property,Tn=kn.value,On=kn.important,gn=kn.sourceValue;return qn+": "+(Tn==null?gn:Tn)+(On?" !important":"")+";"},Tr="";mr.forEach(function(kn){Tr+=Ur(kn)+" "});var va=gr+" { "+Tr+" }";zt.insertRule(va,fr)}var St=new Map,qt=new Map,Dr=0,kr=0,wn={rule:null,rules:[],isGroup:!0},fi=new WeakMap;function Fa(zt){if(zt==null)return wn;if(fi.has(zt))return fi.get(zt);var fr={rule:zt,rules:[],isGroup:!0};fi.set(zt,fr);var Ir=Fa(zt.parentRule);return Ir.rules.push(fr),fr}c.forEach(function(zt){return zt()}),c.clear(),Ot.filter(function(zt){return zt}).forEach(function(zt){var fr=zt.selector,Ir=zt.declarations,gr=zt.parentRule,mr=Fa(gr),Ur={selector:fr,declarations:[],isGroup:!1},Tr=Ur.declarations;mr.rules.push(Ur);function va(qn,Tn,On,gn){var ea=++Dr,Hn={property:qn,value:null,important:On,asyncKey:ea,sourceValue:gn};Tr.push(Hn);var vi=t;Tn.then(function(ht){!ht||$()||vi!==t||(Hn.value=ht,Pi.add(function(){$()||vi!==t||dl(ea)}))})}function kn(qn,Tn,On,gn){var ea=Tn,Hn=ea.declarations,vi=ea.onTypeChange,ht=++kr,Ln=t,Wr=Tr.length,Gr=[];if(Hn.length===0){var on={property:qn,value:gn,important:On,sourceValue:gn,varKey:ht};Tr.push(on),Gr=[on]}Hn.forEach(function(En){if(En.value instanceof Promise)va(En.property,En.value,On,gn);else{var ln={property:En.property,value:En.value,important:On,sourceValue:gn,varKey:ht};Tr.push(ln),Gr.push(ln)}}),vi.addListener(function(En){if(!($()||Ln!==t)){var ln=En.map(function(Ba){return{property:Ba.property,value:Ba.value,important:On,sourceValue:gn,varKey:ht}}),Ga=Tr.indexOf(Gr[0],Wr);Tr.splice.apply(Tr,oe([Ga,Gr.length],H(ln),!1)),Gr=ln,oo(ht)}}),c.add(function(){return vi.removeListeners()})}Ir.forEach(function(qn){var Tn=qn.property,On=qn.value,gn=qn.important,ea=qn.sourceValue;if(typeof On=="function"){var Hn=On(K);Hn instanceof Promise?va(Tn,Hn,gn,ea):Tn.startsWith("--")?kn(Tn,Hn,gn,ea):Tr.push({property:Tn,value:Hn,important:gn,sourceValue:ea})}else Tr.push({property:Tn,value:On,important:gn,sourceValue:ea})})});var io=ae();function ko(){function zt(Ir,gr){var mr=Ir.rule;if(mr instanceof CSSMediaRule){var Ur=mr.media,Tr=gr.cssRules.length;return gr.insertRule("@media "+Ur.mediaText+" {}",Tr),gr.cssRules[Tr]}return gr}function fr(Ir,gr,mr){Ir.rules.forEach(function(Ur){if(Ur.isGroup){var Tr=zt(Ur,gr);fr(Ur,Tr,mr)}else mr(Ur,gr)})}fr(wn,io,function(Ir,gr){var mr=gr.cssRules.length;Ir.declarations.forEach(function(Ur){var Tr=Ur.asyncKey,va=Ur.varKey;Tr!=null&&St.set(Tr,{rule:Ir,target:gr,index:mr}),va!=null&&qt.set(va,{rule:Ir,target:gr,index:mr})}),Pt(gr,mr,Ir)})}function dl(zt){var fr=St.get(zt),Ir=fr.rule,gr=fr.target,mr=fr.index;gr.deleteRule(mr),Pt(gr,mr,Ir),St.delete(zt)}function oo(zt){var fr=qt.get(zt),Ir=fr.rule,gr=fr.target,mr=fr.index;gr.deleteRule(mr),Pt(gr,mr,Ir)}ko()}return{modifySheet:F,shouldRebuildStyle:C}}var al='style, link[rel*="stylesheet" i]:not([disabled])';function Ri(t){return(t instanceof HTMLStyleElement||t instanceof SVGStyleElement||t instanceof HTMLLinkElement&&t.rel&&t.rel.toLowerCase().includes("stylesheet")&&!t.disabled&&(qe?!t.href.startsWith("moz-extension://"):!0))&&!t.classList.contains("darkreader")&&t.media.toLowerCase()!=="print"&&!t.classList.contains("stylus")}function wa(t,r,i){return r===void 0&&(r=[]),i===void 0&&(i=!0),Ri(t)?r.push(t):(t instanceof Element||At&&t instanceof ShadowRoot||t===document)&&(Mt(t.querySelectorAll(al),function(c){return wa(c,r,!1)}),i&&It(t,function(c){return wa(c.shadowRoot,r,!1)})),r}var to=new WeakSet,Io=new WeakSet,Ii=!1;typeof document!="undefined"&&document.addEventListener("__darkreader__inlineScriptsAllowed",function(){Ii=!0});var Va=0,Ta=new Map;function Zi(){Ta.clear()}function ro(t,r){for(var i=r.update,c=r.loadingStart,g=r.loadingEnd,h=[],x=t;(x=x.nextElementSibling)&&x.matches(".darkreader");)h.push(x);var C=h.find(function(ht){return ht.matches(".darkreader--cors")&&!Io.has(ht)})||null,F=h.find(function(ht){return ht.matches(".darkreader--sync")&&!to.has(ht)})||null,R=null,N=null,K=!1,Te=!0,Me=Ro(),ae=new MutationObserver(function(){i()}),$={attributes:!0,childList:!0,subtree:!0,characterData:!0};function q(){return t instanceof HTMLStyleElement&&t.textContent.trim().match(Re)}function pe(ht,Ln){var Wr=!1;if(ht){var Gr=void 0;e:for(var on=0,En=ht.length;on<En;on++)if(Gr=ht[on],Gr.href)if(Ln){if(Gr.href.startsWith("http")&&!Gr.href.startsWith(location.origin)){Wr=!0;break e}}else{Wr=!0;break e}}return Wr}function Je(){if(C)return Ft("[getRulesSync] Using cors-copy."),C.sheet.cssRules;if(q())return Ft("[getRulesSync] CSSImport detected."),null;var ht=ko();return t instanceof HTMLLinkElement&&!Pn(t.href)&&pe(ht,!1)?(Ft("[getRulesSync] CSSImportRule detected on non-local href."),null):pe(ht,!0)?(Ft("[getRulesSync] Cross-Origin CSSImportRule detected."),null):(Ft("[getRulesSync] Using cssRules."),!ht&&ke("[getRulesSync] cssRules is null, trying again."),ht)}function nt(){C?(t.nextSibling!==C&&t.parentNode.insertBefore(C,t.nextSibling),C.nextSibling!==F&&t.parentNode.insertBefore(F,C.nextSibling)):t.nextSibling!==F&&t.parentNode.insertBefore(F,t.nextSibling)}function Ot(){F=t instanceof SVGStyleElement?document.createElementNS("http://www.w3.org/2000/svg","style"):document.createElement("style"),F.classList.add("darkreader"),F.classList.add("darkreader--sync"),F.media="screen",!we&&t.title&&(F.title=t.title),to.add(F)}var Pt=!1,St=!1,qt=++Va;function Dr(){return M(this,void 0,void 0,function(){var ht,Ln,Wr,Gr,on,En,ln,Ga,Ba;return G(this,function(_n){switch(_n.label){case 0:if(!(t instanceof HTMLLinkElement))return[3,7];if(Wr=H(Fa(),2),Gr=Wr[0],on=Wr[1],on&&ke(on),!(!Gr&&!on&&!se||se&&!t.sheet||io(on)))return[3,5];_n.label=1;case 1:return _n.trys.push([1,3,,4]),Ft("Linkelement "+qt+" is not loaded yet and thus will be await for",t),[4,il(t,qt)];case 2:return _n.sent(),[3,4];case 3:return En=_n.sent(),ke(En),St=!0,[3,4];case 4:if(K)return[2,null];Ba=H(Fa(),2),Gr=Ba[0],on=Ba[1],on&&ke(on),_n.label=5;case 5:return Gr&&!pe(Gr,!1)?[2,Gr]:[4,Ao(t.href)];case 6:return ht=_n.sent(),Ln=_e(t.href),K?[2,null]:[3,8];case 7:if(q())ht=t.textContent.trim(),Ln=_e(location.href);else return[2,null];_n.label=8;case 8:if(!ht)return[3,13];_n.label=9;case 9:return _n.trys.push([9,11,,12]),[4,Oo(ht,Ln)];case 10:return ln=_n.sent(),C=Lo(t,ln),[3,12];case 11:return Ga=_n.sent(),ke(Ga),[3,12];case 12:if(C)return R=hr(C,"prev-sibling"),[2,C.sheet.cssRules];_n.label=13;case 13:return[2,null]}})})}function kr(ht){var Ln=Je();return Ln?{rules:Ln}:ht.secondRound?(ke("Detected dead-lock at details(), returning early to prevent it."),null):(Pt||St||(Pt=!0,c(),Dr().then(function(Wr){Pt=!1,g(),Wr&&i()}).catch(function(Wr){ke(Wr),Pt=!1,g()})),null)}var wn=!1;function fi(ht,Ln){var Wr=Je();if(!Wr)return;K=!1;function Gr(ln){try{if(ln.replaceSync){ln.replaceSync("");return}}catch(Ba){ke("Could not use fastpath for removing rules from stylesheet",Ba)}for(var Ga=ln.cssRules.length-1;Ga>=0;Ga--)ln.deleteRule(Ga)}function on(){F||Ot(),N&&N.stop(),nt(),F.sheet==null&&(F.textContent="");var ln=F.sheet;return Gr(ln),N?N.run():N=hr(F,"prev-sibling",function(){wn=!0,En()}),F.sheet}function En(){var ln=wn;wn=!1,Me.modifySheet({prepareSheet:on,sourceCSSRules:Wr,theme:ht,ignoreImageAnalysis:Ln,force:ln,isAsyncCancelled:function(){return K}}),Te=F.sheet.cssRules.length===0,Me.shouldRebuildStyle()&&mt(function(){return i()})}En()}function Fa(){try{return t.sheet==null?[null,null]:[t.sheet.cssRules,null]}catch(ht){return[null,ht]}}function io(ht){return ht&&ht.message&&ht.message.includes("loading")}function ko(){var ht=H(Fa(),2),Ln=ht[0],Wr=ht[1];return Wr?(ke(Wr),null):Ln}function dl(){va(),!be&&!(Ii&&t.sheet)&&gr()}var oo=null,zt=null;function fr(){var ht=ko();return ht?ht.length:null}function Ir(){return fr()!==oo}function gr(){oo=fr(),mr();var ht=function(){if(Ir()&&(oo=fr(),i()),Ii&&t.sheet){mr();return}zt=requestAnimationFrame(ht)};ht()}function mr(){cancelAnimationFrame(zt)}var Ur=!1;function Tr(){if(Ii=!0,mr(),Ur)return;function ht(){Ur=!1,!K&&i()}Ur=!0,typeof queueMicrotask=="function"?queueMicrotask(ht):requestAnimationFrame(ht)}function va(){t.addEventListener("__darkreader__updateSheet",Tr)}function kn(){t.removeEventListener("__darkreader__updateSheet",Tr)}function qn(){kn(),mr()}function Tn(){ae.disconnect(),K=!0,R&&R.stop(),N&&N.stop(),qn()}function On(){if(Tn(),er(C),er(F),g(),Ta.has(qt)){var ht=Ta.get(qt);Ta.delete(qt),ht&&ht()}}function gn(){ae.observe(t,$),t instanceof HTMLStyleElement&&dl()}var ea=10,Hn=0;function vi(){if(!!F){if(Hn++,Hn>ea){ke("Style sheet was moved multiple times",t);return}ke("Restore style",F,t),nt(),R&&R.skip(),N&&N.skip(),Te||(wn=!0,i())}}return{details:kr,render:fi,pause:Tn,destroy:On,watch:gn,restore:vi}}function il(t,r){return M(this,void 0,void 0,function(){return G(this,function(i){return[2,new Promise(function(c,g){var h=function(){t.removeEventListener("load",x),t.removeEventListener("error",C),Ta.delete(r)},x=function(){h(),Ft("Linkelement "+r+" has been loaded"),c()},C=function(){h(),g("Linkelement "+r+" couldn't be loaded. "+t.href)};Ta.set(r,function(){h(),g()}),t.addEventListener("load",x),t.addEventListener("error",C),t.href||C()})]})})}function Zo(t){return Ce(t.substring(7).trim().replace(/;$/,"").replace(/screen$/,""))}function Ao(t){return M(this,void 0,void 0,function(){return G(this,function(r){switch(r.label){case 0:return t.startsWith("data:")?[4,fetch(t)]:[3,3];case 1:return[4,r.sent().text()];case 2:return[2,r.sent()];case 3:return[4,vo({url:t,responseType:"text",mimeType:"text/css",origin:window.location.origin})];case 4:return[2,r.sent()]}})})}function Oo(t,r,i){return i===void 0&&(i=new Map),M(this,void 0,void 0,function(){var c,g,h,x,C,F,R,N,K,Te,Me;return G(this,function(ae){switch(ae.label){case 0:t=Ee(t),t=tt(t),t=rt(t,r),c=dn(Re,t),ae.label=1;case 1:ae.trys.push([1,10,11,12]),g=Y(c),h=g.next(),ae.label=2;case 2:return h.done?[3,9]:(x=h.value,C=Zo(x),F=Or(r,C),R=void 0,i.has(F)?(R=i.get(F),[3,7]):[3,3]);case 3:return ae.trys.push([3,6,,7]),[4,Ao(F)];case 4:return R=ae.sent(),i.set(F,R),[4,Oo(R,_e(F),i)];case 5:return R=ae.sent(),[3,7];case 6:return N=ae.sent(),ke(N),R="",[3,7];case 7:t=t.split(x).join(R),ae.label=8;case 8:return h=g.next(),[3,2];case 9:return[3,12];case 10:return K=ae.sent(),Te={error:K},[3,12];case 11:try{h&&!h.done&&(Me=g.return)&&Me.call(g)}finally{if(Te)throw Te.error}return[7];case 12:return t=t.trim(),[2,t]}})})}function Lo(t,r){if(!r)return null;var i=document.createElement("style");return i.classList.add("darkreader"),i.classList.add("darkreader--cors"),i.media="screen",i.textContent=r,t.parentNode.insertBefore(i,t.nextSibling),i.sheet.disabled=!0,Io.add(i),i}var Ka=[],no,Ea=new Map,ao;function Ai(t){!Et||Mt(t.querySelectorAll(":not(:defined)"),function(r){var i=r.tagName.toLowerCase();if(!i.includes("-")){var c=r.getAttribute("is");if(c)i=c;else return}Ea.has(i)||(Ea.set(i,new Set),ol(i).then(function(){if(ao){var g=Ea.get(i);Ea.delete(i),ao(Array.from(g))}})),Ea.get(i).add(r)})}var Oi=!1;typeof document!="undefined"&&document.addEventListener("__darkreader__inlineScriptsAllowed",function(){Oi=!0});var da=new Map;function Do(t){if(Oi=!0,da.has(t.detail.tag)){var r=da.get(t.detail.tag);r()}}function ol(t){return M(this,void 0,void 0,function(){return G(this,function(r){return[2,new Promise(function(i){if(window.customElements&&typeof customElements.whenDefined=="function")customElements.whenDefined(t).then(function(){return i()});else if(Oi)da.set(t,i),document.dispatchEvent(new CustomEvent("__darkreader__addUndefinedResolver",{detail:{tag:t}}));else{var c=function(){var g=Ea.get(t);g&&g.size>0&&(g.values().next().value.matches(":defined")?i():requestAnimationFrame(c))};requestAnimationFrame(c)}})]})})}function ll(t){ao=t}function si(){ao=null,Ea.clear(),document.removeEventListener("__darkreader__isDefined",Do)}function sl(t,r,i){ci();var c=new Set(t),g=new WeakMap,h=new WeakMap;function x($){g.set($,$.previousElementSibling),h.set($,$.nextElementSibling)}function C($){g.delete($),h.delete($)}function F($){return $.previousElementSibling!==g.get($)||$.nextElementSibling!==h.get($)}t.forEach(x);function R($){var q=$.createdStyles,pe=$.removedStyles,Je=$.movedStyles;q.forEach(function(nt){return x(nt)}),Je.forEach(function(nt){return x(nt)}),pe.forEach(function(nt){return C(nt)}),q.forEach(function(nt){return c.add(nt)}),pe.forEach(function(nt){return c.delete(nt)}),q.size+pe.size+Je.size>0&&r({created:Array.from(q),removed:Array.from(pe),moved:Array.from(Je),updated:[]})}function N($){var q=$.additions,pe=$.moves,Je=$.deletions,nt=new Set,Ot=new Set,Pt=new Set;q.forEach(function(St){return wa(St).forEach(function(qt){return nt.add(qt)})}),Je.forEach(function(St){return wa(St).forEach(function(qt){return Ot.add(qt)})}),pe.forEach(function(St){return wa(St).forEach(function(qt){return Pt.add(qt)})}),R({createdStyles:nt,removedStyles:Ot,movedStyles:Pt}),q.forEach(function(St){It(St,ae),Ai(St)})}function K($){var q=new Set(wa($)),pe=new Set,Je=new Set,nt=new Set;q.forEach(function(Ot){c.has(Ot)||pe.add(Ot)}),c.forEach(function(Ot){q.has(Ot)||Je.add(Ot)}),q.forEach(function(Ot){!pe.has(Ot)&&!Je.has(Ot)&&F(Ot)&&nt.add(Ot)}),R({createdStyles:pe,removedStyles:Je,movedStyles:nt}),It($,ae),Ai($)}function Te($){var q=new Set,pe=new Set;$.forEach(function(Je){var nt=Je.target;nt.isConnected&&(Ri(nt)?q.add(nt):nt instanceof HTMLLinkElement&&nt.disabled&&pe.add(nt))}),q.size+pe.size>0&&r({updated:Array.from(q),created:[],removed:Array.from(pe),moved:[]})}function Me($){var q=Mr($,{onMinorMutations:N,onHugeMutations:K}),pe=new MutationObserver(Te);pe.observe($,{attributes:!0,attributeFilter:["rel","disabled","media"],subtree:!0}),Ka.push(q,pe),no.add($)}function ae($){var q=$.shadowRoot;q==null||no.has(q)||(Me(q),i(q))}Me(document),It(document.documentElement,ae),ll(function($){var q=[];$.forEach(function(pe){return xr(q,wa(pe.shadowRoot))}),r({created:q,updated:[],removed:[],moved:[]}),$.forEach(function(pe){var Je=pe.shadowRoot;Je!=null&&(ae(pe),It(Je,ae),Ai(Je))})}),document.addEventListener("__darkreader__isDefined",Do),Ai(document)}function jo(){Ka.forEach(function(t){return t.disconnect()}),Ka.splice(0,Ka.length),no=new WeakSet}function ci(){jo(),si()}function cl(t){return(t<16?"0":"")+t.toString(16)}function yl(){if(typeof crypto=="undefined")return"xxx-xxxx";if("randomUUID"in crypto){var t=crypto.randomUUID();return t.substring(0,8)+t.substring(9,13)+t.substring(14,18)+t.substring(19,23)+t.substring(24)}return Array.from(crypto.getRandomValues(new Uint8Array(16))).map(function(r){return cl(r)}).join("")}var Fo=new WeakMap,fa=new WeakSet;function ui(t){var r=!1;function i(h,x){var C=oe([],H(t.adoptedStyleSheets),!1),F=C.indexOf(h),R=C.indexOf(x);F!==R-1&&(R>=0&&C.splice(R,1),C.splice(F+1,0,x),t.adoptedStyleSheets=C)}function c(){r=!0;var h=oe([],H(t.adoptedStyleSheets),!1);t.adoptedStyleSheets.forEach(function(x){if(fa.has(x)){var C=h.indexOf(x);C>=0&&h.splice(C,1),Fo.delete(x),fa.delete(x)}}),t.adoptedStyleSheets=h}function g(h,x){t.adoptedStyleSheets.forEach(function(C){if(fa.has(C))return;var F=C.rules,R=new CSSStyleSheet;function N(){for(var Te=R.cssRules.length-1;Te>=0;Te--)R.deleteRule(Te);return i(C,R),Fo.set(C,R),fa.add(R),R}var K=Ro();K.modifySheet({prepareSheet:N,sourceCSSRules:F,theme:h,ignoreImageAnalysis:x,force:!1,isAsyncCancelled:function(){return r}})})}return{render:g,destroy:c}}function Cn(t){document.dispatchEvent(new CustomEvent("__darkreader__inlineScriptsAllowed"));var r=Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype,"addRule"),i=Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype,"insertRule"),c=Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype,"deleteRule"),g=Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype,"removeRule"),h=t?Object.getOwnPropertyDescriptor(Document.prototype,"styleSheets"):null,x=location.hostname.endsWith("baidu.com"),C=x?Object.getOwnPropertyDescriptor(Element.prototype,"getElementsByTagName"):null,F=function(){Object.defineProperty(CSSStyleSheet.prototype,"addRule",r),Object.defineProperty(CSSStyleSheet.prototype,"insertRule",i),Object.defineProperty(CSSStyleSheet.prototype,"deleteRule",c),Object.defineProperty(CSSStyleSheet.prototype,"removeRule",g),document.removeEventListener("__darkreader__cleanUp",F),document.removeEventListener("__darkreader__addUndefinedResolver",R),t&&Object.defineProperty(Document.prototype,"styleSheets",h),x&&Object.defineProperty(Element.prototype,"getElementsByTagName",C)},R=function(pe){customElements.whenDefined(pe.detail.tag).then(function(){document.dispatchEvent(new CustomEvent("__darkreader__isDefined",{detail:{tag:pe.detail.tag}}))})};document.addEventListener("__darkreader__cleanUp",F),document.addEventListener("__darkreader__addUndefinedResolver",R);var N=new Event("__darkreader__updateSheet");function K(pe,Je,nt){return r.value.call(this,pe,Je,nt),this.ownerNode&&!this.ownerNode.classList.contains("darkreader")&&this.ownerNode.dispatchEvent(N),-1}function Te(pe,Je){var nt=i.value.call(this,pe,Je);return this.ownerNode&&!this.ownerNode.classList.contains("darkreader")&&this.ownerNode.dispatchEvent(N),nt}function Me(pe){c.value.call(this,pe),this.ownerNode&&!this.ownerNode.classList.contains("darkreader")&&this.ownerNode.dispatchEvent(N)}function ae(pe){g.value.call(this,pe),this.ownerNode&&!this.ownerNode.classList.contains("darkreader")&&this.ownerNode.dispatchEvent(N)}function $(){var pe=h.get.call(this),Je=oe([],H(pe),!1).filter(function(nt){return!nt.ownerNode.classList.contains("darkreader")});return Object.setPrototypeOf(Je,StyleSheetList.prototype)}function q(pe){var Je=this;if(pe!=="style")return C.value.call(this,pe);var nt=function(){var St=C.value.call(Je,pe);return Object.setPrototypeOf(oe([],H(St),!1).filter(function(qt){return!qt.classList.contains("darkreader")}),NodeList.prototype)},Ot=nt(),Pt={get:function(St,qt){return nt()[Number(qt)]}};return Ot=new Proxy(Ot,Pt),Ot}Object.defineProperty(CSSStyleSheet.prototype,"addRule",Object.assign({},r,{value:K})),Object.defineProperty(CSSStyleSheet.prototype,"insertRule",Object.assign({},i,{value:Te})),Object.defineProperty(CSSStyleSheet.prototype,"deleteRule",Object.assign({},c,{value:Me})),Object.defineProperty(CSSStyleSheet.prototype,"removeRule",Object.assign({},g,{value:ae})),t&&Object.defineProperty(Document.prototype,"styleSheets",Object.assign({},h,{get:$})),x&&Object.defineProperty(Element.prototype,"getElementsByTagName",Object.assign({},C,{value:q}))}var $a=yl(),In=new Map,Li=[],Tt=null,Qt=null,Bo=null,Nn=null,di=null;function Zn(t,r){r===void 0&&(r=document.head||document);var i=r.querySelector("."+t);return i||(i=document.createElement("style"),i.classList.add("darkreader"),i.classList.add(t),i.media="screen",i.textContent=""),i}function ul(t,r){r===void 0&&(r=document.head||document);var i=r.querySelector("."+t);return i||(i=document.createElement("script"),i.classList.add("darkreader"),i.classList.add(t)),i}var l=new Map;function o(t,r){l.has(r)&&l.get(r).stop(),l.set(r,hr(t,"parent"))}function f(){Mt(l.values(),function(t){return t.stop()}),l.clear()}function y(){var t=Zn("darkreader--fallback",document);t.textContent=_i(Tt,{strict:!0}),document.head.insertBefore(t,document.head.firstChild),o(t,"fallback");var r=Zn("darkreader--user-agent");r.textContent=go(Tt,Bo,Tt.styleSystemControls),document.head.insertBefore(r,t.nextSibling),o(r,"user-agent");var i=Zn("darkreader--text");Tt.useFont||Tt.textStroke>0?i.textContent=hl(Tt):i.textContent="",document.head.insertBefore(i,t.nextSibling),o(i,"text");var c=Zn("darkreader--invert");Qt&&Array.isArray(Qt.invert)&&Qt.invert.length>0?c.textContent=[Qt.invert.join(", ")+" {","    filter: "+ti(P(P({},Tt),{contrast:Tt.mode===0?Tt.contrast:Fn(Tt.contrast-10,0,100)}))+" !important;","}"].join(`
`):c.textContent="",document.head.insertBefore(c,i.nextSibling),o(c,"invert");var g=Zn("darkreader--inline");g.textContent=Eo(),document.head.insertBefore(g,c.nextSibling),o(g,"inline");var h=Zn("darkreader--override");h.textContent=Qt&&Qt.css?L(Qt.css):"",document.head.appendChild(h),o(h,"override");var x=Zn("darkreader--variables"),C=mo(Tt),F=Tt.darkSchemeBackgroundColor,R=Tt.darkSchemeTextColor,N=Tt.lightSchemeBackgroundColor,K=Tt.lightSchemeTextColor,Te=Tt.mode,Me=Te===0?N:F,ae=Te===0?K:R;Me=Qr(_(Me),Tt),ae=Gn(_(ae),Tt),x.textContent=[":root {","   --darkreader-neutral-background: "+Me+";","   --darkreader-neutral-text: "+ae+";","   --darkreader-selection-background: "+C.backgroundColorSelection+";","   --darkreader-selection-text: "+C.foregroundColorSelection+";","}"].join(`
`),document.head.insertBefore(x,g.nextSibling),o(x,"variables");var $=Zn("darkreader--root-vars");document.head.insertBefore($,x.nextSibling);var q=ul("darkreader--proxy");q.append("("+Cn+")(!"+(Qt&&Qt.disableStyleSheetsProxy)+")"),document.head.insertBefore(q,$.nextSibling),q.remove()}var E=new Set;function Z(t){var r=Zn("darkreader--inline",t);r.textContent=Eo(),t.insertBefore(r,t.firstChild);var i=Zn("darkreader--override",t);i.textContent=Qt&&Qt.css?L(Qt.css):"",t.insertBefore(i,r.nextSibling);var c=Zn("darkreader--invert",t);Qt&&Array.isArray(Qt.invert)&&Qt.invert.length>0?c.textContent=[Qt.invert.join(", ")+" {","    filter: "+ti(P(P({},Tt),{contrast:Tt.mode===0?Tt.contrast:Fn(Tt.contrast-10,0,100)}))+" !important;","}"].join(`
`):c.textContent="",t.insertBefore(c,i.nextSibling),E.add(t)}function L(t){return t.replace(/\${(.+?)}/g,function(r,i){var c=xa(i);return c?qa(c,Tt):(ke("Couldn't parse CSSTemplate's color."),i)})}function k(){var t=document.querySelector(".darkreader--fallback");t&&(t.textContent="")}function te(){he();var t=wa(document),r=t.filter(function(c){return!In.has(c)}).map(function(c){return de(c)});r.map(function(c){return c.details({secondRound:!1})}).filter(function(c){return c&&c.rules.length>0}).forEach(function(c){hn.addRulesForMatching(c.rules)}),hn.matchVariablesAndDependants(),hn.setOnRootVariableChange(function(){hn.putRootVars(document.head.querySelector(".darkreader--root-vars"),Tt)}),hn.putRootVars(document.head.querySelector(".darkreader--root-vars"),Tt),In.forEach(function(c){return c.render(Tt,Nn)}),re.size===0&&k(),r.forEach(function(c){return c.watch()});var i=wr(document.querySelectorAll(Wa));It(document.documentElement,function(c){Z(c.shadowRoot);var g=c.shadowRoot.querySelectorAll(Wa);g.length>0&&xr(i,g)}),i.forEach(function(c){return Mi(c,Tt,di,Nn)}),ze(document)}var ce=0,re=new Set;function de(t){var r=++ce;Ft("New manager for element, with loadingStyleID "+r,t);function i(){if(!ge()||!Fe){re.add(r),Ft("Current amount of styles loading: "+re.size);var x=document.querySelector(".darkreader--fallback");x.textContent||(x.textContent=_i(Tt,{strict:!1}))}}function c(){re.delete(r),Ft("Removed loadingStyle "+r+", now awaiting: "+re.size),Ft("To-do to be loaded",re),re.size===0&&ge()&&k()}function g(){var x=h.details({secondRound:!0});!x||(hn.addRulesForMatching(x.rules),hn.matchVariablesAndDependants(),h.render(Tt,Nn))}var h=ro(t,{update:g,loadingStart:i,loadingEnd:c});return In.set(t,h),h}function me(t){var r=In.get(t);r&&(r.destroy(),In.delete(t))}var ue=cr(function(t){In.forEach(function(r){return r.render(Tt,Nn)}),Li.forEach(function(r){return r.render(Tt,Nn)}),t&&t()}),he=function(){ue.cancel()};function Ie(){if(re.size===0){k();return}ke("DOM is ready, but still have styles being loaded.",re)}var Ze=null,Fe=typeof document!="undefined"&&!document.hidden;function xe(t){var r=Boolean(Ze);Ze=function(){document.hidden||(Pe(),t(),Fe=!0)},r||document.addEventListener("visibilitychange",Ze)}function Pe(){document.removeEventListener("visibilitychange",Ze),Ze=null}function De(){y();function t(){te(),jt()}document.hidden?xe(t):t(),Ji(Tt)}function ze(t){if(Array.isArray(t.adoptedStyleSheets)&&t.adoptedStyleSheets.length>0){var r=ui(t);Li.push(r),r.render(Tt,Nn)}}function jt(){var t=Array.from(In.keys());sl(t,function(r){var i=r.created,c=r.updated,g=r.removed,h=r.moved,x=g,C=i.concat(c).concat(h).filter(function(N){return!In.has(N)}),F=h.filter(function(N){return In.has(N)});Ft("Styles to be removed:",x),x.forEach(function(N){return me(N)});var R=C.map(function(N){return de(N)});R.map(function(N){return N.details({secondRound:!1})}).filter(function(N){return N&&N.rules.length>0}).forEach(function(N){hn.addRulesForMatching(N.rules)}),hn.matchVariablesAndDependants(),R.forEach(function(N){return N.render(Tt,Nn)}),R.forEach(function(N){return N.watch()}),F.forEach(function(N){return In.get(N).restore()})},function(r){Z(r),ze(r)}),Mo(function(r){if(Mi(r,Tt,di,Nn),r===document.documentElement){var i=r.getAttribute("style");i.includes("--")&&(hn.matchVariablesAndDependants(),hn.putRootVars(document.head.querySelector(".darkreader--root-vars"),Tt))}},function(r){Z(r);var i=r.querySelectorAll(Wa);i.length>0&&Mt(i,function(c){return Mi(c,Tt,di,Nn)})}),kt(Ie)}function Be(){In.forEach(function(t){return t.pause()}),f(),ci(),nr(),Xt(Ie),Yt()}function Nr(){var t=document.createElement("meta");t.name="darkreader",t.content=$a,document.head.appendChild(t)}function An(){var t=document.querySelector('meta[name="darkreader"]');return t?t.content!==$a:(Nr(),!1)}function Jt(t,r,i){if(Tt=t,Qt=r,Qt?(Nn=Array.isArray(Qt.ignoreImageAnalysis)?Qt.ignoreImageAnalysis:[],di=Array.isArray(Qt.ignoreInlineStyle)?Qt.ignoreInlineStyle:[]):(Nn=[],di=[]),Bo=i,document.head){if(An())return;document.documentElement.setAttribute("data-darkreader-mode","dynamic"),document.documentElement.setAttribute("data-darkreader-scheme",Tt.mode?"dark":"dimmed"),De()}else{if(!qe){var c=Zn("darkreader--fallback");document.documentElement.appendChild(c),c.textContent=_i(Tt,{strict:!0})}var g=new MutationObserver(function(){if(document.head){if(g.disconnect(),An()){zr();return}De()}});g.observe(document,{childList:!0,subtree:!0})}}function ja(){document.dispatchEvent(new CustomEvent("__darkreader__cleanUp")),er(document.head.querySelector(".darkreader--proxy"))}function zr(){document.documentElement.removeAttribute("data-darkreader-mode"),document.documentElement.removeAttribute("data-darkreader-scheme"),Ma(),er(document.querySelector(".darkreader--fallback")),document.head&&(Po(),er(document.head.querySelector(".darkreader--user-agent")),er(document.head.querySelector(".darkreader--text")),er(document.head.querySelector(".darkreader--invert")),er(document.head.querySelector(".darkreader--inline")),er(document.head.querySelector(".darkreader--override")),er(document.head.querySelector(".darkreader--variables")),er(document.head.querySelector(".darkreader--root-vars")),er(document.head.querySelector('meta[name="darkreader"]')),ja()),E.forEach(function(t){er(t.querySelector(".darkreader--inline")),er(t.querySelector(".darkreader--override"))}),E.clear(),Mt(In.keys(),function(t){return me(t)}),re.clear(),Zi(),Mt(document.querySelectorAll(".darkreader"),er),Li.forEach(function(t){t.destroy()}),Li.splice(0)}function Ma(){hn.clear(),ur.clear(),Pe(),he(),Be(),Ui()}var an=/url\(\"(blob\:.*?)\"\)/g;function bt(t){return M(this,void 0,void 0,function(){var r,i;return G(this,function(c){switch(c.label){case 0:return r=[],dn(an,t,1).forEach(function(g){var h=fe(g);r.push(h)}),[4,Promise.all(r)];case 1:return i=c.sent(),[2,t.replace(an,function(){return'url("'+i.shift()+'")'})]}})})}var Ue=`/*
                        _______
                       /       \\
                      .==.    .==.
                     ((  ))==((  ))
                    / "=="    "=="\\
                   /____|| || ||___\\
       ________     ____    ________  ___    ___
       |  ___  \\   /    \\   |  ___  \\ |  |  /  /
       |  |  \\  \\ /  /\\  \\  |  |  \\  \\|  |_/  /
       |  |   )  /  /__\\  \\ |  |__/  /|  ___  \\
       |  |__/  /  ______  \\|  ____  \\|  |  \\  \\
_______|_______/__/ ____ \\__\\__|___\\__\\__|___\\__\\____
|  ___  \\ |  ____/ /    \\   |  ___  \\ |  ____|  ___  \\
|  |  \\  \\|  |___ /  /\\  \\  |  |  \\  \\|  |___|  |  \\  \\
|  |__/  /|  ____/  /__\\  \\ |  |   )  |  ____|  |__/  /
|  ____  \\|  |__/  ______  \\|  |__/  /|  |___|  ____  \\
|__|   \\__\\____/__/      \\__\\_______/ |______|__|   \\__\\
                https://darkreader.org
*/

/*! Dark reader generated CSS | Licensed under MIT https://github.com/darkreader/darkreader/blob/master/LICENSE */
`;function ft(){return M(this,void 0,void 0,function(){function t(x,C){var F=document.querySelector(x);F&&F.textContent&&(r.push("/* "+C+" */"),r.push(F.textContent),r.push(""))}var r,i,c,g,h;return G(this,function(x){switch(x.label){case 0:return r=[Ue],t(".darkreader--fallback","Fallback Style"),t(".darkreader--user-agent","User-Agent Style"),t(".darkreader--text","Text Style"),t(".darkreader--invert","Invert Style"),t(".darkreader--variables","Variables Style"),i=[],document.querySelectorAll(".darkreader--sync").forEach(function(C){Mt(C.sheet.cssRules,function(F){F&&F.cssText&&i.push(F.cssText)})}),i.length?(c=lo(i.join(`
`)),r.push("/* Modified CSS */"),h=(g=r).push,[4,bt(c)]):[3,2];case 1:h.apply(g,[x.sent()]),r.push(""),x.label=2;case 2:return t(".darkreader--override","Override Style"),[2,r.join(`
`)]}})})}var vt=!1,lt=function(){if(typeof window=="undefined")return!1;try{return window.self!==window.top}catch(t){return console.warn(t),!0}}();function tr(t,r){t===void 0&&(t={}),r===void 0&&(r=null);var i=P(P({},w),t);if(i.engine!==O.dynamicTheme)throw new Error("Theme engine is not supported.");Jt(i,r,lt),vt=!0}function Cr(){return vt}function _t(){zr(),vt=!1}var Kt={themeOptions:null,fixes:null};function Rr(){var t=matchMedia("(prefers-color-scheme: dark)");t.matches?tr(Kt.themeOptions,Kt.fixes):_t()}function Ut(t,r){t===void 0&&(t={}),r===void 0&&(r=null);var i=matchMedia("(prefers-color-scheme: dark)");t?(Kt={themeOptions:t,fixes:r},Rr(),ot?i.addEventListener("change",Rr):i.addListener(Rr)):(ot?i.removeEventListener("change",Rr):i.removeListener(Rr),_t())}function Pa(){return M(this,void 0,void 0,function(){return G(this,function(t){switch(t.label){case 0:return[4,ft()];case 1:return[2,t.sent()]}})})}var No=Nt;m.auto=Ut,m.disable=_t,m.enable=tr,m.exportGeneratedCSS=Pa,m.isEnabled=Cr,m.setFetchMethod=No,Object.defineProperty(m,"__esModule",{value:!0})})},45095:function(Ct,ve,m){"use strict";m.d(ve,{l:function(){return Y}});var P=m(67294),M=function(){return M=Object.assign||function(X){for(var B,J=1,we=arguments.length;J<we;J++){B=arguments[J];for(var be in B)Object.prototype.hasOwnProperty.call(B,be)&&(X[be]=B[be])}return X},M.apply(this,arguments)};function G(X){var B,J=(typeof window!="undefined"?window:{}).URL,we=new J((B=window==null?void 0:window.location)===null||B===void 0?void 0:B.href);return Object.keys(X).forEach(function(be){var qe=X[be];qe!=null?Array.isArray(qe)?(we.searchParams.delete(be),qe.forEach(function(se){we.searchParams.append(be,se)})):qe instanceof Date?Number.isNaN(qe.getTime())||we.searchParams.set(be,qe.toISOString()):typeof qe=="object"?we.searchParams.set(be,JSON.stringify(qe)):we.searchParams.set(be,qe):we.searchParams.delete(be)}),we}function Y(X,B){var J;X===void 0&&(X={}),B===void 0&&(B={disabled:!1});var we=(0,P.useState)(),be=we[1],qe=typeof window!="undefined"&&((J=window==null?void 0:window.location)===null||J===void 0?void 0:J.search),se=(0,P.useMemo)(function(){return B.disabled?{}:new URLSearchParams(qe||{})},[B.disabled,qe]),Qe=(0,P.useMemo)(function(){if(B.disabled)return{};if(typeof window=="undefined"||!window.URL)return{};var ot=[];se.forEach(function(p,fe){ot.push({key:fe,value:p})}),ot=ot.reduce(function(p,fe){return(p[fe.key]=p[fe.key]||[]).push(fe),p},{}),ot=Object.keys(ot).map(function(p){var fe=ot[p];return fe.length===1?[p,fe[0].value]:[p,fe.map(function(u){var U=u.value;return U})]});var Et=M({},X);return ot.forEach(function(p){var fe=p[0],u=p[1];Et[fe]=oe(fe,u,{},X)}),Et},[B.disabled,X,se]);function He(ot){if(!(typeof window=="undefined"||!window.URL)){var Et=G(ot);window.location.search!==Et.search&&window.history.replaceState({},"",Et.toString()),se.toString()!==Et.searchParams.toString()&&be({})}}(0,P.useEffect)(function(){B.disabled||typeof window=="undefined"||!window.URL||He(M(M({},X),Qe))},[B.disabled,Qe]);var At=function(ot){He(ot)};return(0,P.useEffect)(function(){if(B.disabled)return function(){};if(typeof window=="undefined"||!window.URL)return function(){};var ot=function(){be({})};return window.addEventListener("popstate",ot),function(){window.removeEventListener("popstate",ot)}},[B.disabled]),[Qe,At]}var H={true:!0,false:!1};function oe(X,B,J,we){if(!J)return B;var be=J[X],qe=B===void 0?we[X]:B;return be===Number?Number(qe):be===Boolean||B==="true"||B==="false"?H[qe]:Array.isArray(be)?be.find(function(se){return se==qe})||we[X]:qe}},35247:function(Ct,ve,m){"use strict";m.d(ve,{Z:function(){return Se}});var P=m(96156),M=m(22122),G=m(85061),Y=m(94184),H=m.n(Y),oe=m(50344),X=m(67294),B=m(53124),J=m(28682),we=m(96159),be=m(13622),qe=m(81555),se=function(Ke,O){var wt={};for(var w in Ke)Object.prototype.hasOwnProperty.call(Ke,w)&&O.indexOf(w)<0&&(wt[w]=Ke[w]);if(Ke!=null&&typeof Object.getOwnPropertySymbols=="function")for(var gt=0,w=Object.getOwnPropertySymbols(Ke);gt<w.length;gt++)O.indexOf(w[gt])<0&&Object.prototype.propertyIsEnumerable.call(Ke,w[gt])&&(wt[w[gt]]=Ke[w[gt]]);return wt},Qe=function(O){var wt=O.prefixCls,w=O.separator,gt=w===void 0?"/":w,Mt=O.children,xr=O.menu,wr=O.overlay,Ft=O.dropdownProps,ke=se(O,["prefixCls","separator","children","menu","overlay","dropdownProps"]),cr=X.useContext(B.E_),jr=cr.getPrefixCls,Vt=jr("breadcrumb",wt),er=function(ge){if(xr||wr){var st=(0,M.Z)({},Ft);return"overlay"in O&&(st.overlay=wr),X.createElement(qe.Z,(0,M.Z)({menu:xr,placement:"bottom"},st),X.createElement("span",{className:"".concat(Vt,"-overlay-link")},ge,X.createElement(be.Z,null)))}return ge},hr;return"href"in ke?hr=X.createElement("a",(0,M.Z)({className:"".concat(Vt,"-link")},ke),Mt):hr=X.createElement("span",(0,M.Z)({className:"".concat(Vt,"-link")},ke),Mt),hr=er(hr),Mt!=null?X.createElement("li",null,hr,gt&&X.createElement("span",{className:"".concat(Vt,"-separator")},gt)):null};Qe.__ANT_BREADCRUMB_ITEM=!0;var He=Qe,At=function(O){var wt=O.children,w=X.useContext(B.E_),gt=w.getPrefixCls,Mt=gt("breadcrumb");return X.createElement("span",{className:"".concat(Mt,"-separator")},wt||"/")};At.__ANT_BREADCRUMB_SEPARATOR=!0;var ot=At,Et=function(Ke,O){var wt={};for(var w in Ke)Object.prototype.hasOwnProperty.call(Ke,w)&&O.indexOf(w)<0&&(wt[w]=Ke[w]);if(Ke!=null&&typeof Object.getOwnPropertySymbols=="function")for(var gt=0,w=Object.getOwnPropertySymbols(Ke);gt<w.length;gt++)O.indexOf(w[gt])<0&&Object.prototype.propertyIsEnumerable.call(Ke,w[gt])&&(wt[w[gt]]=Ke[w[gt]]);return wt};function p(Ke,O){if(!Ke.breadcrumbName)return null;var wt=Object.keys(O).join("|"),w=Ke.breadcrumbName.replace(new RegExp(":(".concat(wt,")"),"g"),function(gt,Mt){return O[Mt]||gt});return w}function fe(Ke,O,wt,w){var gt=wt.indexOf(Ke)===wt.length-1,Mt=p(Ke,O);return gt?X.createElement("span",null,Mt):X.createElement("a",{href:"#/".concat(w.join("/"))},Mt)}var u=function(O,wt){return O=(O||"").replace(/^\//,""),Object.keys(wt).forEach(function(w){O=O.replace(":".concat(w),wt[w])}),O},U=function(O,wt,w){var gt=(0,G.Z)(O),Mt=u(wt||"",w);return Mt&&gt.push(Mt),gt},We=function(O){var wt=O.prefixCls,w=O.separator,gt=w===void 0?"/":w,Mt=O.style,xr=O.className,wr=O.routes,Ft=O.children,ke=O.itemRender,cr=ke===void 0?fe:ke,jr=O.params,Vt=jr===void 0?{}:jr,er=Et(O,["prefixCls","separator","style","className","routes","children","itemRender","params"]),hr=X.useContext(B.E_),It=hr.getPrefixCls,ge=hr.direction,st,kt=It("breadcrumb",wt);if(wr&&wr.length>0){var Xt=[];st=wr.map(function(ut){var mt=u(ut.path,Vt);mt&&Xt.push(mt);var Yt;ut.children&&ut.children.length&&(Yt=X.createElement(J.Z,{items:ut.children.map(function(Ht){return{key:Ht.path||Ht.breadcrumbName,label:cr(Ht,Vt,wr,U(Xt,Ht.path,Vt))}})}));var Zt={separator:gt};return Yt&&(Zt.overlay=Yt),X.createElement(He,(0,M.Z)({},Zt,{key:mt||ut.breadcrumbName}),cr(ut,Vt,wr,Xt))})}else Ft&&(st=(0,oe.Z)(Ft).map(function(ut,mt){return ut&&(0,we.Tm)(ut,{separator:gt,key:mt})}));var ct=H()(kt,(0,P.Z)({},"".concat(kt,"-rtl"),ge==="rtl"),xr);return X.createElement("nav",(0,M.Z)({className:ct,style:Mt},er),X.createElement("ol",null,st))};We.Item=He,We.Separator=ot;var Nt=We,Se=Nt},36017:function(Ct,ve,m){"use strict";var P=m(38663),M=m.n(P),G=m(81262),Y=m.n(G),H=m(59250),oe=m(30887)},68370:function(Ct,ve,m){"use strict";m.d(ve,{rS:function(){return P}});var P=null},43574:function(Ct,ve,m){"use strict";m.d(ve,{Z:function(){return hr}});var P=m(96156),M=m(22122),G=m(90484),Y=m(94184),H=m.n(Y),oe=m(67294),X=m(53124),B=m(98423),J=function(ge){var st=ge.prefixCls,kt=ge.className,Xt=ge.style,ct=ge.size,ut=ge.shape,mt=H()((0,P.Z)((0,P.Z)({},"".concat(st,"-lg"),ct==="large"),"".concat(st,"-sm"),ct==="small")),Yt=H()((0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(st,"-circle"),ut==="circle"),"".concat(st,"-square"),ut==="square"),"".concat(st,"-round"),ut==="round")),Zt=oe.useMemo(function(){return typeof ct=="number"?{width:ct,height:ct,lineHeight:"".concat(ct,"px")}:{}},[ct]);return oe.createElement("span",{className:H()(st,mt,Yt,kt),style:(0,M.Z)((0,M.Z)({},Zt),Xt)})},we=J,be=function(ge){var st=ge.prefixCls,kt=ge.className,Xt=ge.active,ct=ge.shape,ut=ct===void 0?"circle":ct,mt=ge.size,Yt=mt===void 0?"default":mt,Zt=oe.useContext(X.E_),Ht=Zt.getPrefixCls,rr=Ht("skeleton",st),br=(0,B.Z)(ge,["prefixCls","className"]),Ar=H()(rr,"".concat(rr,"-element"),(0,P.Z)({},"".concat(rr,"-active"),Xt),kt);return oe.createElement("div",{className:Ar},oe.createElement(we,(0,M.Z)({prefixCls:"".concat(rr,"-avatar"),shape:ut,size:Yt},br)))},qe=be,se=function(ge){var st=ge.prefixCls,kt=ge.className,Xt=ge.active,ct=ge.block,ut=ct===void 0?!1:ct,mt=ge.size,Yt=mt===void 0?"default":mt,Zt=oe.useContext(X.E_),Ht=Zt.getPrefixCls,rr=Ht("skeleton",st),br=(0,B.Z)(ge,["prefixCls"]),Ar=H()(rr,"".concat(rr,"-element"),(0,P.Z)((0,P.Z)({},"".concat(rr,"-active"),Xt),"".concat(rr,"-block"),ut),kt);return oe.createElement("div",{className:Ar},oe.createElement(we,(0,M.Z)({prefixCls:"".concat(rr,"-button"),size:Yt},br)))},Qe=se,He=m(28991),At={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM288 604a64 64 0 10128 0 64 64 0 10-128 0zm118-224a48 48 0 1096 0 48 48 0 10-96 0zm158 228a96 96 0 10192 0 96 96 0 10-192 0zm148-314a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"dot-chart",theme:"outlined"},ot=At,Et=m(27713),p=function(ge,st){return oe.createElement(Et.Z,(0,He.Z)((0,He.Z)({},ge),{},{ref:st,icon:ot}))},fe=oe.forwardRef(p),u=fe,U=function(ge){var st=ge.prefixCls,kt=ge.className,Xt=ge.style,ct=ge.active,ut=ge.children,mt=oe.useContext(X.E_),Yt=mt.getPrefixCls,Zt=Yt("skeleton",st),Ht=H()(Zt,"".concat(Zt,"-element"),(0,P.Z)({},"".concat(Zt,"-active"),ct),kt),rr=ut!=null?ut:oe.createElement(u,null);return oe.createElement("div",{className:Ht},oe.createElement("div",{className:H()("".concat(Zt,"-image"),kt),style:Xt},rr))},We=U,Nt="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",Se=function(ge){var st=ge.prefixCls,kt=ge.className,Xt=ge.style,ct=ge.active,ut=oe.useContext(X.E_),mt=ut.getPrefixCls,Yt=mt("skeleton",st),Zt=H()(Yt,"".concat(Yt,"-element"),(0,P.Z)({},"".concat(Yt,"-active"),ct),kt);return oe.createElement("div",{className:Zt},oe.createElement("div",{className:H()("".concat(Yt,"-image"),kt),style:Xt},oe.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:"".concat(Yt,"-image-svg")},oe.createElement("path",{d:Nt,className:"".concat(Yt,"-image-path")}))))},Ke=Se,O=function(ge){var st=ge.prefixCls,kt=ge.className,Xt=ge.active,ct=ge.block,ut=ge.size,mt=ut===void 0?"default":ut,Yt=oe.useContext(X.E_),Zt=Yt.getPrefixCls,Ht=Zt("skeleton",st),rr=(0,B.Z)(ge,["prefixCls"]),br=H()(Ht,"".concat(Ht,"-element"),(0,P.Z)((0,P.Z)({},"".concat(Ht,"-active"),Xt),"".concat(Ht,"-block"),ct),kt);return oe.createElement("div",{className:br},oe.createElement(we,(0,M.Z)({prefixCls:"".concat(Ht,"-input"),size:mt},rr)))},wt=O,w=m(85061),gt=function(ge){var st=function(Zt){var Ht=ge.width,rr=ge.rows,br=rr===void 0?2:rr;if(Array.isArray(Ht))return Ht[Zt];if(br-1===Zt)return Ht},kt=ge.prefixCls,Xt=ge.className,ct=ge.style,ut=ge.rows,mt=(0,w.Z)(Array(ut)).map(function(Yt,Zt){return oe.createElement("li",{key:Zt,style:{width:st(Zt)}})});return oe.createElement("ul",{className:H()(kt,Xt),style:ct},mt)},Mt=gt,xr=function(ge){var st=ge.prefixCls,kt=ge.className,Xt=ge.width,ct=ge.style;return oe.createElement("h3",{className:H()(st,kt),style:(0,M.Z)({width:Xt},ct)})},wr=xr;function Ft(It){return It&&(0,G.Z)(It)==="object"?It:{}}function ke(It,ge){return It&&!ge?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function cr(It,ge){return!It&&ge?{width:"38%"}:It&&ge?{width:"50%"}:{}}function jr(It,ge){var st={};return(!It||!ge)&&(st.width="61%"),!It&&ge?st.rows=3:st.rows=2,st}var Vt=function(ge){var st=ge.prefixCls,kt=ge.loading,Xt=ge.className,ct=ge.style,ut=ge.children,mt=ge.avatar,Yt=mt===void 0?!1:mt,Zt=ge.title,Ht=Zt===void 0?!0:Zt,rr=ge.paragraph,br=rr===void 0?!0:rr,Ar=ge.active,Kr=ge.round,Mr=oe.useContext(X.E_),cn=Mr.getPrefixCls,ur=Mr.direction,pt=cn("skeleton",st);if(kt||!("loading"in ge)){var ar=!!Yt,Or=!!Ht,Pn=!!br,un;if(ar){var Q=(0,M.Z)((0,M.Z)({prefixCls:"".concat(pt,"-avatar")},ke(Or,Pn)),Ft(Yt));un=oe.createElement("div",{className:"".concat(pt,"-header")},oe.createElement(we,(0,M.Z)({},Q)))}var $e;if(Or||Pn){var V;if(Or){var Ae=(0,M.Z)((0,M.Z)({prefixCls:"".concat(pt,"-title")},cr(ar,Pn)),Ft(Ht));V=oe.createElement(wr,(0,M.Z)({},Ae))}var Re;if(Pn){var Ce=(0,M.Z)((0,M.Z)({prefixCls:"".concat(pt,"-paragraph")},jr(ar,Or)),Ft(br));Re=oe.createElement(Mt,(0,M.Z)({},Ce))}$e=oe.createElement("div",{className:"".concat(pt,"-content")},V,Re)}var _e=H()(pt,(0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(pt,"-with-avatar"),ar),"".concat(pt,"-active"),Ar),"".concat(pt,"-rtl"),ur==="rtl"),"".concat(pt,"-round"),Kr),Xt);return oe.createElement("div",{className:_e,style:ct},un,$e)}return typeof ut!="undefined"?ut:null};Vt.Button=Qe,Vt.Avatar=qe,Vt.Input=wt,Vt.Image=Ke,Vt.Node=We;var er=Vt,hr=er},84164:function(Ct,ve,m){"use strict";m.d(ve,{Z:function(){return G}});var P=m(90484),M=m(67294);function G(Y,H,oe){var X=M.useRef({});function B(J){if(!X.current||X.current.data!==Y||X.current.childrenColumnName!==H||X.current.getRowKey!==oe){let be=function(qe){qe.forEach(function(se,Qe){var He=oe(se,Qe);we.set(He,se),se&&(0,P.Z)(se)==="object"&&H in se&&be(se[H]||[])})};var we=new Map;be(Y),X.current={data:Y,childrenColumnName:H,kvMap:we,getRowKey:oe}}return X.current.kvMap.get(J)}return[B]}},28293:function(Ct,ve){"use strict";ve.Z="4.24.16"},96671:function(Ct){(function(ve,m){Ct.exports=m()})(this,function(){"use strict";var ve="month",m="quarter";return function(P,M){var G=M.prototype;G.quarter=function(oe){return this.$utils().u(oe)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(oe-1))};var Y=G.add;G.add=function(oe,X){return oe=Number(oe),this.$utils().p(X)===m?this.add(3*oe,ve):Y.bind(this)(oe,X)};var H=G.startOf;G.startOf=function(oe,X){var B=this.$utils(),J=!!B.u(X)||X;if(B.p(oe)===m){var we=this.quarter()-1;return J?this.month(3*we).startOf(ve).startOf("day"):this.month(3*we+2).endOf(ve).endOf("day")}return H.bind(this)(oe,X)}}})},97435:function(Ct,ve){"use strict";function m(P,M){for(var G=Object.assign({},P),Y=0;Y<M.length;Y+=1){var H=M[Y];delete G[H]}return G}ve.Z=m},14779:function(Ct){Ct.exports=qe,Ct.exports.parse=M,Ct.exports.compile=G,Ct.exports.tokensToFunction=Y,Ct.exports.tokensToRegExp=be;var ve="/",m="./",P=new RegExp(["(\\\\.)","(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?"].join("|"),"g");function M(se,Qe){for(var He=[],At=0,ot=0,Et="",p=Qe&&Qe.delimiter||ve,fe=Qe&&Qe.delimiters||m,u=!1,U;(U=P.exec(se))!==null;){var We=U[0],Nt=U[1],Se=U.index;if(Et+=se.slice(ot,Se),ot=Se+We.length,Nt){Et+=Nt[1],u=!0;continue}var Ke="",O=se[ot],wt=U[2],w=U[3],gt=U[4],Mt=U[5];if(!u&&Et.length){var xr=Et.length-1;fe.indexOf(Et[xr])>-1&&(Ke=Et[xr],Et=Et.slice(0,xr))}Et&&(He.push(Et),Et="",u=!1);var wr=Ke!==""&&O!==void 0&&O!==Ke,Ft=Mt==="+"||Mt==="*",ke=Mt==="?"||Mt==="*",cr=Ke||p,jr=w||gt;He.push({name:wt||At++,prefix:Ke,delimiter:cr,optional:ke,repeat:Ft,partial:wr,pattern:jr?oe(jr):"[^"+H(cr)+"]+?"})}return(Et||ot<se.length)&&He.push(Et+se.substr(ot)),He}function G(se,Qe){return Y(M(se,Qe))}function Y(se){for(var Qe=new Array(se.length),He=0;He<se.length;He++)typeof se[He]=="object"&&(Qe[He]=new RegExp("^(?:"+se[He].pattern+")$"));return function(At,ot){for(var Et="",p=ot&&ot.encode||encodeURIComponent,fe=0;fe<se.length;fe++){var u=se[fe];if(typeof u=="string"){Et+=u;continue}var U=At?At[u.name]:void 0,We;if(Array.isArray(U)){if(!u.repeat)throw new TypeError('Expected "'+u.name+'" to not repeat, but got array');if(U.length===0){if(u.optional)continue;throw new TypeError('Expected "'+u.name+'" to not be empty')}for(var Nt=0;Nt<U.length;Nt++){if(We=p(U[Nt],u),!Qe[fe].test(We))throw new TypeError('Expected all "'+u.name+'" to match "'+u.pattern+'"');Et+=(Nt===0?u.prefix:u.delimiter)+We}continue}if(typeof U=="string"||typeof U=="number"||typeof U=="boolean"){if(We=p(String(U),u),!Qe[fe].test(We))throw new TypeError('Expected "'+u.name+'" to match "'+u.pattern+'", but got "'+We+'"');Et+=u.prefix+We;continue}if(u.optional){u.partial&&(Et+=u.prefix);continue}throw new TypeError('Expected "'+u.name+'" to be '+(u.repeat?"an array":"a string"))}return Et}}function H(se){return se.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function oe(se){return se.replace(/([=!:$/()])/g,"\\$1")}function X(se){return se&&se.sensitive?"":"i"}function B(se,Qe){if(!Qe)return se;var He=se.source.match(/\((?!\?)/g);if(He)for(var At=0;At<He.length;At++)Qe.push({name:At,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,pattern:null});return se}function J(se,Qe,He){for(var At=[],ot=0;ot<se.length;ot++)At.push(qe(se[ot],Qe,He).source);return new RegExp("(?:"+At.join("|")+")",X(He))}function we(se,Qe,He){return be(M(se,He),Qe,He)}function be(se,Qe,He){He=He||{};for(var At=He.strict,ot=He.start!==!1,Et=He.end!==!1,p=H(He.delimiter||ve),fe=He.delimiters||m,u=[].concat(He.endsWith||[]).map(H).concat("$").join("|"),U=ot?"^":"",We=se.length===0,Nt=0;Nt<se.length;Nt++){var Se=se[Nt];if(typeof Se=="string")U+=H(Se),We=Nt===se.length-1&&fe.indexOf(Se[Se.length-1])>-1;else{var Ke=Se.repeat?"(?:"+Se.pattern+")(?:"+H(Se.delimiter)+"(?:"+Se.pattern+"))*":Se.pattern;Qe&&Qe.push(Se),Se.optional?Se.partial?U+=H(Se.prefix)+"("+Ke+")?":U+="(?:"+H(Se.prefix)+"("+Ke+"))?":U+=H(Se.prefix)+"("+Ke+")"}}return Et?(At||(U+="(?:"+p+")?"),U+=u==="$"?"$":"(?="+u+")"):(At||(U+="(?:"+p+"(?="+u+"))?"),We||(U+="(?="+p+"|"+u+")")),new RegExp(U,X(He))}function qe(se,Qe,He){return se instanceof RegExp?B(se,Qe):Array.isArray(se)?J(se,Qe,He):we(se,Qe,He)}},48531:function(Ct,ve,m){"use strict";Ct=m.nmd(Ct);/** @license React v17.0.2
 * react-dom-test-utils.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var P=m(27418),M=m(67294),G=m(73935),Y=m(63840);function H(S){for(var _="https://reactjs.org/docs/error-decoder.html?invariant="+S,z=1;z<arguments.length;z++)_+="&args[]="+encodeURIComponent(arguments[z]);return"Minified React error #"+S+"; visit "+_+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var oe=M.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function X(S){var _=S,z=S;if(S.alternate)for(;_.return;)_=_.return;else{S=_;do _=S,(_.flags&1026)!=0&&(z=_.return),S=_.return;while(S)}return _.tag===3?z:null}function B(S){if(X(S)!==S)throw Error(H(188))}function J(S){var _=S.alternate;if(!_){if(_=X(S),_===null)throw Error(H(188));return _!==S?null:S}for(var z=S,Ne=_;;){var it=z.return;if(it===null)break;var Xe=it.alternate;if(Xe===null){if(Ne=it.return,Ne!==null){z=Ne;continue}break}if(it.child===Xe.child){for(Xe=it.child;Xe;){if(Xe===z)return B(it),S;if(Xe===Ne)return B(it),_;Xe=Xe.sibling}throw Error(H(188))}if(z.return!==Ne.return)z=it,Ne=Xe;else{for(var dt=!1,xt=it.child;xt;){if(xt===z){dt=!0,z=it,Ne=Xe;break}if(xt===Ne){dt=!0,Ne=it,z=Xe;break}xt=xt.sibling}if(!dt){for(xt=Xe.child;xt;){if(xt===z){dt=!0,z=Xe,Ne=it;break}if(xt===Ne){dt=!0,Ne=Xe,z=it;break}xt=xt.sibling}if(!dt)throw Error(H(189))}}if(z.alternate!==Ne)throw Error(H(190))}if(z.tag!==3)throw Error(H(188));return z.stateNode.current===z?S:_}function we(S){var _=S.keyCode;return"charCode"in S?(S=S.charCode,S===0&&_===13&&(S=13)):S=_,S===10&&(S=13),32<=S||S===13?S:0}function be(){return!0}function qe(){return!1}function se(S){function _(z,Ne,it,Xe,dt){this._reactName=z,this._targetInst=it,this.type=Ne,this.nativeEvent=Xe,this.target=dt,this.currentTarget=null;for(var xt in S)S.hasOwnProperty(xt)&&(z=S[xt],this[xt]=z?z(Xe):Xe[xt]);return this.isDefaultPrevented=(Xe.defaultPrevented!=null?Xe.defaultPrevented:Xe.returnValue===!1)?be:qe,this.isPropagationStopped=qe,this}return P(_.prototype,{preventDefault:function(){this.defaultPrevented=!0;var z=this.nativeEvent;z&&(z.preventDefault?z.preventDefault():typeof z.returnValue!="unknown"&&(z.returnValue=!1),this.isDefaultPrevented=be)},stopPropagation:function(){var z=this.nativeEvent;z&&(z.stopPropagation?z.stopPropagation():typeof z.cancelBubble!="unknown"&&(z.cancelBubble=!0),this.isPropagationStopped=be)},persist:function(){},isPersistent:be}),_}var Qe={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(S){return S.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},He=se(Qe),At=P({},Qe,{view:0,detail:0});se(At);var ot,Et,p,fe=P({},At,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gt,button:0,buttons:0,relatedTarget:function(S){return S.relatedTarget===void 0?S.fromElement===S.srcElement?S.toElement:S.fromElement:S.relatedTarget},movementX:function(S){return"movementX"in S?S.movementX:(S!==p&&(p&&S.type==="mousemove"?(ot=S.screenX-p.screenX,Et=S.screenY-p.screenY):Et=ot=0,p=S),ot)},movementY:function(S){return"movementY"in S?S.movementY:Et}});se(fe);var u=P({},fe,{dataTransfer:0});se(u);var U=P({},At,{relatedTarget:0});se(U);var We=P({},Qe,{animationName:0,elapsedTime:0,pseudoElement:0});se(We);var Nt=P({},Qe,{clipboardData:function(S){return"clipboardData"in S?S.clipboardData:window.clipboardData}});se(Nt);var Se=P({},Qe,{data:0});se(Se);var Ke={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},O={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},wt={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function w(S){var _=this.nativeEvent;return _.getModifierState?_.getModifierState(S):(S=wt[S])?!!_[S]:!1}function gt(){return w}var Mt=P({},At,{key:function(S){if(S.key){var _=Ke[S.key]||S.key;if(_!=="Unidentified")return _}return S.type==="keypress"?(S=we(S),S===13?"Enter":String.fromCharCode(S)):S.type==="keydown"||S.type==="keyup"?O[S.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gt,charCode:function(S){return S.type==="keypress"?we(S):0},keyCode:function(S){return S.type==="keydown"||S.type==="keyup"?S.keyCode:0},which:function(S){return S.type==="keypress"?we(S):S.type==="keydown"||S.type==="keyup"?S.keyCode:0}});se(Mt);var xr=P({},fe,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0});se(xr);var wr=P({},At,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gt});se(wr);var Ft=P({},Qe,{propertyName:0,elapsedTime:0,pseudoElement:0});se(Ft);var ke=P({},fe,{deltaX:function(S){return"deltaX"in S?S.deltaX:"wheelDeltaX"in S?-S.wheelDeltaX:0},deltaY:function(S){return"deltaY"in S?S.deltaY:"wheelDeltaY"in S?-S.wheelDeltaY:"wheelDelta"in S?-S.wheelDelta:0},deltaZ:0,deltaMode:0});se(ke);var cr=null;function jr(S){if(cr===null)try{var _=("require"+Math.random()).slice(0,7);cr=(Ct&&Ct[_]).call(Ct,"timers").setImmediate}catch(z){cr=function(Ne){var it=new MessageChannel;it.port1.onmessage=Ne,it.port2.postMessage(void 0)}}return cr(S)}var Vt=G.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events,er=Vt[5],hr=Vt[6],It=G.unstable_batchedUpdates,ge=oe.IsSomeRendererActing,st=typeof Y.unstable_flushAllWithoutAsserting=="function",kt=Y.unstable_flushAllWithoutAsserting||function(){for(var S=!1;er();)S=!0;return S};function Xt(S){try{kt(),jr(function(){kt()?Xt(S):S()})}catch(_){S(_)}}var ct=0,ut=!1,mt=G.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events[6],Yt=G.unstable_batchedUpdates,Zt=oe.IsSomeRendererActing;function Ht(S,_){jest.runOnlyPendingTimers(),jr(function(){try{Y.unstable_flushAllWithoutAsserting()?Ht(S,_):S()}catch(z){_(z)}})}function rr(S,_,z,Ne,it,Xe,dt,xt,yn){var tn=Array.prototype.slice.call(arguments,3);try{_.apply(z,tn)}catch(rn){this.onError(rn)}}var br=!1,Ar=null,Kr=!1,Mr=null,cn={onError:function(S){br=!0,Ar=S}};function ur(S,_,z,Ne,it,Xe,dt,xt,yn){br=!1,Ar=null,rr.apply(cn,arguments)}function pt(S,_,z,Ne,it,Xe,dt,xt,yn){if(ur.apply(this,arguments),br){if(br){var tn=Ar;br=!1,Ar=null}else throw Error(H(198));Kr||(Kr=!0,Mr=tn)}}var ar=G.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events,Or=ar[0],Pn=ar[1],un=ar[2],Q=ar[3],$e=ar[4];function V(){}function Ae(S,_){if(!S)return[];if(S=J(S),!S)return[];for(var z=S,Ne=[];;){if(z.tag===5||z.tag===6||z.tag===1||z.tag===0){var it=z.stateNode;_(it)&&Ne.push(it)}if(z.child)z.child.return=z,z=z.child;else{if(z===S)return Ne;for(;!z.sibling;){if(!z.return||z.return===S)return Ne;z=z.return}z.sibling.return=z.return,z=z.sibling}}}function Re(S,_){if(S&&!S._reactInternals){var z=""+S;throw S=Array.isArray(S)?"an array":S&&S.nodeType===1&&S.tagName?"a DOM node":z==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":z,Error(H(286,_,S))}}function Ce(S){return!(!S||S.nodeType!==1||!S.tagName)}function _e(S){return Ce(S)?!1:S!=null&&typeof S.render=="function"&&typeof S.setState=="function"}function rt(S,_){return _e(S)?S._reactInternals.type===_:!1}function Lt(S,_){return Re(S,"findAllInRenderedTree"),S?Ae(S._reactInternals,_):[]}function Ee(S,_){return Re(S,"scryRenderedDOMComponentsWithClass"),Lt(S,function(z){if(Ce(z)){var Ne=z.className;typeof Ne!="string"&&(Ne=z.getAttribute("class")||"");var it=Ne.split(/\s+/);if(!Array.isArray(_)){if(_===void 0)throw Error(H(11));_=_.split(/\s+/)}return _.every(function(Xe){return it.indexOf(Xe)!==-1})}return!1})}function yt(S,_){return Re(S,"scryRenderedDOMComponentsWithTag"),Lt(S,function(z){return Ce(z)&&z.tagName.toUpperCase()===_.toUpperCase()})}function tt(S,_){return Re(S,"scryRenderedComponentsWithType"),Lt(S,function(z){return rt(z,_)})}function Dt(S,_,z){var Ne=S.type||"unknown-event";S.currentTarget=Pn(z),pt(Ne,_,void 0,S),S.currentTarget=null}function Sr(S,_,z){for(var Ne=[];S;){Ne.push(S);do S=S.return;while(S&&S.tag!==5);S=S||null}for(S=Ne.length;0<S--;)_(Ne[S],"captured",z);for(S=0;S<Ne.length;S++)_(Ne[S],"bubbled",z)}function ir(S,_){var z=S.stateNode;if(!z)return null;var Ne=un(z);if(!Ne)return null;z=Ne[_];e:switch(_){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(Ne=!Ne.disabled)||(S=S.type,Ne=!(S==="button"||S==="input"||S==="select"||S==="textarea")),S=!Ne;break e;default:S=!1}if(S)return null;if(z&&typeof z!="function")throw Error(H(231,_,typeof z));return z}function Lr(S,_,z){S&&z&&z._reactName&&(_=ir(S,z._reactName))&&(z._dispatchListeners==null&&(z._dispatchListeners=[]),z._dispatchInstances==null&&(z._dispatchInstances=[]),z._dispatchListeners.push(_),z._dispatchInstances.push(S))}function dr(S,_,z){var Ne=z._reactName;_==="captured"&&(Ne+="Capture"),(_=ir(S,Ne))&&(z._dispatchListeners==null&&(z._dispatchListeners=[]),z._dispatchInstances==null&&(z._dispatchInstances=[]),z._dispatchListeners.push(_),z._dispatchInstances.push(S))}var et={},Yr=new Set(["mouseEnter","mouseLeave","pointerEnter","pointerLeave"]);function _r(S){return function(_,z){if(M.isValidElement(_))throw Error(H(228));if(_e(_))throw Error(H(229));var Ne="on"+S[0].toUpperCase()+S.slice(1),it=new V;it.target=_,it.type=S.toLowerCase();var Xe=Or(_),dt=new He(Ne,it.type,Xe,it,_);dt.persist(),P(dt,z),Yr.has(S)?dt&&dt._reactName&&Lr(dt._targetInst,null,dt):dt&&dt._reactName&&Sr(dt._targetInst,dr,dt),G.unstable_batchedUpdates(function(){if(Q(_),dt){var xt=dt._dispatchListeners,yn=dt._dispatchInstances;if(Array.isArray(xt))for(var tn=0;tn<xt.length&&!dt.isPropagationStopped();tn++)Dt(dt,xt[tn],yn[tn]);else xt&&Dt(dt,xt,yn);dt._dispatchListeners=null,dt._dispatchInstances=null,dt.isPersistent()||dt.constructor.release(dt)}if(Kr)throw xt=Mr,Kr=!1,Mr=null,xt}),$e()}}"blur cancel click close contextMenu copy cut auxClick doubleClick dragEnd dragStart drop focus input invalid keyDown keyPress keyUp mouseDown mouseUp paste pause play pointerCancel pointerDown pointerUp rateChange reset seeked submit touchCancel touchEnd touchStart volumeChange drag dragEnter dragExit dragLeave dragOver mouseMove mouseOut mouseOver pointerMove pointerOut pointerOver scroll toggle touchMove wheel abort animationEnd animationIteration animationStart canPlay canPlayThrough durationChange emptied encrypted ended error gotPointerCapture load loadedData loadedMetadata loadStart lostPointerCapture playing progress seeking stalled suspend timeUpdate transitionEnd waiting mouseEnter mouseLeave pointerEnter pointerLeave change select beforeInput compositionEnd compositionStart compositionUpdate".split(" ").forEach(function(S){et[S]=_r(S)}),ve.Simulate=et,ve.act=function(S){function _(){ct--,ge.current=z,hr.current=Ne}ut===!1&&(ut=!0,console.error("act(...) is not supported in production builds of React, and might not behave as expected.")),ct++;var z=ge.current,Ne=hr.current;ge.current=!0,hr.current=!0;try{var it=It(S)}catch(Xe){throw _(),Xe}if(it!==null&&typeof it=="object"&&typeof it.then=="function")return{then:function(Xe,dt){it.then(function(){1<ct||st===!0&&z===!0?(_(),Xe()):Xt(function(xt){_(),xt?dt(xt):Xe()})},function(xt){_(),dt(xt)})}};try{ct!==1||st!==!1&&z!==!1||kt(),_()}catch(Xe){throw _(),Xe}return{then:function(Xe){Xe()}}},ve.findAllInRenderedTree=Lt,ve.findRenderedComponentWithType=function(S,_){if(Re(S,"findRenderedComponentWithType"),S=tt(S,_),S.length!==1)throw Error("Did not find exactly one match (found: "+S.length+") for componentType:"+_);return S[0]},ve.findRenderedDOMComponentWithClass=function(S,_){if(Re(S,"findRenderedDOMComponentWithClass"),S=Ee(S,_),S.length!==1)throw Error("Did not find exactly one match (found: "+S.length+") for class:"+_);return S[0]},ve.findRenderedDOMComponentWithTag=function(S,_){if(Re(S,"findRenderedDOMComponentWithTag"),S=yt(S,_),S.length!==1)throw Error("Did not find exactly one match (found: "+S.length+") for tag:"+_);return S[0]},ve.isCompositeComponent=_e,ve.isCompositeComponentWithType=rt,ve.isDOMComponent=Ce,ve.isDOMComponentElement=function(S){return!!(S&&M.isValidElement(S)&&S.tagName)},ve.isElement=function(S){return M.isValidElement(S)},ve.isElementOfType=function(S,_){return M.isValidElement(S)&&S.type===_},ve.mockComponent=function(S,_){return _=_||S.mockTagName||"div",S.prototype.render.mockImplementation(function(){return M.createElement(_,null,this.props.children)}),this},ve.nativeTouchData=function(S,_){return{touches:[{pageX:S,pageY:_}]}},ve.renderIntoDocument=function(S){var _=document.createElement("div");return G.render(S,_)},ve.scryRenderedComponentsWithType=tt,ve.scryRenderedDOMComponentsWithClass=Ee,ve.scryRenderedDOMComponentsWithTag=yt,ve.traverseTwoPhase=Sr,ve.unstable_concurrentAct=function(S){function _(){Zt.current=z,mt.current=Ne}if(Y.unstable_flushAllWithoutAsserting===void 0)throw Error("This version of `act` requires a special mock build of Scheduler.");if(setTimeout._isMockFunction!==!0)throw Error("This version of `act` requires Jest's timer mocks (i.e. jest.useFakeTimers).");var z=Zt.current,Ne=mt.current;Zt.current=!0,mt.current=!0;try{var it=Yt(S);if(typeof it=="object"&&it!==null&&typeof it.then=="function")return{then:function(dt,xt){it.then(function(){Ht(function(){_(),dt()},function(yn){_(),xt(yn)})},function(yn){_(),xt(yn)})}};try{do var Xe=Y.unstable_flushAllWithoutAsserting();while(Xe)}finally{_()}}catch(dt){throw _(),dt}}},8267:function(Ct,ve,m){"use strict";Ct.exports=m(48531)},29405:function(Ct,ve,m){"use strict";m.d(ve,{J$:function(){return Or},ZP:function(){return un},kY:function(){return Mr}});var P=m(67294);/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function M(Q,$e,V,Ae){function Re(Ce){return Ce instanceof V?Ce:new V(function(_e){_e(Ce)})}return new(V||(V=Promise))(function(Ce,_e){function rt(yt){try{Ee(Ae.next(yt))}catch(tt){_e(tt)}}function Lt(yt){try{Ee(Ae.throw(yt))}catch(tt){_e(tt)}}function Ee(yt){yt.done?Ce(yt.value):Re(yt.value).then(rt,Lt)}Ee((Ae=Ae.apply(Q,$e||[])).next())})}function G(Q,$e){var V={label:0,sent:function(){if(Ce[0]&1)throw Ce[1];return Ce[1]},trys:[],ops:[]},Ae,Re,Ce,_e;return _e={next:rt(0),throw:rt(1),return:rt(2)},typeof Symbol=="function"&&(_e[Symbol.iterator]=function(){return this}),_e;function rt(Ee){return function(yt){return Lt([Ee,yt])}}function Lt(Ee){if(Ae)throw new TypeError("Generator is already executing.");for(;V;)try{if(Ae=1,Re&&(Ce=Ee[0]&2?Re.return:Ee[0]?Re.throw||((Ce=Re.return)&&Ce.call(Re),0):Re.next)&&!(Ce=Ce.call(Re,Ee[1])).done)return Ce;switch(Re=0,Ce&&(Ee=[Ee[0]&2,Ce.value]),Ee[0]){case 0:case 1:Ce=Ee;break;case 4:return V.label++,{value:Ee[1],done:!1};case 5:V.label++,Re=Ee[1],Ee=[0];continue;case 7:Ee=V.ops.pop(),V.trys.pop();continue;default:if(Ce=V.trys,!(Ce=Ce.length>0&&Ce[Ce.length-1])&&(Ee[0]===6||Ee[0]===2)){V=0;continue}if(Ee[0]===3&&(!Ce||Ee[1]>Ce[0]&&Ee[1]<Ce[3])){V.label=Ee[1];break}if(Ee[0]===6&&V.label<Ce[1]){V.label=Ce[1],Ce=Ee;break}if(Ce&&V.label<Ce[2]){V.label=Ce[2],V.ops.push(Ee);break}Ce[2]&&V.ops.pop(),V.trys.pop();continue}Ee=$e.call(Q,V)}catch(yt){Ee=[6,yt],Re=0}finally{Ae=Ce=0}if(Ee[0]&5)throw Ee[1];return{value:Ee[0]?Ee[1]:void 0,done:!0}}}var Y=function(){},H=Y(),oe=Object,X=function(Q){return Q===H},B=function(Q){return typeof Q=="function"},J=function(Q,$e){return oe.assign({},Q,$e)},we="undefined",be=function(){return typeof window!=we},qe=function(){return typeof document!=we},se=function(){return be()&&typeof window.requestAnimationFrame!=we},Qe=new WeakMap,He=0,At=function(Q){var $e=typeof Q,V=Q&&Q.constructor,Ae=V==Date,Re,Ce;if(oe(Q)===Q&&!Ae&&V!=RegExp){if(Re=Qe.get(Q),Re)return Re;if(Re=++He+"~",Qe.set(Q,Re),V==Array){for(Re="@",Ce=0;Ce<Q.length;Ce++)Re+=At(Q[Ce])+",";Qe.set(Q,Re)}if(V==oe){Re="#";for(var _e=oe.keys(Q).sort();!X(Ce=_e.pop());)X(Q[Ce])||(Re+=Ce+":"+At(Q[Ce])+",");Qe.set(Q,Re)}}else Re=Ae?Q.toJSON():$e=="symbol"?Q.toString():$e=="string"?JSON.stringify(Q):""+Q;return Re},ot=!0,Et=function(){return ot},p=be(),fe=qe(),u=p&&window.addEventListener?window.addEventListener.bind(window):Y,U=fe?document.addEventListener.bind(document):Y,We=p&&window.removeEventListener?window.removeEventListener.bind(window):Y,Nt=fe?document.removeEventListener.bind(document):Y,Se=function(){var Q=fe&&document.visibilityState;return X(Q)||Q!=="hidden"},Ke=function(Q){return U("visibilitychange",Q),u("focus",Q),function(){Nt("visibilitychange",Q),We("focus",Q)}},O=function(Q){var $e=function(){ot=!0,Q()},V=function(){ot=!1};return u("online",$e),u("offline",V),function(){We("online",$e),We("offline",V)}},wt={isOnline:Et,isVisible:Se},w={initFocus:Ke,initReconnect:O},gt=!be()||"Deno"in window,Mt=function(Q){return se()?window.requestAnimationFrame(Q):setTimeout(Q,1)},xr=gt?P.useEffect:P.useLayoutEffect,wr=typeof navigator!="undefined"&&navigator.connection,Ft=!gt&&wr&&(["slow-2g","2g"].includes(wr.effectiveType)||wr.saveData),ke=function(Q){if(B(Q))try{Q=Q()}catch(Ae){Q=""}var $e=[].concat(Q);Q=typeof Q=="string"?Q:(Array.isArray(Q)?Q.length:Q)?At(Q):"";var V=Q?"$swr$"+Q:"";return[Q,$e,V]},cr=new WeakMap,jr=0,Vt=1,er=2,hr=function(Q,$e,V,Ae,Re,Ce,_e){_e===void 0&&(_e=!0);var rt=cr.get(Q),Lt=rt[0],Ee=rt[1],yt=rt[3],tt=Lt[$e],Dt=Ee[$e];if(_e&&Dt)for(var Sr=0;Sr<Dt.length;++Sr)Dt[Sr](V,Ae,Re);return Ce&&(delete yt[$e],tt&&tt[0])?tt[0](er).then(function(){return Q.get($e)}):Q.get($e)},It=0,ge=function(){return++It},st=function(){for(var Q=[],$e=0;$e<arguments.length;$e++)Q[$e]=arguments[$e];return M(void 0,void 0,void 0,function(){var V,Ae,Re,Ce,_e,rt,Lt,Ee,yt,tt,Dt,Sr,ir,Lr,dr,et,Yr,_r,S,_,z;return G(this,function(Ne){switch(Ne.label){case 0:if(V=Q[0],Ae=Q[1],Re=Q[2],Ce=Q[3],_e=typeof Ce=="boolean"?{revalidate:Ce}:Ce||{},rt=X(_e.populateCache)?!0:_e.populateCache,Lt=_e.revalidate!==!1,Ee=_e.rollbackOnError!==!1,yt=_e.optimisticData,tt=ke(Ae),Dt=tt[0],Sr=tt[2],!Dt)return[2];if(ir=cr.get(V),Lr=ir[2],Q.length<3)return[2,hr(V,Dt,V.get(Dt),H,H,Lt,!0)];if(dr=Re,Yr=ge(),Lr[Dt]=[Yr,0],_r=!X(yt),S=V.get(Dt),_r&&(_=B(yt)?yt(S):yt,V.set(Dt,_),hr(V,Dt,_)),B(dr))try{dr=dr(V.get(Dt))}catch(it){et=it}return dr&&B(dr.then)?[4,dr.catch(function(it){et=it})]:[3,2];case 1:if(dr=Ne.sent(),Yr!==Lr[Dt][0]){if(et)throw et;return[2,dr]}else et&&_r&&Ee&&(rt=!0,dr=S,V.set(Dt,S));Ne.label=2;case 2:return rt&&(et||(B(rt)&&(dr=rt(dr,S)),V.set(Dt,dr)),V.set(Sr,J(V.get(Sr),{error:et}))),Lr[Dt][1]=ge(),[4,hr(V,Dt,dr,et,H,Lt,!!rt)];case 3:if(z=Ne.sent(),et)throw et;return[2,rt?z:dr]}})})},kt=function(Q,$e){for(var V in Q)Q[V][0]&&Q[V][0]($e)},Xt=function(Q,$e){if(!cr.has(Q)){var V=J(w,$e),Ae={},Re=st.bind(H,Q),Ce=Y;if(cr.set(Q,[Ae,{},{},{},Re]),!gt){var _e=V.initFocus(setTimeout.bind(H,kt.bind(H,Ae,jr))),rt=V.initReconnect(setTimeout.bind(H,kt.bind(H,Ae,Vt)));Ce=function(){_e&&_e(),rt&&rt(),cr.delete(Q)}}return[Q,Re,Ce]}return[Q,cr.get(Q)[4]]},ct=function(Q,$e,V,Ae,Re){var Ce=V.errorRetryCount,_e=Re.retryCount,rt=~~((Math.random()+.5)*(1<<(_e<8?_e:8)))*V.errorRetryInterval;!X(Ce)&&_e>Ce||setTimeout(Ae,rt,Re)},ut=Xt(new Map),mt=ut[0],Yt=ut[1],Zt=J({onLoadingSlow:Y,onSuccess:Y,onError:Y,onErrorRetry:ct,onDiscarded:Y,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:Ft?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:Ft?5e3:3e3,compare:function(Q,$e){return At(Q)==At($e)},isPaused:function(){return!1},cache:mt,mutate:Yt,fallback:{}},wt),Ht=function(Q,$e){var V=J(Q,$e);if($e){var Ae=Q.use,Re=Q.fallback,Ce=$e.use,_e=$e.fallback;Ae&&Ce&&(V.use=Ae.concat(Ce)),Re&&_e&&(V.fallback=J(Re,_e))}return V},rr=(0,P.createContext)({}),br=function(Q){var $e=Q.value,V=Ht((0,P.useContext)(rr),$e),Ae=$e&&$e.provider,Re=(0,P.useState)(function(){return Ae?Xt(Ae(V.cache||mt),$e):H})[0];return Re&&(V.cache=Re[0],V.mutate=Re[1]),xr(function(){return Re?Re[2]:H},[]),(0,P.createElement)(rr.Provider,J(Q,{value:V}))},Ar=function(Q,$e){var V=(0,P.useState)({})[1],Ae=(0,P.useRef)(Q),Re=(0,P.useRef)({data:!1,error:!1,isValidating:!1}),Ce=(0,P.useCallback)(function(_e){var rt=!1,Lt=Ae.current;for(var Ee in _e){var yt=Ee;Lt[yt]!==_e[yt]&&(Lt[yt]=_e[yt],Re.current[yt]&&(rt=!0))}rt&&!$e.current&&V({})},[]);return xr(function(){Ae.current=Q}),[Ae,Re.current,Ce]},Kr=function(Q){return B(Q[1])?[Q[0],Q[1],Q[2]||{}]:[Q[0],null,(Q[1]===null?Q[2]:Q[1])||{}]},Mr=function(){return J(Zt,(0,P.useContext)(rr))},cn=function(Q){return function(){for(var V=[],Ae=0;Ae<arguments.length;Ae++)V[Ae]=arguments[Ae];var Re=Mr(),Ce=Kr(V),_e=Ce[0],rt=Ce[1],Lt=Ce[2],Ee=Ht(Re,Lt),yt=Q,tt=Ee.use;if(tt)for(var Dt=tt.length;Dt-- >0;)yt=tt[Dt](yt);return yt(_e,rt||Ee.fetcher,Ee)}},ur=function(Q,$e,V){var Ae=$e[Q]||($e[Q]=[]);return Ae.push(V),function(){var Re=Ae.indexOf(V);Re>=0&&(Ae[Re]=Ae[Ae.length-1],Ae.pop())}},pt={dedupe:!0},ar=function(Q,$e,V){var Ae=V.cache,Re=V.compare,Ce=V.fallbackData,_e=V.suspense,rt=V.revalidateOnMount,Lt=V.refreshInterval,Ee=V.refreshWhenHidden,yt=V.refreshWhenOffline,tt=cr.get(Ae),Dt=tt[0],Sr=tt[1],ir=tt[2],Lr=tt[3],dr=ke(Q),et=dr[0],Yr=dr[1],_r=dr[2],S=(0,P.useRef)(!1),_=(0,P.useRef)(!1),z=(0,P.useRef)(et),Ne=(0,P.useRef)($e),it=(0,P.useRef)(V),Xe=function(){return it.current},dt=function(){return Xe().isVisible()&&Xe().isOnline()},xt=function(fn){return Ae.set(_r,J(Ae.get(_r),fn))},yn=Ae.get(et),tn=X(Ce)?V.fallback[et]:Ce,rn=X(yn)?tn:yn,Xa=Ae.get(_r)||{},Wn=Xa.error,Bi=!S.current,Qa=function(){return Bi&&!X(rt)?rt:Xe().isPaused()?!1:_e?X(rn)?!1:V.revalidateIfStale:X(rn)||V.revalidateIfStale},Ni=function(){return!et||!$e?!1:Xa.isValidating?!0:Bi&&Qa()},ra=Ni(),ka=Ar({data:rn,error:Wn,isValidating:ra},_),Fr=ka[0],Fn=ka[1],Vn=ka[2],dn=(0,P.useCallback)(function(fn){return M(void 0,void 0,void 0,function(){var vn,Br,$r,xn,na,Xr,Pr,Kn,bn,Ja,aa,ia,Bn;return G(this,function($n){switch($n.label){case 0:if(vn=Ne.current,!et||!vn||_.current||Xe().isPaused())return[2,!1];xn=!0,na=fn||{},Xr=!Lr[et]||!na.dedupe,Pr=function(){return!_.current&&et===z.current&&S.current},Kn=function(){var qa=Lr[et];qa&&qa[1]===$r&&delete Lr[et]},bn={isValidating:!1},Ja=function(){xt({isValidating:!1}),Pr()&&Vn(bn)},xt({isValidating:!0}),Vn({isValidating:!0}),$n.label=1;case 1:return $n.trys.push([1,3,,4]),Xr&&(hr(Ae,et,Fr.current.data,Fr.current.error,!0),V.loadingTimeout&&!Ae.get(et)&&setTimeout(function(){xn&&Pr()&&Xe().onLoadingSlow(et,V)},V.loadingTimeout),Lr[et]=[vn.apply(void 0,Yr),ge()]),Bn=Lr[et],Br=Bn[0],$r=Bn[1],[4,Br];case 2:return Br=$n.sent(),Xr&&setTimeout(Kn,V.dedupingInterval),!Lr[et]||Lr[et][1]!==$r?(Xr&&Pr()&&Xe().onDiscarded(et),[2,!1]):(xt({error:H}),bn.error=H,aa=ir[et],!X(aa)&&($r<=aa[0]||$r<=aa[1]||aa[1]===0)?(Ja(),Xr&&Pr()&&Xe().onDiscarded(et),[2,!1]):(Re(Fr.current.data,Br)?bn.data=Fr.current.data:bn.data=Br,Re(Ae.get(et),Br)||Ae.set(et,Br),Xr&&Pr()&&Xe().onSuccess(Br,et,V),[3,4]));case 3:return ia=$n.sent(),Kn(),Xe().isPaused()||(xt({error:ia}),bn.error=ia,Xr&&Pr()&&(Xe().onError(ia,et,V),(typeof V.shouldRetryOnError=="boolean"&&V.shouldRetryOnError||B(V.shouldRetryOnError)&&V.shouldRetryOnError(ia))&&dt()&&Xe().onErrorRetry(ia,et,V,dn,{retryCount:(na.retryCount||0)+1,dedupe:!0}))),[3,4];case 4:return xn=!1,Ja(),Pr()&&Xr&&hr(Ae,et,bn.data,bn.error,!1),[2,!0]}})})},[et]),lo=(0,P.useCallback)(st.bind(H,Ae,function(){return z.current}),[]);if(xr(function(){Ne.current=$e,it.current=V}),xr(function(){if(!!et){var fn=et!==z.current,vn=dn.bind(H,pt),Br=function(Pr,Kn,bn){Vn(J({error:Kn,isValidating:bn},Re(Fr.current.data,Pr)?H:{data:Pr}))},$r=0,xn=function(Pr){if(Pr==jr){var Kn=Date.now();Xe().revalidateOnFocus&&Kn>$r&&dt()&&($r=Kn+Xe().focusThrottleInterval,vn())}else if(Pr==Vt)Xe().revalidateOnReconnect&&dt()&&vn();else if(Pr==er)return dn()},na=ur(et,Sr,Br),Xr=ur(et,Dt,xn);return _.current=!1,z.current=et,S.current=!0,fn&&Vn({data:rn,error:Wn,isValidating:ra}),Qa()&&(X(rn)||gt?vn():Mt(vn)),function(){_.current=!0,na(),Xr()}}},[et,dn]),xr(function(){var fn;function vn(){var $r=B(Lt)?Lt(rn):Lt;$r&&fn!==-1&&(fn=setTimeout(Br,$r))}function Br(){!Fr.current.error&&(Ee||Xe().isVisible())&&(yt||Xe().isOnline())?dn(pt).then(vn):vn()}return vn(),function(){fn&&(clearTimeout(fn),fn=-1)}},[Lt,Ee,yt,dn]),(0,P.useDebugValue)(rn),_e&&X(rn)&&et)throw Ne.current=$e,it.current=V,_.current=!1,X(Wn)?dn(pt):Wn;return{mutate:lo,get data(){return Fn.data=!0,rn},get error(){return Fn.error=!0,Wn},get isValidating(){return Fn.isValidating=!0,ra}}},Or=oe.defineProperty(br,"default",{value:Zt}),Pn=function(Q){return ke(Q)[0]},un=cn(ar)},57186:function(Ct,ve,m){"use strict";m.d(ve,{f:function(){return M}});var P=m(67294);function M(Y){var H=P.createContext(null);function oe(B){var J=Y(B.initialState);return P.createElement(H.Provider,{value:J},B.children)}function X(){var B=P.useContext(H);if(B===null)throw new Error("Component must be wrapped with <Container.Provider>");return B}return{Provider:oe,useContainer:X}}function G(Y){return Y.useContainer()}},38069:function(Ct,ve,m){"use strict";m.d(ve,{ZP:function(){return Et}});var P=m(67294);function M(p,fe){return X(p)||oe(p,fe)||Y(p,fe)||G()}function G(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Y(p,fe){if(!!p){if(typeof p=="string")return H(p,fe);var u=Object.prototype.toString.call(p).slice(8,-1);if(u==="Object"&&p.constructor&&(u=p.constructor.name),u==="Map"||u==="Set")return Array.from(p);if(u==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(u))return H(p,fe)}}function H(p,fe){(fe==null||fe>p.length)&&(fe=p.length);for(var u=0,U=new Array(fe);u<fe;u++)U[u]=p[u];return U}function oe(p,fe){var u=p&&(typeof Symbol!="undefined"&&p[Symbol.iterator]||p["@@iterator"]);if(u!=null){var U=[],We=!0,Nt=!1,Se,Ke;try{for(u=u.call(p);!(We=(Se=u.next()).done)&&(U.push(Se.value),!(fe&&U.length===fe));We=!0);}catch(O){Nt=!0,Ke=O}finally{try{!We&&u.return!=null&&u.return()}finally{if(Nt)throw Ke}}return U}}function X(p){if(Array.isArray(p))return p}function B(p){var fe=typeof window=="undefined",u=(0,P.useState)(function(){return fe?!1:window.matchMedia(p).matches}),U=M(u,2),We=U[0],Nt=U[1];return(0,P.useLayoutEffect)(function(){if(!fe){var Se=window.matchMedia(p),Ke=function(wt){return Nt(wt.matches)};return Se.addListener(Ke),function(){return Se.removeListener(Ke)}}},[p]),We}function J(p,fe){return Qe(p)||se(p,fe)||be(p,fe)||we()}function we(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function be(p,fe){if(!!p){if(typeof p=="string")return qe(p,fe);var u=Object.prototype.toString.call(p).slice(8,-1);if(u==="Object"&&p.constructor&&(u=p.constructor.name),u==="Map"||u==="Set")return Array.from(p);if(u==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(u))return qe(p,fe)}}function qe(p,fe){(fe==null||fe>p.length)&&(fe=p.length);for(var u=0,U=new Array(fe);u<fe;u++)U[u]=p[u];return U}function se(p,fe){var u=p&&(typeof Symbol!="undefined"&&p[Symbol.iterator]||p["@@iterator"]);if(u!=null){var U=[],We=!0,Nt=!1,Se,Ke;try{for(u=u.call(p);!(We=(Se=u.next()).done)&&(U.push(Se.value),!(fe&&U.length===fe));We=!0);}catch(O){Nt=!0,Ke=O}finally{try{!We&&u.return!=null&&u.return()}finally{if(Nt)throw Ke}}return U}}function Qe(p){if(Array.isArray(p))return p}var He={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},At=function(){var fe="md";if(typeof window=="undefined")return fe;var u=Object.keys(He).find(function(U){var We=He[U].matchMedia;return!!window.matchMedia(We).matches});return fe=u,fe},ot=function(){var fe=B(He.md.matchMedia),u=B(He.lg.matchMedia),U=B(He.xxl.matchMedia),We=B(He.xl.matchMedia),Nt=B(He.sm.matchMedia),Se=B(He.xs.matchMedia),Ke=(0,P.useState)(At()),O=J(Ke,2),wt=O[0],w=O[1];return(0,P.useEffect)(function(){if(U){w("xxl");return}if(We){w("xl");return}if(u){w("lg");return}if(fe){w("md");return}if(Nt){w("sm");return}if(Se){w("xs");return}w("md")},[fe,u,U,We,Nt,Se]),wt},Et=ot}}]);
