(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[8270],{25855:function(ee,b,t){"use strict";t.r(b),t.d(b,{default:function(){return _}});var te=t(57663),D=t(71577),y=t(39428),E=t(3182),ne=t(71194),O=t(50146),re=t(71153),A=t(60331),S=t(94657),m=t(3980),$=t(87588),w=t(51042),K=t(36450),V=t(16894),G=t(93279),P=t(80582),Z=t(67294),v=t(11849),L=t(96389),N=t(50727),ue=t(34792),k=t(48086),ae=t(43185),U=t(28525),z=t(69753),M=t(84391),J=t(43347),e=t(85893),W=<PERSON><PERSON><PERSON><PERSON>Dragger;function H(o){var h=o.current,p=o.onCancel,n=o.onOk,c=(0,Z.useState)(!1),x=(0,S.Z)(c,2),s=x[0],a=x[1],d=(0,Z.useState)([]),i=(0,S.Z)(d,2),C=i[0],r=i[1],I={multiple:!1,accept:".xlsx",beforeUpload:function(u){return r([u]),!1},onRemove:function(){r([])},maxCount:1};return(0,e.jsx)(O.Z,{title:"Excel\u6A21\u677F\u5BFC\u5165\u5B57\u5178\u9879",onCancel:p,open:!0,maskClosable:!1,footer:[(0,e.jsx)(D.Z,{onClick:p,children:"\u53D6\u6D88"},"cancel"),(0,e.jsx)(D.Z,{icon:(0,e.jsx)(z.Z,{}),onClick:function(){m.hi.download("/api/file/downloadTemplate?name=\u5B57\u5178\u5BFC\u5165\u6A21\u677F")},children:"\u4E0B\u8F7D\u6A21\u677F"},"downloadTemplate"),(0,e.jsx)(D.Z,{icon:(0,e.jsx)(M.Z,{}),type:"primary",loading:s,onClick:function(){if(C.length===0){k.default.error("\u8BF7\u9009\u62E9\u6587\u4EF6");return}a(!0),m.hi.importDictItems({file:C[0],dictCode:h.code}).then(function(u){a(!1),u.success?(k.default.success("\u5BFC\u5165\u6210\u529F"),n()):k.default.error(u.message)})},children:"\u5BFC\u5165"},"importTemplate")],children:(0,e.jsxs)(W,(0,v.Z)((0,v.Z)({},I),{},{children:[(0,e.jsx)("p",{className:"ant-upload-drag-icon",children:(0,e.jsx)(J.Z,{})}),(0,e.jsx)("p",{className:"ant-upload-text",children:"\u70B9\u51FB\u6216\u8005\u62D6\u62FDExcel\u6587\u4EF6\u5230\u6B64\u5904"})]}))})}var Q=(0,P.Pi)(function(o){var h=o.dict,p=o.onClose,n=(0,Z.useRef)(),c=(0,Z.useState)([]),x=(0,S.Z)(c,2),s=x[0],a=x[1],d=(0,Z.useState)(!1),i=(0,S.Z)(d,2),C=i[0],r=i[1],I=[{title:"\u5B57\u5178\u9879\u540D\u79F0",dataIndex:"itemName",formItemProps:function(u,f){var F=f.rowIndex;return{rules:[{required:!0,message:"\u6B64\u9879\u4E3A\u5FC5\u586B\u9879"}]}}},{title:"\u5B57\u5178\u9879\u503C",dataIndex:"itemValue",formItemProps:function(u,f){var F=f.rowIndex;return{rules:[{required:!0,message:"\u6B64\u9879\u4E3A\u5FC5\u586B\u9879"}]}}},{title:"\u7236\u5B57\u5178\u9879\u503C",dataIndex:"parentItemValue"},{title:"\u5C42\u7EA7",dataIndex:"itemLevel",valueType:"digit"},{title:"\u987A\u5E8F",dataIndex:"itemOrder",valueType:"digit",tooltip:"\u6570\u5B57\u8D8A\u5C0F\uFF0C\u4F18\u5148\u7EA7\u8D8A\u9AD8"},{title:"\u64CD\u4F5C",valueType:"option",render:function(u,f,F,B){return[(0,e.jsx)("a",{onClick:function(){var R;B==null||(R=B.startEditable)===null||R===void 0||R.call(B,f.id)},children:"\u7F16\u8F91"},"editable")]}}];return(0,e.jsxs)(O.Z,{open:!0,title:!1,onCancel:p,footer:!1,width:800,maskClosable:!1,children:[(0,e.jsx)(N.Z,{columns:I,actionRef:n,bordered:!0,request:function(){var l=(0,E.Z)((0,y.Z)().mark(function u(f){var F;return(0,y.Z)().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return g.next=2,m.hi.loadDictItems((0,v.Z)((0,v.Z)({},f),{},{dictCode:h.code}));case 2:return F=g.sent,g.abrupt("return",{success:!0,data:F.list,total:F.total});case 4:case"end":return g.stop()}},u)}));return function(u){return l.apply(this,arguments)}}(),columnsState:{persistenceKey:"system-dictItem",persistenceType:"localStorage"},rowKey:"id",pagination:{pageSize:100},recordCreatorProps:{record:function(){return{id:(0,L.kb)(8)}}},editable:{type:"multiple",editableKeys:s,onSave:function(){var l=(0,E.Z)((0,y.Z)().mark(function f(F,B,g){return(0,y.Z)().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:return T.abrupt("return",m.hi.saveOrUpdateDictItem((0,v.Z)((0,v.Z)({},B),{},{dictCode:h.code})));case 1:case"end":return T.stop()}},f)}));function u(f,F,B){return l.apply(this,arguments)}return u}(),onDelete:function(u,f){return m.hi.deleteDictItem(u)},onChange:a},dateFormatter:"string",headerTitle:"\u5B57\u5178\u5217\u8868",toolBarRender:function(){return[(0,e.jsx)(D.Z,{icon:(0,e.jsx)(w.Z,{}),type:"primary",onClick:function(){return r(!0)},children:"\u5BFC\u5165"},"button")]}}),C&&(0,e.jsx)(H,{current:h,onCancel:function(){return r(!1)},onOk:function(){var u;r(!1),(u=n.current)===null||u===void 0||u.reload()}})]})}),X=Q,j=t(21307),Y=(0,P.Pi)(function(o){var h=o.onOk,p=o.onClose,n=o.dict,c=(0,Z.useRef)(),x=!!(n!=null&&n.id);return(0,e.jsxs)(j.aN,{title:x?"\u4FEE\u6539\u5B57\u5178":"\u65B0\u589E\u5B57\u5178",formRef:c,visible:!0,initialValues:n,drawerProps:{forceRender:!0,destroyOnClose:!0,onClose:function(){p()}},onFinish:function(){var s=(0,E.Z)((0,y.Z)().mark(function a(d){var i;return(0,y.Z)().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(!(n!=null&&n.id)){r.next=6;break}return r.next=3,m.hi.updateDict((0,v.Z)((0,v.Z)({},n),d));case 3:i=r.sent,r.next=9;break;case 6:return r.next=8,m.hi.saveDict((0,v.Z)((0,v.Z)({},n),d));case 8:i=r.sent;case 9:i.success&&h();case 10:case"end":return r.stop()}},a)}));return function(a){return s.apply(this,arguments)}}(),children:[(0,e.jsxs)(j.ZP.Group,{children:[(0,e.jsx)(j.V,{name:"name",width:"md",label:"\u5B57\u5178\u540D\u79F0",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u540D\u79F0",required:!0,rules:[{required:!0,message:"\u5B57\u5178\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A"}]}),(0,e.jsx)(j._I,{name:"dictType",width:"md",label:"\u5B57\u5178\u7C7B\u578B",placeholder:"\u8BF7\u9009\u62E9\u5B57\u5178\u7C7B\u578B",required:!0,tooltip:"\u7CFB\u7EDF\u5B57\u5178\u4E0D\u80FD\u5220\u9664\uFF0C\u95EE\u5377\u5B57\u5178\u7528\u4E8E\u95EE\u5377\u6A2A\u5411\u586B\u7A7A\u9898\u4E0B\u62C9",options:[{label:"\u95EE\u5377\u5B57\u5178",value:1}],rules:[{required:!0,message:"\u5B57\u5178\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A"}]}),(0,e.jsx)(j.V,{width:"md",name:"code",label:"\u5B57\u5178\u7F16\u7801",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u7F16\u7801",rules:[{required:!0,message:"\u5B57\u5178\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A"}],required:!0})]}),(0,e.jsx)(j.ZP.Group,{children:(0,e.jsx)(j.$J,{name:"remark",width:"xl",label:"\u63CF\u8FF0",placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u63CF\u8FF0\u4FE1\u606F"})})]})}),q=(0,P.Pi)(function(){var o=(0,Z.useRef)(),h=(0,Z.useState)(),p=(0,S.Z)(h,2),n=p[0],c=p[1],x=[{title:"\u5B57\u5178\u540D\u79F0",dataIndex:"name",ellipsis:!0},{title:"\u5B57\u5178\u7F16\u7801",dataIndex:"code",hideInSearch:!0},{title:"\u5B57\u5178\u7C7B\u578B",dataIndex:"dictType",hideInSearch:!0,renderText:function(a){return a===2?(0,e.jsx)(A.Z,{color:"red",children:"\u7CFB\u7EDF\u5B57\u5178"}):(0,e.jsx)(A.Z,{color:"green",children:"\u95EE\u5377\u5B57\u5178"})}},{title:"\u63CF\u8FF0",dataIndex:"remark",search:!1},{title:"\u521B\u5EFA\u65F6\u95F4",key:"showTime",dataIndex:"createAt",hideInSearch:!0,width:200},{title:"\u64CD\u4F5C",valueType:"option",width:200,render:function(a,d){return d.dictType===2?[(0,e.jsx)("a",{onClick:function(){c({visible:!0,current:d,type:"dictItem"})},children:"\u5B57\u5178\u9879"},"readonly")]:[(0,e.jsx)("a",{onClick:function(){c({visible:!0,current:d,type:"dictItem"})},children:"\u5B57\u5178\u9879"},"readonly"),(0,e.jsx)(G.Z,{onSelect:function(C){C==="edit"&&c({current:d,visible:!0,type:"dict"}),C==="delete"&&O.Z.confirm({title:"\u5220\u9664",content:"\u786E\u5B9A\u5220\u9664\u5F53\u524D\u5B57\u5178?",icon:(0,e.jsx)($.Z,{}),onOk:function(){m.hi.deleteDict(d.id).then(function(){var I;(I=o.current)===null||I===void 0||I.reload()})}})},menus:[{key:"edit",name:"\u7F16\u8F91"},{key:"delete",name:"\u5220\u9664"}]},"actionGroup")]}}];return(0,e.jsxs)(K._z,{title:!1,children:[(0,e.jsx)(V.ZP,{columns:x,bordered:!0,actionRef:o,request:function(){var s=(0,E.Z)((0,y.Z)().mark(function a(d){var i;return(0,y.Z)().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,m.hi.loadDicts(d);case 2:return i=r.sent,r.abrupt("return",{success:!0,data:i.list,total:i.total});case 4:case"end":return r.stop()}},a)}));return function(a){return s.apply(this,arguments)}}(),scroll:{x:800},columnsState:{persistenceKey:"system-dict",persistenceType:"localStorage"},rowKey:"id",search:{labelWidth:"auto"},pagination:{defaultPageSize:10},dateFormatter:"string",headerTitle:"\u5B57\u5178\u5217\u8868",toolBarRender:function(){return[(0,e.jsx)(D.Z,{icon:(0,e.jsx)(w.Z,{}),type:"primary",onClick:function(){c({visible:!0,type:"dict"})},children:"\u65B0\u5EFA"},"button")]}}),(n==null?void 0:n.visible)&&n.type==="dict"&&(0,e.jsx)(Y,{onClose:function(){c(void 0)},onOk:function(){var a;c(void 0),(a=o.current)===null||a===void 0||a.reload()},dict:n.current}),(n==null?void 0:n.current)&&n.type==="dictItem"&&(0,e.jsx)(X,{onClose:function(){c(void 0)},dict:n.current})]})}),_=q}}]);
