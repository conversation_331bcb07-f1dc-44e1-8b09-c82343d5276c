(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[2592],{65987:function(S){"use strict";var i={single_source_shortest_paths:function(u,r,o){var t={},e={};e[r]=0;var n=i.PriorityQueue.make();n.push(r,0);for(var s,c,l,f,E,b,a,m,C;!n.empty();){s=n.pop(),c=s.value,f=s.cost,E=u[c]||{};for(l in E)E.hasOwnProperty(l)&&(b=E[l],a=f+b,m=e[l],C=typeof e[l]=="undefined",(C||m>a)&&(e[l]=a,n.push(l,a),t[l]=c))}if(typeof o!="undefined"&&typeof e[o]=="undefined"){var R=["Could not find a path from ",r," to ",o,"."].join("");throw new Error(R)}return t},extract_shortest_path_from_predecessor_list:function(u,r){for(var o=[],t=r,e;t;)o.push(t),e=u[t],t=u[t];return o.reverse(),o},find_path:function(u,r,o){var t=i.single_source_shortest_paths(u,r,o);return i.extract_shortest_path_from_predecessor_list(t,o)},PriorityQueue:{make:function(u){var r=i.PriorityQueue,o={},t;u=u||{};for(t in r)r.hasOwnProperty(t)&&(o[t]=r[t]);return o.queue=[],o.sorter=u.sorter||r.default_sorter,o},default_sorter:function(u,r){return u.cost-r.cost},push:function(u,r){var o={value:u,cost:r};this.queue.push(o),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};S.exports=i},62378:function(S){"use strict";S.exports=function(u){for(var r=[],o=u.length,t=0;t<o;t++){var e=u.charCodeAt(t);if(e>=55296&&e<=56319&&o>t+1){var n=u.charCodeAt(t+1);n>=56320&&n<=57343&&(e=(e-55296)*1024+n-56320+65536,t+=1)}if(e<128){r.push(e);continue}if(e<2048){r.push(e>>6|192),r.push(e&63|128);continue}if(e<55296||e>=57344&&e<65536){r.push(e>>12|224),r.push(e>>6&63|128),r.push(e&63|128);continue}if(e>=65536&&e<=1114111){r.push(e>>18|240),r.push(e>>12&63|128),r.push(e>>6&63|128),r.push(e&63|128);continue}r.push(239,191,189)}return new Uint8Array(r).buffer}},92592:function(S,i,u){const r=u(47138),o=u(95115),t=u(6907),e=u(93776);function n(s,c,l,f,E){const b=[].slice.call(arguments,1),a=b.length,m=typeof b[a-1]=="function";if(!m&&!r())throw new Error("Callback required as last argument");if(m){if(a<2)throw new Error("Too few arguments provided");a===2?(E=l,l=c,c=f=void 0):a===3&&(c.getContext&&typeof E=="undefined"?(E=f,f=void 0):(E=f,f=l,l=c,c=void 0))}else{if(a<1)throw new Error("Too few arguments provided");return a===1?(l=c,c=f=void 0):a===2&&!c.getContext&&(f=l,l=c,c=void 0),new Promise(function(C,R){try{const L=o.create(l,f);C(s(L,c,f))}catch(L){R(L)}})}try{const C=o.create(l,f);E(null,s(C,c,f))}catch(C){E(C)}}i.create=o.create,i.toCanvas=n.bind(null,t.render),i.toDataURL=n.bind(null,t.renderToDataURL),i.toString=n.bind(null,function(s,c,l){return e.render(s,l)})},47138:function(S){S.exports=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}},21845:function(S,i,u){const r=u(10242).getSymbolSize;i.getRowColCoords=function(t){if(t===1)return[];const e=Math.floor(t/7)+2,n=r(t),s=n===145?26:Math.ceil((n-13)/(2*e-2))*2,c=[n-7];for(let l=1;l<e-1;l++)c[l]=c[l-1]-s;return c.push(6),c.reverse()},i.getPositions=function(t){const e=[],n=i.getRowColCoords(t),s=n.length;for(let c=0;c<s;c++)for(let l=0;l<s;l++)c===0&&l===0||c===0&&l===s-1||c===s-1&&l===0||e.push([n[c],n[l]]);return e}},8260:function(S,i,u){const r=u(76910),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function t(e){this.mode=r.ALPHANUMERIC,this.data=e}t.getBitsLength=function(n){return 11*Math.floor(n/2)+6*(n%2)},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(n){let s;for(s=0;s+2<=this.data.length;s+=2){let c=o.indexOf(this.data[s])*45;c+=o.indexOf(this.data[s+1]),n.put(c,11)}this.data.length%2&&n.put(o.indexOf(this.data[s]),6)},S.exports=t},97245:function(S){function i(){this.buffer=[],this.length=0}i.prototype={get:function(u){const r=Math.floor(u/8);return(this.buffer[r]>>>7-u%8&1)==1},put:function(u,r){for(let o=0;o<r;o++)this.putBit((u>>>r-o-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(u){const r=Math.floor(this.length/8);this.buffer.length<=r&&this.buffer.push(0),u&&(this.buffer[r]|=128>>>this.length%8),this.length++}},S.exports=i},73280:function(S){function i(u){if(!u||u<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=u,this.data=new Uint8Array(u*u),this.reservedBit=new Uint8Array(u*u)}i.prototype.set=function(u,r,o,t){const e=u*this.size+r;this.data[e]=o,t&&(this.reservedBit[e]=!0)},i.prototype.get=function(u,r){return this.data[u*this.size+r]},i.prototype.xor=function(u,r,o){this.data[u*this.size+r]^=o},i.prototype.isReserved=function(u,r){return this.reservedBit[u*this.size+r]},S.exports=i},43424:function(S,i,u){const r=u(62378),o=u(76910);function t(e){this.mode=o.BYTE,typeof e=="string"&&(e=r(e)),this.data=new Uint8Array(e)}t.getBitsLength=function(n){return n*8},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(e){for(let n=0,s=this.data.length;n<s;n++)e.put(this.data[n],8)},S.exports=t},26245:function(S,i,u){const r=u(64908),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],t=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];i.getBlocksCount=function(n,s){switch(s){case r.L:return o[(n-1)*4+0];case r.M:return o[(n-1)*4+1];case r.Q:return o[(n-1)*4+2];case r.H:return o[(n-1)*4+3];default:return}},i.getTotalCodewordsCount=function(n,s){switch(s){case r.L:return t[(n-1)*4+0];case r.M:return t[(n-1)*4+1];case r.Q:return t[(n-1)*4+2];case r.H:return t[(n-1)*4+3];default:return}}},64908:function(S,i){i.L={bit:1},i.M={bit:0},i.Q={bit:3},i.H={bit:2};function u(r){if(typeof r!="string")throw new Error("Param is not a string");switch(r.toLowerCase()){case"l":case"low":return i.L;case"m":case"medium":return i.M;case"q":case"quartile":return i.Q;case"h":case"high":return i.H;default:throw new Error("Unknown EC Level: "+r)}}i.isValid=function(o){return o&&typeof o.bit!="undefined"&&o.bit>=0&&o.bit<4},i.from=function(o,t){if(i.isValid(o))return o;try{return u(o)}catch(e){return t}}},76526:function(S,i,u){const r=u(10242).getSymbolSize,o=7;i.getPositions=function(e){const n=r(e);return[[0,0],[n-o,0],[0,n-o]]}},61642:function(S,i,u){const r=u(10242),o=1<<10|1<<8|1<<5|1<<4|1<<2|1<<1|1<<0,t=1<<14|1<<12|1<<10|1<<4|1<<1,e=r.getBCHDigit(o);i.getEncodedBits=function(s,c){const l=s.bit<<3|c;let f=l<<10;for(;r.getBCHDigit(f)-e>=0;)f^=o<<r.getBCHDigit(f)-e;return(l<<10|f)^t}},69729:function(S,i){const u=new Uint8Array(512),r=new Uint8Array(256);(function(){let t=1;for(let e=0;e<255;e++)u[e]=t,r[t]=e,t<<=1,t&256&&(t^=285);for(let e=255;e<512;e++)u[e]=u[e-255]})(),i.log=function(t){if(t<1)throw new Error("log("+t+")");return r[t]},i.exp=function(t){return u[t]},i.mul=function(t,e){return t===0||e===0?0:u[r[t]+r[e]]}},35442:function(S,i,u){const r=u(76910),o=u(10242);function t(e){this.mode=r.KANJI,this.data=e}t.getBitsLength=function(n){return n*13},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(e){let n;for(n=0;n<this.data.length;n++){let s=o.toSJIS(this.data[n]);if(s>=33088&&s<=40956)s-=33088;else if(s>=57408&&s<=60351)s-=49472;else throw new Error("Invalid SJIS character: "+this.data[n]+`
Make sure your charset is UTF-8`);s=(s>>>8&255)*192+(s&255),e.put(s,13)}},S.exports=t},27126:function(S,i){i.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const u={N1:3,N2:3,N3:40,N4:10};i.isValid=function(t){return t!=null&&t!==""&&!isNaN(t)&&t>=0&&t<=7},i.from=function(t){return i.isValid(t)?parseInt(t,10):void 0},i.getPenaltyN1=function(t){const e=t.size;let n=0,s=0,c=0,l=null,f=null;for(let E=0;E<e;E++){s=c=0,l=f=null;for(let b=0;b<e;b++){let a=t.get(E,b);a===l?s++:(s>=5&&(n+=u.N1+(s-5)),l=a,s=1),a=t.get(b,E),a===f?c++:(c>=5&&(n+=u.N1+(c-5)),f=a,c=1)}s>=5&&(n+=u.N1+(s-5)),c>=5&&(n+=u.N1+(c-5))}return n},i.getPenaltyN2=function(t){const e=t.size;let n=0;for(let s=0;s<e-1;s++)for(let c=0;c<e-1;c++){const l=t.get(s,c)+t.get(s,c+1)+t.get(s+1,c)+t.get(s+1,c+1);(l===4||l===0)&&n++}return n*u.N2},i.getPenaltyN3=function(t){const e=t.size;let n=0,s=0,c=0;for(let l=0;l<e;l++){s=c=0;for(let f=0;f<e;f++)s=s<<1&2047|t.get(l,f),f>=10&&(s===1488||s===93)&&n++,c=c<<1&2047|t.get(f,l),f>=10&&(c===1488||c===93)&&n++}return n*u.N3},i.getPenaltyN4=function(t){let e=0;const n=t.data.length;for(let c=0;c<n;c++)e+=t.data[c];return Math.abs(Math.ceil(e*100/n/5)-10)*u.N4};function r(o,t,e){switch(o){case i.Patterns.PATTERN000:return(t+e)%2==0;case i.Patterns.PATTERN001:return t%2==0;case i.Patterns.PATTERN010:return e%3==0;case i.Patterns.PATTERN011:return(t+e)%3==0;case i.Patterns.PATTERN100:return(Math.floor(t/2)+Math.floor(e/3))%2==0;case i.Patterns.PATTERN101:return t*e%2+t*e%3==0;case i.Patterns.PATTERN110:return(t*e%2+t*e%3)%2==0;case i.Patterns.PATTERN111:return(t*e%3+(t+e)%2)%2==0;default:throw new Error("bad maskPattern:"+o)}}i.applyMask=function(t,e){const n=e.size;for(let s=0;s<n;s++)for(let c=0;c<n;c++)e.isReserved(c,s)||e.xor(c,s,r(t,c,s))},i.getBestMask=function(t,e){const n=Object.keys(i.Patterns).length;let s=0,c=Infinity;for(let l=0;l<n;l++){e(l),i.applyMask(l,t);const f=i.getPenaltyN1(t)+i.getPenaltyN2(t)+i.getPenaltyN3(t)+i.getPenaltyN4(t);i.applyMask(l,t),f<c&&(c=f,s=l)}return s}},76910:function(S,i,u){const r=u(43114),o=u(7007);i.NUMERIC={id:"Numeric",bit:1<<0,ccBits:[10,12,14]},i.ALPHANUMERIC={id:"Alphanumeric",bit:1<<1,ccBits:[9,11,13]},i.BYTE={id:"Byte",bit:1<<2,ccBits:[8,16,16]},i.KANJI={id:"Kanji",bit:1<<3,ccBits:[8,10,12]},i.MIXED={bit:-1},i.getCharCountIndicator=function(n,s){if(!n.ccBits)throw new Error("Invalid mode: "+n);if(!r.isValid(s))throw new Error("Invalid version: "+s);return s>=1&&s<10?n.ccBits[0]:s<27?n.ccBits[1]:n.ccBits[2]},i.getBestModeForData=function(n){return o.testNumeric(n)?i.NUMERIC:o.testAlphanumeric(n)?i.ALPHANUMERIC:o.testKanji(n)?i.KANJI:i.BYTE},i.toString=function(n){if(n&&n.id)return n.id;throw new Error("Invalid mode")},i.isValid=function(n){return n&&n.bit&&n.ccBits};function t(e){if(typeof e!="string")throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return i.NUMERIC;case"alphanumeric":return i.ALPHANUMERIC;case"kanji":return i.KANJI;case"byte":return i.BYTE;default:throw new Error("Unknown mode: "+e)}}i.from=function(n,s){if(i.isValid(n))return n;try{return t(n)}catch(c){return s}}},41085:function(S,i,u){const r=u(76910);function o(t){this.mode=r.NUMERIC,this.data=t.toString()}o.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(e){let n,s,c;for(n=0;n+3<=this.data.length;n+=3)s=this.data.substr(n,3),c=parseInt(s,10),e.put(c,10);const l=this.data.length-n;l>0&&(s=this.data.substr(n),c=parseInt(s,10),e.put(c,l*3+1))},S.exports=o},26143:function(S,i,u){const r=u(69729);i.mul=function(t,e){const n=new Uint8Array(t.length+e.length-1);for(let s=0;s<t.length;s++)for(let c=0;c<e.length;c++)n[s+c]^=r.mul(t[s],e[c]);return n},i.mod=function(t,e){let n=new Uint8Array(t);for(;n.length-e.length>=0;){const s=n[0];for(let l=0;l<e.length;l++)n[l]^=r.mul(e[l],s);let c=0;for(;c<n.length&&n[c]===0;)c++;n=n.slice(c)}return n},i.generateECPolynomial=function(t){let e=new Uint8Array([1]);for(let n=0;n<t;n++)e=i.mul(e,new Uint8Array([1,r.exp(n)]));return e}},95115:function(S,i,u){const r=u(10242),o=u(64908),t=u(97245),e=u(73280),n=u(21845),s=u(76526),c=u(27126),l=u(26245),f=u(52882),E=u(23103),b=u(61642),a=u(76910),m=u(16130);function C(g,A){const w=g.size,d=s.getPositions(A);for(let T=0;T<d.length;T++){const y=d[T][0],h=d[T][1];for(let M=-1;M<=7;M++)if(!(y+M<=-1||w<=y+M))for(let I=-1;I<=7;I++)h+I<=-1||w<=h+I||(M>=0&&M<=6&&(I===0||I===6)||I>=0&&I<=6&&(M===0||M===6)||M>=2&&M<=4&&I>=2&&I<=4?g.set(y+M,h+I,!0,!0):g.set(y+M,h+I,!1,!0))}}function R(g){const A=g.size;for(let w=8;w<A-8;w++){const d=w%2==0;g.set(w,6,d,!0),g.set(6,w,d,!0)}}function L(g,A){const w=n.getPositions(A);for(let d=0;d<w.length;d++){const T=w[d][0],y=w[d][1];for(let h=-2;h<=2;h++)for(let M=-2;M<=2;M++)h===-2||h===2||M===-2||M===2||h===0&&M===0?g.set(T+h,y+M,!0,!0):g.set(T+h,y+M,!1,!0)}}function N(g,A){const w=g.size,d=E.getEncodedBits(A);let T,y,h;for(let M=0;M<18;M++)T=Math.floor(M/3),y=M%3+w-8-3,h=(d>>M&1)==1,g.set(T,y,h,!0),g.set(y,T,h,!0)}function P(g,A,w){const d=g.size,T=b.getEncodedBits(A,w);let y,h;for(y=0;y<15;y++)h=(T>>y&1)==1,y<6?g.set(y,8,h,!0):y<8?g.set(y+1,8,h,!0):g.set(d-15+y,8,h,!0),y<8?g.set(8,d-y-1,h,!0):y<9?g.set(8,15-y-1+1,h,!0):g.set(8,15-y-1,h,!0);g.set(d-8,8,1,!0)}function p(g,A){const w=g.size;let d=-1,T=w-1,y=7,h=0;for(let M=w-1;M>0;M-=2)for(M===6&&M--;;){for(let I=0;I<2;I++)if(!g.isReserved(T,M-I)){let z=!1;h<A.length&&(z=(A[h]>>>y&1)==1),g.set(T,M-I,z),y--,y===-1&&(h++,y=7)}if(T+=d,T<0||w<=T){T-=d,d=-d;break}}}function B(g,A,w){const d=new t;w.forEach(function(I){d.put(I.mode.bit,4),d.put(I.getLength(),a.getCharCountIndicator(I.mode,g)),I.write(d)});const T=r.getSymbolTotalCodewords(g),y=l.getTotalCodewordsCount(g,A),h=(T-y)*8;for(d.getLengthInBits()+4<=h&&d.put(0,4);d.getLengthInBits()%8!=0;)d.putBit(0);const M=(h-d.getLengthInBits())/8;for(let I=0;I<M;I++)d.put(I%2?17:236,8);return D(d,g,A)}function D(g,A,w){const d=r.getSymbolTotalCodewords(A),T=l.getTotalCodewordsCount(A,w),y=d-T,h=l.getBlocksCount(A,w),M=d%h,I=h-M,z=Math.floor(d/h),H=Math.floor(y/h),$=H+1,G=z-H,v=new f(G);let J=0;const K=new Array(h),Q=new Array(h);let Y=0;const W=new Uint8Array(g.buffer);for(let V=0;V<h;V++){const j=V<I?H:$;K[V]=W.slice(J,J+j),Q[V]=v.encode(K[V]),J+=j,Y=Math.max(Y,j)}const O=new Uint8Array(d);let _=0,F,k;for(F=0;F<Y;F++)for(k=0;k<h;k++)F<K[k].length&&(O[_++]=K[k][F]);for(F=0;F<G;F++)for(k=0;k<h;k++)O[_++]=Q[k][F];return O}function U(g,A,w,d){let T;if(Array.isArray(g))T=m.fromArray(g);else if(typeof g=="string"){let z=A;if(!z){const H=m.rawSplit(g);z=E.getBestVersionForData(H,w)}T=m.fromString(g,z||40)}else throw new Error("Invalid data");const y=E.getBestVersionForData(T,w);if(!y)throw new Error("The amount of data is too big to be stored in a QR Code");if(!A)A=y;else if(A<y)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+y+`.
`);const h=B(A,w,T),M=r.getSymbolSize(A),I=new e(M);return C(I,A),R(I),L(I,A),P(I,w,0),A>=7&&N(I,A),p(I,h),isNaN(d)&&(d=c.getBestMask(I,P.bind(null,I,w))),c.applyMask(d,I),P(I,w,d),{modules:I,version:A,errorCorrectionLevel:w,maskPattern:d,segments:T}}i.create=function(A,w){if(typeof A=="undefined"||A==="")throw new Error("No input text");let d=o.M,T,y;return typeof w!="undefined"&&(d=o.from(w.errorCorrectionLevel,o.M),T=E.from(w.version),y=c.from(w.maskPattern),w.toSJISFunc&&r.setToSJISFunction(w.toSJISFunc)),U(A,T,d,y)}},52882:function(S,i,u){const r=u(26143);function o(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}o.prototype.initialize=function(e){this.degree=e,this.genPoly=r.generateECPolynomial(this.degree)},o.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");const n=new Uint8Array(e.length+this.degree);n.set(e);const s=r.mod(n,this.genPoly),c=this.degree-s.length;if(c>0){const l=new Uint8Array(this.degree);return l.set(s,c),l}return s},S.exports=o},7007:function(S,i){const u="[0-9]+",r="[A-Z $%*+\\-./:]+";let o="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";o=o.replace(/u/g,"\\u");const t="(?:(?![A-Z0-9 $%*+\\-./:]|"+o+`)(?:.|[\r
]))+`;i.KANJI=new RegExp(o,"g"),i.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),i.BYTE=new RegExp(t,"g"),i.NUMERIC=new RegExp(u,"g"),i.ALPHANUMERIC=new RegExp(r,"g");const e=new RegExp("^"+o+"$"),n=new RegExp("^"+u+"$"),s=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");i.testKanji=function(l){return e.test(l)},i.testNumeric=function(l){return n.test(l)},i.testAlphanumeric=function(l){return s.test(l)}},16130:function(S,i,u){const r=u(76910),o=u(41085),t=u(8260),e=u(43424),n=u(35442),s=u(7007),c=u(10242),l=u(65987);function f(N){return unescape(encodeURIComponent(N)).length}function E(N,P,p){const B=[];let D;for(;(D=N.exec(p))!==null;)B.push({data:D[0],index:D.index,mode:P,length:D[0].length});return B}function b(N){const P=E(s.NUMERIC,r.NUMERIC,N),p=E(s.ALPHANUMERIC,r.ALPHANUMERIC,N);let B,D;return c.isKanjiModeEnabled()?(B=E(s.BYTE,r.BYTE,N),D=E(s.KANJI,r.KANJI,N)):(B=E(s.BYTE_KANJI,r.BYTE,N),D=[]),P.concat(p,B,D).sort(function(g,A){return g.index-A.index}).map(function(g){return{data:g.data,mode:g.mode,length:g.length}})}function a(N,P){switch(P){case r.NUMERIC:return o.getBitsLength(N);case r.ALPHANUMERIC:return t.getBitsLength(N);case r.KANJI:return n.getBitsLength(N);case r.BYTE:return e.getBitsLength(N)}}function m(N){return N.reduce(function(P,p){const B=P.length-1>=0?P[P.length-1]:null;return B&&B.mode===p.mode?(P[P.length-1].data+=p.data,P):(P.push(p),P)},[])}function C(N){const P=[];for(let p=0;p<N.length;p++){const B=N[p];switch(B.mode){case r.NUMERIC:P.push([B,{data:B.data,mode:r.ALPHANUMERIC,length:B.length},{data:B.data,mode:r.BYTE,length:B.length}]);break;case r.ALPHANUMERIC:P.push([B,{data:B.data,mode:r.BYTE,length:B.length}]);break;case r.KANJI:P.push([B,{data:B.data,mode:r.BYTE,length:f(B.data)}]);break;case r.BYTE:P.push([{data:B.data,mode:r.BYTE,length:f(B.data)}])}}return P}function R(N,P){const p={},B={start:{}};let D=["start"];for(let U=0;U<N.length;U++){const g=N[U],A=[];for(let w=0;w<g.length;w++){const d=g[w],T=""+U+w;A.push(T),p[T]={node:d,lastCount:0},B[T]={};for(let y=0;y<D.length;y++){const h=D[y];p[h]&&p[h].node.mode===d.mode?(B[h][T]=a(p[h].lastCount+d.length,d.mode)-a(p[h].lastCount,d.mode),p[h].lastCount+=d.length):(p[h]&&(p[h].lastCount=d.length),B[h][T]=a(d.length,d.mode)+4+r.getCharCountIndicator(d.mode,P))}}D=A}for(let U=0;U<D.length;U++)B[D[U]].end=0;return{map:B,table:p}}function L(N,P){let p;const B=r.getBestModeForData(N);if(p=r.from(P,B),p!==r.BYTE&&p.bit<B.bit)throw new Error('"'+N+'" cannot be encoded with mode '+r.toString(p)+`.
 Suggested mode is: `+r.toString(B));switch(p===r.KANJI&&!c.isKanjiModeEnabled()&&(p=r.BYTE),p){case r.NUMERIC:return new o(N);case r.ALPHANUMERIC:return new t(N);case r.KANJI:return new n(N);case r.BYTE:return new e(N)}}i.fromArray=function(P){return P.reduce(function(p,B){return typeof B=="string"?p.push(L(B,null)):B.data&&p.push(L(B.data,B.mode)),p},[])},i.fromString=function(P,p){const B=b(P,c.isKanjiModeEnabled()),D=C(B),U=R(D,p),g=l.find_path(U.map,"start","end"),A=[];for(let w=1;w<g.length-1;w++)A.push(U.table[g[w]].node);return i.fromArray(m(A))},i.rawSplit=function(P){return i.fromArray(b(P,c.isKanjiModeEnabled()))}},10242:function(S,i){let u;const r=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];i.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17},i.getSymbolTotalCodewords=function(t){return r[t]},i.getBCHDigit=function(o){let t=0;for(;o!==0;)t++,o>>>=1;return t},i.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');u=t},i.isKanjiModeEnabled=function(){return typeof u!="undefined"},i.toSJIS=function(t){return u(t)}},43114:function(S,i){i.isValid=function(r){return!isNaN(r)&&r>=1&&r<=40}},23103:function(S,i,u){const r=u(10242),o=u(26245),t=u(64908),e=u(76910),n=u(43114),s=1<<12|1<<11|1<<10|1<<9|1<<8|1<<5|1<<2|1<<0,c=r.getBCHDigit(s);function l(a,m,C){for(let R=1;R<=40;R++)if(m<=i.getCapacity(R,C,a))return R}function f(a,m){return e.getCharCountIndicator(a,m)+4}function E(a,m){let C=0;return a.forEach(function(R){C+=f(R.mode,m)+R.getBitsLength()}),C}function b(a,m){for(let C=1;C<=40;C++)if(E(a,C)<=i.getCapacity(C,m,e.MIXED))return C}i.from=function(m,C){return n.isValid(m)?parseInt(m,10):C},i.getCapacity=function(m,C,R){if(!n.isValid(m))throw new Error("Invalid QR Code version");typeof R=="undefined"&&(R=e.BYTE);const L=r.getSymbolTotalCodewords(m),N=o.getTotalCodewordsCount(m,C),P=(L-N)*8;if(R===e.MIXED)return P;const p=P-f(R,m);switch(R){case e.NUMERIC:return Math.floor(p/10*3);case e.ALPHANUMERIC:return Math.floor(p/11*2);case e.KANJI:return Math.floor(p/13);case e.BYTE:default:return Math.floor(p/8)}},i.getBestVersionForData=function(m,C){let R;const L=t.from(C,t.M);if(Array.isArray(m)){if(m.length>1)return b(m,L);if(m.length===0)return 1;R=m[0]}else R=m;return l(R.mode,R.getLength(),L)},i.getEncodedBits=function(m){if(!n.isValid(m)||m<7)throw new Error("Invalid QR Code version");let C=m<<12;for(;r.getBCHDigit(C)-c>=0;)C^=s<<r.getBCHDigit(C)-c;return m<<12|C}},6907:function(S,i,u){const r=u(89653);function o(e,n,s){e.clearRect(0,0,n.width,n.height),n.style||(n.style={}),n.height=s,n.width=s,n.style.height=s+"px",n.style.width=s+"px"}function t(){try{return document.createElement("canvas")}catch(e){throw new Error("You need to specify a canvas element")}}i.render=function(n,s,c){let l=c,f=s;typeof l=="undefined"&&(!s||!s.getContext)&&(l=s,s=void 0),s||(f=t()),l=r.getOptions(l);const E=r.getImageWidth(n.modules.size,l),b=f.getContext("2d"),a=b.createImageData(E,E);return r.qrToImageData(a.data,n,l),o(b,f,E),b.putImageData(a,0,0),f},i.renderToDataURL=function(n,s,c){let l=c;typeof l=="undefined"&&(!s||!s.getContext)&&(l=s,s=void 0),l||(l={});const f=i.render(n,s,l),E=l.type||"image/png",b=l.rendererOpts||{};return f.toDataURL(E,b.quality)}},93776:function(S,i,u){const r=u(89653);function o(n,s){const c=n.a/255,l=s+'="'+n.hex+'"';return c<1?l+" "+s+'-opacity="'+c.toFixed(2).slice(1)+'"':l}function t(n,s,c){let l=n+s;return typeof c!="undefined"&&(l+=" "+c),l}function e(n,s,c){let l="",f=0,E=!1,b=0;for(let a=0;a<n.length;a++){const m=Math.floor(a%s),C=Math.floor(a/s);!m&&!E&&(E=!0),n[a]?(b++,a>0&&m>0&&n[a-1]||(l+=E?t("M",m+c,.5+C+c):t("m",f,0),f=0,E=!1),m+1<s&&n[a+1]||(l+=t("h",b),b=0)):f++}return l}i.render=function(s,c,l){const f=r.getOptions(c),E=s.modules.size,b=s.modules.data,a=E+f.margin*2,m=f.color.light.a?"<path "+o(f.color.light,"fill")+' d="M0 0h'+a+"v"+a+'H0z"/>':"",C="<path "+o(f.color.dark,"stroke")+' d="'+e(b,E,f.margin)+'"/>',R='viewBox="0 0 '+a+" "+a+'"',L=f.width?'width="'+f.width+'" height="'+f.width+'" ':"",N='<svg xmlns="http://www.w3.org/2000/svg" '+L+R+' shape-rendering="crispEdges">'+m+C+`</svg>
`;return typeof l=="function"&&l(null,N),N}},89653:function(S,i){function u(r){if(typeof r=="number"&&(r=r.toString()),typeof r!="string")throw new Error("Color should be defined as hex string");let o=r.slice().replace("#","").split("");if(o.length<3||o.length===5||o.length>8)throw new Error("Invalid hex color: "+r);(o.length===3||o.length===4)&&(o=Array.prototype.concat.apply([],o.map(function(e){return[e,e]}))),o.length===6&&o.push("F","F");const t=parseInt(o.join(""),16);return{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:t&255,hex:"#"+o.slice(0,6).join("")}}i.getOptions=function(o){o||(o={}),o.color||(o.color={});const t=typeof o.margin=="undefined"||o.margin===null||o.margin<0?4:o.margin,e=o.width&&o.width>=21?o.width:void 0,n=o.scale||4;return{width:e,scale:e?4:n,margin:t,color:{dark:u(o.color.dark||"#000000ff"),light:u(o.color.light||"#ffffffff")},type:o.type,rendererOpts:o.rendererOpts||{}}},i.getScale=function(o,t){return t.width&&t.width>=o+t.margin*2?t.width/(o+t.margin*2):t.scale},i.getImageWidth=function(o,t){const e=i.getScale(o,t);return Math.floor((o+t.margin*2)*e)},i.qrToImageData=function(o,t,e){const n=t.modules.size,s=t.modules.data,c=i.getScale(n,e),l=Math.floor((n+e.margin*2)*c),f=e.margin*c,E=[e.color.light,e.color.dark];for(let b=0;b<l;b++)for(let a=0;a<l;a++){let m=(b*l+a)*4,C=e.color.light;if(b>=f&&a>=f&&b<l-f&&a<l-f){const R=Math.floor((b-f)/c),L=Math.floor((a-f)/c);C=E[s[R*n+L]?1:0]}o[m++]=C.r,o[m++]=C.g,o[m++]=C.b,o[m]=C.a}}}}]);
