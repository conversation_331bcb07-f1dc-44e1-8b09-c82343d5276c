plugins {
    id 'java'
}

version 'unspecified'

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    annotationProcessor "org.springframework.boot:spring-boot-autoconfigure-processor"
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
    implementation 'com.anji-plus:spring-boot-starter-captcha'

    implementation 'org.dhatim:fastexcel:0.12.13'
    implementation 'org.dhatim:fastexcel-reader:0.12.3'
    implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.2'
    implementation 'io.jsonwebtoken:jjwt-jackson:0.11.2'

    implementation 'com.google.zxing:javase:3.5.0'
    implementation 'net.coobird:thumbnailator:0.4.14'
    implementation 'com.github.ben-manes.caffeine:caffeine:2.9.2'
    implementation 'com.belerweb:pinyin4j:2.5.0'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
}

test {
    useJUnitPlatform()
}