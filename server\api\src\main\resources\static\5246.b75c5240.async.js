(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[5246],{36688:function(Ye,be){"use strict";var c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"};be.Z=c},55246:function(Ye,be,c){"use strict";c.d(be,{Z:function(){return Kt}});var T=c(65223),P=c(96156),x=c(22122),Y=c(85061),Xe=c(94184),le=c.n(Xe),Se=c(5461),n=c(67294),me=c(53124),Re=c(33603),G=c(28481);function ve(e){var t=n.useState(e),a=(0,G.Z)(t,2),r=a[0],l=a[1];return n.useEffect(function(){var o=setTimeout(function(){l(e)},e.length?0:10);return function(){clearTimeout(o)}},[e]),r}var pe=[];function ye(e,t,a){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;return{key:typeof e=="string"?e:"".concat(a,"-").concat(r),error:e,errorStatus:t}}function Ne(e){var t=e.help,a=e.helpStatus,r=e.errors,l=r===void 0?pe:r,o=e.warnings,u=o===void 0?pe:o,i=e.className,d=e.fieldId,m=e.onVisibleChanged,S=n.useContext(T.Rk),b=S.prefixCls,Z=n.useContext(me.E_),h=Z.getPrefixCls,y="".concat(b,"-item-explain"),w=h(),f=ve(l),O=ve(u),R=n.useMemo(function(){return t!=null?[ye(t,a,"help")]:[].concat((0,Y.Z)(f.map(function(C,E){return ye(C,"error","error",E)})),(0,Y.Z)(O.map(function(C,E){return ye(C,"warning","warning",E)})))},[t,a,f,O]),I={};return d&&(I.id="".concat(d,"_help")),n.createElement(Se.default,{motionDeadline:Re.ZP.motionDeadline,motionName:"".concat(w,"-show-help"),visible:!!R.length,onVisibleChanged:m},function(C){var E=C.className,V=C.style;return n.createElement("div",(0,x.Z)({},I,{className:le()(y,E,i),style:V,role:"alert"}),n.createElement(Se.CSSMotionList,(0,x.Z)({keys:R},Re.ZP,{motionName:"".concat(w,"-show-help-item"),component:!1}),function(F){var p=F.key,v=F.error,g=F.errorStatus,j=F.className,L=F.style;return n.createElement("div",{key:p,className:le()(j,(0,P.Z)({},"".concat(y,"-").concat(g),g)),style:L},v)}))})}var ge=c(90484),ue=c(46016),we=c(98866),Oe=c(97647),Be=c(37920);function Me(e){return typeof e=="object"&&e!=null&&e.nodeType===1}function Le(e,t){return(!t||e!=="hidden")&&e!=="visible"&&e!=="clip"}function xe(e,t){if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){var a=getComputedStyle(e,null);return Le(a.overflowY,t)||Le(a.overflowX,t)||function(r){var l=function(o){if(!o.ownerDocument||!o.ownerDocument.defaultView)return null;try{return o.ownerDocument.defaultView.frameElement}catch(u){return null}}(r);return!!l&&(l.clientHeight<r.scrollHeight||l.clientWidth<r.scrollWidth)}(e)}return!1}function he(e,t,a,r,l,o,u,i){return o<e&&u>t||o>e&&u<t?0:o<=e&&i<=a||u>=t&&i>=a?o-e-r:u>t&&i<a||o<e&&i>a?u-t+l:0}var Pe=function(e,t){var a=window,r=t.scrollMode,l=t.block,o=t.inline,u=t.boundary,i=t.skipOverflowHiddenElements,d=typeof u=="function"?u:function(se){return se!==u};if(!Me(e))throw new TypeError("Invalid target");for(var m,S,b=document.scrollingElement||document.documentElement,Z=[],h=e;Me(h)&&d(h);){if((h=(S=(m=h).parentElement)==null?m.getRootNode().host||null:S)===b){Z.push(h);break}h!=null&&h===document.body&&xe(h)&&!xe(document.documentElement)||h!=null&&xe(h,i)&&Z.push(h)}for(var y=a.visualViewport?a.visualViewport.width:innerWidth,w=a.visualViewport?a.visualViewport.height:innerHeight,f=window.scrollX||pageXOffset,O=window.scrollY||pageYOffset,R=e.getBoundingClientRect(),I=R.height,C=R.width,E=R.top,V=R.right,F=R.bottom,p=R.left,v=l==="start"||l==="nearest"?E:l==="end"?F:E+I/2,g=o==="center"?p+C/2:o==="end"?V:p,j=[],L=0;L<Z.length;L++){var s=Z[L],$=s.getBoundingClientRect(),z=$.height,W=$.width,M=$.top,U=$.right,te=$.bottom,oe=$.left;if(r==="if-needed"&&E>=0&&p>=0&&F<=w&&V<=y&&E>=M&&F<=te&&p>=oe&&V<=U)return j;var q=getComputedStyle(s),re=parseInt(q.borderLeftWidth,10),ne=parseInt(q.borderTopWidth,10),X=parseInt(q.borderRightWidth,10),ie=parseInt(q.borderBottomWidth,10),D=0,Q=0,J="offsetWidth"in s?s.offsetWidth-s.clientWidth-re-X:0,k="offsetHeight"in s?s.offsetHeight-s.clientHeight-ne-ie:0,N="offsetWidth"in s?s.offsetWidth===0?0:W/s.offsetWidth:0,A="offsetHeight"in s?s.offsetHeight===0?0:z/s.offsetHeight:0;if(b===s)D=l==="start"?v:l==="end"?v-w:l==="nearest"?he(O,O+w,w,ne,ie,O+v,O+v+I,I):v-w/2,Q=o==="start"?g:o==="center"?g-y/2:o==="end"?g-y:he(f,f+y,y,re,X,f+g,f+g+C,C),D=Math.max(0,D+O),Q=Math.max(0,Q+f);else{D=l==="start"?v-M-ne:l==="end"?v-te+ie+k:l==="nearest"?he(M,te,z,ne,ie+k,v,v+I,I):v-(M+z/2)+k/2,Q=o==="start"?g-oe-re:o==="center"?g-(oe+W/2)+J/2:o==="end"?g-U+X+J:he(oe,U,W,re,X+J,g,g+C,C);var K=s.scrollLeft,H=s.scrollTop;v+=H-(D=Math.max(0,Math.min(H+D/A,s.scrollHeight-z/A+k))),g+=K-(Q=Math.max(0,Math.min(K+Q/N,s.scrollWidth-W/N+J)))}j.push({el:s,top:D,left:Q})}return j};function Ve(e){return e===Object(e)&&Object.keys(e).length!==0}function Ge(e,t){t===void 0&&(t="auto");var a="scrollBehavior"in document.body.style;e.forEach(function(r){var l=r.el,o=r.top,u=r.left;l.scroll&&a?l.scroll({top:o,left:u,behavior:t}):(l.scrollTop=o,l.scrollLeft=u)})}function Je(e){return e===!1?{block:"end",inline:"nearest"}:Ve(e)?e:{block:"start",inline:"nearest"}}function _e(e,t){var a=e.isConnected||e.ownerDocument.documentElement.contains(e);if(Ve(t)&&typeof t.behavior=="function")return t.behavior(a?Pe(e,t):[]);if(!!a){var r=Je(t);return Ge(Pe(e,r),r.behavior)}}var et=_e,tt=["parentNode"],rt="form_item";function de(e){return e===void 0||e===!1?[]:Array.isArray(e)?e:[e]}function Te(e,t){if(!!e.length){var a=e.join("_");if(t)return"".concat(t,"_").concat(a);var r=tt.includes(a);return r?"".concat(rt,"_").concat(a):a}}function je(e){var t=de(e);return t.join("_")}function We(e){var t=(0,ue.useForm)(),a=(0,G.Z)(t,1),r=a[0],l=n.useRef({}),o=n.useMemo(function(){return e!=null?e:(0,x.Z)((0,x.Z)({},r),{__INTERNAL__:{itemRef:function(i){return function(d){var m=je(i);d?l.current[m]=d:delete l.current[m]}}},scrollToField:function(i){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=de(i),S=Te(m,o.__INTERNAL__.name),b=S?document.getElementById(S):null;b&&et(b,(0,x.Z)({scrollMode:"if-needed",block:"nearest"},d))},getFieldInstance:function(i){var d=je(i);return l.current[d]}})},[e,r]);return[o]}var nt=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)t.indexOf(r[l])<0&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(a[r[l]]=e[r[l]]);return a},at=function(t,a){var r=n.useContext(Oe.Z),l=n.useContext(we.Z),o=n.useContext(me.E_),u=o.getPrefixCls,i=o.direction,d=o.form,m=t.prefixCls,S=t.className,b=S===void 0?"":S,Z=t.size,h=Z===void 0?r:Z,y=t.disabled,w=y===void 0?l:y,f=t.form,O=t.colon,R=t.labelAlign,I=t.labelWrap,C=t.labelCol,E=t.wrapperCol,V=t.hideRequiredMark,F=t.layout,p=F===void 0?"horizontal":F,v=t.scrollToFirstError,g=t.requiredMark,j=t.onFinishFailed,L=t.name,s=nt(t,["prefixCls","className","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name"]),$=n.useContext(Be.Z),z=(0,n.useMemo)(function(){return g!==void 0?g:d&&d.requiredMark!==void 0?d.requiredMark:!V},[V,g,d]),W=O!=null?O:d==null?void 0:d.colon,M=u("form",m),U=le()(M,(0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(M,"-").concat(p),!0),"".concat(M,"-hide-required-mark"),z===!1),"".concat(M,"-rtl"),i==="rtl"),"".concat(M,"-").concat(h),h),b),te=We(f),oe=(0,G.Z)(te,1),q=oe[0],re=q.__INTERNAL__;re.name=L;var ne=(0,n.useMemo)(function(){return{name:L,labelAlign:R,labelCol:C,labelWrap:I,wrapperCol:E,vertical:p==="vertical",colon:W,requiredMark:z,itemRef:re.itemRef,form:q}},[L,R,C,E,p,W,z,q]);n.useImperativeHandle(a,function(){return q});var X=function(D){j==null||j(D);var Q={block:"nearest"};v&&D.errorFields.length&&((0,ge.Z)(v)==="object"&&(Q=v),q.scrollToField(D.errorFields[0].name,Q))};return n.createElement(we.n,{disabled:w},n.createElement(Oe.q,{size:h},n.createElement(T.RV,(0,x.Z)({},{validateMessages:$}),n.createElement(T.q3.Provider,{value:ne},n.createElement(ue.default,(0,x.Z)({id:L},s,{name:L,onFinishFailed:X,form:q,className:U}))))))},lt=n.forwardRef(at),ot=lt,it=c(30470),$e=c(42550),st=function(){var t=(0,n.useContext)(T.aM),a=t.status;return{status:a}},ut=st,ke=c(96159),ct=c(93355),Ae=c(75164);function dt(e){var t=n.useState(e),a=(0,G.Z)(t,2),r=a[0],l=a[1],o=(0,n.useRef)(null),u=(0,n.useRef)([]),i=(0,n.useRef)(!1);n.useEffect(function(){return i.current=!1,function(){i.current=!0,Ae.Z.cancel(o.current),o.current=null}},[]);function d(m){i.current||(o.current===null&&(u.current=[],o.current=(0,Ae.Z)(function(){o.current=null,l(function(S){var b=S;return u.current.forEach(function(Z){b=Z(b)}),b})})),u.current.push(m))}return[r,d]}function ft(){var e=n.useContext(T.q3),t=e.itemRef,a=n.useRef({});function r(l,o){var u=o&&(0,ge.Z)(o)==="object"&&o.ref,i=l.join("_");return(a.current.name!==i||a.current.originRef!==u)&&(a.current.name=i,a.current.originRef=u,a.current.ref=(0,$e.sQ)(t(l),u)),a.current.ref}return r}var mt=c(19735),vt=c(17012),gt=c(47918),ht=c(19267),Ct=c(8410),bt=c(98423),yt=c(92820),ze=c(28991),xt=c(36688),Zt=c(27713),Et=function(t,a){return n.createElement(Zt.Z,(0,ze.Z)((0,ze.Z)({},t),{},{ref:a,icon:xt.Z}))},Ft=n.forwardRef(Et),It=Ft,He=c(21584),St=c(42051),Rt=c(85636),pt=c(94199),Nt=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)t.indexOf(r[l])<0&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(a[r[l]]=e[r[l]]);return a};function wt(e){return e?(0,ge.Z)(e)==="object"&&!n.isValidElement(e)?e:{title:e}:null}var Ot=function(t){var a=t.prefixCls,r=t.label,l=t.htmlFor,o=t.labelCol,u=t.labelAlign,i=t.colon,d=t.required,m=t.requiredMark,S=t.tooltip,b=(0,St.E)("Form"),Z=(0,G.Z)(b,1),h=Z[0];return r?n.createElement(T.q3.Consumer,{key:"label"},function(y){var w=y.vertical,f=y.labelAlign,O=y.labelCol,R=y.labelWrap,I=y.colon,C,E=o||O||{},V=u||f,F="".concat(a,"-item-label"),p=le()(F,V==="left"&&"".concat(F,"-left"),E.className,(0,P.Z)({},"".concat(F,"-wrap"),!!R)),v=r,g=i===!0||I!==!1&&i!==!1,j=g&&!w;j&&typeof r=="string"&&r.trim()!==""&&(v=r.replace(/[:|：]\s*$/,""));var L=wt(S);if(L){var s=L.icon,$=s===void 0?n.createElement(It,null):s,z=Nt(L,["icon"]),W=n.createElement(pt.Z,(0,x.Z)({},z),n.cloneElement($,{className:"".concat(a,"-item-tooltip"),title:"",onClick:function(te){te.preventDefault()},tabIndex:null}));v=n.createElement(n.Fragment,null,v,W)}m==="optional"&&!d&&(v=n.createElement(n.Fragment,null,v,n.createElement("span",{className:"".concat(a,"-item-optional"),title:""},(h==null?void 0:h.optional)||((C=Rt.Z.Form)===null||C===void 0?void 0:C.optional))));var M=le()((0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(a,"-item-required"),d),"".concat(a,"-item-required-mark-optional"),m==="optional"),"".concat(a,"-item-no-colon"),!g));return n.createElement(He.Z,(0,x.Z)({},E,{className:p}),n.createElement("label",{htmlFor:l,className:M,title:typeof r=="string"?r:""},v))}):null},Mt=Ot,Lt=function(t){var a=t.prefixCls,r=t.status,l=t.wrapperCol,o=t.children,u=t.errors,i=t.warnings,d=t._internalItemRender,m=t.extra,S=t.help,b=t.fieldId,Z=t.marginBottom,h=t.onErrorVisibleChanged,y="".concat(a,"-item"),w=n.useContext(T.q3),f=l||w.wrapperCol||{},O=le()("".concat(y,"-control"),f.className),R=n.useMemo(function(){return(0,x.Z)({},w)},[w]);delete R.labelCol,delete R.wrapperCol;var I=n.createElement("div",{className:"".concat(y,"-control-input")},n.createElement("div",{className:"".concat(y,"-control-input-content")},o)),C=n.useMemo(function(){return{prefixCls:a,status:r}},[a,r]),E=Z!==null||u.length||i.length?n.createElement("div",{style:{display:"flex",flexWrap:"nowrap"}},n.createElement(T.Rk.Provider,{value:C},n.createElement(Ne,{fieldId:b,errors:u,warnings:i,help:S,helpStatus:r,className:"".concat(y,"-explain-connected"),onVisibleChanged:h})),!!Z&&n.createElement("div",{style:{width:0,height:Z}})):null,V={};b&&(V.id="".concat(b,"_extra"));var F=m?n.createElement("div",(0,x.Z)({},V,{className:"".concat(y,"-extra")}),m):null,p=d&&d.mark==="pro_table_render"&&d.render?d.render(t,{input:I,errorList:E,extra:F}):n.createElement(n.Fragment,null,I,E,F);return n.createElement(T.q3.Provider,{value:R},n.createElement(He.Z,(0,x.Z)({},f,{className:O}),p))},Pt=Lt,Vt=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)t.indexOf(r[l])<0&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(a[r[l]]=e[r[l]]);return a},Tt={success:mt.Z,warning:gt.Z,error:vt.Z,validating:ht.Z};function jt(e){var t=e.prefixCls,a=e.className,r=e.style,l=e.help,o=e.errors,u=e.warnings,i=e.validateStatus,d=e.meta,m=e.hasFeedback,S=e.hidden,b=e.children,Z=e.fieldId,h=e.isRequired,y=e.onSubItemMetaChange,w=Vt(e,["prefixCls","className","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","isRequired","onSubItemMetaChange"]),f="".concat(t,"-item"),O=n.useContext(T.q3),R=O.requiredMark,I=n.useRef(null),C=ve(o),E=ve(u),V=l!=null,F=!!(V||o.length||u.length),p=n.useState(null),v=(0,G.Z)(p,2),g=v[0],j=v[1];(0,Ct.Z)(function(){if(F&&I.current){var W=getComputedStyle(I.current);j(parseInt(W.marginBottom,10))}},[F]);var L=function(M){M||j(null)},s="";i!==void 0?s=i:d.validating?s="validating":C.length?s="error":E.length?s="warning":d.touched&&(s="success");var $=n.useMemo(function(){var W;if(m){var M=s&&Tt[s];W=M?n.createElement("span",{className:le()("".concat(f,"-feedback-icon"),"".concat(f,"-feedback-icon-").concat(s))},n.createElement(M,null)):null}return{status:s,hasFeedback:m,feedbackIcon:W,isFormItemInput:!0}},[s,m]),z=(0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)({},f,!0),"".concat(f,"-with-help"),V||C.length||E.length),"".concat(a),!!a),"".concat(f,"-has-feedback"),s&&m),"".concat(f,"-has-success"),s==="success"),"".concat(f,"-has-warning"),s==="warning"),"".concat(f,"-has-error"),s==="error"),"".concat(f,"-is-validating"),s==="validating"),"".concat(f,"-hidden"),S);return n.createElement("div",{className:le()(z),style:r,ref:I},n.createElement(yt.Z,(0,x.Z)({className:"".concat(f,"-row")},(0,bt.Z)(w,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","required","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol"])),n.createElement(Mt,(0,x.Z)({htmlFor:Z,required:h,requiredMark:R},e,{prefixCls:t})),n.createElement(Pt,(0,x.Z)({},e,d,{errors:C,warnings:E,prefixCls:t,status:s,help:l,marginBottom:g,onErrorVisibleChanged:L}),n.createElement(T.qI.Provider,{value:y},n.createElement(T.aM.Provider,{value:$},b)))),!!g&&n.createElement("div",{className:"".concat(f,"-margin-offset"),style:{marginBottom:-g}}))}var Wt="__SPLIT__",Xt=(0,ct.b)("success","warning","error","validating",""),$t=n.memo(function(e){var t=e.children;return t},function(e,t){return e.value===t.value&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every(function(a,r){return a===t.childProps[r]})});function kt(e){return e!=null}function qe(){return{errors:[],warnings:[],touched:!1,validating:!1,validated:!1,name:[]}}function At(e){var t=e.name,a=e.noStyle,r=e.dependencies,l=e.prefixCls,o=e.shouldUpdate,u=e.rules,i=e.children,d=e.required,m=e.label,S=e.messageVariables,b=e.trigger,Z=b===void 0?"onChange":b,h=e.validateTrigger,y=e.hidden,w=(0,n.useContext)(me.E_),f=w.getPrefixCls,O=(0,n.useContext)(T.q3),R=O.name,I=typeof i=="function",C=(0,n.useContext)(T.qI),E=(0,n.useContext)(ue.FieldContext),V=E.validateTrigger,F=h!==void 0?h:V,p=kt(t),v=f("form",l),g=n.useContext(ue.ListContext),j=n.useRef(),L=dt({}),s=(0,G.Z)(L,2),$=s[0],z=s[1],W=(0,it.Z)(function(){return qe()}),M=(0,G.Z)(W,2),U=M[0],te=M[1],oe=function(N){var A=g==null?void 0:g.getKey(N.name);if(te(N.destroy?qe():N,!0),a&&C){var K=N.name;if(N.destroy)K=j.current||K;else if(A!==void 0){var H=(0,G.Z)(A,2),se=H[0],ae=H[1];K=[se].concat((0,Y.Z)(ae)),j.current=K}C(N,K)}},q=function(N,A){z(function(K){var H=(0,x.Z)({},K),se=[].concat((0,Y.Z)(N.name.slice(0,-1)),(0,Y.Z)(A)),ae=se.join(Wt);return N.destroy?delete H[ae]:H[ae]=N,H})},re=n.useMemo(function(){var k=(0,Y.Z)(U.errors),N=(0,Y.Z)(U.warnings);return Object.values($).forEach(function(A){k.push.apply(k,(0,Y.Z)(A.errors||[])),N.push.apply(N,(0,Y.Z)(A.warnings||[]))}),[k,N]},[$,U.errors,U.warnings]),ne=(0,G.Z)(re,2),X=ne[0],ie=ne[1],D=ft();function Q(k,N,A){return a&&!y?k:n.createElement(jt,(0,x.Z)({key:"row"},e,{prefixCls:v,fieldId:N,isRequired:A,errors:X,warnings:ie,meta:U,onSubItemMetaChange:q}),k)}if(!p&&!I&&!r)return Q(i);var J={};return typeof m=="string"?J.label=m:t&&(J.label=String(t)),S&&(J=(0,x.Z)((0,x.Z)({},J),S)),n.createElement(ue.Field,(0,x.Z)({},e,{messageVariables:J,trigger:Z,validateTrigger:F,onMetaChange:oe}),function(k,N,A){var K=de(t).length&&N?N.name:[],H=Te(K,R),se=d!==void 0?d:!!(u&&u.some(function(_){if(_&&(0,ge.Z)(_)==="object"&&_.required&&!_.warningOnly)return!0;if(typeof _=="function"){var ce=_(A);return ce&&ce.required&&!ce.warningOnly}return!1})),ae=(0,x.Z)({},k),fe=null;if(Array.isArray(i)&&p)fe=i;else if(!(I&&(!(o||r)||p))){if(!(r&&!I&&!p))if((0,ke.l$)(i)){var B=(0,x.Z)((0,x.Z)({},i.props),ae);if(B.id||(B.id=H),e.help||X.length>0||ie.length>0||e.extra){var Ze=[];(e.help||X.length>0)&&Ze.push("".concat(H,"_help")),e.extra&&Ze.push("".concat(H,"_extra")),B["aria-describedby"]=Ze.join(" ")}X.length>0&&(B["aria-invalid"]="true"),se&&(B["aria-required"]="true"),(0,$e.Yr)(i)&&(B.ref=D(K,i));var Ut=new Set([].concat((0,Y.Z)(de(Z)),(0,Y.Z)(de(F))));Ut.forEach(function(_){B[_]=function(){for(var ce,Qe,Ee,Ke,Fe,Ue=arguments.length,Ie=new Array(Ue),Ce=0;Ce<Ue;Ce++)Ie[Ce]=arguments[Ce];(Ee=ae[_])===null||Ee===void 0||(ce=Ee).call.apply(ce,[ae].concat(Ie)),(Fe=(Ke=i.props)[_])===null||Fe===void 0||(Qe=Fe).call.apply(Qe,[Ke].concat(Ie))}});var Yt=[B["aria-required"],B["aria-invalid"],B["aria-describedby"]];fe=n.createElement($t,{value:ae[e.valuePropName||"value"],update:i,childProps:Yt},(0,ke.Tm)(i,B))}else I&&(o||r)&&!p?fe=i(A):fe=i}return Q(fe,H,se)})}var De=At;De.useStatus=ut;var zt=De,Ht=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)t.indexOf(r[l])<0&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(a[r[l]]=e[r[l]]);return a},qt=function(t){var a=t.prefixCls,r=t.children,l=Ht(t,["prefixCls","children"]),o=n.useContext(me.E_),u=o.getPrefixCls,i=u("form",a),d=n.useMemo(function(){return{prefixCls:i,status:"error"}},[i]);return n.createElement(ue.List,(0,x.Z)({},l),function(m,S,b){return n.createElement(T.Rk.Provider,{value:d},r(m.map(function(Z){return(0,x.Z)((0,x.Z)({},Z),{fieldKey:Z.key})}),S,{errors:b.errors,warnings:b.warnings}))})},Dt=qt;function Qt(){var e=(0,n.useContext)(T.q3),t=e.form;return t}var ee=ot;ee.Item=zt,ee.List=Dt,ee.ErrorList=Ne,ee.useForm=We,ee.useFormInstance=Qt,ee.useWatch=ue.useWatch,ee.Provider=T.RV,ee.create=function(){};var Kt=ee}}]);
