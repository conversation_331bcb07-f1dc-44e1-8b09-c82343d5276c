(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[5565],{87588:function(F,M,e){"use strict";var i=e(28991),c=e(67294),h=e(61144),E=e(27029),u=function(I,Z){return c.createElement(E.Z,(0,i.Z)((0,i.Z)({},I),{},{ref:Z,icon:h.Z}))};u.displayName="ExclamationCircleOutlined",M.Z=c.forwardRef(u)},82029:function(F,M,e){"use strict";var i=e(28991),c=e(67294),h=e(75573),E=e(27029),u=function(I,Z){return c.createElement(E.Z,(0,i.Z)((0,i.Z)({},I),{},{ref:Z,icon:h.Z}))};u.displayName="FileOutlined",M.Z=c.forwardRef(u)},54734:function(F,M,e){"use strict";var i=e(28991),c=e(67294),h=e(48898),E=e(27029),u=function(I,Z){return c.createElement(E.Z,(0,i.Z)((0,i.Z)({},I),{},{ref:Z,icon:h.Z}))};u.displayName="FolderOpenOutlined",M.Z=c.forwardRef(u)},55725:function(F,M,e){"use strict";var i=e(28991),c=e(67294),h=e(85118),E=e(27029),u=function(I,Z){return c.createElement(E.Z,(0,i.Z)((0,i.Z)({},I),{},{ref:Z,icon:h.Z}))};u.displayName="FolderOutlined",M.Z=c.forwardRef(u)},70347:function(){},18067:function(){},86468:function(F,M,e){"use strict";e.d(M,{lq:function(){return N},FQ:function(){return Q},fl:function(){return U}});var i=e(43358),c=e(34041),h=e(20228),E=e(11382),u=e(11849),l=e(94657),I=e(93224),Z=e(3980),W=e(21307),S=e(67294),v=e(85893),G=["fps","fetcher","width","className","defaultOptions","readonly","value"],N=function(_){var g=_.fps,H=g===void 0?30:g,t=_.fetcher,d=_.width,r=_.className,n=_.defaultOptions,a=_.readonly,s=_.value,o=(0,I.Z)(_,G),D=(0,S.useState)(!1),f=(0,l.Z)(D,2),m=f[0],O=f[1],T=(0,S.useState)(n||[]),A=(0,l.Z)(T,2),y=A[0],B=A[1],C=S.useRef(0),j=U(d),x=(0,Z.zE)(function(b){O(!0),B([]),C.current+=1;var L=C.current;_.fetcher(b).then(function(P){L===C.current&&(B(P),O(!1))})},H);if(a){var K;return(0,v.jsx)(W.ZP.Item,(0,u.Z)((0,u.Z)({},o),{},{children:(0,v.jsx)("span",{children:(K=y.find(function(b){return b.value===s}))===null||K===void 0?void 0:K.label})}))}return(0,v.jsx)(W.ZP.Item,(0,u.Z)((0,u.Z)({},o),{},{children:(0,v.jsx)(c.Z,(0,u.Z)({showSearch:!0,placeholder:_.placeholder,filterOption:!1,onSearch:x,allowClear:!0,onFocus:function(){return x("")},onClear:function(){return x("")},loading:m,notFoundContent:m?(0,v.jsx)(E.Z,{size:"small"}):null,options:y},j))}))},z=e(62999),V=e(54680),$=["width","className","treeData","placeholder","readonly","value"],Q=function(_){var g=_.width,H=_.className,t=_.treeData,d=_.placeholder,r=_.readonly,n=_.value,a=(0,I.Z)(_,$),s=U(g);if(r){var o;return(0,v.jsx)(W.ZP.Item,(0,u.Z)((0,u.Z)({},a),{},{children:(0,v.jsx)("span",{children:(o=(0,Z.hE)(t,n,"key"))===null||o===void 0?void 0:o.title})}))}return(0,v.jsx)(W.ZP.Item,(0,u.Z)((0,u.Z)({},a),{},{children:(0,v.jsx)(V.Z,(0,u.Z)({dropdownStyle:{maxHeight:400,overflow:"auto"},treeData:t,placeholder:d,treeDefaultExpandAll:!0},s))}))};function U(p){if(!p)return{};if(typeof p=="number")return{style:{width:p}};if(typeof p=="string")return{className:"pro-field ".concat(typeof p=="string"?"pro-field-".concat(p):"")}}},79871:function(F,M,e){"use strict";e.d(M,{G:function(){return n},y:function(){return r}});var i=e(57663),c=e(71577),h=e(39428),E=e(3182),u=e(34792),l=e(48086),I=e(71194),Z=e(50146),W=e(94657),S=e(83279),v=e(3980),G=e(87588),N=e(51042),z=e(16894),V=e(9761),$=e(60780),Q=e.n($),U=e(67294),p=e(11849),_=e(86468),g=e(21307),H=e(80582),t=e(85893),d=(0,H.Pi)(function(a){var s=a.dept,o=a.onClose,D=a.onOk,f=(0,v.m2)(),m=(0,U.useRef)(),O=!!(s!=null&&s.id),T=f.depts,A=(0,U.useMemo)(function(){return Q()(T.map(function(y){return{value:y.id,title:y.name,key:y.id,parentId:y.parentId}}),{parentProperty:"parentId",customID:"value"})},[T]);return(0,t.jsxs)(g.aN,{title:O?"\u4FEE\u6539\u7EC4\u7EC7\u673A\u6784":"\u65B0\u5EFA\u7EC4\u7EC7\u673A\u6784",formRef:m,initialValues:(0,p.Z)({},s),visible:!0,drawerProps:{forceRender:!0,destroyOnClose:!0,onClose:function(){o()}},onFinish:function(){var y=(0,E.Z)((0,h.Z)().mark(function B(C){var j;return(0,h.Z)().wrap(function(K){for(;;)switch(K.prev=K.next){case 0:return K.next=2,f.saveOrUpdateDept((0,p.Z)((0,p.Z)({},s),C));case 2:j=K.sent,j.success?D():l.default.error("\u4FDD\u5B58\u5931\u8D25");case 4:case"end":return K.stop()}},B)}));return function(B){return y.apply(this,arguments)}}(),children:[(0,t.jsxs)(g.ZP.Group,{children:[(!s||s&&s.id!=="1")&&T.length>0&&(0,t.jsx)(_.FQ,{name:"parentId",width:"md",treeData:A,label:"\u4E0A\u7EA7\u90E8\u95E8",placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",required:!0,value:s==null?void 0:s.id,rules:[{required:!0,message:"\u4E0A\u7EA7\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A"}]}),(0,t.jsx)(g.V,{name:"code",width:"md",label:"\u7F16\u7801",placeholder:"\u8BF7\u8F93\u5165\u7F16\u7801",required:!0,rules:[{required:!0,message:"\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A"}]})]}),(0,t.jsxs)(g.ZP.Group,{children:[(0,t.jsx)(g.V,{name:"name",width:"md",label:"\u5168\u79F0",id:"form-name",placeholder:"\u8BF7\u8F93\u5165\u5168\u79F0",required:!0,rules:[{required:!0,message:"\u5168\u79F0\u4E0D\u80FD\u4E3A\u7A7A"}]}),(0,t.jsx)(g.V,{name:"shortName",width:"md",label:"\u7B80\u79F0",id:"form-name",placeholder:"\u8BF7\u8F93\u5165\u7B80\u79F0",required:!0})]}),(0,t.jsx)(g.ZP.Group,{children:(0,t.jsx)(_.lq,{fps:2,name:"managerId",width:"md",label:"\u8D1F\u8D23\u4EBA",placeholder:"\u8F93\u5165\u59D3\u540D\u68C0\u7D22",value:s==null?void 0:s.managerId,defaultOptions:O?[{label:s.managerName,value:s.managerId}]:void 0,fetcher:function(B){return v.hi.loadUsers({name:B}).then(function(C){return C.list.map(function(j){return{label:j.name,value:j.id}})})}})}),(0,t.jsx)(g.ZP.Group,{children:(0,t.jsx)(g.$J,{name:"remark",width:690,label:"\u5907\u6CE8",id:"form-name",placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"})})]})});function r(a,s){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"id",D=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!s)return a;for(var f=(0,S.Z)(a);f.length;){var m=f[D?"pop":"shift"]();if(m&&m[o]===s)return[m];m!=null&&m.children&&f.push.apply(f,(0,S.Z)(m.children))}return null}var n=(0,V.Pi)(function(a){var s=a.selectedDept,o=(0,v.m2)(),D=o.loading,f=(0,U.useRef)(),m=(0,U.useState)({visible:!1}),O=(0,W.Z)(m,2),T=O[0],A=O[1],y=r(Q()(o.depts,{customID:"id",parentProperty:"parentId"}),s),B=[{title:"\u673A\u6784\u540D\u79F0",dataIndex:"name"},{title:"\u673A\u6784\u7B80\u79F0",dataIndex:"shortName",search:!1,width:200},{title:"\u8D1F\u8D23\u4EBA",dataIndex:"managerName",search:!1,width:150},{title:"\u64CD\u4F5C",valueType:"option",render:function(j,x){return[(0,t.jsx)("a",{onClick:function(){return A({visible:!0,current:x})},children:"\u7F16\u8F91"},"edit"),(0,t.jsx)("a",{onClick:function(){if(x.id==="1"){Z.Z.info({title:"\u63D0\u793A",content:"\u5185\u7F6E\u673A\u6784\u4E0D\u80FD\u5220\u9664"});return}if(x.children&&x.children.length>0){l.default.error("\u8BF7\u5148\u5220\u9664\u5B50\u673A\u6784\uFF01");return}Z.Z.confirm({title:"\u5220\u9664",content:"\u786E\u5B9A\u5220\u9664\u5F53\u524D\u673A\u6784?",icon:(0,t.jsx)(G.Z,{}),onOk:function(){o.deleteDept(x.id)}})},children:"\u5220\u9664"},"delete")]}}];return(0,t.jsxs)("div",{children:[(0,t.jsx)(z.ZP,{columns:B,actionRef:f,loading:D,bordered:!0,dataSource:y,request:(0,E.Z)((0,h.Z)().mark(function C(){return(0,h.Z)().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:return x.abrupt("return",o.loadDepts());case 1:case"end":return x.stop()}},C)})),columnsState:{persistenceKey:"pro-table-dept",persistenceType:"localStorage"},rowKey:"id",search:!1,scroll:{x:650},pagination:!1,dateFormatter:"string",headerTitle:"\u673A\u6784\u7BA1\u7406",toolBarRender:function(){return[(0,t.jsx)(c.Z,{icon:(0,t.jsx)(N.Z,{}),type:"primary",onClick:function(){A({visible:!0})},children:"\u65B0\u5EFA"},"button")]}}),T.visible&&(0,t.jsx)(d,{onOk:function(){A({visible:!1})},dept:T.current,readonly:T.readonly,onClose:function(){return A({visible:!1})}})]})})},40017:function(F,M,e){"use strict";e.d(M,{A:function(){return a}});var i=e(58024),c=e(91894),h=e(32157),E=e(38614),u=e(34792),l=e(48086),I=e(49111),Z=e(19650),W=e(22385),S=e(94199),v=e(94657),G=e(83279),N=e(3980),z=e(81162),V=e(17828),$=e(59879),Q=e(54734),U=e(82029),p=e(55725),_=e(9761),g=e(60780),H=e.n(g),t=e(67294),d=e(79871),r=e(85893);function n(s){var o=[];return s.forEach(function(D){D.children&&(o.push(D.key),o.push.apply(o,(0,G.Z)(n(D.children))))}),o}var a=(0,_.Pi)(function(s){var o=(0,N.m2)(),D=o.depts;(0,t.useEffect)(function(){o.loadDepts()},[o]);var f=(0,t.useMemo)(function(){return H()(D.map(function(L){return{title:L.name,key:L.id,parentId:L.parentId}}),{parentProperty:"parentId",customID:"key"})},[D]),m=(0,t.useState)([]),O=(0,v.Z)(m,2),T=O[0],A=O[1],y=(0,t.useState)(!0),B=(0,v.Z)(y,2),C=B[0],j=B[1],x=(0,t.useMemo)(function(){return n(f)},[f]),K=function(P){A(P),j(!1)},b=function(P){P.length>0?s.onSelect(P[0]):s.onSelect(void 0)};return(0,r.jsx)(c.Z,{title:"\u7EC4\u7EC7\u673A\u6784",extra:(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(Z.Z,{children:[T.length>0?(0,r.jsx)(S.Z,{title:"\u6298\u53E0\u6240\u6709",children:(0,r.jsx)(z.Z,{style:{cursor:"pointer"},onClick:function(){return A([])}})}):(0,r.jsx)(S.Z,{title:"\u5C55\u5F00\u6240\u6709",children:(0,r.jsx)(V.Z,{style:{cursor:"pointer"},onClick:function(){return A(x)}})}),(0,r.jsx)(S.Z,{title:"\u5237\u65B0",children:(0,r.jsx)($.Z,{style:{cursor:"pointer"},onClick:function(){return o.loadDepts()}})})]})}),children:(0,r.jsx)(E.Z,{expandedKeys:T,onExpand:K,draggable:!0,autoExpandParent:C,onSelect:b,showIcon:!0,treeData:f,onDrop:function(P){if(P.dragNode.pos!=="".concat(P.node.pos,"-0")){if(P.node.parentId!==P.dragNode.parentId&&P.dragNode.parentId!==P.node.key){l.default.error("\u53EA\u80FD\u6539\u53D8\u540C\u7EA7\u673A\u6784\u7684\u987A\u5E8F");return}var Y=P.dragNode.parentId,J=(0,d.y)(f,Y,"key");if(J){var q,k=(q=J[0].children)===null||q===void 0?void 0:q.map(function(ee){return ee.key}),ne=P.dragNode.key,ae=k.indexOf(ne);k.splice(ae,1,""),k.splice(P.dropPosition,0,ne),o.sortDept(k.filter(function(ee){return ee}))}}},icon:function(P){var Y=P.expanded;return Y?(0,r.jsx)(Q.Z,{}):!P.data.children||P.data.children.length===0?(0,r.jsx)(U.Z,{}):(0,r.jsx)(p.Z,{})}})})})},83197:function(F,M,e){"use strict";e.r(M);var i=e(13062),c=e(71230),h=e(89032),E=e(15746),u=e(94657),l=e(36450),I=e(9761),Z=e(67294),W=e(79871),S=e(40017),v=e(85893),G=(0,I.Pi)(function(){var N=(0,Z.useState)(),z=(0,u.Z)(N,2),V=z[0],$=z[1];return(0,v.jsx)(l._z,{title:!1,children:(0,v.jsxs)(c.Z,{gutter:20,children:[(0,v.jsx)(E.Z,{xs:0,md:8,children:(0,v.jsx)(S.A,{onSelect:function(U){return $(U)}})}),(0,v.jsx)(E.Z,{xs:24,md:16,children:(0,v.jsx)(W.G,{selectedDept:V})})]})})});M.default=G},91894:function(F,M,e){"use strict";e.d(M,{Z:function(){return H}});var i=e(96156),c=e(22122),h=e(94184),E=e.n(h),u=e(98423),l=e(67294),I=e(53124),Z=e(97647),W=e(43574),S=e(72488),v=function(t,d){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&d.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)d.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(r[n[a]]=t[n[a]]);return r},G=function(d){var r=d.prefixCls,n=d.className,a=d.hoverable,s=a===void 0?!0:a,o=v(d,["prefixCls","className","hoverable"]);return l.createElement(I.C,null,function(D){var f=D.getPrefixCls,m=f("card",r),O=E()("".concat(m,"-grid"),n,(0,i.Z)({},"".concat(m,"-grid-hoverable"),s));return l.createElement("div",(0,c.Z)({},o,{className:O}))})},N=G,z=function(t,d){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&d.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)d.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(r[n[a]]=t[n[a]]);return r};function V(t){var d=t.map(function(r,n){return l.createElement("li",{style:{width:"".concat(100/t.length,"%")},key:"action-".concat(n)},l.createElement("span",null,r))});return d}var $=l.forwardRef(function(t,d){var r=l.useContext(I.E_),n=r.getPrefixCls,a=r.direction,s=l.useContext(Z.Z),o=function(X){var w;(w=t.onTabChange)===null||w===void 0||w.call(t,X)},D=function(){var X;return l.Children.forEach(t.children,function(w){w&&w.type&&w.type===N&&(X=!0)}),X},f=t.prefixCls,m=t.className,O=t.extra,T=t.headStyle,A=T===void 0?{}:T,y=t.bodyStyle,B=y===void 0?{}:y,C=t.title,j=t.loading,x=t.bordered,K=x===void 0?!0:x,b=t.size,L=t.type,P=t.cover,Y=t.actions,J=t.tabList,q=t.children,k=t.activeTabKey,ne=t.defaultActiveTabKey,ae=t.tabBarExtraContent,ee=t.hoverable,re=t.tabProps,ie=re===void 0?{}:re,ue=z(t,["prefixCls","className","extra","headStyle","bodyStyle","title","loading","bordered","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps"]),R=n("card",f),ce=l.createElement(W.Z,{loading:!0,active:!0,paragraph:{rows:4},title:!1},q),le=k!==void 0,ve=(0,c.Z)((0,c.Z)({},ie),(0,i.Z)((0,i.Z)({},le?"activeKey":"defaultActiveKey",le?k:ne),"tabBarExtraContent",ae)),se,oe=J&&J.length?l.createElement(S.Z,(0,c.Z)({size:"large"},ve,{className:"".concat(R,"-head-tabs"),onChange:o,items:J.map(function(te){var X;return{label:te.tab,key:te.key,disabled:(X=te.disabled)!==null&&X!==void 0?X:!1}})})):null;(C||O||oe)&&(se=l.createElement("div",{className:"".concat(R,"-head"),style:A},l.createElement("div",{className:"".concat(R,"-head-wrapper")},C&&l.createElement("div",{className:"".concat(R,"-head-title")},C),O&&l.createElement("div",{className:"".concat(R,"-extra")},O)),oe));var _e=P?l.createElement("div",{className:"".concat(R,"-cover")},P):null,fe=l.createElement("div",{className:"".concat(R,"-body"),style:B},j?ce:q),Ee=Y&&Y.length?l.createElement("ul",{className:"".concat(R,"-actions")},V(Y)):null,me=(0,u.Z)(ue,["onTabChange"]),de=b||s,Oe=E()(R,(0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)((0,i.Z)({},"".concat(R,"-loading"),j),"".concat(R,"-bordered"),K),"".concat(R,"-hoverable"),ee),"".concat(R,"-contain-grid"),D()),"".concat(R,"-contain-tabs"),J&&J.length),"".concat(R,"-").concat(de),de),"".concat(R,"-type-").concat(L),!!L),"".concat(R,"-rtl"),a==="rtl"),m);return l.createElement("div",(0,c.Z)({ref:d},me,{className:Oe}),se,_e,fe,Ee)}),Q=$,U=function(t,d){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&d.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)d.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(r[n[a]]=t[n[a]]);return r},p=function(d){return l.createElement(I.C,null,function(r){var n=r.getPrefixCls,a=d.prefixCls,s=d.className,o=d.avatar,D=d.title,f=d.description,m=U(d,["prefixCls","className","avatar","title","description"]),O=n("card",a),T=E()("".concat(O,"-meta"),s),A=o?l.createElement("div",{className:"".concat(O,"-meta-avatar")},o):null,y=D?l.createElement("div",{className:"".concat(O,"-meta-title")},D):null,B=f?l.createElement("div",{className:"".concat(O,"-meta-description")},f):null,C=y||B?l.createElement("div",{className:"".concat(O,"-meta-detail")},y,B):null;return l.createElement("div",(0,c.Z)({},m,{className:T}),A,C)})},_=p,g=Q;g.Grid=N,g.Meta=_;var H=g},58024:function(F,M,e){"use strict";var i=e(38663),c=e.n(i),h=e(70347),E=e.n(h),u=e(71748),l=e(18106)},71748:function(F,M,e){"use strict";var i=e(38663),c=e.n(i),h=e(18067),E=e.n(h)}}]);
