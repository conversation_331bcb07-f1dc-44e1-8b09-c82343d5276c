version: 1.9.0
spring:
  application:
    name: survey
  profiles:
    active: @activeProfile@ # maven 配置
#    active: ${activeProfile} # gradle 配置
  mvc:
    async:
      request-timeout: -1
    static-path-pattern: /notfound # 覆盖默认的 /**
    throw-exception-if-no-handler-found: true
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB
  jackson:
    default-property-inclusion: non_null # 序列化忽略空值
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      FAIL_ON_EMPTY_BEANS: false
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false # 反序列化时允许未知属性
sk:
  security:
    url-token-authentication:
      enabled: true # 默认开启 url token 认证
flowable:
  async-executor-activate: false
  database-schema-update: false

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: local # 缓存 local/redis...
    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: survey # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 2 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败5次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制

api:
  prefix: /api

surveyking:
  ai:
    siliconflow:
      enabled: true
      base-url: https://api.siliconflow.cn
      token: ${AI_SILICONFLOW_TOKEN:your_token}
      model-id: deepseek-chat
      model-types:
        - display-name: "DeepSeek Chat"
          value: "deepseek-chat"
          description: "DeepSeek 对话模型"
        - display-name: "Qwen2.5-72B-Instruct"
          value: "Qwen/Qwen2.5-72B-Instruct"
          description: "Qwen2.5 72B 指令模型"
        - display-name: "Llama-3.1-70B-Instruct"
          value: "meta-llama/Meta-Llama-3.1-70B-Instruct"
          description: "Llama 3.1 70B 指令模型"