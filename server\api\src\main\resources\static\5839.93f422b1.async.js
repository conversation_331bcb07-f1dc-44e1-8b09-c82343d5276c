(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[5839],{22963:function(Ct,ot,v){"use strict";v.d(ot,{ID:function(){return B},X0:function(){return u},lQ:function(){return w}});var j=v(35151),Q=v(44292),X=v(27214),$=v(85669),k=v(33051);function B(O,E,Z,Y,z){var D=O.getArea(),P=D.x,b=D.y,W=D.width,S=D.height,I=Z.get(["lineStyle","width"])||2;P-=I/2,b-=I/2,W+=I,S+=I,W=Math.ceil(W),P!==Math.floor(P)&&(P=Math.floor(P),W++);var m=new j.Z({shape:{x:P,y:b,width:W,height:S}});if(E){var T=O.getBaseAxis(),A=T.isHorizontal(),R=T.inverse;A?(R&&(m.shape.x+=W),m.shape.width=0):(R||(m.shape.y+=S),m.shape.height=0);var U=(0,k.mf)(z)?function(K){z(K,m)}:null;Q.KZ(m,{shape:{width:W,height:S,x:P,y:b}},Z,null,Y,U)}return m}function u(O,E,Z){var Y=O.getArea(),z=(0,$.NM)(Y.r0,1),D=(0,$.NM)(Y.r,1),P=new X.C({shape:{cx:(0,$.NM)(O.cx,1),cy:(0,$.NM)(O.cy,1),r0:z,r:D,startAngle:Y.startAngle,endAngle:Y.endAngle,clockwise:Y.clockwise}});if(E){var b=O.getBaseAxis().dim==="angle";b?P.shape.endAngle=Y.startAngle:P.shape.r=z,Q.KZ(P,{shape:{endAngle:Y.endAngle,r:D}},Z)}return P}function w(O,E,Z,Y,z){if(O){if(O.type==="polar")return u(O,E,Z);if(O.type==="cartesian2d")return B(O,E,Z,Y,z)}else return null;return null}},38455:function(Ct,ot,v){"use strict";v.d(ot,{Z:function(){return m}});var j=v(33051),Q=v(5101),X=v(55623),$=v(10381),k=v(32234),B=v(54267),u=function(){function T(A){this.coordSysDims=[],this.axisMap=(0,j.kW)(),this.categoryAxisMap=(0,j.kW)(),this.coordSysName=A}return T}();function w(T){var A=T.get("coordinateSystem"),R=new u(A),U=O[A];if(U)return U(T,R,R.axisMap,R.categoryAxisMap),R}var O={cartesian2d:function(T,A,R,U){var K=T.getReferringComponents("xAxis",k.C6).models[0],J=T.getReferringComponents("yAxis",k.C6).models[0];A.coordSysDims=["x","y"],R.set("x",K),R.set("y",J),E(K)&&(U.set("x",K),A.firstCategoryDimIndex=0),E(J)&&(U.set("y",J),A.firstCategoryDimIndex==null&&(A.firstCategoryDimIndex=1))},singleAxis:function(T,A,R,U){var K=T.getReferringComponents("singleAxis",k.C6).models[0];A.coordSysDims=["single"],R.set("single",K),E(K)&&(U.set("single",K),A.firstCategoryDimIndex=0)},polar:function(T,A,R,U){var K=T.getReferringComponents("polar",k.C6).models[0],J=K.findAxisModel("radiusAxis"),nt=K.findAxisModel("angleAxis");A.coordSysDims=["radius","angle"],R.set("radius",J),R.set("angle",nt),E(J)&&(U.set("radius",J),A.firstCategoryDimIndex=0),E(nt)&&(U.set("angle",nt),A.firstCategoryDimIndex==null&&(A.firstCategoryDimIndex=1))},geo:function(T,A,R,U){A.coordSysDims=["lng","lat"]},parallel:function(T,A,R,U){var K=T.ecModel,J=K.getComponent("parallel",T.get("parallelIndex")),nt=A.coordSysDims=J.dimensions.slice();(0,j.S6)(J.parallelAxisIndex,function(ut,et){var ct=K.getComponent("parallelAxis",ut),dt=nt[et];R.set(dt,ct),E(ct)&&(U.set(dt,ct),A.firstCategoryDimIndex==null&&(A.firstCategoryDimIndex=et))})}};function E(T){return T.get("type")==="category"}var Z=v(99574),Y=v(99936),z=v(61772),D=v(94279);function P(T,A){var R=T.get("coordinateSystem"),U=B.Z.get(R),K;return A&&A.coordSysDims&&(K=j.UI(A.coordSysDims,function(J){var nt={name:J},ut=A.axisMap.get(J);if(ut){var et=ut.get("type");nt.type=(0,$.T)(et)}return nt})),K||(K=U&&(U.getDimensionsInfo?U.getDimensionsInfo():U.dimensions.slice())||["x","y"]),K}function b(T,A,R){var U,K;return R&&j.S6(T,function(J,nt){var ut=J.coordDim,et=R.categoryAxisMap.get(ut);et&&(U==null&&(U=nt),J.ordinalMeta=et.getOrdinalMeta(),A&&(J.createInvertedIndices=!0)),J.otherDims.itemName!=null&&(K=!0)}),!K&&U!=null&&(T[U].otherDims.itemName=0),U}function W(T,A,R){R=R||{};var U=A.getSourceManager(),K,J=!1;T?(J=!0,K=(0,Z.nx)(T)):(K=U.getSource(),J=K.sourceFormat===D.cy);var nt=w(A),ut=P(A,nt),et=R.useEncodeDefaulter,ct=j.mf(et)?et:et?j.WA(z.pY,ut,A):null,dt={coordDimensions:ut,generateCoord:R.generateCoord,encodeDefine:A.getEncode(),encodeDefaulter:ct,canOmitUnusedDimensions:!J},ht=(0,X.Z)(K,dt),vt=b(ht.dimensions,R.createInvertedIndices,nt),ft=J?null:U.getSharedDataStore(ht),At=(0,Y.BM)(A,{schema:ht,store:ft}),mt=new Q.Z(ht,A);mt.setCalculationInfo(At);var yt=vt!=null&&S(K)?function(Mt,St,pt,Tt){return Tt===vt?pt:this.defaultDimValueGetter(Mt,St,pt,Tt)}:null;return mt.hasItemOption=!1,mt.initData(J?K:ft,null,yt),mt}function S(T){if(T.sourceFormat===D.cy){var A=I(T.data||[]);return!j.kJ((0,k.C4)(A))}}function I(T){for(var A=0;A<T.length&&T[A]==null;)A++;return T[A]}var m=W},33140:function(Ct,ot,v){"use strict";v.d(ot,{H:function(){return X},O:function(){return $}});var j=v(68540),Q=v(33051);function X(k,B){var u=k.mapDimensionsAll("defaultedLabel"),w=u.length;if(w===1){var O=(0,j.hk)(k,B,u[0]);return O!=null?O+"":null}else if(w){for(var E=[],Z=0;Z<u.length;Z++)E.push((0,j.hk)(k,B,u[Z]));return E.join(" ")}}function $(k,B){var u=k.mapDimensionsAll("defaultedLabel");if(!(0,Q.kJ)(B))return B+"";for(var w=[],O=0;O<u.length;O++){var E=k.getDimensionIndex(u[O]);E>=0&&w.push(B[E])}return w.join(" ")}},25559:function(Ct,ot,v){"use strict";var j=v(18299),Q=v(18490),X=v(33166),$={},k=function(B){(0,j.ZT)(u,B);function u(){var w=B!==null&&B.apply(this,arguments)||this;return w.type=u.type,w}return u.prototype.render=function(w,O,E,Z){this.axisPointerClass&&Q.iG(w),B.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(w,E,!0)},u.prototype.updateAxisPointer=function(w,O,E,Z){this._doUpdateAxisPointerClass(w,E,!1)},u.prototype.remove=function(w,O){var E=this._axisPointer;E&&E.remove(O)},u.prototype.dispose=function(w,O){this._disposeAxisPointer(O),B.prototype.dispose.apply(this,arguments)},u.prototype._doUpdateAxisPointerClass=function(w,O,E){var Z=u.getAxisPointerClass(this.axisPointerClass);if(!!Z){var Y=Q.np(w);Y?(this._axisPointer||(this._axisPointer=new Z)).render(w,Y,O,E):this._disposeAxisPointer(O)}},u.prototype._disposeAxisPointer=function(w){this._axisPointer&&this._axisPointer.dispose(w),this._axisPointer=null},u.registerAxisPointerClass=function(w,O){$[w]=O},u.getAxisPointerClass=function(w){return w&&$[w]},u.type="axis",u}(X.Z);ot.Z=k},92448:function(Ct,ot,v){"use strict";v.d(ot,{Z:function(){return X}});var j=v(33051),Q=v(32234);function X($,k){var B=[],u=$.seriesIndex,w;if(u==null||!(w=k.getSeriesByIndex(u)))return{point:[]};var O=w.getData(),E=Q.gO(O,$);if(E==null||E<0||j.kJ(E))return{point:[]};var Z=O.getItemGraphicEl(E),Y=w.coordinateSystem;if(w.getTooltipPosition)B=w.getTooltipPosition(E)||[];else if(Y&&Y.dataToPoint)if($.isStacked){var z=Y.getBaseAxis(),D=Y.getOtherAxis(z),P=D.dim,b=z.dim,W=P==="x"||P==="radius"?1:0,S=O.mapDimension(b),I=[];I[W]=O.get(S,E),I[1-W]=O.get(O.getCalculationInfo("stackResultDimension"),E),B=Y.dataToPoint(I)||[]}else B=Y.dataToPoint(O.getValues(j.UI(Y.dimensions,function(T){return O.mapDimension(T)}),E))||[];else if(Z){var m=Z.getBoundingRect().clone();m.applyTransform(Z.transform),B=[m.x+m.width/2,m.y+m.height/2]}return{point:B,el:Z}}},56996:function(Ct,ot,v){"use strict";v.d(ot,{z:function(){return B},E:function(){return Y}});var j=v(33051),Q=v(66387),X=v(32234),$=(0,X.Yf)(),k=j.S6;function B(z,D,P){if(!Q.Z.node){var b=D.getZr();$(b).records||($(b).records={}),u(b,D);var W=$(b).records[z]||($(b).records[z]={});W.handler=P}}function u(z,D){if($(z).initialized)return;$(z).initialized=!0,P("click",j.WA(E,"click")),P("mousemove",j.WA(E,"mousemove")),P("globalout",O);function P(b,W){z.on(b,function(S){var I=Z(D);k($(z).records,function(m){m&&W(m,S,I.dispatchAction)}),w(I.pendings,D)})}}function w(z,D){var P=z.showTip.length,b=z.hideTip.length,W;P?W=z.showTip[P-1]:b&&(W=z.hideTip[b-1]),W&&(W.dispatchAction=null,D.dispatchAction(W))}function O(z,D,P){z.handler("leave",null,P)}function E(z,D,P,b){D.handler(z,P,b)}function Z(z){var D={showTip:[],hideTip:[]},P=function(b){var W=D[b.type];W?W.push(b):(b.dispatchAction=P,z.dispatchAction(b))};return{dispatchAction:P,pendings:D}}function Y(z,D){if(!Q.Z.node){var P=D.getZr(),b=($(P).records||{})[z];b&&($(P).records[z]=null)}}},47649:function(Ct,ot,v){"use strict";v.d(ot,{N:function(){return Dt}});var j=v(25559),Q=v(18299),X=v(33051),$=v(50453),k=v(38154),B=v(9074),u=v(44292),w=v(18490),O=v(61158),E=v(270),Z=v(32234),Y=(0,Z.Yf)(),z=X.d9,D=X.ak,P=function(){function x(){this._dragging=!1,this.animationThreshold=15}return x.prototype.render=function(g,d,C,_){var L=d.get("value"),N=d.get("status");if(this._axisModel=g,this._axisPointerModel=d,this._api=C,!(!_&&this._lastValue===L&&this._lastStatus===N)){this._lastValue=L,this._lastStatus=N;var G=this._group,V=this._handle;if(!N||N==="hide"){G&&G.hide(),V&&V.hide();return}G&&G.show(),V&&V.show();var q={};this.makeElOption(q,L,g,d,C);var tt=q.graphicKey;tt!==this._lastGraphicKey&&this.clear(C),this._lastGraphicKey=tt;var c=this._moveAnimation=this.determineAnimation(g,d);if(!G)G=this._group=new k.Z,this.createPointerEl(G,q,g,d),this.createLabelEl(G,q,g,d),C.getZr().add(G);else{var l=X.WA(b,d,c);this.updatePointerEl(G,q,l),this.updateLabelEl(G,q,l,d)}m(G,d,!0),this._renderHandle(L)}},x.prototype.remove=function(g){this.clear(g)},x.prototype.dispose=function(g){this.clear(g)},x.prototype.determineAnimation=function(g,d){var C=d.get("animation"),_=g.axis,L=_.type==="category",N=d.get("snap");if(!N&&!L)return!1;if(C==="auto"||C==null){var G=this.animationThreshold;if(L&&_.getBandWidth()>G)return!0;if(N){var V=w.r(g).seriesDataCount,q=_.getExtent();return Math.abs(q[0]-q[1])/V>G}return!1}return C===!0},x.prototype.makeElOption=function(g,d,C,_,L){},x.prototype.createPointerEl=function(g,d,C,_){var L=d.pointer;if(L){var N=Y(g).pointerEl=new $[L.type](z(d.pointer));g.add(N)}},x.prototype.createLabelEl=function(g,d,C,_){if(d.label){var L=Y(g).labelEl=new B.ZP(z(d.label));g.add(L),S(L,_)}},x.prototype.updatePointerEl=function(g,d,C){var _=Y(g).pointerEl;_&&d.pointer&&(_.setStyle(d.pointer.style),C(_,{shape:d.pointer.shape}))},x.prototype.updateLabelEl=function(g,d,C,_){var L=Y(g).labelEl;L&&(L.setStyle(d.label.style),C(L,{x:d.label.x,y:d.label.y}),S(L,_))},x.prototype._renderHandle=function(g){if(!(this._dragging||!this.updateHandleTransform)){var d=this._axisPointerModel,C=this._api.getZr(),_=this._handle,L=d.getModel("handle"),N=d.get("status");if(!L.get("show")||!N||N==="hide"){_&&C.remove(_),this._handle=null;return}var G;this._handle||(G=!0,_=this._handle=$.createIcon(L.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(q){O.sT(q.event)},onmousedown:D(this._onHandleDragMove,this,0,0),drift:D(this._onHandleDragMove,this),ondragend:D(this._onHandleDragEnd,this)}),C.add(_)),m(_,d,!1),_.setStyle(L.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var V=L.get("size");X.kJ(V)||(V=[V,V]),_.scaleX=V[0]/2,_.scaleY=V[1]/2,E.T9(this,"_doDispatchAxisPointer",L.get("throttle")||0,"fixRate"),this._moveHandleToValue(g,G)}},x.prototype._moveHandleToValue=function(g,d){b(this._axisPointerModel,!d&&this._moveAnimation,this._handle,I(this.getHandleTransform(g,this._axisModel,this._axisPointerModel)))},x.prototype._onHandleDragMove=function(g,d){var C=this._handle;if(!!C){this._dragging=!0;var _=this.updateHandleTransform(I(C),[g,d],this._axisModel,this._axisPointerModel);this._payloadInfo=_,C.stopAnimation(),C.attr(I(_)),Y(C).lastProp=null,this._doDispatchAxisPointer()}},x.prototype._doDispatchAxisPointer=function(){var g=this._handle;if(!!g){var d=this._payloadInfo,C=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:d.cursorPoint[0],y:d.cursorPoint[1],tooltipOption:d.tooltipOption,axesInfo:[{axisDim:C.axis.dim,axisIndex:C.componentIndex}]})}},x.prototype._onHandleDragEnd=function(){this._dragging=!1;var g=this._handle;if(!!g){var d=this._axisPointerModel.get("value");this._moveHandleToValue(d),this._api.dispatchAction({type:"hideTip"})}},x.prototype.clear=function(g){this._lastValue=null,this._lastStatus=null;var d=g.getZr(),C=this._group,_=this._handle;d&&C&&(this._lastGraphicKey=null,C&&d.remove(C),_&&d.remove(_),this._group=null,this._handle=null,this._payloadInfo=null),E.ZH(this,"_doDispatchAxisPointer")},x.prototype.doClear=function(){},x.prototype.buildLabel=function(g,d,C){return C=C||0,{x:g[C],y:g[1-C],width:d[C],height:d[1-C]}},x}();function b(x,g,d,C){W(Y(d).lastProp,C)||(Y(d).lastProp=C,g?u.D(d,C,x):(d.stopAnimation(),d.attr(C)))}function W(x,g){if(X.Kn(x)&&X.Kn(g)){var d=!0;return X.S6(g,function(C,_){d=d&&W(x[_],C)}),!!d}else return x===g}function S(x,g){x[g.get(["label","show"])?"show":"hide"]()}function I(x){return{x:x.x||0,y:x.y||0,rotation:x.rotation||0}}function m(x,g,d){var C=g.get("z"),_=g.get("zlevel");x&&x.traverse(function(L){L.type!=="group"&&(C!=null&&(L.z=C),_!=null&&(L.zlevel=_),L.silent=d)})}var T=P,A=v(75539),R=v(49069),U=function(x){(0,Q.ZT)(g,x);function g(){return x!==null&&x.apply(this,arguments)||this}return g.prototype.makeElOption=function(d,C,_,L,N){var G=_.axis,V=G.grid,q=L.get("type"),tt=K(V,G).getOtherAxis(G).getGlobalExtent(),c=G.toGlobalCoord(G.dataToCoord(C,!0));if(q&&q!=="none"){var l=A.fk(L),s=J[q](G,c,tt);s.style=l,d.graphicKey=s.type,d.pointer=s}var e=R.bK(V.model,_);A.gf(C,d,e,_,L,N)},g.prototype.getHandleTransform=function(d,C,_){var L=R.bK(C.axis.grid.model,C,{labelInside:!1});L.labelMargin=_.get(["handle","margin"]);var N=A.Zh(C.axis,d,L);return{x:N[0],y:N[1],rotation:L.rotation+(L.labelDirection<0?Math.PI:0)}},g.prototype.updateHandleTransform=function(d,C,_,L){var N=_.axis,G=N.grid,V=N.getGlobalExtent(!0),q=K(G,N).getOtherAxis(N).getGlobalExtent(),tt=N.dim==="x"?0:1,c=[d.x,d.y];c[tt]+=C[tt],c[tt]=Math.min(V[1],c[tt]),c[tt]=Math.max(V[0],c[tt]);var l=(q[1]+q[0])/2,s=[l,l];s[tt]=c[tt];var e=[{verticalAlign:"middle"},{align:"center"}];return{x:c[0],y:c[1],rotation:d.rotation,cursorPoint:s,tooltipOption:e[tt]}},g}(T);function K(x,g){var d={};return d[g.dim+"AxisIndex"]=g.index,x.getCartesian(d)}var J={line:function(x,g,d){var C=A.BL([g,d[0]],[g,d[1]],nt(x));return{type:"Line",subPixelOptimize:!0,shape:C}},shadow:function(x,g,d){var C=Math.max(1,x.getBandWidth()),_=d[1]-d[0];return{type:"Rect",shape:A.uE([g-C/2,d[0]],[C,_],nt(x))}}};function nt(x){return x.dim==="x"?0:1}var ut=U,et=v(98071),ct=function(x){(0,Q.ZT)(g,x);function g(){var d=x!==null&&x.apply(this,arguments)||this;return d.type=g.type,d}return g.type="axisPointer",g.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},g}(et.Z),dt=ct,ht=v(56996),vt=v(33166),ft=function(x){(0,Q.ZT)(g,x);function g(){var d=x!==null&&x.apply(this,arguments)||this;return d.type=g.type,d}return g.prototype.render=function(d,C,_){var L=C.getComponent("tooltip"),N=d.get("triggerOn")||L&&L.get("triggerOn")||"mousemove|click";ht.z("axisPointer",_,function(G,V,q){N!=="none"&&(G==="leave"||N.indexOf(G)>=0)&&q({type:"updateAxisPointer",currTrigger:G,x:V&&V.offsetX,y:V&&V.offsetY})})},g.prototype.remove=function(d,C){ht.E("axisPointer",C)},g.prototype.dispose=function(d,C){ht.E("axisPointer",C)},g.type="axisPointer",g}(vt.Z),At=ft,mt=v(92448),yt=(0,Z.Yf)();function Mt(x,g,d){var C=x.currTrigger,_=[x.x,x.y],L=x,N=x.dispatchAction||(0,X.ak)(d.dispatchAction,d),G=g.getComponent("axisPointer").coordSysAxesInfo;if(!!G){wt(_)&&(_=(0,mt.Z)({seriesIndex:L.seriesIndex,dataIndex:L.dataIndex},g).point);var V=wt(_),q=L.axesInfo,tt=G.axesInfo,c=C==="leave"||wt(_),l={},s={},e={list:[],map:{}},t={showPointer:(0,X.WA)(Tt,s),showTooltip:(0,X.WA)(Pt,e)};(0,X.S6)(G.coordSysMap,function(n,r){var o=V||n.containPoint(_);(0,X.S6)(G.coordSysAxesInfo[r],function(a,h){var f=a.axis,p=Ot(q,a);if(!c&&o&&(!q||p)){var y=p&&p.value;y==null&&!V&&(y=f.pointToData(_)),y!=null&&St(a,y,t,!1,l)}})});var i={};return(0,X.S6)(tt,function(n,r){var o=n.linkGroup;o&&!s[r]&&(0,X.S6)(o.axesInfo,function(a,h){var f=s[h];if(a!==n&&f){var p=f.value;o.mapper&&(p=n.axis.scale.parse(o.mapper(p,Bt(a),Bt(n)))),i[n.key]=p}})}),(0,X.S6)(i,function(n,r){St(tt[r],n,t,!0,l)}),Lt(s,tt,l),Ht(e,_,x,N),bt(tt,N,d),l}}function St(x,g,d,C,_){var L=x.axis;if(!(L.scale.isBlank()||!L.containData(g))){if(!x.involveSeries){d.showPointer(x,g);return}var N=pt(g,x),G=N.payloadBatch,V=N.snapToValue;G[0]&&_.seriesIndex==null&&(0,X.l7)(_,G[0]),!C&&x.snap&&L.containData(V)&&V!=null&&(g=V),d.showPointer(x,g,G),d.showTooltip(x,N,V)}}function pt(x,g){var d=g.axis,C=d.dim,_=x,L=[],N=Number.MAX_VALUE,G=-1;return(0,X.S6)(g.seriesModels,function(V,q){var tt=V.getData().mapDimensionsAll(C),c,l;if(V.getAxisTooltipData){var s=V.getAxisTooltipData(tt,x,d);l=s.dataIndices,c=s.nestestValue}else{if(l=V.getData().indicesOfNearest(tt[0],x,d.type==="category"?.5:null),!l.length)return;c=V.getData().get(tt[0],l[0])}if(!(c==null||!isFinite(c))){var e=x-c,t=Math.abs(e);t<=N&&((t<N||e>=0&&G<0)&&(N=t,G=e,_=c,L.length=0),(0,X.S6)(l,function(i){L.push({seriesIndex:V.seriesIndex,dataIndexInside:i,dataIndex:V.getData().getRawIndex(i)})}))}}),{payloadBatch:L,snapToValue:_}}function Tt(x,g,d,C){x[g.key]={value:d,payloadBatch:C}}function Pt(x,g,d,C){var _=d.payloadBatch,L=g.axis,N=L.model,G=g.axisPointerModel;if(!(!g.triggerTooltip||!_.length)){var V=g.coordSys.model,q=w.zm(V),tt=x.map[q];tt||(tt=x.map[q]={coordSysId:V.id,coordSysIndex:V.componentIndex,coordSysType:V.type,coordSysMainType:V.mainType,dataByAxis:[]},x.list.push(tt)),tt.dataByAxis.push({axisDim:L.dim,axisIndex:N.componentIndex,axisType:N.type,axisId:N.id,value:C,valueLabelOpt:{precision:G.get(["label","precision"]),formatter:G.get(["label","formatter"])},seriesDataIndices:_.slice()})}}function Lt(x,g,d){var C=d.axesInfo=[];(0,X.S6)(g,function(_,L){var N=_.axisPointerModel.option,G=x[L];G?(!_.useHandle&&(N.status="show"),N.value=G.value,N.seriesDataIndices=(G.payloadBatch||[]).slice()):!_.useHandle&&(N.status="hide"),N.status==="show"&&C.push({axisDim:_.axis.dim,axisIndex:_.axis.model.componentIndex,value:N.value})})}function Ht(x,g,d,C){if(wt(g)||!x.list.length){C({type:"hideTip"});return}var _=((x.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};C({type:"showTip",escapeConnect:!0,x:g[0],y:g[1],tooltipOption:d.tooltipOption,position:d.position,dataIndexInside:_.dataIndexInside,dataIndex:_.dataIndex,seriesIndex:_.seriesIndex,dataByCoordSys:x.list})}function bt(x,g,d){var C=d.getZr(),_="axisPointerLastHighlights",L=yt(C)[_]||{},N=yt(C)[_]={};(0,X.S6)(x,function(q,tt){var c=q.axisPointerModel.option;c.status==="show"&&q.triggerEmphasis&&(0,X.S6)(c.seriesDataIndices,function(l){var s=l.seriesIndex+" | "+l.dataIndex;N[s]=l})});var G=[],V=[];(0,X.S6)(L,function(q,tt){!N[tt]&&V.push(q)}),(0,X.S6)(N,function(q,tt){!L[tt]&&G.push(q)}),V.length&&d.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:V}),G.length&&d.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:G})}function Ot(x,g){for(var d=0;d<(x||[]).length;d++){var C=x[d];if(g.axis.dim===C.axisDim&&g.axis.model.componentIndex===C.axisIndex)return C}}function Bt(x){var g=x.axis.model,d={},C=d.axisDim=x.axis.dim;return d.axisIndex=d[C+"AxisIndex"]=g.componentIndex,d.axisName=d[C+"AxisName"]=g.name,d.axisId=d[C+"AxisId"]=g.id,d}function wt(x){return!x||x[0]==null||isNaN(x[0])||x[1]==null||isNaN(x[1])}function Dt(x){j.Z.registerAxisPointerClass("CartesianAxisPointer",ut),x.registerComponentModel(dt),x.registerComponentView(At),x.registerPreprocessor(function(g){if(g){(!g.axisPointer||g.axisPointer.length===0)&&(g.axisPointer={});var d=g.axisPointer.link;d&&!(0,X.kJ)(d)&&(g.axisPointer.link=[d])}}),x.registerProcessor(x.PRIORITY.PROCESSOR.STATISTIC,function(g,d){g.getComponent("axisPointer").coordSysAxesInfo=(0,w.KM)(g,d)}),x.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},Mt)}},18490:function(Ct,ot,v){"use strict";v.d(ot,{KM:function(){return X},iG:function(){return O},r:function(){return E},np:function(){return Z},zm:function(){return z}});var j=v(1497),Q=v(33051);function X(D,P){var b={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return $(b,D,P),b.seriesInvolved&&B(b,D),b}function $(D,P,b){var W=P.getComponent("tooltip"),S=P.getComponent("axisPointer"),I=S.get("link",!0)||[],m=[];(0,Q.S6)(b.getCoordinateSystems(),function(T){if(!T.axisPointerEnabled)return;var A=z(T.model),R=D.coordSysAxesInfo[A]={};D.coordSysMap[A]=T;var U=T.model,K=U.getModel("tooltip",W);if((0,Q.S6)(T.getAxes(),(0,Q.WA)(et,!1,null)),T.getTooltipAxes&&W&&K.get("show")){var J=K.get("trigger")==="axis",nt=K.get(["axisPointer","type"])==="cross",ut=T.getTooltipAxes(K.get(["axisPointer","axis"]));(J||nt)&&(0,Q.S6)(ut.baseAxes,(0,Q.WA)(et,nt?"cross":!0,J)),nt&&(0,Q.S6)(ut.otherAxes,(0,Q.WA)(et,"cross",!1))}function et(ct,dt,ht){var vt=ht.model.getModel("axisPointer",S),ft=vt.get("show");if(!(!ft||ft==="auto"&&!ct&&!Y(vt))){dt==null&&(dt=vt.get("triggerTooltip")),vt=ct?k(ht,K,S,P,ct,dt):vt;var At=vt.get("snap"),mt=vt.get("triggerEmphasis"),yt=z(ht.model),Mt=dt||At||ht.type==="category",St=D.axesInfo[yt]={key:yt,axis:ht,coordSys:T,axisPointerModel:vt,triggerTooltip:dt,triggerEmphasis:mt,involveSeries:Mt,snap:At,useHandle:Y(vt),seriesModels:[],linkGroup:null};R[yt]=St,D.seriesInvolved=D.seriesInvolved||Mt;var pt=u(I,ht);if(pt!=null){var Tt=m[pt]||(m[pt]={axesInfo:{}});Tt.axesInfo[yt]=St,Tt.mapper=I[pt].mapper,St.linkGroup=Tt}}}})}function k(D,P,b,W,S,I){var m=P.getModel("axisPointer"),T=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],A={};(0,Q.S6)(T,function(J){A[J]=(0,Q.d9)(m.get(J))}),A.snap=D.type!=="category"&&!!I,m.get("type")==="cross"&&(A.type="line");var R=A.label||(A.label={});if(R.show==null&&(R.show=!1),S==="cross"){var U=m.get(["label","show"]);if(R.show=U!=null?U:!0,!I){var K=A.lineStyle=m.get("crossStyle");K&&(0,Q.ce)(R,K.textStyle)}}return D.model.getModel("axisPointer",new j.Z(A,b,W))}function B(D,P){P.eachSeries(function(b){var W=b.coordinateSystem,S=b.get(["tooltip","trigger"],!0),I=b.get(["tooltip","show"],!0);!W||S==="none"||S===!1||S==="item"||I===!1||b.get(["axisPointer","show"],!0)===!1||(0,Q.S6)(D.coordSysAxesInfo[z(W.model)],function(m){var T=m.axis;W.getAxis(T.dim)===T&&(m.seriesModels.push(b),m.seriesDataCount==null&&(m.seriesDataCount=0),m.seriesDataCount+=b.getData().count())})})}function u(D,P){for(var b=P.model,W=P.dim,S=0;S<D.length;S++){var I=D[S]||{};if(w(I[W+"AxisId"],b.id)||w(I[W+"AxisIndex"],b.componentIndex)||w(I[W+"AxisName"],b.name))return S}}function w(D,P){return D==="all"||(0,Q.kJ)(D)&&(0,Q.cq)(D,P)>=0||D===P}function O(D){var P=E(D);if(!!P){var b=P.axisPointerModel,W=P.axis.scale,S=b.option,I=b.get("status"),m=b.get("value");m!=null&&(m=W.parse(m));var T=Y(b);I==null&&(S.status=T?"show":"hide");var A=W.getExtent().slice();A[0]>A[1]&&A.reverse(),(m==null||m>A[1])&&(m=A[1]),m<A[0]&&(m=A[0]),S.value=m,T&&(S.status=P.axis.scale.isBlank()?"hide":"show")}}function E(D){var P=(D.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return P&&P.axesInfo[z(D)]}function Z(D){var P=E(D);return P&&P.axisPointerModel}function Y(D){return!!D.get(["handle","show"])}function z(D){return D.type+"||"+D.id}},75539:function(Ct,ot,v){"use strict";v.d(ot,{fk:function(){return O},gk:function(){return Y},Zh:function(){return z},gf:function(){return D},BL:function(){return P},uE:function(){return b}});var j=v(33051),Q=v(50453),X=v(80423),$=v(78988),k=v(32892),B=v(88199),u=v(58608),w=v(36006);function O(S){var I=S.get("type"),m=S.getModel(I+"Style"),T;return I==="line"?(T=m.getLineStyle(),T.fill=null):I==="shadow"&&(T=m.getAreaStyle(),T.stroke=null),T}function E(S,I,m,T,A){var R=m.get("value"),U=Y(R,I.axis,I.ecModel,m.get("seriesDataIndices"),{precision:m.get(["label","precision"]),formatter:m.get(["label","formatter"])}),K=m.getModel("label"),J=$.MY(K.get("padding")||0),nt=K.getFont(),ut=X.lP(U,nt),et=A.position,ct=ut.width+J[1]+J[3],dt=ut.height+J[0]+J[2],ht=A.align;ht==="right"&&(et[0]-=ct),ht==="center"&&(et[0]-=ct/2);var vt=A.verticalAlign;vt==="bottom"&&(et[1]-=dt),vt==="middle"&&(et[1]-=dt/2),Z(et,ct,dt,T);var ft=K.get("backgroundColor");(!ft||ft==="auto")&&(ft=I.get(["axisLine","lineStyle","color"])),S.label={x:et[0],y:et[1],style:(0,w.Lr)(K,{text:U,font:nt,fill:K.getTextColor(),padding:J,backgroundColor:ft}),z2:10}}function Z(S,I,m,T){var A=T.getWidth(),R=T.getHeight();S[0]=Math.min(S[0]+I,A)-I,S[1]=Math.min(S[1]+m,R)-m,S[0]=Math.max(S[0],0),S[1]=Math.max(S[1],0)}function Y(S,I,m,T,A){S=I.scale.parse(S);var R=I.scale.getLabel({value:S},{precision:A.precision}),U=A.formatter;if(U){var K={value:B.DX(I,{value:S}),axisDimension:I.dim,axisIndex:I.index,seriesData:[]};j.S6(T,function(J){var nt=m.getSeriesByIndex(J.seriesIndex),ut=J.dataIndexInside,et=nt&&nt.getDataParams(ut);et&&K.seriesData.push(et)}),j.HD(U)?R=U.replace("{value}",R):j.mf(U)&&(R=U(K))}return R}function z(S,I,m){var T=k.Ue();return k.U1(T,T,m.rotation),k.Iu(T,T,m.position),Q.applyTransform([S.dataToCoord(I),(m.labelOffset||0)+(m.labelDirection||1)*(m.labelMargin||0)],T)}function D(S,I,m,T,A,R){var U=u.Z.innerTextLayout(m.rotation,0,m.labelDirection);m.labelMargin=A.get(["label","margin"]),E(I,T,A,R,{position:z(T.axis,S,m),align:U.textAlign,verticalAlign:U.textVerticalAlign})}function P(S,I,m){return m=m||0,{x1:S[m],y1:S[1-m],x2:I[m],y2:I[1-m]}}function b(S,I,m){return m=m||0,{x:S[m],y:S[1-m],width:I[m],height:I[1-m]}}function W(S,I,m,T,A,R){return{cx:S,cy:I,r0:m,r:T,startAngle:A,endAngle:R,clockwise:!0}}},73761:function(Ct,ot,v){"use strict";v.d(ot,{N:function(){return tt}});var j=v(18299),Q=v(33166),X=v(98071),$=function(c){(0,j.ZT)(l,c);function l(){return c!==null&&c.apply(this,arguments)||this}return l.type="grid",l.dependencies=["xAxis","yAxis"],l.layoutMode="box",l.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},l}(X.Z),k=$,B=v(35151),u=v(33051),w=v(16650),O=v(32234),E=function(c){(0,j.ZT)(l,c);function l(){return c!==null&&c.apply(this,arguments)||this}return l.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",O.C6).models[0]},l.type="cartesian2dAxis",l}(X.Z);u.jB(E,w.W);var Z=null,Y=v(66484),z=v(76172),D=v(51401),P={value:1,category:1,time:1,log:1};function b(c,l,s,e){(0,u.S6)(P,function(t,i){var n=(0,u.TS)((0,u.TS)({},Y.Z[i],!0),e,!0),r=function(o){(0,j.ZT)(a,o);function a(){var h=o!==null&&o.apply(this,arguments)||this;return h.type=l+"Axis."+i,h}return a.prototype.mergeDefaultAndTheme=function(h,f){var p=(0,z.YD)(this),y=p?(0,z.tE)(h):{},M=f.getTheme();(0,u.TS)(h,M.get(i+"Axis")),(0,u.TS)(h,this.getDefaultOption()),h.type=W(h),p&&(0,z.dt)(h,y,p)},a.prototype.optionUpdated=function(){var h=this.option;h.type==="category"&&(this.__ordinalMeta=D.Z.createByAxisModel(this))},a.prototype.getCategories=function(h){var f=this.option;if(f.type==="category")return h?f.data:this.__ordinalMeta.categories},a.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},a.type=l+"Axis."+i,a.defaultOption=n,a}(s);c.registerComponentModel(r)}),c.registerSubTypeDefaulter(l+"Axis",W)}function W(c){return c.type||(c.data?"category":"value")}var S=v(88199),I=v(60479),m=function(){function c(l){this.type="cartesian",this._dimList=[],this._axes={},this.name=l||""}return c.prototype.getAxis=function(l){return this._axes[l]},c.prototype.getAxes=function(){return u.UI(this._dimList,function(l){return this._axes[l]},this)},c.prototype.getAxesByScale=function(l){return l=l.toLowerCase(),u.hX(this.getAxes(),function(s){return s.scale.type===l})},c.prototype.addAxis=function(l){var s=l.dim;this._axes[s]=l,this._dimList.push(s)},c}(),T=m,A=v(32892),R=v(45280),U=["x","y"];function K(c){return c.type==="interval"||c.type==="time"}var J=function(c){(0,j.ZT)(l,c);function l(){var s=c!==null&&c.apply(this,arguments)||this;return s.type="cartesian2d",s.dimensions=U,s}return l.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var s=this.getAxis("x").scale,e=this.getAxis("y").scale;if(!(!K(s)||!K(e))){var t=s.getExtent(),i=e.getExtent(),n=this.dataToPoint([t[0],i[0]]),r=this.dataToPoint([t[1],i[1]]),o=t[1]-t[0],a=i[1]-i[0];if(!(!o||!a)){var h=(r[0]-n[0])/o,f=(r[1]-n[1])/a,p=n[0]-t[0]*h,y=n[1]-i[0]*f,M=this._transform=[h,0,0,f,p,y];this._invTransform=(0,A.U_)([],M)}}},l.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},l.prototype.containPoint=function(s){var e=this.getAxis("x"),t=this.getAxis("y");return e.contain(e.toLocalCoord(s[0]))&&t.contain(t.toLocalCoord(s[1]))},l.prototype.containData=function(s){return this.getAxis("x").containData(s[0])&&this.getAxis("y").containData(s[1])},l.prototype.containZone=function(s,e){var t=this.dataToPoint(s),i=this.dataToPoint(e),n=this.getArea(),r=new I.Z(t[0],t[1],i[0]-t[0],i[1]-t[1]);return n.intersect(r)},l.prototype.dataToPoint=function(s,e,t){t=t||[];var i=s[0],n=s[1];if(this._transform&&i!=null&&isFinite(i)&&n!=null&&isFinite(n))return(0,R.Ne)(t,s,this._transform);var r=this.getAxis("x"),o=this.getAxis("y");return t[0]=r.toGlobalCoord(r.dataToCoord(i,e)),t[1]=o.toGlobalCoord(o.dataToCoord(n,e)),t},l.prototype.clampData=function(s,e){var t=this.getAxis("x").scale,i=this.getAxis("y").scale,n=t.getExtent(),r=i.getExtent(),o=t.parse(s[0]),a=i.parse(s[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(n[0],n[1]),o),Math.max(n[0],n[1])),e[1]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e},l.prototype.pointToData=function(s,e){var t=[];if(this._invTransform)return(0,R.Ne)(t,s,this._invTransform);var i=this.getAxis("x"),n=this.getAxis("y");return t[0]=i.coordToData(i.toLocalCoord(s[0]),e),t[1]=n.coordToData(n.toLocalCoord(s[1]),e),t},l.prototype.getOtherAxis=function(s){return this.getAxis(s.dim==="x"?"y":"x")},l.prototype.getArea=function(s){s=s||0;var e=this.getAxis("x").getGlobalExtent(),t=this.getAxis("y").getGlobalExtent(),i=Math.min(e[0],e[1])-s,n=Math.min(t[0],t[1])-s,r=Math.max(e[0],e[1])-i+s,o=Math.max(t[0],t[1])-n+s;return new I.Z(i,n,r,o)},l}(T),nt=J,ut=v(12950),et=function(c){(0,j.ZT)(l,c);function l(s,e,t,i,n){var r=c.call(this,s,e,t)||this;return r.index=0,r.type=i||"value",r.position=n||"bottom",r}return l.prototype.isHorizontal=function(){var s=this.position;return s==="top"||s==="bottom"},l.prototype.getGlobalExtent=function(s){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),s&&e[0]>e[1]&&e.reverse(),e},l.prototype.pointToData=function(s,e){return this.coordToData(this.toLocalCoord(s[this.dim==="x"?0:1]),e)},l.prototype.setCategorySortInfo=function(s){if(this.type!=="category")return!1;this.model.option.categorySortInfo=s,this.scale.setSortInfo(s)},l}(ut.Z),ct=et,dt=v(49069),ht=v(65021),vt=v(28259),ft=function(){function c(l,s,e){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=U,this._initCartesian(l,s,e),this.model=l}return c.prototype.getRect=function(){return this._rect},c.prototype.update=function(l,s){var e=this._axesMap;this._updateScale(l,this.model);function t(n){var r,o=(0,u.XP)(n),a=o.length;if(!!a){for(var h=[],f=a-1;f>=0;f--){var p=+o[f],y=n[p],M=y.model,H=y.scale;(0,ht.lM)(H)&&M.get("alignTicks")&&M.get("interval")==null?h.push(y):((0,S.Jk)(H,M),(0,ht.lM)(H)&&(r=y))}h.length&&(r||(r=h.pop(),(0,S.Jk)(r.scale,r.model)),(0,u.S6)(h,function(F){(0,vt.z)(F.scale,F.model,r.scale)}))}}t(e.x),t(e.y);var i={};(0,u.S6)(e.x,function(n){mt(e,"y",n,i)}),(0,u.S6)(e.y,function(n){mt(e,"x",n,i)}),this.resize(this.model,s)},c.prototype.resize=function(l,s,e){var t=l.getBoxLayoutParams(),i=!e&&l.get("containLabel"),n=(0,z.ME)(t,{width:s.getWidth(),height:s.getHeight()});this._rect=n;var r=this._axesList;o(),i&&((0,u.S6)(r,function(a){if(!a.model.get(["axisLabel","inside"])){var h=(0,S.Do)(a);if(h){var f=a.isHorizontal()?"height":"width",p=a.model.get(["axisLabel","margin"]);n[f]-=h[f]+p,a.position==="top"?n.y+=h.height+p:a.position==="left"&&(n.x+=h.width+p)}}}),o()),(0,u.S6)(this._coordsList,function(a){a.calcAffineTransform()});function o(){(0,u.S6)(r,function(a){var h=a.isHorizontal(),f=h?[0,n.width]:[0,n.height],p=a.inverse?1:0;a.setExtent(f[p],f[1-p]),Mt(a,h?n.x:n.y)})}},c.prototype.getAxis=function(l,s){var e=this._axesMap[l];if(e!=null)return e[s||0]},c.prototype.getAxes=function(){return this._axesList.slice()},c.prototype.getCartesian=function(l,s){if(l!=null&&s!=null){var e="x"+l+"y"+s;return this._coordsMap[e]}(0,u.Kn)(l)&&(s=l.yAxisIndex,l=l.xAxisIndex);for(var t=0,i=this._coordsList;t<i.length;t++)if(i[t].getAxis("x").index===l||i[t].getAxis("y").index===s)return i[t]},c.prototype.getCartesians=function(){return this._coordsList.slice()},c.prototype.convertToPixel=function(l,s,e){var t=this._findConvertTarget(s);return t.cartesian?t.cartesian.dataToPoint(e):t.axis?t.axis.toGlobalCoord(t.axis.dataToCoord(e)):null},c.prototype.convertFromPixel=function(l,s,e){var t=this._findConvertTarget(s);return t.cartesian?t.cartesian.pointToData(e):t.axis?t.axis.coordToData(t.axis.toLocalCoord(e)):null},c.prototype._findConvertTarget=function(l){var s=l.seriesModel,e=l.xAxisModel||s&&s.getReferringComponents("xAxis",O.C6).models[0],t=l.yAxisModel||s&&s.getReferringComponents("yAxis",O.C6).models[0],i=l.gridModel,n=this._coordsList,r,o;if(s)r=s.coordinateSystem,(0,u.cq)(n,r)<0&&(r=null);else if(e&&t)r=this.getCartesian(e.componentIndex,t.componentIndex);else if(e)o=this.getAxis("x",e.componentIndex);else if(t)o=this.getAxis("y",t.componentIndex);else if(i){var a=i.coordinateSystem;a===this&&(r=this._coordsList[0])}return{cartesian:r,axis:o}},c.prototype.containPoint=function(l){var s=this._coordsList[0];if(s)return s.containPoint(l)},c.prototype._initCartesian=function(l,s,e){var t=this,i=this,n={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},o={x:0,y:0};if(s.eachComponent("xAxis",a("x"),this),s.eachComponent("yAxis",a("y"),this),!o.x||!o.y){this._axesMap={},this._axesList=[];return}this._axesMap=r,(0,u.S6)(r.x,function(h,f){(0,u.S6)(r.y,function(p,y){var M="x"+f+"y"+y,H=new nt(M);H.master=t,H.model=l,t._coordsMap[M]=H,t._coordsList.push(H),H.addAxis(h),H.addAxis(p)})});function a(h){return function(f,p){if(!!At(f,l)){var y=f.get("position");h==="x"?y!=="top"&&y!=="bottom"&&(y=n.bottom?"top":"bottom"):y!=="left"&&y!=="right"&&(y=n.left?"right":"left"),n[y]=!0;var M=new ct(h,(0,S.aG)(f),[0,0],f.get("type"),y),H=M.type==="category";M.onBand=H&&f.get("boundaryGap"),M.inverse=f.get("inverse"),f.axis=M,M.model=f,M.grid=i,M.index=p,i._axesList.push(M),r[h][p]=M,o[h]++}}}},c.prototype._updateScale=function(l,s){(0,u.S6)(this._axesList,function(t){if(t.scale.setExtent(Infinity,-Infinity),t.type==="category"){var i=t.model.get("categorySortInfo");t.scale.setSortInfo(i)}}),l.eachSeries(function(t){if((0,dt.Yh)(t)){var i=(0,dt.Mk)(t),n=i.xAxisModel,r=i.yAxisModel;if(!At(n,s)||!At(r,s))return;var o=this.getCartesian(n.componentIndex,r.componentIndex),a=t.getData(),h=o.getAxis("x"),f=o.getAxis("y");e(a,h),e(a,f)}},this);function e(t,i){(0,u.S6)((0,S.PY)(t,i.dim),function(n){i.scale.unionExtentFromData(t,n)})}},c.prototype.getTooltipAxes=function(l){var s=[],e=[];return(0,u.S6)(this.getCartesians(),function(t){var i=l!=null&&l!=="auto"?t.getAxis(l):t.getBaseAxis(),n=t.getOtherAxis(i);(0,u.cq)(s,i)<0&&s.push(i),(0,u.cq)(e,n)<0&&e.push(n)}),{baseAxes:s,otherAxes:e}},c.create=function(l,s){var e=[];return l.eachComponent("grid",function(t,i){var n=new c(t,l,s);n.name="grid_"+i,n.resize(t,s,!0),t.coordinateSystem=n,e.push(n)}),l.eachSeries(function(t){if(!!(0,dt.Yh)(t)){var i=(0,dt.Mk)(t),n=i.xAxisModel,r=i.yAxisModel,o=n.getCoordSysModel(),a=o.coordinateSystem;t.coordinateSystem=a.getCartesian(n.componentIndex,r.componentIndex)}}),e},c.dimensions=U,c}();function At(c,l){return c.getCoordSysModel()===l}function mt(c,l,s,e){s.getAxesOnZeroOf=function(){return i?[i]:[]};var t=c[l],i,n=s.model,r=n.get(["axisLine","onZero"]),o=n.get(["axisLine","onZeroAxisIndex"]);if(!r)return;if(o!=null)yt(t[o])&&(i=t[o]);else for(var a in t)if(t.hasOwnProperty(a)&&yt(t[a])&&!e[h(t[a])]){i=t[a];break}i&&(e[h(i)]=!0);function h(f){return f.dim+"_"+f.index}}function yt(c){return c&&c.type!=="category"&&c.type!=="time"&&(0,S.Yb)(c)}function Mt(c,l){var s=c.getExtent(),e=s[0]+s[1];c.toGlobalCoord=c.dim==="x"?function(t){return t+l}:function(t){return e-t+l},c.toLocalCoord=c.dim==="x"?function(t){return t-l}:function(t){return e-t+l}}var St=ft,pt=v(50453),Tt=v(38154),Pt=v(22095),Lt=v(58608),Ht=v(25559),bt=(0,O.Yf)();function Ot(c,l,s,e){var t=s.axis;if(!t.scale.isBlank()){var i=s.getModel("splitArea"),n=i.getModel("areaStyle"),r=n.get("color"),o=e.coordinateSystem.getRect(),a=t.getTicksCoords({tickModel:i,clamp:!0});if(!!a.length){var h=r.length,f=bt(c).splitAreaColors,p=u.kW(),y=0;if(f)for(var M=0;M<a.length;M++){var H=f.get(a[M].tickValue);if(H!=null){y=(H+(h-1)*M)%h;break}}var F=t.toGlobalCoord(a[0].coord),at=n.getAreaStyle();r=u.kJ(r)?r:[r];for(var M=1;M<a.length;M++){var it=t.toGlobalCoord(a[M].coord),st=void 0,rt=void 0,gt=void 0,lt=void 0;t.isHorizontal()?(st=F,rt=o.y,gt=it-st,lt=o.height,F=st+gt):(st=o.x,rt=F,gt=o.width,lt=it-rt,F=rt+lt);var xt=a[M-1].tickValue;xt!=null&&p.set(xt,y),l.add(new B.Z({anid:xt!=null?"area_"+xt:null,shape:{x:st,y:rt,width:gt,height:lt},style:u.ce({fill:r[y]},at),autoBatch:!0,silent:!0})),y=(y+1)%h}bt(c).splitAreaColors=p}}}function Bt(c){bt(c).splitAreaColors=null}var wt=["axisLine","axisTickLabel","axisName"],Dt=["splitArea","splitLine","minorSplitLine"],x=function(c){(0,j.ZT)(l,c);function l(){var s=c!==null&&c.apply(this,arguments)||this;return s.type=l.type,s.axisPointerClass="CartesianAxisPointer",s}return l.prototype.render=function(s,e,t,i){this.group.removeAll();var n=this._axisGroup;if(this._axisGroup=new Tt.Z,this.group.add(this._axisGroup),!!s.get("show")){var r=s.getCoordSysModel(),o=dt.bK(r,s),a=new Lt.Z(s,u.l7({handleAutoShown:function(f){for(var p=r.coordinateSystem.getCartesians(),y=0;y<p.length;y++)if((0,ht.lM)(p[y].getOtherAxis(s.axis).scale))return!0;return!1}},o));u.S6(wt,a.add,a),this._axisGroup.add(a.getGroup()),u.S6(Dt,function(f){s.get([f,"show"])&&g[f](this,this._axisGroup,s,r)},this);var h=i&&i.type==="changeAxisOrder"&&i.isInitSort;h||pt.groupTransition(n,this._axisGroup,s),c.prototype.render.call(this,s,e,t,i)}},l.prototype.remove=function(){Bt(this)},l.type="cartesianAxis",l}(Ht.Z),g={splitLine:function(c,l,s,e){var t=s.axis;if(!t.scale.isBlank()){var i=s.getModel("splitLine"),n=i.getModel("lineStyle"),r=n.get("color");r=u.kJ(r)?r:[r];for(var o=e.coordinateSystem.getRect(),a=t.isHorizontal(),h=0,f=t.getTicksCoords({tickModel:i}),p=[],y=[],M=n.getLineStyle(),H=0;H<f.length;H++){var F=t.toGlobalCoord(f[H].coord);a?(p[0]=F,p[1]=o.y,y[0]=F,y[1]=o.y+o.height):(p[0]=o.x,p[1]=F,y[0]=o.x+o.width,y[1]=F);var at=h++%r.length,it=f[H].tickValue,st=new Pt.Z({anid:it!=null?"line_"+f[H].tickValue:null,autoBatch:!0,shape:{x1:p[0],y1:p[1],x2:y[0],y2:y[1]},style:u.ce({stroke:r[at]},M),silent:!0});pt.subPixelOptimizeLine(st.shape,M.lineWidth),l.add(st)}}},minorSplitLine:function(c,l,s,e){var t=s.axis,i=s.getModel("minorSplitLine"),n=i.getModel("lineStyle"),r=e.coordinateSystem.getRect(),o=t.isHorizontal(),a=t.getMinorTicksCoords();if(!!a.length)for(var h=[],f=[],p=n.getLineStyle(),y=0;y<a.length;y++)for(var M=0;M<a[y].length;M++){var H=t.toGlobalCoord(a[y][M].coord);o?(h[0]=H,h[1]=r.y,f[0]=H,f[1]=r.y+r.height):(h[0]=r.x,h[1]=H,f[0]=r.x+r.width,f[1]=H);var F=new Pt.Z({anid:"minor_line_"+a[y][M].tickValue,autoBatch:!0,shape:{x1:h[0],y1:h[1],x2:f[0],y2:f[1]},style:p,silent:!0});pt.subPixelOptimizeLine(F.shape,p.lineWidth),l.add(F)}},splitArea:function(c,l,s,e){Ot(c,l,s,e)}},d=function(c){(0,j.ZT)(l,c);function l(){var s=c!==null&&c.apply(this,arguments)||this;return s.type=l.type,s}return l.type="xAxis",l}(x),C=function(c){(0,j.ZT)(l,c);function l(){var s=c!==null&&c.apply(this,arguments)||this;return s.type=d.type,s}return l.type="yAxis",l}(x),_=null,L=function(c){(0,j.ZT)(l,c);function l(){var s=c!==null&&c.apply(this,arguments)||this;return s.type="grid",s}return l.prototype.render=function(s,e){this.group.removeAll(),s.get("show")&&this.group.add(new B.Z({shape:s.coordinateSystem.getRect(),style:(0,u.ce)({fill:s.get("backgroundColor")},s.getItemStyle()),silent:!0,z2:-1}))},l.type="grid",l}(Q.Z),N={offset:0};function G(c){c.registerComponentView(L),c.registerComponentModel(k),c.registerCoordinateSystem("cartesian2d",St),b(c,"x",E,N),b(c,"y",E,N),c.registerComponentView(d),c.registerComponentView(C),c.registerPreprocessor(function(l){l.xAxis&&l.yAxis&&!l.grid&&(l.grid={})})}var V=v(47649),q=v(68023);function tt(c){(0,q.D)(G),(0,q.D)(V.N)}},17813:function(Ct,ot,v){"use strict";v.d(ot,{N:function(){return s}});var j=v(47649),Q=v(68023),X=v(18299),$=v(98071),k=function(e){(0,X.ZT)(t,e);function t(){var i=e!==null&&e.apply(this,arguments)||this;return i.type=t.type,i}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}($.Z),B=k,u=v(33051),w=v(66387),O=v(61158),E=v(54058),Z=v(78988);function Y(e){var t=e.get("confine");return t!=null?!!t:e.get("renderMode")==="richText"}function z(e){if(!!w.Z.domSupported){for(var t=document.documentElement.style,i=0,n=e.length;i<n;i++)if(e[i]in t)return e[i]}}var D=z(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),P=z(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function b(e,t){if(!e)return t;t=(0,Z.zW)(t,!0);var i=e.indexOf(t);return e=i===-1?t:"-"+e.slice(0,i)+"-"+t,e.toLowerCase()}function W(e,t){var i=e.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(e);return i?t?i[t]:i:null}var S=v(5685),I=b(P,"transition"),m=b(D,"transform"),T="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(w.Z.transform3dSupported?"will-change:transform;":"");function A(e){return e=e==="left"?"right":e==="right"?"left":e==="top"?"bottom":"top",e}function R(e,t,i){if(!(0,u.HD)(i)||i==="inside")return"";var n=e.get("backgroundColor"),r=e.get("borderWidth");t=(0,Z.Lz)(t);var o=A(i),a=Math.max(Math.round(r)*1.5,6),h="",f=m+":",p;(0,u.cq)(["left","right"],o)>-1?(h+="top:50%",f+="translateY(-50%) rotate("+(p=o==="left"?-225:-45)+"deg)"):(h+="left:50%",f+="translateX(-50%) rotate("+(p=o==="top"?225:45)+"deg)");var y=p*Math.PI/180,M=a+r,H=M*Math.abs(Math.cos(y))+M*Math.abs(Math.sin(y)),F=Math.round(((H-Math.SQRT2*r)/2+Math.SQRT2*r-(H-M)/2)*100)/100;h+=";"+o+":-"+F+"px";var at=t+" solid "+r+"px;",it=["position:absolute;width:"+a+"px;height:"+a+"px;z-index:-1;",h+";"+f+";","border-bottom:"+at,"border-right:"+at,"background-color:"+n+";"];return'<div style="'+it.join("")+'"></div>'}function U(e,t){var i="cubic-bezier(0.23,1,0.32,1)",n=" "+e/2+"s "+i,r="opacity"+n+",visibility"+n;return t||(n=" "+e+"s "+i,r+=w.Z.transformSupported?","+m+n:",left"+n+",top"+n),I+":"+r}function K(e,t,i){var n=e.toFixed(0)+"px",r=t.toFixed(0)+"px";if(!w.Z.transformSupported)return i?"top:"+r+";left:"+n+";":[["top",r],["left",n]];var o=w.Z.transform3dSupported,a="translate"+(o?"3d":"")+"("+n+","+r+(o?",0":"")+")";return i?"top:0;left:0;"+m+":"+a+";":[["top",0],["left",0],[D,a]]}function J(e){var t=[],i=e.get("fontSize"),n=e.getTextColor();n&&t.push("color:"+n),t.push("font:"+e.getFont()),i&&t.push("line-height:"+Math.round(i*3/2)+"px");var r=e.get("textShadowColor"),o=e.get("textShadowBlur")||0,a=e.get("textShadowOffsetX")||0,h=e.get("textShadowOffsetY")||0;return r&&o&&t.push("text-shadow:"+a+"px "+h+"px "+o+"px "+r),(0,u.S6)(["decoration","align"],function(f){var p=e.get(f);p&&t.push("text-"+f+":"+p)}),t.join(";")}function nt(e,t,i){var n=[],r=e.get("transitionDuration"),o=e.get("backgroundColor"),a=e.get("shadowBlur"),h=e.get("shadowColor"),f=e.get("shadowOffsetX"),p=e.get("shadowOffsetY"),y=e.getModel("textStyle"),M=(0,S.d_)(e,"html"),H=f+"px "+p+"px "+a+"px "+h;return n.push("box-shadow:"+H),t&&r&&n.push(U(r,i)),o&&n.push("background-color:"+o),(0,u.S6)(["width","color","radius"],function(F){var at="border-"+F,it=(0,Z.zW)(at),st=e.get(it);st!=null&&n.push(at+":"+st+(F==="color"?"":"px"))}),n.push(J(y)),M!=null&&n.push("padding:"+(0,Z.MY)(M).join("px ")+"px"),n.join(";")+";"}function ut(e,t,i,n,r){var o=t&&t.painter;if(i){var a=o&&o.getViewportRoot();a&&(0,E.YB)(e,a,i,n,r)}else{e[0]=n,e[1]=r;var h=o&&o.getViewportRootOffset();h&&(e[0]+=h.offsetLeft,e[1]+=h.offsetTop)}e[2]=e[0]/t.getWidth(),e[3]=e[1]/t.getHeight()}var et=function(){function e(t,i){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,w.Z.wxa)return null;var n=document.createElement("div");n.domBelongToZr=!0,this.el=n;var r=this._zr=t.getZr(),o=i.appendTo,a=o&&((0,u.HD)(o)?document.querySelector(o):(0,u.Mh)(o)?o:(0,u.mf)(o)&&o(t.getDom()));ut(this._styleCoord,r,a,t.getWidth()/2,t.getHeight()/2),(a||t.getDom()).appendChild(n),this._api=t,this._container=a;var h=this;n.onmouseenter=function(){h._enterable&&(clearTimeout(h._hideTimeout),h._show=!0),h._inContent=!0},n.onmousemove=function(f){if(f=f||window.event,!h._enterable){var p=r.handler,y=r.painter.getViewportRoot();(0,O.OD)(y,f,!0),p.dispatch("mousemove",f)}},n.onmouseleave=function(){h._inContent=!1,h._enterable&&h._show&&h.hideLater(h._hideDelay)}}return e.prototype.update=function(t){if(!this._container){var i=this._api.getDom(),n=W(i,"position"),r=i.style;r.position!=="absolute"&&n!=="absolute"&&(r.position="relative")}var o=t.get("alwaysShowContent");o&&this._moveIfResized(),this._alwaysShowContent=o,this.el.className=t.get("className")||""},e.prototype.show=function(t,i){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,r=n.style,o=this._styleCoord;n.innerHTML?r.cssText=T+nt(t,!this._firstShow,this._longHide)+K(o[0],o[1],!0)+("border-color:"+(0,Z.Lz)(i)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):r.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},e.prototype.setContent=function(t,i,n,r,o){var a=this.el;if(t==null){a.innerHTML="";return}var h="";if((0,u.HD)(o)&&n.get("trigger")==="item"&&!Y(n)&&(h=R(n,r,o)),(0,u.HD)(t))a.innerHTML=t+h;else if(t){a.innerHTML="",(0,u.kJ)(t)||(t=[t]);for(var f=0;f<t.length;f++)(0,u.Mh)(t[f])&&t[f].parentNode!==a&&a.appendChild(t[f]);if(h&&a.childNodes.length){var p=document.createElement("div");p.innerHTML=h,a.appendChild(p)}}},e.prototype.setEnterable=function(t){this._enterable=t},e.prototype.getSize=function(){var t=this.el;return[t.offsetWidth,t.offsetHeight]},e.prototype.moveTo=function(t,i){var n=this._styleCoord;if(ut(n,this._zr,this._container,t,i),n[0]!=null&&n[1]!=null){var r=this.el.style,o=K(n[0],n[1]);(0,u.S6)(o,function(a){r[a[0]]=a[1]})}},e.prototype._moveIfResized=function(){var t=this._styleCoord[2],i=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),i*this._zr.getHeight())},e.prototype.hide=function(){var t=this,i=this.el.style;i.visibility="hidden",i.opacity="0",w.Z.transform3dSupported&&(i.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},e.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout((0,u.ak)(this.hide,this),t)):this.hide())},e.prototype.isShow=function(){return this._show},e.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},e}(),ct=et,dt=v(9074),ht=v(70175),vt=function(){function e(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),mt(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return e.prototype.update=function(t){var i=t.get("alwaysShowContent");i&&this._moveIfResized(),this._alwaysShowContent=i},e.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},e.prototype.setContent=function(t,i,n,r,o){var a=this;u.Kn(t)&&(0,ht._y)(""),this.el&&this._zr.remove(this.el);var h=n.getModel("textStyle");this.el=new dt.ZP({style:{rich:i.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:r,textShadowColor:h.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:(0,S.d_)(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),u.S6(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(p){a.el.style[p]=n.get(p)}),u.S6(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(p){a.el.style[p]=h.get(p)||0}),this._zr.add(this.el);var f=this;this.el.on("mouseover",function(){f._enterable&&(clearTimeout(f._hideTimeout),f._show=!0),f._inContent=!0}),this.el.on("mouseout",function(){f._enterable&&f._show&&f.hideLater(f._hideDelay),f._inContent=!1})},e.prototype.setEnterable=function(t){this._enterable=t},e.prototype.getSize=function(){var t=this.el,i=this.el.getBoundingRect(),n=At(t.style);return[i.width+n.left+n.right,i.height+n.top+n.bottom]},e.prototype.moveTo=function(t,i){var n=this.el;if(n){var r=this._styleCoord;mt(r,this._zr,t,i),t=r[0],i=r[1];var o=n.style,a=ft(o.borderWidth||0),h=At(o);n.x=t+a+h.left,n.y=i+a+h.top,n.markRedraw()}},e.prototype._moveIfResized=function(){var t=this._styleCoord[2],i=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),i*this._zr.getHeight())},e.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},e.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(u.ak(this.hide,this),t)):this.hide())},e.prototype.isShow=function(){return this._show},e.prototype.dispose=function(){this._zr.remove(this.el)},e}();function ft(e){return Math.max(0,e)}function At(e){var t=ft(e.shadowBlur||0),i=ft(e.shadowOffsetX||0),n=ft(e.shadowOffsetY||0);return{left:ft(t-i),right:ft(t+i),top:ft(t-n),bottom:ft(t+n)}}function mt(e,t,i,n){e[0]=i,e[1]=n,e[2]=e[0]/t.getWidth(),e[3]=e[1]/t.getHeight()}var yt=vt,Mt=v(85669),St=v(35151),pt=v(92448),Tt=v(76172),Pt=v(1497),Lt=v(56996),Ht=v(88199),bt=v(75539),Ot=v(32234),Bt=v(33166),wt=v(15015),Dt=v(30106),x=v(61219),g=v(18310),d=v(270),C=new St.Z({shape:{x:-1,y:-1,width:2,height:2}}),_=function(e){(0,X.ZT)(t,e);function t(){var i=e!==null&&e.apply(this,arguments)||this;return i.type=t.type,i}return t.prototype.init=function(i,n){if(!(w.Z.node||!n.getDom())){var r=i.getComponent("tooltip"),o=this._renderMode=(0,Ot.U9)(r.get("renderMode"));this._tooltipContent=o==="richText"?new yt(n):new ct(n,{appendTo:r.get("appendToBody",!0)?"body":r.get("appendTo",!0)})}},t.prototype.render=function(i,n,r){if(!(w.Z.node||!r.getDom())){this.group.removeAll(),this._tooltipModel=i,this._ecModel=n,this._api=r;var o=this._tooltipContent;o.update(i),o.setEnterable(i.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&i.get("transitionDuration")?(0,d.T9)(this,"_updatePosition",50,"fixRate"):(0,d.ZH)(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var i=this._tooltipModel,n=i.get("triggerOn");Lt.z("itemTooltip",this._api,(0,u.ak)(function(r,o,a){n!=="none"&&(n.indexOf(r)>=0?this._tryShow(o,a):r==="leave"&&this._hide(a))},this))},t.prototype._keepShow=function(){var i=this._tooltipModel,n=this._ecModel,r=this._api,o=i.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&o!=="none"&&o!=="click"){var a=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!r.isDisposed()&&a.manuallyShowTip(i,n,r,{x:a._lastX,y:a._lastY,dataByCoordSys:a._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(i,n,r,o){if(!(o.from===this.uid||w.Z.node||!r.getDom())){var a=N(o,r);this._ticket="";var h=o.dataByCoordSys,f=c(o,n,r);if(f){var p=f.el.getBoundingRect().clone();p.applyTransform(f.el.transform),this._tryShow({offsetX:p.x+p.width/2,offsetY:p.y+p.height/2,target:f.el,position:o.position,positionDefault:"bottom"},a)}else if(o.tooltip&&o.x!=null&&o.y!=null){var y=C;y.x=o.x,y.y=o.y,y.update(),(0,Dt.A)(y).tooltipConfig={name:null,option:o.tooltip},this._tryShow({offsetX:o.x,offsetY:o.y,target:y},a)}else if(h)this._tryShow({offsetX:o.x,offsetY:o.y,position:o.position,dataByCoordSys:h,tooltipOption:o.tooltipOption},a);else if(o.seriesIndex!=null){if(this._manuallyAxisShowTip(i,n,r,o))return;var M=(0,pt.Z)(o,n),H=M.point[0],F=M.point[1];H!=null&&F!=null&&this._tryShow({offsetX:H,offsetY:F,target:M.el,position:o.position,positionDefault:"bottom"},a)}else o.x!=null&&o.y!=null&&(r.dispatchAction({type:"updateAxisPointer",x:o.x,y:o.y}),this._tryShow({offsetX:o.x,offsetY:o.y,position:o.position,target:r.getZr().findHover(o.x,o.y).target},a))}},t.prototype.manuallyHideTip=function(i,n,r,o){var a=this._tooltipContent;this._tooltipModel&&a.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,o.from!==this.uid&&this._hide(N(o,r))},t.prototype._manuallyAxisShowTip=function(i,n,r,o){var a=o.seriesIndex,h=o.dataIndex,f=n.getComponent("axisPointer").coordSysAxesInfo;if(!(a==null||h==null||f==null)){var p=n.getSeriesByIndex(a);if(!!p){var y=p.getData(),M=L([y.getItemModel(h),p,(p.coordinateSystem||{}).model],this._tooltipModel);if(M.get("trigger")==="axis")return r.dispatchAction({type:"updateAxisPointer",seriesIndex:a,dataIndex:h,position:o.position}),!0}}},t.prototype._tryShow=function(i,n){var r=i.target,o=this._tooltipModel;if(!!o){this._lastX=i.offsetX,this._lastY=i.offsetY;var a=i.dataByCoordSys;if(a&&a.length)this._showAxisTooltip(a,i);else if(r){var h=(0,Dt.A)(r);if(h.ssrType==="legend")return;this._lastDataByCoordSys=null;var f,p;(0,g.o)(r,function(y){if((0,Dt.A)(y).dataIndex!=null)return f=y,!0;if((0,Dt.A)(y).tooltipConfig!=null)return p=y,!0},!0),f?this._showSeriesItemTooltip(i,f,n):p?this._showComponentItemTooltip(i,p,n):this._hide(n)}else this._lastDataByCoordSys=null,this._hide(n)}},t.prototype._showOrMove=function(i,n){var r=i.get("showDelay");n=(0,u.ak)(n,this),clearTimeout(this._showTimout),r>0?this._showTimout=setTimeout(n,r):n()},t.prototype._showAxisTooltip=function(i,n){var r=this._ecModel,o=this._tooltipModel,a=[n.offsetX,n.offsetY],h=L([n.tooltipOption],o),f=this._renderMode,p=[],y=(0,S.TX)("section",{blocks:[],noHeader:!0}),M=[],H=new S.iv;(0,u.S6)(i,function(gt){(0,u.S6)(gt.dataByAxis,function(lt){var xt=r.getComponent(lt.axisDim+"Axis",lt.axisIndex),It=lt.value;if(!(!xt||It==null)){var Et=bt.gk(It,xt.axis,r,lt.seriesDataIndices,lt.valueLabelOpt),Zt=(0,S.TX)("section",{header:Et,noHeader:!(0,u.fy)(Et),sortBlocks:!0,blocks:[]});y.blocks.push(Zt),(0,u.S6)(lt.seriesDataIndices,function(zt){var Rt=r.getSeriesByIndex(zt.seriesIndex),Nt=zt.dataIndexInside,_t=Rt.getDataParams(Nt);if(!(_t.dataIndex<0)){_t.axisDim=lt.axisDim,_t.axisIndex=lt.axisIndex,_t.axisType=lt.axisType,_t.axisId=lt.axisId,_t.axisValue=Ht.DX(xt.axis,{value:It}),_t.axisValueLabel=Et,_t.marker=H.makeTooltipMarker("item",(0,Z.Lz)(_t.color),f);var Wt=(0,x.f)(Rt.formatTooltip(Nt,!0,null)),kt=Wt.frag;if(kt){var Gt=L([Rt],o).get("valueFormatter");Zt.blocks.push(Gt?(0,u.l7)({valueFormatter:Gt},kt):kt)}Wt.text&&M.push(Wt.text),p.push(_t)}})}})}),y.blocks.reverse(),M.reverse();var F=n.position,at=h.get("order"),it=(0,S.BY)(y,H,f,at,r.get("useUTC"),h.get("textStyle"));it&&M.unshift(it);var st=f==="richText"?`

`:"<br/>",rt=M.join(st);this._showOrMove(h,function(){this._updateContentNotChangedOnAxis(i,p)?this._updatePosition(h,F,a[0],a[1],this._tooltipContent,p):this._showTooltipContent(h,rt,p,Math.random()+"",a[0],a[1],F,null,H)})},t.prototype._showSeriesItemTooltip=function(i,n,r){var o=this._ecModel,a=(0,Dt.A)(n),h=a.seriesIndex,f=o.getSeriesByIndex(h),p=a.dataModel||f,y=a.dataIndex,M=a.dataType,H=p.getData(M),F=this._renderMode,at=i.positionDefault,it=L([H.getItemModel(y),p,f&&(f.coordinateSystem||{}).model],this._tooltipModel,at?{position:at}:null),st=it.get("trigger");if(!(st!=null&&st!=="item")){var rt=p.getDataParams(y,M),gt=new S.iv;rt.marker=gt.makeTooltipMarker("item",(0,Z.Lz)(rt.color),F);var lt=(0,x.f)(p.formatTooltip(y,!1,M)),xt=it.get("order"),It=it.get("valueFormatter"),Et=lt.frag,Zt=Et?(0,S.BY)(It?(0,u.l7)({valueFormatter:It},Et):Et,gt,F,xt,o.get("useUTC"),it.get("textStyle")):lt.text,zt="item_"+p.name+"_"+y;this._showOrMove(it,function(){this._showTooltipContent(it,Zt,rt,zt,i.offsetX,i.offsetY,i.position,i.target,gt)}),r({type:"showTip",dataIndexInside:y,dataIndex:H.getRawIndex(y),seriesIndex:h,from:this.uid})}},t.prototype._showComponentItemTooltip=function(i,n,r){var o=(0,Dt.A)(n),a=o.tooltipConfig,h=a.option||{};if((0,u.HD)(h)){var f=h;h={content:f,formatter:f}}var p=[h],y=this._ecModel.getComponent(o.componentMainType,o.componentIndex);y&&p.push(y),p.push({formatter:h.content});var M=i.positionDefault,H=L(p,this._tooltipModel,M?{position:M}:null),F=H.get("content"),at=Math.random()+"",it=new S.iv;this._showOrMove(H,function(){var st=(0,u.d9)(H.get("formatterParams")||{});this._showTooltipContent(H,F,st,at,i.offsetX,i.offsetY,i.position,n,it)}),r({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(i,n,r,o,a,h,f,p,y){if(this._ticket="",!(!i.get("showContent")||!i.get("show"))){var M=this._tooltipContent;M.setEnterable(i.get("enterable"));var H=i.get("formatter");f=f||i.get("position");var F=n,at=this._getNearestPoint([a,h],r,i.get("trigger"),i.get("borderColor")),it=at.color;if(H)if((0,u.HD)(H)){var st=i.ecModel.get("useUTC"),rt=(0,u.kJ)(r)?r[0]:r,gt=rt&&rt.axisType&&rt.axisType.indexOf("time")>=0;F=H,gt&&(F=(0,wt.WU)(rt.axisValue,F,st)),F=(0,Z.kF)(F,r,!0)}else if((0,u.mf)(H)){var lt=(0,u.ak)(function(xt,It){xt===this._ticket&&(M.setContent(It,y,i,it,f),this._updatePosition(i,f,a,h,M,r,p))},this);this._ticket=o,F=H(r,o,lt)}else F=H;M.setContent(F,y,i,it,f),M.show(i,it),this._updatePosition(i,f,a,h,M,r,p)}},t.prototype._getNearestPoint=function(i,n,r,o){if(r==="axis"||(0,u.kJ)(n))return{color:o||(this._renderMode==="html"?"#fff":"none")};if(!(0,u.kJ)(n))return{color:o||n.color||n.borderColor}},t.prototype._updatePosition=function(i,n,r,o,a,h,f){var p=this._api.getWidth(),y=this._api.getHeight();n=n||i.get("position");var M=a.getSize(),H=i.get("align"),F=i.get("verticalAlign"),at=f&&f.getBoundingRect().clone();if(f&&at.applyTransform(f.transform),(0,u.mf)(n)&&(n=n([r,o],h,a.el,at,{viewSize:[p,y],contentSize:M.slice()})),(0,u.kJ)(n))r=(0,Mt.GM)(n[0],p),o=(0,Mt.GM)(n[1],y);else if((0,u.Kn)(n)){var it=n;it.width=M[0],it.height=M[1];var st=(0,Tt.ME)(it,{width:p,height:y});r=st.x,o=st.y,H=null,F=null}else if((0,u.HD)(n)&&f){var rt=q(n,at,M,i.get("borderWidth"));r=rt[0],o=rt[1]}else{var rt=G(r,o,a,p,y,H?null:20,F?null:20);r=rt[0],o=rt[1]}if(H&&(r-=tt(H)?M[0]/2:H==="right"?M[0]:0),F&&(o-=tt(F)?M[1]/2:F==="bottom"?M[1]:0),Y(i)){var rt=V(r,o,a,p,y);r=rt[0],o=rt[1]}a.moveTo(r,o)},t.prototype._updateContentNotChangedOnAxis=function(i,n){var r=this._lastDataByCoordSys,o=this._cbParamsList,a=!!r&&r.length===i.length;return a&&(0,u.S6)(r,function(h,f){var p=h.dataByAxis||[],y=i[f]||{},M=y.dataByAxis||[];a=a&&p.length===M.length,a&&(0,u.S6)(p,function(H,F){var at=M[F]||{},it=H.seriesDataIndices||[],st=at.seriesDataIndices||[];a=a&&H.value===at.value&&H.axisType===at.axisType&&H.axisId===at.axisId&&it.length===st.length,a&&(0,u.S6)(it,function(rt,gt){var lt=st[gt];a=a&&rt.seriesIndex===lt.seriesIndex&&rt.dataIndex===lt.dataIndex}),o&&(0,u.S6)(H.seriesDataIndices,function(rt){var gt=rt.seriesIndex,lt=n[gt],xt=o[gt];lt&&xt&&xt.data!==lt.data&&(a=!1)})})}),this._lastDataByCoordSys=i,this._cbParamsList=n,!!a},t.prototype._hide=function(i){this._lastDataByCoordSys=null,i({type:"hideTip",from:this.uid})},t.prototype.dispose=function(i,n){w.Z.node||!n.getDom()||((0,d.ZH)(this,"_updatePosition"),this._tooltipContent.dispose(),Lt.E("itemTooltip",n))},t.type="tooltip",t}(Bt.Z);function L(e,t,i){var n=t.ecModel,r;i?(r=new Pt.Z(i,n,n),r=new Pt.Z(t.option,r,n)):r=t;for(var o=e.length-1;o>=0;o--){var a=e[o];a&&(a instanceof Pt.Z&&(a=a.get("tooltip",!0)),(0,u.HD)(a)&&(a={formatter:a}),a&&(r=new Pt.Z(a,r,n)))}return r}function N(e,t){return e.dispatchAction||(0,u.ak)(t.dispatchAction,t)}function G(e,t,i,n,r,o,a){var h=i.getSize(),f=h[0],p=h[1];return o!=null&&(e+f+o+2>n?e-=f+o:e+=o),a!=null&&(t+p+a>r?t-=p+a:t+=a),[e,t]}function V(e,t,i,n,r){var o=i.getSize(),a=o[0],h=o[1];return e=Math.min(e+a,n)-a,t=Math.min(t+h,r)-h,e=Math.max(e,0),t=Math.max(t,0),[e,t]}function q(e,t,i,n){var r=i[0],o=i[1],a=Math.ceil(Math.SQRT2*n)+8,h=0,f=0,p=t.width,y=t.height;switch(e){case"inside":h=t.x+p/2-r/2,f=t.y+y/2-o/2;break;case"top":h=t.x+p/2-r/2,f=t.y-o-a;break;case"bottom":h=t.x+p/2-r/2,f=t.y+y+a;break;case"left":h=t.x-r-a,f=t.y+y/2-o/2;break;case"right":h=t.x+p+a,f=t.y+y/2-o/2}return[h,f]}function tt(e){return e==="center"||e==="middle"}function c(e,t,i){var n=(0,Ot.zH)(e).queryOptionMap,r=n.keys()[0];if(!(!r||r==="series")){var o=(0,Ot.HZ)(t,r,n.get(r),{useDefault:!1,enableAll:!1,enableNone:!1}),a=o.models[0];if(!!a){var h=i.getViewOfComponentModel(a),f;if(h.group.traverse(function(p){var y=(0,Dt.A)(p).tooltipConfig;if(y&&y.name===e.name)return f=p,!0}),f)return{componentMainType:r,componentIndex:a.componentIndex,el:f}}}}var l=_;function s(e){(0,Q.D)(j.N),e.registerComponentModel(B),e.registerComponentView(l),e.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},u.ZT),e.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},u.ZT)}},31073:function(Ct,ot,v){"use strict";v.d(ot,{H:function(){return j}});function j(Q,X){return Q.type===X}},49069:function(Ct,ot,v){"use strict";v.d(ot,{bK:function(){return X},Yh:function(){return $},Mk:function(){return k}});var j=v(33051),Q=v(32234);function X(B,u,w){w=w||{};var O=B.coordinateSystem,E=u.axis,Z={},Y=E.getAxesOnZeroOf()[0],z=E.position,D=Y?"onZero":z,P=E.dim,b=O.getRect(),W=[b.x,b.x+b.width,b.y,b.y+b.height],S={left:0,right:1,top:0,bottom:1,onZero:2},I=u.get("offset")||0,m=P==="x"?[W[2]-I,W[3]+I]:[W[0]-I,W[1]+I];if(Y){var T=Y.toGlobalCoord(Y.dataToCoord(0));m[S.onZero]=Math.max(Math.min(T,m[1]),m[0])}Z.position=[P==="y"?m[S[D]]:W[0],P==="x"?m[S[D]]:W[3]],Z.rotation=Math.PI/2*(P==="x"?0:1);var A={top:-1,bottom:1,left:-1,right:1};Z.labelDirection=Z.tickDirection=Z.nameDirection=A[z],Z.labelOffset=Y?m[S[z]]-m[S.onZero]:0,u.get(["axisTick","inside"])&&(Z.tickDirection=-Z.tickDirection),j.Jv(w.labelInside,u.get(["axisLabel","inside"]))&&(Z.labelDirection=-Z.labelDirection);var R=u.get(["axisLabel","rotate"]);return Z.labelRotate=D==="top"?-R:R,Z.z2=1,Z}function $(B){return B.get("coordinateSystem")==="cartesian2d"}function k(B){var u={xAxisModel:null,yAxisModel:null};return j.S6(u,function(w,O){var E=O.replace(/Model$/,""),Z=B.getReferringComponents(E,Q.C6).models[0];u[O]=Z}),u}},64088:function(Ct,ot,v){"use strict";v.d(ot,{Z:function(){return $}});var j=v(33051),Q={average:function(k){for(var B=0,u=0,w=0;w<k.length;w++)isNaN(k[w])||(B+=k[w],u++);return u===0?NaN:B/u},sum:function(k){for(var B=0,u=0;u<k.length;u++)B+=k[u]||0;return B},max:function(k){for(var B=-Infinity,u=0;u<k.length;u++)k[u]>B&&(B=k[u]);return isFinite(B)?B:NaN},min:function(k){for(var B=Infinity,u=0;u<k.length;u++)k[u]<B&&(B=k[u]);return isFinite(B)?B:NaN},minmax:function(k){for(var B=-Infinity,u=-Infinity,w=0;w<k.length;w++){var O=k[w],E=Math.abs(O);E>B&&(B=E,u=O)}return isFinite(u)?u:NaN},nearest:function(k){return k[0]}},X=function(k){return Math.round(k.length/2)};function $(k){return{seriesType:k,reset:function(B,u,w){var O=B.getData(),E=B.get("sampling"),Z=B.coordinateSystem,Y=O.count();if(Y>10&&Z.type==="cartesian2d"&&E){var z=Z.getBaseAxis(),D=Z.getOtherAxis(z),P=z.getExtent(),b=w.getDevicePixelRatio(),W=Math.abs(P[1]-P[0])*(b||1),S=Math.round(Y/W);if(isFinite(S)&&S>1){E==="lttb"&&B.setData(O.lttbDownSample(O.mapDimension(D.dim),1/S));var I=void 0;(0,j.HD)(E)?I=Q[E]:(0,j.mf)(E)&&(I=E),I&&B.setData(O.downSample(O.mapDimension(D.dim),1/S,I,X))}}}}}}}]);
