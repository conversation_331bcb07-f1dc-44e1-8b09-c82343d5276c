plugins {
    id 'org.springframework.boot' version '2.7.7'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'java'
    id "io.spring.javaformat" version "0.0.27"
}

repositories {
	maven { url 'https://repo.spring.io/snapshot' }
	mavenCentral()
}

allprojects {
    group = 'cn.surveyking'
    version = 'v1.8.0'
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'io.spring.javaformat'
    ext{

    }
    dependencies {
        implementation "org.mapstruct:mapstruct:1.4.2.Final"
        compileOnly "org.projectlombok:lombok"
        annotationProcessor "org.projectlombok:lombok-mapstruct-binding:0.2.0"
        annotationProcessor "org.mapstruct:mapstruct-processor:1.4.2.Final"
        annotationProcessor "org.projectlombok:lombok"

        implementation ('org.springframework.boot:spring-boot-starter-web') {
            exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
        }
        implementation 'org.springframework.boot:spring-boot-starter-undertow'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        implementation 'org.springframework.boot:spring-boot-starter-security'
        implementation 'org.apache.commons:commons-lang3:3.0'
        implementation 'com.anji-plus:spring-boot-starter-captcha:1.3.0'

        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.springframework.security:spring-security-test'
    }
    repositories {
        mavenCentral()
    }

    compileJava {
        options.compilerArgs += [
                '-Amapstruct.suppressGeneratorTimestamp=true',
                '-Amapstruct.suppressGeneratorVersionInfoComment=true',
                '-Amapstruct.verbose=true',
                '-Amapstruct.defaultComponentModel=spring',
                '-Amapstruct.unmappedTargetPolicy=IGNORE'
        ]
    }
}