(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[953],{69753:function(H,C,n){"use strict";var m=n(28991),f=n(67294),Z=n(49495),R=n(27029),P=function(h,D){return f.createElement(R.Z,(0,m.Z)((0,m.Z)({},h),{},{ref:D,icon:Z.Z}))};P.displayName="DownloadOutlined",C.Z=f.forwardRef(P)},3471:function(H,C,n){"use strict";var m=n(28991),f=n(67294),Z=n(29245),R=n(27029),P=function(h,D){return f.createElement(R.Z,(0,m.Z)((0,m.Z)({},h),{},{ref:D,icon:Z.Z}))};P.displayName="EllipsisOutlined",C.Z=f.forwardRef(P)},87588:function(H,C,n){"use strict";var m=n(28991),f=n(67294),Z=n(61144),R=n(27029),P=function(h,D){return f.createElement(R.Z,(0,m.Z)((0,m.Z)({},h),{},{ref:D,icon:Z.Z}))};P.displayName="ExclamationCircleOutlined",C.Z=f.forwardRef(P)},93279:function(H,C,n){"use strict";var m=n(28991),f=n(81253),Z=n(57663),R=n(71577),P=n(59250),B=n(13013),h=n(30887),D=n(28682),se=n(84305),o=n(88182),r=n(85893),q=n(34804),ee=n(3471),ne=n(94184),G=n.n(ne),J=n(67294),Y=n(32070),ue=n.n(Y),te=["key","name"],le=function(_){var N=_.children,I=_.menus,T=_.onSelect,K=_.className,V=_.style,S=(0,J.useContext)(o.ZP.ConfigContext),$=S.getPrefixCls,e=$("pro-table-dropdown"),E=(0,r.jsx)(D.Z,{onClick:function(M){return T&&T(M.key)},items:I==null?void 0:I.map(function(O){return{label:O.name,key:O.key}})});return(0,r.jsx)(B.Z,{overlay:E,className:G()(e,K),children:(0,r.jsxs)(R.Z,{style:V,children:[N," ",(0,r.jsx)(q.Z,{})]})})},z=function(_){var N=_.className,I=_.style,T=_.onSelect,K=_.menus,V=K===void 0?[]:K,S=_.children,$=(0,J.useContext)(o.ZP.ConfigContext),e=$.getPrefixCls,E=e("pro-table-dropdown"),O=(0,r.jsx)(D.Z,{onClick:function(y){T==null||T(y.key)},items:V.map(function(M){var y=M.key,A=M.name,L=(0,f.Z)(M,te);return(0,m.Z)((0,m.Z)({key:y},L),{},{title:L.title,label:A})})});return(0,r.jsx)(B.Z,{overlay:O,className:G()(E,N),children:(0,r.jsx)("a",{style:I,children:S||(0,r.jsx)(ee.Z,{})})})};z.Button=le,C.Z=z},50727:function(H,C,n){"use strict";var m=n(9715),f=n(55246),Z=n(57663),R=n(71577),P=n(96156),B=n(28481),h=n(81253),D=n(7353),se=n(92137),o=n(28991),r=n(85893),q=n(51042),ee=n(59773),ne=n(97324),G=n(80392),J=n(19912),Y=n(29111),ue=n(70460),te=n(86705),le=n(21770),z=n(88306),Q=n(8880),_=n(67294),N=n(70751),I=["onTableChange","maxLength","formItemProps","recordCreatorProps","rowKey","controlled","defaultValue","onChange","editableFormRef"],T=["record","position","creatorButtonText","newRecordType","parentKey","style"],K=_.createContext(void 0);function V(e){var E=e.children,O=e.record,M=e.position,y=e.newRecordType,A=e.parentKey,L=(0,_.useContext)(K);return _.cloneElement(E,(0,o.Z)((0,o.Z)({},E.props),{},{onClick:function(){var F=(0,se.Z)((0,D.Z)().mark(function ae(oe){var X,k,x,U;return(0,D.Z)().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.next=2,(X=(k=E.props).onClick)===null||X===void 0?void 0:X.call(k,oe);case 2:if(U=c.sent,U!==!1){c.next=5;break}return c.abrupt("return");case 5:L==null||(x=L.current)===null||x===void 0||x.addEditRecord(O,{position:M,newRecordType:y,parentKey:A});case 6:case"end":return c.stop()}},ae)}));function W(ae){return F.apply(this,arguments)}return W}()}))}function S(e){var E,O,M=(0,G.YB)(),y=e.onTableChange,A=e.maxLength,L=e.formItemProps,F=e.recordCreatorProps,W=e.rowKey,ae=e.controlled,oe=e.defaultValue,X=e.onChange,k=e.editableFormRef,x=(0,h.Z)(e,I),U=(0,J.Z)(e.value),p=(0,_.useRef)(),c=(0,_.useRef)();(0,_.useImperativeHandle)(x.actionRef,function(){return p.current});var me=(0,le.Z)(function(){return e.value||oe||[]},{value:e.value,onChange:e.onChange}),ve=(0,B.Z)(me,2),d=ve[0],fe=ve[1],b=_.useMemo(function(){return typeof W=="function"?W:function(u,t){return u[W]||t}},[W]),ce=function(t){if(typeof t=="number"&&!e.name){if(t>=d.length)return t;var l=d&&d[t];return b==null?void 0:b(l,t)}if((typeof t=="string"||t>=d.length)&&e.name){var a=d.findIndex(function(i,s){var v;return(b==null||(v=b(i,s))===null||v===void 0?void 0:v.toString())===(t==null?void 0:t.toString())});return a}return t};(0,_.useImperativeHandle)(k,function(){var u=function(a){var i,s;if(a==null)throw new Error("rowIndex is required");var v=ce(a),g=[e.name,(i=v==null?void 0:v.toString())!==null&&i!==void 0?i:""].flat(1).filter(Boolean);return(s=c.current)===null||s===void 0?void 0:s.getFieldValue(g)},t=function(){var a,i=[e.name].flat(1).filter(Boolean);if(Array.isArray(i)&&i.length===0){var s,v=(s=c.current)===null||s===void 0?void 0:s.getFieldsValue();return Array.isArray(v)?v:Object.keys(v).map(function(g){return v[g]})}return(a=c.current)===null||a===void 0?void 0:a.getFieldValue(i)};return(0,o.Z)((0,o.Z)({},c.current),{},{getRowData:u,getRowsData:t,setRowData:function(a,i){var s,v,g,_e;if(a==null)throw new Error("rowIndex is required");var de=ce(a),Te=[e.name,(s=de==null?void 0:de.toString())!==null&&s!==void 0?s:""].flat(1).filter(Boolean),Ze=((v=c.current)===null||v===void 0||(g=v.getFieldsValue)===null||g===void 0?void 0:g.call(v))||{},Be=(0,Q.Z)(Ze,Te,(0,o.Z)((0,o.Z)({},u(a)),i||{}));return(_e=c.current)===null||_e===void 0?void 0:_e.setFieldsValue(Be)}})}),(0,_.useEffect)(function(){!e.controlled||d.forEach(function(u,t){var l;(l=c.current)===null||l===void 0||l.setFieldsValue((0,P.Z)({},b(u,t),u))},{})},[d,e.controlled]),(0,_.useEffect)(function(){if(e.name){var u;c.current=e==null||(u=e.editable)===null||u===void 0?void 0:u.form}},[(E=e.editable)===null||E===void 0?void 0:E.form,e.name]);var j=F||{},Pe=j.record,re=j.position,De=j.creatorButtonText,Oe=j.newRecordType,Me=j.parentKey,Ce=j.style,Re=(0,h.Z)(j,T),Ee=re==="top",w=(0,_.useMemo)(function(){return A&&A<=(d==null?void 0:d.length)?!1:F!==!1&&(0,r.jsx)(V,{record:(0,Y.h)(Pe,d==null?void 0:d.length,d)||{},position:re,parentKey:(0,Y.h)(Me,d==null?void 0:d.length,d),newRecordType:Oe,children:(0,r.jsx)(R.Z,(0,o.Z)((0,o.Z)({type:"dashed",style:(0,o.Z)({display:"block",margin:"10px 0",width:"100%"},Ce),icon:(0,r.jsx)(q.Z,{})},Re),{},{children:De||M.getMessage("editableTable.action.add","\u6DFB\u52A0\u4E00\u884C\u6570\u636E")}))})},[F,A,d==null?void 0:d.length]),he=(0,_.useMemo)(function(){return w?Ee?{components:{header:{wrapper:function(t){var l,a=t.className,i=t.children;return(0,r.jsxs)("thead",{className:a,children:[i,(0,r.jsxs)("tr",{style:{position:"relative"},children:[(0,r.jsx)("td",{colSpan:0,style:{visibility:"hidden"},children:w}),(0,r.jsx)("td",{style:{position:"absolute",left:0,width:"100%"},colSpan:(l=x.columns)===null||l===void 0?void 0:l.length,children:w})]})]})}}}}:{tableViewRender:function(t,l){var a,i;return(0,r.jsxs)(r.Fragment,{children:[(a=(i=e.tableViewRender)===null||i===void 0?void 0:i.call(e,t,l))!==null&&a!==void 0?a:l,w]})}}:{}},[Ee,w]),ie=(0,o.Z)({},e.editable),ge=(0,ue.J)(function(u,t){var l,a,i;if((l=e.editable)===null||l===void 0||(a=l.onValuesChange)===null||a===void 0||a.call(l,u,t),(i=e.onValuesChange)===null||i===void 0||i.call(e,t,u),e.controlled){var s;e==null||(s=e.onChange)===null||s===void 0||s.call(e,t)}});return((e==null?void 0:e.onValuesChange)||((O=e.editable)===null||O===void 0?void 0:O.onValuesChange)||e.controlled&&(e==null?void 0:e.onChange))&&(ie.onValuesChange=ge),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(K.Provider,{value:p,children:(0,r.jsx)(N.Z,(0,o.Z)((0,o.Z)((0,o.Z)({search:!1,options:!1,pagination:!1,rowKey:W,revalidateOnFocus:!1},x),he),{},{tableLayout:"fixed",actionRef:p,onChange:y,editable:(0,o.Z)((0,o.Z)({},ie),{},{formProps:(0,o.Z)({formRef:c},ie.formProps)}),dataSource:d,onDataSourceChange:function(t){if(fe(t),e.name&&re==="top"){var l,a=(0,Q.Z)({},[e.name].flat(1).filter(Boolean),t);(l=c.current)===null||l===void 0||l.setFieldsValue(a)}}}))}),e.name?(0,r.jsx)(ee.Z,{name:[e.name],children:function(t){var l,a,i=(0,z.Z)(t,[e.name].flat(1)),s=i==null?void 0:i.find(function(v,g){return!(0,te.Z)(v,U==null?void 0:U[g])});return s&&U&&(e==null||(l=e.editable)===null||l===void 0||(a=l.onValuesChange)===null||a===void 0||a.call(l,s,i)),null}}):null]})}function $(e){var E=ne.ZP.useFormInstance();return e.name?(0,r.jsx)(f.Z.Item,(0,o.Z)((0,o.Z)({style:{maxWidth:"100%"}},e==null?void 0:e.formItemProps),{},{name:e.name,children:(0,r.jsx)(S,(0,o.Z)((0,o.Z)({},e),{},{editable:(0,o.Z)((0,o.Z)({},e.editable),{},{form:E})}))})):(0,r.jsx)(S,(0,o.Z)({},e))}$.RecordCreator=V,C.Z=$},32070:function(){}}]);
