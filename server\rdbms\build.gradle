plugins {
    id 'java'
}

version 'unspecified'

repositories {
    mavenCentral()
}

dependencies {
    implementation project(':shared')
    implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.0'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.dhatim:fastexcel-reader:0.12.3'
    
//    implementation 'com.h2database:h2:2.1.214'
    implementation 'mysql:mysql-connector-java:8.0.26'
}

test {
    useJUnitPlatform()
}