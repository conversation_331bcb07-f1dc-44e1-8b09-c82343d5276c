<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="keywords" content="SurveyKing,survey,卷王问卷" />
    <meta name="description" content="问卷考试系统" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>SurveyKing</title>
    <link rel="icon" href="/favicon.ico" type="image/x-icon" />

    <script>
      (function () {
        if (
          typeof WeixinJSBridge == "object" &&
          typeof WeixinJSBridge.invoke == "function"
        ) {
          handleFontSize();
        } else {
          if (document.addEventListener) {
            document.addEventListener(
              "WeixinJSBridgeReady",
              handleFontSize,
              false
            );
          } else if (document.attachEvent) {
            document.attachEvent("WeixinJSBridgeReady", handleFontSize);
            document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
          }
        }
        function handleFontSize() {
          // 设置网页字体为默认大小
          WeixinJSBridge.invoke("setFontSizeCallback", {
            fontSize: 0,
          });
          // 重写设置网页字体大小的事件
          WeixinJSBridge.on("menu:setfont", function () {
            WeixinJSBridge.invoke("setFontSizeCallback", {
              fontSize: 0,
            });
          });
        }
      })();
    </script>
    <link rel="stylesheet" href="/umi.5e512287.css" />
    <script>
      window.routerBase = "/";
    </script>
    <script>
      //! umi version: 3.5.41
    </script>
  </head>

  <body>
    <noscript>
      <div class="noscript-container">
        Hi there! Please
        <div class="noscript-enableJS">
          <a
            href="https://www.enablejavascript.io/en"
            target="_blank"
            rel="noopener noreferrer"
          >
            <b>enable Javascript</b>
          </a>
        </div>
        卷王问卷考试系统
      </div>
    </noscript>
    <div id="root">
      <style>
        html,
        body,
        #root {
          height: 100%;
          margin: 0;
          padding: 0;
        }

        #root {
          background-repeat: no-repeat;
          background-size: 100% auto;
        }

        .noscript-container {
          display: flex;
          align-content: center;
          justify-content: center;
          margin-top: 90px;
          font-size: 20px;
          font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
            "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
        }

        .noscript-enableJS {
          padding-right: 3px;
          padding-left: 3px;
        }

        .spinner {
          width: 50px;
          height: 40px;
          margin: 100px auto;
          font-size: 10px;
          text-align: center;
        }

        .spinner > div {
          display: inline-block;
          width: 6px;
          height: 100%;
          background-color: #3873f6;

          -webkit-animation: sk-stretchdelay 1.2s infinite ease-in-out;
          animation: sk-stretchdelay 1.2s infinite ease-in-out;
        }

        .spinner .rect2 {
          -webkit-animation-delay: -1.1s;
          animation-delay: -1.1s;
        }

        .spinner .rect3 {
          -webkit-animation-delay: -1s;
          animation-delay: -1s;
        }

        .spinner .rect4 {
          -webkit-animation-delay: -0.9s;
          animation-delay: -0.9s;
        }

        .spinner .rect5 {
          -webkit-animation-delay: -0.8s;
          animation-delay: -0.8s;
        }

        @-webkit-keyframes sk-stretchdelay {
          0%,
          40%,
          100% {
            -webkit-transform: scaleY(0.4);
          }

          20% {
            -webkit-transform: scaleY(1);
          }
        }

        @keyframes sk-stretchdelay {
          0%,
          40%,
          100% {
            -webkit-transform: scaleY(0.4);
            transform: scaleY(0.4);
          }

          20% {
            -webkit-transform: scaleY(1);
            transform: scaleY(1);
          }
        }
      </style>
      <div
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          min-height: 420px;
        "
      >
        <div class="page-loading-warp">
          <div class="spinner">
            <div class="rect1"></div>
            <div class="rect2"></div>
            <div class="rect3"></div>
            <div class="rect4"></div>
            <div class="rect5"></div>
          </div>
        </div>
        <div
          style="display: flex; align-items: center; justify-content: center"
        ></div>
      </div>
    </div>

    <script>
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?43e89c38a9e9332e702161a0c19bba11";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();
    </script>

    <script src="/umi.4af4ba2d.js"></script>
  </body>
</html>
