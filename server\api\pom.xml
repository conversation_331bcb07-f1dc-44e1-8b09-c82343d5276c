<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>survey-server</artifactId>
        <groupId>cn.surveyking</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>api</artifactId>
    <packaging>jar</packaging>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.surveyking</groupId>
            <artifactId>shared</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.surveyking</groupId>
            <artifactId>rdbms</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.surveyking</groupId>
            <artifactId>ai</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>surveyking-${revision}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <activeProfile>dev</activeProfile>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <activeProfile>pro</activeProfile>
            </properties>
        </profile>
    </profiles>
</project>