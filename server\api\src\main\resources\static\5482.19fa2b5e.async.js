(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[5482],{80341:function(){},25482:function(oe,z,a){"use strict";a.r(z);var u=a(11849),K=a(83279),p=a(54029),w=a(79166),b=a(39428),M=a(3182),t=a(94657),d=a(1615),T=a(27484),B=a.n(T),x=a(67294),H=a(51005),$=a(42285),W=a(3980),Y=a(94975),c=a(85893),V=function(){var ee,le=(0,x.useState)([]),re=(0,t.Z)(le,2),F=re[0],ue=re[1],o=(0,x.useState)(!0),e=(0,t.Z)(o,2),f=e[0],n=e[1],s=(0,x.useState)([]),i=(0,t.Z)(s,2),y=i[0],v=i[1],N=(0,x.useState)(),Z=(0,t.Z)(N,2),O=Z[0],S=Z[1],A=(0,Y.Z)((0,M.Z)((0,b.Z)().mark(function r(){var l,g;return(0,b.Z)().wrap(function(_){for(;;)switch(_.prev=_.next){case 0:return _.next=2,W.hi.loadDictItems({dictCode:"repoType"});case 2:return g=_.sent,S((l=g.list)===null||l===void 0?void 0:l[0].itemName),_.abrupt("return",g.list);case 5:case"end":return _.stop()}},r)})));(0,x.useEffect)(function(){O?W.hi.loadRepo({pageSize:100,category:O}).then(function(r){v(r.list||[])}):v([])},[O]);var m=(0,x.useRef)({current:1,pageSize:10}),R=function(l){return l.tempSave===0?(0,c.jsx)(w.Z,{status:"processing",text:"\u8FDB\u884C\u4E2D"}):(0,c.jsx)(w.Z,{status:"success",text:"\u5DF2\u5B8C\u6210"})};function D(r){return h.apply(this,arguments)}function h(){return h=(0,M.Z)((0,b.Z)().mark(function r(l){var g,E,_,k,Q,C,G;return(0,b.Z)().wrap(function(L){for(;;)switch(L.prev=L.next){case 0:return f&&!l&&F.length>0&&(m.current.current=m.current.current+1),g=(0,u.Z)((0,u.Z)({},m.current),{},{current:l||m.current.current}),L.next=4,W.hi.listExercise(g);case 4:return E=L.sent,_=m.current,k=_.pageSize,Q=k===void 0?10:k,C=_.total,G=C===void 0?E.total||0:C,l||(m.current=(0,u.Z)((0,u.Z)({},m.current),{},{total:E.total}),n(G/Q>m.current.current)),L.abrupt("return",E.list);case 8:case"end":return L.stop()}},r)})),h.apply(this,arguments)}var P=function(){var r=(0,M.Z)((0,b.Z)().mark(function l(){var g;return(0,b.Z)().wrap(function(_){for(;;)switch(_.prev=_.next){case 0:return _.next=2,D();case 2:g=_.sent,ue([].concat((0,K.Z)(F),(0,K.Z)(g)));case 4:case"end":return _.stop()}},l)}));return function(){return r.apply(this,arguments)}}(),j=function(l){var g=[];return g.push({key:"start",text:l.tempSave===0?"\u7EE7\u7EED\u7B54\u9898":"\u67E5\u770B\u7ED3\u679C",primary:!0,onClick:function(){$.m8.push("/t/".concat(l.repoId,"/").concat(l.answerId))}}),g.push({key:"cancel",text:"\u53D6\u6D88"}),g};return(0,c.jsxs)("div",{children:[(0,c.jsxs)(d.Zb,{style:{margin:8},bodyStyle:{padding:0},children:[(0,c.jsx)(d.mQ,{activeKey:O,onChange:function(l){return S(l)},children:(ee=A.value)===null||ee===void 0?void 0:ee.map(function(r){var l=r.itemName;return(0,c.jsx)(d.mQ.Tab,{title:l},l)})}),(0,c.jsxs)(d.rj,{columns:2,gap:8,style:{paddingBlock:8},children:[y.map(function(r){return(0,c.jsx)(d.Zb,{extra:(0,c.jsx)(H.Z,{}),style:{background:"#f6f6f8"},title:(0,c.jsxs)("div",{style:{fontWeight:"normal",padding:8},children:[r.name,(0,c.jsx)("div",{style:{color:"#a6a6a8"},children:r.description})]}),onClick:function(){$.m8.push("/exercise/".concat(r.id),{state:{name:r.name}})}},r.id)}),(0,c.jsx)(d.rj.Item,{span:2,children:y.length===0&&(0,c.jsx)(d.HY,{description:"\u6682\u65E0\u6570\u636E"})})]})]}),(0,c.jsxs)(d.Zb,{title:"\u7EC3\u4E60\u8BB0\u5F55",style:{margin:8},bodyStyle:{padding:0},children:[(0,c.jsx)(d.aV,{children:F.map(function(r,l){return(0,c.jsxs)(d.aV.Item,{onClick:function(){d.u_.show({content:r.projectName,closeOnAction:!0,actions:j(r)})},extra:R(r),description:r.createTime?B()(r.examStartTime).format("YYYY-MM-DD HH:mm"):void 0,children:[r.projectName,(0,c.jsx)(d.ko,{percent:r.percent,style:{"--fill-color":r.tempSave===0?"var(--adm-color-primary)":"var(--adm-color-success)"}})]},l)})}),(0,c.jsx)(d.v_,{loadMore:P,hasMore:f})]})]})};z.default=V},79166:function(oe,z,a){"use strict";a.d(z,{Z:function(){return ue}});var u=a(96156),K=a(90484),p=a(22122),w=a(94184),b=a.n(w),M=a(5461),t=a(67294),d=a(53124),T=a(96159),B=a(98787);function x(o){return B.Y.includes(o)}var H=function(e){var f=e.className,n=e.prefixCls,s=e.style,i=e.color,y=e.children,v=e.text,N=e.placement,Z=N===void 0?"end":N,O=t.useContext(d.E_),S=O.getPrefixCls,A=O.direction,m=S("ribbon",n),R=x(i),D=b()(m,"".concat(m,"-placement-").concat(Z),(0,u.Z)((0,u.Z)({},"".concat(m,"-rtl"),A==="rtl"),"".concat(m,"-color-").concat(i),R),f),h={},P={};return i&&!R&&(h.background=i,P.color=i),t.createElement("div",{className:"".concat(m,"-wrapper")},y,t.createElement("div",{className:D,style:(0,p.Z)((0,p.Z)({},h),s)},t.createElement("span",{className:"".concat(m,"-text")},v),t.createElement("div",{className:"".concat(m,"-corner"),style:P})))},$=H,W=a(28481);function Y(o){var e=o.prefixCls,f=o.value,n=o.current,s=o.offset,i=s===void 0?0:s,y;return i&&(y={position:"absolute",top:"".concat(i,"00%"),left:0}),t.createElement("span",{style:y,className:b()("".concat(e,"-only-unit"),{current:n})},f)}function c(o,e,f){for(var n=o,s=0;(n+10)%10!==e;)n+=f,s+=f;return s}function V(o){var e=o.prefixCls,f=o.count,n=o.value,s=Number(n),i=Math.abs(f),y=t.useState(s),v=(0,W.Z)(y,2),N=v[0],Z=v[1],O=t.useState(i),S=(0,W.Z)(O,2),A=S[0],m=S[1],R=function(){Z(s),m(i)};t.useEffect(function(){var E=setTimeout(function(){R()},1e3);return function(){clearTimeout(E)}},[s]);var D,h;if(N===s||Number.isNaN(s)||Number.isNaN(N))D=[t.createElement(Y,(0,p.Z)({},o,{key:s,current:!0}))],h={transition:"none"};else{D=[];for(var P=s+10,j=[],r=s;r<=P;r+=1)j.push(r);var l=j.findIndex(function(E){return E%10===N});D=j.map(function(E,_){var k=E%10;return t.createElement(Y,(0,p.Z)({},o,{key:E,value:k,offset:_-l,current:_===l}))});var g=A<i?1:-1;h={transform:"translateY(".concat(-c(N,s,g),"00%)")}}return t.createElement("span",{className:"".concat(e,"-only"),style:h,onTransitionEnd:R},D)}var I=function(o,e){var f={};for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&e.indexOf(n)<0&&(f[n]=o[n]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(o);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(o,n[s])&&(f[n[s]]=o[n[s]]);return f},ee=function(e){var f=e.prefixCls,n=e.count,s=e.className,i=e.motionClassName,y=e.style,v=e.title,N=e.show,Z=e.component,O=Z===void 0?"sup":Z,S=e.children,A=I(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),m=t.useContext(d.E_),R=m.getPrefixCls,D=R("scroll-number",f),h=(0,p.Z)((0,p.Z)({},A),{"data-show":N,style:y,className:b()(D,s,i),title:v}),P=n;if(n&&Number(n)%1==0){var j=String(n).split("");P=j.map(function(r,l){return t.createElement(V,{prefixCls:D,count:Number(n),value:r,key:j.length-l})})}return y&&y.borderColor&&(h.style=(0,p.Z)((0,p.Z)({},y),{boxShadow:"0 0 0 1px ".concat(y.borderColor," inset")})),S?(0,T.Tm)(S,function(r){return{className:b()("".concat(D,"-custom-component"),r==null?void 0:r.className,i)}}):t.createElement(O,h,P)},le=ee,re=function(o,e){var f={};for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&e.indexOf(n)<0&&(f[n]=o[n]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(o);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(o,n[s])&&(f[n[s]]=o[n[s]]);return f},F=function(e){var f=e.prefixCls,n=e.scrollNumberPrefixCls,s=e.children,i=e.status,y=e.text,v=e.color,N=e.count,Z=N===void 0?null:N,O=e.overflowCount,S=O===void 0?99:O,A=e.dot,m=A===void 0?!1:A,R=e.size,D=R===void 0?"default":R,h=e.title,P=e.offset,j=e.style,r=e.className,l=e.showZero,g=l===void 0?!1:l,E=re(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","showZero"]),_=t.useContext(d.E_),k=_.getPrefixCls,Q=_.direction,C=k("badge",f),G=Z>S?"".concat(S,"+"):Z,te=G==="0"||G===0,L=Z===null||te&&!g,ce=(i!=null||v!=null)&&L,ne=m&&!te,J=ne?"":G,X=(0,t.useMemo)(function(){var U=J==null||J==="";return(U||te&&!g)&&!ne},[J,te,g,ne]),fe=(0,t.useRef)(Z);X||(fe.current=Z);var q=fe.current,ve=(0,t.useRef)(J);X||(ve.current=J);var ie=ve.current,me=(0,t.useRef)(ne);X||(me.current=ne);var ae=(0,t.useMemo)(function(){if(!P)return(0,p.Z)({},j);var U={marginTop:P[1]};return Q==="rtl"?U.left=parseInt(P[0],10):U.right=-parseInt(P[0],10),(0,p.Z)((0,p.Z)({},U),j)},[Q,P,j]),ye=h!=null?h:typeof q=="string"||typeof q=="number"?q:void 0,ge=X||!y?null:t.createElement("span",{className:"".concat(C,"-status-text")},y),Ce=!q||(0,K.Z)(q)!=="object"?void 0:(0,T.Tm)(q,function(U){return{style:(0,p.Z)((0,p.Z)({},ae),U.style)}}),Ee=b()((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(C,"-status-dot"),ce),"".concat(C,"-status-").concat(i),!!i),"".concat(C,"-status-").concat(v),x(v))),_e={};v&&!x(v)&&(_e.background=v);var pe=b()(C,(0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(C,"-status"),ce),"".concat(C,"-not-a-wrapper"),!s),"".concat(C,"-rtl"),Q==="rtl"),r);if(!s&&ce){var he=ae.color;return t.createElement("span",(0,p.Z)({},E,{className:pe,style:ae}),t.createElement("span",{className:Ee,style:_e}),y&&t.createElement("span",{style:{color:he},className:"".concat(C,"-status-text")},y))}return t.createElement("span",(0,p.Z)({},E,{className:pe}),s,t.createElement(M.default,{visible:!X,motionName:"".concat(C,"-zoom"),motionAppear:!1,motionDeadline:1e3},function(U){var be=U.className,xe=k("scroll-number",n),de=me.current,Ze=b()((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(C,"-dot"),de),"".concat(C,"-count"),!de),"".concat(C,"-count-sm"),D==="small"),"".concat(C,"-multiple-words"),!de&&ie&&ie.toString().length>1),"".concat(C,"-status-").concat(i),!!i),"".concat(C,"-status-").concat(v),x(v))),se=(0,p.Z)({},ae);return v&&!x(v)&&(se=se||{},se.background=v),t.createElement(le,{prefixCls:xe,show:!X,motionClassName:be,className:Ze,count:ie,title:ye,style:se,key:"scrollNumber"},Ce)}),ge)};F.Ribbon=$;var ue=F},54029:function(oe,z,a){"use strict";var u=a(38663),K=a.n(u),p=a(80341),w=a.n(p)},94975:function(oe,z,a){"use strict";a.d(z,{Z:function(){return b}});var u=a(67294),K=a(65353);function p(){var M=(0,u.useRef)(!1),t=(0,u.useCallback)(function(){return M.current},[]);return(0,u.useEffect)(function(){return M.current=!0,function(){M.current=!1}},[]),t}function w(M,t,d){t===void 0&&(t=[]),d===void 0&&(d={loading:!1});var T=(0,u.useRef)(0),B=p(),x=(0,u.useState)(d),H=x[0],$=x[1],W=(0,u.useCallback)(function(){for(var Y=[],c=0;c<arguments.length;c++)Y[c]=arguments[c];var V=++T.current;return H.loading||$(function(I){return(0,K.pi)((0,K.pi)({},I),{loading:!0})}),M.apply(void 0,Y).then(function(I){return B()&&V===T.current&&$({value:I,loading:!1}),I},function(I){return B()&&V===T.current&&$({error:I,loading:!1}),I})},t);return[H,W]}function b(M,t){t===void 0&&(t=[]);var d=w(M,t,{loading:!0}),T=d[0],B=d[1];return(0,u.useEffect)(function(){B()},[B]),T}}}]);
