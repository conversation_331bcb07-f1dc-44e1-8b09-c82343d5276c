(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[4950],{44292:function(Zt,pt,P){"use strict";P.d(pt,{ke:function(){return F},D:function(){return W},KZ:function(){return K},eq:function(){return R},bX:function(){return b},XD:function(){return S},Zi:function(){return m},_W:function(){return g}});var C=P(33051),k=P(32234),v=(0,k.Yf)();function F(y,E,h,c,_){var M;if(E&&E.ecModel){var l=E.ecModel.getUpdatePayload();M=l&&l.animation}var O=E&&E.isAnimationEnabled(),d=y==="update";if(O){var f=void 0,u=void 0,p=void 0;c?(f=(0,C.pD)(c.duration,200),u=(0,C.pD)(c.easing,"cubicOut"),p=0):(f=E.getShallow(d?"animationDurationUpdate":"animationDuration"),u=E.getShallow(d?"animationEasingUpdate":"animationEasing"),p=E.getShallow(d?"animationDelayUpdate":"animationDelay")),M&&(M.duration!=null&&(f=M.duration),M.easing!=null&&(u=M.easing),M.delay!=null&&(p=M.delay)),(0,C.mf)(p)&&(p=p(h,_)),(0,C.mf)(f)&&(f=f(h));var n={duration:f||0,delay:p,easing:u};return n}else return null}function z(y,E,h,c,_,M,l){var O=!1,d;(0,C.mf)(_)?(l=M,M=_,_=null):(0,C.Kn)(_)&&(M=_.cb,l=_.during,O=_.isFrom,d=_.removeOpt,_=_.dataIndex);var f=y==="leave";f||E.stopAnimation("leave");var u=F(y,c,_,f?d||{}:null,c&&c.getAnimationDelayParams?c.getAnimationDelayParams(E,_):null);if(u&&u.duration>0){var p=u.duration,n=u.delay,i=u.easing,t={duration:p,delay:n||0,easing:i,done:M,force:!!M||!!l,setToFinal:!f,scope:y,during:l};O?E.animateFrom(h,t):E.animateTo(h,t)}else E.stopAnimation(),!O&&E.attr(h),l&&l(1),M&&M()}function W(y,E,h,c,_,M){z("update",y,E,h,c,_,M)}function K(y,E,h,c,_,M){z("enter",y,E,h,c,_,M)}function R(y){if(!y.__zr)return!0;for(var E=0;E<y.animators.length;E++){var h=y.animators[E];if(h.scope==="leave")return!0}return!1}function b(y,E,h,c,_,M){R(y)||z("leave",y,E,h,c,_,M)}function A(y,E,h,c){y.removeTextContent(),y.removeTextGuideLine(),b(y,{style:{opacity:0}},E,h,c)}function S(y,E,h){function c(){y.parent&&y.parent.remove(y)}y.isGroup?y.traverse(function(_){_.isGroup||A(_,E,h,c)}):A(y,E,h,c)}function m(y){v(y).oldStyle=y.style}function g(y){return v(y).oldStyle}},95682:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return k}});var C=P(32234);function k(){var v=(0,C.Yf)();return function(F){var z=v(F),W=F.pipelineContext,K=!!z.large,R=!!z.progressiveRender,b=z.large=!!(W&&W.large),A=z.progressiveRender=!!(W&&W.progressiveRender);return(K!==b||R!==A)&&"reset"}}},30090:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return F}});var C=P(55623),k=P(5101),v=P(33051);function F(z,W,K){W=(0,v.kJ)(W)&&{coordDimensions:W}||(0,v.l7)({encodeDefine:z.getEncode()},W);var R=z.getSource(),b=(0,C.Z)(R,W).dimensions,A=new k.Z(b,z);return A.initData(R,K),A}},58608:function(Zt,pt,P){"use strict";var C=P(33051),k=P(38154),v=P(22095),F=P(50453),z=P(9074),W=P(30106),K=P(36006),R=P(1497),b=P(85669),A=P(41525),S=P(32892),m=P(45280),g=P(88199),y=P(54162),E=Math.PI,h=function(){function i(t,r){this.group=new k.Z,this.opt=r,this.axisModel=t,(0,C.ce)(r,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var e=new k.Z({x:r.position[0],y:r.position[1],rotation:r.rotation});e.updateTransform(),this._transformGroup=e}return i.prototype.hasBuilder=function(t){return!!c[t]},i.prototype.add=function(t){c[t](this.opt,this.axisModel,this.group,this._transformGroup)},i.prototype.getGroup=function(){return this.group},i.innerTextLayout=function(t,r,e){var a=(0,b.wW)(r-t),s,o;return(0,b.mW)(a)?(o=e>0?"top":"bottom",s="center"):(0,b.mW)(a-E)?(o=e>0?"bottom":"top",s="center"):(o="middle",a>0&&a<E?s=e>0?"right":"left":s=e>0?"left":"right"),{rotation:a,textAlign:s,textVerticalAlign:o}},i.makeAxisEventDataBase=function(t){var r={componentType:t.mainType,componentIndex:t.componentIndex};return r[t.mainType+"Index"]=t.componentIndex,r},i.isLabelSilent=function(t){var r=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||r&&r.show)},i}(),c={axisLine:function(i,t,r,e){var a=t.get(["axisLine","show"]);if(a==="auto"&&i.handleAutoShown&&(a=i.handleAutoShown("axisLine")),!!a){var s=t.axis.getExtent(),o=e.transform,D=[s[0],0],B=[s[1],0],I=D[0]>B[0];o&&((0,m.Ne)(D,D,o),(0,m.Ne)(B,B,o));var L=(0,C.l7)({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),U=new v.Z({shape:{x1:D[0],y1:D[1],x2:B[0],y2:B[1]},style:L,strokeContainThreshold:i.strokeContainThreshold||5,silent:!0,z2:1});F.subPixelOptimizeLine(U.shape,U.style.lineWidth),U.anid="line",r.add(U);var x=t.get(["axisLine","symbol"]);if(x!=null){var T=t.get(["axisLine","symbolSize"]);(0,C.HD)(x)&&(x=[x,x]),((0,C.HD)(T)||(0,C.hj)(T))&&(T=[T,T]);var w=(0,A.Cq)(t.get(["axisLine","symbolOffset"])||0,T),V=T[0],N=T[1];(0,C.S6)([{rotate:i.rotation+Math.PI/2,offset:w[0],r:0},{rotate:i.rotation-Math.PI/2,offset:w[1],r:Math.sqrt((D[0]-B[0])*(D[0]-B[0])+(D[1]-B[1])*(D[1]-B[1]))}],function(G,Z){if(x[Z]!=="none"&&x[Z]!=null){var H=(0,A.th)(x[Z],-V/2,-N/2,V,N,L.stroke,!0),X=G.r+G.offset,ut=I?B:D;H.attr({rotation:G.rotate,x:ut[0]+X*Math.cos(i.rotation),y:ut[1]-X*Math.sin(i.rotation),silent:!0,z2:11}),r.add(H)}})}}},axisTickLabel:function(i,t,r,e){var a=u(r,e,t,i),s=n(r,e,t,i);if(M(t,s,a),p(r,e,t,i.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=(0,y.VT)((0,C.UI)(s,function(D){return{label:D,priority:D.z2,defaultAttr:{ignore:D.ignore}}}));(0,y.yl)(o)}},axisName:function(i,t,r,e){var a=(0,C.Jv)(i.axisName,t.get("name"));if(!!a){var s=t.get("nameLocation"),o=i.nameDirection,D=t.getModel("nameTextStyle"),B=t.get("nameGap")||0,I=t.axis.getExtent(),L=I[0]>I[1]?-1:1,U=[s==="start"?I[0]-L*B:s==="end"?I[1]+L*B:(I[0]+I[1])/2,d(s)?i.labelOffset+o*B:0],x,T=t.get("nameRotate");T!=null&&(T=T*E/180);var w;d(s)?x=h.innerTextLayout(i.rotation,T!=null?T:i.rotation,o):(x=_(i.rotation,s,T||0,I),w=i.axisNameAvailableWidth,w!=null&&(w=Math.abs(w/Math.sin(x.rotation)),!isFinite(w)&&(w=null)));var V=D.getFont(),N=t.get("nameTruncate",!0)||{},G=N.ellipsis,Z=(0,C.Jv)(i.nameTruncateMaxWidth,N.maxWidth,w),H=new z.ZP({x:U[0],y:U[1],rotation:x.rotation,silent:h.isLabelSilent(t),style:(0,K.Lr)(D,{text:a,font:V,overflow:"truncate",width:Z,ellipsis:G,fill:D.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:D.get("align")||x.textAlign,verticalAlign:D.get("verticalAlign")||x.textVerticalAlign}),z2:1});if(F.setTooltipConfig({el:H,componentModel:t,itemName:a}),H.__fullText=a,H.anid="name",t.get("triggerEvent")){var X=h.makeAxisEventDataBase(t);X.targetType="axisName",X.name=a,(0,W.A)(H).eventData=X}e.add(H),H.updateTransform(),r.add(H),H.decomposeTransform()}}};function _(i,t,r,e){var a=(0,b.wW)(r-i),s,o,D=e[0]>e[1],B=t==="start"&&!D||t!=="start"&&D;return(0,b.mW)(a-E/2)?(o=B?"bottom":"top",s="center"):(0,b.mW)(a-E*1.5)?(o=B?"top":"bottom",s="center"):(o="middle",a<E*1.5&&a>E/2?s=B?"left":"right":s=B?"right":"left"),{rotation:a,textAlign:s,textVerticalAlign:o}}function M(i,t,r){if(!(0,g.WY)(i.axis)){var e=i.get(["axisLabel","showMinLabel"]),a=i.get(["axisLabel","showMaxLabel"]);t=t||[],r=r||[];var s=t[0],o=t[1],D=t[t.length-1],B=t[t.length-2],I=r[0],L=r[1],U=r[r.length-1],x=r[r.length-2];e===!1?(l(s),l(I)):O(s,o)&&(e?(l(o),l(L)):(l(s),l(I))),a===!1?(l(D),l(U)):O(B,D)&&(a?(l(B),l(x)):(l(D),l(U)))}}function l(i){i&&(i.ignore=!0)}function O(i,t){var r=i&&i.getBoundingRect().clone(),e=t&&t.getBoundingRect().clone();if(!(!r||!e)){var a=S.yR([]);return S.U1(a,a,-i.rotation),r.applyTransform(S.dC([],a,i.getLocalTransform())),e.applyTransform(S.dC([],a,t.getLocalTransform())),r.intersect(e)}}function d(i){return i==="middle"||i==="center"}function f(i,t,r,e,a){for(var s=[],o=[],D=[],B=0;B<i.length;B++){var I=i[B].coord;o[0]=I,o[1]=0,D[0]=I,D[1]=r,t&&((0,m.Ne)(o,o,t),(0,m.Ne)(D,D,t));var L=new v.Z({shape:{x1:o[0],y1:o[1],x2:D[0],y2:D[1]},style:e,z2:2,autoBatch:!0,silent:!0});F.subPixelOptimizeLine(L.shape,L.style.lineWidth),L.anid=a+"_"+i[B].tickValue,s.push(L)}return s}function u(i,t,r,e){var a=r.axis,s=r.getModel("axisTick"),o=s.get("show");if(o==="auto"&&e.handleAutoShown&&(o=e.handleAutoShown("axisTick")),!(!o||a.scale.isBlank())){for(var D=s.getModel("lineStyle"),B=e.tickDirection*s.get("length"),I=a.getTicksCoords(),L=f(I,t.transform,B,(0,C.ce)(D.getLineStyle(),{stroke:r.get(["axisLine","lineStyle","color"])}),"ticks"),U=0;U<L.length;U++)i.add(L[U]);return L}}function p(i,t,r,e){var a=r.axis,s=r.getModel("minorTick");if(!(!s.get("show")||a.scale.isBlank())){var o=a.getMinorTicksCoords();if(!!o.length)for(var D=s.getModel("lineStyle"),B=e*s.get("length"),I=(0,C.ce)(D.getLineStyle(),(0,C.ce)(r.getModel("axisTick").getLineStyle(),{stroke:r.get(["axisLine","lineStyle","color"])})),L=0;L<o.length;L++)for(var U=f(o[L],t.transform,B,I,"minorticks_"+L),x=0;x<U.length;x++)i.add(U[x])}}function n(i,t,r,e){var a=r.axis,s=(0,C.Jv)(e.axisLabelShow,r.get(["axisLabel","show"]));if(!(!s||a.scale.isBlank())){var o=r.getModel("axisLabel"),D=o.get("margin"),B=a.getViewLabels(),I=((0,C.Jv)(e.labelRotate,o.get("rotate"))||0)*E/180,L=h.innerTextLayout(e.rotation,I,e.labelDirection),U=r.getCategories&&r.getCategories(!0),x=[],T=h.isLabelSilent(r),w=r.get("triggerEvent");return(0,C.S6)(B,function(V,N){var G=a.scale.type==="ordinal"?a.scale.getRawOrdinalNumber(V.tickValue):V.tickValue,Z=V.formattedLabel,H=V.rawLabel,X=o;if(U&&U[G]){var ut=U[G];(0,C.Kn)(ut)&&ut.textStyle&&(X=new R.Z(ut.textStyle,o,r.ecModel))}var lt=X.getTextColor()||r.get(["axisLine","lineStyle","color"]),ot=a.dataToCoord(G),q=X.getShallow("align",!0)||L.textAlign,tt=(0,C.pD)(X.getShallow("alignMinLabel",!0),q),j=(0,C.pD)(X.getShallow("alignMaxLabel",!0),q),et=X.getShallow("verticalAlign",!0)||X.getShallow("baseline",!0)||L.textVerticalAlign,ct=(0,C.pD)(X.getShallow("verticalAlignMinLabel",!0),et),_t=(0,C.pD)(X.getShallow("verticalAlignMaxLabel",!0),et),St=new z.ZP({x:ot,y:e.labelOffset+e.labelDirection*D,rotation:L.rotation,silent:T,z2:10+(V.level||0),style:(0,K.Lr)(X,{text:Z,align:N===0?tt:N===B.length-1?j:q,verticalAlign:N===0?ct:N===B.length-1?_t:et,fill:(0,C.mf)(lt)?lt(a.type==="category"?H:a.type==="value"?G+"":G,N):lt})});if(St.anid="label_"+G,w){var At=h.makeAxisEventDataBase(r);At.targetType="axisLabel",At.value=H,At.tickIndex=N,a.type==="category"&&(At.dataIndex=G),(0,W.A)(St).eventData=At}t.add(St),St.updateTransform(),x.push(St),i.add(St),St.decomposeTransform()}),x}}pt.Z=h},99337:function(Zt,pt,P){"use strict";P.d(pt,{b:function(){return F},l:function(){return z}});var C=P(76172),k=P(78988),v=P(35151);function F(W,K,R){var b=K.getBoxLayoutParams(),A=K.get("padding"),S={width:R.getWidth(),height:R.getHeight()},m=(0,C.ME)(b,S,A);(0,C.BZ)(K.get("orient"),W,K.get("itemGap"),m.width,m.height),(0,C.p$)(W,b,S,A)}function z(W,K){var R=k.MY(K.get("padding")),b=K.getItemStyle(["color","opacity"]);return b.fill=K.get("backgroundColor"),W=new v.Z({shape:{x:W.x-R[3],y:W.y-R[0],width:W.width+R[1]+R[3],height:W.height+R[0]+R[2],r:K.get("borderRadius")},style:b,silent:!0,z2:-1}),W}},93450:function(Zt,pt,P){"use strict";P.d(pt,{N:function(){return ot}});var C=P(68023),k=P(18299),v=P(33051),F=P(1497),z=P(32234),W=P(98071),K=function(q,tt){if(tt==="all")return{type:"all",title:q.getLocaleModel().get(["legend","selector","all"])};if(tt==="inverse")return{type:"inverse",title:q.getLocaleModel().get(["legend","selector","inverse"])}},R=function(q){(0,k.ZT)(tt,q);function tt(){var j=q!==null&&q.apply(this,arguments)||this;return j.type=tt.type,j.layoutMode={type:"box",ignoreSize:!0},j}return tt.prototype.init=function(j,et,ct){this.mergeDefaultAndTheme(j,ct),j.selected=j.selected||{},this._updateSelector(j)},tt.prototype.mergeOption=function(j,et){q.prototype.mergeOption.call(this,j,et),this._updateSelector(j)},tt.prototype._updateSelector=function(j){var et=j.selector,ct=this.ecModel;et===!0&&(et=j.selector=["all","inverse"]),v.kJ(et)&&v.S6(et,function(_t,St){v.HD(_t)&&(_t={type:_t}),et[St]=v.TS(_t,K(ct,_t.type))})},tt.prototype.optionUpdated=function(){this._updateData(this.ecModel);var j=this._data;if(j[0]&&this.get("selectedMode")==="single"){for(var et=!1,ct=0;ct<j.length;ct++){var _t=j[ct].get("name");if(this.isSelected(_t)){this.select(_t),et=!0;break}}!et&&this.select(j[0].get("name"))}},tt.prototype._updateData=function(j){var et=[],ct=[];j.eachRawSeries(function(Ct){var Et=Ct.name;ct.push(Et);var Ot;if(Ct.legendVisualProvider){var Wt=Ct.legendVisualProvider,Ut=Wt.getAllNames();j.isSeriesFiltered(Ct)||(ct=ct.concat(Ut)),Ut.length?et=et.concat(Ut):Ot=!0}else Ot=!0;Ot&&(0,z.yu)(Ct)&&et.push(Ct.name)}),this._availableNames=ct;var _t=this.get("data")||et,St=v.kW(),At=v.UI(_t,function(Ct){return(v.HD(Ct)||v.hj(Ct))&&(Ct={name:Ct}),St.get(Ct.name)?null:(St.set(Ct.name,!0),new F.Z(Ct,this,this.ecModel))},this);this._data=v.hX(At,function(Ct){return!!Ct})},tt.prototype.getData=function(){return this._data},tt.prototype.select=function(j){var et=this.option.selected,ct=this.get("selectedMode");if(ct==="single"){var _t=this._data;v.S6(_t,function(St){et[St.get("name")]=!1})}et[j]=!0},tt.prototype.unSelect=function(j){this.get("selectedMode")!=="single"&&(this.option.selected[j]=!1)},tt.prototype.toggleSelected=function(j){var et=this.option.selected;et.hasOwnProperty(j)||(et[j]=!0),this[et[j]?"unSelect":"select"](j)},tt.prototype.allSelect=function(){var j=this._data,et=this.option.selected;v.S6(j,function(ct){et[ct.get("name",!0)]=!0})},tt.prototype.inverseSelect=function(){var j=this._data,et=this.option.selected;v.S6(j,function(ct){var _t=ct.get("name",!0);et.hasOwnProperty(_t)||(et[_t]=!0),et[_t]=!et[_t]})},tt.prototype.isSelected=function(j){var et=this.option.selected;return!(et.hasOwnProperty(j)&&!et[j])&&v.cq(this._availableNames,j)>=0},tt.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},tt.type="legend.plain",tt.dependencies=["series"],tt.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},tt}(W.Z),b=R,A=P(21092),S=P(50453),m=P(38154),g=P(9074),y=P(35151),E=P(26357),h=P(36006),c=P(99337),_=P(76172),M=P(33166),l=P(41525),O=P(48625),d=P(30106),f=v.WA,u=v.S6,p=m.Z,n=function(q){(0,k.ZT)(tt,q);function tt(){var j=q!==null&&q.apply(this,arguments)||this;return j.type=tt.type,j.newlineDisabled=!1,j}return tt.prototype.init=function(){this.group.add(this._contentGroup=new p),this.group.add(this._selectorGroup=new p),this._isFirstRender=!0},tt.prototype.getContentGroup=function(){return this._contentGroup},tt.prototype.getSelectorGroup=function(){return this._selectorGroup},tt.prototype.render=function(j,et,ct){var _t=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!j.get("show",!0)){var St=j.get("align"),At=j.get("orient");(!St||St==="auto")&&(St=j.get("left")==="right"&&At==="vertical"?"right":"left");var Ct=j.get("selector",!0),Et=j.get("selectorPosition",!0);Ct&&(!Et||Et==="auto")&&(Et=At==="horizontal"?"end":"start"),this.renderInner(St,j,et,ct,Ct,At,Et);var Ot=j.getBoxLayoutParams(),Wt={width:ct.getWidth(),height:ct.getHeight()},Ut=j.get("padding"),Bt=_.ME(Ot,Wt,Ut),Ht=this.layoutInner(j,St,Bt,_t,Ct,Et),kt=_.ME(v.ce({width:Ht.width,height:Ht.height},Ot),Wt,Ut);this.group.x=kt.x-Ht.x,this.group.y=kt.y-Ht.y,this.group.markRedraw(),this.group.add(this._backgroundEl=(0,c.l)(Ht,j))}},tt.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},tt.prototype.renderInner=function(j,et,ct,_t,St,At,Ct){var Et=this.getContentGroup(),Ot=v.kW(),Wt=et.get("selectedMode"),Ut=[];ct.eachRawSeries(function(Bt){!Bt.get("legendHoverLink")&&Ut.push(Bt.id)}),u(et.getData(),function(Bt,Ht){var kt=Bt.get("name");if(!this.newlineDisabled&&(kt===""||kt===`
`)){var Xt=new p;Xt.newline=!0,Et.add(Xt);return}var Vt=ct.getSeriesByName(kt)[0];if(!Ot.get(kt))if(Vt){var st=Vt.getData(),gt=st.getVisual("legendLineStyle")||{},xt=st.getVisual("legendIcon"),Rt=st.getVisual("style"),dt=this._createItem(Vt,kt,Ht,Bt,et,j,gt,Rt,xt,Wt,_t);dt.on("click",f(r,kt,null,_t,Ut)).on("mouseover",f(a,Vt.name,null,_t,Ut)).on("mouseout",f(s,Vt.name,null,_t,Ut)),ct.ssr&&dt.eachChild(function(Mt){var vt=(0,d.A)(Mt);vt.seriesIndex=Vt.seriesIndex,vt.dataIndex=Ht,vt.ssrType="legend"}),Ot.set(kt,!0)}else ct.eachRawSeries(function(Mt){if(!Ot.get(kt)&&Mt.legendVisualProvider){var vt=Mt.legendVisualProvider;if(!vt.containName(kt))return;var Y=vt.indexOfName(kt),$=vt.getItemVisual(Y,"style"),Q=vt.getItemVisual(Y,"legendIcon"),ft=(0,A.Qc)($.fill);ft&&ft[3]===0&&(ft[3]=.2,$=v.l7(v.l7({},$),{fill:(0,A.Pz)(ft,"rgba")}));var Tt=this._createItem(Mt,kt,Ht,Bt,et,j,{},$,Q,Wt,_t);Tt.on("click",f(r,null,kt,_t,Ut)).on("mouseover",f(a,null,kt,_t,Ut)).on("mouseout",f(s,null,kt,_t,Ut)),ct.ssr&&Tt.eachChild(function(wt){var bt=(0,d.A)(wt);bt.seriesIndex=Mt.seriesIndex,bt.dataIndex=Ht,bt.ssrType="legend"}),Ot.set(kt,!0)}},this)},this),St&&this._createSelector(St,et,_t,At,Ct)},tt.prototype._createSelector=function(j,et,ct,_t,St){var At=this.getSelectorGroup();u(j,function(Et){var Ot=Et.type,Wt=new g.ZP({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){ct.dispatchAction({type:Ot==="all"?"legendAllSelect":"legendInverseSelect"})}});At.add(Wt);var Ut=et.getModel("selectorLabel"),Bt=et.getModel(["emphasis","selectorLabel"]);(0,h.ni)(Wt,{normal:Ut,emphasis:Bt},{defaultText:Et.title}),(0,E.vF)(Wt)})},tt.prototype._createItem=function(j,et,ct,_t,St,At,Ct,Et,Ot,Wt,Ut){var Bt=j.visualDrawType,Ht=St.get("itemWidth"),kt=St.get("itemHeight"),Xt=St.isSelected(et),Vt=_t.get("symbolRotate"),st=_t.get("symbolKeepAspect"),gt=_t.get("icon");Ot=gt||Ot||"roundRect";var xt=i(Ot,_t,Ct,Et,Bt,Xt,Ut),Rt=new p,dt=_t.getModel("textStyle");if(v.mf(j.getLegendIcon)&&(!gt||gt==="inherit"))Rt.add(j.getLegendIcon({itemWidth:Ht,itemHeight:kt,icon:Ot,iconRotate:Vt,itemStyle:xt.itemStyle,lineStyle:xt.lineStyle,symbolKeepAspect:st}));else{var Mt=gt==="inherit"&&j.getData().getVisual("symbol")?Vt==="inherit"?j.getData().getVisual("symbolRotate"):Vt:0;Rt.add(t({itemWidth:Ht,itemHeight:kt,icon:Ot,iconRotate:Mt,itemStyle:xt.itemStyle,lineStyle:xt.lineStyle,symbolKeepAspect:st}))}var vt=At==="left"?Ht+5:-5,Y=At,$=St.get("formatter"),Q=et;v.HD($)&&$?Q=$.replace("{name}",et!=null?et:""):v.mf($)&&(Q=$(et));var ft=Xt?dt.getTextColor():_t.get("inactiveColor");Rt.add(new g.ZP({style:(0,h.Lr)(dt,{text:Q,x:vt,y:kt/2,fill:ft,align:Y,verticalAlign:"middle"},{inheritColor:ft})}));var Tt=new y.Z({shape:Rt.getBoundingRect(),style:{fill:"transparent"}}),wt=_t.getModel("tooltip");return wt.get("show")&&S.setTooltipConfig({el:Tt,componentModel:St,itemName:et,itemTooltipOption:wt.option}),Rt.add(Tt),Rt.eachChild(function(bt){bt.silent=!0}),Tt.silent=!Wt,this.getContentGroup().add(Rt),(0,E.vF)(Rt),Rt.__legendDataIndex=ct,Rt},tt.prototype.layoutInner=function(j,et,ct,_t,St,At){var Ct=this.getContentGroup(),Et=this.getSelectorGroup();_.BZ(j.get("orient"),Ct,j.get("itemGap"),ct.width,ct.height);var Ot=Ct.getBoundingRect(),Wt=[-Ot.x,-Ot.y];if(Et.markRedraw(),Ct.markRedraw(),St){_.BZ("horizontal",Et,j.get("selectorItemGap",!0));var Ut=Et.getBoundingRect(),Bt=[-Ut.x,-Ut.y],Ht=j.get("selectorButtonGap",!0),kt=j.getOrient().index,Xt=kt===0?"width":"height",Vt=kt===0?"height":"width",st=kt===0?"y":"x";At==="end"?Bt[kt]+=Ot[Xt]+Ht:Wt[kt]+=Ut[Xt]+Ht,Bt[1-kt]+=Ot[Vt]/2-Ut[Vt]/2,Et.x=Bt[0],Et.y=Bt[1],Ct.x=Wt[0],Ct.y=Wt[1];var gt={x:0,y:0};return gt[Xt]=Ot[Xt]+Ht+Ut[Xt],gt[Vt]=Math.max(Ot[Vt],Ut[Vt]),gt[st]=Math.min(0,Ut[st]+Bt[1-kt]),gt}else return Ct.x=Wt[0],Ct.y=Wt[1],this.group.getBoundingRect()},tt.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},tt.type="legend.plain",tt}(M.Z);function i(q,tt,j,et,ct,_t,St){function At(Xt,Vt){Xt.lineWidth==="auto"&&(Xt.lineWidth=Vt.lineWidth>0?2:0),u(Xt,function(st,gt){Xt[gt]==="inherit"&&(Xt[gt]=Vt[gt])})}var Ct=tt.getModel("itemStyle"),Et=Ct.getItemStyle(),Ot=q.lastIndexOf("empty",0)===0?"fill":"stroke",Wt=Ct.getShallow("decal");Et.decal=!Wt||Wt==="inherit"?et.decal:(0,O.I)(Wt,St),Et.fill==="inherit"&&(Et.fill=et[ct]),Et.stroke==="inherit"&&(Et.stroke=et[Ot]),Et.opacity==="inherit"&&(Et.opacity=(ct==="fill"?et:j).opacity),At(Et,et);var Ut=tt.getModel("lineStyle"),Bt=Ut.getLineStyle();if(At(Bt,j),Et.fill==="auto"&&(Et.fill=et.fill),Et.stroke==="auto"&&(Et.stroke=et.fill),Bt.stroke==="auto"&&(Bt.stroke=et.fill),!_t){var Ht=tt.get("inactiveBorderWidth"),kt=Et[Ot];Et.lineWidth=Ht==="auto"?et.lineWidth>0&&kt?2:0:Et.lineWidth,Et.fill=tt.get("inactiveColor"),Et.stroke=tt.get("inactiveBorderColor"),Bt.stroke=Ut.get("inactiveColor"),Bt.lineWidth=Ut.get("inactiveWidth")}return{itemStyle:Et,lineStyle:Bt}}function t(q){var tt=q.icon||"roundRect",j=(0,l.th)(tt,0,0,q.itemWidth,q.itemHeight,q.itemStyle.fill,q.symbolKeepAspect);return j.setStyle(q.itemStyle),j.rotation=(q.iconRotate||0)*Math.PI/180,j.setOrigin([q.itemWidth/2,q.itemHeight/2]),tt.indexOf("empty")>-1&&(j.style.stroke=j.style.fill,j.style.fill="#fff",j.style.lineWidth=2),j}function r(q,tt,j,et){s(q,tt,j,et),j.dispatchAction({type:"legendToggleSelect",name:q!=null?q:tt}),a(q,tt,j,et)}function e(q){for(var tt=q.getZr().storage.getDisplayList(),j,et=0,ct=tt.length;et<ct&&!(j=tt[et].states.emphasis);)et++;return j&&j.hoverLayer}function a(q,tt,j,et){e(j)||j.dispatchAction({type:"highlight",seriesName:q,name:tt,excludeSeriesId:et})}function s(q,tt,j,et){e(j)||j.dispatchAction({type:"downplay",seriesName:q,name:tt,excludeSeriesId:et})}var o=n;function D(q){var tt=q.findComponents({mainType:"legend"});tt&&tt.length&&q.filterSeries(function(j){for(var et=0;et<tt.length;et++)if(!tt[et].isSelected(j.name))return!1;return!0})}function B(q,tt,j){var et={},ct=q==="toggleSelected",_t;return j.eachComponent("legend",function(St){ct&&_t!=null?St[_t?"select":"unSelect"](tt.name):q==="allSelect"||q==="inverseSelect"?St[q]():(St[q](tt.name),_t=St.isSelected(tt.name));var At=St.getData();(0,v.S6)(At,function(Ct){var Et=Ct.get("name");if(!(Et===`
`||Et==="")){var Ot=St.isSelected(Et);et.hasOwnProperty(Et)?et[Et]=et[Et]&&Ot:et[Et]=Ot}})}),q==="allSelect"||q==="inverseSelect"?{selected:et}:{name:tt.name,selected:et}}function I(q){q.registerAction("legendToggleSelect","legendselectchanged",(0,v.WA)(B,"toggleSelected")),q.registerAction("legendAllSelect","legendselectall",(0,v.WA)(B,"allSelect")),q.registerAction("legendInverseSelect","legendinverseselect",(0,v.WA)(B,"inverseSelect")),q.registerAction("legendSelect","legendselected",(0,v.WA)(B,"select")),q.registerAction("legendUnSelect","legendunselected",(0,v.WA)(B,"unSelect"))}function L(q){q.registerComponentModel(b),q.registerComponentView(o),q.registerProcessor(q.PRIORITY.PROCESSOR.SERIES_FILTER,D),q.registerSubTypeDefaulter("legend",function(){return"plain"}),I(q)}var U=P(42151),x=function(q){(0,k.ZT)(tt,q);function tt(){var j=q!==null&&q.apply(this,arguments)||this;return j.type=tt.type,j}return tt.prototype.setScrollDataIndex=function(j){this.option.scrollDataIndex=j},tt.prototype.init=function(j,et,ct){var _t=(0,_.tE)(j);q.prototype.init.call(this,j,et,ct),T(this,j,_t)},tt.prototype.mergeOption=function(j,et){q.prototype.mergeOption.call(this,j,et),T(this,this.option,j)},tt.type="legend.scroll",tt.defaultOption=(0,U.ZL)(b.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),tt}(b);function T(q,tt,j){var et=q.getOrient(),ct=[1,1];ct[et.index]=0,(0,_.dt)(tt,j,{type:"box",ignoreSize:!!ct})}var w=x,V=P(44292),N=m.Z,G=["width","height"],Z=["x","y"],H=function(q){(0,k.ZT)(tt,q);function tt(){var j=q!==null&&q.apply(this,arguments)||this;return j.type=tt.type,j.newlineDisabled=!0,j._currentIndex=0,j}return tt.prototype.init=function(){q.prototype.init.call(this),this.group.add(this._containerGroup=new N),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new N)},tt.prototype.resetInner=function(){q.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},tt.prototype.renderInner=function(j,et,ct,_t,St,At,Ct){var Et=this;q.prototype.renderInner.call(this,j,et,ct,_t,St,At,Ct);var Ot=this._controllerGroup,Wt=et.get("pageIconSize",!0),Ut=v.kJ(Wt)?Wt:[Wt,Wt];Ht("pagePrev",0);var Bt=et.getModel("pageTextStyle");Ot.add(new g.ZP({name:"pageText",style:{text:"xx/xx",fill:Bt.getTextColor(),font:Bt.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),Ht("pageNext",1);function Ht(kt,Xt){var Vt=kt+"DataIndex",st=S.createIcon(et.get("pageIcons",!0)[et.getOrient().name][Xt],{onclick:v.ak(Et._pageGo,Et,Vt,et,_t)},{x:-Ut[0]/2,y:-Ut[1]/2,width:Ut[0],height:Ut[1]});st.name=kt,Ot.add(st)}},tt.prototype.layoutInner=function(j,et,ct,_t,St,At){var Ct=this.getSelectorGroup(),Et=j.getOrient().index,Ot=G[Et],Wt=Z[Et],Ut=G[1-Et],Bt=Z[1-Et];St&&_.BZ("horizontal",Ct,j.get("selectorItemGap",!0));var Ht=j.get("selectorButtonGap",!0),kt=Ct.getBoundingRect(),Xt=[-kt.x,-kt.y],Vt=v.d9(ct);St&&(Vt[Ot]=ct[Ot]-kt[Ot]-Ht);var st=this._layoutContentAndController(j,_t,Vt,Et,Ot,Ut,Bt,Wt);if(St){if(At==="end")Xt[Et]+=st[Ot]+Ht;else{var gt=kt[Ot]+Ht;Xt[Et]-=gt,st[Wt]-=gt}st[Ot]+=kt[Ot]+Ht,Xt[1-Et]+=st[Bt]+st[Ut]/2-kt[Ut]/2,st[Ut]=Math.max(st[Ut],kt[Ut]),st[Bt]=Math.min(st[Bt],kt[Bt]+Xt[1-Et]),Ct.x=Xt[0],Ct.y=Xt[1],Ct.markRedraw()}return st},tt.prototype._layoutContentAndController=function(j,et,ct,_t,St,At,Ct,Et){var Ot=this.getContentGroup(),Wt=this._containerGroup,Ut=this._controllerGroup;_.BZ(j.get("orient"),Ot,j.get("itemGap"),_t?ct.width:null,_t?null:ct.height),_.BZ("horizontal",Ut,j.get("pageButtonItemGap",!0));var Bt=Ot.getBoundingRect(),Ht=Ut.getBoundingRect(),kt=this._showController=Bt[St]>ct[St],Xt=[-Bt.x,-Bt.y];et||(Xt[_t]=Ot[Et]);var Vt=[0,0],st=[-Ht.x,-Ht.y],gt=v.pD(j.get("pageButtonGap",!0),j.get("itemGap",!0));if(kt){var xt=j.get("pageButtonPosition",!0);xt==="end"?st[_t]+=ct[St]-Ht[St]:Vt[_t]+=Ht[St]+gt}st[1-_t]+=Bt[At]/2-Ht[At]/2,Ot.setPosition(Xt),Wt.setPosition(Vt),Ut.setPosition(st);var Rt={x:0,y:0};if(Rt[St]=kt?ct[St]:Bt[St],Rt[At]=Math.max(Bt[At],Ht[At]),Rt[Ct]=Math.min(0,Ht[Ct]+st[1-_t]),Wt.__rectSize=ct[St],kt){var dt={x:0,y:0};dt[St]=Math.max(ct[St]-Ht[St]-gt,0),dt[At]=Rt[At],Wt.setClipPath(new y.Z({shape:dt})),Wt.__rectSize=dt[St]}else Ut.eachChild(function(vt){vt.attr({invisible:!0,silent:!0})});var Mt=this._getPageInfo(j);return Mt.pageIndex!=null&&V.D(Ot,{x:Mt.contentPosition[0],y:Mt.contentPosition[1]},kt?j:null),this._updatePageInfoView(j,Mt),Rt},tt.prototype._pageGo=function(j,et,ct){var _t=this._getPageInfo(et)[j];_t!=null&&ct.dispatchAction({type:"legendScroll",scrollDataIndex:_t,legendId:et.id})},tt.prototype._updatePageInfoView=function(j,et){var ct=this._controllerGroup;v.S6(["pagePrev","pageNext"],function(Ot){var Wt=Ot+"DataIndex",Ut=et[Wt]!=null,Bt=ct.childOfName(Ot);Bt&&(Bt.setStyle("fill",Ut?j.get("pageIconColor",!0):j.get("pageIconInactiveColor",!0)),Bt.cursor=Ut?"pointer":"default")});var _t=ct.childOfName("pageText"),St=j.get("pageFormatter"),At=et.pageIndex,Ct=At!=null?At+1:0,Et=et.pageCount;_t&&St&&_t.setStyle("text",v.HD(St)?St.replace("{current}",Ct==null?"":Ct+"").replace("{total}",Et==null?"":Et+""):St({current:Ct,total:Et}))},tt.prototype._getPageInfo=function(j){var et=j.get("scrollDataIndex",!0),ct=this.getContentGroup(),_t=this._containerGroup.__rectSize,St=j.getOrient().index,At=G[St],Ct=Z[St],Et=this._findTargetItemIndex(et),Ot=ct.children(),Wt=Ot[Et],Ut=Ot.length,Bt=Ut?1:0,Ht={contentPosition:[ct.x,ct.y],pageCount:Bt,pageIndex:Bt-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!Wt)return Ht;var kt=xt(Wt);Ht.contentPosition[St]=-kt.s;for(var Xt=Et+1,Vt=kt,st=kt,gt=null;Xt<=Ut;++Xt)gt=xt(Ot[Xt]),(!gt&&st.e>Vt.s+_t||gt&&!Rt(gt,Vt.s))&&(st.i>Vt.i?Vt=st:Vt=gt,Vt&&(Ht.pageNextDataIndex==null&&(Ht.pageNextDataIndex=Vt.i),++Ht.pageCount)),st=gt;for(var Xt=Et-1,Vt=kt,st=kt,gt=null;Xt>=-1;--Xt)gt=xt(Ot[Xt]),(!gt||!Rt(st,gt.s))&&Vt.i<st.i&&(st=Vt,Ht.pagePrevDataIndex==null&&(Ht.pagePrevDataIndex=Vt.i),++Ht.pageCount,++Ht.pageIndex),Vt=gt;return Ht;function xt(dt){if(dt){var Mt=dt.getBoundingRect(),vt=Mt[Ct]+dt[Ct];return{s:vt,e:vt+Mt[At],i:dt.__legendDataIndex}}}function Rt(dt,Mt){return dt.e>=Mt&&dt.s<=Mt+_t}},tt.prototype._findTargetItemIndex=function(j){if(!this._showController)return 0;var et,ct=this.getContentGroup(),_t;return ct.eachChild(function(St,At){var Ct=St.__legendDataIndex;_t==null&&Ct!=null&&(_t=At),Ct===j&&(et=At)}),et!=null?et:_t},tt.type="legend.scroll",tt}(o),X=H;function ut(q){q.registerAction("legendScroll","legendscroll",function(tt,j){var et=tt.scrollDataIndex;et!=null&&j.eachComponent({mainType:"legend",subType:"scroll",query:tt},function(ct){ct.setScrollDataIndex(et)})})}function lt(q){(0,C.D)(L),q.registerComponentModel(w),q.registerComponentView(X),ut(q)}function ot(q){(0,C.D)(L),(0,C.D)(lt)}},8690:function(Zt,pt,P){"use strict";P.d(pt,{N:function(){return g}});var C=P(18299),k=P(33051),v=P(9074),F=P(35151),z=P(30106),W=P(36006),K=P(76172),R=P(98071),b=P(33166),A=P(78988),S=function(y){(0,C.ZT)(E,y);function E(){var h=y!==null&&y.apply(this,arguments)||this;return h.type=E.type,h.layoutMode={type:"box",ignoreSize:!0},h}return E.type="title",E.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},E}(R.Z),m=function(y){(0,C.ZT)(E,y);function E(){var h=y!==null&&y.apply(this,arguments)||this;return h.type=E.type,h}return E.prototype.render=function(h,c,_){if(this.group.removeAll(),!!h.get("show")){var M=this.group,l=h.getModel("textStyle"),O=h.getModel("subtextStyle"),d=h.get("textAlign"),f=k.pD(h.get("textBaseline"),h.get("textVerticalAlign")),u=new v.ZP({style:(0,W.Lr)(l,{text:h.get("text"),fill:l.getTextColor()},{disableBox:!0}),z2:10}),p=u.getBoundingRect(),n=h.get("subtext"),i=new v.ZP({style:(0,W.Lr)(O,{text:n,fill:O.getTextColor(),y:p.height+h.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),t=h.get("link"),r=h.get("sublink"),e=h.get("triggerEvent",!0);u.silent=!t&&!e,i.silent=!r&&!e,t&&u.on("click",function(){(0,A.MI)(t,"_"+h.get("target"))}),r&&i.on("click",function(){(0,A.MI)(r,"_"+h.get("subtarget"))}),(0,z.A)(u).eventData=(0,z.A)(i).eventData=e?{componentType:"title",componentIndex:h.componentIndex}:null,M.add(u),n&&M.add(i);var a=M.getBoundingRect(),s=h.getBoxLayoutParams();s.width=a.width,s.height=a.height;var o=(0,K.ME)(s,{width:_.getWidth(),height:_.getHeight()},h.get("padding"));d||(d=h.get("left")||h.get("right"),d==="middle"&&(d="center"),d==="right"?o.x+=o.width:d==="center"&&(o.x+=o.width/2)),f||(f=h.get("top")||h.get("bottom"),f==="center"&&(f="middle"),f==="bottom"?o.y+=o.height:f==="middle"&&(o.y+=o.height/2),f=f||"top"),M.x=o.x,M.y=o.y,M.markRedraw();var D={align:d,verticalAlign:f};u.setStyle(D),i.setStyle(D),a=M.getBoundingRect();var B=o.margin,I=h.getItemStyle(["color","opacity"]);I.fill=h.get("backgroundColor");var L=new F.Z({shape:{x:a.x-B[3],y:a.y-B[0],width:a.width+B[1]+B[3],height:a.height+B[0]+B[2],r:h.get("borderRadius")},style:I,subPixelOptimize:!0,silent:!0});M.add(L)}},E.type="title",E}(b.Z);function g(y){y.registerComponentModel(S),y.registerComponentView(m)}},5685:function(Zt,pt,P){"use strict";P.d(pt,{TX:function(){return A},BY:function(){return h},jT:function(){return f},d_:function(){return u},iv:function(){return p}});var C=P(54058),k=P(78988),v=P(33051),F=P(98407),z=P(85669),W="line-height:1";function K(n,i){var t=n.color||"#6e7079",r=n.fontSize||12,e=n.fontWeight||"400",a=n.color||"#464646",s=n.fontSize||14,o=n.fontWeight||"900";return i==="html"?{nameStyle:"font-size:"+(0,C.F1)(r+"")+"px;color:"+(0,C.F1)(t)+";font-weight:"+(0,C.F1)(e+""),valueStyle:"font-size:"+(0,C.F1)(s+"")+"px;color:"+(0,C.F1)(a)+";font-weight:"+(0,C.F1)(o+"")}:{nameStyle:{fontSize:r,fill:t,fontWeight:e},valueStyle:{fontSize:s,fill:a,fontWeight:o}}}var R=[0,10,20,30],b=["",`
`,`

`,`


`];function A(n,i){return i.type=n,i}function S(n){return n.type==="section"}function m(n){return S(n)?y:E}function g(n){if(S(n)){var i=0,t=n.blocks.length,r=t>1||t>0&&!n.noHeader;return(0,v.S6)(n.blocks,function(e){var a=g(e);a>=i&&(i=a+ +(r&&(!a||S(e)&&!e.noHeader)))}),i}return 0}function y(n,i,t,r){var e=i.noHeader,a=c(g(i)),s=[],o=i.blocks||[];(0,v.hu)(!o||(0,v.kJ)(o)),o=o||[];var D=n.orderMode;if(i.sortBlocks&&D){o=o.slice();var B={valueAsc:"asc",valueDesc:"desc"};if((0,v.RI)(B,D)){var I=new F.ID(B[D],null);o.sort(function(T,w){return I.evaluate(T.sortParam,w.sortParam)})}else D==="seriesDesc"&&o.reverse()}(0,v.S6)(o,function(T,w){var V=i.valueFormatter,N=m(T)(V?(0,v.l7)((0,v.l7)({},n),{valueFormatter:V}):n,T,w>0?a.html:0,r);N!=null&&s.push(N)});var L=n.renderMode==="richText"?s.join(a.richText):_(s.join(""),e?t:a.html);if(e)return L;var U=(0,k.uX)(i.header,"ordinal",n.useUTC),x=K(r,n.renderMode).nameStyle;return n.renderMode==="richText"?O(n,U,x)+a.richText+L:_('<div style="'+x+";"+W+';">'+(0,C.F1)(U)+"</div>"+L,t)}function E(n,i,t,r){var e=n.renderMode,a=i.noName,s=i.noValue,o=!i.markerType,D=i.name,B=n.useUTC,I=i.valueFormatter||n.valueFormatter||function(H){return H=(0,v.kJ)(H)?H:[H],(0,v.UI)(H,function(X,ut){return(0,k.uX)(X,(0,v.kJ)(x)?x[ut]:x,B)})};if(!(a&&s)){var L=o?"":n.markupStyleCreator.makeTooltipMarker(i.markerType,i.markerColor||"#333",e),U=a?"":(0,k.uX)(D,"ordinal",B),x=i.valueType,T=s?[]:I(i.value,i.dataIndex),w=!o||!a,V=!o&&a,N=K(r,e),G=N.nameStyle,Z=N.valueStyle;return e==="richText"?(o?"":L)+(a?"":O(n,U,G))+(s?"":d(n,T,w,V,Z)):_((o?"":L)+(a?"":M(U,!o,G))+(s?"":l(T,w,V,Z)),t)}}function h(n,i,t,r,e,a){if(!!n){var s=m(n),o={useUTC:e,renderMode:t,orderMode:r,markupStyleCreator:i,valueFormatter:n.valueFormatter};return s(o,n,0,a)}}function c(n){return{html:R[n],richText:b[n]}}function _(n,i){var t='<div style="clear:both"></div>',r="margin: "+i+"px 0 0";return'<div style="'+r+";"+W+';">'+n+t+"</div>"}function M(n,i,t){var r=i?"margin-left:2px":"";return'<span style="'+t+";"+r+'">'+(0,C.F1)(n)+"</span>"}function l(n,i,t,r){var e=t?"10px":"20px",a=i?"float:right;margin-left:"+e:"";return n=(0,v.kJ)(n)?n:[n],'<span style="'+a+";"+r+'">'+(0,v.UI)(n,function(s){return(0,C.F1)(s)}).join("&nbsp;&nbsp;")+"</span>"}function O(n,i,t){return n.markupStyleCreator.wrapRichTextStyle(i,t)}function d(n,i,t,r,e){var a=[e],s=r?10:20;return t&&a.push({padding:[0,0,0,s],align:"right"}),n.markupStyleCreator.wrapRichTextStyle((0,v.kJ)(i)?i.join("  "):i,a)}function f(n,i){var t=n.getData().getItemVisual(i,"style"),r=t[n.visualDrawType];return(0,k.Lz)(r)}function u(n,i){var t=n.get("padding");return t!=null?t:i==="richText"?[8,10]:10}var p=function(){function n(){this.richTextStyles={},this._nextStyleNameId=(0,z.jj)()}return n.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},n.prototype.makeTooltipMarker=function(i,t,r){var e=r==="richText"?this._generateStyleName():null,a=(0,k.A0)({color:t,type:i,renderMode:r,markerId:e});return(0,v.HD)(a)?a:(this.richTextStyles[e]=a.style,a.content)},n.prototype.wrapRichTextStyle=function(i,t){var r={};(0,v.kJ)(t)?(0,v.S6)(t,function(a){return(0,v.l7)(r,a)}):(0,v.l7)(r,t);var e=this._generateStyleName();return this.richTextStyles[e]=r,"{"+e+"|"+i+"}"},n}()},12950:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return p}});var C=P(33051),k=P(85669),v=P(80423),F=P(32234),z=P(88199),W=(0,F.Yf)();function K(n){return n.type==="category"?b(n):m(n)}function R(n,i){return n.type==="category"?S(n,i):{ticks:C.UI(n.scale.getTicks(),function(t){return t.value})}}function b(n){var i=n.getLabelModel(),t=A(n,i);return!i.get("show")||n.scale.isBlank()?{labels:[],labelCategoryInterval:t.labelCategoryInterval}:t}function A(n,i){var t=g(n,"labels"),r=(0,z.rk)(i),e=y(t,r);if(e)return e;var a,s;return C.mf(r)?a=l(n,r):(s=r==="auto"?h(n):r,a=M(n,s)),E(t,r,{labels:a,labelCategoryInterval:s})}function S(n,i){var t=g(n,"ticks"),r=(0,z.rk)(i),e=y(t,r);if(e)return e;var a,s;if((!i.get("show")||n.scale.isBlank())&&(a=[]),C.mf(r))a=l(n,r,!0);else if(r==="auto"){var o=A(n,n.getLabelModel());s=o.labelCategoryInterval,a=C.UI(o.labels,function(D){return D.tickValue})}else s=r,a=M(n,s,!0);return E(t,r,{ticks:a,tickCategoryInterval:s})}function m(n){var i=n.scale.getTicks(),t=(0,z.J9)(n);return{labels:C.UI(i,function(r,e){return{level:r.level,formattedLabel:t(r,e),rawLabel:n.scale.getLabel(r),tickValue:r.value}})}}function g(n,i){return W(n)[i]||(W(n)[i]=[])}function y(n,i){for(var t=0;t<n.length;t++)if(n[t].key===i)return n[t].value}function E(n,i,t){return n.push({key:i,value:t}),t}function h(n){var i=W(n).autoInterval;return i!=null?i:W(n).autoInterval=n.calculateCategoryInterval()}function c(n){var i=_(n),t=(0,z.J9)(n),r=(i.axisRotate-i.labelRotate)/180*Math.PI,e=n.scale,a=e.getExtent(),s=e.count();if(a[1]-a[0]<1)return 0;var o=1;s>40&&(o=Math.max(1,Math.floor(s/40)));for(var D=a[0],B=n.dataToCoord(D+1)-n.dataToCoord(D),I=Math.abs(B*Math.cos(r)),L=Math.abs(B*Math.sin(r)),U=0,x=0;D<=a[1];D+=o){var T=0,w=0,V=v.lP(t({value:D}),i.font,"center","top");T=V.width*1.3,w=V.height*1.3,U=Math.max(U,T,7),x=Math.max(x,w,7)}var N=U/I,G=x/L;isNaN(N)&&(N=Infinity),isNaN(G)&&(G=Infinity);var Z=Math.max(0,Math.floor(Math.min(N,G))),H=W(n.model),X=n.getExtent(),ut=H.lastAutoInterval,lt=H.lastTickCount;return ut!=null&&lt!=null&&Math.abs(ut-Z)<=1&&Math.abs(lt-s)<=1&&ut>Z&&H.axisExtent0===X[0]&&H.axisExtent1===X[1]?Z=ut:(H.lastTickCount=s,H.lastAutoInterval=Z,H.axisExtent0=X[0],H.axisExtent1=X[1]),Z}function _(n){var i=n.getLabelModel();return{axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:i.get("rotate")||0,font:i.getFont()}}function M(n,i,t){var r=(0,z.J9)(n),e=n.scale,a=e.getExtent(),s=n.getLabelModel(),o=[],D=Math.max((i||0)+1,1),B=a[0],I=e.count();B!==0&&D>1&&I/D>2&&(B=Math.round(Math.ceil(B/D)*D));var L=(0,z.WY)(n),U=s.get("showMinLabel")||L,x=s.get("showMaxLabel")||L;U&&B!==a[0]&&w(a[0]);for(var T=B;T<=a[1];T+=D)w(T);x&&T-D!==a[1]&&w(a[1]);function w(V){var N={value:V};o.push(t?V:{formattedLabel:r(N),rawLabel:e.getLabel(N),tickValue:V})}return o}function l(n,i,t){var r=n.scale,e=(0,z.J9)(n),a=[];return C.S6(r.getTicks(),function(s){var o=r.getLabel(s),D=s.value;i(s.value,o)&&a.push(t?D:{formattedLabel:e(s),rawLabel:o,tickValue:D})}),a}var O=[0,1],d=function(){function n(i,t,r){this.onBand=!1,this.inverse=!1,this.dim=i,this.scale=t,this._extent=r||[0,0]}return n.prototype.contain=function(i){var t=this._extent,r=Math.min(t[0],t[1]),e=Math.max(t[0],t[1]);return i>=r&&i<=e},n.prototype.containData=function(i){return this.scale.contain(i)},n.prototype.getExtent=function(){return this._extent.slice()},n.prototype.getPixelPrecision=function(i){return(0,k.M9)(i||this.scale.getExtent(),this._extent)},n.prototype.setExtent=function(i,t){var r=this._extent;r[0]=i,r[1]=t},n.prototype.dataToCoord=function(i,t){var r=this._extent,e=this.scale;return i=e.normalize(i),this.onBand&&e.type==="ordinal"&&(r=r.slice(),f(r,e.count())),(0,k.NU)(i,O,r,t)},n.prototype.coordToData=function(i,t){var r=this._extent,e=this.scale;this.onBand&&e.type==="ordinal"&&(r=r.slice(),f(r,e.count()));var a=(0,k.NU)(i,r,O,t);return this.scale.scale(a)},n.prototype.pointToData=function(i,t){},n.prototype.getTicksCoords=function(i){i=i||{};var t=i.tickModel||this.getTickModel(),r=R(this,t),e=r.ticks,a=(0,C.UI)(e,function(o){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(o):o),tickValue:o}},this),s=t.get("alignWithLabel");return u(this,a,s,i.clamp),a},n.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var i=this.model.getModel("minorTick"),t=i.get("splitNumber");t>0&&t<100||(t=5);var r=this.scale.getMinorTicks(t),e=(0,C.UI)(r,function(a){return(0,C.UI)(a,function(s){return{coord:this.dataToCoord(s),tickValue:s}},this)},this);return e},n.prototype.getViewLabels=function(){return K(this).labels},n.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},n.prototype.getTickModel=function(){return this.model.getModel("axisTick")},n.prototype.getBandWidth=function(){var i=this._extent,t=this.scale.getExtent(),r=t[1]-t[0]+(this.onBand?1:0);r===0&&(r=1);var e=Math.abs(i[1]-i[0]);return Math.abs(e)/r},n.prototype.calculateCategoryInterval=function(){return c(this)},n}();function f(n,i){var t=n[1]-n[0],r=i,e=t/r/2;n[0]+=e,n[1]-=e}function u(n,i,t,r){var e=i.length;if(!n.onBand||t||!e)return;var a=n.getExtent(),s,o;if(e===1)i[0].coord=a[0],s=i[1]={coord:a[1]};else{var D=i[e-1].tickValue-i[0].tickValue,B=(i[e-1].coord-i[0].coord)/D;(0,C.S6)(i,function(x){x.coord-=B/2});var I=n.scale.getExtent();o=1+I[1]-i[e-1].tickValue,s={coord:i[e-1].coord+B*o},i.push(s)}var L=a[0]>a[1];U(i[0].coord,a[0])&&(r?i[0].coord=a[0]:i.shift()),r&&U(a[0],i[0].coord)&&i.unshift({coord:a[0]}),U(a[1],s.coord)&&(r?s.coord=a[1]:i.pop()),r&&U(s.coord,a[1])&&i.push({coord:a[1]});function U(x,T){return x=(0,k.NM)(x),T=(0,k.NM)(T),L?x>T:x<T}}var p=d},28259:function(Zt,pt,P){"use strict";P.d(pt,{z:function(){return W}});var C=P(85669),k=P(70103),v=P(88199),F=P(65021),z=Math.log;function W(K,R,b){var A=k.Z.prototype,S=A.getTicks.call(b),m=A.getTicks.call(b,!0),g=S.length-1,y=A.getInterval.call(b),E=(0,v.Xv)(K,R),h=E.extent,c=E.fixMin,_=E.fixMax;if(K.type==="log"){var M=z(K.base);h=[z(h[0])/M,z(h[1])/M]}K.setExtent(h[0],h[1]),K.calcNiceExtent({splitNumber:g,fixMin:c,fixMax:_});var l=A.getExtent.call(K);c&&(h[0]=l[0]),_&&(h[1]=l[1]);var O=A.getInterval.call(K),d=h[0],f=h[1];if(c&&_)O=(f-d)/g;else if(c)for(f=h[0]+O*g;f<h[1]&&isFinite(f)&&isFinite(h[1]);)O=(0,F.r1)(O),f=h[0]+O*g;else if(_)for(d=h[1]-O*g;d>h[0]&&isFinite(d)&&isFinite(h[0]);)O=(0,F.r1)(O),d=h[1]-O*g;else{var u=K.getTicks().length-1;u>g&&(O=(0,F.r1)(O));var p=O*g;f=Math.ceil(h[1]/O)*O,d=(0,C.NM)(f-p),d<0&&h[0]>=0?(d=0,f=(0,C.NM)(p)):f>0&&h[1]<=0&&(f=0,d=-(0,C.NM)(p))}var n=(S[0].value-m[0].value)/y,i=(S[g].value-m[g].value)/y;if(A.setExtent.call(K,d+O*n,f+O*i),A.setInterval.call(K,O),(n||i)&&A.setNiceExtent.call(K,d+O,f-O),!1)var t}},66484:function(Zt,pt,P){"use strict";var C=P(33051),k={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},v=C.TS({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},k),F=C.TS({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},k),z=C.TS({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},F),W=C.ce({logBase:10},F);pt.Z={category:v,value:F,time:z,log:W}},88199:function(Zt,pt,P){"use strict";P.d(pt,{aG:function(){return w},Do:function(){return Z},DX:function(){return G},PY:function(){return lt},rk:function(){return X},Xv:function(){return U},Yb:function(){return V},J9:function(){return N},Jk:function(){return T},WY:function(){return ut},AH:function(){return ot}});var C=P(33051),k=P(18299),v=P(20380),F=P(51401),z=P(65021),W=function(q){(0,k.ZT)(tt,q);function tt(j){var et=q.call(this,j)||this;et.type="ordinal";var ct=et.getSetting("ordinalMeta");return ct||(ct=new F.Z({})),(0,C.kJ)(ct)&&(ct=new F.Z({categories:(0,C.UI)(ct,function(_t){return(0,C.Kn)(_t)?_t.value:_t})})),et._ordinalMeta=ct,et._extent=et.getSetting("extent")||[0,ct.categories.length-1],et}return tt.prototype.parse=function(j){return j==null?NaN:(0,C.HD)(j)?this._ordinalMeta.getOrdinal(j):Math.round(j)},tt.prototype.contain=function(j){return j=this.parse(j),z.XS(j,this._extent)&&this._ordinalMeta.categories[j]!=null},tt.prototype.normalize=function(j){return j=this._getTickNumber(this.parse(j)),z.Fv(j,this._extent)},tt.prototype.scale=function(j){return j=Math.round(z.bA(j,this._extent)),this.getRawOrdinalNumber(j)},tt.prototype.getTicks=function(){for(var j=[],et=this._extent,ct=et[0];ct<=et[1];)j.push({value:ct}),ct++;return j},tt.prototype.getMinorTicks=function(j){},tt.prototype.setSortInfo=function(j){if(j==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var et=j.ordinalNumbers,ct=this._ordinalNumbersByTick=[],_t=this._ticksByOrdinalNumber=[],St=0,At=this._ordinalMeta.categories.length,Ct=Math.min(At,et.length);St<Ct;++St){var Et=et[St];ct[St]=Et,_t[Et]=St}for(var Ot=0;St<At;++St){for(;_t[Ot]!=null;)Ot++;ct.push(Ot),_t[Ot]=St}},tt.prototype._getTickNumber=function(j){var et=this._ticksByOrdinalNumber;return et&&j>=0&&j<et.length?et[j]:j},tt.prototype.getRawOrdinalNumber=function(j){var et=this._ordinalNumbersByTick;return et&&j>=0&&j<et.length?et[j]:j},tt.prototype.getLabel=function(j){if(!this.isBlank()){var et=this.getRawOrdinalNumber(j.value),ct=this._ordinalMeta.categories[et];return ct==null?"":ct+""}},tt.prototype.count=function(){return this._extent[1]-this._extent[0]+1},tt.prototype.unionExtentFromData=function(j,et){this.unionExtent(j.getApproximateExtent(et))},tt.prototype.isInExtentRange=function(j){return j=this._getTickNumber(j),this._extent[0]<=j&&this._extent[1]>=j},tt.prototype.getOrdinalMeta=function(){return this._ordinalMeta},tt.prototype.calcNiceTicks=function(){},tt.prototype.calcNiceExtent=function(){},tt.type="ordinal",tt}(v.Z);v.Z.registerClass(W);var K=W,R=P(70103),b=P(79093),A=P(60479),S=P(85669),m=P(15015),g=function(q,tt,j,et){for(;j<et;){var ct=j+et>>>1;q[ct][1]<tt?j=ct+1:et=ct}return j},y=function(q){(0,k.ZT)(tt,q);function tt(j){var et=q.call(this,j)||this;return et.type="time",et}return tt.prototype.getLabel=function(j){var et=this.getSetting("useUTC");return(0,m.WU)(j.value,m.V8[(0,m.xC)((0,m.Tj)(this._minLevelUnit))]||m.V8.second,et,this.getSetting("locale"))},tt.prototype.getFormattedLabel=function(j,et,ct){var _t=this.getSetting("useUTC"),St=this.getSetting("locale");return(0,m.k7)(j,et,ct,St,_t)},tt.prototype.getTicks=function(){var j=this._interval,et=this._extent,ct=[];if(!j)return ct;ct.push({value:et[0],level:0});var _t=this.getSetting("useUTC"),St=f(this._minLevelUnit,this._approxInterval,_t,et);return ct=ct.concat(St),ct.push({value:et[1],level:0}),ct},tt.prototype.calcNiceExtent=function(j){var et=this._extent;if(et[0]===et[1]&&(et[0]-=m.s2,et[1]+=m.s2),et[1]===-Infinity&&et[0]===Infinity){var ct=new Date;et[1]=+new Date(ct.getFullYear(),ct.getMonth(),ct.getDate()),et[0]=et[1]-m.s2}this.calcNiceTicks(j.splitNumber,j.minInterval,j.maxInterval)},tt.prototype.calcNiceTicks=function(j,et,ct){j=j||10;var _t=this._extent,St=_t[1]-_t[0];this._approxInterval=St/j,et!=null&&this._approxInterval<et&&(this._approxInterval=et),ct!=null&&this._approxInterval>ct&&(this._approxInterval=ct);var At=E.length,Ct=Math.min(g(E,this._approxInterval,0,At),At-1);this._interval=E[Ct][1],this._minLevelUnit=E[Math.max(Ct-1,0)][0]},tt.prototype.parse=function(j){return(0,C.hj)(j)?j:+S.sG(j)},tt.prototype.contain=function(j){return z.XS(this.parse(j),this._extent)},tt.prototype.normalize=function(j){return z.Fv(this.parse(j),this._extent)},tt.prototype.scale=function(j){return z.bA(j,this._extent)},tt.type="time",tt}(R.Z),E=[["second",m.WT],["minute",m.yR],["hour",m.dV],["quarter-day",m.dV*6],["half-day",m.dV*12],["day",m.s2*1.2],["half-week",m.s2*3.5],["week",m.s2*7],["month",m.s2*31],["quarter",m.s2*95],["half-year",m.P5/2],["year",m.P5]];function h(q,tt,j,et){var ct=S.sG(tt),_t=S.sG(j),St=function(Ht){return(0,m.q5)(ct,Ht,et)===(0,m.q5)(_t,Ht,et)},At=function(){return St("year")},Ct=function(){return At()&&St("month")},Et=function(){return Ct()&&St("day")},Ot=function(){return Et()&&St("hour")},Wt=function(){return Ot()&&St("minute")},Ut=function(){return Wt()&&St("second")},Bt=function(){return Ut()&&St("millisecond")};switch(q){case"year":return At();case"month":return Ct();case"day":return Et();case"hour":return Ot();case"minute":return Wt();case"second":return Ut();case"millisecond":return Bt()}}function c(q,tt){return q/=m.s2,q>16?16:q>7.5?7:q>3.5?4:q>1.5?2:1}function _(q){var tt=30*m.s2;return q/=tt,q>6?6:q>3?3:q>2?2:1}function M(q){return q/=m.dV,q>12?12:q>6?6:q>3.5?4:q>2?2:1}function l(q,tt){return q/=tt?m.yR:m.WT,q>30?30:q>20?20:q>15?15:q>10?10:q>5?5:q>2?2:1}function O(q){return S.kx(q,!0)}function d(q,tt,j){var et=new Date(q);switch((0,m.Tj)(tt)){case"year":case"month":et[(0,m.vh)(j)](0);case"day":et[(0,m.f5)(j)](1);case"hour":et[(0,m.En)(j)](0);case"minute":et[(0,m.eN)(j)](0);case"second":et[(0,m.rM)(j)](0),et[(0,m.cb)(j)](0)}return et.getTime()}function f(q,tt,j,et){var ct=1e4,_t=m.FW,St=0;function At($,Q,ft,Tt,wt,bt,zt){for(var Qt=new Date(Q),qt=Q,re=Qt[Tt]();qt<ft&&qt<=et[1];)zt.push({value:qt}),re+=$,Qt[wt](re),qt=Qt.getTime();zt.push({value:qt,notAdd:!0})}function Ct($,Q,ft){var Tt=[],wt=!Q.length;if(!h((0,m.Tj)($),et[0],et[1],j)){wt&&(Q=[{value:d(new Date(et[0]),$,j)},{value:et[1]}]);for(var bt=0;bt<Q.length-1;bt++){var zt=Q[bt].value,Qt=Q[bt+1].value;if(zt!==Qt){var qt=void 0,re=void 0,ie=void 0,oe=!1;switch($){case"year":qt=Math.max(1,Math.round(tt/m.s2/365)),re=(0,m.sx)(j),ie=(0,m.xL)(j);break;case"half-year":case"quarter":case"month":qt=_(tt),re=(0,m.CW)(j),ie=(0,m.vh)(j);break;case"week":case"half-week":case"day":qt=c(tt,31),re=(0,m.xz)(j),ie=(0,m.f5)(j),oe=!0;break;case"half-day":case"quarter-day":case"hour":qt=M(tt),re=(0,m.Wp)(j),ie=(0,m.En)(j);break;case"minute":qt=l(tt,!0),re=(0,m.fn)(j),ie=(0,m.eN)(j);break;case"second":qt=l(tt,!1),re=(0,m.MV)(j),ie=(0,m.rM)(j);break;case"millisecond":qt=O(tt),re=(0,m.RZ)(j),ie=(0,m.cb)(j);break}At(qt,zt,Qt,re,ie,oe,Tt),$==="year"&&ft.length>1&&bt===0&&ft.unshift({value:ft[0].value-qt})}}for(var bt=0;bt<Tt.length;bt++)ft.push(Tt[bt]);return Tt}}for(var Et=[],Ot=[],Wt=0,Ut=0,Bt=0;Bt<_t.length&&St++<ct;++Bt){var Ht=(0,m.Tj)(_t[Bt]);if(!!(0,m.$K)(_t[Bt])){Ct(_t[Bt],Et[Et.length-1]||[],Ot);var kt=_t[Bt+1]?(0,m.Tj)(_t[Bt+1]):null;if(Ht!==kt){if(Ot.length){Ut=Wt,Ot.sort(function($,Q){return $.value-Q.value});for(var Xt=[],Vt=0;Vt<Ot.length;++Vt){var st=Ot[Vt].value;(Vt===0||Ot[Vt-1].value!==st)&&(Xt.push(Ot[Vt]),st>=et[0]&&st<=et[1]&&Wt++)}var gt=(et[1]-et[0])/tt;if(Wt>gt*1.5&&Ut>gt/1.5||(Et.push(Xt),Wt>gt||q===_t[Bt]))break}Ot=[]}}}for(var xt=(0,C.hX)((0,C.UI)(Et,function($){return(0,C.hX)($,function(Q){return Q.value>=et[0]&&Q.value<=et[1]&&!Q.notAdd})}),function($){return $.length>0}),Rt=[],dt=xt.length-1,Bt=0;Bt<xt.length;++Bt)for(var Mt=xt[Bt],vt=0;vt<Mt.length;++vt)Rt.push({value:Mt[vt].value,level:dt-Bt});Rt.sort(function($,Q){return $.value-Q.value});for(var Y=[],Bt=0;Bt<Rt.length;++Bt)(Bt===0||Rt[Bt].value!==Rt[Bt-1].value)&&Y.push(Rt[Bt]);return Y}v.Z.registerClass(y);var u=y,p=v.Z.prototype,n=R.Z.prototype,i=S.NM,t=Math.floor,r=Math.ceil,e=Math.pow,a=Math.log,s=function(q){(0,k.ZT)(tt,q);function tt(){var j=q!==null&&q.apply(this,arguments)||this;return j.type="log",j.base=10,j._originalScale=new R.Z,j._interval=0,j}return tt.prototype.getTicks=function(j){var et=this._originalScale,ct=this._extent,_t=et.getExtent(),St=n.getTicks.call(this,j);return C.UI(St,function(At){var Ct=At.value,Et=S.NM(e(this.base,Ct));return Et=Ct===ct[0]&&this._fixMin?D(Et,_t[0]):Et,Et=Ct===ct[1]&&this._fixMax?D(Et,_t[1]):Et,{value:Et}},this)},tt.prototype.setExtent=function(j,et){var ct=a(this.base);j=a(Math.max(0,j))/ct,et=a(Math.max(0,et))/ct,n.setExtent.call(this,j,et)},tt.prototype.getExtent=function(){var j=this.base,et=p.getExtent.call(this);et[0]=e(j,et[0]),et[1]=e(j,et[1]);var ct=this._originalScale,_t=ct.getExtent();return this._fixMin&&(et[0]=D(et[0],_t[0])),this._fixMax&&(et[1]=D(et[1],_t[1])),et},tt.prototype.unionExtent=function(j){this._originalScale.unionExtent(j);var et=this.base;j[0]=a(j[0])/a(et),j[1]=a(j[1])/a(et),p.unionExtent.call(this,j)},tt.prototype.unionExtentFromData=function(j,et){this.unionExtent(j.getApproximateExtent(et))},tt.prototype.calcNiceTicks=function(j){j=j||10;var et=this._extent,ct=et[1]-et[0];if(!(ct===Infinity||ct<=0)){var _t=S.Xd(ct),St=j/ct*_t;for(St<=.5&&(_t*=10);!isNaN(_t)&&Math.abs(_t)<1&&Math.abs(_t)>0;)_t*=10;var At=[S.NM(r(et[0]/_t)*_t),S.NM(t(et[1]/_t)*_t)];this._interval=_t,this._niceExtent=At}},tt.prototype.calcNiceExtent=function(j){n.calcNiceExtent.call(this,j),this._fixMin=j.fixMin,this._fixMax=j.fixMax},tt.prototype.parse=function(j){return j},tt.prototype.contain=function(j){return j=a(j)/a(this.base),z.XS(j,this._extent)},tt.prototype.normalize=function(j){return j=a(j)/a(this.base),z.Fv(j,this._extent)},tt.prototype.scale=function(j){return j=z.bA(j,this._extent),e(this.base,j)},tt.type="log",tt}(v.Z),o=s.prototype;o.getMinorTicks=n.getMinorTicks,o.getLabel=n.getLabel;function D(q,tt){return i(q,S.p8(tt))}v.Z.registerClass(s);var B=s,I=P(99936),L=P(38986);function U(q,tt){var j=q.type,et=(0,L.Qw)(q,tt,q.getExtent()).calculate();q.setBlank(et.isBlank);var ct=et.min,_t=et.max,St=tt.ecModel;if(St&&j==="time"){var At=(0,b.Ge)("bar",St),Ct=!1;if(C.S6(At,function(Wt){Ct=Ct||Wt.getBaseAxis()===tt.axis}),Ct){var Et=(0,b.My)(At),Ot=x(ct,_t,tt,Et);ct=Ot.min,_t=Ot.max}}return{extent:[ct,_t],fixMin:et.minFixed,fixMax:et.maxFixed}}function x(q,tt,j,et){var ct=j.axis.getExtent(),_t=ct[1]-ct[0],St=(0,b.G_)(et,j.axis);if(St===void 0)return{min:q,max:tt};var At=Infinity;C.S6(St,function(Bt){At=Math.min(Bt.offset,At)});var Ct=-Infinity;C.S6(St,function(Bt){Ct=Math.max(Bt.offset+Bt.width,Ct)}),At=Math.abs(At),Ct=Math.abs(Ct);var Et=At+Ct,Ot=tt-q,Wt=1-(At+Ct)/_t,Ut=Ot/Wt-Ot;return tt+=Ut*(Ct/Et),q-=Ut*(At/Et),{min:q,max:tt}}function T(q,tt){var j=tt,et=U(q,j),ct=et.extent,_t=j.get("splitNumber");q instanceof B&&(q.base=j.get("logBase"));var St=q.type,At=j.get("interval"),Ct=St==="interval"||St==="time";q.setExtent(ct[0],ct[1]),q.calcNiceExtent({splitNumber:_t,fixMin:et.fixMin,fixMax:et.fixMax,minInterval:Ct?j.get("minInterval"):null,maxInterval:Ct?j.get("maxInterval"):null}),At!=null&&q.setInterval&&q.setInterval(At)}function w(q,tt){if(tt=tt||q.get("type"),tt)switch(tt){case"category":return new K({ordinalMeta:q.getOrdinalMeta?q.getOrdinalMeta():q.getCategories(),extent:[Infinity,-Infinity]});case"time":return new u({locale:q.ecModel.getLocaleModel(),useUTC:q.ecModel.get("useUTC")});default:return new(v.Z.getClass(tt)||R.Z)}}function V(q){var tt=q.scale.getExtent(),j=tt[0],et=tt[1];return!(j>0&&et>0||j<0&&et<0)}function N(q){var tt=q.getLabelModel().get("formatter"),j=q.type==="category"?q.scale.getExtent()[0]:null;return q.scale.type==="time"?function(et){return function(ct,_t){return q.scale.getFormattedLabel(ct,_t,et)}}(tt):C.HD(tt)?function(et){return function(ct){var _t=q.scale.getLabel(ct),St=et.replace("{value}",_t!=null?_t:"");return St}}(tt):C.mf(tt)?function(et){return function(ct,_t){return j!=null&&(_t=ct.value-j),et(G(q,ct),_t,ct.level!=null?{level:ct.level}:null)}}(tt):function(et){return q.scale.getLabel(et)}}function G(q,tt){return q.type==="category"?q.scale.getLabel(tt):tt.value}function Z(q){var tt=q.model,j=q.scale;if(!(!tt.get(["axisLabel","show"])||j.isBlank())){var et,ct,_t=j.getExtent();j instanceof K?ct=j.count():(et=j.getTicks(),ct=et.length);var St=q.getLabelModel(),At=N(q),Ct,Et=1;ct>40&&(Et=Math.ceil(ct/40));for(var Ot=0;Ot<ct;Ot+=Et){var Wt=et?et[Ot]:{value:_t[0]+Ot},Ut=At(Wt,Ot),Bt=St.getTextRect(Ut),Ht=H(Bt,St.get("rotate")||0);Ct?Ct.union(Ht):Ct=Ht}return Ct}}function H(q,tt){var j=tt*Math.PI/180,et=q.width,ct=q.height,_t=et*Math.abs(Math.cos(j))+Math.abs(ct*Math.sin(j)),St=et*Math.abs(Math.sin(j))+Math.abs(ct*Math.cos(j)),At=new A.Z(q.x,q.y,_t,St);return At}function X(q){var tt=q.get("interval");return tt==null?"auto":tt}function ut(q){return q.type==="category"&&X(q.getLabelModel())===0}function lt(q,tt){var j={};return C.S6(q.mapDimensionsAll(tt),function(et){j[(0,I.IR)(q,et)]=!0}),C.XP(j)}function ot(q,tt,j){tt&&C.S6(lt(tt,j),function(et){var ct=tt.getApproximateExtent(et);ct[0]<q[0]&&(q[0]=ct[0]),ct[1]>q[1]&&(q[1]=ct[1])})}},16650:function(Zt,pt,P){"use strict";P.d(pt,{W:function(){return C}});var C=function(){function k(){}return k.prototype.getNeedCrossZero=function(){var v=this.option;return!v.scale},k.prototype.getCoordSysModel=function(){},k}()},38986:function(Zt,pt,P){"use strict";P.d(pt,{Qw:function(){return W}});var C=P(33051),k=P(80423),v=function(){function R(b,A,S){this._prepareParams(b,A,S)}return R.prototype._prepareParams=function(b,A,S){S[1]<S[0]&&(S=[NaN,NaN]),this._dataMin=S[0],this._dataMax=S[1];var m=this._isOrdinal=b.type==="ordinal";this._needCrossZero=b.type==="interval"&&A.getNeedCrossZero&&A.getNeedCrossZero();var g=this._modelMinRaw=A.get("min",!0);(0,C.mf)(g)?this._modelMinNum=K(b,g({min:S[0],max:S[1]})):g!=="dataMin"&&(this._modelMinNum=K(b,g));var y=this._modelMaxRaw=A.get("max",!0);if((0,C.mf)(y)?this._modelMaxNum=K(b,y({min:S[0],max:S[1]})):y!=="dataMax"&&(this._modelMaxNum=K(b,y)),m)this._axisDataLen=A.getCategories().length;else{var E=A.get("boundaryGap"),h=(0,C.kJ)(E)?E:[E||0,E||0];typeof h[0]=="boolean"||typeof h[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[(0,k.GM)(h[0],1),(0,k.GM)(h[1],1)]}},R.prototype.calculate=function(){var b=this._isOrdinal,A=this._dataMin,S=this._dataMax,m=this._axisDataLen,g=this._boundaryGapInner,y=b?null:S-A||Math.abs(A),E=this._modelMinRaw==="dataMin"?A:this._modelMinNum,h=this._modelMaxRaw==="dataMax"?S:this._modelMaxNum,c=E!=null,_=h!=null;E==null&&(E=b?m?0:NaN:A-g[0]*y),h==null&&(h=b?m?m-1:NaN:S+g[1]*y),(E==null||!isFinite(E))&&(E=NaN),(h==null||!isFinite(h))&&(h=NaN);var M=(0,C.Bu)(E)||(0,C.Bu)(h)||b&&!m;this._needCrossZero&&(E>0&&h>0&&!c&&(E=0),E<0&&h<0&&!_&&(h=0));var l=this._determinedMin,O=this._determinedMax;return l!=null&&(E=l,c=!0),O!=null&&(h=O,_=!0),{min:E,max:h,minFixed:c,maxFixed:_,isBlank:M}},R.prototype.modifyDataMinMax=function(b,A){this[z[b]]=A},R.prototype.setDeterminedMinMax=function(b,A){var S=F[b];this[S]=A},R.prototype.freeze=function(){this.frozen=!0},R}(),F={min:"_determinedMin",max:"_determinedMax"},z={min:"_dataMin",max:"_dataMax"};function W(R,b,A){var S=R.rawExtentInfo;return S||(S=new v(R,b,A),R.rawExtentInfo=S,S)}function K(R,b){return b==null?null:(0,C.Bu)(b)?NaN:R.parse(b)}},54267:function(Zt,pt,P){"use strict";var C=P(33051),k={},v=function(){function F(){this._coordinateSystems=[]}return F.prototype.create=function(z,W){var K=[];C.S6(k,function(R,b){var A=R.create(z,W);K=K.concat(A||[])}),this._coordinateSystems=K},F.prototype.update=function(z,W){C.S6(this._coordinateSystems,function(K){K.update&&K.update(z,W)})},F.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},F.register=function(z,W){k[z]=W},F.get=function(z){return k[z]},F}();pt.Z=v},81615:function(Zt,pt,P){"use strict";P.d(pt,{Hr:function(){return bn},S1:function(){return Rn},zl:function(){return be},RS:function(){return Un},qR:function(){return Fn},yn:function(){return Hr},je:function(){return kn},sq:function(){return Bn},Br:function(){return Wn},ds:function(){return zr},Pu:function(){return Kr},OB:function(){return Nn},YK:function(){return sr},Og:function(){return De}});var C=P(18299),k=P(99448),v=P(33051),F=P(66387),z=P(19455),W=P(23510),K=P(32234),R=P(1497),b=P(98071),A="";typeof navigator!="undefined"&&(A=navigator.platform||"");var S="rgba(0, 0, 0, 0.2)",m={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:S,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:S,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:S,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:S,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:S,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:S,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:A.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},g=P(61772),y=P(82468),E=P(75494),h,c,_,M="\0_ec_inner",l=1,O={grid:"GridComponent",polar:"PolarComponent",geo:"GeoComponent",singleAxis:"SingleAxisComponent",parallel:"ParallelComponent",calendar:"CalendarComponent",graphic:"GraphicComponent",toolbox:"ToolboxComponent",tooltip:"TooltipComponent",axisPointer:"AxisPointerComponent",brush:"BrushComponent",title:"TitleComponent",timeline:"TimelineComponent",markPoint:"MarkPointComponent",markLine:"MarkLineComponent",markArea:"MarkAreaComponent",legend:"LegendComponent",dataZoom:"DataZoomComponent",visualMap:"VisualMapComponent",xAxis:"GridComponent",yAxis:"GridComponent",angleAxis:"PolarComponent",radiusAxis:"PolarComponent"},d={line:"LineChart",bar:"BarChart",pie:"PieChart",scatter:"ScatterChart",radar:"RadarChart",map:"MapChart",tree:"TreeChart",treemap:"TreemapChart",graph:"GraphChart",gauge:"GaugeChart",funnel:"FunnelChart",parallel:"ParallelChart",sankey:"SankeyChart",boxplot:"BoxplotChart",candlestick:"CandlestickChart",effectScatter:"EffectScatterChart",lines:"LinesChart",heatmap:"HeatmapChart",pictorialBar:"PictorialBarChart",themeRiver:"ThemeRiverChart",sunburst:"SunburstChart",custom:"CustomChart"},f={};function u(rt){each(rt,function(it,J){if(!ComponentModel.hasClass(J)){var nt=O[J];nt&&!f[nt]&&(error("Component "+J+` is used but not imported.
import { `+nt+` } from 'echarts/components';
echarts.use([`+nt+"]);"),f[nt]=!0)}})}var p=function(rt){(0,C.ZT)(it,rt);function it(){return rt!==null&&rt.apply(this,arguments)||this}return it.prototype.init=function(J,nt,at,ht,mt,Ft){ht=ht||{},this.option=null,this._theme=new R.Z(ht),this._locale=new R.Z(mt),this._optionManager=Ft},it.prototype.setOption=function(J,nt,at){var ht=e(nt);this._optionManager.setOption(J,at,ht),this._resetOption(null,ht)},it.prototype.resetOption=function(J,nt){return this._resetOption(J,e(nt))},it.prototype._resetOption=function(J,nt){var at=!1,ht=this._optionManager;if(!J||J==="recreate"){var mt=ht.mountOption(J==="recreate");!this.option||J==="recreate"?_(this,mt):(this.restoreData(),this._mergeOption(mt,nt)),at=!0}if((J==="timeline"||J==="media")&&this.restoreData(),!J||J==="recreate"||J==="timeline"){var Ft=ht.getTimelineOption(this);Ft&&(at=!0,this._mergeOption(Ft,nt))}if(!J||J==="recreate"||J==="media"){var It=ht.getMediaOption(this);It.length&&(0,v.S6)(It,function(Gt){at=!0,this._mergeOption(Gt,nt)},this)}return at},it.prototype.mergeOption=function(J){this._mergeOption(J,null)},it.prototype._mergeOption=function(J,nt){var at=this.option,ht=this._componentsMap,mt=this._componentsCount,Ft=[],It=(0,v.kW)(),Gt=nt&&nt.replaceMergeMainTypeMap;(0,g.md)(this),(0,v.S6)(J,function(yt,Pt){yt!=null&&(b.Z.hasClass(Pt)?Pt&&(Ft.push(Pt),It.set(Pt,!0)):at[Pt]=at[Pt]==null?(0,v.d9)(yt):(0,v.TS)(at[Pt],yt,!0))}),Gt&&Gt.each(function(yt,Pt){b.Z.hasClass(Pt)&&!It.get(Pt)&&(Ft.push(Pt),It.set(Pt,!0))}),b.Z.topologicalTravel(Ft,b.Z.getAllClassMainTypes(),Yt,this);function Yt(yt){var Pt=(0,y.R)(this,yt,K.kF(J[yt])),Dt=ht.get(yt),Lt=Dt?Gt&&Gt.get(yt)?"replaceMerge":"normalMerge":"replaceAll",Nt=K.ab(Dt,Pt,Lt);K.O0(Nt,yt,b.Z),at[yt]=null,ht.set(yt,null),mt.set(yt,0);var Kt=[],jt=[],$t=0,Jt,te;(0,v.S6)(Nt,function(ne,ee){var ae=ne.existing,fe=ne.newOption;if(!fe)ae&&(ae.mergeOption({},this),ae.optionUpdated({},!1));else{var se=yt==="series",ve=b.Z.getClass(yt,ne.keyInfo.subType,!se);if(!ve){if(!1)var ye,lr;return}if(yt==="tooltip"){if(Jt)return;Jt=!0}if(ae&&ae.constructor===ve)ae.name=ne.keyInfo.name,ae.mergeOption(fe,this),ae.optionUpdated(fe,!1);else{var Ie=(0,v.l7)({componentIndex:ee},ne.keyInfo);ae=new ve(fe,this,this,Ie),(0,v.l7)(ae,Ie),ne.brandNew&&(ae.__requireNewView=!0),ae.init(fe,this,this),ae.optionUpdated(null,!0)}}ae?(Kt.push(ae.option),jt.push(ae),$t++):(Kt.push(void 0),jt.push(void 0))},this),at[yt]=Kt,ht.set(yt,jt),mt.set(yt,$t),yt==="series"&&h(this)}this._seriesIndices||h(this)},it.prototype.getOption=function(){var J=(0,v.d9)(this.option);return(0,v.S6)(J,function(nt,at){if(b.Z.hasClass(at)){for(var ht=K.kF(nt),mt=ht.length,Ft=!1,It=mt-1;It>=0;It--)ht[It]&&!K.lY(ht[It])?Ft=!0:(ht[It]=null,!Ft&&mt--);ht.length=mt,J[at]=ht}}),delete J[M],J},it.prototype.getTheme=function(){return this._theme},it.prototype.getLocaleModel=function(){return this._locale},it.prototype.setUpdatePayload=function(J){this._payload=J},it.prototype.getUpdatePayload=function(){return this._payload},it.prototype.getComponent=function(J,nt){var at=this._componentsMap.get(J);if(at){var ht=at[nt||0];if(ht)return ht;if(nt==null){for(var mt=0;mt<at.length;mt++)if(at[mt])return at[mt]}}},it.prototype.queryComponents=function(J){var nt=J.mainType;if(!nt)return[];var at=J.index,ht=J.id,mt=J.name,Ft=this._componentsMap.get(nt);if(!Ft||!Ft.length)return[];var It;return at!=null?(It=[],(0,v.S6)(K.kF(at),function(Gt){Ft[Gt]&&It.push(Ft[Gt])})):ht!=null?It=t("id",ht,Ft):mt!=null?It=t("name",mt,Ft):It=(0,v.hX)(Ft,function(Gt){return!!Gt}),r(It,J)},it.prototype.findComponents=function(J){var nt=J.query,at=J.mainType,ht=Ft(nt),mt=ht?this.queryComponents(ht):(0,v.hX)(this._componentsMap.get(at),function(Gt){return!!Gt});return It(r(mt,J));function Ft(Gt){var Yt=at+"Index",yt=at+"Id",Pt=at+"Name";return Gt&&(Gt[Yt]!=null||Gt[yt]!=null||Gt[Pt]!=null)?{mainType:at,index:Gt[Yt],id:Gt[yt],name:Gt[Pt]}:null}function It(Gt){return J.filter?(0,v.hX)(Gt,J.filter):Gt}},it.prototype.eachComponent=function(J,nt,at){var ht=this._componentsMap;if((0,v.mf)(J)){var mt=nt,Ft=J;ht.each(function(yt,Pt){for(var Dt=0;yt&&Dt<yt.length;Dt++){var Lt=yt[Dt];Lt&&Ft.call(mt,Pt,Lt,Lt.componentIndex)}})}else for(var It=(0,v.HD)(J)?ht.get(J):(0,v.Kn)(J)?this.findComponents(J):null,Gt=0;It&&Gt<It.length;Gt++){var Yt=It[Gt];Yt&&nt.call(at,Yt,Yt.componentIndex)}},it.prototype.getSeriesByName=function(J){var nt=K.U5(J,null);return(0,v.hX)(this._componentsMap.get("series"),function(at){return!!at&&nt!=null&&at.name===nt})},it.prototype.getSeriesByIndex=function(J){return this._componentsMap.get("series")[J]},it.prototype.getSeriesByType=function(J){return(0,v.hX)(this._componentsMap.get("series"),function(nt){return!!nt&&nt.subType===J})},it.prototype.getSeries=function(){return(0,v.hX)(this._componentsMap.get("series"),function(J){return!!J})},it.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},it.prototype.eachSeries=function(J,nt){c(this),(0,v.S6)(this._seriesIndices,function(at){var ht=this._componentsMap.get("series")[at];J.call(nt,ht,at)},this)},it.prototype.eachRawSeries=function(J,nt){(0,v.S6)(this._componentsMap.get("series"),function(at){at&&J.call(nt,at,at.componentIndex)})},it.prototype.eachSeriesByType=function(J,nt,at){c(this),(0,v.S6)(this._seriesIndices,function(ht){var mt=this._componentsMap.get("series")[ht];mt.subType===J&&nt.call(at,mt,ht)},this)},it.prototype.eachRawSeriesByType=function(J,nt,at){return(0,v.S6)(this.getSeriesByType(J),nt,at)},it.prototype.isSeriesFiltered=function(J){return c(this),this._seriesIndicesMap.get(J.componentIndex)==null},it.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},it.prototype.filterSeries=function(J,nt){c(this);var at=[];(0,v.S6)(this._seriesIndices,function(ht){var mt=this._componentsMap.get("series")[ht];J.call(nt,mt,ht)&&at.push(ht)},this),this._seriesIndices=at,this._seriesIndicesMap=(0,v.kW)(at)},it.prototype.restoreData=function(J){h(this);var nt=this._componentsMap,at=[];nt.each(function(ht,mt){b.Z.hasClass(mt)&&at.push(mt)}),b.Z.topologicalTravel(at,b.Z.getAllClassMainTypes(),function(ht){(0,v.S6)(nt.get(ht),function(mt){mt&&(ht!=="series"||!n(mt,J))&&mt.restoreData()})})},it.internalField=function(){h=function(J){var nt=J._seriesIndices=[];(0,v.S6)(J._componentsMap.get("series"),function(at){at&&nt.push(at.componentIndex)}),J._seriesIndicesMap=(0,v.kW)(nt)},c=function(J){},_=function(J,nt){J.option={},J.option[M]=l,J._componentsMap=(0,v.kW)({series:[]}),J._componentsCount=(0,v.kW)();var at=nt.aria;(0,v.Kn)(at)&&at.enabled==null&&(at.enabled=!0),i(nt,J._theme.option),(0,v.TS)(nt,m,!1),J._mergeOption(nt,null)}}(),it}(R.Z);function n(rt,it){if(it){var J=it.seriesIndex,nt=it.seriesId,at=it.seriesName;return J!=null&&rt.componentIndex!==J||nt!=null&&rt.id!==nt||at!=null&&rt.name!==at}}function i(rt,it){var J=rt.color&&!rt.colorLayer;(0,v.S6)(it,function(nt,at){at==="colorLayer"&&J||b.Z.hasClass(at)||(typeof nt=="object"?rt[at]=rt[at]?(0,v.TS)(rt[at],nt,!1):(0,v.d9)(nt):rt[at]==null&&(rt[at]=nt))})}function t(rt,it,J){if((0,v.kJ)(it)){var nt=(0,v.kW)();return(0,v.S6)(it,function(ht){if(ht!=null){var mt=K.U5(ht,null);mt!=null&&nt.set(ht,!0)}}),(0,v.hX)(J,function(ht){return ht&&nt.get(ht[rt])})}else{var at=K.U5(it,null);return(0,v.hX)(J,function(ht){return ht&&at!=null&&ht[rt]===at})}}function r(rt,it){return it.hasOwnProperty("subType")?(0,v.hX)(rt,function(J){return J&&J.subType===it.subType}):rt}function e(rt){var it=(0,v.kW)();return rt&&(0,v.S6)(K.kF(rt.replaceMerge),function(J){it.set(J,!0)}),{replaceMergeMainTypeMap:it}}(0,v.jB)(p,E._);var a=p,s=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],o=function(){function rt(it){v.S6(s,function(J){this[J]=v.ak(it[J],it)},this)}return rt}(),D=o,B=P(54267),I=/^(min|max)?(.+)$/,L=function(){function rt(it){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=it}return rt.prototype.setOption=function(it,J,nt){it&&((0,v.S6)((0,K.kF)(it.series),function(mt){mt&&mt.data&&(0,v.fU)(mt.data)&&(0,v.s7)(mt.data)}),(0,v.S6)((0,K.kF)(it.dataset),function(mt){mt&&mt.source&&(0,v.fU)(mt.source)&&(0,v.s7)(mt.source)})),it=(0,v.d9)(it);var at=this._optionBackup,ht=U(it,J,!at);this._newBaseOption=ht.baseOption,at?(ht.timelineOptions.length&&(at.timelineOptions=ht.timelineOptions),ht.mediaList.length&&(at.mediaList=ht.mediaList),ht.mediaDefault&&(at.mediaDefault=ht.mediaDefault)):this._optionBackup=ht},rt.prototype.mountOption=function(it){var J=this._optionBackup;return this._timelineOptions=J.timelineOptions,this._mediaList=J.mediaList,this._mediaDefault=J.mediaDefault,this._currentMediaIndices=[],(0,v.d9)(it?J.baseOption:this._newBaseOption)},rt.prototype.getTimelineOption=function(it){var J,nt=this._timelineOptions;if(nt.length){var at=it.getComponent("timeline");at&&(J=(0,v.d9)(nt[at.getCurrentIndex()]))}return J},rt.prototype.getMediaOption=function(it){var J=this._api.getWidth(),nt=this._api.getHeight(),at=this._mediaList,ht=this._mediaDefault,mt=[],Ft=[];if(!at.length&&!ht)return Ft;for(var It=0,Gt=at.length;It<Gt;It++)x(at[It].query,J,nt)&&mt.push(It);return!mt.length&&ht&&(mt=[-1]),mt.length&&!w(mt,this._currentMediaIndices)&&(Ft=(0,v.UI)(mt,function(Yt){return(0,v.d9)(Yt===-1?ht.option:at[Yt].option)})),this._currentMediaIndices=mt,Ft},rt}();function U(rt,it,J){var nt=[],at,ht,mt=rt.baseOption,Ft=rt.timeline,It=rt.options,Gt=rt.media,Yt=!!rt.media,yt=!!(It||Ft||mt&&mt.timeline);mt?(ht=mt,ht.timeline||(ht.timeline=Ft)):((yt||Yt)&&(rt.options=rt.media=null),ht=rt),Yt&&(0,v.kJ)(Gt)&&(0,v.S6)(Gt,function(Dt){Dt&&Dt.option&&(Dt.query?nt.push(Dt):at||(at=Dt))}),Pt(ht),(0,v.S6)(It,function(Dt){return Pt(Dt)}),(0,v.S6)(nt,function(Dt){return Pt(Dt.option)});function Pt(Dt){(0,v.S6)(it,function(Lt){Lt(Dt,J)})}return{baseOption:ht,timelineOptions:It||[],mediaDefault:at,mediaList:nt}}function x(rt,it,J){var nt={width:it,height:J,aspectratio:it/J},at=!0;return(0,v.S6)(rt,function(ht,mt){var Ft=mt.match(I);if(!(!Ft||!Ft[1]||!Ft[2])){var It=Ft[1],Gt=Ft[2].toLowerCase();T(nt[Gt],ht,It)||(at=!1)}}),at}function T(rt,it,J){return J==="min"?rt>=it:J==="max"?rt<=it:rt===it}function w(rt,it){return rt.join(",")===it.join(",")}var V=L,N=v.S6,G=v.Kn,Z=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function H(rt){var it=rt&&rt.itemStyle;if(!!it)for(var J=0,nt=Z.length;J<nt;J++){var at=Z[J],ht=it.normal,mt=it.emphasis;ht&&ht[at]&&(rt[at]=rt[at]||{},rt[at].normal?v.TS(rt[at].normal,ht[at]):rt[at].normal=ht[at],ht[at]=null),mt&&mt[at]&&(rt[at]=rt[at]||{},rt[at].emphasis?v.TS(rt[at].emphasis,mt[at]):rt[at].emphasis=mt[at],mt[at]=null)}}function X(rt,it,J){if(rt&&rt[it]&&(rt[it].normal||rt[it].emphasis)){var nt=rt[it].normal,at=rt[it].emphasis;nt&&(J?(rt[it].normal=rt[it].emphasis=null,v.ce(rt[it],nt)):rt[it]=nt),at&&(rt.emphasis=rt.emphasis||{},rt.emphasis[it]=at,at.focus&&(rt.emphasis.focus=at.focus),at.blurScope&&(rt.emphasis.blurScope=at.blurScope))}}function ut(rt){X(rt,"itemStyle"),X(rt,"lineStyle"),X(rt,"areaStyle"),X(rt,"label"),X(rt,"labelLine"),X(rt,"upperLabel"),X(rt,"edgeLabel")}function lt(rt,it){var J=G(rt)&&rt[it],nt=G(J)&&J.textStyle;if(nt)for(var at=0,ht=K.Td.length;at<ht;at++){var mt=K.Td[at];nt.hasOwnProperty(mt)&&(J[mt]=nt[mt])}}function ot(rt){rt&&(ut(rt),lt(rt,"label"),rt.emphasis&&lt(rt.emphasis,"label"))}function q(rt){if(!!G(rt)){H(rt),ut(rt),lt(rt,"label"),lt(rt,"upperLabel"),lt(rt,"edgeLabel"),rt.emphasis&&(lt(rt.emphasis,"label"),lt(rt.emphasis,"upperLabel"),lt(rt.emphasis,"edgeLabel"));var it=rt.markPoint;it&&(H(it),ot(it));var J=rt.markLine;J&&(H(J),ot(J));var nt=rt.markArea;nt&&ot(nt);var at=rt.data;if(rt.type==="graph"){at=at||rt.nodes;var ht=rt.links||rt.edges;if(ht&&!v.fU(ht))for(var mt=0;mt<ht.length;mt++)ot(ht[mt]);v.S6(rt.categories,function(Gt){ut(Gt)})}if(at&&!v.fU(at))for(var mt=0;mt<at.length;mt++)ot(at[mt]);if(it=rt.markPoint,it&&it.data)for(var Ft=it.data,mt=0;mt<Ft.length;mt++)ot(Ft[mt]);if(J=rt.markLine,J&&J.data)for(var It=J.data,mt=0;mt<It.length;mt++)v.kJ(It[mt])?(ot(It[mt][0]),ot(It[mt][1])):ot(It[mt]);rt.type==="gauge"?(lt(rt,"axisLabel"),lt(rt,"title"),lt(rt,"detail")):rt.type==="treemap"?(X(rt.breadcrumb,"itemStyle"),v.S6(rt.levels,function(Gt){ut(Gt)})):rt.type==="tree"&&ut(rt.leaves)}}function tt(rt){return v.kJ(rt)?rt:rt?[rt]:[]}function j(rt){return(v.kJ(rt)?rt[0]:rt)||{}}function et(rt,it){N(tt(rt.series),function(nt){G(nt)&&q(nt)});var J=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];it&&J.push("valueAxis","categoryAxis","logAxis","timeAxis"),N(J,function(nt){N(tt(rt[nt]),function(at){at&&(lt(at,"axisLabel"),lt(at.axisPointer,"label"))})}),N(tt(rt.parallel),function(nt){var at=nt&&nt.parallelAxisDefault;lt(at,"axisLabel"),lt(at&&at.axisPointer,"label")}),N(tt(rt.calendar),function(nt){X(nt,"itemStyle"),lt(nt,"dayLabel"),lt(nt,"monthLabel"),lt(nt,"yearLabel")}),N(tt(rt.radar),function(nt){lt(nt,"name"),nt.name&&nt.axisName==null&&(nt.axisName=nt.name,delete nt.name),nt.nameGap!=null&&nt.axisNameGap==null&&(nt.axisNameGap=nt.nameGap,delete nt.nameGap)}),N(tt(rt.geo),function(nt){G(nt)&&(ot(nt),N(tt(nt.regions),function(at){ot(at)}))}),N(tt(rt.timeline),function(nt){ot(nt),X(nt,"label"),X(nt,"itemStyle"),X(nt,"controlStyle",!0);var at=nt.data;v.kJ(at)&&v.S6(at,function(ht){v.Kn(ht)&&(X(ht,"label"),X(ht,"itemStyle"))})}),N(tt(rt.toolbox),function(nt){X(nt,"iconStyle"),N(nt.feature,function(at){X(at,"iconStyle")})}),lt(j(rt.axisPointer),"label"),lt(j(rt.tooltip).axisPointer,"label")}function ct(rt,it){for(var J=it.split(","),nt=rt,at=0;at<J.length&&(nt=nt&&nt[J[at]],nt!=null);at++);return nt}function _t(rt,it,J,nt){for(var at=it.split(","),ht=rt,mt,Ft=0;Ft<at.length-1;Ft++)mt=at[Ft],ht[mt]==null&&(ht[mt]={}),ht=ht[mt];(nt||ht[at[Ft]]==null)&&(ht[at[Ft]]=J)}function St(rt){rt&&(0,v.S6)(At,function(it){it[0]in rt&&!(it[1]in rt)&&(rt[it[1]]=rt[it[0]])})}var At=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Ct=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Et=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Ot(rt){var it=rt&&rt.itemStyle;if(it)for(var J=0;J<Et.length;J++){var nt=Et[J][1],at=Et[J][0];it[nt]!=null&&(it[at]=it[nt])}}function Wt(rt){!rt||rt.alignTo==="edge"&&rt.margin!=null&&rt.edgeDistance==null&&(rt.edgeDistance=rt.margin)}function Ut(rt){!rt||rt.downplay&&!rt.blur&&(rt.blur=rt.downplay)}function Bt(rt){!rt||rt.focusNodeAdjacency!=null&&(rt.emphasis=rt.emphasis||{},rt.emphasis.focus==null&&(rt.emphasis.focus="adjacency"))}function Ht(rt,it){if(rt)for(var J=0;J<rt.length;J++)it(rt[J]),rt[J]&&Ht(rt[J].children,it)}function kt(rt,it){et(rt,it),rt.series=(0,K.kF)(rt.series),(0,v.S6)(rt.series,function(J){if(!!(0,v.Kn)(J)){var nt=J.type;if(nt==="line")J.clipOverflow!=null&&(J.clip=J.clipOverflow);else if(nt==="pie"||nt==="gauge"){J.clockWise!=null&&(J.clockwise=J.clockWise),Wt(J.label);var at=J.data;if(at&&!(0,v.fU)(at))for(var ht=0;ht<at.length;ht++)Wt(at[ht]);J.hoverOffset!=null&&(J.emphasis=J.emphasis||{},(J.emphasis.scaleSize=null)&&(J.emphasis.scaleSize=J.hoverOffset))}else if(nt==="gauge"){var mt=ct(J,"pointer.color");mt!=null&&_t(J,"itemStyle.color",mt)}else if(nt==="bar"){Ot(J),Ot(J.backgroundStyle),Ot(J.emphasis);var at=J.data;if(at&&!(0,v.fU)(at))for(var ht=0;ht<at.length;ht++)typeof at[ht]=="object"&&(Ot(at[ht]),Ot(at[ht]&&at[ht].emphasis))}else if(nt==="sunburst"){var Ft=J.highlightPolicy;Ft&&(J.emphasis=J.emphasis||{},J.emphasis.focus||(J.emphasis.focus=Ft)),Ut(J),Ht(J.data,Ut)}else nt==="graph"||nt==="sankey"?Bt(J):nt==="map"&&(J.mapType&&!J.map&&(J.map=J.mapType),J.mapLocation&&(0,v.ce)(J,J.mapLocation));J.hoverAnimation!=null&&(J.emphasis=J.emphasis||{},J.emphasis&&J.emphasis.scale==null&&(J.emphasis.scale=J.hoverAnimation)),St(J)}}),rt.dataRange&&(rt.visualMap=rt.dataRange),(0,v.S6)(Ct,function(J){var nt=rt[J];nt&&((0,v.kJ)(nt)||(nt=[nt]),(0,v.S6)(nt,function(at){St(at)}))})}var Xt=P(85669);function Vt(rt){var it=(0,v.kW)();rt.eachSeries(function(J){var nt=J.get("stack");if(nt){var at=it.get(nt)||it.set(nt,[]),ht=J.getData(),mt={stackResultDimension:ht.getCalculationInfo("stackResultDimension"),stackedOverDimension:ht.getCalculationInfo("stackedOverDimension"),stackedDimension:ht.getCalculationInfo("stackedDimension"),stackedByDimension:ht.getCalculationInfo("stackedByDimension"),isStackedByIndex:ht.getCalculationInfo("isStackedByIndex"),data:ht,seriesModel:J};if(!mt.stackedDimension||!(mt.isStackedByIndex||mt.stackedByDimension))return;at.length&&ht.setCalculationInfo("stackedOnSeries",at[at.length-1].seriesModel),at.push(mt)}}),it.each(st)}function st(rt){(0,v.S6)(rt,function(it,J){var nt=[],at=[NaN,NaN],ht=[it.stackResultDimension,it.stackedOverDimension],mt=it.data,Ft=it.isStackedByIndex,It=it.seriesModel.get("stackStrategy")||"samesign";mt.modify(ht,function(Gt,Yt,yt){var Pt=mt.get(it.stackedDimension,yt);if(isNaN(Pt))return at;var Dt,Lt;Ft?Lt=mt.getRawIndex(yt):Dt=mt.get(it.stackedByDimension,yt);for(var Nt=NaN,Kt=J-1;Kt>=0;Kt--){var jt=rt[Kt];if(Ft||(Lt=jt.data.rawIndexOf(jt.stackedByDimension,Dt)),Lt>=0){var $t=jt.data.getByRawIndex(jt.stackResultDimension,Lt);if(It==="all"||It==="positive"&&$t>0||It==="negative"&&$t<0||It==="samesign"&&Pt>=0&&$t>0||It==="samesign"&&Pt<=0&&$t<0){Pt=(0,Xt.S$)(Pt,$t),Nt=$t;break}}}return nt[0]=Pt,nt[1]=Nt,nt})})}var gt=P(95761),xt=P(33166),Rt=P(75797),dt=P(35151),Mt=P(44535),vt=P(44292),Y=P(4665),$=P(30106),Q=P(26357),ft=P(270),Tt=P(59066),wt=P(89887),bt=P(77515),zt=(0,K.Yf)(),Qt={itemStyle:(0,Tt.Z)(wt.t,!0),lineStyle:(0,Tt.Z)(bt.v,!0)},qt={lineStyle:"stroke",itemStyle:"fill"};function re(rt,it){var J=rt.visualStyleMapper||Qt[it];return J||(console.warn("Unknown style type '"+it+"'."),Qt.itemStyle)}function ie(rt,it){var J=rt.visualDrawType||qt[it];return J||(console.warn("Unknown style type '"+it+"'."),"fill")}var oe={createOnAllSeries:!0,performRawSeries:!0,reset:function(rt,it){var J=rt.getData(),nt=rt.visualStyleAccessPath||"itemStyle",at=rt.getModel(nt),ht=re(rt,nt),mt=ht(at),Ft=at.getShallow("decal");Ft&&(J.setVisual("decal",Ft),Ft.dirty=!0);var It=ie(rt,nt),Gt=mt[It],Yt=(0,v.mf)(Gt)?Gt:null,yt=mt.fill==="auto"||mt.stroke==="auto";if(!mt[It]||Yt||yt){var Pt=rt.getColorFromPalette(rt.name,null,it.getSeriesCount());mt[It]||(mt[It]=Pt,J.setVisual("colorFromPalette",!0)),mt.fill=mt.fill==="auto"||(0,v.mf)(mt.fill)?Pt:mt.fill,mt.stroke=mt.stroke==="auto"||(0,v.mf)(mt.stroke)?Pt:mt.stroke}if(J.setVisual("style",mt),J.setVisual("drawType",It),!it.isSeriesFiltered(rt)&&Yt)return J.setVisual("colorFromPalette",!1),{dataEach:function(Dt,Lt){var Nt=rt.getDataParams(Lt),Kt=(0,v.l7)({},mt);Kt[It]=Yt(Nt),Dt.setItemVisual(Lt,"style",Kt)}}}},ue=new R.Z,Me={createOnAllSeries:!0,performRawSeries:!0,reset:function(rt,it){if(!(rt.ignoreStyleOnData||it.isSeriesFiltered(rt))){var J=rt.getData(),nt=rt.visualStyleAccessPath||"itemStyle",at=re(rt,nt),ht=J.getVisual("drawType");return{dataEach:J.hasItemOption?function(mt,Ft){var It=mt.getRawDataItem(Ft);if(It&&It[nt]){ue.option=It[nt];var Gt=at(ue),Yt=mt.ensureUniqueItemVisual(Ft,"style");(0,v.l7)(Yt,Gt),ue.option.decal&&(mt.setItemVisual(Ft,"decal",ue.option.decal),ue.option.decal.dirty=!0),ht in Gt&&mt.setItemVisual(Ft,"colorFromPalette",!1)}}:null}}}},Te={performRawSeries:!0,overallReset:function(rt){var it=(0,v.kW)();rt.eachSeries(function(J){var nt=J.getColorBy();if(!J.isColorBySeries()){var at=J.type+"-"+nt,ht=it.get(at);ht||(ht={},it.set(at,ht)),zt(J).scope=ht}}),rt.eachSeries(function(J){if(!(J.isColorBySeries()||rt.isSeriesFiltered(J))){var nt=J.getRawData(),at={},ht=J.getData(),mt=zt(J).scope,Ft=J.visualStyleAccessPath||"itemStyle",It=ie(J,Ft);ht.each(function(Gt){var Yt=ht.getRawIndex(Gt);at[Yt]=Gt}),nt.each(function(Gt){var Yt=at[Gt],yt=ht.getItemVisual(Yt,"colorFromPalette");if(yt){var Pt=ht.ensureUniqueItemVisual(Yt,"style"),Dt=nt.getName(Gt)||Gt+"",Lt=nt.count();Pt[It]=J.getColorFromPalette(Dt,mt,Lt)}})}})}},Ee=P(38154),Pe=P(9074),Ge=P(14826),Ce=Math.PI;function Vr(rt,it){it=it||{},v.ce(it,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var J=new Ee.Z,nt=new dt.Z({style:{fill:it.maskColor},zlevel:it.zlevel,z:1e4});J.add(nt);var at=new Pe.ZP({style:{text:it.text,fill:it.textColor,fontSize:it.fontSize,fontWeight:it.fontWeight,fontStyle:it.fontStyle,fontFamily:it.fontFamily},zlevel:it.zlevel,z:10001}),ht=new dt.Z({style:{fill:"none"},textContent:at,textConfig:{position:"right",distance:10},zlevel:it.zlevel,z:10001});J.add(ht);var mt;return it.showSpinner&&(mt=new Ge.Z({shape:{startAngle:-Ce/2,endAngle:-Ce/2+.1,r:it.spinnerRadius},style:{stroke:it.color,lineCap:"round",lineWidth:it.lineWidth},zlevel:it.zlevel,z:10001}),mt.animateShape(!0).when(1e3,{endAngle:Ce*3/2}).start("circularInOut"),mt.animateShape(!0).when(1e3,{startAngle:Ce*3/2}).delay(300).start("circularInOut"),J.add(mt)),J.resize=function(){var Ft=at.getBoundingRect().width,It=it.showSpinner?it.spinnerRadius:0,Gt=(rt.getWidth()-It*2-(it.showSpinner&&Ft?10:0)-Ft)/2-(it.showSpinner&&Ft?0:5+Ft/2)+(it.showSpinner?0:Ft/2)+(Ft?0:It),Yt=rt.getHeight()/2;it.showSpinner&&mt.setShape({cx:Gt,cy:Yt}),ht.setShape({x:Gt-It,y:Yt-It,width:It*2,height:It*2}),nt.setShape({x:0,y:0,width:rt.getWidth(),height:rt.getHeight()})},J.resize(),J}var He=P(8674),Yr=P(42151),Xr=function(){function rt(it,J,nt,at){this._stageTaskMap=(0,v.kW)(),this.ecInstance=it,this.api=J,nt=this._dataProcessorHandlers=nt.slice(),at=this._visualHandlers=at.slice(),this._allHandlers=nt.concat(at)}return rt.prototype.restoreData=function(it,J){it.restoreData(J),this._stageTaskMap.each(function(nt){var at=nt.overallTask;at&&at.dirty()})},rt.prototype.getPerformArgs=function(it,J){if(!!it.__pipeline){var nt=this._pipelineMap.get(it.__pipeline.id),at=nt.context,ht=!J&&nt.progressiveEnabled&&(!at||at.progressiveRender)&&it.__idxInPipeline>nt.blockIndex,mt=ht?nt.step:null,Ft=at&&at.modDataCount,It=Ft!=null?Math.ceil(Ft/mt):null;return{step:mt,modBy:It,modDataCount:Ft}}},rt.prototype.getPipeline=function(it){return this._pipelineMap.get(it)},rt.prototype.updateStreamModes=function(it,J){var nt=this._pipelineMap.get(it.uid),at=it.getData(),ht=at.count(),mt=nt.progressiveEnabled&&J.incrementalPrepareRender&&ht>=nt.threshold,Ft=it.get("large")&&ht>=it.get("largeThreshold"),It=it.get("progressiveChunkMode")==="mod"?ht:null;it.pipelineContext=nt.context={progressiveRender:mt,modDataCount:It,large:Ft}},rt.prototype.restorePipelines=function(it){var J=this,nt=J._pipelineMap=(0,v.kW)();it.eachSeries(function(at){var ht=at.getProgressive(),mt=at.uid;nt.set(mt,{id:mt,head:null,tail:null,threshold:at.getProgressiveThreshold(),progressiveEnabled:ht&&!(at.preventIncremental&&at.preventIncremental()),blockIndex:-1,step:Math.round(ht||700),count:0}),J._pipe(at,at.dataTask)})},rt.prototype.prepareStageTasks=function(){var it=this._stageTaskMap,J=this.api.getModel(),nt=this.api;(0,v.S6)(this._allHandlers,function(at){var ht=it.get(at.uid)||it.set(at.uid,{}),mt="";(0,v.hu)(!(at.reset&&at.overallReset),mt),at.reset&&this._createSeriesStageTask(at,ht,J,nt),at.overallReset&&this._createOverallStageTask(at,ht,J,nt)},this)},rt.prototype.prepareView=function(it,J,nt,at){var ht=it.renderTask,mt=ht.context;mt.model=J,mt.ecModel=nt,mt.api=at,ht.__block=!it.incrementalPrepareRender,this._pipe(J,ht)},rt.prototype.performDataProcessorTasks=function(it,J){this._performStageTasks(this._dataProcessorHandlers,it,J,{block:!0})},rt.prototype.performVisualTasks=function(it,J,nt){this._performStageTasks(this._visualHandlers,it,J,nt)},rt.prototype._performStageTasks=function(it,J,nt,at){at=at||{};var ht=!1,mt=this;(0,v.S6)(it,function(It,Gt){if(!(at.visualType&&at.visualType!==It.visualType)){var Yt=mt._stageTaskMap.get(It.uid),yt=Yt.seriesTaskMap,Pt=Yt.overallTask;if(Pt){var Dt,Lt=Pt.agentStubMap;Lt.each(function(Kt){Ft(at,Kt)&&(Kt.dirty(),Dt=!0)}),Dt&&Pt.dirty(),mt.updatePayload(Pt,nt);var Nt=mt.getPerformArgs(Pt,at.block);Lt.each(function(Kt){Kt.perform(Nt)}),Pt.perform(Nt)&&(ht=!0)}else yt&&yt.each(function(Kt,jt){Ft(at,Kt)&&Kt.dirty();var $t=mt.getPerformArgs(Kt,at.block);$t.skip=!It.performRawSeries&&J.isSeriesFiltered(Kt.context.model),mt.updatePayload(Kt,nt),Kt.perform($t)&&(ht=!0)})}});function Ft(It,Gt){return It.setDirty&&(!It.dirtyMap||It.dirtyMap.get(Gt.__pipeline.id))}this.unfinished=ht||this.unfinished},rt.prototype.performSeriesTasks=function(it){var J;it.eachSeries(function(nt){J=nt.dataTask.perform()||J}),this.unfinished=J||this.unfinished},rt.prototype.plan=function(){this._pipelineMap.each(function(it){var J=it.tail;do{if(J.__block){it.blockIndex=J.__idxInPipeline;break}J=J.getUpstream()}while(J)})},rt.prototype.updatePayload=function(it,J){J!=="remain"&&(it.context.payload=J)},rt.prototype._createSeriesStageTask=function(it,J,nt,at){var ht=this,mt=J.seriesTaskMap,Ft=J.seriesTaskMap=(0,v.kW)(),It=it.seriesType,Gt=it.getTargetSeries;it.createOnAllSeries?nt.eachRawSeries(Yt):It?nt.eachRawSeriesByType(It,Yt):Gt&&Gt(nt,at).each(Yt);function Yt(yt){var Pt=yt.uid,Dt=Ft.set(Pt,mt&&mt.get(Pt)||(0,He.v)({plan:tn,reset:en,count:nn}));Dt.context={model:yt,ecModel:nt,api:at,useClearVisual:it.isVisual&&!it.isLayout,plan:it.plan,reset:it.reset,scheduler:ht},ht._pipe(yt,Dt)}},rt.prototype._createOverallStageTask=function(it,J,nt,at){var ht=this,mt=J.overallTask=J.overallTask||(0,He.v)({reset:Jr});mt.context={ecModel:nt,api:at,overallReset:it.overallReset,scheduler:ht};var Ft=mt.agentStubMap,It=mt.agentStubMap=(0,v.kW)(),Gt=it.seriesType,Yt=it.getTargetSeries,yt=!0,Pt=!1,Dt="";(0,v.hu)(!it.createOnAllSeries,Dt),Gt?nt.eachRawSeriesByType(Gt,Lt):Yt?Yt(nt,at).each(Lt):(yt=!1,(0,v.S6)(nt.getSeries(),Lt));function Lt(Nt){var Kt=Nt.uid,jt=It.set(Kt,Ft&&Ft.get(Kt)||(Pt=!0,(0,He.v)({reset:Qr,onDirty:qr})));jt.context={model:Nt,overallProgress:yt},jt.agent=mt,jt.__block=yt,ht._pipe(Nt,jt)}Pt&&mt.dirty()},rt.prototype._pipe=function(it,J){var nt=it.uid,at=this._pipelineMap.get(nt);!at.head&&(at.head=J),at.tail&&at.tail.pipe(J),at.tail=J,J.__idxInPipeline=at.count++,J.__pipeline=at},rt.wrapStageHandler=function(it,J){return(0,v.mf)(it)&&(it={overallReset:it,seriesType:an(it)}),it.uid=(0,Yr.Kr)("stageHandler"),J&&(it.visualType=J),it},rt}();function Jr(rt){rt.overallReset(rt.ecModel,rt.api,rt.payload)}function Qr(rt){return rt.overallProgress&&$r}function $r(){this.agent.dirty(),this.getDownstream().dirty()}function qr(){this.agent&&this.agent.dirty()}function tn(rt){return rt.plan?rt.plan(rt.model,rt.ecModel,rt.api,rt.payload):null}function en(rt){rt.useClearVisual&&rt.data.clearAllVisual();var it=rt.resetDefines=(0,K.kF)(rt.reset(rt.model,rt.ecModel,rt.api,rt.payload));return it.length>1?(0,v.UI)(it,function(J,nt){return fr(nt)}):rn}var rn=fr(0);function fr(rt){return function(it,J){var nt=J.data,at=J.resetDefines[rt];if(at&&at.dataEach)for(var ht=it.start;ht<it.end;ht++)at.dataEach(nt,ht);else at&&at.progress&&at.progress(it,nt)}}function nn(rt){return rt.data.count()}function an(rt){Re=null;try{rt(Oe,cr)}catch(it){}return Re}var Oe={},cr={},Re;hr(Oe,a),hr(cr,D),Oe.eachSeriesByType=Oe.eachRawSeriesByType=function(rt){Re=rt},Oe.eachComponent=function(rt){rt.mainType==="series"&&rt.subType&&(Re=rt.subType)};function hr(rt,it){for(var J in it.prototype)rt[J]=v.ZT}var vr=Xr,dr=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],on={color:dr,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],dr]},ce="#B9B8CE",pr="#100C2A",Be=function(){return{axisLine:{lineStyle:{color:ce}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},_r=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],gr={darkMode:!0,color:_r,backgroundColor:pr,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:ce}},textStyle:{color:ce},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:ce}},dataZoom:{borderColor:"#71708A",textStyle:{color:ce},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:ce}},timeline:{lineStyle:{color:ce},label:{color:ce},controlStyle:{color:ce,borderColor:ce}},calendar:{itemStyle:{color:pr},dayLabel:{color:ce},monthLabel:{color:ce},yearLabel:{color:ce}},timeAxis:Be(),logAxis:Be(),valueAxis:Be(),categoryAxis:Be(),line:{symbol:"circle"},graph:{color:_r},gauge:{title:{color:ce},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:ce},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};gr.categoryAxis.splitLine.show=!1;var sn=gr,je=P(34251),un=function(){function rt(){}return rt.prototype.normalizeQuery=function(it){var J={},nt={},at={};if(v.HD(it)){var ht=(0,je.u9)(it);J.mainType=ht.main||null,J.subType=ht.sub||null}else{var mt=["Index","Name","Id"],Ft={name:1,dataIndex:1,dataType:1};v.S6(it,function(It,Gt){for(var Yt=!1,yt=0;yt<mt.length;yt++){var Pt=mt[yt],Dt=Gt.lastIndexOf(Pt);if(Dt>0&&Dt===Gt.length-Pt.length){var Lt=Gt.slice(0,Dt);Lt!=="data"&&(J.mainType=Lt,J[Pt.toLowerCase()]=It,Yt=!0)}}Ft.hasOwnProperty(Gt)&&(nt[Gt]=It,Yt=!0),Yt||(at[Gt]=It)})}return{cptQuery:J,dataQuery:nt,otherQuery:at}},rt.prototype.filter=function(it,J){var nt=this.eventInfo;if(!nt)return!0;var at=nt.targetEl,ht=nt.packedEvent,mt=nt.model,Ft=nt.view;if(!mt||!Ft)return!0;var It=J.cptQuery,Gt=J.dataQuery;return Yt(It,mt,"mainType")&&Yt(It,mt,"subType")&&Yt(It,mt,"index","componentIndex")&&Yt(It,mt,"name")&&Yt(It,mt,"id")&&Yt(Gt,ht,"name")&&Yt(Gt,ht,"dataIndex")&&Yt(Gt,ht,"dataType")&&(!Ft.filterForExposedEvent||Ft.filterForExposedEvent(it,J.otherQuery,at,ht));function Yt(yt,Pt,Dt,Lt){return yt[Dt]==null||Pt[Lt||Dt]===yt[Dt]}},rt.prototype.afterTrigger=function(){this.eventInfo=null},rt}(),Ve=["symbol","symbolSize","symbolRotate","symbolOffset"],yr=Ve.concat(["symbolKeepAspect"]),ln={createOnAllSeries:!0,performRawSeries:!0,reset:function(rt,it){var J=rt.getData();if(rt.legendIcon&&J.setVisual("legendIcon",rt.legendIcon),!rt.hasSymbolVisual)return;for(var nt={},at={},ht=!1,mt=0;mt<Ve.length;mt++){var Ft=Ve[mt],It=rt.get(Ft);(0,v.mf)(It)?(ht=!0,at[Ft]=It):nt[Ft]=It}if(nt.symbol=nt.symbol||rt.defaultSymbol,J.setVisual((0,v.l7)({legendIcon:rt.legendIcon||nt.symbol,symbolKeepAspect:rt.get("symbolKeepAspect")},nt)),it.isSeriesFiltered(rt))return;var Gt=(0,v.XP)(at);function Yt(yt,Pt){for(var Dt=rt.getRawValue(Pt),Lt=rt.getDataParams(Pt),Nt=0;Nt<Gt.length;Nt++){var Kt=Gt[Nt];yt.setItemVisual(Pt,Kt,at[Kt](Dt,Lt))}}return{dataEach:ht?Yt:null}}},fn={createOnAllSeries:!0,performRawSeries:!0,reset:function(rt,it){if(!rt.hasSymbolVisual||it.isSeriesFiltered(rt))return;var J=rt.getData();function nt(at,ht){for(var mt=at.getItemModel(ht),Ft=0;Ft<yr.length;Ft++){var It=yr[Ft],Gt=mt.getShallow(It,!0);Gt!=null&&at.setItemVisual(ht,It,Gt)}}return{dataEach:J.hasItemOption?nt:null}}},mr=P(26211),Sr=P(70175),cn=P(31891),hn=P(10437),Dr=P(73917),We=P(18310),Mr=P(48625);function vn(rt,it){rt.eachRawSeries(function(J){if(!rt.isSeriesFiltered(J)){var nt=J.getData();nt.hasItemVisual()&&nt.each(function(mt){var Ft=nt.getItemVisual(mt,"decal");if(Ft){var It=nt.ensureUniqueItemVisual(mt,"style");It.decal=(0,Mr.I)(Ft,it)}});var at=nt.getVisual("decal");if(at){var ht=nt.getVisual("style");ht.decal=(0,Mr.I)(at,it)}}})}var dn=new W.Z,ge=dn,pn=P(23132),_n=P(49428),Gn="5.5.0",Hn={zrender:"5.5.0"},gn=1,yn=800,mn=900,Sn=1e3,Dn=2e3,Mn=5e3,Tr=1e3,Tn=1100,Ye=2e3,Er=3e3,En=4e3,Ue=4500,Pn=4600,Cn=5e3,wn=6e3,Pr=7e3,bn={PROCESSOR:{FILTER:Sn,SERIES_FILTER:yn,STATISTIC:Mn},VISUAL:{LAYOUT:Tr,PROGRESSIVE_LAYOUT:Tn,GLOBAL:Ye,CHART:Er,POST_CHART_LAYOUT:Pn,COMPONENT:En,BRUSH:Cn,CHART_ITEM:Ue,ARIA:wn,DECAL:Pr}},le="__flagInMainProcess",he="__pendingUpdate",Xe="__needsUpdateStatus",Cr=/^[a-zA-Z0-9_]+$/,Je="__connectUpdateStatus",wr=0,On=1,Ln=2;function br(rt){return function(){for(var it=[],J=0;J<arguments.length;J++)it[J]=arguments[J];if(this.isDisposed()){de(this.id);return}return Lr(this,rt,it)}}function Or(rt){return function(){for(var it=[],J=0;J<arguments.length;J++)it[J]=arguments[J];return Lr(this,rt,it)}}function Lr(rt,it,J){return J[0]=J[0]&&J[0].toLowerCase(),W.Z.prototype[it].apply(rt,J)}var Ar=function(rt){(0,C.ZT)(it,rt);function it(){return rt!==null&&rt.apply(this,arguments)||this}return it}(W.Z),xr=Ar.prototype;xr.on=Or("on"),xr.off=Or("off");var we,Qe,Fe,me,$e,qe,tr,Le,Ae,Ir,Rr,er,Br,ke,Wr,Ur,pe,Fr,Ne=function(rt){(0,C.ZT)(it,rt);function it(J,nt,at){var ht=rt.call(this,new un)||this;ht._chartsViews=[],ht._chartsMap={},ht._componentsViews=[],ht._componentsMap={},ht._pendingActions=[],at=at||{},(0,v.HD)(nt)&&(nt=kr[nt]),ht._dom=J;var mt="canvas",Ft="auto",It=!1;if(!1)var Gt;at.ssr&&k.Qq(function(Dt){var Lt=(0,$.A)(Dt),Nt=Lt.dataIndex;if(Nt!=null){var Kt=(0,v.kW)();return Kt.set("series_index",Lt.seriesIndex),Kt.set("data_index",Nt),Lt.ssrType&&Kt.set("ssr_type",Lt.ssrType),Kt}});var Yt=ht._zr=k.S1(J,{renderer:at.renderer||mt,devicePixelRatio:at.devicePixelRatio,width:at.width,height:at.height,ssr:at.ssr,useDirtyRect:(0,v.pD)(at.useDirtyRect,It),useCoarsePointer:(0,v.pD)(at.useCoarsePointer,Ft),pointerSize:at.pointerSize});ht._ssr=at.ssr,ht._throttledZrFlush=(0,ft.P2)((0,v.ak)(Yt.flush,Yt),17),nt=(0,v.d9)(nt),nt&&kt(nt,!0),ht._theme=nt,ht._locale=(0,Dr.D0)(at.locale||Dr.sO),ht._coordSysMgr=new B.Z;var yt=ht._api=Wr(ht);function Pt(Dt,Lt){return Dt.__prio-Lt.__prio}return(0,z.Z)(ze,Pt),(0,z.Z)(nr,Pt),ht._scheduler=new vr(ht,yt,nr,ze),ht._messageCenter=new Ar,ht._initEvents(),ht.resize=(0,v.ak)(ht.resize,ht),Yt.animation.on("frame",ht._onframe,ht),Ir(Yt,ht),Rr(Yt,ht),(0,v.s7)(ht),ht}return it.prototype._onframe=function(){if(!this._disposed){Fr(this);var J=this._scheduler;if(this[he]){var nt=this[he].silent;this[le]=!0;try{we(this),me.update.call(this,null,this[he].updateParams)}catch(It){throw this[le]=!1,this[he]=null,It}this._zr.flush(),this[le]=!1,this[he]=null,Le.call(this,nt),Ae.call(this,nt)}else if(J.unfinished){var at=gn,ht=this._model,mt=this._api;J.unfinished=!1;do{var Ft=+new Date;J.performSeriesTasks(ht),J.performDataProcessorTasks(ht),qe(this,ht),J.performVisualTasks(ht),ke(this,this._model,mt,"remain",{}),at-=+new Date-Ft}while(at>0&&J.unfinished);J.unfinished||this._zr.flush()}}},it.prototype.getDom=function(){return this._dom},it.prototype.getId=function(){return this.id},it.prototype.getZr=function(){return this._zr},it.prototype.isSSR=function(){return this._ssr},it.prototype.setOption=function(J,nt,at){if(!this[le]){if(this._disposed){de(this.id);return}var ht,mt,Ft;if((0,v.Kn)(nt)&&(at=nt.lazyUpdate,ht=nt.silent,mt=nt.replaceMerge,Ft=nt.transition,nt=nt.notMerge),this[le]=!0,!this._model||nt){var It=new V(this._api),Gt=this._theme,Yt=this._model=new a;Yt.scheduler=this._scheduler,Yt.ssr=this._ssr,Yt.init(null,null,null,Gt,this._locale,It)}this._model.setOption(J,{replaceMerge:mt},ir);var yt={seriesTransition:Ft,optionChanged:!0};if(at)this[he]={silent:ht,updateParams:yt},this[le]=!1,this.getZr().wakeUp();else{try{we(this),me.update.call(this,null,yt)}catch(Pt){throw this[he]=null,this[le]=!1,Pt}this._ssr||this._zr.flush(),this[he]=null,this[le]=!1,Le.call(this,ht),Ae.call(this,ht)}}},it.prototype.setTheme=function(){(0,Sr.Sh)("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},it.prototype.getModel=function(){return this._model},it.prototype.getOption=function(){return this._model&&this._model.getOption()},it.prototype.getWidth=function(){return this._zr.getWidth()},it.prototype.getHeight=function(){return this._zr.getHeight()},it.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||F.Z.hasGlobalWindow&&window.devicePixelRatio||1},it.prototype.getRenderedCanvas=function(J){return this.renderToCanvas(J)},it.prototype.renderToCanvas=function(J){J=J||{};var nt=this._zr.painter;return nt.getRenderedCanvas({backgroundColor:J.backgroundColor||this._model.get("backgroundColor"),pixelRatio:J.pixelRatio||this.getDevicePixelRatio()})},it.prototype.renderToSVGString=function(J){J=J||{};var nt=this._zr.painter;return nt.renderToString({useViewBox:J.useViewBox})},it.prototype.getSvgDataURL=function(){if(!!F.Z.svgSupported){var J=this._zr,nt=J.storage.getDisplayList();return(0,v.S6)(nt,function(at){at.stopAnimation(null,!0)}),J.painter.toDataURL()}},it.prototype.getDataURL=function(J){if(this._disposed){de(this.id);return}J=J||{};var nt=J.excludeComponents,at=this._model,ht=[],mt=this;(0,v.S6)(nt,function(It){at.eachComponent({mainType:It},function(Gt){var Yt=mt._componentsMap[Gt.__viewId];Yt.group.ignore||(ht.push(Yt),Yt.group.ignore=!0)})});var Ft=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(J).toDataURL("image/"+(J&&J.type||"png"));return(0,v.S6)(ht,function(It){It.group.ignore=!1}),Ft},it.prototype.getConnectedDataURL=function(J){if(this._disposed){de(this.id);return}var nt=J.type==="svg",at=this.group,ht=Math.min,mt=Math.max,Ft=Infinity;if(Ke[at]){var It=Ft,Gt=Ft,Yt=-Ft,yt=-Ft,Pt=[],Dt=J&&J.pixelRatio||this.getDevicePixelRatio();(0,v.S6)(Se,function(Jt,te){if(Jt.group===at){var ne=nt?Jt.getZr().painter.getSvgDom().innerHTML:Jt.renderToCanvas((0,v.d9)(J)),ee=Jt.getDom().getBoundingClientRect();It=ht(ee.left,It),Gt=ht(ee.top,Gt),Yt=mt(ee.right,Yt),yt=mt(ee.bottom,yt),Pt.push({dom:ne,left:ee.left,top:ee.top})}}),It*=Dt,Gt*=Dt,Yt*=Dt,yt*=Dt;var Lt=Yt-It,Nt=yt-Gt,Kt=pn.qW.createCanvas(),jt=k.S1(Kt,{renderer:nt?"svg":"canvas"});if(jt.resize({width:Lt,height:Nt}),nt){var $t="";return(0,v.S6)(Pt,function(Jt){var te=Jt.left-It,ne=Jt.top-Gt;$t+='<g transform="translate('+te+","+ne+')">'+Jt.dom+"</g>"}),jt.painter.getSvgRoot().innerHTML=$t,J.connectedBackgroundColor&&jt.painter.setBackgroundColor(J.connectedBackgroundColor),jt.refreshImmediately(),jt.painter.toDataURL()}else return J.connectedBackgroundColor&&jt.add(new dt.Z({shape:{x:0,y:0,width:Lt,height:Nt},style:{fill:J.connectedBackgroundColor}})),(0,v.S6)(Pt,function(Jt){var te=new Mt.ZP({style:{x:Jt.left*Dt-It,y:Jt.top*Dt-Gt,image:Jt.dom}});jt.add(te)}),jt.refreshImmediately(),Kt.toDataURL("image/"+(J&&J.type||"png"))}else return this.getDataURL(J)},it.prototype.convertToPixel=function(J,nt){return $e(this,"convertToPixel",J,nt)},it.prototype.convertFromPixel=function(J,nt){return $e(this,"convertFromPixel",J,nt)},it.prototype.containPixel=function(J,nt){if(this._disposed){de(this.id);return}var at=this._model,ht,mt=K.pm(at,J);return(0,v.S6)(mt,function(Ft,It){It.indexOf("Models")>=0&&(0,v.S6)(Ft,function(Gt){var Yt=Gt.coordinateSystem;if(Yt&&Yt.containPoint)ht=ht||!!Yt.containPoint(nt);else if(It==="seriesModels"){var yt=this._chartsMap[Gt.__viewId];yt&&yt.containPoint&&(ht=ht||yt.containPoint(nt,Gt))}},this)},this),!!ht},it.prototype.getVisual=function(J,nt){var at=this._model,ht=K.pm(at,J,{defaultMainType:"series"}),mt=ht.seriesModel,Ft=mt.getData(),It=ht.hasOwnProperty("dataIndexInside")?ht.dataIndexInside:ht.hasOwnProperty("dataIndex")?Ft.indexOfRawIndex(ht.dataIndex):null;return It!=null?(0,mr.Or)(Ft,It,nt):(0,mr.UL)(Ft,nt)},it.prototype.getViewOfComponentModel=function(J){return this._componentsMap[J.__viewId]},it.prototype.getViewOfSeriesModel=function(J){return this._chartsMap[J.__viewId]},it.prototype._initEvents=function(){var J=this;(0,v.S6)(An,function(nt){var at=function(ht){var mt=J.getModel(),Ft=ht.target,It,Gt=nt==="globalout";if(Gt?It={}:Ft&&(0,We.o)(Ft,function(Lt){var Nt=(0,$.A)(Lt);if(Nt&&Nt.dataIndex!=null){var Kt=Nt.dataModel||mt.getSeriesByIndex(Nt.seriesIndex);return It=Kt&&Kt.getDataParams(Nt.dataIndex,Nt.dataType,Ft)||{},!0}else if(Nt.eventData)return It=(0,v.l7)({},Nt.eventData),!0},!0),It){var Yt=It.componentType,yt=It.componentIndex;(Yt==="markLine"||Yt==="markPoint"||Yt==="markArea")&&(Yt="series",yt=It.seriesIndex);var Pt=Yt&&yt!=null&&mt.getComponent(Yt,yt),Dt=Pt&&J[Pt.mainType==="series"?"_chartsMap":"_componentsMap"][Pt.__viewId];It.event=ht,It.type=nt,J._$eventProcessor.eventInfo={targetEl:Ft,packedEvent:It,model:Pt,view:Dt},J.trigger(nt,It)}};at.zrEventfulCallAtLast=!0,J._zr.on(nt,at,J)}),(0,v.S6)(xe,function(nt,at){J._messageCenter.on(at,function(ht){this.trigger(at,ht)},J)}),(0,v.S6)(["selectchanged"],function(nt){J._messageCenter.on(nt,function(at){this.trigger(nt,at)},J)}),(0,cn.s)(this._messageCenter,this,this._api)},it.prototype.isDisposed=function(){return this._disposed},it.prototype.clear=function(){if(this._disposed){de(this.id);return}this.setOption({series:[]},!0)},it.prototype.dispose=function(){if(this._disposed){de(this.id);return}this._disposed=!0;var J=this.getDom();J&&K.P$(this.getDom(),or,"");var nt=this,at=nt._api,ht=nt._model;(0,v.S6)(nt._componentsViews,function(mt){mt.dispose(ht,at)}),(0,v.S6)(nt._chartsViews,function(mt){mt.dispose(ht,at)}),nt._zr.dispose(),nt._dom=nt._model=nt._chartsMap=nt._componentsMap=nt._chartsViews=nt._componentsViews=nt._scheduler=nt._api=nt._zr=nt._throttledZrFlush=nt._theme=nt._coordSysMgr=nt._messageCenter=null,delete Se[nt.id]},it.prototype.resize=function(J){if(!this[le]){if(this._disposed){de(this.id);return}this._zr.resize(J);var nt=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!nt){var at=nt.resetOption("media"),ht=J&&J.silent;this[he]&&(ht==null&&(ht=this[he].silent),at=!0,this[he]=null),this[le]=!0;try{at&&we(this),me.update.call(this,{type:"resize",animation:(0,v.l7)({duration:0},J&&J.animation)})}catch(mt){throw this[le]=!1,mt}this[le]=!1,Le.call(this,ht),Ae.call(this,ht)}}},it.prototype.showLoading=function(J,nt){if(this._disposed){de(this.id);return}if((0,v.Kn)(J)&&(nt=J,J=""),J=J||"default",this.hideLoading(),!!ar[J]){var at=ar[J](this._api,nt),ht=this._zr;this._loadingFX=at,ht.add(at)}},it.prototype.hideLoading=function(){if(this._disposed){de(this.id);return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},it.prototype.makeActionFromEvent=function(J){var nt=(0,v.l7)({},J);return nt.type=xe[J.type],nt},it.prototype.dispatchAction=function(J,nt){if(this._disposed){de(this.id);return}if((0,v.Kn)(nt)||(nt={silent:!!nt}),!!Ze[J.type]&&!!this._model){if(this[le]){this._pendingActions.push(J);return}var at=nt.silent;tr.call(this,J,at);var ht=nt.flush;ht?this._zr.flush():ht!==!1&&F.Z.browser.weChat&&this._throttledZrFlush(),Le.call(this,at),Ae.call(this,at)}},it.prototype.updateLabelLayout=function(){ge.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},it.prototype.appendData=function(J){if(this._disposed){de(this.id);return}var nt=J.seriesIndex,at=this.getModel(),ht=at.getSeriesByIndex(nt);ht.appendData(J),this._scheduler.unfinished=!0,this.getZr().wakeUp()},it.internalField=function(){we=function(yt){var Pt=yt._scheduler;Pt.restorePipelines(yt._model),Pt.prepareStageTasks(),Qe(yt,!0),Qe(yt,!1),Pt.plan()},Qe=function(yt,Pt){for(var Dt=yt._model,Lt=yt._scheduler,Nt=Pt?yt._componentsViews:yt._chartsViews,Kt=Pt?yt._componentsMap:yt._chartsMap,jt=yt._zr,$t=yt._api,Jt=0;Jt<Nt.length;Jt++)Nt[Jt].__alive=!1;Pt?Dt.eachComponent(function(ee,ae){ee!=="series"&&te(ae)}):Dt.eachSeries(te);function te(ee){var ae=ee.__requireNewView;ee.__requireNewView=!1;var fe="_ec_"+ee.id+"_"+ee.type,se=!ae&&Kt[fe];if(!se){var ve=(0,je.u9)(ee.type),ye=Pt?xt.Z.getClass(ve.main,ve.sub):Rt.Z.getClass(ve.sub);se=new ye,se.init(Dt,$t),Kt[fe]=se,Nt.push(se),jt.add(se.group)}ee.__viewId=se.__id=fe,se.__alive=!0,se.__model=ee,se.group.__ecComponentInfo={mainType:ee.mainType,index:ee.componentIndex},!Pt&&Lt.prepareView(se,ee,Dt,$t)}for(var Jt=0;Jt<Nt.length;){var ne=Nt[Jt];ne.__alive?Jt++:(!Pt&&ne.renderTask.dispose(),jt.remove(ne.group),ne.dispose(Dt,$t),Nt.splice(Jt,1),Kt[ne.__id]===ne&&delete Kt[ne.__id],ne.__id=ne.group.__ecComponentInfo=null)}},Fe=function(yt,Pt,Dt,Lt,Nt){var Kt=yt._model;if(Kt.setUpdatePayload(Dt),!Lt){(0,v.S6)([].concat(yt._componentsViews).concat(yt._chartsViews),ne);return}var jt={};jt[Lt+"Id"]=Dt[Lt+"Id"],jt[Lt+"Index"]=Dt[Lt+"Index"],jt[Lt+"Name"]=Dt[Lt+"Name"];var $t={mainType:Lt,query:jt};Nt&&($t.subType=Nt);var Jt=Dt.excludeSeriesId,te;Jt!=null&&(te=(0,v.kW)(),(0,v.S6)(K.kF(Jt),function(ee){var ae=K.U5(ee,null);ae!=null&&te.set(ae,!0)})),Kt&&Kt.eachComponent($t,function(ee){var ae=te&&te.get(ee.id)!=null;if(!ae)if((0,Q.xp)(Dt))if(ee instanceof gt.Z)Dt.type===Q.Ki&&!Dt.notBlur&&!ee.get(["emphasis","disabled"])&&(0,Q.UL)(ee,Dt,yt._api);else{var fe=(0,Q.oJ)(ee.mainType,ee.componentIndex,Dt.name,yt._api),se=fe.focusSelf,ve=fe.dispatchers;Dt.type===Q.Ki&&se&&!Dt.notBlur&&(0,Q.zI)(ee.mainType,ee.componentIndex,yt._api),ve&&(0,v.S6)(ve,function(ye){Dt.type===Q.Ki?(0,Q.fD)(ye):(0,Q.Mh)(ye)})}else(0,Q.aG)(Dt)&&ee instanceof gt.Z&&((0,Q.og)(ee,Dt,yt._api),(0,Q.ci)(ee),pe(yt))},yt),Kt&&Kt.eachComponent($t,function(ee){var ae=te&&te.get(ee.id)!=null;ae||ne(yt[Lt==="series"?"_chartsMap":"_componentsMap"][ee.__viewId])},yt);function ne(ee){ee&&ee.__alive&&ee[Pt]&&ee[Pt](ee.__model,Kt,yt._api,Dt)}},me={prepareAndUpdate:function(yt){we(this),me.update.call(this,yt,{optionChanged:yt.newOption!=null})},update:function(yt,Pt){var Dt=this._model,Lt=this._api,Nt=this._zr,Kt=this._coordSysMgr,jt=this._scheduler;if(!!Dt){Dt.setUpdatePayload(yt),jt.restoreData(Dt,yt),jt.performSeriesTasks(Dt),Kt.create(Dt,Lt),jt.performDataProcessorTasks(Dt,yt),qe(this,Dt),Kt.update(Dt,Lt),J(Dt),jt.performVisualTasks(Dt,yt),er(this,Dt,Lt,yt,Pt);var $t=Dt.get("backgroundColor")||"transparent",Jt=Dt.get("darkMode");Nt.setBackgroundColor($t),Jt!=null&&Jt!=="auto"&&Nt.setDarkMode(Jt),ge.trigger("afterupdate",Dt,Lt)}},updateTransform:function(yt){var Pt=this,Dt=this._model,Lt=this._api;if(!!Dt){Dt.setUpdatePayload(yt);var Nt=[];Dt.eachComponent(function(jt,$t){if(jt!=="series"){var Jt=Pt.getViewOfComponentModel($t);if(Jt&&Jt.__alive)if(Jt.updateTransform){var te=Jt.updateTransform($t,Dt,Lt,yt);te&&te.update&&Nt.push(Jt)}else Nt.push(Jt)}});var Kt=(0,v.kW)();Dt.eachSeries(function(jt){var $t=Pt._chartsMap[jt.__viewId];if($t.updateTransform){var Jt=$t.updateTransform(jt,Dt,Lt,yt);Jt&&Jt.update&&Kt.set(jt.uid,1)}else Kt.set(jt.uid,1)}),J(Dt),this._scheduler.performVisualTasks(Dt,yt,{setDirty:!0,dirtyMap:Kt}),ke(this,Dt,Lt,yt,{},Kt),ge.trigger("afterupdate",Dt,Lt)}},updateView:function(yt){var Pt=this._model;!Pt||(Pt.setUpdatePayload(yt),Rt.Z.markUpdateMethod(yt,"updateView"),J(Pt),this._scheduler.performVisualTasks(Pt,yt,{setDirty:!0}),er(this,Pt,this._api,yt,{}),ge.trigger("afterupdate",Pt,this._api))},updateVisual:function(yt){var Pt=this,Dt=this._model;!Dt||(Dt.setUpdatePayload(yt),Dt.eachSeries(function(Lt){Lt.getData().clearAllVisual()}),Rt.Z.markUpdateMethod(yt,"updateVisual"),J(Dt),this._scheduler.performVisualTasks(Dt,yt,{visualType:"visual",setDirty:!0}),Dt.eachComponent(function(Lt,Nt){if(Lt!=="series"){var Kt=Pt.getViewOfComponentModel(Nt);Kt&&Kt.__alive&&Kt.updateVisual(Nt,Dt,Pt._api,yt)}}),Dt.eachSeries(function(Lt){var Nt=Pt._chartsMap[Lt.__viewId];Nt.updateVisual(Lt,Dt,Pt._api,yt)}),ge.trigger("afterupdate",Dt,this._api))},updateLayout:function(yt){me.update.call(this,yt)}},$e=function(yt,Pt,Dt,Lt){if(yt._disposed){de(yt.id);return}for(var Nt=yt._model,Kt=yt._coordSysMgr.getCoordinateSystems(),jt,$t=K.pm(Nt,Dt),Jt=0;Jt<Kt.length;Jt++){var te=Kt[Jt];if(te[Pt]&&(jt=te[Pt](Nt,$t,Lt))!=null)return jt}},qe=function(yt,Pt){var Dt=yt._chartsMap,Lt=yt._scheduler;Pt.eachSeries(function(Nt){Lt.updateStreamModes(Nt,Dt[Nt.__viewId])})},tr=function(yt,Pt){var Dt=this,Lt=this.getModel(),Nt=yt.type,Kt=yt.escapeConnect,jt=Ze[Nt],$t=jt.actionInfo,Jt=($t.update||"update").split(":"),te=Jt.pop(),ne=Jt[0]!=null&&(0,je.u9)(Jt[0]);this[le]=!0;var ee=[yt],ae=!1;yt.batch&&(ae=!0,ee=(0,v.UI)(yt.batch,function(_e){return _e=(0,v.ce)((0,v.l7)({},_e),yt),_e.batch=null,_e}));var fe=[],se,ve=(0,Q.aG)(yt),ye=(0,Q.xp)(yt);if(ye&&(0,Q.T5)(this._api),(0,v.S6)(ee,function(_e){if(se=jt.action(_e,Dt._model,Dt._api),se=se||(0,v.l7)({},_e),se.type=$t.event||se.type,fe.push(se),ye){var jr=K.zH(yt),Zn=jr.queryOptionMap,zn=jr.mainTypeSpecified,Kn=zn?Zn.keys()[0]:"series";Fe(Dt,te,_e,Kn),pe(Dt)}else ve?(Fe(Dt,te,_e,"series"),pe(Dt)):ne&&Fe(Dt,te,_e,ne.main,ne.sub)}),te!=="none"&&!ye&&!ve&&!ne)try{this[he]?(we(this),me.update.call(this,yt),this[he]=null):me[te].call(this,yt)}catch(_e){throw this[le]=!1,_e}if(ae?se={type:$t.event||Nt,escapeConnect:Kt,batch:fe}:se=fe[0],this[le]=!1,!Pt){var lr=this._messageCenter;if(lr.trigger(se.type,se),ve){var Ie={type:"selectchanged",escapeConnect:Kt,selected:(0,Q.C5)(Lt),isFromClick:yt.isFromClick||!1,fromAction:yt.type,fromActionPayload:yt};lr.trigger(Ie.type,Ie)}}},Le=function(yt){for(var Pt=this._pendingActions;Pt.length;){var Dt=Pt.shift();tr.call(this,Dt,yt)}},Ae=function(yt){!yt&&this.trigger("updated")},Ir=function(yt,Pt){yt.on("rendered",function(Dt){Pt.trigger("rendered",Dt),yt.animation.isFinished()&&!Pt[he]&&!Pt._scheduler.unfinished&&!Pt._pendingActions.length&&Pt.trigger("finished")})},Rr=function(yt,Pt){yt.on("mouseover",function(Dt){var Lt=Dt.target,Nt=(0,We.o)(Lt,Q.Av);Nt&&((0,Q.$l)(Nt,Dt,Pt._api),pe(Pt))}).on("mouseout",function(Dt){var Lt=Dt.target,Nt=(0,We.o)(Lt,Q.Av);Nt&&((0,Q.xr)(Nt,Dt,Pt._api),pe(Pt))}).on("click",function(Dt){var Lt=Dt.target,Nt=(0,We.o)(Lt,function($t){return(0,$.A)($t).dataIndex!=null},!0);if(Nt){var Kt=Nt.selected?"unselect":"select",jt=(0,$.A)(Nt);Pt._api.dispatchAction({type:Kt,dataType:jt.dataType,dataIndexInside:jt.dataIndex,seriesIndex:jt.seriesIndex,isFromClick:!0})}})};function J(yt){yt.clearColorPalette(),yt.eachSeries(function(Pt){Pt.clearColorPalette()})}function nt(yt){var Pt=[],Dt=[],Lt=!1;if(yt.eachComponent(function($t,Jt){var te=Jt.get("zlevel")||0,ne=Jt.get("z")||0,ee=Jt.getZLevelKey();Lt=Lt||!!ee,($t==="series"?Dt:Pt).push({zlevel:te,z:ne,idx:Jt.componentIndex,type:$t,key:ee})}),Lt){var Nt=Pt.concat(Dt),Kt,jt;(0,z.Z)(Nt,function($t,Jt){return $t.zlevel===Jt.zlevel?$t.z-Jt.z:$t.zlevel-Jt.zlevel}),(0,v.S6)(Nt,function($t){var Jt=yt.getComponent($t.type,$t.idx),te=$t.zlevel,ne=$t.key;Kt!=null&&(te=Math.max(Kt,te)),ne?(te===Kt&&ne!==jt&&te++,jt=ne):jt&&(te===Kt&&te++,jt=""),Kt=te,Jt.setZLevel(te)})}}er=function(yt,Pt,Dt,Lt,Nt){nt(Pt),Br(yt,Pt,Dt,Lt,Nt),(0,v.S6)(yt._chartsViews,function(Kt){Kt.__alive=!1}),ke(yt,Pt,Dt,Lt,Nt),(0,v.S6)(yt._chartsViews,function(Kt){Kt.__alive||Kt.remove(Pt,Dt)})},Br=function(yt,Pt,Dt,Lt,Nt,Kt){(0,v.S6)(Kt||yt._componentsViews,function(jt){var $t=jt.__model;Gt($t,jt),jt.render($t,Pt,Dt,Lt),Ft($t,jt),Yt($t,jt)})},ke=function(yt,Pt,Dt,Lt,Nt,Kt){var jt=yt._scheduler;Nt=(0,v.l7)(Nt||{},{updatedSeries:Pt.getSeries()}),ge.trigger("series:beforeupdate",Pt,Dt,Nt);var $t=!1;Pt.eachSeries(function(Jt){var te=yt._chartsMap[Jt.__viewId];te.__alive=!0;var ne=te.renderTask;jt.updatePayload(ne,Lt),Gt(Jt,te),Kt&&Kt.get(Jt.uid)&&ne.dirty(),ne.perform(jt.getPerformArgs(ne))&&($t=!0),te.group.silent=!!Jt.get("silent"),mt(Jt,te),(0,Q.ci)(Jt)}),jt.unfinished=$t||jt.unfinished,ge.trigger("series:layoutlabels",Pt,Dt,Nt),ge.trigger("series:transition",Pt,Dt,Nt),Pt.eachSeries(function(Jt){var te=yt._chartsMap[Jt.__viewId];Ft(Jt,te),Yt(Jt,te)}),ht(yt,Pt),ge.trigger("series:afterupdate",Pt,Dt,Nt)},pe=function(yt){yt[Xe]=!0,yt.getZr().wakeUp()},Fr=function(yt){!yt[Xe]||(yt.getZr().storage.traverse(function(Pt){vt.eq(Pt)||at(Pt)}),yt[Xe]=!1)};function at(yt){for(var Pt=[],Dt=yt.currentStates,Lt=0;Lt<Dt.length;Lt++){var Nt=Dt[Lt];Nt==="emphasis"||Nt==="blur"||Nt==="select"||Pt.push(Nt)}yt.selected&&yt.states.select&&Pt.push("select"),yt.hoverState===Q.wU&&yt.states.emphasis?Pt.push("emphasis"):yt.hoverState===Q.CX&&yt.states.blur&&Pt.push("blur"),yt.useStates(Pt)}function ht(yt,Pt){var Dt=yt._zr,Lt=Dt.storage,Nt=0;Lt.traverse(function(Kt){Kt.isGroup||Nt++}),Nt>Pt.get("hoverLayerThreshold")&&!F.Z.node&&!F.Z.worker&&Pt.eachSeries(function(Kt){if(!Kt.preventUsingHoverLayer){var jt=yt._chartsMap[Kt.__viewId];jt.__alive&&jt.eachRendered(function($t){$t.states.emphasis&&($t.states.emphasis.hoverLayer=!0)})}})}function mt(yt,Pt){var Dt=yt.get("blendMode")||null;Pt.eachRendered(function(Lt){Lt.isGroup||(Lt.style.blend=Dt)})}function Ft(yt,Pt){if(!yt.preventAutoZ){var Dt=yt.get("z")||0,Lt=yt.get("zlevel")||0;Pt.eachRendered(function(Nt){return It(Nt,Dt,Lt,-Infinity),!0})}}function It(yt,Pt,Dt,Lt){var Nt=yt.getTextContent(),Kt=yt.getTextGuideLine(),jt=yt.isGroup;if(jt)for(var $t=yt.childrenRef(),Jt=0;Jt<$t.length;Jt++)Lt=Math.max(It($t[Jt],Pt,Dt,Lt),Lt);else yt.z=Pt,yt.zlevel=Dt,Lt=Math.max(yt.z2,Lt);if(Nt&&(Nt.z=Pt,Nt.zlevel=Dt,isFinite(Lt)&&(Nt.z2=Lt+2)),Kt){var te=yt.textGuideLineConfig;Kt.z=Pt,Kt.zlevel=Dt,isFinite(Lt)&&(Kt.z2=Lt+(te&&te.showAbove?1:-1))}return Lt}function Gt(yt,Pt){Pt.eachRendered(function(Dt){if(!vt.eq(Dt)){var Lt=Dt.getTextContent(),Nt=Dt.getTextGuideLine();Dt.stateTransition&&(Dt.stateTransition=null),Lt&&Lt.stateTransition&&(Lt.stateTransition=null),Nt&&Nt.stateTransition&&(Nt.stateTransition=null),Dt.hasState()?(Dt.prevStates=Dt.currentStates,Dt.clearStates()):Dt.prevStates&&(Dt.prevStates=null)}})}function Yt(yt,Pt){var Dt=yt.getModel("stateAnimation"),Lt=yt.isAnimationEnabled(),Nt=Dt.get("duration"),Kt=Nt>0?{duration:Nt,delay:Dt.get("delay"),easing:Dt.get("easing")}:null;Pt.eachRendered(function(jt){if(jt.states&&jt.states.emphasis){if(vt.eq(jt))return;if(jt instanceof Y.ZP&&(0,Q.e9)(jt),jt.__dirty){var $t=jt.prevStates;$t&&jt.useStates($t)}if(Lt){jt.stateTransition=Kt;var Jt=jt.getTextContent(),te=jt.getTextGuideLine();Jt&&(Jt.stateTransition=Kt),te&&(te.stateTransition=Kt)}jt.__dirty&&at(jt)}})}Wr=function(yt){return new(function(Pt){(0,C.ZT)(Dt,Pt);function Dt(){return Pt!==null&&Pt.apply(this,arguments)||this}return Dt.prototype.getCoordinateSystems=function(){return yt._coordSysMgr.getCoordinateSystems()},Dt.prototype.getComponentByElement=function(Lt){for(;Lt;){var Nt=Lt.__ecComponentInfo;if(Nt!=null)return yt._model.getComponent(Nt.mainType,Nt.index);Lt=Lt.parent}},Dt.prototype.enterEmphasis=function(Lt,Nt){(0,Q.fD)(Lt,Nt),pe(yt)},Dt.prototype.leaveEmphasis=function(Lt,Nt){(0,Q.Mh)(Lt,Nt),pe(yt)},Dt.prototype.enterBlur=function(Lt){(0,Q.SX)(Lt),pe(yt)},Dt.prototype.leaveBlur=function(Lt){(0,Q.VP)(Lt),pe(yt)},Dt.prototype.enterSelect=function(Lt){(0,Q.XX)(Lt),pe(yt)},Dt.prototype.leaveSelect=function(Lt){(0,Q.SJ)(Lt),pe(yt)},Dt.prototype.getModel=function(){return yt.getModel()},Dt.prototype.getViewOfComponentModel=function(Lt){return yt.getViewOfComponentModel(Lt)},Dt.prototype.getViewOfSeriesModel=function(Lt){return yt.getViewOfSeriesModel(Lt)},Dt}(D))(yt)},Ur=function(yt){function Pt(Dt,Lt){for(var Nt=0;Nt<Dt.length;Nt++){var Kt=Dt[Nt];Kt[Je]=Lt}}(0,v.S6)(xe,function(Dt,Lt){yt._messageCenter.on(Lt,function(Nt){if(Ke[yt.group]&&yt[Je]!==wr){if(Nt&&Nt.escapeConnect)return;var Kt=yt.makeActionFromEvent(Nt),jt=[];(0,v.S6)(Se,function($t){$t!==yt&&$t.group===yt.group&&jt.push($t)}),Pt(jt,wr),(0,v.S6)(jt,function($t){$t[Je]!==On&&$t.dispatchAction(Kt)}),Pt(jt,Ln)}})})}}(),it}(W.Z),rr=Ne.prototype;rr.on=br("on"),rr.off=br("off"),rr.one=function(rt,it,J){var nt=this;(0,Sr.Sh)("ECharts#one is deprecated.");function at(){for(var ht=[],mt=0;mt<arguments.length;mt++)ht[mt]=arguments[mt];it&&it.apply&&it.apply(this,ht),nt.off(rt,at)}this.on.call(this,rt,at,J)};var An=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function de(rt){}var Ze={},xe={},nr=[],ir=[],ze=[],kr={},ar={},Se={},Ke={},xn=+new Date-0,In=+new Date-0,or="_echarts_instance_";function Rn(rt,it,J){var nt=!(J&&J.ssr);if(nt){var at=Nr(rt);if(at)return at}var ht=new Ne(rt,it,J);return ht.id="ec_"+xn++,Se[ht.id]=ht,nt&&K.P$(rt,or,ht.id),Ur(ht),ge.trigger("afterinit",ht),ht}function jn(rt){if(isArray(rt)){var it=rt;rt=null,each(it,function(J){J.group!=null&&(rt=J.group)}),rt=rt||"g_"+In++,each(it,function(J){J.group=rt})}return Ke[rt]=!0,rt}function Vn(rt){Ke[rt]=!1}var Yn=null;function Xn(rt){isString(rt)?rt=Se[rt]:rt instanceof Ne||(rt=Nr(rt)),rt instanceof Ne&&!rt.isDisposed()&&rt.dispose()}function Nr(rt){return Se[K.IL(rt,or)]}function Jn(rt){return Se[rt]}function Zr(rt,it){kr[rt]=it}function zr(rt){(0,v.cq)(ir,rt)<0&&ir.push(rt)}function Kr(rt,it){ur(nr,rt,it,Dn)}function Bn(rt){sr("afterinit",rt)}function Wn(rt){sr("afterupdate",rt)}function sr(rt,it){ge.on(rt,it)}function be(rt,it,J){(0,v.mf)(it)&&(J=it,it="");var nt=(0,v.Kn)(rt)?rt.type:[rt,rt={event:it}][0];rt.event=(rt.event||nt).toLowerCase(),it=rt.event,!xe[it]&&((0,v.hu)(Cr.test(nt)&&Cr.test(it)),Ze[nt]||(Ze[nt]={action:J,actionInfo:rt}),xe[it]=nt)}function Un(rt,it){B.Z.register(rt,it)}function Qn(rt){var it=CoordinateSystemManager.get(rt);if(it)return it.getDimensionsInfo?it.getDimensionsInfo():it.dimensions.slice()}function Fn(rt,it){ur(ze,rt,it,Tr,"layout")}function De(rt,it){ur(ze,rt,it,Er,"visual")}var Gr=[];function ur(rt,it,J,nt,at){if(((0,v.mf)(it)||(0,v.Kn)(it))&&(J=it,it=nt),!((0,v.cq)(Gr,J)>=0)){Gr.push(J);var ht=vr.wrapStageHandler(J,at);ht.__prio=it,ht.__raw=J,rt.push(ht)}}function Hr(rt,it){ar[rt]=it}function $n(rt){setPlatformAPI({createCanvas:rt})}function kn(rt,it,J){var nt=(0,_n.C)("registerMap");nt&&nt(rt,it,J)}function qn(rt){var it=getImpl("getMap");return it&&it(rt)}var Nn=hn.DA;De(Ye,oe),De(Ue,Me),De(Ue,Te),De(Ye,ln),De(Ue,fn),De(Pr,vn),zr(kt),Kr(mn,Vt),Hr("default",Vr),be({type:Q.Ki,event:Q.Ki,update:Q.Ki},v.ZT),be({type:Q.yx,event:Q.yx,update:Q.yx},v.ZT),be({type:Q.Hg,event:Q.Hg,update:Q.Hg},v.ZT),be({type:Q.JQ,event:Q.JQ,update:Q.JQ},v.ZT),be({type:Q.iK,event:Q.iK,update:Q.iK},v.ZT),Zr("light",on),Zr("dark",sn);var ti={}},49428:function(Zt,pt,P){"use strict";P.d(pt,{M:function(){return k},C:function(){return v}});var C={};function k(F,z){C[F]=z}function v(F){return C[F]}},73917:function(Zt,pt,P){"use strict";P.d(pt,{sO:function(){return S},D0:function(){return g},Li:function(){return E},G8:function(){return y}});var C=P(1497),k=P(66387),v={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},F={time:{month:["\u4E00\u6708","\u4E8C\u6708","\u4E09\u6708","\u56DB\u6708","\u4E94\u6708","\u516D\u6708","\u4E03\u6708","\u516B\u6708","\u4E5D\u6708","\u5341\u6708","\u5341\u4E00\u6708","\u5341\u4E8C\u6708"],monthAbbr:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],dayOfWeek:["\u661F\u671F\u65E5","\u661F\u671F\u4E00","\u661F\u671F\u4E8C","\u661F\u671F\u4E09","\u661F\u671F\u56DB","\u661F\u671F\u4E94","\u661F\u671F\u516D"],dayOfWeekAbbr:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"]},legend:{selector:{all:"\u5168\u9009",inverse:"\u53CD\u9009"}},toolbox:{brush:{title:{rect:"\u77E9\u5F62\u9009\u62E9",polygon:"\u5708\u9009",lineX:"\u6A2A\u5411\u9009\u62E9",lineY:"\u7EB5\u5411\u9009\u62E9",keep:"\u4FDD\u6301\u9009\u62E9",clear:"\u6E05\u9664\u9009\u62E9"}},dataView:{title:"\u6570\u636E\u89C6\u56FE",lang:["\u6570\u636E\u89C6\u56FE","\u5173\u95ED","\u5237\u65B0"]},dataZoom:{title:{zoom:"\u533A\u57DF\u7F29\u653E",back:"\u533A\u57DF\u7F29\u653E\u8FD8\u539F"}},magicType:{title:{line:"\u5207\u6362\u4E3A\u6298\u7EBF\u56FE",bar:"\u5207\u6362\u4E3A\u67F1\u72B6\u56FE",stack:"\u5207\u6362\u4E3A\u5806\u53E0",tiled:"\u5207\u6362\u4E3A\u5E73\u94FA"}},restore:{title:"\u8FD8\u539F"},saveAsImage:{title:"\u4FDD\u5B58\u4E3A\u56FE\u7247",lang:["\u53F3\u952E\u53E6\u5B58\u4E3A\u56FE\u7247"]}},series:{typeNames:{pie:"\u997C\u56FE",bar:"\u67F1\u72B6\u56FE",line:"\u6298\u7EBF\u56FE",scatter:"\u6563\u70B9\u56FE",effectScatter:"\u6D9F\u6F2A\u6563\u70B9\u56FE",radar:"\u96F7\u8FBE\u56FE",tree:"\u6811\u56FE",treemap:"\u77E9\u5F62\u6811\u56FE",boxplot:"\u7BB1\u578B\u56FE",candlestick:"K\u7EBF\u56FE",k:"K\u7EBF\u56FE",heatmap:"\u70ED\u529B\u56FE",map:"\u5730\u56FE",parallel:"\u5E73\u884C\u5750\u6807\u56FE",lines:"\u7EBF\u56FE",graph:"\u5173\u7CFB\u56FE",sankey:"\u6851\u57FA\u56FE",funnel:"\u6F0F\u6597\u56FE",gauge:"\u4EEA\u8868\u76D8\u56FE",pictorialBar:"\u8C61\u5F62\u67F1\u56FE",themeRiver:"\u4E3B\u9898\u6CB3\u6D41\u56FE",sunburst:"\u65ED\u65E5\u56FE",custom:"\u81EA\u5B9A\u4E49\u56FE\u8868",chart:"\u56FE\u8868"}},aria:{general:{withTitle:"\u8FD9\u662F\u4E00\u4E2A\u5173\u4E8E\u201C{title}\u201D\u7684\u56FE\u8868\u3002",withoutTitle:"\u8FD9\u662F\u4E00\u4E2A\u56FE\u8868\uFF0C"},series:{single:{prefix:"",withName:"\u56FE\u8868\u7C7B\u578B\u662F{seriesType}\uFF0C\u8868\u793A{seriesName}\u3002",withoutName:"\u56FE\u8868\u7C7B\u578B\u662F{seriesType}\u3002"},multiple:{prefix:"\u5B83\u7531{seriesCount}\u4E2A\u56FE\u8868\u7CFB\u5217\u7EC4\u6210\u3002",withName:"\u7B2C{seriesId}\u4E2A\u7CFB\u5217\u662F\u4E00\u4E2A\u8868\u793A{seriesName}\u7684{seriesType}\uFF0C",withoutName:"\u7B2C{seriesId}\u4E2A\u7CFB\u5217\u662F\u4E00\u4E2A{seriesType}\uFF0C",separator:{middle:"\uFF1B",end:"\u3002"}}},data:{allData:"\u5176\u6570\u636E\u662F\u2014\u2014",partialData:"\u5176\u4E2D\uFF0C\u524D{displayCnt}\u9879\u662F\u2014\u2014",withName:"{name}\u7684\u6570\u636E\u662F{value}",withoutName:"{value}",separator:{middle:"\uFF0C",end:""}}}},z=P(33051),W="ZH",K="EN",R=K,b={},A={},S=k.Z.domSupported?function(){var h=(document.documentElement.lang||navigator.language||navigator.browserLanguage||R).toUpperCase();return h.indexOf(W)>-1?W:R}():R;function m(h,c){h=h.toUpperCase(),A[h]=new C.Z(c),b[h]=c}function g(h){if((0,z.HD)(h)){var c=b[h.toUpperCase()]||{};return h===W||h===K?(0,z.d9)(c):(0,z.TS)((0,z.d9)(c),(0,z.d9)(b[R]),!1)}else return(0,z.TS)((0,z.d9)(h),(0,z.d9)(b[R]),!1)}function y(h){return A[h]}function E(){return A[R]}m(K,v),m(W,F)},8674:function(Zt,pt,P){"use strict";P.d(pt,{v:function(){return k}});var C=P(33051);function k(z){return new v(z)}var v=function(){function z(W){W=W||{},this._reset=W.reset,this._plan=W.plan,this._count=W.count,this._onDirty=W.onDirty,this._dirty=!0}return z.prototype.perform=function(W){var K=this._upstream,R=W&&W.skip;if(this._dirty&&K){var b=this.context;b.data=b.outputData=K.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var A;this._plan&&!R&&(A=this._plan(this.context));var S=E(this._modBy),m=this._modDataCount||0,g=E(W&&W.modBy),y=W&&W.modDataCount||0;(S!==g||m!==y)&&(A="reset");function E(f){return!(f>=1)&&(f=1),f}var h;(this._dirty||A==="reset")&&(this._dirty=!1,h=this._doReset(R)),this._modBy=g,this._modDataCount=y;var c=W&&W.step;if(K?this._dueEnd=K._outputDueEnd:this._dueEnd=this._count?this._count(this.context):Infinity,this._progress){var _=this._dueIndex,M=Math.min(c!=null?this._dueIndex+c:Infinity,this._dueEnd);if(!R&&(h||_<M)){var l=this._progress;if((0,C.kJ)(l))for(var O=0;O<l.length;O++)this._doProgress(l[O],_,M,g,y);else this._doProgress(l,_,M,g,y)}this._dueIndex=M;var d=this._settedOutputEnd!=null?this._settedOutputEnd:M;this._outputDueEnd=d}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},z.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},z.prototype._doProgress=function(W,K,R,b,A){F.reset(K,R,b,A),this._callingProgress=W,this._callingProgress({start:K,end:R,count:R-K,next:F.next},this.context)},z.prototype._doReset=function(W){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var K,R;!W&&this._reset&&(K=this._reset(this.context),K&&K.progress&&(R=K.forceFirstProgress,K=K.progress),(0,C.kJ)(K)&&!K.length&&(K=null)),this._progress=K,this._modBy=this._modDataCount=null;var b=this._downstream;return b&&b.dirty(),R},z.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},z.prototype.pipe=function(W){(this._downstream!==W||this._dirty)&&(this._downstream=W,W._upstream=this,W.dirty())},z.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},z.prototype.getUpstream=function(){return this._upstream},z.prototype.getDownstream=function(){return this._downstream},z.prototype.setOutputEnd=function(W){this._outputDueEnd=this._settedOutputEnd=W},z}(),F=function(){var z,W,K,R,b,A={reset:function(g,y,E,h){W=g,z=y,K=E,R=h,b=Math.ceil(R/K),A.next=K>1&&R>0?m:S}};return A;function S(){return W<z?W++:null}function m(){var g=W%b*K+Math.ceil(W/b),y=W>=z?null:g<R?g:W;return W++,y}}()},4130:function(Zt,pt){"use strict";function P(v){return v==null?0:v.length||1}function C(v){return v}var k=function(){function v(F,z,W,K,R,b){this._old=F,this._new=z,this._oldKeyGetter=W||C,this._newKeyGetter=K||C,this.context=R,this._diffModeMultiple=b==="multiple"}return v.prototype.add=function(F){return this._add=F,this},v.prototype.update=function(F){return this._update=F,this},v.prototype.updateManyToOne=function(F){return this._updateManyToOne=F,this},v.prototype.updateOneToMany=function(F){return this._updateOneToMany=F,this},v.prototype.updateManyToMany=function(F){return this._updateManyToMany=F,this},v.prototype.remove=function(F){return this._remove=F,this},v.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},v.prototype._executeOneToOne=function(){var F=this._old,z=this._new,W={},K=new Array(F.length),R=new Array(z.length);this._initIndexMap(F,null,K,"_oldKeyGetter"),this._initIndexMap(z,W,R,"_newKeyGetter");for(var b=0;b<F.length;b++){var A=K[b],S=W[A],m=P(S);if(m>1){var g=S.shift();S.length===1&&(W[A]=S[0]),this._update&&this._update(g,b)}else m===1?(W[A]=null,this._update&&this._update(S,b)):this._remove&&this._remove(b)}this._performRestAdd(R,W)},v.prototype._executeMultiple=function(){var F=this._old,z=this._new,W={},K={},R=[],b=[];this._initIndexMap(F,W,R,"_oldKeyGetter"),this._initIndexMap(z,K,b,"_newKeyGetter");for(var A=0;A<R.length;A++){var S=R[A],m=W[S],g=K[S],y=P(m),E=P(g);if(y>1&&E===1)this._updateManyToOne&&this._updateManyToOne(g,m),K[S]=null;else if(y===1&&E>1)this._updateOneToMany&&this._updateOneToMany(g,m),K[S]=null;else if(y===1&&E===1)this._update&&this._update(g,m),K[S]=null;else if(y>1&&E>1)this._updateManyToMany&&this._updateManyToMany(g,m),K[S]=null;else if(y>1)for(var h=0;h<y;h++)this._remove&&this._remove(m[h]);else this._remove&&this._remove(m)}this._performRestAdd(b,K)},v.prototype._performRestAdd=function(F,z){for(var W=0;W<F.length;W++){var K=F[W],R=z[K],b=P(R);if(b>1)for(var A=0;A<b;A++)this._add&&this._add(R[A]);else b===1&&this._add&&this._add(R);z[K]=null}},v.prototype._initIndexMap=function(F,z,W,K){for(var R=this._diffModeMultiple,b=0;b<F.length;b++){var A="_ec_"+this[K](F[b],b);if(R||(W[b]=A),!!z){var S=z[A],m=P(S);m===0?(z[A]=b,R&&W.push(A)):m===1?z[A]=[S,b]:S.push(b)}}},v}();pt.Z=k},43834:function(Zt,pt,P){"use strict";P.d(pt,{hG:function(){return K}});var C=P(33051),k=P(98407),v=P(99574),F="undefined",z=typeof Uint32Array===F?Array:Uint32Array,W=typeof Uint16Array===F?Array:Uint16Array,K=typeof Int32Array===F?Array:Int32Array,R=typeof Float64Array===F?Array:Float64Array,b={float:R,int:K,ordinal:Array,number:Array,time:R},A;function S(h){return h>65535?z:W}function m(){return[Infinity,-Infinity]}function g(h){var c=h.constructor;return c===Array?h.slice():new c(h)}function y(h,c,_,M,l){var O=b[_||"float"];if(l){var d=h[c],f=d&&d.length;if(f!==M){for(var u=new O(M),p=0;p<f;p++)u[p]=d[p];h[c]=u}}else h[c]=new O(M)}var E=function(){function h(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=(0,C.kW)()}return h.prototype.initData=function(c,_,M){this._provider=c,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var l=c.getSource(),O=this.defaultDimValueGetter=A[l.sourceFormat];this._dimValueGetter=M||O,this._rawExtent=[];var d=(0,v.QY)(l);this._dimensions=(0,C.UI)(_,function(f){return{type:f.type,property:f.property}}),this._initDataFromProvider(0,c.count())},h.prototype.getProvider=function(){return this._provider},h.prototype.getSource=function(){return this._provider.getSource()},h.prototype.ensureCalculationDimension=function(c,_){var M=this._calcDimNameToIdx,l=this._dimensions,O=M.get(c);if(O!=null){if(l[O].type===_)return O}else O=l.length;return l[O]={type:_},M.set(c,O),this._chunks[O]=new b[_||"float"](this._rawCount),this._rawExtent[O]=m(),O},h.prototype.collectOrdinalMeta=function(c,_){var M=this._chunks[c],l=this._dimensions[c],O=this._rawExtent,d=l.ordinalOffset||0,f=M.length;d===0&&(O[c]=m());for(var u=O[c],p=d;p<f;p++){var n=M[p]=_.parseAndCollect(M[p]);isNaN(n)||(u[0]=Math.min(n,u[0]),u[1]=Math.max(n,u[1]))}l.ordinalMeta=_,l.ordinalOffset=f,l.type="ordinal"},h.prototype.getOrdinalMeta=function(c){var _=this._dimensions[c],M=_.ordinalMeta;return M},h.prototype.getDimensionProperty=function(c){var _=this._dimensions[c];return _&&_.property},h.prototype.appendData=function(c){var _=this._provider,M=this.count();_.appendData(c);var l=_.count();return _.persistent||(l+=M),M<l&&this._initDataFromProvider(M,l,!0),[M,l]},h.prototype.appendValues=function(c,_){for(var M=this._chunks,l=this._dimensions,O=l.length,d=this._rawExtent,f=this.count(),u=f+Math.max(c.length,_||0),p=0;p<O;p++){var n=l[p];y(M,p,n.type,u,!0)}for(var i=[],t=f;t<u;t++)for(var r=t-f,e=0;e<O;e++){var n=l[e],a=A.arrayRows.call(this,c[r]||i,n.property,r,e);M[e][t]=a;var s=d[e];a<s[0]&&(s[0]=a),a>s[1]&&(s[1]=a)}return this._rawCount=this._count=u,{start:f,end:u}},h.prototype._initDataFromProvider=function(c,_,M){for(var l=this._provider,O=this._chunks,d=this._dimensions,f=d.length,u=this._rawExtent,p=(0,C.UI)(d,function(D){return D.property}),n=0;n<f;n++){var i=d[n];u[n]||(u[n]=m()),y(O,n,i.type,_,M)}if(l.fillStorage)l.fillStorage(c,_,O,u);else for(var t=[],r=c;r<_;r++){t=l.getItem(r,t);for(var e=0;e<f;e++){var a=O[e],s=this._dimValueGetter(t,p[e],r,e);a[r]=s;var o=u[e];s<o[0]&&(o[0]=s),s>o[1]&&(o[1]=s)}}!l.persistent&&l.clean&&l.clean(),this._rawCount=this._count=_,this._extent=[]},h.prototype.count=function(){return this._count},h.prototype.get=function(c,_){if(!(_>=0&&_<this._count))return NaN;var M=this._chunks[c];return M?M[this.getRawIndex(_)]:NaN},h.prototype.getValues=function(c,_){var M=[],l=[];if(_==null){_=c,c=[];for(var O=0;O<this._dimensions.length;O++)l.push(O)}else l=c;for(var O=0,d=l.length;O<d;O++)M.push(this.get(l[O],_));return M},h.prototype.getByRawIndex=function(c,_){if(!(_>=0&&_<this._rawCount))return NaN;var M=this._chunks[c];return M?M[_]:NaN},h.prototype.getSum=function(c){var _=this._chunks[c],M=0;if(_)for(var l=0,O=this.count();l<O;l++){var d=this.get(c,l);isNaN(d)||(M+=d)}return M},h.prototype.getMedian=function(c){var _=[];this.each([c],function(O){isNaN(O)||_.push(O)});var M=_.sort(function(O,d){return O-d}),l=this.count();return l===0?0:l%2==1?M[(l-1)/2]:(M[l/2]+M[l/2-1])/2},h.prototype.indexOfRawIndex=function(c){if(c>=this._rawCount||c<0)return-1;if(!this._indices)return c;var _=this._indices,M=_[c];if(M!=null&&M<this._count&&M===c)return c;for(var l=0,O=this._count-1;l<=O;){var d=(l+O)/2|0;if(_[d]<c)l=d+1;else if(_[d]>c)O=d-1;else return d}return-1},h.prototype.indicesOfNearest=function(c,_,M){var l=this._chunks,O=l[c],d=[];if(!O)return d;M==null&&(M=Infinity);for(var f=Infinity,u=-1,p=0,n=0,i=this.count();n<i;n++){var t=this.getRawIndex(n),r=_-O[t],e=Math.abs(r);e<=M&&((e<f||e===f&&r>=0&&u<0)&&(f=e,u=r,p=0),r===u&&(d[p++]=n))}return d.length=p,d},h.prototype.getIndices=function(){var c,_=this._indices;if(_){var M=_.constructor,l=this._count;if(M===Array){c=new M(l);for(var O=0;O<l;O++)c[O]=_[O]}else c=new M(_.buffer,0,l)}else{var M=S(this._rawCount);c=new M(this.count());for(var O=0;O<c.length;O++)c[O]=O}return c},h.prototype.filter=function(c,_){if(!this._count)return this;for(var M=this.clone(),l=M.count(),O=S(M._rawCount),d=new O(l),f=[],u=c.length,p=0,n=c[0],i=M._chunks,t=0;t<l;t++){var r=void 0,e=M.getRawIndex(t);if(u===0)r=_(t);else if(u===1){var a=i[n][e];r=_(a,t)}else{for(var s=0;s<u;s++)f[s]=i[c[s]][e];f[s]=t,r=_.apply(null,f)}r&&(d[p++]=e)}return p<l&&(M._indices=d),M._count=p,M._extent=[],M._updateGetRawIdx(),M},h.prototype.selectRange=function(c){var _=this.clone(),M=_._count;if(!M)return this;var l=(0,C.XP)(c),O=l.length;if(!O)return this;var d=_.count(),f=S(_._rawCount),u=new f(d),p=0,n=l[0],i=c[n][0],t=c[n][1],r=_._chunks,e=!1;if(!_._indices){var a=0;if(O===1){for(var s=r[l[0]],o=0;o<M;o++){var D=s[o];(D>=i&&D<=t||isNaN(D))&&(u[p++]=a),a++}e=!0}else if(O===2){for(var s=r[l[0]],B=r[l[1]],I=c[l[1]][0],L=c[l[1]][1],o=0;o<M;o++){var D=s[o],U=B[o];(D>=i&&D<=t||isNaN(D))&&(U>=I&&U<=L||isNaN(U))&&(u[p++]=a),a++}e=!0}}if(!e)if(O===1)for(var o=0;o<d;o++){var x=_.getRawIndex(o),D=r[l[0]][x];(D>=i&&D<=t||isNaN(D))&&(u[p++]=x)}else for(var o=0;o<d;o++){for(var T=!0,x=_.getRawIndex(o),w=0;w<O;w++){var V=l[w],D=r[V][x];(D<c[V][0]||D>c[V][1])&&(T=!1)}T&&(u[p++]=_.getRawIndex(o))}return p<d&&(_._indices=u),_._count=p,_._extent=[],_._updateGetRawIdx(),_},h.prototype.map=function(c,_){var M=this.clone(c);return this._updateDims(M,c,_),M},h.prototype.modify=function(c,_){this._updateDims(this,c,_)},h.prototype._updateDims=function(c,_,M){for(var l=c._chunks,O=[],d=_.length,f=c.count(),u=[],p=c._rawExtent,n=0;n<_.length;n++)p[_[n]]=m();for(var i=0;i<f;i++){for(var t=c.getRawIndex(i),r=0;r<d;r++)u[r]=l[_[r]][t];u[d]=i;var e=M&&M.apply(null,u);if(e!=null){typeof e!="object"&&(O[0]=e,e=O);for(var n=0;n<e.length;n++){var a=_[n],s=e[n],o=p[a],D=l[a];D&&(D[t]=s),s<o[0]&&(o[0]=s),s>o[1]&&(o[1]=s)}}}},h.prototype.lttbDownSample=function(c,_){var M=this.clone([c],!0),l=M._chunks,O=l[c],d=this.count(),f=0,u=Math.floor(1/_),p=this.getRawIndex(0),n,i,t,r=new(S(this._rawCount))(Math.min((Math.ceil(d/u)+2)*2,d));r[f++]=p;for(var e=1;e<d-1;e+=u){for(var a=Math.min(e+u,d-1),s=Math.min(e+u*2,d),o=(s+a)/2,D=0,B=a;B<s;B++){var I=this.getRawIndex(B),L=O[I];isNaN(L)||(D+=L)}D/=s-a;var U=e,x=Math.min(e+u,d),T=e-1,w=O[p];n=-1,t=U;for(var V=-1,N=0,B=U;B<x;B++){var I=this.getRawIndex(B),L=O[I];if(isNaN(L)){N++,V<0&&(V=I);continue}i=Math.abs((T-o)*(L-w)-(T-B)*(D-w)),i>n&&(n=i,t=I)}N>0&&N<x-U&&(r[f++]=Math.min(V,t),t=Math.max(V,t)),r[f++]=t,p=t}return r[f++]=this.getRawIndex(d-1),M._count=f,M._indices=r,M.getRawIndex=this._getRawIdx,M},h.prototype.downSample=function(c,_,M,l){for(var O=this.clone([c],!0),d=O._chunks,f=[],u=Math.floor(1/_),p=d[c],n=this.count(),i=O._rawExtent[c]=m(),t=new(S(this._rawCount))(Math.ceil(n/u)),r=0,e=0;e<n;e+=u){u>n-e&&(u=n-e,f.length=u);for(var a=0;a<u;a++){var s=this.getRawIndex(e+a);f[a]=p[s]}var o=M(f),D=this.getRawIndex(Math.min(e+l(f,o)||0,n-1));p[D]=o,o<i[0]&&(i[0]=o),o>i[1]&&(i[1]=o),t[r++]=D}return O._count=r,O._indices=t,O._updateGetRawIdx(),O},h.prototype.each=function(c,_){if(!!this._count)for(var M=c.length,l=this._chunks,O=0,d=this.count();O<d;O++){var f=this.getRawIndex(O);switch(M){case 0:_(O);break;case 1:_(l[c[0]][f],O);break;case 2:_(l[c[0]][f],l[c[1]][f],O);break;default:for(var u=0,p=[];u<M;u++)p[u]=l[c[u]][f];p[u]=O,_.apply(null,p)}}},h.prototype.getDataExtent=function(c){var _=this._chunks[c],M=m();if(!_)return M;var l=this.count(),O=!this._indices,d;if(O)return this._rawExtent[c].slice();if(d=this._extent[c],d)return d.slice();d=M;for(var f=d[0],u=d[1],p=0;p<l;p++){var n=this.getRawIndex(p),i=_[n];i<f&&(f=i),i>u&&(u=i)}return d=[f,u],this._extent[c]=d,d},h.prototype.getRawDataItem=function(c){var _=this.getRawIndex(c);if(this._provider.persistent)return this._provider.getItem(_);for(var M=[],l=this._chunks,O=0;O<l.length;O++)M.push(l[O][_]);return M},h.prototype.clone=function(c,_){var M=new h,l=this._chunks,O=c&&(0,C.u4)(c,function(f,u){return f[u]=!0,f},{});if(O)for(var d=0;d<l.length;d++)M._chunks[d]=O[d]?g(l[d]):l[d];else M._chunks=l;return this._copyCommonProps(M),_||(M._indices=this._cloneIndices()),M._updateGetRawIdx(),M},h.prototype._copyCommonProps=function(c){c._count=this._count,c._rawCount=this._rawCount,c._provider=this._provider,c._dimensions=this._dimensions,c._extent=(0,C.d9)(this._extent),c._rawExtent=(0,C.d9)(this._rawExtent)},h.prototype._cloneIndices=function(){if(this._indices){var c=this._indices.constructor,_=void 0;if(c===Array){var M=this._indices.length;_=new c(M);for(var l=0;l<M;l++)_[l]=this._indices[l]}else _=new c(this._indices);return _}return null},h.prototype._getRawIdxIdentity=function(c){return c},h.prototype._getRawIdx=function(c){return c<this._count&&c>=0?this._indices[c]:-1},h.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},h.internalField=function(){function c(_,M,l,O){return(0,k.yQ)(_[O],this._dimensions[O])}A={arrayRows:c,objectRows:function(_,M,l,O){return(0,k.yQ)(_[M],this._dimensions[O])},keyedColumns:c,original:function(_,M,l,O){var d=_&&(_.value==null?_:_.value);return(0,k.yQ)(d instanceof Array?d[O]:d,this._dimensions[O])},typedArray:function(_,M,l,O){return _[O]}}}(),h}();pt.ZP=E},51401:function(Zt,pt,P){"use strict";var C=P(33051),k=0,v=function(){function z(W){this.categories=W.categories||[],this._needCollect=W.needCollect,this._deduplication=W.deduplication,this.uid=++k}return z.createByAxisModel=function(W){var K=W.option,R=K.data,b=R&&(0,C.UI)(R,F);return new z({categories:b,needCollect:!b,deduplication:K.dedplication!==!1})},z.prototype.getOrdinal=function(W){return this._getOrCreateMap().get(W)},z.prototype.parseAndCollect=function(W){var K,R=this._needCollect;if(!(0,C.HD)(W)&&!R)return W;if(R&&!this._deduplication)return K=this.categories.length,this.categories[K]=W,K;var b=this._getOrCreateMap();return K=b.get(W),K==null&&(R?(K=this.categories.length,this.categories[K]=W,b.set(W,K)):K=NaN),K},z.prototype._getOrCreateMap=function(){return this._map||(this._map=(0,C.kW)(this.categories))},z}();function F(z){return(0,C.Kn)(z)&&z.value!=null?z.value:z+""}pt.Z=v},5101:function(Zt,pt,P){"use strict";var C=P(33051),k=P(1497),v=P(4130),F=P(68540),z=P(10381),W=P(35440),K=P(94279),R=P(32234),b=P(30106),A=P(99574),S=P(43834),m=P(31029),g=C.Kn,y=C.UI,E=typeof Int32Array=="undefined"?Array:Int32Array,h="e\0\0",c=-1,_=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],M=["_approximateExtent"],l,O,d,f,u,p,n,i=function(){function t(r,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"];var a,s=!1;(0,m.bB)(r)?(a=r.dimensions,this._dimOmitted=r.isDimensionOmitted(),this._schema=r):(s=!0,a=r),a=a||["x","y"];for(var o={},D=[],B={},I=!1,L={},U=0;U<a.length;U++){var x=a[U],T=C.HD(x)?new W.Z({name:x}):x instanceof W.Z?x:new W.Z(x),w=T.name;T.type=T.type||"float",T.coordDim||(T.coordDim=w,T.coordDimIndex=0);var V=T.otherDims=T.otherDims||{};D.push(w),o[w]=T,L[w]!=null&&(I=!0),T.createInvertedIndices&&(B[w]=[]),V.itemName===0&&(this._nameDimIdx=U),V.itemId===0&&(this._idDimIdx=U),s&&(T.storeDimIndex=U)}if(this.dimensions=D,this._dimInfos=o,this._initGetDimensionInfo(I),this.hostModel=e,this._invertedIndicesMap=B,this._dimOmitted){var N=this._dimIdxToName=C.kW();C.S6(D,function(G){N.set(o[G].storeDimIndex,G)})}}return t.prototype.getDimension=function(r){var e=this._recognizeDimIndex(r);if(e==null)return r;if(e=r,!this._dimOmitted)return this.dimensions[e];var a=this._dimIdxToName.get(e);if(a!=null)return a;var s=this._schema.getSourceDimension(e);if(s)return s.name},t.prototype.getDimensionIndex=function(r){var e=this._recognizeDimIndex(r);if(e!=null)return e;if(r==null)return-1;var a=this._getDimInfo(r);return a?a.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(r):-1},t.prototype._recognizeDimIndex=function(r){if(C.hj(r)||r!=null&&!isNaN(r)&&!this._getDimInfo(r)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(r)<0))return+r},t.prototype._getStoreDimIndex=function(r){var e=this.getDimensionIndex(r);return e},t.prototype.getDimensionInfo=function(r){return this._getDimInfo(this.getDimension(r))},t.prototype._initGetDimensionInfo=function(r){var e=this._dimInfos;this._getDimInfo=r?function(a){return e.hasOwnProperty(a)?e[a]:void 0}:function(a){return e[a]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(r,e){var a=this._dimSummary;if(e==null)return a.encodeFirstDimNotExtra[r];var s=a.encode[r];return s?s[e]:null},t.prototype.mapDimensionsAll=function(r){var e=this._dimSummary,a=e.encode[r];return(a||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(r,e,a){var s=this,o;if(r instanceof S.ZP&&(o=r),!o){var D=this.dimensions,B=(0,A.Ld)(r)||C.zG(r)?new F.Pl(r,D.length):r;o=new S.ZP;var I=y(D,function(L){return{type:s._dimInfos[L].type,property:L}});o.initData(B,I,a)}this._store=o,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,o.count()),this._dimSummary=(0,z.y)(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(r){var e=this._store.appendData(r);this._doInit(e[0],e[1])},t.prototype.appendValues=function(r,e){var a=this._store.appendValues(r,e.length),s=a.start,o=a.end,D=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var B=s;B<o;B++){var I=B-s;this._nameList[B]=e[I],D&&n(this,B)}},t.prototype._updateOrdinalMeta=function(){for(var r=this._store,e=this.dimensions,a=0;a<e.length;a++){var s=this._dimInfos[e[a]];s.ordinalMeta&&r.collectOrdinalMeta(s.storeDimIndex,s.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var r=this._store.getProvider();return this._idDimIdx==null&&r.getSource().sourceFormat!==K.J5&&!r.fillStorage},t.prototype._doInit=function(r,e){if(!(r>=e)){var a=this._store,s=a.getProvider();this._updateOrdinalMeta();var o=this._nameList,D=this._idList,B=s.getSource().sourceFormat,I=B===K.cy;if(I&&!s.pure)for(var L=[],U=r;U<e;U++){var x=s.getItem(U,L);if(!this.hasItemOption&&(0,R.Co)(x)&&(this.hasItemOption=!0),x){var T=x.name;o[U]==null&&T!=null&&(o[U]=(0,R.U5)(T,null));var w=x.id;D[U]==null&&w!=null&&(D[U]=(0,R.U5)(w,null))}}if(this._shouldMakeIdFromName())for(var U=r;U<e;U++)n(this,U);l(this)}},t.prototype.getApproximateExtent=function(r){return this._approximateExtent[r]||this._store.getDataExtent(this._getStoreDimIndex(r))},t.prototype.setApproximateExtent=function(r,e){e=this.getDimension(e),this._approximateExtent[e]=r.slice()},t.prototype.getCalculationInfo=function(r){return this._calculationInfo[r]},t.prototype.setCalculationInfo=function(r,e){g(r)?C.l7(this._calculationInfo,r):this._calculationInfo[r]=e},t.prototype.getName=function(r){var e=this.getRawIndex(r),a=this._nameList[e];return a==null&&this._nameDimIdx!=null&&(a=d(this,this._nameDimIdx,e)),a==null&&(a=""),a},t.prototype._getCategory=function(r,e){var a=this._store.get(r,e),s=this._store.getOrdinalMeta(r);return s?s.categories[a]:a},t.prototype.getId=function(r){return O(this,this.getRawIndex(r))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(r,e){var a=this._store,s=this._dimInfos[r];if(s)return a.get(s.storeDimIndex,e)},t.prototype.getByRawIndex=function(r,e){var a=this._store,s=this._dimInfos[r];if(s)return a.getByRawIndex(s.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(r){return this._store.getDataExtent(this._getStoreDimIndex(r))},t.prototype.getSum=function(r){return this._store.getSum(this._getStoreDimIndex(r))},t.prototype.getMedian=function(r){return this._store.getMedian(this._getStoreDimIndex(r))},t.prototype.getValues=function(r,e){var a=this,s=this._store;return C.kJ(r)?s.getValues(y(r,function(o){return a._getStoreDimIndex(o)}),e):s.getValues(r)},t.prototype.hasValue=function(r){for(var e=this._dimSummary.dataDimIndicesOnCoord,a=0,s=e.length;a<s;a++)if(isNaN(this._store.get(e[a],r)))return!1;return!0},t.prototype.indexOfName=function(r){for(var e=0,a=this._store.count();e<a;e++)if(this.getName(e)===r)return e;return-1},t.prototype.getRawIndex=function(r){return this._store.getRawIndex(r)},t.prototype.indexOfRawIndex=function(r){return this._store.indexOfRawIndex(r)},t.prototype.rawIndexOf=function(r,e){var a=r&&this._invertedIndicesMap[r],s=a[e];return s==null||isNaN(s)?c:s},t.prototype.indicesOfNearest=function(r,e,a){return this._store.indicesOfNearest(this._getStoreDimIndex(r),e,a)},t.prototype.each=function(r,e,a){"use strict";C.mf(r)&&(a=e,e=r,r=[]);var s=a||this,o=y(f(r),this._getStoreDimIndex,this);this._store.each(o,s?C.ak(e,s):e)},t.prototype.filterSelf=function(r,e,a){"use strict";C.mf(r)&&(a=e,e=r,r=[]);var s=a||this,o=y(f(r),this._getStoreDimIndex,this);return this._store=this._store.filter(o,s?C.ak(e,s):e),this},t.prototype.selectRange=function(r){"use strict";var e=this,a={},s=C.XP(r),o=[];return C.S6(s,function(D){var B=e._getStoreDimIndex(D);a[B]=r[D],o.push(B)}),this._store=this._store.selectRange(a),this},t.prototype.mapArray=function(r,e,a){"use strict";C.mf(r)&&(a=e,e=r,r=[]),a=a||this;var s=[];return this.each(r,function(){s.push(e&&e.apply(this,arguments))},a),s},t.prototype.map=function(r,e,a,s){"use strict";var o=a||s||this,D=y(f(r),this._getStoreDimIndex,this),B=p(this);return B._store=this._store.map(D,o?C.ak(e,o):e),B},t.prototype.modify=function(r,e,a,s){var o=this,D=a||s||this,B=y(f(r),this._getStoreDimIndex,this);this._store.modify(B,D?C.ak(e,D):e)},t.prototype.downSample=function(r,e,a,s){var o=p(this);return o._store=this._store.downSample(this._getStoreDimIndex(r),e,a,s),o},t.prototype.lttbDownSample=function(r,e){var a=p(this);return a._store=this._store.lttbDownSample(this._getStoreDimIndex(r),e),a},t.prototype.getRawDataItem=function(r){return this._store.getRawDataItem(r)},t.prototype.getItemModel=function(r){var e=this.hostModel,a=this.getRawDataItem(r);return new k.Z(a,e,e&&e.ecModel)},t.prototype.diff=function(r){var e=this;return new v.Z(r?r.getStore().getIndices():[],this.getStore().getIndices(),function(a){return O(r,a)},function(a){return O(e,a)})},t.prototype.getVisual=function(r){var e=this._visual;return e&&e[r]},t.prototype.setVisual=function(r,e){this._visual=this._visual||{},g(r)?C.l7(this._visual,r):this._visual[r]=e},t.prototype.getItemVisual=function(r,e){var a=this._itemVisuals[r],s=a&&a[e];return s==null?this.getVisual(e):s},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(r,e){var a=this._itemVisuals,s=a[r];s||(s=a[r]={});var o=s[e];return o==null&&(o=this.getVisual(e),C.kJ(o)?o=o.slice():g(o)&&(o=C.l7({},o)),s[e]=o),o},t.prototype.setItemVisual=function(r,e,a){var s=this._itemVisuals[r]||{};this._itemVisuals[r]=s,g(e)?C.l7(s,e):s[e]=a},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(r,e){g(r)?C.l7(this._layout,r):this._layout[r]=e},t.prototype.getLayout=function(r){return this._layout[r]},t.prototype.getItemLayout=function(r){return this._itemLayouts[r]},t.prototype.setItemLayout=function(r,e,a){this._itemLayouts[r]=a?C.l7(this._itemLayouts[r]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(r,e){var a=this.hostModel&&this.hostModel.seriesIndex;(0,b.Q)(a,this.dataType,r,e),this._graphicEls[r]=e},t.prototype.getItemGraphicEl=function(r){return this._graphicEls[r]},t.prototype.eachItemGraphicEl=function(r,e){C.S6(this._graphicEls,function(a,s){a&&r&&r.call(e,a,s)})},t.prototype.cloneShallow=function(r){return r||(r=new t(this._schema?this._schema:y(this.dimensions,this._getDimInfo,this),this.hostModel)),u(r,this),r._store=this._store,r},t.prototype.wrapMethod=function(r,e){var a=this[r];!C.mf(a)||(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(r),this[r]=function(){var s=a.apply(this,arguments);return e.apply(this,[s].concat(C.tP(arguments)))})},t.internalField=function(){l=function(r){var e=r._invertedIndicesMap;C.S6(e,function(a,s){var o=r._dimInfos[s],D=o.ordinalMeta,B=r._store;if(D){a=e[s]=new E(D.categories.length);for(var I=0;I<a.length;I++)a[I]=c;for(var I=0;I<B.count();I++)a[B.get(o.storeDimIndex,I)]=I}})},d=function(r,e,a){return(0,R.U5)(r._getCategory(e,a),null)},O=function(r,e){var a=r._idList[e];return a==null&&r._idDimIdx!=null&&(a=d(r,r._idDimIdx,e)),a==null&&(a=h+e),a},f=function(r){return C.kJ(r)||(r=r!=null?[r]:[]),r},p=function(r){var e=new t(r._schema?r._schema:y(r.dimensions,r._getDimInfo,r),r.hostModel);return u(e,r),e},u=function(r,e){C.S6(_.concat(e.__wrappedMethods||[]),function(a){e.hasOwnProperty(a)&&(r[a]=e[a])}),r.__wrappedMethods=e.__wrappedMethods,C.S6(M,function(a){r[a]=C.d9(e[a])}),r._calculationInfo=C.l7({},e._calculationInfo)},n=function(r,e){var a=r._nameList,s=r._idList,o=r._nameDimIdx,D=r._idDimIdx,B=a[e],I=s[e];if(B==null&&o!=null&&(a[e]=B=d(r,o,e)),I==null&&D!=null&&(s[e]=I=d(r,D,e)),I==null&&B!=null){var L=r._nameRepeatCount,U=L[B]=(L[B]||0)+1;I=B,U>1&&(I+="__ec__"+U),s[e]=I}}}(),t}();pt.Z=i},35440:function(Zt,pt,P){"use strict";var C=P(33051),k=function(){function v(F){this.otherDims={},F!=null&&C.l7(this,F)}return v}();pt.Z=k},99574:function(Zt,pt,P){"use strict";P.d(pt,{Ld:function(){return W},_P:function(){return K},nx:function(){return R},ML:function(){return b},Kp:function(){return A},QY:function(){return E}});var C=P(33051),k=P(94279),v=P(32234),F=P(61772),z=function(){function h(c){this.data=c.data||(c.sourceFormat===k.hL?{}:[]),this.sourceFormat=c.sourceFormat||k.RA,this.seriesLayoutBy=c.seriesLayoutBy||k.fY,this.startIndex=c.startIndex||0,this.dimensionsDetectedCount=c.dimensionsDetectedCount,this.metaRawOption=c.metaRawOption;var _=this.dimensionsDefine=c.dimensionsDefine;if(_)for(var M=0;M<_.length;M++){var l=_[M];l.type==null&&(0,F.u7)(this,M)===F.Dq.Must&&(l.type="ordinal")}}return h}();function W(h){return h instanceof z}function K(h,c,_){_=_||A(h);var M=c.seriesLayoutBy,l=S(h,_,M,c.sourceHeader,c.dimensions),O=new z({data:h,sourceFormat:_,seriesLayoutBy:M,dimensionsDefine:l.dimensionsDefine,startIndex:l.startIndex,dimensionsDetectedCount:l.dimensionsDetectedCount,metaRawOption:(0,C.d9)(c)});return O}function R(h){return new z({data:h,sourceFormat:(0,C.fU)(h)?k.J5:k.cy})}function b(h){return new z({data:h.data,sourceFormat:h.sourceFormat,seriesLayoutBy:h.seriesLayoutBy,dimensionsDefine:(0,C.d9)(h.dimensionsDefine),startIndex:h.startIndex,dimensionsDetectedCount:h.dimensionsDetectedCount})}function A(h){var c=k.RA;if((0,C.fU)(h))c=k.J5;else if((0,C.kJ)(h)){h.length===0&&(c=k.XD);for(var _=0,M=h.length;_<M;_++){var l=h[_];if(l!=null){if((0,C.kJ)(l)||(0,C.fU)(l)){c=k.XD;break}else if((0,C.Kn)(l)){c=k.qb;break}}}}else if((0,C.Kn)(h)){for(var O in h)if((0,C.RI)(h,O)&&(0,C.zG)(h[O])){c=k.hL;break}}return c}function S(h,c,_,M,l){var O,d;if(!h)return{dimensionsDefine:g(l),startIndex:d,dimensionsDetectedCount:O};if(c===k.XD){var f=h;M==="auto"||M==null?y(function(p){p!=null&&p!=="-"&&((0,C.HD)(p)?d==null&&(d=1):d=0)},_,f,10):d=(0,C.hj)(M)?M:M?1:0,!l&&d===1&&(l=[],y(function(p,n){l[n]=p!=null?p+"":""},_,f,Infinity)),O=l?l.length:_===k.Wc?f.length:f[0]?f[0].length:null}else if(c===k.qb)l||(l=m(h));else if(c===k.hL)l||(l=[],(0,C.S6)(h,function(p,n){l.push(n)}));else if(c===k.cy){var u=(0,v.C4)(h[0]);O=(0,C.kJ)(u)&&u.length||1}else c===k.J5;return{startIndex:d,dimensionsDefine:g(l),dimensionsDetectedCount:O}}function m(h){for(var c=0,_;c<h.length&&!(_=h[c++]););if(_)return(0,C.XP)(_)}function g(h){if(!!h){var c=(0,C.kW)();return(0,C.UI)(h,function(_,M){_=(0,C.Kn)(_)?_:{name:_};var l={name:_.name,displayName:_.displayName,type:_.type};if(l.name==null)return l;l.name+="",l.displayName==null&&(l.displayName=l.name);var O=c.get(l.name);return O?l.name+="-"+O.count++:c.set(l.name,{count:1}),l})}}function y(h,c,_,M){if(c===k.Wc)for(var l=0;l<_.length&&l<M;l++)h(_[l]?_[l][0]:null,l);else for(var O=_[0]||[],l=0;l<O.length&&l<M;l++)h(O[l],l)}function E(h){var c=h.sourceFormat;return c===k.qb||c===k.hL}},31029:function(Zt,pt,P){"use strict";P.d(pt,{Eo:function(){return W},bB:function(){return K},v5:function(){return R},Jj:function(){return b},Jl:function(){return A}});var C=P(33051),k=P(32234),v=P(99574),F=(0,k.Yf)(),z={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},W=function(){function S(m){this.dimensions=m.dimensions,this._dimOmitted=m.dimensionOmitted,this.source=m.source,this._fullDimCount=m.fullDimensionCount,this._updateDimOmitted(m.dimensionOmitted)}return S.prototype.isDimensionOmitted=function(){return this._dimOmitted},S.prototype._updateDimOmitted=function(m){this._dimOmitted=m,!!m&&(this._dimNameMap||(this._dimNameMap=b(this.source)))},S.prototype.getSourceDimensionIndex=function(m){return(0,C.pD)(this._dimNameMap.get(m),-1)},S.prototype.getSourceDimension=function(m){var g=this.source.dimensionsDefine;if(g)return g[m]},S.prototype.makeStoreSchema=function(){for(var m=this._fullDimCount,g=(0,v.QY)(this.source),y=!A(m),E="",h=[],c=0,_=0;c<m;c++){var M=void 0,l=void 0,O=void 0,d=this.dimensions[_];if(d&&d.storeDimIndex===c)M=g?d.name:null,l=d.type,O=d.ordinalMeta,_++;else{var f=this.getSourceDimension(c);f&&(M=g?f.name:null,l=f.type)}h.push({property:M,type:l,ordinalMeta:O}),g&&M!=null&&(!d||!d.isCalculationCoord)&&(E+=y?M.replace(/\`/g,"`1").replace(/\$/g,"`2"):M),E+="$",E+=z[l]||"f",O&&(E+=O.uid),E+="$"}var u=this.source,p=[u.seriesLayoutBy,u.startIndex,E].join("$$");return{dimensions:h,hash:p}},S.prototype.makeOutputDimensionNames=function(){for(var m=[],g=0,y=0;g<this._fullDimCount;g++){var E=void 0,h=this.dimensions[y];if(h&&h.storeDimIndex===g)h.isCalculationCoord||(E=h.name),y++;else{var c=this.getSourceDimension(g);c&&(E=c.name)}m.push(E)}return m},S.prototype.appendCalculationDimension=function(m){this.dimensions.push(m),m.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},S}();function K(S){return S instanceof W}function R(S){for(var m=(0,C.kW)(),g=0;g<(S||[]).length;g++){var y=S[g],E=(0,C.Kn)(y)?y.name:y;E!=null&&m.get(E)==null&&m.set(E,g)}return m}function b(S){var m=F(S);return m.dimNameMap||(m.dimNameMap=R(S.dimensionsDefine))}function A(S){return S>30}},55623:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return A}});var C=P(94279),k=P(35440),v=P(33051),F=P(99574),z=P(43834),W=P(32234),K=P(61772),R=P(31029);function b(y,E){return A(y,E).dimensions}function A(y,E){(0,F.Ld)(y)||(y=(0,F.nx)(y)),E=E||{};var h=E.coordDimensions||[],c=E.dimensionsDefine||y.dimensionsDefine||[],_=(0,v.kW)(),M=[],l=m(y,h,c,E.dimensionsCount),O=E.canOmitUnusedDimensions&&(0,R.Jl)(l),d=c===y.dimensionsDefine,f=d?(0,R.Jj)(y):(0,R.v5)(c),u=E.encodeDefine;!u&&E.encodeDefaulter&&(u=E.encodeDefaulter(y,l));for(var p=(0,v.kW)(u),n=new z.hG(l),i=0;i<n.length;i++)n[i]=-1;function t(x){var T=n[x];if(T<0){var w=c[x],V=(0,v.Kn)(w)?w:{name:w},N=new k.Z,G=V.name;G!=null&&f.get(G)!=null&&(N.name=N.displayName=G),V.type!=null&&(N.type=V.type),V.displayName!=null&&(N.displayName=V.displayName);var Z=M.length;return n[x]=Z,N.storeDimIndex=x,M.push(N),N}return M[T]}if(!O)for(var i=0;i<l;i++)t(i);p.each(function(x,T){var w=(0,W.kF)(x).slice();if(w.length===1&&!(0,v.HD)(w[0])&&w[0]<0){p.set(T,!1);return}var V=p.set(T,[]);(0,v.S6)(w,function(N,G){var Z=(0,v.HD)(N)?f.get(N):N;Z!=null&&Z<l&&(V[G]=Z,e(t(Z),T,G))})});var r=0;(0,v.S6)(h,function(x){var T,w,V,N;if((0,v.HD)(x))T=x,N={};else{N=x,T=N.name;var G=N.ordinalMeta;N.ordinalMeta=null,N=(0,v.l7)({},N),N.ordinalMeta=G,w=N.dimsDef,V=N.otherDims,N.name=N.coordDim=N.coordDimIndex=N.dimsDef=N.otherDims=null}var Z=p.get(T);if(Z!==!1){if(Z=(0,W.kF)(Z),!Z.length)for(var H=0;H<(w&&w.length||1);H++){for(;r<l&&t(r).coordDim!=null;)r++;r<l&&Z.push(r++)}(0,v.S6)(Z,function(X,ut){var lt=t(X);if(d&&N.type!=null&&(lt.type=N.type),e((0,v.ce)(lt,N),T,ut),lt.name==null&&w){var ot=w[ut];!(0,v.Kn)(ot)&&(ot={name:ot}),lt.name=lt.displayName=ot.name,lt.defaultTooltip=ot.defaultTooltip}V&&(0,v.ce)(lt.otherDims,V)})}});function e(x,T,w){C.f7.get(T)!=null?x.otherDims[T]=w:(x.coordDim=T,x.coordDimIndex=w,_.set(T,!0))}var a=E.generateCoord,s=E.generateCoordCount,o=s!=null;s=a?s||1:0;var D=a||"value";function B(x){x.name==null&&(x.name=x.coordDim)}if(O)(0,v.S6)(M,function(x){B(x)}),M.sort(function(x,T){return x.storeDimIndex-T.storeDimIndex});else for(var I=0;I<l;I++){var L=t(I),U=L.coordDim;U==null&&(L.coordDim=g(D,_,o),L.coordDimIndex=0,(!a||s<=0)&&(L.isExtraCoord=!0),s--),B(L),L.type==null&&((0,K.u7)(y,I)===K.Dq.Must||L.isExtraCoord&&(L.otherDims.itemName!=null||L.otherDims.seriesName!=null))&&(L.type="ordinal")}return S(M),new R.Eo({source:y,dimensions:M,fullDimensionCount:l,dimensionOmitted:O})}function S(y){for(var E=(0,v.kW)(),h=0;h<y.length;h++){var c=y[h],_=c.name,M=E.get(_)||0;M>0&&(c.name=_+(M-1)),M++,E.set(_,M)}}function m(y,E,h,c){var _=Math.max(y.dimensionsDetectedCount||1,E.length,h.length,c||0);return(0,v.S6)(E,function(M){var l;(0,v.Kn)(M)&&(l=M.dimsDef)&&(_=Math.max(_,l.length))}),_}function g(y,E,h){if(h||E.hasKey(y)){for(var c=0;E.hasKey(y+c);)c++;y+=c}return E.set(y,!0),y}},68540:function(Zt,pt,P){"use strict";P.d(pt,{Pl:function(){return A},_j:function(){return g},a:function(){return h},tB:function(){return M},hk:function(){return O}});var C=P(33051),k=P(32234),v=P(99574),F=P(94279),z,W,K,R,b,A=function(){function f(u,p){var n=(0,v.Ld)(u)?u:(0,v.nx)(u);this._source=n;var i=this._data=n.data;n.sourceFormat===F.J5&&(this._offset=0,this._dimSize=p,this._data=i),b(this,i,n)}return f.prototype.getSource=function(){return this._source},f.prototype.count=function(){return 0},f.prototype.getItem=function(u,p){},f.prototype.appendData=function(u){},f.prototype.clean=function(){},f.protoInitialize=function(){var u=f.prototype;u.pure=!1,u.persistent=!0}(),f.internalField=function(){var u;b=function(r,e,a){var s=a.sourceFormat,o=a.seriesLayoutBy,D=a.startIndex,B=a.dimensionsDefine,I=R[l(s,o)];if((0,C.l7)(r,I),s===F.J5)r.getItem=p,r.count=i,r.fillStorage=n;else{var L=g(s,o);r.getItem=(0,C.ak)(L,null,e,D,B);var U=h(s,o);r.count=(0,C.ak)(U,null,e,D,B)}};var p=function(r,e){r=r-this._offset,e=e||[];for(var a=this._data,s=this._dimSize,o=s*r,D=0;D<s;D++)e[D]=a[o+D];return e},n=function(r,e,a,s){for(var o=this._data,D=this._dimSize,B=0;B<D;B++){for(var I=s[B],L=I[0]==null?Infinity:I[0],U=I[1]==null?-Infinity:I[1],x=e-r,T=a[B],w=0;w<x;w++){var V=o[w*D+B];T[r+w]=V,V<L&&(L=V),V>U&&(U=V)}I[0]=L,I[1]=U}},i=function(){return this._data?this._data.length/this._dimSize:0};R=(u={},u[F.XD+"_"+F.fY]={pure:!0,appendData:t},u[F.XD+"_"+F.Wc]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},u[F.qb]={pure:!0,appendData:t},u[F.hL]={pure:!0,appendData:function(r){var e=this._data;(0,C.S6)(r,function(a,s){for(var o=e[s]||(e[s]=[]),D=0;D<(a||[]).length;D++)o.push(a[D])})}},u[F.cy]={appendData:t},u[F.J5]={persistent:!1,pure:!0,appendData:function(r){this._data=r},clean:function(){this._offset+=this.count(),this._data=null}},u);function t(r){for(var e=0;e<r.length;e++)this._data.push(r[e])}}(),f}(),S=function(f,u,p,n){return f[n]},m=(z={},z[F.XD+"_"+F.fY]=function(f,u,p,n){return f[n+u]},z[F.XD+"_"+F.Wc]=function(f,u,p,n,i){n+=u;for(var t=i||[],r=f,e=0;e<r.length;e++){var a=r[e];t[e]=a?a[n]:null}return t},z[F.qb]=S,z[F.hL]=function(f,u,p,n,i){for(var t=i||[],r=0;r<p.length;r++){var e=p[r].name,a=f[e];t[r]=a?a[n]:null}return t},z[F.cy]=S,z);function g(f,u){var p=m[l(f,u)];return p}var y=function(f,u,p){return f.length},E=(W={},W[F.XD+"_"+F.fY]=function(f,u,p){return Math.max(0,f.length-u)},W[F.XD+"_"+F.Wc]=function(f,u,p){var n=f[0];return n?Math.max(0,n.length-u):0},W[F.qb]=y,W[F.hL]=function(f,u,p){var n=p[0].name,i=f[n];return i?i.length:0},W[F.cy]=y,W);function h(f,u){var p=E[l(f,u)];return p}var c=function(f,u,p){return f[u]},_=(K={},K[F.XD]=c,K[F.qb]=function(f,u,p){return f[p]},K[F.hL]=c,K[F.cy]=function(f,u,p){var n=(0,k.C4)(f);return n instanceof Array?n[u]:n},K[F.J5]=c,K);function M(f){var u=_[f];return u}function l(f,u){return f===F.XD?f+"_"+u:f}function O(f,u,p){if(!!f){var n=f.getRawDataItem(u);if(n!=null){var i=f.getStore(),t=i.getSource().sourceFormat;if(p!=null){var r=f.getDimensionIndex(p),e=i.getDimensionProperty(r);return M(t)(n,r,e)}else{var a=n;return t===F.cy&&(a=(0,k.C4)(n)),a}}}}function d(f,u,p){if(!!f){var n=f.getStore().getSource().sourceFormat;if(!(n!==SOURCE_FORMAT_ORIGINAL&&n!==SOURCE_FORMAT_OBJECT_ROWS)){var i=f.getRawDataItem(u);if(n===SOURCE_FORMAT_ORIGINAL&&!isObject(i)&&(i=null),i)return i[p]}}}},99936:function(Zt,pt,P){"use strict";P.d(pt,{BM:function(){return v},M:function(){return z},IR:function(){return W}});var C=P(33051),k=P(31029);function v(K,R,b){b=b||{};var A=b.byIndex,S=b.stackedCoordDimension,m,g,y;F(R)?m=R:(g=R.schema,m=g.dimensions,y=R.store);var E=!!(K&&K.get("stack")),h,c,_,M;if((0,C.S6)(m,function(p,n){(0,C.HD)(p)&&(m[n]=p={name:p}),E&&!p.isExtraCoord&&(!A&&!h&&p.ordinalMeta&&(h=p),!c&&p.type!=="ordinal"&&p.type!=="time"&&(!S||S===p.coordDim)&&(c=p))}),c&&!A&&!h&&(A=!0),c){_="__\0ecstackresult_"+K.id,M="__\0ecstackedover_"+K.id,h&&(h.createInvertedIndices=!0);var l=c.coordDim,O=c.type,d=0;(0,C.S6)(m,function(p){p.coordDim===l&&d++});var f={name:_,coordDim:l,coordDimIndex:d,type:O,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:m.length},u={name:M,coordDim:M,coordDimIndex:d+1,type:O,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:m.length+1};g?(y&&(f.storeDimIndex=y.ensureCalculationDimension(M,O),u.storeDimIndex=y.ensureCalculationDimension(_,O)),g.appendCalculationDimension(f),g.appendCalculationDimension(u)):(m.push(f),m.push(u))}return{stackedDimension:c&&c.name,stackedByDimension:h&&h.name,isStackedByIndex:A,stackedOverDimension:M,stackResultDimension:_}}function F(K){return!(0,k.bB)(K.schema)}function z(K,R){return!!R&&R===K.getCalculationInfo("stackedDimension")}function W(K,R){return z(K,R)?K.getCalculationInfo("stackResultDimension"):R}},98407:function(Zt,pt,P){"use strict";P.d(pt,{yQ:function(){return F},ID:function(){return b}});var C=P(85669),k=P(33051),v=P(70175);function F(m,g){var y=g&&g.type;return y==="ordinal"?m:(y==="time"&&!(0,k.hj)(m)&&m!=null&&m!=="-"&&(m=+(0,C.sG)(m)),m==null||m===""?NaN:+m)}var z=(0,k.kW)({number:function(m){return parseFloat(m)},time:function(m){return+(0,C.sG)(m)},trim:function(m){return(0,k.HD)(m)?(0,k.fy)(m):m}});function W(m){return z.get(m)}var K={lt:function(m,g){return m<g},lte:function(m,g){return m<=g},gt:function(m,g){return m>g},gte:function(m,g){return m>=g}},R=function(){function m(g,y){if(!(0,k.hj)(y)){var E="";(0,v._y)(E)}this._opFn=K[g],this._rvalFloat=(0,C.FK)(y)}return m.prototype.evaluate=function(g){return(0,k.hj)(g)?this._opFn(g,this._rvalFloat):this._opFn((0,C.FK)(g),this._rvalFloat)},m}(),b=function(){function m(g,y){var E=g==="desc";this._resultLT=E?1:-1,y==null&&(y=E?"min":"max"),this._incomparable=y==="min"?-Infinity:Infinity}return m.prototype.evaluate=function(g,y){var E=(0,k.hj)(g)?g:(0,C.FK)(g),h=(0,k.hj)(y)?y:(0,C.FK)(y),c=isNaN(E),_=isNaN(h);if(c&&(E=this._incomparable),_&&(h=this._incomparable),c&&_){var M=(0,k.HD)(g),l=(0,k.HD)(y);M&&(E=l?g:0),l&&(h=M?y:0)}return E<h?this._resultLT:E>h?-this._resultLT:0},m}(),A=function(){function m(g,y){this._rval=y,this._isEQ=g,this._rvalTypeof=typeof y,this._rvalFloat=(0,C.FK)(y)}return m.prototype.evaluate=function(g){var y=g===this._rval;if(!y){var E=typeof g;E!==this._rvalTypeof&&(E==="number"||this._rvalTypeof==="number")&&(y=(0,C.FK)(g)===this._rvalFloat)}return this._isEQ?y:!y},m}();function S(m,g){return m==="eq"||m==="ne"?new A(m==="eq",g):hasOwn(K,m)?new R(m,g):null}},10381:function(Zt,pt,P){"use strict";P.d(pt,{y:function(){return F},T:function(){return W}});var C=P(33051),k=P(94279),v=function(){function R(b,A){this._encode=b,this._schema=A}return R.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},R.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},R}();function F(R,b){var A={},S=A.encode={},m=(0,C.kW)(),g=[],y=[],E={};(0,C.S6)(R.dimensions,function(l){var O=R.getDimensionInfo(l),d=O.coordDim;if(d){var f=O.coordDimIndex;z(S,d)[f]=l,O.isExtraCoord||(m.set(d,1),K(O.type)&&(g[0]=l),z(E,d)[f]=R.getDimensionIndex(O.name)),O.defaultTooltip&&y.push(l)}k.f7.each(function(u,p){var n=z(S,p),i=O.otherDims[p];i!=null&&i!==!1&&(n[i]=O.name)})});var h=[],c={};m.each(function(l,O){var d=S[O];c[O]=d[0],h=h.concat(d)}),A.dataDimsOnCoord=h,A.dataDimIndicesOnCoord=(0,C.UI)(h,function(l){return R.getDimensionInfo(l).storeDimIndex}),A.encodeFirstDimNotExtra=c;var _=S.label;_&&_.length&&(g=_.slice());var M=S.tooltip;return M&&M.length?y=M.slice():y.length||(y=g.slice()),S.defaultedLabel=g,S.defaultedTooltip=y,A.userOutput=new v(E,b),A}function z(R,b){return R.hasOwnProperty(b)||(R[b]=[]),R[b]}function W(R){return R==="category"?"ordinal":R==="time"?"time":"float"}function K(R){return!(R==="ordinal"||R==="time")}},61772:function(Zt,pt,P){"use strict";P.d(pt,{Dq:function(){return F},md:function(){return W},pY:function(){return K},Ss:function(){return R},Wd:function(){return b},JT:function(){return A},u7:function(){return S}});var C=P(32234),k=P(33051),v=P(94279),F={Must:1,Might:2,Not:3},z=(0,C.Yf)();function W(g){z(g).datasetMap=(0,k.kW)()}function K(g,y,E){var h={},c=b(y);if(!c||!g)return h;var _=[],M=[],l=y.ecModel,O=z(l).datasetMap,d=c.uid+"_"+E.seriesLayoutBy,f,u;g=g.slice(),(0,k.S6)(g,function(t,r){var e=(0,k.Kn)(t)?t:g[r]={name:t};e.type==="ordinal"&&f==null&&(f=r,u=i(e)),h[e.name]=[]});var p=O.get(d)||O.set(d,{categoryWayDim:u,valueWayDim:0});(0,k.S6)(g,function(t,r){var e=t.name,a=i(t);if(f==null){var s=p.valueWayDim;n(h[e],s,a),n(M,s,a),p.valueWayDim+=a}else if(f===r)n(h[e],0,a),n(_,0,a);else{var s=p.categoryWayDim;n(h[e],s,a),n(M,s,a),p.categoryWayDim+=a}});function n(t,r,e){for(var a=0;a<e;a++)t.push(r+a)}function i(t){var r=t.dimsDef;return r?r.length:1}return _.length&&(h.itemName=_),M.length&&(h.seriesName=M),h}function R(g,y,E){var h={},c=b(g);if(!c)return h;var _=y.sourceFormat,M=y.dimensionsDefine,l;(_===v.qb||_===v.hL)&&(0,k.S6)(M,function(f,u){((0,k.Kn)(f)?f.name:f)==="name"&&(l=u)});var O=function(){for(var f={},u={},p=[],n=0,i=Math.min(5,E);n<i;n++){var t=m(y.data,_,y.seriesLayoutBy,M,y.startIndex,n);p.push(t);var r=t===F.Not;if(r&&f.v==null&&n!==l&&(f.v=n),(f.n==null||f.n===f.v||!r&&p[f.n]===F.Not)&&(f.n=n),e(f)&&p[f.n]!==F.Not)return f;r||(t===F.Might&&u.v==null&&n!==l&&(u.v=n),(u.n==null||u.n===u.v)&&(u.n=n))}function e(a){return a.v!=null&&a.n!=null}return e(f)?f:e(u)?u:null}();if(O){h.value=[O.v];var d=l!=null?l:O.n;h.itemName=[d],h.seriesName=[d]}return h}function b(g){var y=g.get("data",!0);if(!y)return(0,C.HZ)(g.ecModel,"dataset",{index:g.get("datasetIndex",!0),id:g.get("datasetId",!0)},C.C6).models[0]}function A(g){return!g.get("transform",!0)&&!g.get("fromTransformResult",!0)?[]:(0,C.HZ)(g.ecModel,"dataset",{index:g.get("fromDatasetIndex",!0),id:g.get("fromDatasetId",!0)},C.C6).models}function S(g,y){return m(g.data,g.sourceFormat,g.seriesLayoutBy,g.dimensionsDefine,g.startIndex,y)}function m(g,y,E,h,c,_){var M,l=5;if((0,k.fU)(g))return F.Not;var O,d;if(h){var f=h[_];(0,k.Kn)(f)?(O=f.name,d=f.type):(0,k.HD)(f)&&(O=f)}if(d!=null)return d==="ordinal"?F.Must:F.Not;if(y===v.XD){var u=g;if(E===v.Wc){for(var p=u[_],n=0;n<(p||[]).length&&n<l;n++)if((M=o(p[c+n]))!=null)return M}else for(var n=0;n<u.length&&n<l;n++){var i=u[c+n];if(i&&(M=o(i[_]))!=null)return M}}else if(y===v.qb){var t=g;if(!O)return F.Not;for(var n=0;n<t.length&&n<l;n++){var r=t[n];if(r&&(M=o(r[O]))!=null)return M}}else if(y===v.hL){var e=g;if(!O)return F.Not;var p=e[O];if(!p||(0,k.fU)(p))return F.Not;for(var n=0;n<p.length&&n<l;n++)if((M=o(p[n]))!=null)return M}else if(y===v.cy)for(var a=g,n=0;n<a.length&&n<l;n++){var r=a[n],s=(0,C.C4)(r);if(!(0,k.kJ)(s))return F.Not;if((M=o(s[_]))!=null)return M}function o(D){var B=(0,k.HD)(D);if(D!=null&&isFinite(D)&&D!=="")return B?F.Might:F.Not;if(B&&D!=="-")return F.Must}return F.Not}},10437:function(Zt,pt,P){"use strict";P.d(pt,{DA:function(){return E},vK:function(){return h}});var C=P(94279),k=P(32234),v=P(33051),F=P(68540),z=P(98407),W=P(70175),K=P(99574),R=function(){function M(){}return M.prototype.getRawData=function(){throw new Error("not supported")},M.prototype.getRawDataItem=function(l){throw new Error("not supported")},M.prototype.cloneRawData=function(){},M.prototype.getDimensionInfo=function(l){},M.prototype.cloneAllDimensionInfo=function(){},M.prototype.count=function(){},M.prototype.retrieveValue=function(l,O){},M.prototype.retrieveValueFromItem=function(l,O){},M.prototype.convertValue=function(l,O){return(0,z.yQ)(l,O)},M}();function b(M,l){var O=new R,d=M.data,f=O.sourceFormat=M.sourceFormat,u=M.startIndex,p="";M.seriesLayoutBy!==C.fY&&(0,W._y)(p);var n=[],i={},t=M.dimensionsDefine;if(t)(0,v.S6)(t,function(D,B){var I=D.name,L={index:B,name:I,displayName:D.displayName};if(n.push(L),I!=null){var U="";(0,v.RI)(i,I)&&(0,W._y)(U),i[I]=L}});else for(var r=0;r<M.dimensionsDetectedCount;r++)n.push({index:r});var e=(0,F._j)(f,C.fY);l.__isBuiltIn&&(O.getRawDataItem=function(D){return e(d,u,n,D)},O.getRawData=(0,v.ak)(A,null,M)),O.cloneRawData=(0,v.ak)(S,null,M);var a=(0,F.a)(f,C.fY);O.count=(0,v.ak)(a,null,d,u,n);var s=(0,F.tB)(f);O.retrieveValue=function(D,B){var I=e(d,u,n,D);return o(I,B)};var o=O.retrieveValueFromItem=function(D,B){if(D!=null){var I=n[B];if(I)return s(D,B,I.name)}};return O.getDimensionInfo=(0,v.ak)(m,null,n,i),O.cloneAllDimensionInfo=(0,v.ak)(g,null,n),O}function A(M){var l=M.sourceFormat;if(!_(l)){var O="";(0,W._y)(O)}return M.data}function S(M){var l=M.sourceFormat,O=M.data;if(!_(l)){var d="";(0,W._y)(d)}if(l===C.XD){for(var f=[],u=0,p=O.length;u<p;u++)f.push(O[u].slice());return f}else if(l===C.qb){for(var f=[],u=0,p=O.length;u<p;u++)f.push((0,v.l7)({},O[u]));return f}}function m(M,l,O){if(O!=null){if((0,v.hj)(O)||!isNaN(O)&&!(0,v.RI)(l,O))return M[O];if((0,v.RI)(l,O))return l[O]}}function g(M){return(0,v.d9)(M)}var y=(0,v.kW)();function E(M){M=(0,v.d9)(M);var l=M.type,O="";l||(0,W._y)(O);var d=l.split(":");d.length!==2&&(0,W._y)(O);var f=!1;d[0]==="echarts"&&(l=d[1],f=!0),M.__isBuiltIn=f,y.set(l,M)}function h(M,l,O){var d=(0,k.kF)(M),f=d.length,u="";f||(0,W._y)(u);for(var p=0,n=f;p<n;p++){var i=d[p];l=c(i,l,O,f===1?null:p),p!==n-1&&(l.length=Math.max(l.length,1))}return l}function c(M,l,O,d){var f="";l.length||(0,W._y)(f),(0,v.Kn)(M)||(0,W._y)(f);var u=M.type,p=y.get(u);p||(0,W._y)(f);var n=(0,v.UI)(l,function(r){return b(r,p)}),i=(0,k.kF)(p.transform({upstream:n[0],upstreamList:n,config:(0,v.d9)(M.config)}));if(!1)var t;return(0,v.UI)(i,function(r,e){var a="";(0,v.Kn)(r)||(0,W._y)(a),r.data||(0,W._y)(a);var s=(0,K.Kp)(r.data);_(s)||(0,W._y)(a);var o,D=l[0];if(D&&e===0&&!r.dimensions){var B=D.startIndex;B&&(r.data=D.data.slice(0,B).concat(r.data)),o={seriesLayoutBy:C.fY,sourceHeader:B,dimensions:D.metaRawOption.dimensions}}else o={seriesLayoutBy:C.fY,sourceHeader:0,dimensions:r.dimensions};return(0,K._P)(r.data,o,null)})}function _(M){return M===C.XD||M===C.qb}},68023:function(Zt,pt,P){"use strict";P.d(pt,{D:function(){return S}});var C=P(81615),k=P(33166),v=P(75797),F=P(98071),z=P(95761),W=P(33051),K=P(49428),R=P(99448),b=[],A={registerPreprocessor:C.ds,registerProcessor:C.Pu,registerPostInit:C.sq,registerPostUpdate:C.Br,registerUpdateLifecycle:C.YK,registerAction:C.zl,registerCoordinateSystem:C.RS,registerLayout:C.qR,registerVisual:C.Og,registerTransform:C.OB,registerLoading:C.yn,registerMap:C.je,registerImpl:K.M,PRIORITY:C.Hr,ComponentModel:F.Z,ComponentView:k.Z,SeriesModel:z.Z,ChartView:v.Z,registerComponentModel:function(m){F.Z.registerClass(m)},registerComponentView:function(m){k.Z.registerClass(m)},registerSeriesModel:function(m){z.Z.registerClass(m)},registerChartView:function(m){v.Z.registerClass(m)},registerSubTypeDefaulter:function(m,g){F.Z.registerSubTypeDefaulter(m,g)},registerPainter:function(m,g){(0,R.wm)(m,g)}};function S(m){if((0,W.kJ)(m)){(0,W.S6)(m,function(g){S(g)});return}(0,W.cq)(b,m)>=0||(b.push(m),(0,W.mf)(m)&&(m={install:m}),m.install(A))}},54162:function(Zt,pt,P){"use strict";P.d(pt,{VT:function(){return v},GI:function(){return W},yl:function(){return K}});var C=P(41587),k=P(60479);function v(R){for(var b=[],A=0;A<R.length;A++){var S=R[A];if(!S.defaultAttr.ignore){var m=S.label,g=m.getComputedTransform(),y=m.getBoundingRect(),E=!g||g[1]<1e-5&&g[2]<1e-5,h=m.style.margin||0,c=y.clone();c.applyTransform(g),c.x-=h/2,c.y-=h/2,c.width+=h,c.height+=h;var _=E?new C.Z(y,g):null;b.push({label:m,labelLine:S.labelLine,rect:c,localRect:y,obb:_,priority:S.priority,defaultAttr:S.defaultAttr,layoutOption:S.computedLayoutOption,axisAligned:E,transform:g})}}return b}function F(R,b,A,S,m,g){var y=R.length;if(y<2)return;R.sort(function(o,D){return o.rect[b]-D.rect[b]});for(var E=0,h,c=!1,_=[],M=0,l=0;l<y;l++){var O=R[l],d=O.rect;h=d[b]-E,h<0&&(d[b]-=h,O.label[b]-=h,c=!0);var f=Math.max(-h,0);_.push(f),M+=f,E=d[b]+d[A]}M>0&&g&&e(-M/y,0,y);var u=R[0],p=R[y-1],n,i;t(),n<0&&a(-n,.8),i<0&&a(i,.8),t(),r(n,i,1),r(i,n,-1),t(),n<0&&s(-n),i<0&&s(i);function t(){n=u.rect[b]-S,i=m-p.rect[b]-p.rect[A]}function r(o,D,B){if(o<0){var I=Math.min(D,-o);if(I>0){e(I*B,0,y);var L=I+o;L<0&&a(-L*B,1)}else a(-o*B,1)}}function e(o,D,B){o!==0&&(c=!0);for(var I=D;I<B;I++){var L=R[I],U=L.rect;U[b]+=o,L.label[b]+=o}}function a(o,D){for(var B=[],I=0,L=1;L<y;L++){var U=R[L-1].rect,x=Math.max(R[L].rect[b]-U[b]-U[A],0);B.push(x),I+=x}if(!!I){var T=Math.min(Math.abs(o)/I,D);if(o>0)for(var L=0;L<y-1;L++){var w=B[L]*T;e(w,0,L+1)}else for(var L=y-1;L>0;L--){var w=B[L-1]*T;e(-w,L,y)}}}function s(o){var D=o<0?-1:1;o=Math.abs(o);for(var B=Math.ceil(o/(y-1)),I=0;I<y-1;I++)if(D>0?e(B,0,I+1):e(-B,y-I-1,y),o-=B,o<=0)return}return c}function z(R,b,A,S){return F(R,"x","width",b,A,S)}function W(R,b,A,S){return F(R,"y","height",b,A,S)}function K(R){var b=[];R.sort(function(f,u){return u.priority-f.priority});var A=new k.Z(0,0,0,0);function S(f){if(!f.ignore){var u=f.ensureState("emphasis");u.ignore==null&&(u.ignore=!1)}f.ignore=!0}for(var m=0;m<R.length;m++){var g=R[m],y=g.axisAligned,E=g.localRect,h=g.transform,c=g.label,_=g.labelLine;A.copy(g.rect),A.width-=.1,A.height-=.1,A.x+=.05,A.y+=.05;for(var M=g.obb,l=!1,O=0;O<b.length;O++){var d=b[O];if(!!A.intersect(d.rect)){if(y&&d.axisAligned){l=!0;break}if(d.obb||(d.obb=new C.Z(d.localRect,d.transform)),M||(M=new C.Z(E,h)),M.intersect(d.obb)){l=!0;break}}}l?(S(c),_&&S(_)):(c.attr("ignore",g.defaultAttr.ignore),_&&_.attr("ignore",g.defaultAttr.labelGuideIgnore),b.push(g))}}},36006:function(Zt,pt,P){"use strict";P.d(pt,{ni:function(){return R},k3:function(){return b},Lr:function(){return A},qT:function(){return _},qA:function(){return M},pe:function(){return l}});var C=P(9074),k=P(33051),v=P(26357),F=P(32234),z={};function W(d,f){for(var u=0;u<v.L1.length;u++){var p=v.L1[u],n=f[p],i=d.ensureState(p);i.style=i.style||{},i.style.text=n}var t=d.currentStates.slice();d.clearStates(!0),d.setStyle({text:f.normal}),d.useStates(t,!0)}function K(d,f,u){var p=d.labelFetcher,n=d.labelDataIndex,i=d.labelDimIndex,t=f.normal,r;p&&(r=p.getFormattedLabel(n,"normal",null,i,t&&t.get("formatter"),u!=null?{interpolatedValue:u}:null)),r==null&&(r=(0,k.mf)(d.defaultText)?d.defaultText(n,d,u):d.defaultText);for(var e={normal:r},a=0;a<v.L1.length;a++){var s=v.L1[a],o=f[s];e[s]=(0,k.pD)(p?p.getFormattedLabel(n,s,null,i,o&&o.get("formatter")):null,r)}return e}function R(d,f,u,p){u=u||z;for(var n=d instanceof C.ZP,i=!1,t=0;t<v.qc.length;t++){var r=f[v.qc[t]];if(r&&r.getShallow("show")){i=!0;break}}var e=n?d:d.getTextContent();if(i){n||(e||(e=new C.ZP,d.setTextContent(e)),d.stateProxy&&(e.stateProxy=d.stateProxy));var a=K(u,f),s=f.normal,o=!!s.getShallow("show"),D=A(s,p&&p.normal,u,!1,!n);D.text=a.normal,n||d.setTextConfig(S(s,u,!1));for(var t=0;t<v.L1.length;t++){var B=v.L1[t],r=f[B];if(r){var I=e.ensureState(B),L=!!(0,k.pD)(r.getShallow("show"),o);if(L!==o&&(I.ignore=!L),I.style=A(r,p&&p[B],u,!0,!n),I.style.text=a[B],!n){var U=d.ensureState(B);U.textConfig=S(r,u,!0)}}}e.silent=!!s.getShallow("silent"),e.style.x!=null&&(D.x=e.style.x),e.style.y!=null&&(D.y=e.style.y),e.ignore=!o,e.useStyle(D),e.dirty(),u.enableTextSetter&&(M(e).setLabelText=function(x){var T=K(u,f,x);W(e,T)})}else e&&(e.ignore=!0);d.dirty()}function b(d,f){f=f||"label";for(var u={normal:d.getModel(f)},p=0;p<v.L1.length;p++){var n=v.L1[p];u[n]=d.getModel([n,f])}return u}function A(d,f,u,p,n){var i={};return m(i,d,u,p,n),f&&(0,k.l7)(i,f),i}function S(d,f,u){f=f||{};var p={},n,i=d.getShallow("rotate"),t=(0,k.pD)(d.getShallow("distance"),u?null:5),r=d.getShallow("offset");return n=d.getShallow("position")||(u?null:"inside"),n==="outside"&&(n=f.defaultOutsidePosition||"top"),n!=null&&(p.position=n),r!=null&&(p.offset=r),i!=null&&(i*=Math.PI/180,p.rotation=i),t!=null&&(p.distance=t),p.outsideFill=d.get("color")==="inherit"?f.inheritColor||null:"auto",p}function m(d,f,u,p,n){u=u||z;var i=f.ecModel,t=i&&i.option.textStyle,r=g(f),e;if(r){e={};for(var a in r)if(r.hasOwnProperty(a)){var s=f.getModel(["rich",a]);c(e[a]={},s,t,u,p,n,!1,!0)}}e&&(d.rich=e);var o=f.get("overflow");o&&(d.overflow=o);var D=f.get("minMargin");D!=null&&(d.margin=D),c(d,f,t,u,p,n,!0,!1)}function g(d){for(var f;d&&d!==d.ecModel;){var u=(d.option||z).rich;if(u){f=f||{};for(var p=(0,k.XP)(u),n=0;n<p.length;n++){var i=p[n];f[i]=1}}d=d.parentModel}return f}var y=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],E=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],h=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function c(d,f,u,p,n,i,t,r){u=!n&&u||z;var e=p&&p.inheritColor,a=f.getShallow("color"),s=f.getShallow("textBorderColor"),o=(0,k.pD)(f.getShallow("opacity"),u.opacity);(a==="inherit"||a==="auto")&&(e?a=e:a=null),(s==="inherit"||s==="auto")&&(e?s=e:s=null),i||(a=a||u.color,s=s||u.textBorderColor),a!=null&&(d.fill=a),s!=null&&(d.stroke=s);var D=(0,k.pD)(f.getShallow("textBorderWidth"),u.textBorderWidth);D!=null&&(d.lineWidth=D);var B=(0,k.pD)(f.getShallow("textBorderType"),u.textBorderType);B!=null&&(d.lineDash=B);var I=(0,k.pD)(f.getShallow("textBorderDashOffset"),u.textBorderDashOffset);I!=null&&(d.lineDashOffset=I),!n&&o==null&&!r&&(o=p&&p.defaultOpacity),o!=null&&(d.opacity=o),!n&&!i&&d.fill==null&&p.inheritColor&&(d.fill=p.inheritColor);for(var L=0;L<y.length;L++){var U=y[L],x=(0,k.pD)(f.getShallow(U),u[U]);x!=null&&(d[U]=x)}for(var L=0;L<E.length;L++){var U=E[L],x=f.getShallow(U);x!=null&&(d[U]=x)}if(d.verticalAlign==null){var T=f.getShallow("baseline");T!=null&&(d.verticalAlign=T)}if(!t||!p.disableBox){for(var L=0;L<h.length;L++){var U=h[L],x=f.getShallow(U);x!=null&&(d[U]=x)}var w=f.getShallow("borderType");w!=null&&(d.borderDash=w),(d.backgroundColor==="auto"||d.backgroundColor==="inherit")&&e&&(d.backgroundColor=e),(d.borderColor==="auto"||d.borderColor==="inherit")&&e&&(d.borderColor=e)}}function _(d,f){var u=f&&f.getModel("textStyle");return(0,k.fy)([d.fontStyle||u&&u.getShallow("fontStyle")||"",d.fontWeight||u&&u.getShallow("fontWeight")||"",(d.fontSize||u&&u.getShallow("fontSize")||12)+"px",d.fontFamily||u&&u.getShallow("fontFamily")||"sans-serif"].join(" "))}var M=(0,F.Yf)();function l(d,f,u,p){if(!!d){var n=M(d);n.prevValue=n.value,n.value=u;var i=f.normal;n.valueAnimation=i.get("valueAnimation"),n.valueAnimation&&(n.precision=i.get("precision"),n.defaultInterpolatedText=p,n.statesModels=f)}}function O(d,f,u,p,n){var i=M(d);if(!i.valueAnimation||i.prevValue===i.value)return;var t=i.defaultInterpolatedText,r=retrieve2(i.interpolatedValue,i.prevValue),e=i.value;function a(s){var o=interpolateRawValues(u,i.precision,r,e,s);i.interpolatedValue=s===1?null:o;var D=K({labelDataIndex:f,labelFetcher:n,defaultText:t?t(o):o+""},i.statesModels,o);W(d,D)}d.percent=0,(i.prevValue==null?initProps:updateProps)(d,{percent:1},p,f,null,a)}},79093:function(Zt,pt,P){"use strict";P.d(pt,{Ge:function(){return A},My:function(){return m},G_:function(){return y},bK:function(){return E},Bk:function(){return h}});var C=P(33051),k=P(85669),v=P(99936),F=P(95682),z=P(80887),W="__ec_stack_";function K(l){return l.get("stack")||W+l.seriesIndex}function R(l){return l.dim+l.index}function b(l){var O=[],d=l.axis,f="axis0";if(d.type==="category"){for(var u=d.getBandWidth(),p=0;p<l.count;p++)O.push(defaults({bandWidth:u,axisKey:f,stackId:W+p},l));for(var n=g(O),i=[],p=0;p<l.count;p++){var t=n[f][W+p];t.offsetCenter=t.offset+t.width/2,i.push(t)}return i}}function A(l,O){var d=[];return O.eachSeriesByType(l,function(f){c(f)&&d.push(f)}),d}function S(l){var O={};(0,C.S6)(l,function(t){var r=t.coordinateSystem,e=r.getBaseAxis();if(!(e.type!=="time"&&e.type!=="value"))for(var a=t.getData(),s=e.dim+"_"+e.index,o=a.getDimensionIndex(a.mapDimension(e.dim)),D=a.getStore(),B=0,I=D.count();B<I;++B){var L=D.get(o,B);O[s]?O[s].push(L):O[s]=[L]}});var d={};for(var f in O)if(O.hasOwnProperty(f)){var u=O[f];if(u){u.sort(function(t,r){return t-r});for(var p=null,n=1;n<u.length;++n){var i=u[n]-u[n-1];i>0&&(p=p===null?i:Math.min(p,i))}d[f]=p}}return d}function m(l){var O=S(l),d=[];return(0,C.S6)(l,function(f){var u=f.coordinateSystem,p=u.getBaseAxis(),n=p.getExtent(),i;if(p.type==="category")i=p.getBandWidth();else if(p.type==="value"||p.type==="time"){var t=p.dim+"_"+p.index,r=O[t],e=Math.abs(n[1]-n[0]),a=p.scale.getExtent(),s=Math.abs(a[1]-a[0]);i=r?e/s*r:e}else{var o=f.getData();i=Math.abs(n[1]-n[0])/o.count()}var D=(0,k.GM)(f.get("barWidth"),i),B=(0,k.GM)(f.get("barMaxWidth"),i),I=(0,k.GM)(f.get("barMinWidth")||(_(f)?.5:1),i),L=f.get("barGap"),U=f.get("barCategoryGap");d.push({bandWidth:i,barWidth:D,barMaxWidth:B,barMinWidth:I,barGap:L,barCategoryGap:U,axisKey:R(p),stackId:K(f)})}),g(d)}function g(l){var O={};(0,C.S6)(l,function(f,u){var p=f.axisKey,n=f.bandWidth,i=O[p]||{bandWidth:n,remainedWidth:n,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},t=i.stacks;O[p]=i;var r=f.stackId;t[r]||i.autoWidthCount++,t[r]=t[r]||{width:0,maxWidth:0};var e=f.barWidth;e&&!t[r].width&&(t[r].width=e,e=Math.min(i.remainedWidth,e),i.remainedWidth-=e);var a=f.barMaxWidth;a&&(t[r].maxWidth=a);var s=f.barMinWidth;s&&(t[r].minWidth=s);var o=f.barGap;o!=null&&(i.gap=o);var D=f.barCategoryGap;D!=null&&(i.categoryGap=D)});var d={};return(0,C.S6)(O,function(f,u){d[u]={};var p=f.stacks,n=f.bandWidth,i=f.categoryGap;if(i==null){var t=(0,C.XP)(p).length;i=Math.max(35-t*4,15)+"%"}var r=(0,k.GM)(i,n),e=(0,k.GM)(f.gap,1),a=f.remainedWidth,s=f.autoWidthCount,o=(a-r)/(s+(s-1)*e);o=Math.max(o,0),(0,C.S6)(p,function(L){var U=L.maxWidth,x=L.minWidth;if(L.width){var T=L.width;U&&(T=Math.min(T,U)),x&&(T=Math.max(T,x)),L.width=T,a-=T+e*T,s--}else{var T=o;U&&U<T&&(T=Math.min(U,a)),x&&x>T&&(T=x),T!==o&&(L.width=T,a-=T+e*T,s--)}}),o=(a-r)/(s+(s-1)*e),o=Math.max(o,0);var D=0,B;(0,C.S6)(p,function(L,U){L.width||(L.width=o),B=L,D+=L.width*(1+e)}),B&&(D-=B.width*e);var I=-D/2;(0,C.S6)(p,function(L,U){d[u][U]=d[u][U]||{bandWidth:n,offset:I,width:L.width},I+=L.width*(1+e)})}),d}function y(l,O,d){if(l&&O){var f=l[R(O)];return f!=null&&d!=null?f[K(d)]:f}}function E(l,O){var d=A(l,O),f=m(d);(0,C.S6)(d,function(u){var p=u.getData(),n=u.coordinateSystem,i=n.getBaseAxis(),t=K(u),r=f[R(i)][t],e=r.offset,a=r.width;p.setLayout({bandWidth:r.bandWidth,offset:e,size:a})})}function h(l){return{seriesType:l,plan:(0,F.Z)(),reset:function(O){if(!!c(O)){var d=O.getData(),f=O.coordinateSystem,u=f.getBaseAxis(),p=f.getOtherAxis(u),n=d.getDimensionIndex(d.mapDimension(p.dim)),i=d.getDimensionIndex(d.mapDimension(u.dim)),t=O.get("showBackground",!0),r=d.mapDimension(p.dim),e=d.getCalculationInfo("stackResultDimension"),a=(0,v.M)(d,r)&&!!d.getCalculationInfo("stackedOnSeries"),s=p.isHorizontal(),o=M(u,p),D=_(O),B=O.get("barMinHeight")||0,I=e&&d.getDimensionIndex(e),L=d.getLayout("size"),U=d.getLayout("offset");return{progress:function(x,T){for(var w=x.count,V=D&&(0,z.o)(w*3),N=D&&t&&(0,z.o)(w*3),G=D&&(0,z.o)(w),Z=f.master.getRect(),H=s?Z.width:Z.height,X,ut=T.getStore(),lt=0;(X=x.next())!=null;){var ot=ut.get(a?I:n,X),q=ut.get(i,X),tt=o,j=void 0;a&&(j=+ot-ut.get(n,X));var et=void 0,ct=void 0,_t=void 0,St=void 0;if(s){var At=f.dataToPoint([ot,q]);if(a){var Ct=f.dataToPoint([j,q]);tt=Ct[0]}et=tt,ct=At[1]+U,_t=At[0]-tt,St=L,Math.abs(_t)<B&&(_t=(_t<0?-1:1)*B)}else{var At=f.dataToPoint([q,ot]);if(a){var Ct=f.dataToPoint([q,j]);tt=Ct[1]}et=At[0]+U,ct=tt,_t=L,St=At[1]-tt,Math.abs(St)<B&&(St=(St<=0?-1:1)*B)}D?(V[lt]=et,V[lt+1]=ct,V[lt+2]=s?_t:St,N&&(N[lt]=s?Z.x:et,N[lt+1]=s?ct:Z.y,N[lt+2]=H),G[X]=X):T.setItemLayout(X,{x:et,y:ct,width:_t,height:St}),lt+=3}D&&T.setLayout({largePoints:V,largeDataIndices:G,largeBackgroundPoints:N,valueAxisHorizontal:s})}}}}}}function c(l){return l.coordinateSystem&&l.coordinateSystem.type==="cartesian2d"}function _(l){return l.pipelineContext&&l.pipelineContext.large}function M(l,O){return O.toGlobalCoord(O.dataToCoord(O.type==="log"?1:0))}},31891:function(Zt,pt,P){"use strict";P.d(pt,{y:function(){return v},s:function(){return z}});var C=P(33051),k=P(32234);function v(W,K){function R(b,A){var S=[];return b.eachComponent({mainType:"series",subType:W,query:A},function(m){S.push(m.seriesIndex)}),S}(0,C.S6)([[W+"ToggleSelect","toggleSelect"],[W+"Select","select"],[W+"UnSelect","unselect"]],function(b){K(b[0],function(A,S,m){A=(0,C.l7)({},A),m.dispatchAction((0,C.l7)(A,{type:b[1],seriesIndex:R(S,A)}))})})}function F(W,K,R,b,A){var S=W+K;R.isSilent(S)||b.eachComponent({mainType:"series",subType:"pie"},function(m){for(var g=m.seriesIndex,y=m.option.selectedMap,E=A.selected,h=0;h<E.length;h++)if(E[h].seriesIndex===g){var c=m.getData(),_=(0,k.gO)(c,A.fromActionPayload);R.trigger(S,{type:S,seriesId:m.id,name:(0,C.kJ)(_)?c.getName(_[0]):c.getName(_),selected:(0,C.HD)(y)?y:(0,C.l7)({},y)})}})}function z(W,K,R){W.on("selectchanged",function(b){var A=R.getModel();b.isFromClick?(F("map","selectchanged",K,A,b),F("pie","selectchanged",K,A,b)):b.fromAction==="select"?(F("map","selected",K,A,b),F("pie","selected",K,A,b)):b.fromAction==="unselect"&&(F("map","unselected",K,A,b),F("pie","unselected",K,A,b))})}},98071:function(Zt,pt,P){"use strict";var C=P(18299),k=P(33051),v=P(1497),F=P(42151),z=P(34251),W=P(32234),K=P(76172),R=(0,W.Yf)(),b=function(S){(0,C.ZT)(m,S);function m(g,y,E){var h=S.call(this,g,y,E)||this;return h.uid=F.Kr("ec_cpt_model"),h}return m.prototype.init=function(g,y,E){this.mergeDefaultAndTheme(g,E)},m.prototype.mergeDefaultAndTheme=function(g,y){var E=K.YD(this),h=E?K.tE(g):{},c=y.getTheme();k.TS(g,c.get(this.mainType)),k.TS(g,this.getDefaultOption()),E&&K.dt(g,h,E)},m.prototype.mergeOption=function(g,y){k.TS(this.option,g,!0);var E=K.YD(this);E&&K.dt(this.option,g,E)},m.prototype.optionUpdated=function(g,y){},m.prototype.getDefaultOption=function(){var g=this.constructor;if(!(0,z.PT)(g))return g.defaultOption;var y=R(this);if(!y.defaultOption){for(var E=[],h=g;h;){var c=h.prototype.defaultOption;c&&E.push(c),h=h.superClass}for(var _={},M=E.length-1;M>=0;M--)_=k.TS(_,E[M],!0);y.defaultOption=_}return y.defaultOption},m.prototype.getReferringComponents=function(g,y){var E=g+"Index",h=g+"Id";return(0,W.HZ)(this.ecModel,g,{index:this.get(E,!0),id:this.get(h,!0)},y)},m.prototype.getBoxLayoutParams=function(){var g=this;return{left:g.get("left"),top:g.get("top"),right:g.get("right"),bottom:g.get("bottom"),width:g.get("width"),height:g.get("height")}},m.prototype.getZLevelKey=function(){return""},m.prototype.setZLevel=function(g){this.option.zlevel=g},m.protoInitialize=function(){var g=m.prototype;g.type="component",g.id="",g.name="",g.mainType="",g.subType="",g.componentIndex=0}(),m}(v.Z);(0,z.pw)(b,v.Z),(0,z.au)(b),F.cj(b),F.jS(b,A);function A(S){var m=[];return k.S6(b.getClassesByMainType(S),function(g){m=m.concat(g.dependencies||g.prototype.dependencies||[])}),m=k.UI(m,function(g){return(0,z.u9)(g).main}),S!=="dataset"&&k.cq(m,"dataset")<=0&&m.unshift("dataset"),m}pt.Z=b},1497:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return _}});var C=P(66387),k=P(34251),v=P(59066),F=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],z=(0,v.Z)(F),W=function(){function M(){}return M.prototype.getAreaStyle=function(l,O){return z(this,l,O)},M}(),K=P(36006),R=P(9074),b=["textStyle","color"],A=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],S=new R.ZP,m=function(){function M(){}return M.prototype.getTextColor=function(l){var O=this.ecModel;return this.getShallow("color")||(!l&&O?O.get(b):null)},M.prototype.getFont=function(){return(0,K.qT)({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},M.prototype.getTextRect=function(l){for(var O={text:l,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},d=0;d<A.length;d++)O[A[d]]=this.getShallow(A[d]);return S.useStyle(O),S.update(),S.getBoundingRect()},M}(),g=m,y=P(77515),E=P(89887),h=P(33051),c=function(){function M(l,O,d){this.parentModel=O,this.ecModel=d,this.option=l}return M.prototype.init=function(l,O,d){for(var f=[],u=3;u<arguments.length;u++)f[u-3]=arguments[u]},M.prototype.mergeOption=function(l,O){(0,h.TS)(this.option,l,!0)},M.prototype.get=function(l,O){return l==null?this.option:this._doGet(this.parsePath(l),!O&&this.parentModel)},M.prototype.getShallow=function(l,O){var d=this.option,f=d==null?d:d[l];if(f==null&&!O){var u=this.parentModel;u&&(f=u.getShallow(l))}return f},M.prototype.getModel=function(l,O){var d=l!=null,f=d?this.parsePath(l):null,u=d?this._doGet(f):this.option;return O=O||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(f)),new M(u,O,this.ecModel)},M.prototype.isEmpty=function(){return this.option==null},M.prototype.restoreData=function(){},M.prototype.clone=function(){var l=this.constructor;return new l((0,h.d9)(this.option))},M.prototype.parsePath=function(l){return typeof l=="string"?l.split("."):l},M.prototype.resolveParentPath=function(l){return l},M.prototype.isAnimationEnabled=function(){if(!C.Z.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},M.prototype._doGet=function(l,O){var d=this.option;if(!l)return d;for(var f=0;f<l.length&&!(!!l[f]&&(d=d&&typeof d=="object"?d[l[f]]:null,d==null));f++);return d==null&&O&&(d=O._doGet(this.resolveParentPath(l),O.parentModel)),d},M}();(0,k.dm)(c),(0,k.Qj)(c),(0,h.jB)(c,y.K),(0,h.jB)(c,E.D),(0,h.jB)(c,W),(0,h.jB)(c,g);var _=c},95761:function(Zt,pt,P){"use strict";P.d(pt,{V:function(){return n},Z:function(){return I}});var C=P(18299),k=P(33051),v=P(66387),F=P(32234),z=P(98071),W=P(75494),K=P(61219),R=P(76172),b=P(8674),A=P(34251),S=P(99574),m=P(94279),g=P(61772),y=P(10437),E=P(43834),h=P(68540),c=function(){function L(U){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=U}return L.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},L.prototype._setLocalSource=function(U,x){this._sourceList=U,this._upstreamSignList=x,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},L.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},L.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},L.prototype._createSource=function(){this._setLocalSource([],[]);var U=this._sourceHost,x=this._getUpstreamSourceManagers(),T=!!x.length,w,V;if(M(U)){var N=U,G=void 0,Z=void 0,H=void 0;if(T){var X=x[0];X.prepareSource(),H=X.getSource(),G=H.data,Z=H.sourceFormat,V=[X._getVersionSign()]}else G=N.get("data",!0),Z=(0,k.fU)(G)?m.J5:m.cy,V=[];var ut=this._getSourceMetaRawOption()||{},lt=H&&H.metaRawOption||{},ot=(0,k.pD)(ut.seriesLayoutBy,lt.seriesLayoutBy)||null,q=(0,k.pD)(ut.sourceHeader,lt.sourceHeader),tt=(0,k.pD)(ut.dimensions,lt.dimensions),j=ot!==lt.seriesLayoutBy||!!q!=!!lt.sourceHeader||tt;w=j?[(0,S._P)(G,{seriesLayoutBy:ot,sourceHeader:q,dimensions:tt},Z)]:[]}else{var et=U;if(T){var ct=this._applyTransform(x);w=ct.sourceList,V=ct.upstreamSignList}else{var _t=et.get("source",!0);w=[(0,S._P)(_t,this._getSourceMetaRawOption(),null)],V=[]}}this._setLocalSource(w,V)},L.prototype._applyTransform=function(U){var x=this._sourceHost,T=x.get("transform",!0),w=x.get("fromTransformResult",!0);if(w!=null){var V="";U.length!==1&&l(V)}var N,G=[],Z=[];return(0,k.S6)(U,function(H){H.prepareSource();var X=H.getSource(w||0),ut="";w!=null&&!X&&l(ut),G.push(X),Z.push(H._getVersionSign())}),T?N=(0,y.vK)(T,G,{datasetIndex:x.componentIndex}):w!=null&&(N=[(0,S.ML)(G[0])]),{sourceList:N,upstreamSignList:Z}},L.prototype._isDirty=function(){if(this._dirty)return!0;for(var U=this._getUpstreamSourceManagers(),x=0;x<U.length;x++){var T=U[x];if(T._isDirty()||this._upstreamSignList[x]!==T._getVersionSign())return!0}},L.prototype.getSource=function(U){U=U||0;var x=this._sourceList[U];if(!x){var T=this._getUpstreamSourceManagers();return T[0]&&T[0].getSource(U)}return x},L.prototype.getSharedDataStore=function(U){var x=U.makeStoreSchema();return this._innerGetDataStore(x.dimensions,U.source,x.hash)},L.prototype._innerGetDataStore=function(U,x,T){var w=0,V=this._storeList,N=V[w];N||(N=V[w]={});var G=N[T];if(!G){var Z=this._getUpstreamSourceManagers()[0];M(this._sourceHost)&&Z?G=Z._innerGetDataStore(U,x,T):(G=new E.ZP,G.initData(new h.Pl(x,U.length),U)),N[T]=G}return G},L.prototype._getUpstreamSourceManagers=function(){var U=this._sourceHost;if(M(U)){var x=(0,g.Wd)(U);return x?[x.getSourceManager()]:[]}else return(0,k.UI)((0,g.JT)(U),function(T){return T.getSourceManager()})},L.prototype._getSourceMetaRawOption=function(){var U=this._sourceHost,x,T,w;if(M(U))x=U.get("seriesLayoutBy",!0),T=U.get("sourceHeader",!0),w=U.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var V=U;x=V.get("seriesLayoutBy",!0),T=V.get("sourceHeader",!0),w=V.get("dimensions",!0)}return{seriesLayoutBy:x,sourceHeader:T,dimensions:w}},L}();function _(L){var U=L.option.transform;U&&setAsPrimitive(L.option.transform)}function M(L){return L.mainType==="series"}function l(L){throw new Error(L)}var O=P(5685);function d(L){var U=L.series,x=L.dataIndex,T=L.multipleSeries,w=U.getData(),V=w.mapDimensionsAll("defaultedTooltip"),N=V.length,G=U.getRawValue(x),Z=(0,k.kJ)(G),H=(0,O.jT)(U,x),X,ut,lt,ot;if(N>1||Z&&!N){var q=f(G,U,x,V,H);X=q.inlineValues,ut=q.inlineValueTypes,lt=q.blocks,ot=q.inlineValues[0]}else if(N){var tt=w.getDimensionInfo(V[0]);ot=X=(0,h.hk)(w,x,V[0]),ut=tt.type}else ot=X=Z?G[0]:G;var j=(0,F.yu)(U),et=j&&U.name||"",ct=w.getName(x),_t=T?et:ct;return(0,O.TX)("section",{header:et,noHeader:T||!j,sortParam:ot,blocks:[(0,O.TX)("nameValue",{markerType:"item",markerColor:H,name:_t,noName:!(0,k.fy)(_t),value:X,valueType:ut,dataIndex:x})].concat(lt||[])})}function f(L,U,x,T,w){var V=U.getData(),N=(0,k.u4)(L,function(ut,lt,ot){var q=V.getDimensionInfo(ot);return ut=ut||q&&q.tooltip!==!1&&q.displayName!=null},!1),G=[],Z=[],H=[];T.length?(0,k.S6)(T,function(ut){X((0,h.hk)(V,x,ut),ut)}):(0,k.S6)(L,X);function X(ut,lt){var ot=V.getDimensionInfo(lt);!ot||ot.otherDims.tooltip===!1||(N?H.push((0,O.TX)("nameValue",{markerType:"subItem",markerColor:w,name:ot.displayName,value:ut,valueType:ot.type})):(G.push(ut),Z.push(ot.type)))}return{inlineValues:G,inlineValueTypes:Z,blocks:H}}var u=F.Yf();function p(L,U){return L.getName(U)||L.getId(U)}var n="__universalTransitionEnabled",i=function(L){(0,C.ZT)(U,L);function U(){var x=L!==null&&L.apply(this,arguments)||this;return x._selectedDataIndicesMap={},x}return U.prototype.init=function(x,T,w){this.seriesIndex=this.componentIndex,this.dataTask=(0,b.v)({count:e,reset:a}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(x,w);var V=u(this).sourceManager=new c(this);V.prepareSource();var N=this.getInitialData(x,w);o(N,this),this.dataTask.context.data=N,u(this).dataBeforeProcessed=N,t(this),this._initSelectedMapFromData(N)},U.prototype.mergeDefaultAndTheme=function(x,T){var w=(0,R.YD)(this),V=w?(0,R.tE)(x):{},N=this.subType;z.Z.hasClass(N)&&(N+="Series"),k.TS(x,T.getTheme().get(this.subType)),k.TS(x,this.getDefaultOption()),F.Cc(x,"label",["show"]),this.fillDataTextStyle(x.data),w&&(0,R.dt)(x,V,w)},U.prototype.mergeOption=function(x,T){x=k.TS(this.option,x,!0),this.fillDataTextStyle(x.data);var w=(0,R.YD)(this);w&&(0,R.dt)(this.option,x,w);var V=u(this).sourceManager;V.dirty(),V.prepareSource();var N=this.getInitialData(x,T);o(N,this),this.dataTask.dirty(),this.dataTask.context.data=N,u(this).dataBeforeProcessed=N,t(this),this._initSelectedMapFromData(N)},U.prototype.fillDataTextStyle=function(x){if(x&&!k.fU(x))for(var T=["show"],w=0;w<x.length;w++)x[w]&&x[w].label&&F.Cc(x[w],"label",T)},U.prototype.getInitialData=function(x,T){},U.prototype.appendData=function(x){var T=this.getRawData();T.appendData(x.data)},U.prototype.getData=function(x){var T=B(this);if(T){var w=T.context.data;return x==null?w:w.getLinkedData(x)}else return u(this).data},U.prototype.getAllData=function(){var x=this.getData();return x&&x.getLinkedDataAll?x.getLinkedDataAll():[{data:x}]},U.prototype.setData=function(x){var T=B(this);if(T){var w=T.context;w.outputData=x,T!==this.dataTask&&(w.data=x)}u(this).data=x},U.prototype.getEncode=function(){var x=this.get("encode",!0);if(x)return k.kW(x)},U.prototype.getSourceManager=function(){return u(this).sourceManager},U.prototype.getSource=function(){return this.getSourceManager().getSource()},U.prototype.getRawData=function(){return u(this).dataBeforeProcessed},U.prototype.getColorBy=function(){var x=this.get("colorBy");return x||"series"},U.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},U.prototype.getBaseAxis=function(){var x=this.coordinateSystem;return x&&x.getBaseAxis&&x.getBaseAxis()},U.prototype.formatTooltip=function(x,T,w){return d({series:this,dataIndex:x,multipleSeries:T})},U.prototype.isAnimationEnabled=function(){var x=this.ecModel;if(v.Z.node&&!(x&&x.ssr))return!1;var T=this.getShallow("animation");return T&&this.getData().count()>this.getShallow("animationThreshold")&&(T=!1),!!T},U.prototype.restoreData=function(){this.dataTask.dirty()},U.prototype.getColorFromPalette=function(x,T,w){var V=this.ecModel,N=W._.prototype.getColorFromPalette.call(this,x,T,w);return N||(N=V.getColorFromPalette(x,T,w)),N},U.prototype.coordDimToDataDim=function(x){return this.getRawData().mapDimensionsAll(x)},U.prototype.getProgressive=function(){return this.get("progressive")},U.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},U.prototype.select=function(x,T){this._innerSelect(this.getData(T),x)},U.prototype.unselect=function(x,T){var w=this.option.selectedMap;if(!!w){var V=this.option.selectedMode,N=this.getData(T);if(V==="series"||w==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var G=0;G<x.length;G++){var Z=x[G],H=p(N,Z);w[H]=!1,this._selectedDataIndicesMap[H]=-1}}},U.prototype.toggleSelect=function(x,T){for(var w=[],V=0;V<x.length;V++)w[0]=x[V],this.isSelected(x[V],T)?this.unselect(w,T):this.select(w,T)},U.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var x=this._selectedDataIndicesMap,T=k.XP(x),w=[],V=0;V<T.length;V++){var N=x[T[V]];N>=0&&w.push(N)}return w},U.prototype.isSelected=function(x,T){var w=this.option.selectedMap;if(!w)return!1;var V=this.getData(T);return(w==="all"||w[p(V,x)])&&!V.getItemModel(x).get(["select","disabled"])},U.prototype.isUniversalTransitionEnabled=function(){if(this[n])return!0;var x=this.option.universalTransition;return x?x===!0?!0:x&&x.enabled:!1},U.prototype._innerSelect=function(x,T){var w,V,N=this.option,G=N.selectedMode,Z=T.length;if(!(!G||!Z)){if(G==="series")N.selectedMap="all";else if(G==="multiple"){k.Kn(N.selectedMap)||(N.selectedMap={});for(var H=N.selectedMap,X=0;X<Z;X++){var ut=T[X],lt=p(x,ut);H[lt]=!0,this._selectedDataIndicesMap[lt]=x.getRawIndex(ut)}}else if(G==="single"||G===!0){var ot=T[Z-1],lt=p(x,ot);N.selectedMap=(w={},w[lt]=!0,w),this._selectedDataIndicesMap=(V={},V[lt]=x.getRawIndex(ot),V)}}},U.prototype._initSelectedMapFromData=function(x){if(!this.option.selectedMap){var T=[];x.hasItemOption&&x.each(function(w){var V=x.getRawDataItem(w);V&&V.selected&&T.push(w)}),T.length>0&&this._innerSelect(x,T)}},U.registerClass=function(x){return z.Z.registerClass(x)},U.protoInitialize=function(){var x=U.prototype;x.type="series.__base__",x.seriesIndex=0,x.ignoreStyleOnData=!1,x.hasSymbolVisual=!1,x.defaultSymbol="circle",x.visualStyleAccessPath="itemStyle",x.visualDrawType="fill"}(),U}(z.Z);k.jB(i,K.X),k.jB(i,W._),(0,A.pw)(i,z.Z);function t(L){var U=L.name;F.yu(L)||(L.name=r(L)||U)}function r(L){var U=L.getRawData(),x=U.mapDimensionsAll("seriesName"),T=[];return k.S6(x,function(w){var V=U.getDimensionInfo(w);V.displayName&&T.push(V.displayName)}),T.join(" ")}function e(L){return L.model.getRawData().count()}function a(L){var U=L.model;return U.setData(U.getRawData().cloneShallow()),s}function s(L,U){U.outputData&&L.end>U.outputData.count()&&U.model.getRawData().cloneShallow(U.outputData)}function o(L,U){k.S6(k.WW(L.CHANGABLE_METHODS,L.DOWNSAMPLE_METHODS),function(x){L.wrapMethod(x,k.WA(D,U))})}function D(L,U){var x=B(L);return x&&x.setOutputEnd((U||this).count()),U}function B(L){var U=(L.ecModel||{}).scheduler,x=U&&U.getPipeline(L.uid);if(x){var T=x.currentTask;if(T){var w=T.agentStubMap;w&&(T=w.get(L.uid))}return T}}var I=i},82468:function(Zt,pt,P){"use strict";P.d(pt,{f:function(){return v},R:function(){return F}});var C=P(33051),k=(0,C.kW)();function v(z,W){(0,C.hu)(k.get(z)==null&&W),k.set(z,W)}function F(z,W,K){var R=k.get(W);if(!R)return K;var b=R(z);if(!b)return K;if(!1)var A;return K.concat(b)}},61219:function(Zt,pt,P){"use strict";P.d(pt,{X:function(){return z},f:function(){return W}});var C=P(33051),k=P(68540),v=P(78988),F=/\{@(.+?)\}/g,z=function(){function K(){}return K.prototype.getDataParams=function(R,b){var A=this.getData(b),S=this.getRawValue(R,b),m=A.getRawIndex(R),g=A.getName(R),y=A.getRawDataItem(R),E=A.getItemVisual(R,"style"),h=E&&E[A.getItemVisual(R,"drawType")||"fill"],c=E&&E.stroke,_=this.mainType,M=_==="series",l=A.userOutput&&A.userOutput.get();return{componentType:_,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:M?this.subType:null,seriesIndex:this.seriesIndex,seriesId:M?this.id:null,seriesName:M?this.name:null,name:g,dataIndex:m,data:y,dataType:b,value:S,color:h,borderColor:c,dimensionNames:l?l.fullDimensions:null,encode:l?l.encode:null,$vars:["seriesName","name","value"]}},K.prototype.getFormattedLabel=function(R,b,A,S,m,g){b=b||"normal";var y=this.getData(A),E=this.getDataParams(R,A);if(g&&(E.value=g.interpolatedValue),S!=null&&C.kJ(E.value)&&(E.value=E.value[S]),!m){var h=y.getItemModel(R);m=h.get(b==="normal"?["label","formatter"]:[b,"label","formatter"])}if(C.mf(m))return E.status=b,E.dimensionIndex=S,m(E);if(C.HD(m)){var c=(0,v.kF)(m,E);return c.replace(F,function(_,M){var l=M.length,O=M;O.charAt(0)==="["&&O.charAt(l-1)==="]"&&(O=+O.slice(1,l-1));var d=(0,k.hk)(y,R,O);if(g&&C.kJ(g.interpolatedValue)){var f=y.getDimensionIndex(O);f>=0&&(d=g.interpolatedValue[f])}return d!=null?d+"":""})}},K.prototype.getRawValue=function(R,b){return(0,k.hk)(this.getData(b),R)},K.prototype.formatTooltip=function(R,b,A){},K}();function W(K){var R,b;return C.Kn(K)?K.type&&(b=K):R=K,{text:R,frag:b}}},89887:function(Zt,pt,P){"use strict";P.d(pt,{t:function(){return k},D:function(){return F}});var C=P(59066),k=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],v=(0,C.Z)(k),F=function(){function z(){}return z.prototype.getItemStyle=function(W,K){return v(this,W,K)},z}()},77515:function(Zt,pt,P){"use strict";P.d(pt,{v:function(){return k},K:function(){return F}});var C=P(59066),k=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],v=(0,C.Z)(k),F=function(){function z(){}return z.prototype.getLineStyle=function(W){return v(this,W)},z}()},59066:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return k}});var C=P(33051);function k(v,F){for(var z=0;z<v.length;z++)v[z][1]||(v[z][1]=v[z][0]);return F=F||!1,function(W,K,R){for(var b={},A=0;A<v.length;A++){var S=v[A][1];if(!(K&&C.cq(K,S)>=0||R&&C.cq(R,S)<0)){var m=W.getShallow(S,F);m!=null&&(b[v[A][0]]=m)}}return b}}},75494:function(Zt,pt,P){"use strict";P.d(pt,{_:function(){return F}});var C=P(32234),k=(0,C.Yf)(),v=(0,C.Yf)(),F=function(){function b(){}return b.prototype.getColorFromPalette=function(A,S,m){var g=(0,C.kF)(this.get("color",!0)),y=this.get("colorLayer",!0);return K(this,k,g,y,A,S,m)},b.prototype.clearColorPalette=function(){R(this,k)},b}();function z(b,A,S,m){var g=normalizeToArray(b.get(["aria","decal","decals"]));return K(b,v,g,null,A,S,m)}function W(b,A){for(var S=b.length,m=0;m<S;m++)if(b[m].length>A)return b[m];return b[S-1]}function K(b,A,S,m,g,y,E){y=y||b;var h=A(y),c=h.paletteIdx||0,_=h.paletteNameMap=h.paletteNameMap||{};if(_.hasOwnProperty(g))return _[g];var M=E==null||!m?S:W(m,E);if(M=M||S,!(!M||!M.length)){var l=M[c];return g&&(_[g]=l),h.paletteIdx=(c+1)%M.length,l}}function R(b,A){A(b).paletteIdx=0,A(b).paletteNameMap={}}},22528:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return C}});function C(k){return{seriesType:k,reset:function(v,F){var z=F.findComponents({mainType:"legend"});if(!(!z||!z.length)){var W=v.getData();W.filterSelf(function(K){for(var R=W.getName(K),b=0;b<z.length;b++)if(!z[b].isSelected(R))return!1;return!0})}}}}},70103:function(Zt,pt,P){"use strict";var C=P(18299),k=P(85669),v=P(78988),F=P(20380),z=P(65021),W=k.NM,K=function(R){(0,C.ZT)(b,R);function b(){var A=R!==null&&R.apply(this,arguments)||this;return A.type="interval",A._interval=0,A._intervalPrecision=2,A}return b.prototype.parse=function(A){return A},b.prototype.contain=function(A){return z.XS(A,this._extent)},b.prototype.normalize=function(A){return z.Fv(A,this._extent)},b.prototype.scale=function(A){return z.bA(A,this._extent)},b.prototype.setExtent=function(A,S){var m=this._extent;isNaN(A)||(m[0]=parseFloat(A)),isNaN(S)||(m[1]=parseFloat(S))},b.prototype.unionExtent=function(A){var S=this._extent;A[0]<S[0]&&(S[0]=A[0]),A[1]>S[1]&&(S[1]=A[1]),this.setExtent(S[0],S[1])},b.prototype.getInterval=function(){return this._interval},b.prototype.setInterval=function(A){this._interval=A,this._niceExtent=this._extent.slice(),this._intervalPrecision=z.lb(A)},b.prototype.getTicks=function(A){var S=this._interval,m=this._extent,g=this._niceExtent,y=this._intervalPrecision,E=[];if(!S)return E;var h=1e4;m[0]<g[0]&&(A?E.push({value:W(g[0]-S,y)}):E.push({value:m[0]}));for(var c=g[0];c<=g[1]&&(E.push({value:c}),c=W(c+S,y),c!==E[E.length-1].value);)if(E.length>h)return[];var _=E.length?E[E.length-1].value:g[1];return m[1]>_&&(A?E.push({value:W(_+S,y)}):E.push({value:m[1]})),E},b.prototype.getMinorTicks=function(A){for(var S=this.getTicks(!0),m=[],g=this.getExtent(),y=1;y<S.length;y++){for(var E=S[y],h=S[y-1],c=0,_=[],M=E.value-h.value,l=M/A;c<A-1;){var O=W(h.value+(c+1)*l);O>g[0]&&O<g[1]&&_.push(O),c++}m.push(_)}return m},b.prototype.getLabel=function(A,S){if(A==null)return"";var m=S&&S.precision;m==null?m=k.p8(A.value)||0:m==="auto"&&(m=this._intervalPrecision);var g=W(A.value,m,!0);return v.OD(g)},b.prototype.calcNiceTicks=function(A,S,m){A=A||5;var g=this._extent,y=g[1]-g[0];if(!!isFinite(y)){y<0&&(y=-y,g.reverse());var E=z.Qf(g,A,S,m);this._intervalPrecision=E.intervalPrecision,this._interval=E.interval,this._niceExtent=E.niceTickExtent}},b.prototype.calcNiceExtent=function(A){var S=this._extent;if(S[0]===S[1])if(S[0]!==0){var m=Math.abs(S[0]);A.fixMax||(S[1]+=m/2),S[0]-=m/2}else S[1]=1;var g=S[1]-S[0];isFinite(g)||(S[0]=0,S[1]=1),this.calcNiceTicks(A.splitNumber,A.minInterval,A.maxInterval);var y=this._interval;A.fixMin||(S[0]=W(Math.floor(S[0]/y)*y)),A.fixMax||(S[1]=W(Math.ceil(S[1]/y)*y))},b.prototype.setNiceExtent=function(A,S){this._niceExtent=[A,S]},b.type="interval",b}(F.Z);F.Z.registerClass(K),pt.Z=K},20380:function(Zt,pt,P){"use strict";var C=P(34251),k=function(){function v(F){this._setting=F||{},this._extent=[Infinity,-Infinity]}return v.prototype.getSetting=function(F){return this._setting[F]},v.prototype.unionExtent=function(F){var z=this._extent;F[0]<z[0]&&(z[0]=F[0]),F[1]>z[1]&&(z[1]=F[1])},v.prototype.unionExtentFromData=function(F,z){this.unionExtent(F.getApproximateExtent(z))},v.prototype.getExtent=function(){return this._extent.slice()},v.prototype.setExtent=function(F,z){var W=this._extent;isNaN(F)||(W[0]=F),isNaN(z)||(W[1]=z)},v.prototype.isInExtentRange=function(F){return this._extent[0]<=F&&this._extent[1]>=F},v.prototype.isBlank=function(){return this._isBlank},v.prototype.setBlank=function(F){this._isBlank=F},v}();C.au(k),pt.Z=k},65021:function(Zt,pt,P){"use strict";P.d(pt,{lM:function(){return v},Qf:function(){return F},r1:function(){return z},lb:function(){return W},XS:function(){return b},Fv:function(){return A},bA:function(){return S}});var C=P(85669);function k(m){var g=Math.pow(10,quantityExponent(Math.abs(m))),y=Math.abs(m/g);return y===0||y===1||y===2||y===3||y===5}function v(m){return m.type==="interval"||m.type==="log"}function F(m,g,y,E){var h={},c=m[1]-m[0],_=h.interval=(0,C.kx)(c/g,!0);y!=null&&_<y&&(_=h.interval=y),E!=null&&_>E&&(_=h.interval=E);var M=h.intervalPrecision=W(_),l=h.niceTickExtent=[(0,C.NM)(Math.ceil(m[0]/_)*_,M),(0,C.NM)(Math.floor(m[1]/_)*_,M)];return R(l,m),h}function z(m){var g=Math.pow(10,(0,C.xW)(m)),y=m/g;return y?y===2?y=3:y===3?y=5:y*=2:y=1,(0,C.NM)(y*g)}function W(m){return(0,C.p8)(m)+2}function K(m,g,y){m[g]=Math.max(Math.min(m[g],y[1]),y[0])}function R(m,g){!isFinite(m[0])&&(m[0]=g[0]),!isFinite(m[1])&&(m[1]=g[1]),K(m,0,g),K(m,1,g),m[0]>m[1]&&(m[0]=m[1])}function b(m,g){return m>=g[0]&&m<=g[1]}function A(m,g){return g[1]===g[0]?.5:(m-g[0])/(g[1]-g[0])}function S(m,g){return m*(g[1]-g[0])+g[0]}},34251:function(Zt,pt,P){"use strict";P.d(pt,{u9:function(){return W},PT:function(){return R},dm:function(){return b},pw:function(){return S},Qj:function(){return g},au:function(){return h}});var C=P(18299),k=P(33051),v=".",F="___EC__COMPONENT__CONTAINER___",z="___EC__EXTENDED_CLASS___";function W(c){var _={main:"",sub:""};if(c){var M=c.split(v);_.main=M[0]||"",_.sub=M[1]||""}return _}function K(c){k.hu(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(c),'componentType "'+c+'" illegal')}function R(c){return!!(c&&c[z])}function b(c,_){c.$constructor=c,c.extend=function(M){var l=this,O;return A(l)?O=function(d){(0,C.ZT)(f,d);function f(){return d.apply(this,arguments)||this}return f}(l):(O=function(){(M.$constructor||l).apply(this,arguments)},k.XW(O,this)),k.l7(O.prototype,M),O[z]=!0,O.extend=this.extend,O.superCall=y,O.superApply=E,O.superClass=l,O}}function A(c){return k.mf(c)&&/^class\s/.test(Function.prototype.toString.call(c))}function S(c,_){c.extend=_.extend}var m=Math.round(Math.random()*10);function g(c){var _=["__\0is_clz",m++].join("_");c.prototype[_]=!0,c.isInstance=function(M){return!!(M&&M[_])}}function y(c,_){for(var M=[],l=2;l<arguments.length;l++)M[l-2]=arguments[l];return this.superClass.prototype[_].apply(c,M)}function E(c,_,M){return this.superClass.prototype[_].apply(c,M)}function h(c){var _={};c.registerClass=function(l){var O=l.type||l.prototype.type;if(O){K(O),l.prototype.type=O;var d=W(O);if(!d.sub)_[d.main]=l;else if(d.sub!==F){var f=M(d);f[d.sub]=l}}return l},c.getClass=function(l,O,d){var f=_[l];if(f&&f[F]&&(f=O?f[O]:null),d&&!f)throw new Error(O?"Component "+l+"."+(O||"")+" is used but not imported.":l+".type should be specified.");return f},c.getClassesByMainType=function(l){var O=W(l),d=[],f=_[O.main];return f&&f[F]?k.S6(f,function(u,p){p!==F&&d.push(u)}):d.push(f),d},c.hasClass=function(l){var O=W(l);return!!_[O.main]},c.getAllClassMainTypes=function(){var l=[];return k.S6(_,function(O,d){l.push(d)}),l},c.hasSubTypes=function(l){var O=W(l),d=_[O.main];return d&&d[F]};function M(l){var O=_[l.main];return(!O||!O[F])&&(O=_[l.main]={},O[F]=!0),O}}},42151:function(Zt,pt,P){"use strict";P.d(pt,{Kr:function(){return F},cj:function(){return z},jS:function(){return W},ZL:function(){return K}});var C=P(33051),k=P(34251),v=Math.round(Math.random()*10);function F(R){return[R||"",v++].join("_")}function z(R){var b={};R.registerSubTypeDefaulter=function(A,S){var m=(0,k.u9)(A);b[m.main]=S},R.determineSubType=function(A,S){var m=S.type;if(!m){var g=(0,k.u9)(A).main;R.hasSubTypes(A)&&b[g]&&(m=b[g](S))}return m}}function W(R,b){R.topologicalTravel=function(g,y,E,h){if(!g.length)return;var c=A(y),_=c.graph,M=c.noEntryList,l={};for(C.S6(g,function(n){l[n]=!0});M.length;){var O=M.pop(),d=_[O],f=!!l[O];f&&(E.call(h,O,d.originalDeps.slice()),delete l[O]),C.S6(d.successor,f?p:u)}C.S6(l,function(){var n="";throw new Error(n)});function u(n){_[n].entryCount--,_[n].entryCount===0&&M.push(n)}function p(n){l[n]=!0,u(n)}};function A(g){var y={},E=[];return C.S6(g,function(h){var c=S(y,h),_=c.originalDeps=b(h),M=m(_,g);c.entryCount=M.length,c.entryCount===0&&E.push(h),C.S6(M,function(l){C.cq(c.predecessor,l)<0&&c.predecessor.push(l);var O=S(y,l);C.cq(O.successor,l)<0&&O.successor.push(h)})}),{graph:y,noEntryList:E}}function S(g,y){return g[y]||(g[y]={predecessor:[],successor:[]}),g[y]}function m(g,y){var E=[];return C.S6(g,function(h){C.cq(y,h)>=0&&E.push(h)}),E}}function K(R,b){return C.TS(C.TS({},R,!0),b,!0)}},48625:function(Zt,pt,P){"use strict";P.d(pt,{I:function(){return y}});var C=Math.round(Math.random()*9),k=typeof Object.defineProperty=="function",v=function(){function l(){this._id="__ec_inner_"+C++}return l.prototype.get=function(O){return this._guard(O)[this._id]},l.prototype.set=function(O,d){var f=this._guard(O);return k?Object.defineProperty(f,this._id,{value:d,enumerable:!1,configurable:!0}):f[this._id]=d,this},l.prototype.delete=function(O){return this.has(O)?(delete this._guard(O)[this._id],!0):!1},l.prototype.has=function(O){return!!this._guard(O)[this._id]},l.prototype._guard=function(O){if(O!==Object(O))throw TypeError("Value of WeakMap is not a non-null object.");return O},l}(),F=v,z=P(92528),W=P(33051),K=P(85669),R=P(41525),b=P(97772),A=P(23132),S=new F,m=new z.ZP(100),g=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function y(l,O){if(l==="none")return null;var d=O.getDevicePixelRatio(),f=O.getZr(),u=f.painter.type==="svg";l.dirty&&S.delete(l);var p=S.get(l);if(p)return p;var n=(0,W.ce)(l,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});n.backgroundColor==="none"&&(n.backgroundColor=null);var i={repeat:"repeat"};return t(i),i.rotation=n.rotation,i.scaleX=i.scaleY=u?1:1/d,S.set(l,i),l.dirty=!1,i;function t(r){for(var e=[d],a=!0,s=0;s<g.length;++s){var o=n[g[s]];if(o!=null&&!(0,W.kJ)(o)&&!(0,W.HD)(o)&&!(0,W.hj)(o)&&typeof o!="boolean"){a=!1;break}e.push(o)}var D;if(a){D=e.join(",")+(u?"-svg":"");var B=m.get(D);B&&(u?r.svgElement=B:r.image=B)}var I=h(n.dashArrayX),L=c(n.dashArrayY),U=E(n.symbol),x=_(I),T=M(L),w=!u&&A.qW.createCanvas(),V=u&&{tag:"g",attrs:{},key:"dcl",children:[]},N=Z(),G;w&&(w.width=N.width*d,w.height=N.height*d,G=w.getContext("2d")),H(),a&&m.put(D,w||V),r.image=w,r.svgElement=V,r.svgWidth=N.width,r.svgHeight=N.height;function Z(){for(var X=1,ut=0,lt=x.length;ut<lt;++ut)X=(0,K.nl)(X,x[ut]);for(var ot=1,ut=0,lt=U.length;ut<lt;++ut)ot=(0,K.nl)(ot,U[ut].length);X*=ot;var q=T*x.length*U.length;if(!1)var tt;return{width:Math.max(1,Math.min(X,n.maxTileWidth)),height:Math.max(1,Math.min(q,n.maxTileHeight))}}function H(){G&&(G.clearRect(0,0,w.width,w.height),n.backgroundColor&&(G.fillStyle=n.backgroundColor,G.fillRect(0,0,w.width,w.height)));for(var X=0,ut=0;ut<L.length;++ut)X+=L[ut];if(X<=0)return;for(var lt=-T,ot=0,q=0,tt=0;lt<N.height;){if(ot%2==0){for(var j=q/2%U.length,et=0,ct=0,_t=0;et<N.width*2;){for(var St=0,ut=0;ut<I[tt].length;++ut)St+=I[tt][ut];if(St<=0)break;if(ct%2==0){var At=(1-n.symbolSize)*.5,Ct=et+I[tt][ct]*At,Et=lt+L[ot]*At,Ot=I[tt][ct]*n.symbolSize,Wt=L[ot]*n.symbolSize,Ut=_t/2%U[j].length;Bt(Ct,Et,Ot,Wt,U[j][Ut])}et+=I[tt][ct],++_t,++ct,ct===I[tt].length&&(ct=0)}++tt,tt===I.length&&(tt=0)}lt+=L[ot],++q,++ot,ot===L.length&&(ot=0)}function Bt(Ht,kt,Xt,Vt,st){var gt=u?1:d,xt=(0,R.th)(st,Ht*gt,kt*gt,Xt*gt,Vt*gt,n.color,n.symbolKeepAspect);if(u){var Rt=f.painter.renderOneToVNode(xt);Rt&&V.children.push(Rt)}else(0,b.RV)(G,xt)}}}}function E(l){if(!l||l.length===0)return[["rect"]];if((0,W.HD)(l))return[[l]];for(var O=!0,d=0;d<l.length;++d)if(!(0,W.HD)(l[d])){O=!1;break}if(O)return E([l]);for(var f=[],d=0;d<l.length;++d)(0,W.HD)(l[d])?f.push([l[d]]):f.push(l[d]);return f}function h(l){if(!l||l.length===0)return[[0,0]];if((0,W.hj)(l)){var O=Math.ceil(l);return[[O,O]]}for(var d=!0,f=0;f<l.length;++f)if(!(0,W.hj)(l[f])){d=!1;break}if(d)return h([l]);for(var u=[],f=0;f<l.length;++f)if((0,W.hj)(l[f])){var O=Math.ceil(l[f]);u.push([O,O])}else{var O=(0,W.UI)(l[f],function(i){return Math.ceil(i)});O.length%2==1?u.push(O.concat(O)):u.push(O)}return u}function c(l){if(!l||typeof l=="object"&&l.length===0)return[0,0];if((0,W.hj)(l)){var O=Math.ceil(l);return[O,O]}var d=(0,W.UI)(l,function(f){return Math.ceil(f)});return l.length%2?d.concat(d):d}function _(l){return(0,W.UI)(l,function(O){return M(O)})}function M(l){for(var O=0,d=0;d<l.length;++d)O+=l[d];return l.length%2==1?O*2:O}},18310:function(Zt,pt,P){"use strict";P.d(pt,{o:function(){return C}});function C(k,v,F){for(var z;k&&!(v(k)&&(z=k,F));)k=k.__hostTarget||k.parent;return z}},78988:function(Zt,pt,P){"use strict";P.d(pt,{OD:function(){return z},zW:function(){return W},MY:function(){return K},uX:function(){return R},kF:function(){return S},A0:function(){return g},Lz:function(){return h},MI:function(){return c}});var C=P(33051),k=P(54058),v=P(85669),F=P(15015);function z(_){if(!(0,v.kE)(_))return C.HD(_)?_:"-";var M=(_+"").split(".");return M[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(M.length>1?"."+M[1]:"")}function W(_,M){return _=(_||"").toLowerCase().replace(/-(.)/g,function(l,O){return O.toUpperCase()}),M&&_&&(_=_.charAt(0).toUpperCase()+_.slice(1)),_}var K=C.MY;function R(_,M,l){var O="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function d(t){return t&&C.fy(t)?t:"-"}function f(t){return!!(t!=null&&!isNaN(t)&&isFinite(t))}var u=M==="time",p=_ instanceof Date;if(u||p){var n=u?(0,v.sG)(_):_;if(isNaN(+n)){if(p)return"-"}else return(0,F.WU)(n,O,l)}if(M==="ordinal")return C.cd(_)?d(_):C.hj(_)&&f(_)?_+"":"-";var i=(0,v.FK)(_);return f(i)?z(i):C.cd(_)?d(_):typeof _=="boolean"?_+"":"-"}var b=["a","b","c","d","e","f","g"],A=function(_,M){return"{"+_+(M==null?"":M)+"}"};function S(_,M,l){C.kJ(M)||(M=[M]);var O=M.length;if(!O)return"";for(var d=M[0].$vars||[],f=0;f<d.length;f++){var u=b[f];_=_.replace(A(u),A(u,0))}for(var p=0;p<O;p++)for(var n=0;n<d.length;n++){var i=M[p][d[n]];_=_.replace(A(b[n],p),l?(0,k.F1)(i):i)}return _}function m(_,M,l){return zrUtil.each(M,function(O,d){_=_.replace("{"+d+"}",l?encodeHTML(O):O)}),_}function g(_,M){var l=C.HD(_)?{color:_,extraCssText:M}:_||{},O=l.color,d=l.type;M=l.extraCssText;var f=l.renderMode||"html";if(!O)return"";if(f==="html")return d==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+(0,k.F1)(O)+";"+(M||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+(0,k.F1)(O)+";"+(M||"")+'"></span>';var u=l.markerId||"markerX";return{renderMode:f,content:"{"+u+"|}  ",style:d==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:O}:{width:10,height:10,borderRadius:5,backgroundColor:O}}}function y(_,M,l){(_==="week"||_==="month"||_==="quarter"||_==="half-year"||_==="year")&&(_=`MM-dd
yyyy`);var O=parseDate(M),d=l?"getUTC":"get",f=O[d+"FullYear"](),u=O[d+"Month"]()+1,p=O[d+"Date"](),n=O[d+"Hours"](),i=O[d+"Minutes"](),t=O[d+"Seconds"](),r=O[d+"Milliseconds"]();return _=_.replace("MM",pad(u,2)).replace("M",u).replace("yyyy",f).replace("yy",pad(f%100+"",2)).replace("dd",pad(p,2)).replace("d",p).replace("hh",pad(n,2)).replace("h",n).replace("mm",pad(i,2)).replace("m",i).replace("ss",pad(t,2)).replace("s",t).replace("SSS",pad(r,3)),_}function E(_){return _&&_.charAt(0).toUpperCase()+_.substr(1)}function h(_,M){return M=M||"transparent",C.HD(_)?_:C.Kn(_)&&_.colorStops&&(_.colorStops[0]||{}).color||M}function c(_,M){if(M==="_blank"||M==="blank"){var l=window.open();l.opener=null,l.location.href=_}else window.open(_,M)}},50453:function(Zt,pt,P){"use strict";P.r(pt),P.d(pt,{Arc:function(){return n.Z},BezierCurve:function(){return p},BoundingRect:function(){return s.Z},Circle:function(){return b.Z},CompoundPath:function(){return i.Z},Ellipse:function(){return g},Group:function(){return K.Z},Image:function(){return W.ZP},IncrementalDisplayable:function(){return U},Line:function(){return M.Z},LinearGradient:function(){return t.Z},OrientedBoundingRect:function(){return o.Z},Path:function(){return F.ZP},Point:function(){return D.Z},Polygon:function(){return h.Z},Polyline:function(){return c.Z},RadialGradient:function(){return a},Rect:function(){return _.Z},Ring:function(){return E.Z},Sector:function(){return y.C},Text:function(){return R.ZP},applyTransform:function(){return Et},clipPointsByRect:function(){return Ht},clipRectByRect:function(){return kt},createIcon:function(){return Xt},extendPath:function(){return ut},extendShape:function(){return H},getShapeClass:function(){return ot},getTransform:function(){return Ct},groupTransition:function(){return Bt},initProps:function(){return V.KZ},isElementRemoved:function(){return V.eq},lineLineIntersect:function(){return st},linePolygonIntersect:function(){return Vt},makeImage:function(){return tt},makePath:function(){return q},mergePath:function(){return et},registerShape:function(){return lt},removeElement:function(){return V.bX},removeElementWithFadeOut:function(){return V.XD},resizePath:function(){return ct},setTooltipConfig:function(){return Rt},subPixelOptimize:function(){return At},subPixelOptimizeLine:function(){return _t},subPixelOptimizeRect:function(){return St},transformDirection:function(){return Ot},traverseElements:function(){return Mt},updateProps:function(){return V.D}});var C=P(7494),k=P(32892),v=P(45280),F=P(4665),z=P(87411),W=P(44535),K=P(38154),R=P(9074),b=P(69538),A=P(4311),S=function(){function vt(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return vt}(),m=function(vt){(0,A.ZT)(Y,vt);function Y($){return vt.call(this,$)||this}return Y.prototype.getDefaultShape=function(){return new S},Y.prototype.buildPath=function($,Q){var ft=.5522848,Tt=Q.cx,wt=Q.cy,bt=Q.rx,zt=Q.ry,Qt=bt*ft,qt=zt*ft;$.moveTo(Tt-bt,wt),$.bezierCurveTo(Tt-bt,wt-qt,Tt-Qt,wt-zt,Tt,wt-zt),$.bezierCurveTo(Tt+Qt,wt-zt,Tt+bt,wt-qt,Tt+bt,wt),$.bezierCurveTo(Tt+bt,wt+qt,Tt+Qt,wt+zt,Tt,wt+zt),$.bezierCurveTo(Tt-Qt,wt+zt,Tt-bt,wt+qt,Tt-bt,wt),$.closePath()},Y}(F.ZP);m.prototype.type="ellipse";var g=m,y=P(27214),E=P(85795),h=P(95094),c=P(62514),_=P(35151),M=P(22095),l=P(18554),O=[],d=function(){function vt(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return vt}();function f(vt,Y,$){var Q=vt.cpx2,ft=vt.cpy2;return Q!=null||ft!=null?[($?l.X_:l.af)(vt.x1,vt.cpx1,vt.cpx2,vt.x2,Y),($?l.X_:l.af)(vt.y1,vt.cpy1,vt.cpy2,vt.y2,Y)]:[($?l.AZ:l.Zm)(vt.x1,vt.cpx1,vt.x2,Y),($?l.AZ:l.Zm)(vt.y1,vt.cpy1,vt.y2,Y)]}var u=function(vt){(0,A.ZT)(Y,vt);function Y($){return vt.call(this,$)||this}return Y.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Y.prototype.getDefaultShape=function(){return new d},Y.prototype.buildPath=function($,Q){var ft=Q.x1,Tt=Q.y1,wt=Q.x2,bt=Q.y2,zt=Q.cpx1,Qt=Q.cpy1,qt=Q.cpx2,re=Q.cpy2,ie=Q.percent;ie!==0&&($.moveTo(ft,Tt),qt==null||re==null?(ie<1&&((0,l.Lx)(ft,zt,wt,ie,O),zt=O[1],wt=O[2],(0,l.Lx)(Tt,Qt,bt,ie,O),Qt=O[1],bt=O[2]),$.quadraticCurveTo(zt,Qt,wt,bt)):(ie<1&&((0,l.Vz)(ft,zt,qt,wt,ie,O),zt=O[1],qt=O[2],wt=O[3],(0,l.Vz)(Tt,Qt,re,bt,ie,O),Qt=O[1],re=O[2],bt=O[3]),$.bezierCurveTo(zt,Qt,qt,re,wt,bt)))},Y.prototype.pointAt=function($){return f(this.shape,$,!1)},Y.prototype.tangentAt=function($){var Q=f(this.shape,$,!0);return v.Fv(Q,Q)},Y}(F.ZP);u.prototype.type="bezier-curve";var p=u,n=P(14826),i=P(52776),t=P(74438),r=P(31797),e=function(vt){(0,A.ZT)(Y,vt);function Y($,Q,ft,Tt,wt){var bt=vt.call(this,Tt)||this;return bt.x=$==null?.5:$,bt.y=Q==null?.5:Q,bt.r=ft==null?.5:ft,bt.type="radial",bt.global=wt||!1,bt}return Y}(r.Z),a=e,s=P(60479),o=P(41587),D=P(41610),B=P(7719),I=[],L=function(vt){(0,A.ZT)(Y,vt);function Y(){var $=vt!==null&&vt.apply(this,arguments)||this;return $.notClear=!0,$.incremental=!0,$._displayables=[],$._temporaryDisplayables=[],$._cursor=0,$}return Y.prototype.traverse=function($,Q){$.call(Q,this)},Y.prototype.useStyle=function(){this.style={}},Y.prototype.getCursor=function(){return this._cursor},Y.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},Y.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},Y.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},Y.prototype.addDisplayable=function($,Q){Q?this._temporaryDisplayables.push($):this._displayables.push($),this.markRedraw()},Y.prototype.addDisplayables=function($,Q){Q=Q||!1;for(var ft=0;ft<$.length;ft++)this.addDisplayable($[ft],Q)},Y.prototype.getDisplayables=function(){return this._displayables},Y.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},Y.prototype.eachPendingDisplayable=function($){for(var Q=this._cursor;Q<this._displayables.length;Q++)$&&$(this._displayables[Q]);for(var Q=0;Q<this._temporaryDisplayables.length;Q++)$&&$(this._temporaryDisplayables[Q])},Y.prototype.update=function(){this.updateTransform();for(var $=this._cursor;$<this._displayables.length;$++){var Q=this._displayables[$];Q.parent=this,Q.update(),Q.parent=null}for(var $=0;$<this._temporaryDisplayables.length;$++){var Q=this._temporaryDisplayables[$];Q.parent=this,Q.update(),Q.parent=null}},Y.prototype.getBoundingRect=function(){if(!this._rect){for(var $=new s.Z(Infinity,Infinity,-Infinity,-Infinity),Q=0;Q<this._displayables.length;Q++){var ft=this._displayables[Q],Tt=ft.getBoundingRect().clone();ft.needLocalTransform()&&Tt.applyTransform(ft.getLocalTransform(I)),$.union(Tt)}this._rect=$}return this._rect},Y.prototype.contain=function($,Q){var ft=this.transformCoordToLocal($,Q),Tt=this.getBoundingRect();if(Tt.contain(ft[0],ft[1]))for(var wt=0;wt<this._displayables.length;wt++){var bt=this._displayables[wt];if(bt.contain($,Q))return!0}return!1},Y}(B.ZP),U=L,x=P(24111),T=P(33051),w=P(30106),V=P(44292),N=Math.max,G=Math.min,Z={};function H(vt){return F.ZP.extend(vt)}var X=C.Pc;function ut(vt,Y){return X(vt,Y)}function lt(vt,Y){Z[vt]=Y}function ot(vt){if(Z.hasOwnProperty(vt))return Z[vt]}function q(vt,Y,$,Q){var ft=C.iR(vt,Y);return $&&(Q==="center"&&($=j($,ft.getBoundingRect())),ct(ft,$)),ft}function tt(vt,Y,$){var Q=new W.ZP({style:{image:vt,x:Y.x,y:Y.y,width:Y.width,height:Y.height},onload:function(ft){if($==="center"){var Tt={width:ft.width,height:ft.height};Q.setStyle(j(Y,Tt))}}});return Q}function j(vt,Y){var $=Y.width/Y.height,Q=vt.height*$,ft;Q<=vt.width?ft=vt.height:(Q=vt.width,ft=Q/$);var Tt=vt.x+vt.width/2,wt=vt.y+vt.height/2;return{x:Tt-Q/2,y:wt-ft/2,width:Q,height:ft}}var et=C.AA;function ct(vt,Y){if(!!vt.applyTransform){var $=vt.getBoundingRect(),Q=$.calculateTransform(Y);vt.applyTransform(Q)}}function _t(vt,Y){return x._3(vt,vt,{lineWidth:Y}),vt}function St(vt){return x.Pw(vt.shape,vt.shape,vt.style),vt}var At=x.vu;function Ct(vt,Y){for(var $=k.yR([]);vt&&vt!==Y;)k.dC($,vt.getLocalTransform(),$),vt=vt.parent;return $}function Et(vt,Y,$){return Y&&!(0,T.zG)(Y)&&(Y=z.ZP.getLocalTransform(Y)),$&&(Y=k.U_([],Y)),v.Ne([],vt,Y)}function Ot(vt,Y,$){var Q=Y[4]===0||Y[5]===0||Y[0]===0?1:Math.abs(2*Y[4]/Y[0]),ft=Y[4]===0||Y[5]===0||Y[2]===0?1:Math.abs(2*Y[4]/Y[2]),Tt=[vt==="left"?-Q:vt==="right"?Q:0,vt==="top"?-ft:vt==="bottom"?ft:0];return Tt=Et(Tt,Y,$),Math.abs(Tt[0])>Math.abs(Tt[1])?Tt[0]>0?"right":"left":Tt[1]>0?"bottom":"top"}function Wt(vt){return!vt.isGroup}function Ut(vt){return vt.shape!=null}function Bt(vt,Y,$){if(!vt||!Y)return;function Q(wt){var bt={};return wt.traverse(function(zt){Wt(zt)&&zt.anid&&(bt[zt.anid]=zt)}),bt}function ft(wt){var bt={x:wt.x,y:wt.y,rotation:wt.rotation};return Ut(wt)&&(bt.shape=(0,T.l7)({},wt.shape)),bt}var Tt=Q(vt);Y.traverse(function(wt){if(Wt(wt)&&wt.anid){var bt=Tt[wt.anid];if(bt){var zt=ft(wt);wt.attr(ft(bt)),(0,V.D)(wt,zt,$,(0,w.A)(wt).dataIndex)}}})}function Ht(vt,Y){return(0,T.UI)(vt,function($){var Q=$[0];Q=N(Q,Y.x),Q=G(Q,Y.x+Y.width);var ft=$[1];return ft=N(ft,Y.y),ft=G(ft,Y.y+Y.height),[Q,ft]})}function kt(vt,Y){var $=N(vt.x,Y.x),Q=G(vt.x+vt.width,Y.x+Y.width),ft=N(vt.y,Y.y),Tt=G(vt.y+vt.height,Y.y+Y.height);if(Q>=$&&Tt>=ft)return{x:$,y:ft,width:Q-$,height:Tt-ft}}function Xt(vt,Y,$){var Q=(0,T.l7)({rectHover:!0},Y),ft=Q.style={strokeNoScale:!0};if($=$||{x:-1,y:-1,width:2,height:2},vt)return vt.indexOf("image://")===0?(ft.image=vt.slice(8),(0,T.ce)(ft,$),new W.ZP(Q)):q(vt.replace("path://",""),Q,$,"center")}function Vt(vt,Y,$,Q,ft){for(var Tt=0,wt=ft[ft.length-1];Tt<ft.length;Tt++){var bt=ft[Tt];if(st(vt,Y,$,Q,bt[0],bt[1],wt[0],wt[1]))return!0;wt=bt}}function st(vt,Y,$,Q,ft,Tt,wt,bt){var zt=$-vt,Qt=Q-Y,qt=wt-ft,re=bt-Tt,ie=gt(qt,re,zt,Qt);if(xt(ie))return!1;var oe=vt-ft,ue=Y-Tt,Me=gt(oe,ue,zt,Qt)/ie;if(Me<0||Me>1)return!1;var Te=gt(oe,ue,qt,re)/ie;return!(Te<0||Te>1)}function gt(vt,Y,$,Q){return vt*Q-$*Y}function xt(vt){return vt<=1e-6&&vt>=-1e-6}function Rt(vt){var Y=vt.itemTooltipOption,$=vt.componentModel,Q=vt.itemName,ft=(0,T.HD)(Y)?{formatter:Y}:Y,Tt=$.mainType,wt=$.componentIndex,bt={componentType:Tt,name:Q,$vars:["name"]};bt[Tt+"Index"]=wt;var zt=vt.formatterParamsExtra;zt&&(0,T.S6)((0,T.XP)(zt),function(qt){(0,T.RI)(bt,qt)||(bt[qt]=zt[qt],bt.$vars.push(qt))});var Qt=(0,w.A)(vt.el);Qt.componentMainType=Tt,Qt.componentIndex=wt,Qt.tooltipConfig={name:Q,option:(0,T.ce)({content:Q,formatterParams:bt},ft)}}function dt(vt,Y){var $;vt.isGroup&&($=Y(vt)),$||vt.traverse(Y)}function Mt(vt,Y){if(vt)if((0,T.kJ)(vt))for(var $=0;$<vt.length;$++)dt(vt[$],Y);else dt(vt,Y)}lt("circle",b.Z),lt("ellipse",g),lt("sector",y.C),lt("ring",E.Z),lt("polygon",h.Z),lt("polyline",c.Z),lt("rect",_.Z),lt("line",M.Z),lt("bezierCurve",p),lt("arc",n.Z)},30106:function(Zt,pt,P){"use strict";P.d(pt,{A:function(){return k},Q:function(){return v}});var C=P(32234),k=(0,C.Yf)(),v=function(F,z,W,K){if(K){var R=k(K);R.dataIndex=W,R.dataType=z,R.seriesIndex=F,R.ssrType="chart",K.type==="group"&&K.traverse(function(b){var A=k(b);A.seriesIndex=F,A.dataIndex=W,A.dataType=z,A.ssrType="chart"})}}},76172:function(Zt,pt,P){"use strict";P.d(pt,{BZ:function(){return b},ME:function(){return g},p$:function(){return y},YD:function(){return h},dt:function(){return c},tE:function(){return _}});var C=P(33051),k=P(60479),v=P(85669),F=P(78988),z=C.S6,W=["left","right","top","bottom","width","height"],K=[["width","left","right"],["height","top","bottom"]];function R(l,O,d,f,u){var p=0,n=0;f==null&&(f=Infinity),u==null&&(u=Infinity);var i=0;O.eachChild(function(t,r){var e=t.getBoundingRect(),a=O.childAt(r+1),s=a&&a.getBoundingRect(),o,D;if(l==="horizontal"){var B=e.width+(s?-s.x+e.x:0);o=p+B,o>f||t.newline?(p=0,o=B,n+=i+d,i=e.height):i=Math.max(i,e.height)}else{var I=e.height+(s?-s.y+e.y:0);D=n+I,D>u||t.newline?(p+=i+d,n=0,D=I,i=e.width):i=Math.max(i,e.width)}t.newline||(t.x=p,t.y=n,t.markRedraw(),l==="horizontal"?p=o+d:n=D+d)})}var b=R,A=C.WA(R,"vertical"),S=C.WA(R,"horizontal");function m(l,O,d){var f=O.width,u=O.height,p=parsePercent(l.left,f),n=parsePercent(l.top,u),i=parsePercent(l.right,f),t=parsePercent(l.bottom,u);return(isNaN(p)||isNaN(parseFloat(l.left)))&&(p=0),(isNaN(i)||isNaN(parseFloat(l.right)))&&(i=f),(isNaN(n)||isNaN(parseFloat(l.top)))&&(n=0),(isNaN(t)||isNaN(parseFloat(l.bottom)))&&(t=u),d=formatUtil.normalizeCssArray(d||0),{width:Math.max(i-p-d[1]-d[3],0),height:Math.max(t-n-d[0]-d[2],0)}}function g(l,O,d){d=F.MY(d||0);var f=O.width,u=O.height,p=(0,v.GM)(l.left,f),n=(0,v.GM)(l.top,u),i=(0,v.GM)(l.right,f),t=(0,v.GM)(l.bottom,u),r=(0,v.GM)(l.width,f),e=(0,v.GM)(l.height,u),a=d[2]+d[0],s=d[1]+d[3],o=l.aspect;switch(isNaN(r)&&(r=f-i-s-p),isNaN(e)&&(e=u-t-a-n),o!=null&&(isNaN(r)&&isNaN(e)&&(o>f/u?r=f*.8:e=u*.8),isNaN(r)&&(r=o*e),isNaN(e)&&(e=r/o)),isNaN(p)&&(p=f-i-r-s),isNaN(n)&&(n=u-t-e-a),l.left||l.right){case"center":p=f/2-r/2-d[3];break;case"right":p=f-r-s;break}switch(l.top||l.bottom){case"middle":case"center":n=u/2-e/2-d[0];break;case"bottom":n=u-e-a;break}p=p||0,n=n||0,isNaN(r)&&(r=f-s-p-(i||0)),isNaN(e)&&(e=u-a-n-(t||0));var D=new k.Z(p+d[3],n+d[0],r,e);return D.margin=d,D}function y(l,O,d,f,u,p){var n=!u||!u.hv||u.hv[0],i=!u||!u.hv||u.hv[1],t=u&&u.boundingMode||"all";if(p=p||l,p.x=l.x,p.y=l.y,!n&&!i)return!1;var r;if(t==="raw")r=l.type==="group"?new k.Z(0,0,+O.width||0,+O.height||0):l.getBoundingRect();else if(r=l.getBoundingRect(),l.needLocalTransform()){var e=l.getLocalTransform();r=r.clone(),r.applyTransform(e)}var a=g(C.ce({width:r.width,height:r.height},O),d,f),s=n?a.x-r.x:0,o=i?a.y-r.y:0;return t==="raw"?(p.x=s,p.y=o):(p.x+=s,p.y+=o),p===l&&l.markRedraw(),!0}function E(l,O){return l[K[O][0]]!=null||l[K[O][1]]!=null&&l[K[O][2]]!=null}function h(l){var O=l.layoutMode||l.constructor.layoutMode;return C.Kn(O)?O:O?{type:O}:null}function c(l,O,d){var f=d&&d.ignoreSize;!C.kJ(f)&&(f=[f,f]);var u=n(K[0],0),p=n(K[1],1);r(K[0],l,u),r(K[1],l,p);function n(e,a){var s={},o=0,D={},B=0,I=2;if(z(e,function(x){D[x]=l[x]}),z(e,function(x){i(O,x)&&(s[x]=D[x]=O[x]),t(s,x)&&o++,t(D,x)&&B++}),f[a])return t(O,e[1])?D[e[2]]=null:t(O,e[2])&&(D[e[1]]=null),D;if(B===I||!o)return D;if(o>=I)return s;for(var L=0;L<e.length;L++){var U=e[L];if(!i(s,U)&&i(l,U)){s[U]=l[U];break}}return s}function i(e,a){return e.hasOwnProperty(a)}function t(e,a){return e[a]!=null&&e[a]!=="auto"}function r(e,a,s){z(e,function(o){a[o]=s[o]})}}function _(l){return M({},l)}function M(l,O){return O&&l&&z(W,function(d){O.hasOwnProperty(d)&&(l[d]=O[d])}),l}},70175:function(Zt,pt,P){"use strict";P.d(pt,{ZK:function(){return W},Sh:function(){return R},_y:function(){return S}});var C="[ECharts] ",k={},v=typeof console!="undefined"&&console.warn&&console.log;function F(m,g,y){if(v){if(y){if(k[g])return;k[g]=!0}console[m](C+g)}}function z(m,g){F("log",m,g)}function W(m,g){F("warn",m,g)}function K(m,g){F("error",m,g)}function R(m){}function b(m,g,y){}function A(){for(var m=[],g=0;g<arguments.length;g++)m[g]=arguments[g];var y="";if(!1)var E;return y}function S(m){throw new Error(m)}},32234:function(Zt,pt,P){"use strict";P.d(pt,{kF:function(){return K},Cc:function(){return R},Td:function(){return b},C4:function(){return A},Co:function(){return S},ab:function(){return m},U5:function(){return O},yu:function(){return u},lY:function(){return p},g0:function(){return n},O0:function(){return i},XI:function(){return r},gO:function(){return e},Yf:function(){return a},pm:function(){return o},zH:function(){return D},C6:function(){return B},iP:function(){return I},HZ:function(){return L},P$:function(){return U},IL:function(){return x},U9:function(){return T},pk:function(){return V}});var C=P(33051),k=P(66387),v=P(85669);function F(N,G,Z){return(G-N)*Z+N}var z="series\0",W="\0_ec_\0";function K(N){return N instanceof Array?N:N==null?[]:[N]}function R(N,G,Z){if(N){N[G]=N[G]||{},N.emphasis=N.emphasis||{},N.emphasis[G]=N.emphasis[G]||{};for(var H=0,X=Z.length;H<X;H++){var ut=Z[H];!N.emphasis[G].hasOwnProperty(ut)&&N[G].hasOwnProperty(ut)&&(N.emphasis[G][ut]=N[G][ut])}}}var b=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function A(N){return(0,C.Kn)(N)&&!(0,C.kJ)(N)&&!(N instanceof Date)?N.value:N}function S(N){return(0,C.Kn)(N)&&!(N instanceof Array)}function m(N,G,Z){var H=Z==="normalMerge",X=Z==="replaceMerge",ut=Z==="replaceAll";N=N||[],G=(G||[]).slice();var lt=(0,C.kW)();(0,C.S6)(G,function(q,tt){if(!(0,C.Kn)(q)){G[tt]=null;return}});var ot=g(N,lt,Z);return(H||X)&&y(ot,N,lt,G),H&&E(ot,G),H||X?h(ot,G,X):ut&&c(ot,G),_(ot),ot}function g(N,G,Z){var H=[];if(Z==="replaceAll")return H;for(var X=0;X<N.length;X++){var ut=N[X];ut&&ut.id!=null&&G.set(ut.id,X),H.push({existing:Z==="replaceMerge"||p(ut)?null:ut,newOption:null,keyInfo:null,brandNew:null})}return H}function y(N,G,Z,H){(0,C.S6)(H,function(X,ut){if(!(!X||X.id==null)){var lt=l(X.id),ot=Z.get(lt);if(ot!=null){var q=N[ot];(0,C.hu)(!q.newOption,'Duplicated option on id "'+lt+'".'),q.newOption=X,q.existing=G[ot],H[ut]=null}}})}function E(N,G){(0,C.S6)(G,function(Z,H){if(!(!Z||Z.name==null))for(var X=0;X<N.length;X++){var ut=N[X].existing;if(!N[X].newOption&&ut&&(ut.id==null||Z.id==null)&&!p(Z)&&!p(ut)&&M("name",ut,Z)){N[X].newOption=Z,G[H]=null;return}}})}function h(N,G,Z){(0,C.S6)(G,function(H){if(!!H){for(var X,ut=0;(X=N[ut])&&(X.newOption||p(X.existing)||X.existing&&H.id!=null&&!M("id",H,X.existing));)ut++;X?(X.newOption=H,X.brandNew=Z):N.push({newOption:H,brandNew:Z,existing:null,keyInfo:null}),ut++}})}function c(N,G){(0,C.S6)(G,function(Z){N.push({newOption:Z,brandNew:!0,existing:null,keyInfo:null})})}function _(N){var G=(0,C.kW)();(0,C.S6)(N,function(Z){var H=Z.existing;H&&G.set(H.id,Z)}),(0,C.S6)(N,function(Z){var H=Z.newOption;(0,C.hu)(!H||H.id==null||!G.get(H.id)||G.get(H.id)===Z,"id duplicates: "+(H&&H.id)),H&&H.id!=null&&G.set(H.id,Z),!Z.keyInfo&&(Z.keyInfo={})}),(0,C.S6)(N,function(Z,H){var X=Z.existing,ut=Z.newOption,lt=Z.keyInfo;if(!!(0,C.Kn)(ut)){if(lt.name=ut.name!=null?l(ut.name):X?X.name:z+H,X)lt.id=l(X.id);else if(ut.id!=null)lt.id=l(ut.id);else{var ot=0;do lt.id="\0"+lt.name+"\0"+ot++;while(G.get(lt.id))}G.set(lt.id,Z)}})}function M(N,G,Z){var H=O(G[N],null),X=O(Z[N],null);return H!=null&&X!=null&&H===X}function l(N){return O(N,"")}function O(N,G){return N==null?G:(0,C.HD)(N)?N:(0,C.hj)(N)||(0,C.cd)(N)?N+"":G}function d(N){}function f(N){return isStringSafe(N)||isNumeric(N)}function u(N){var G=N.name;return!!(G&&G.indexOf(z))}function p(N){return N&&N.id!=null&&l(N.id).indexOf(W)===0}function n(N){return W+N}function i(N,G,Z){(0,C.S6)(N,function(H){var X=H.newOption;(0,C.Kn)(X)&&(H.keyInfo.mainType=G,H.keyInfo.subType=t(G,X,H.existing,Z))})}function t(N,G,Z,H){var X=G.type?G.type:Z?Z.subType:H.determineSubType(N,G);return X}function r(N,G){var Z={},H={};return X(N||[],Z),X(G||[],H,Z),[ut(Z),ut(H)];function X(lt,ot,q){for(var tt=0,j=lt.length;tt<j;tt++){var et=O(lt[tt].seriesId,null);if(et==null)return;for(var ct=K(lt[tt].dataIndex),_t=q&&q[et],St=0,At=ct.length;St<At;St++){var Ct=ct[St];_t&&_t[Ct]?_t[Ct]=null:(ot[et]||(ot[et]={}))[Ct]=1}}}function ut(lt,ot){var q=[];for(var tt in lt)if(lt.hasOwnProperty(tt)&&lt[tt]!=null)if(ot)q.push(+tt);else{var j=ut(lt[tt],!0);j.length&&q.push({seriesId:tt,dataIndex:j})}return q}}function e(N,G){if(G.dataIndexInside!=null)return G.dataIndexInside;if(G.dataIndex!=null)return(0,C.kJ)(G.dataIndex)?(0,C.UI)(G.dataIndex,function(Z){return N.indexOfRawIndex(Z)}):N.indexOfRawIndex(G.dataIndex);if(G.name!=null)return(0,C.kJ)(G.name)?(0,C.UI)(G.name,function(Z){return N.indexOfName(Z)}):N.indexOfName(G.name)}function a(){var N="__ec_inner_"+s++;return function(G){return G[N]||(G[N]={})}}var s=(0,v.jj)();function o(N,G,Z){var H=D(G,Z),X=H.mainTypeSpecified,ut=H.queryOptionMap,lt=H.others,ot=lt,q=Z?Z.defaultMainType:null;return!X&&q&&ut.set(q,{}),ut.each(function(tt,j){var et=L(N,j,tt,{useDefault:q===j,enableAll:Z&&Z.enableAll!=null?Z.enableAll:!0,enableNone:Z&&Z.enableNone!=null?Z.enableNone:!0});ot[j+"Models"]=et.models,ot[j+"Model"]=et.models[0]}),ot}function D(N,G){var Z;if((0,C.HD)(N)){var H={};H[N+"Index"]=0,Z=H}else Z=N;var X=(0,C.kW)(),ut={},lt=!1;return(0,C.S6)(Z,function(ot,q){if(q==="dataIndex"||q==="dataIndexInside"){ut[q]=ot;return}var tt=q.match(/^(\w+)(Index|Id|Name)$/)||[],j=tt[1],et=(tt[2]||"").toLowerCase();if(!(!j||!et||G&&G.includeMainTypes&&(0,C.cq)(G.includeMainTypes,j)<0)){lt=lt||!!j;var ct=X.get(j)||X.set(j,{});ct[et]=ot}}),{mainTypeSpecified:lt,queryOptionMap:X,others:ut}}var B={useDefault:!0,enableAll:!1,enableNone:!1},I={useDefault:!1,enableAll:!0,enableNone:!0};function L(N,G,Z,H){H=H||B;var X=Z.index,ut=Z.id,lt=Z.name,ot={models:null,specified:X!=null||ut!=null||lt!=null};if(!ot.specified){var q=void 0;return ot.models=H.useDefault&&(q=N.getComponent(G))?[q]:[],ot}return X==="none"||X===!1?((0,C.hu)(H.enableNone,'`"none"` or `false` is not a valid value on index option.'),ot.models=[],ot):(X==="all"&&((0,C.hu)(H.enableAll,'`"all"` is not a valid value on index option.'),X=ut=lt=null),ot.models=N.queryComponents({mainType:G,index:X,id:ut,name:lt}),ot)}function U(N,G,Z){N.setAttribute?N.setAttribute(G,Z):N[G]=Z}function x(N,G){return N.getAttribute?N.getAttribute(G):N[G]}function T(N){return N==="auto"?k.Z.domSupported?"html":"richText":N||"html"}function w(N,G){var Z=createHashMap(),H=[];return each(N,function(X){var ut=G(X);(Z.get(ut)||(H.push(ut),Z.set(ut,[]))).push(X)}),{keys:H,buckets:Z}}function V(N,G,Z,H,X){var ut=G==null||G==="auto";if(H==null)return H;if((0,C.hj)(H)){var lt=F(Z||0,H,X);return(0,v.NM)(lt,ut?Math.max((0,v.p8)(Z||0),(0,v.p8)(H)):G)}else{if((0,C.HD)(H))return X<1?Z:H;for(var ot=[],q=Z,tt=H,j=Math.max(q?q.length:0,tt.length),et=0;et<j;++et){var ct=N.getDimensionInfo(et);if(ct&&ct.type==="ordinal")ot[et]=(X<1&&q?q:tt)[et];else{var _t=q&&q[et]?q[et]:0,St=tt[et],lt=F(_t,St,X);ot[et]=(0,v.NM)(lt,ut?Math.max((0,v.p8)(_t),(0,v.p8)(St)):G)}}return ot}}},85669:function(Zt,pt,P){"use strict";P.d(pt,{NU:function(){return z},GM:function(){return W},NM:function(){return K},dt:function(){return R},p8:function(){return b},M9:function(){return S},HD:function(){return g},S$:function(){return y},wW:function(){return h},mW:function(){return c},sG:function(){return M},Xd:function(){return l},xW:function(){return O},kx:function(){return d},nR:function(){return u},FK:function(){return p},kE:function(){return n},jj:function(){return i},nl:function(){return r}});var C=P(33051),k=1e-4,v=20;function F(e){return e.replace(/^\s+|\s+$/g,"")}function z(e,a,s,o){var D=a[0],B=a[1],I=s[0],L=s[1],U=B-D,x=L-I;if(U===0)return x===0?I:(I+L)/2;if(o)if(U>0){if(e<=D)return I;if(e>=B)return L}else{if(e>=D)return I;if(e<=B)return L}else{if(e===D)return I;if(e===B)return L}return(e-D)/U*x+I}function W(e,a){switch(e){case"center":case"middle":e="50%";break;case"left":case"top":e="0%";break;case"right":case"bottom":e="100%";break}return C.HD(e)?F(e).match(/%$/)?parseFloat(e)/100*a:parseFloat(e):e==null?NaN:+e}function K(e,a,s){return a==null&&(a=10),a=Math.min(Math.max(0,a),v),e=(+e).toFixed(a),s?e:+e}function R(e){return e.sort(function(a,s){return a-s}),e}function b(e){if(e=+e,isNaN(e))return 0;if(e>1e-14){for(var a=1,s=0;s<15;s++,a*=10)if(Math.round(e*a)/a===e)return s}return A(e)}function A(e){var a=e.toString().toLowerCase(),s=a.indexOf("e"),o=s>0?+a.slice(s+1):0,D=s>0?s:a.length,B=a.indexOf("."),I=B<0?0:D-1-B;return Math.max(0,I-o)}function S(e,a){var s=Math.log,o=Math.LN10,D=Math.floor(s(e[1]-e[0])/o),B=Math.round(s(Math.abs(a[1]-a[0]))/o),I=Math.min(Math.max(-D+B,0),20);return isFinite(I)?I:20}function m(e,a,s){if(!e[a])return 0;var o=g(e,s);return o[a]||0}function g(e,a){var s=C.u4(e,function(N,G){return N+(isNaN(G)?0:G)},0);if(s===0)return[];for(var o=Math.pow(10,a),D=C.UI(e,function(N){return(isNaN(N)?0:N)/s*o*100}),B=o*100,I=C.UI(D,function(N){return Math.floor(N)}),L=C.u4(I,function(N,G){return N+G},0),U=C.UI(D,function(N,G){return N-I[G]});L<B;){for(var x=Number.NEGATIVE_INFINITY,T=null,w=0,V=U.length;w<V;++w)U[w]>x&&(x=U[w],T=w);++I[T],U[T]=0,++L}return C.UI(I,function(N){return N/o})}function y(e,a){var s=Math.max(b(e),b(a)),o=e+a;return s>v?o:K(o,s)}var E=9007199254740991;function h(e){var a=Math.PI*2;return(e%a+a)%a}function c(e){return e>-k&&e<k}var _=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function M(e){if(e instanceof Date)return e;if(C.HD(e)){var a=_.exec(e);if(!a)return new Date(NaN);if(a[8]){var s=+a[4]||0;return a[8].toUpperCase()!=="Z"&&(s-=+a[8].slice(0,3)),new Date(Date.UTC(+a[1],+(a[2]||1)-1,+a[3]||1,s,+(a[5]||0),+a[6]||0,a[7]?+a[7].substring(0,3):0))}else return new Date(+a[1],+(a[2]||1)-1,+a[3]||1,+a[4]||0,+(a[5]||0),+a[6]||0,a[7]?+a[7].substring(0,3):0)}else if(e==null)return new Date(NaN);return new Date(Math.round(e))}function l(e){return Math.pow(10,O(e))}function O(e){if(e===0)return 0;var a=Math.floor(Math.log(e)/Math.LN10);return e/Math.pow(10,a)>=10&&a++,a}function d(e,a){var s=O(e),o=Math.pow(10,s),D=e/o,B;return a?D<1.5?B=1:D<2.5?B=2:D<4?B=3:D<7?B=5:B=10:D<1?B=1:D<2?B=2:D<3?B=3:D<5?B=5:B=10,e=B*o,s>=-20?+e.toFixed(s<0?-s:0):e}function f(e,a){var s=(e.length-1)*a+1,o=Math.floor(s),D=+e[o-1],B=s-o;return B?D+B*(e[o]-D):D}function u(e){e.sort(function(U,x){return L(U,x,0)?-1:1});for(var a=-Infinity,s=1,o=0;o<e.length;){for(var D=e[o].interval,B=e[o].close,I=0;I<2;I++)D[I]<=a&&(D[I]=a,B[I]=I?1:1-s),a=D[I],s=B[I];D[0]===D[1]&&B[0]*B[1]!=1?e.splice(o,1):o++}return e;function L(U,x,T){return U.interval[T]<x.interval[T]||U.interval[T]===x.interval[T]&&(U.close[T]-x.close[T]==(T?-1:1)||!T&&L(U,x,1))}}function p(e){var a=parseFloat(e);return a==e&&(a!==0||!C.HD(e)||e.indexOf("x")<=0)?a:NaN}function n(e){return!isNaN(p(e))}function i(){return Math.round(Math.random()*9)}function t(e,a){return a===0?e:t(a,e%a)}function r(e,a){return e==null?a:a==null?e:e*a/t(e,a)}},26357:function(Zt,pt,P){"use strict";P.d(pt,{CX:function(){return S},wU:function(){return m},L1:function(){return g},qc:function(){return y},Ki:function(){return c},yx:function(){return _},Hg:function(){return M},JQ:function(){return l},iK:function(){return O},Gl:function(){return s},fD:function(){return V},Mh:function(){return N},SX:function(){return G},VP:function(){return Z},XX:function(){return H},SJ:function(){return X},T5:function(){return lt},zI:function(){return q},UL:function(){return tt},oJ:function(){return j},$l:function(){return et},xr:function(){return ct},og:function(){return _t},ci:function(){return St},C5:function(){return At},vF:function(){return Ct},k5:function(){return Ot},WO:function(){return Ht},Nj:function(){return kt},Av:function(){return Xt},RW:function(){return st},aG:function(){return gt},xp:function(){return xt},e9:function(){return Rt}});var C=P(33051),k=P(30106),v=P(21092),F=P(32234),z=P(4665),W=1,K={},R=(0,F.Yf)(),b=(0,F.Yf)(),A=0,S=1,m=2,g=["emphasis","blur","select"],y=["normal","emphasis","blur","select"],E=10,h=9,c="highlight",_="downplay",M="select",l="unselect",O="toggleSelect";function d(dt){return dt!=null&&dt!=="none"}function f(dt,Mt,vt){dt.onHoverStateChange&&(dt.hoverState||0)!==vt&&dt.onHoverStateChange(Mt),dt.hoverState=vt}function u(dt){f(dt,"emphasis",m)}function p(dt){dt.hoverState===m&&f(dt,"normal",A)}function n(dt){f(dt,"blur",S)}function i(dt){dt.hoverState===S&&f(dt,"normal",A)}function t(dt){dt.selected=!0}function r(dt){dt.selected=!1}function e(dt,Mt,vt){Mt(dt,vt)}function a(dt,Mt,vt){e(dt,Mt,vt),dt.isGroup&&dt.traverse(function(Y){e(Y,Mt,vt)})}function s(dt,Mt){switch(Mt){case"emphasis":dt.hoverState=m;break;case"normal":dt.hoverState=A;break;case"blur":dt.hoverState=S;break;case"select":dt.selected=!0}}function o(dt){dt.isGroup?dt.traverse(function(Mt){Mt.clearStates()}):dt.clearStates()}function D(dt,Mt,vt,Y){for(var $=dt.style,Q={},ft=0;ft<Mt.length;ft++){var Tt=Mt[ft],wt=$[Tt];Q[Tt]=wt==null?Y&&Y[Tt]:wt}for(var ft=0;ft<dt.animators.length;ft++){var bt=dt.animators[ft];bt.__fromStateTransition&&bt.__fromStateTransition.indexOf(vt)<0&&bt.targetName==="style"&&bt.saveTo(Q,Mt)}return Q}function B(dt,Mt,vt,Y){var $=vt&&(0,C.cq)(vt,"select")>=0,Q=!1;if(dt instanceof z.ZP){var ft=R(dt),Tt=$&&ft.selectFill||ft.normalFill,wt=$&&ft.selectStroke||ft.normalStroke;if(d(Tt)||d(wt)){Y=Y||{};var bt=Y.style||{};bt.fill==="inherit"?(Q=!0,Y=(0,C.l7)({},Y),bt=(0,C.l7)({},bt),bt.fill=Tt):!d(bt.fill)&&d(Tt)?(Q=!0,Y=(0,C.l7)({},Y),bt=(0,C.l7)({},bt),bt.fill=(0,v.fD)(Tt)):!d(bt.stroke)&&d(wt)&&(Q||(Y=(0,C.l7)({},Y),bt=(0,C.l7)({},bt)),bt.stroke=(0,v.fD)(wt)),Y.style=bt}}if(Y&&Y.z2==null){Q||(Y=(0,C.l7)({},Y));var zt=dt.z2EmphasisLift;Y.z2=dt.z2+(zt!=null?zt:E)}return Y}function I(dt,Mt,vt){if(vt&&vt.z2==null){vt=(0,C.l7)({},vt);var Y=dt.z2SelectLift;vt.z2=dt.z2+(Y!=null?Y:h)}return vt}function L(dt,Mt,vt){var Y=(0,C.cq)(dt.currentStates,Mt)>=0,$=dt.style.opacity,Q=Y?null:D(dt,["opacity"],Mt,{opacity:1});vt=vt||{};var ft=vt.style||{};return ft.opacity==null&&(vt=(0,C.l7)({},vt),ft=(0,C.l7)({opacity:Y?$:Q.opacity*.1},ft),vt.style=ft),vt}function U(dt,Mt){var vt=this.states[dt];if(this.style){if(dt==="emphasis")return B(this,dt,Mt,vt);if(dt==="blur")return L(this,dt,vt);if(dt==="select")return I(this,dt,vt)}return vt}function x(dt){dt.stateProxy=U;var Mt=dt.getTextContent(),vt=dt.getTextGuideLine();Mt&&(Mt.stateProxy=U),vt&&(vt.stateProxy=U)}function T(dt,Mt){!ut(dt,Mt)&&!dt.__highByOuter&&a(dt,u)}function w(dt,Mt){!ut(dt,Mt)&&!dt.__highByOuter&&a(dt,p)}function V(dt,Mt){dt.__highByOuter|=1<<(Mt||0),a(dt,u)}function N(dt,Mt){!(dt.__highByOuter&=~(1<<(Mt||0)))&&a(dt,p)}function G(dt){a(dt,n)}function Z(dt){a(dt,i)}function H(dt){a(dt,t)}function X(dt){a(dt,r)}function ut(dt,Mt){return dt.__highDownSilentOnTouch&&Mt.zrByTouch}function lt(dt){var Mt=dt.getModel(),vt=[],Y=[];Mt.eachComponent(function($,Q){var ft=b(Q),Tt=$==="series",wt=Tt?dt.getViewOfSeriesModel(Q):dt.getViewOfComponentModel(Q);!Tt&&Y.push(wt),ft.isBlured&&(wt.group.traverse(function(bt){i(bt)}),Tt&&vt.push(Q)),ft.isBlured=!1}),(0,C.S6)(Y,function($){$&&$.toggleBlurSeries&&$.toggleBlurSeries(vt,!1,Mt)})}function ot(dt,Mt,vt,Y){var $=Y.getModel();vt=vt||"coordinateSystem";function Q(bt,zt){for(var Qt=0;Qt<zt.length;Qt++){var qt=bt.getItemGraphicEl(zt[Qt]);qt&&Z(qt)}}if(dt!=null&&!(!Mt||Mt==="none")){var ft=$.getSeriesByIndex(dt),Tt=ft.coordinateSystem;Tt&&Tt.master&&(Tt=Tt.master);var wt=[];$.eachSeries(function(bt){var zt=ft===bt,Qt=bt.coordinateSystem;Qt&&Qt.master&&(Qt=Qt.master);var qt=Qt&&Tt?Qt===Tt:zt;if(!(vt==="series"&&!zt||vt==="coordinateSystem"&&!qt||Mt==="series"&&zt)){var re=Y.getViewOfSeriesModel(bt);if(re.group.traverse(function(ue){ue.__highByOuter&&zt&&Mt==="self"||n(ue)}),(0,C.zG)(Mt))Q(bt.getData(),Mt);else if((0,C.Kn)(Mt))for(var ie=(0,C.XP)(Mt),oe=0;oe<ie.length;oe++)Q(bt.getData(ie[oe]),Mt[ie[oe]]);wt.push(bt),b(bt).isBlured=!0}}),$.eachComponent(function(bt,zt){if(bt!=="series"){var Qt=Y.getViewOfComponentModel(zt);Qt&&Qt.toggleBlurSeries&&Qt.toggleBlurSeries(wt,!0,$)}})}}function q(dt,Mt,vt){if(!(dt==null||Mt==null)){var Y=vt.getModel().getComponent(dt,Mt);if(!!Y){b(Y).isBlured=!0;var $=vt.getViewOfComponentModel(Y);!$||!$.focusBlurEnabled||$.group.traverse(function(Q){n(Q)})}}}function tt(dt,Mt,vt){var Y=dt.seriesIndex,$=dt.getData(Mt.dataType);if(!!$){var Q=(0,F.gO)($,Mt);Q=((0,C.kJ)(Q)?Q[0]:Q)||0;var ft=$.getItemGraphicEl(Q);if(!ft)for(var Tt=$.count(),wt=0;!ft&&wt<Tt;)ft=$.getItemGraphicEl(wt++);if(ft){var bt=(0,k.A)(ft);ot(Y,bt.focus,bt.blurScope,vt)}else{var zt=dt.get(["emphasis","focus"]),Qt=dt.get(["emphasis","blurScope"]);zt!=null&&ot(Y,zt,Qt,vt)}}}function j(dt,Mt,vt,Y){var $={focusSelf:!1,dispatchers:null};if(dt==null||dt==="series"||Mt==null||vt==null)return $;var Q=Y.getModel().getComponent(dt,Mt);if(!Q)return $;var ft=Y.getViewOfComponentModel(Q);if(!ft||!ft.findHighDownDispatchers)return $;for(var Tt=ft.findHighDownDispatchers(vt),wt,bt=0;bt<Tt.length;bt++)if((0,k.A)(Tt[bt]).focus==="self"){wt=!0;break}return{focusSelf:wt,dispatchers:Tt}}function et(dt,Mt,vt){var Y=(0,k.A)(dt),$=j(Y.componentMainType,Y.componentIndex,Y.componentHighDownName,vt),Q=$.dispatchers,ft=$.focusSelf;Q?(ft&&q(Y.componentMainType,Y.componentIndex,vt),(0,C.S6)(Q,function(Tt){return T(Tt,Mt)})):(ot(Y.seriesIndex,Y.focus,Y.blurScope,vt),Y.focus==="self"&&q(Y.componentMainType,Y.componentIndex,vt),T(dt,Mt))}function ct(dt,Mt,vt){lt(vt);var Y=(0,k.A)(dt),$=j(Y.componentMainType,Y.componentIndex,Y.componentHighDownName,vt).dispatchers;$?(0,C.S6)($,function(Q){return w(Q,Mt)}):w(dt,Mt)}function _t(dt,Mt,vt){if(!!gt(Mt)){var Y=Mt.dataType,$=dt.getData(Y),Q=(0,F.gO)($,Mt);(0,C.kJ)(Q)||(Q=[Q]),dt[Mt.type===O?"toggleSelect":Mt.type===M?"select":"unselect"](Q,Y)}}function St(dt){var Mt=dt.getAllData();(0,C.S6)(Mt,function(vt){var Y=vt.data,$=vt.type;Y.eachItemGraphicEl(function(Q,ft){dt.isSelected(ft,$)?H(Q):X(Q)})})}function At(dt){var Mt=[];return dt.eachSeries(function(vt){var Y=vt.getAllData();(0,C.S6)(Y,function($){var Q=$.data,ft=$.type,Tt=vt.getSelectedDataIndices();if(Tt.length>0){var wt={dataIndex:Tt,seriesIndex:vt.seriesIndex};ft!=null&&(wt.dataType=ft),Mt.push(wt)}})}),Mt}function Ct(dt,Mt,vt){kt(dt,!0),a(dt,x),Wt(dt,Mt,vt)}function Et(dt){kt(dt,!1)}function Ot(dt,Mt,vt,Y){Y?Et(dt):Ct(dt,Mt,vt)}function Wt(dt,Mt,vt){var Y=(0,k.A)(dt);Mt!=null?(Y.focus=Mt,Y.blurScope=vt):Y.focus&&(Y.focus=null)}var Ut=["emphasis","blur","select"],Bt={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Ht(dt,Mt,vt,Y){vt=vt||"itemStyle";for(var $=0;$<Ut.length;$++){var Q=Ut[$],ft=Mt.getModel([Q,vt]),Tt=dt.ensureState(Q);Tt.style=Y?Y(ft):ft[Bt[vt]]()}}function kt(dt,Mt){var vt=Mt===!1,Y=dt;dt.highDownSilentOnTouch&&(Y.__highDownSilentOnTouch=dt.highDownSilentOnTouch),(!vt||Y.__highDownDispatcher)&&(Y.__highByOuter=Y.__highByOuter||0,Y.__highDownDispatcher=!vt)}function Xt(dt){return!!(dt&&dt.__highDownDispatcher)}function Vt(dt,Mt,vt){var Y=getECData(dt);Y.componentMainType=Mt.mainType,Y.componentIndex=Mt.componentIndex,Y.componentHighDownName=vt}function st(dt){var Mt=K[dt];return Mt==null&&W<=32&&(Mt=K[dt]=W++),Mt}function gt(dt){var Mt=dt.type;return Mt===M||Mt===l||Mt===O}function xt(dt){var Mt=dt.type;return Mt===c||Mt===_}function Rt(dt){var Mt=R(dt);Mt.normalFill=dt.style.fill,Mt.normalStroke=dt.style.stroke;var vt=dt.states.select||{};Mt.selectFill=vt.style&&vt.style.fill||null,Mt.selectStroke=vt.style&&vt.style.stroke||null}},41525:function(Zt,pt,P){"use strict";P.d(pt,{Pw:function(){return h},th:function(){return M},zp:function(){return l},Cq:function(){return O}});var C=P(33051),k=P(4665),v=P(22095),F=P(35151),z=P(69538),W=P(50453),K=P(60479),R=P(80423),b=P(85669),A=k.ZP.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(d,f){var u=f.cx,p=f.cy,n=f.width/2,i=f.height/2;d.moveTo(u,p-i),d.lineTo(u+n,p+i),d.lineTo(u-n,p+i),d.closePath()}}),S=k.ZP.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(d,f){var u=f.cx,p=f.cy,n=f.width/2,i=f.height/2;d.moveTo(u,p-i),d.lineTo(u+n,p),d.lineTo(u,p+i),d.lineTo(u-n,p),d.closePath()}}),m=k.ZP.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(d,f){var u=f.x,p=f.y,n=f.width/5*3,i=Math.max(n,f.height),t=n/2,r=t*t/(i-t),e=p-i+t+r,a=Math.asin(r/t),s=Math.cos(a)*t,o=Math.sin(a),D=Math.cos(a),B=t*.6,I=t*.7;d.moveTo(u-s,e+r),d.arc(u,e,t,Math.PI-a,Math.PI*2+a),d.bezierCurveTo(u+s-o*B,e+r+D*B,u,p-I,u,p),d.bezierCurveTo(u,p-I,u-s+o*B,e+r+D*B,u-s,e+r),d.closePath()}}),g=k.ZP.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(d,f){var u=f.height,p=f.width,n=f.x,i=f.y,t=p/3*2;d.moveTo(n,i),d.lineTo(n+t,i+u),d.lineTo(n,i+u/4*3),d.lineTo(n-t,i+u),d.lineTo(n,i),d.closePath()}}),y={line:v.Z,rect:F.Z,roundRect:F.Z,square:F.Z,circle:z.Z,diamond:S,pin:m,arrow:g,triangle:A},E={line:function(d,f,u,p,n){n.x1=d,n.y1=f+p/2,n.x2=d+u,n.y2=f+p/2},rect:function(d,f,u,p,n){n.x=d,n.y=f,n.width=u,n.height=p},roundRect:function(d,f,u,p,n){n.x=d,n.y=f,n.width=u,n.height=p,n.r=Math.min(u,p)/4},square:function(d,f,u,p,n){var i=Math.min(u,p);n.x=d,n.y=f,n.width=i,n.height=i},circle:function(d,f,u,p,n){n.cx=d+u/2,n.cy=f+p/2,n.r=Math.min(u,p)/2},diamond:function(d,f,u,p,n){n.cx=d+u/2,n.cy=f+p/2,n.width=u,n.height=p},pin:function(d,f,u,p,n){n.x=d+u/2,n.y=f+p/2,n.width=u,n.height=p},arrow:function(d,f,u,p,n){n.x=d+u/2,n.y=f+p/2,n.width=u,n.height=p},triangle:function(d,f,u,p,n){n.cx=d+u/2,n.cy=f+p/2,n.width=u,n.height=p}},h={};(0,C.S6)(y,function(d,f){h[f]=new d});var c=k.ZP.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(d,f,u){var p=(0,R.wI)(d,f,u),n=this.shape;return n&&n.symbolType==="pin"&&f.position==="inside"&&(p.y=u.y+u.height*.4),p},buildPath:function(d,f,u){var p=f.symbolType;if(p!=="none"){var n=h[p];n||(p="rect",n=h[p]),E[p](f.x,f.y,f.width,f.height,n.shape),n.buildPath(d,n.shape,u)}}});function _(d,f){if(this.type!=="image"){var u=this.style;this.__isEmptyBrush?(u.stroke=d,u.fill=f||"#fff",u.lineWidth=2):this.shape.symbolType==="line"?u.stroke=d:u.fill=d,this.markRedraw()}}function M(d,f,u,p,n,i,t){var r=d.indexOf("empty")===0;r&&(d=d.substr(5,1).toLowerCase()+d.substr(6));var e;return d.indexOf("image://")===0?e=W.makeImage(d.slice(8),new K.Z(f,u,p,n),t?"center":"cover"):d.indexOf("path://")===0?e=W.makePath(d.slice(7),{},new K.Z(f,u,p,n),t?"center":"cover"):e=new c({shape:{symbolType:d,x:f,y:u,width:p,height:n}}),e.__isEmptyBrush=r,e.setColor=_,i&&e.setColor(i),e}function l(d){return(0,C.kJ)(d)||(d=[+d,+d]),[d[0]||0,d[1]||0]}function O(d,f){if(d!=null)return(0,C.kJ)(d)||(d=[d,d]),[(0,b.GM)(d[0],f[0])||0,(0,b.GM)((0,C.pD)(d[1],d[0]),f[1])||0]}},270:function(Zt,pt,P){"use strict";P.d(pt,{P2:function(){return F},T9:function(){return z},ZH:function(){return W}});var C="\0__throttleOriginMethod",k="\0__throttleRate",v="\0__throttleType";function F(K,R,b){var A,S=0,m=0,g=null,y,E,h,c;R=R||0;function _(){m=new Date().getTime(),g=null,K.apply(E,h||[])}var M=function(){for(var l=[],O=0;O<arguments.length;O++)l[O]=arguments[O];A=new Date().getTime(),E=this,h=l;var d=c||R,f=c||b;c=null,y=A-(f?S:m)-d,clearTimeout(g),f?g=setTimeout(_,d):y>=0?_():g=setTimeout(_,-y),S=A};return M.clear=function(){g&&(clearTimeout(g),g=null)},M.debounceNextCall=function(l){c=l},M}function z(K,R,b,A){var S=K[R];if(!!S){var m=S[C]||S,g=S[v],y=S[k];if(y!==b||g!==A){if(b==null||!A)return K[R]=m;S=K[R]=F(m,b,A==="debounce"),S[C]=m,S[v]=A,S[k]=b}return S}}function W(K,R){var b=K[R];b&&b[C]&&(b.clear&&b.clear(),K[R]=b[C])}},15015:function(Zt,pt,P){"use strict";P.d(pt,{WT:function(){return z},yR:function(){return W},dV:function(){return K},s2:function(){return R},P5:function(){return b},V8:function(){return m},FW:function(){return y},Tj:function(){return h},$K:function(){return c},xC:function(){return _},WU:function(){return M},k7:function(){return l},q5:function(){return d},sx:function(){return f},CW:function(){return u},xz:function(){return p},Wp:function(){return n},fn:function(){return i},MV:function(){return t},RZ:function(){return r},xL:function(){return e},vh:function(){return a},f5:function(){return s},En:function(){return o},eN:function(){return D},rM:function(){return B},cb:function(){return I}});var C=P(33051),k=P(85669),v=P(73917),F=P(1497),z=1e3,W=z*60,K=W*60,R=K*24,b=R*365,A={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},S="{yyyy}-{MM}-{dd}",m={year:"{yyyy}",month:"{yyyy}-{MM}",day:S,hour:S+" "+A.hour,minute:S+" "+A.minute,second:S+" "+A.second,millisecond:A.none},g=["year","month","day","hour","minute","second","millisecond"],y=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function E(L,U){return L+="","0000".substr(0,U-L.length)+L}function h(L){switch(L){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return L}}function c(L){return L===h(L)}function _(L){switch(L){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function M(L,U,x,T){var w=k.sG(L),V=w[f(x)](),N=w[u(x)]()+1,G=Math.floor((N-1)/3)+1,Z=w[p(x)](),H=w["get"+(x?"UTC":"")+"Day"](),X=w[n(x)](),ut=(X-1)%12+1,lt=w[i(x)](),ot=w[t(x)](),q=w[r(x)](),tt=T instanceof F.Z?T:(0,v.G8)(T||v.sO)||(0,v.Li)(),j=tt.getModel("time"),et=j.get("month"),ct=j.get("monthAbbr"),_t=j.get("dayOfWeek"),St=j.get("dayOfWeekAbbr");return(U||"").replace(/{yyyy}/g,V+"").replace(/{yy}/g,E(V%100+"",2)).replace(/{Q}/g,G+"").replace(/{MMMM}/g,et[N-1]).replace(/{MMM}/g,ct[N-1]).replace(/{MM}/g,E(N,2)).replace(/{M}/g,N+"").replace(/{dd}/g,E(Z,2)).replace(/{d}/g,Z+"").replace(/{eeee}/g,_t[H]).replace(/{ee}/g,St[H]).replace(/{e}/g,H+"").replace(/{HH}/g,E(X,2)).replace(/{H}/g,X+"").replace(/{hh}/g,E(ut+"",2)).replace(/{h}/g,ut+"").replace(/{mm}/g,E(lt,2)).replace(/{m}/g,lt+"").replace(/{ss}/g,E(ot,2)).replace(/{s}/g,ot+"").replace(/{SSS}/g,E(q,3)).replace(/{S}/g,q+"")}function l(L,U,x,T,w){var V=null;if(C.HD(x))V=x;else if(C.mf(x))V=x(L.value,U,{level:L.level});else{var N=C.l7({},A);if(L.level>0)for(var G=0;G<g.length;++G)N[g[G]]="{primary|"+N[g[G]]+"}";var Z=x?x.inherit===!1?x:C.ce(x,N):N,H=O(L.value,w);if(Z[H])V=Z[H];else if(Z.inherit){for(var X=y.indexOf(H),G=X-1;G>=0;--G)if(Z[H]){V=Z[H];break}V=V||N.none}if(C.kJ(V)){var ut=L.level==null?0:L.level>=0?L.level:V.length+L.level;ut=Math.min(ut,V.length-1),V=V[ut]}}return M(new Date(L.value),V,w,T)}function O(L,U){var x=k.sG(L),T=x[u(U)]()+1,w=x[p(U)](),V=x[n(U)](),N=x[i(U)](),G=x[t(U)](),Z=x[r(U)](),H=Z===0,X=H&&G===0,ut=X&&N===0,lt=ut&&V===0,ot=lt&&w===1,q=ot&&T===1;return q?"year":ot?"month":lt?"day":ut?"hour":X?"minute":H?"second":"millisecond"}function d(L,U,x){var T=C.hj(L)?k.sG(L):L;switch(U=U||O(L,x),U){case"year":return T[f(x)]();case"half-year":return T[u(x)]()>=6?1:0;case"quarter":return Math.floor((T[u(x)]()+1)/4);case"month":return T[u(x)]();case"day":return T[p(x)]();case"half-day":return T[n(x)]()/24;case"hour":return T[n(x)]();case"minute":return T[i(x)]();case"second":return T[t(x)]();case"millisecond":return T[r(x)]()}}function f(L){return L?"getUTCFullYear":"getFullYear"}function u(L){return L?"getUTCMonth":"getMonth"}function p(L){return L?"getUTCDate":"getDate"}function n(L){return L?"getUTCHours":"getHours"}function i(L){return L?"getUTCMinutes":"getMinutes"}function t(L){return L?"getUTCSeconds":"getSeconds"}function r(L){return L?"getUTCMilliseconds":"getMilliseconds"}function e(L){return L?"setUTCFullYear":"setFullYear"}function a(L){return L?"setUTCMonth":"setMonth"}function s(L){return L?"setUTCDate":"setDate"}function o(L){return L?"setUTCHours":"setHours"}function D(L){return L?"setUTCMinutes":"setMinutes"}function B(L){return L?"setUTCSeconds":"setSeconds"}function I(L){return L?"setUTCMilliseconds":"setMilliseconds"}},94279:function(Zt,pt,P){"use strict";P.d(pt,{f7:function(){return k},cy:function(){return v},XD:function(){return F},qb:function(){return z},hL:function(){return W},J5:function(){return K},RA:function(){return R},fY:function(){return b},Wc:function(){return A}});var C=P(33051),k=(0,C.kW)(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),v="original",F="arrayRows",z="objectRows",W="keyedColumns",K="typedArray",R="unknown",b="column",A="row"},80887:function(Zt,pt,P){"use strict";P.d(pt,{o:function(){return F}});var C=P(33051),k=typeof Float32Array!="undefined",v=k?Float32Array:Array;function F(z){return(0,C.kJ)(z)?k?new Float32Array(z):z:new v(z)}},75797:function(Zt,pt,P){"use strict";var C=P(33051),k=P(38154),v=P(42151),F=P(34251),z=P(32234),W=P(26357),K=P(8674),R=P(95682),b=P(50453),A=z.Yf(),S=(0,R.Z)(),m=function(){function _(){this.group=new k.Z,this.uid=v.Kr("viewChart"),this.renderTask=(0,K.v)({plan:E,reset:h}),this.renderTask.context={view:this}}return _.prototype.init=function(M,l){},_.prototype.render=function(M,l,O,d){},_.prototype.highlight=function(M,l,O,d){var f=M.getData(d&&d.dataType);!f||y(f,d,"emphasis")},_.prototype.downplay=function(M,l,O,d){var f=M.getData(d&&d.dataType);!f||y(f,d,"normal")},_.prototype.remove=function(M,l){this.group.removeAll()},_.prototype.dispose=function(M,l){},_.prototype.updateView=function(M,l,O,d){this.render(M,l,O,d)},_.prototype.updateLayout=function(M,l,O,d){this.render(M,l,O,d)},_.prototype.updateVisual=function(M,l,O,d){this.render(M,l,O,d)},_.prototype.eachRendered=function(M){(0,b.traverseElements)(this.group,M)},_.markUpdateMethod=function(M,l){A(M).updateMethod=l},_.protoInitialize=function(){var M=_.prototype;M.type="chart"}(),_}();function g(_,M,l){_&&(0,W.Av)(_)&&(M==="emphasis"?W.fD:W.Mh)(_,l)}function y(_,M,l){var O=z.gO(_,M),d=M&&M.highlightKey!=null?(0,W.RW)(M.highlightKey):null;O!=null?(0,C.S6)(z.kF(O),function(f){g(_.getItemGraphicEl(f),l,d)}):_.eachItemGraphicEl(function(f){g(f,l,d)})}F.dm(m,["dispose"]),F.au(m);function E(_){return S(_.model)}function h(_){var M=_.model,l=_.ecModel,O=_.api,d=_.payload,f=M.pipelineContext.progressiveRender,u=_.view,p=d&&A(d).updateMethod,n=f?"incrementalPrepareRender":p&&u[p]?p:"render";return n!=="render"&&u[n](M,l,O,d),c[n]}var c={incrementalPrepareRender:{progress:function(_,M){M.view.incrementalRender(_,M.model,M.ecModel,M.api,M.payload)}},render:{forceFirstProgress:!0,progress:function(_,M){M.view.render(M.model,M.ecModel,M.api,M.payload)}}};pt.Z=m},33166:function(Zt,pt,P){"use strict";var C=P(38154),k=P(42151),v=P(34251),F=function(){function z(){this.group=new C.Z,this.uid=k.Kr("viewComponent")}return z.prototype.init=function(W,K){},z.prototype.render=function(W,K,R,b){},z.prototype.dispose=function(W,K){},z.prototype.updateView=function(W,K,R,b){},z.prototype.updateLayout=function(W,K,R,b){},z.prototype.updateVisual=function(W,K,R,b){},z.prototype.toggleBlurSeries=function(W,K,R){},z.prototype.eachRendered=function(W){var K=this.group;K&&K.traverse(W)},z}();v.dm(F),v.au(F),pt.Z=F},72019:function(Zt,pt){"use strict";var P=function(){function C(k,v){this._getDataWithEncodedVisual=k,this._getRawData=v}return C.prototype.getAllNames=function(){var k=this._getRawData();return k.mapArray(k.getName)},C.prototype.containName=function(k){var v=this._getRawData();return v.indexOfName(k)>=0},C.prototype.indexOfName=function(k){var v=this._getDataWithEncodedVisual();return v.indexOfName(k)},C.prototype.getItemVisual=function(k,v){var F=this._getDataWithEncodedVisual();return F.getItemVisual(k,v)},C}();pt.Z=P},26211:function(Zt,pt,P){"use strict";P.d(pt,{Or:function(){return C},UL:function(){return k},LZ:function(){return v}});function C(F,z,W){switch(W){case"color":var K=F.getItemVisual(z,"style");return K[F.getVisual("drawType")];case"opacity":return F.getItemVisual(z,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return F.getItemVisual(z,W);default:}}function k(F,z){switch(z){case"color":var W=F.getVisual("style");return W[F.getVisual("drawType")];case"opacity":return F.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return F.getVisual(z);default:}}function v(F,z,W,K){switch(W){case"color":var R=F.ensureUniqueItemVisual(z,"style");R[F.getVisual("drawType")]=K,F.setItemVisual(z,"colorFromPalette",!1);break;case"opacity":F.ensureUniqueItemVisual(z,"style").opacity=K;break;case"symbol":case"symbolSize":case"liftZ":F.setItemVisual(z,W,K);break;default:}}},18299:function(Zt,pt,P){"use strict";P.d(pt,{ZT:function(){return k}});/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var C=function(i,t){return C=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a])},C(i,t)};function k(i,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");C(i,t);function r(){this.constructor=i}i.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var v=function(){return v=Object.assign||function(t){for(var r,e=1,a=arguments.length;e<a;e++){r=arguments[e];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(t[s]=r[s])}return t},v.apply(this,arguments)};function F(i,t){var r={};for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&t.indexOf(e)<0&&(r[e]=i[e]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(i);a<e.length;a++)t.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(i,e[a])&&(r[e[a]]=i[e[a]]);return r}function z(i,t,r,e){var a=arguments.length,s=a<3?t:e===null?e=Object.getOwnPropertyDescriptor(t,r):e,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(i,t,r,e);else for(var D=i.length-1;D>=0;D--)(o=i[D])&&(s=(a<3?o(s):a>3?o(t,r,s):o(t,r))||s);return a>3&&s&&Object.defineProperty(t,r,s),s}function W(i,t){return function(r,e){t(r,e,i)}}function K(i,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(i,t)}function R(i,t,r,e){function a(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function D(L){try{I(e.next(L))}catch(U){o(U)}}function B(L){try{I(e.throw(L))}catch(U){o(U)}}function I(L){L.done?s(L.value):a(L.value).then(D,B)}I((e=e.apply(i,t||[])).next())})}function b(i,t){var r={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},e,a,s,o;return o={next:D(0),throw:D(1),return:D(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function D(I){return function(L){return B([I,L])}}function B(I){if(e)throw new TypeError("Generator is already executing.");for(;r;)try{if(e=1,a&&(s=I[0]&2?a.return:I[0]?a.throw||((s=a.return)&&s.call(a),0):a.next)&&!(s=s.call(a,I[1])).done)return s;switch(a=0,s&&(I=[I[0]&2,s.value]),I[0]){case 0:case 1:s=I;break;case 4:return r.label++,{value:I[1],done:!1};case 5:r.label++,a=I[1],I=[0];continue;case 7:I=r.ops.pop(),r.trys.pop();continue;default:if(s=r.trys,!(s=s.length>0&&s[s.length-1])&&(I[0]===6||I[0]===2)){r=0;continue}if(I[0]===3&&(!s||I[1]>s[0]&&I[1]<s[3])){r.label=I[1];break}if(I[0]===6&&r.label<s[1]){r.label=s[1],s=I;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(I);break}s[2]&&r.ops.pop(),r.trys.pop();continue}I=t.call(i,r)}catch(L){I=[6,L],a=0}finally{e=s=0}if(I[0]&5)throw I[1];return{value:I[0]?I[1]:void 0,done:!0}}}var A=Object.create?function(i,t,r,e){e===void 0&&(e=r),Object.defineProperty(i,e,{enumerable:!0,get:function(){return t[r]}})}:function(i,t,r,e){e===void 0&&(e=r),i[e]=t[r]};function S(i,t){for(var r in i)r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r)&&A(t,i,r)}function m(i){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&i[t],e=0;if(r)return r.call(i);if(i&&typeof i.length=="number")return{next:function(){return i&&e>=i.length&&(i=void 0),{value:i&&i[e++],done:!i}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function g(i,t){var r=typeof Symbol=="function"&&i[Symbol.iterator];if(!r)return i;var e=r.call(i),a,s=[],o;try{for(;(t===void 0||t-- >0)&&!(a=e.next()).done;)s.push(a.value)}catch(D){o={error:D}}finally{try{a&&!a.done&&(r=e.return)&&r.call(e)}finally{if(o)throw o.error}}return s}function y(){for(var i=[],t=0;t<arguments.length;t++)i=i.concat(g(arguments[t]));return i}function E(){for(var i=0,t=0,r=arguments.length;t<r;t++)i+=arguments[t].length;for(var e=Array(i),a=0,t=0;t<r;t++)for(var s=arguments[t],o=0,D=s.length;o<D;o++,a++)e[a]=s[o];return e}function h(i,t,r){if(r||arguments.length===2)for(var e=0,a=t.length,s;e<a;e++)(s||!(e in t))&&(s||(s=Array.prototype.slice.call(t,0,e)),s[e]=t[e]);return i.concat(s||t)}function c(i){return this instanceof c?(this.v=i,this):new c(i)}function _(i,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=r.apply(i,t||[]),a,s=[];return a={},o("next"),o("throw"),o("return"),a[Symbol.asyncIterator]=function(){return this},a;function o(x){e[x]&&(a[x]=function(T){return new Promise(function(w,V){s.push([x,T,w,V])>1||D(x,T)})})}function D(x,T){try{B(e[x](T))}catch(w){U(s[0][3],w)}}function B(x){x.value instanceof c?Promise.resolve(x.value.v).then(I,L):U(s[0][2],x)}function I(x){D("next",x)}function L(x){D("throw",x)}function U(x,T){x(T),s.shift(),s.length&&D(s[0][0],s[0][1])}}function M(i){var t,r;return t={},e("next"),e("throw",function(a){throw a}),e("return"),t[Symbol.iterator]=function(){return this},t;function e(a,s){t[a]=i[a]?function(o){return(r=!r)?{value:c(i[a](o)),done:a==="return"}:s?s(o):o}:s}}function l(i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=i[Symbol.asyncIterator],r;return t?t.call(i):(i=typeof m=="function"?m(i):i[Symbol.iterator](),r={},e("next"),e("throw"),e("return"),r[Symbol.asyncIterator]=function(){return this},r);function e(s){r[s]=i[s]&&function(o){return new Promise(function(D,B){o=i[s](o),a(D,B,o.done,o.value)})}}function a(s,o,D,B){Promise.resolve(B).then(function(I){s({value:I,done:D})},o)}}function O(i,t){return Object.defineProperty?Object.defineProperty(i,"raw",{value:t}):i.raw=t,i}var d=Object.create?function(i,t){Object.defineProperty(i,"default",{enumerable:!0,value:t})}:function(i,t){i.default=t};function f(i){if(i&&i.__esModule)return i;var t={};if(i!=null)for(var r in i)r!=="default"&&Object.prototype.hasOwnProperty.call(i,r)&&A(t,i,r);return d(t,i),t}function u(i){return i&&i.__esModule?i:{default:i}}function p(i,t,r,e){if(r==="a"&&!e)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?i!==t||!e:!t.has(i))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?e:r==="a"?e.call(i):e?e.value:t.get(i)}function n(i,t,r,e,a){if(e==="m")throw new TypeError("Private method is not writable");if(e==="a"&&!a)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?i!==t||!a:!t.has(i))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e==="a"?a.call(i,r):a?a.value=r:t.set(i,r),r}},85823:function(Zt,pt,P){"use strict";var C=P(87411),k=P(95622),v=P(60479),F=P(23510),z=P(80423),W=P(33051),K=P(4990),R=P(21092),b=P(14414),A="__zr_normal__",S=C.dN.concat(["ignore"]),m=(0,W.u4)(C.dN,function(f,u){return f[u]=!0,f},{ignore:!1}),g={},y=new v.Z(0,0,0,0),E=function(){function f(u){this.id=(0,W.M8)(),this.animators=[],this.currentStates=[],this.states={},this._init(u)}return f.prototype._init=function(u){this.attr(u)},f.prototype.drift=function(u,p,n){switch(this.draggable){case"horizontal":p=0;break;case"vertical":u=0;break}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=u,i[5]+=p,this.decomposeTransform(),this.markRedraw()},f.prototype.beforeUpdate=function(){},f.prototype.afterUpdate=function(){},f.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},f.prototype.updateInnerText=function(u){var p=this._textContent;if(p&&(!p.ignore||u)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,t=p.innerTransformable,r=void 0,e=void 0,a=!1;t.parent=i?this:null;var s=!1;if(t.copyTransform(p),n.position!=null){var o=y;n.layoutRect?o.copy(n.layoutRect):o.copy(this.getBoundingRect()),i||o.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(g,n,o):(0,z.wI)(g,n,o),t.x=g.x,t.y=g.y,r=g.align,e=g.verticalAlign;var D=n.origin;if(D&&n.rotation!=null){var B=void 0,I=void 0;D==="center"?(B=o.width*.5,I=o.height*.5):(B=(0,z.GM)(D[0],o.width),I=(0,z.GM)(D[1],o.height)),s=!0,t.originX=-t.x+B+(i?0:o.x),t.originY=-t.y+I+(i?0:o.y)}}n.rotation!=null&&(t.rotation=n.rotation);var L=n.offset;L&&(t.x+=L[0],t.y+=L[1],s||(t.originX=-L[0],t.originY=-L[1]));var U=n.inside==null?typeof n.position=="string"&&n.position.indexOf("inside")>=0:n.inside,x=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),T=void 0,w=void 0,V=void 0;U&&this.canBeInsideText()?(T=n.insideFill,w=n.insideStroke,(T==null||T==="auto")&&(T=this.getInsideTextFill()),(w==null||w==="auto")&&(w=this.getInsideTextStroke(T),V=!0)):(T=n.outsideFill,w=n.outsideStroke,(T==null||T==="auto")&&(T=this.getOutsideFill()),(w==null||w==="auto")&&(w=this.getOutsideStroke(T),V=!0)),T=T||"#000",(T!==x.fill||w!==x.stroke||V!==x.autoStroke||r!==x.align||e!==x.verticalAlign)&&(a=!0,x.fill=T,x.stroke=w,x.autoStroke=V,x.align=r,x.verticalAlign=e,p.setDefaultTextStyle(x)),p.__dirty|=b.YV,a&&p.dirtyStyle(!0)}},f.prototype.canBeInsideText=function(){return!0},f.prototype.getInsideTextFill=function(){return"#fff"},f.prototype.getInsideTextStroke=function(u){return"#000"},f.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?K.GD:K.vU},f.prototype.getOutsideStroke=function(u){var p=this.__zr&&this.__zr.getBackgroundColor(),n=typeof p=="string"&&(0,R.Qc)(p);n||(n=[255,255,255,1]);for(var i=n[3],t=this.__zr.isDarkMode(),r=0;r<3;r++)n[r]=n[r]*i+(t?0:255)*(1-i);return n[3]=1,(0,R.Pz)(n,"rgba")},f.prototype.traverse=function(u,p){},f.prototype.attrKV=function(u,p){u==="textConfig"?this.setTextConfig(p):u==="textContent"?this.setTextContent(p):u==="clipPath"?this.setClipPath(p):u==="extra"?(this.extra=this.extra||{},(0,W.l7)(this.extra,p)):this[u]=p},f.prototype.hide=function(){this.ignore=!0,this.markRedraw()},f.prototype.show=function(){this.ignore=!1,this.markRedraw()},f.prototype.attr=function(u,p){if(typeof u=="string")this.attrKV(u,p);else if((0,W.Kn)(u))for(var n=u,i=(0,W.XP)(n),t=0;t<i.length;t++){var r=i[t];this.attrKV(r,u[r])}return this.markRedraw(),this},f.prototype.saveCurrentToNormalState=function(u){this._innerSaveToNormal(u);for(var p=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],t=i.__fromStateTransition;if(!(i.getLoop()||t&&t!==A)){var r=i.targetName,e=r?p[r]:p;i.saveTo(e)}}},f.prototype._innerSaveToNormal=function(u){var p=this._normalState;p||(p=this._normalState={}),u.textConfig&&!p.textConfig&&(p.textConfig=this.textConfig),this._savePrimaryToNormal(u,p,S)},f.prototype._savePrimaryToNormal=function(u,p,n){for(var i=0;i<n.length;i++){var t=n[i];u[t]!=null&&!(t in p)&&(p[t]=this[t])}},f.prototype.hasState=function(){return this.currentStates.length>0},f.prototype.getState=function(u){return this.states[u]},f.prototype.ensureState=function(u){var p=this.states;return p[u]||(p[u]={}),p[u]},f.prototype.clearStates=function(u){this.useState(A,!1,u)},f.prototype.useState=function(u,p,n,i){var t=u===A,r=this.hasState();if(!(!r&&t)){var e=this.currentStates,a=this.stateTransition;if(!((0,W.cq)(e,u)>=0&&(p||e.length===1))){var s;if(this.stateProxy&&!t&&(s=this.stateProxy(u)),s||(s=this.states&&this.states[u]),!s&&!t){(0,W.H)("State "+u+" not exists.");return}t||this.saveCurrentToNormalState(s);var o=!!(s&&s.hoverLayer||i);o&&this._toggleHoverLayerFlag(!0),this._applyStateObj(u,s,this._normalState,p,!n&&!this.__inHover&&a&&a.duration>0,a);var D=this._textContent,B=this._textGuide;return D&&D.useState(u,p,n,o),B&&B.useState(u,p,n,o),t?(this.currentStates=[],this._normalState={}):p?this.currentStates.push(u):this.currentStates=[u],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~b.YV),s}}},f.prototype.useStates=function(u,p,n){if(!u.length)this.clearStates();else{var i=[],t=this.currentStates,r=u.length,e=r===t.length;if(e){for(var a=0;a<r;a++)if(u[a]!==t[a]){e=!1;break}}if(e)return;for(var a=0;a<r;a++){var s=u[a],o=void 0;this.stateProxy&&(o=this.stateProxy(s,u)),o||(o=this.states[s]),o&&i.push(o)}var D=i[r-1],B=!!(D&&D.hoverLayer||n);B&&this._toggleHoverLayerFlag(!0);var I=this._mergeStates(i),L=this.stateTransition;this.saveCurrentToNormalState(I),this._applyStateObj(u.join(","),I,this._normalState,!1,!p&&!this.__inHover&&L&&L.duration>0,L);var U=this._textContent,x=this._textGuide;U&&U.useStates(u,p,B),x&&x.useStates(u,p,B),this._updateAnimationTargets(),this.currentStates=u.slice(),this.markRedraw(),!B&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~b.YV)}},f.prototype.isSilent=function(){for(var u=this.silent,p=this.parent;!u&&p;){if(p.silent){u=!0;break}p=p.parent}return u},f.prototype._updateAnimationTargets=function(){for(var u=0;u<this.animators.length;u++){var p=this.animators[u];p.targetName&&p.changeTarget(this[p.targetName])}},f.prototype.removeState=function(u){var p=(0,W.cq)(this.currentStates,u);if(p>=0){var n=this.currentStates.slice();n.splice(p,1),this.useStates(n)}},f.prototype.replaceState=function(u,p,n){var i=this.currentStates.slice(),t=(0,W.cq)(i,u),r=(0,W.cq)(i,p)>=0;t>=0?r?i.splice(t,1):i[t]=p:n&&!r&&i.push(p),this.useStates(i)},f.prototype.toggleState=function(u,p){p?this.useState(u,!0):this.removeState(u)},f.prototype._mergeStates=function(u){for(var p={},n,i=0;i<u.length;i++){var t=u[i];(0,W.l7)(p,t),t.textConfig&&(n=n||{},(0,W.l7)(n,t.textConfig))}return n&&(p.textConfig=n),p},f.prototype._applyStateObj=function(u,p,n,i,t,r){var e=!(p&&i);p&&p.textConfig?(this.textConfig=(0,W.l7)({},i?this.textConfig:n.textConfig),(0,W.l7)(this.textConfig,p.textConfig)):e&&n.textConfig&&(this.textConfig=n.textConfig);for(var a={},s=!1,o=0;o<S.length;o++){var D=S[o],B=t&&m[D];p&&p[D]!=null?B?(s=!0,a[D]=p[D]):this[D]=p[D]:e&&n[D]!=null&&(B?(s=!0,a[D]=n[D]):this[D]=n[D])}if(!t)for(var o=0;o<this.animators.length;o++){var I=this.animators[o],L=I.targetName;I.getLoop()||I.__changeFinalValue(L?(p||n)[L]:p||n)}s&&this._transitionState(u,a,r)},f.prototype._attachComponent=function(u){if(!(u.__zr&&!u.__hostTarget)&&u!==this){var p=this.__zr;p&&u.addSelfToZr(p),u.__zr=p,u.__hostTarget=this}},f.prototype._detachComponent=function(u){u.__zr&&u.removeSelfFromZr(u.__zr),u.__zr=null,u.__hostTarget=null},f.prototype.getClipPath=function(){return this._clipPath},f.prototype.setClipPath=function(u){this._clipPath&&this._clipPath!==u&&this.removeClipPath(),this._attachComponent(u),this._clipPath=u,this.markRedraw()},f.prototype.removeClipPath=function(){var u=this._clipPath;u&&(this._detachComponent(u),this._clipPath=null,this.markRedraw())},f.prototype.getTextContent=function(){return this._textContent},f.prototype.setTextContent=function(u){var p=this._textContent;p!==u&&(p&&p!==u&&this.removeTextContent(),u.innerTransformable=new C.ZP,this._attachComponent(u),this._textContent=u,this.markRedraw())},f.prototype.setTextConfig=function(u){this.textConfig||(this.textConfig={}),(0,W.l7)(this.textConfig,u),this.markRedraw()},f.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},f.prototype.removeTextContent=function(){var u=this._textContent;u&&(u.innerTransformable=null,this._detachComponent(u),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},f.prototype.getTextGuideLine=function(){return this._textGuide},f.prototype.setTextGuideLine=function(u){this._textGuide&&this._textGuide!==u&&this.removeTextGuideLine(),this._attachComponent(u),this._textGuide=u,this.markRedraw()},f.prototype.removeTextGuideLine=function(){var u=this._textGuide;u&&(this._detachComponent(u),this._textGuide=null,this.markRedraw())},f.prototype.markRedraw=function(){this.__dirty|=b.YV;var u=this.__zr;u&&(this.__inHover?u.refreshHover():u.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},f.prototype.dirty=function(){this.markRedraw()},f.prototype._toggleHoverLayerFlag=function(u){this.__inHover=u;var p=this._textContent,n=this._textGuide;p&&(p.__inHover=u),n&&(n.__inHover=u)},f.prototype.addSelfToZr=function(u){if(this.__zr!==u){this.__zr=u;var p=this.animators;if(p)for(var n=0;n<p.length;n++)u.animation.addAnimator(p[n]);this._clipPath&&this._clipPath.addSelfToZr(u),this._textContent&&this._textContent.addSelfToZr(u),this._textGuide&&this._textGuide.addSelfToZr(u)}},f.prototype.removeSelfFromZr=function(u){if(!!this.__zr){this.__zr=null;var p=this.animators;if(p)for(var n=0;n<p.length;n++)u.animation.removeAnimator(p[n]);this._clipPath&&this._clipPath.removeSelfFromZr(u),this._textContent&&this._textContent.removeSelfFromZr(u),this._textGuide&&this._textGuide.removeSelfFromZr(u)}},f.prototype.animate=function(u,p,n){var i=u?this[u]:this,t=new k.Z(i,p,n);return u&&(t.targetName=u),this.addAnimator(t,u),t},f.prototype.addAnimator=function(u,p){var n=this.__zr,i=this;u.during(function(){i.updateDuringAnimation(p)}).done(function(){var t=i.animators,r=(0,W.cq)(t,u);r>=0&&t.splice(r,1)}),this.animators.push(u),n&&n.animation.addAnimator(u),n&&n.wakeUp()},f.prototype.updateDuringAnimation=function(u){this.markRedraw()},f.prototype.stopAnimation=function(u,p){for(var n=this.animators,i=n.length,t=[],r=0;r<i;r++){var e=n[r];!u||u===e.scope?e.stop(p):t.push(e)}return this.animators=t,this},f.prototype.animateTo=function(u,p,n){h(this,u,p,n)},f.prototype.animateFrom=function(u,p,n){h(this,u,p,n,!0)},f.prototype._transitionState=function(u,p,n,i){for(var t=h(this,p,n,i),r=0;r<t.length;r++)t[r].__fromStateTransition=u},f.prototype.getBoundingRect=function(){return null},f.prototype.getPaintRect=function(){return null},f.initDefaultProps=function(){var u=f.prototype;u.type="element",u.name="",u.ignore=u.silent=u.isGroup=u.draggable=u.dragging=u.ignoreClip=u.__inHover=!1,u.__dirty=b.YV;var p={};function n(t,r,e){p[t+r+e]||(console.warn("DEPRECATED: '"+t+"' has been deprecated. use '"+r+"', '"+e+"' instead"),p[t+r+e]=!0)}function i(t,r,e,a){Object.defineProperty(u,t,{get:function(){if(!this[r]){var o=this[r]=[];s(this,o)}return this[r]},set:function(o){this[e]=o[0],this[a]=o[1],this[r]=o,s(this,o)}});function s(o,D){Object.defineProperty(D,0,{get:function(){return o[e]},set:function(B){o[e]=B}}),Object.defineProperty(D,1,{get:function(){return o[a]},set:function(B){o[a]=B}})}}Object.defineProperty&&(i("position","_legacyPos","x","y"),i("scale","_legacyScale","scaleX","scaleY"),i("origin","_legacyOrigin","originX","originY"))}(),f}();(0,W.jB)(E,F.Z),(0,W.jB)(E,C.ZP);function h(f,u,p,n,i){p=p||{};var t=[];d(f,"",f,u,p,n,t,i);var r=t.length,e=!1,a=p.done,s=p.aborted,o=function(){e=!0,r--,r<=0&&(e?a&&a():s&&s())},D=function(){r--,r<=0&&(e?a&&a():s&&s())};r||a&&a(),t.length>0&&p.during&&t[0].during(function(L,U){p.during(U)});for(var B=0;B<t.length;B++){var I=t[B];o&&I.done(o),D&&I.aborted(D),p.force&&I.duration(p.duration),I.start(p.easing)}return t}function c(f,u,p){for(var n=0;n<p;n++)f[n]=u[n]}function _(f){return(0,W.zG)(f[0])}function M(f,u,p){if((0,W.zG)(u[p]))if((0,W.zG)(f[p])||(f[p]=[]),(0,W.fU)(u[p])){var n=u[p].length;f[p].length!==n&&(f[p]=new u[p].constructor(n),c(f[p],u[p],n))}else{var i=u[p],t=f[p],r=i.length;if(_(i))for(var e=i[0].length,a=0;a<r;a++)t[a]?c(t[a],i[a],e):t[a]=Array.prototype.slice.call(i[a]);else c(t,i,r);t.length=i.length}else f[p]=u[p]}function l(f,u){return f===u||(0,W.zG)(f)&&(0,W.zG)(u)&&O(f,u)}function O(f,u){var p=f.length;if(p!==u.length)return!1;for(var n=0;n<p;n++)if(f[n]!==u[n])return!1;return!0}function d(f,u,p,n,i,t,r,e){for(var a=(0,W.XP)(n),s=i.duration,o=i.delay,D=i.additive,B=i.setToFinal,I=!(0,W.Kn)(t),L=f.animators,U=[],x=0;x<a.length;x++){var T=a[x],w=n[T];if(w!=null&&p[T]!=null&&(I||t[T]))if((0,W.Kn)(w)&&!(0,W.zG)(w)&&!(0,W.Qq)(w)){if(u){e||(p[T]=w,f.updateDuringAnimation(u));continue}d(f,T,p[T],w,i,t&&t[T],r,e)}else U.push(T);else e||(p[T]=w,f.updateDuringAnimation(u),U.push(T))}var V=U.length;if(!D&&V)for(var N=0;N<L.length;N++){var G=L[N];if(G.targetName===u){var Z=G.stopTracks(U);if(Z){var H=(0,W.cq)(L,G);L.splice(H,1)}}}if(i.force||(U=(0,W.hX)(U,function(ot){return!l(n[ot],p[ot])}),V=U.length),V>0||i.force&&!r.length){var X=void 0,ut=void 0,lt=void 0;if(e){ut={},B&&(X={});for(var N=0;N<V;N++){var T=U[N];ut[T]=p[T],B?X[T]=n[T]:p[T]=n[T]}}else if(B){lt={};for(var N=0;N<V;N++){var T=U[N];lt[T]=(0,k.V)(p[T]),M(p,n,T)}}var G=new k.Z(p,!1,!1,D?(0,W.hX)(L,function(q){return q.targetName===u}):null);G.targetName=u,i.scope&&(G.scope=i.scope),B&&X&&G.whenWithKeys(0,X,U),lt&&G.whenWithKeys(0,lt,U),G.whenWithKeys(s==null?500:s,e?ut:n,U).delay(o||0),f.addAnimator(G,u),r.push(G)}}pt.Z=E},95622:function(Zt,pt,P){"use strict";P.d(pt,{V:function(){return c},Z:function(){return s}});var C={linear:function(o){return o},quadraticIn:function(o){return o*o},quadraticOut:function(o){return o*(2-o)},quadraticInOut:function(o){return(o*=2)<1?.5*o*o:-.5*(--o*(o-2)-1)},cubicIn:function(o){return o*o*o},cubicOut:function(o){return--o*o*o+1},cubicInOut:function(o){return(o*=2)<1?.5*o*o*o:.5*((o-=2)*o*o+2)},quarticIn:function(o){return o*o*o*o},quarticOut:function(o){return 1- --o*o*o*o},quarticInOut:function(o){return(o*=2)<1?.5*o*o*o*o:-.5*((o-=2)*o*o*o-2)},quinticIn:function(o){return o*o*o*o*o},quinticOut:function(o){return--o*o*o*o*o+1},quinticInOut:function(o){return(o*=2)<1?.5*o*o*o*o*o:.5*((o-=2)*o*o*o*o+2)},sinusoidalIn:function(o){return 1-Math.cos(o*Math.PI/2)},sinusoidalOut:function(o){return Math.sin(o*Math.PI/2)},sinusoidalInOut:function(o){return .5*(1-Math.cos(Math.PI*o))},exponentialIn:function(o){return o===0?0:Math.pow(1024,o-1)},exponentialOut:function(o){return o===1?1:1-Math.pow(2,-10*o)},exponentialInOut:function(o){return o===0?0:o===1?1:(o*=2)<1?.5*Math.pow(1024,o-1):.5*(-Math.pow(2,-10*(o-1))+2)},circularIn:function(o){return 1-Math.sqrt(1-o*o)},circularOut:function(o){return Math.sqrt(1- --o*o)},circularInOut:function(o){return(o*=2)<1?-.5*(Math.sqrt(1-o*o)-1):.5*(Math.sqrt(1-(o-=2)*o)+1)},elasticIn:function(o){var D,B=.1,I=.4;return o===0?0:o===1?1:(!B||B<1?(B=1,D=I/4):D=I*Math.asin(1/B)/(2*Math.PI),-(B*Math.pow(2,10*(o-=1))*Math.sin((o-D)*(2*Math.PI)/I)))},elasticOut:function(o){var D,B=.1,I=.4;return o===0?0:o===1?1:(!B||B<1?(B=1,D=I/4):D=I*Math.asin(1/B)/(2*Math.PI),B*Math.pow(2,-10*o)*Math.sin((o-D)*(2*Math.PI)/I)+1)},elasticInOut:function(o){var D,B=.1,I=.4;return o===0?0:o===1?1:(!B||B<1?(B=1,D=I/4):D=I*Math.asin(1/B)/(2*Math.PI),(o*=2)<1?-.5*(B*Math.pow(2,10*(o-=1))*Math.sin((o-D)*(2*Math.PI)/I)):B*Math.pow(2,-10*(o-=1))*Math.sin((o-D)*(2*Math.PI)/I)*.5+1)},backIn:function(o){var D=1.70158;return o*o*((D+1)*o-D)},backOut:function(o){var D=1.70158;return--o*o*((D+1)*o+D)+1},backInOut:function(o){var D=1.70158*1.525;return(o*=2)<1?.5*(o*o*((D+1)*o-D)):.5*((o-=2)*o*((D+1)*o+D)+2)},bounceIn:function(o){return 1-C.bounceOut(1-o)},bounceOut:function(o){return o<1/2.75?7.5625*o*o:o<2/2.75?7.5625*(o-=1.5/2.75)*o+.75:o<2.5/2.75?7.5625*(o-=2.25/2.75)*o+.9375:7.5625*(o-=2.625/2.75)*o+.984375},bounceInOut:function(o){return o<.5?C.bounceIn(o*2)*.5:C.bounceOut(o*2-1)*.5+.5}},k=C,v=P(33051),F=P(75188),z=function(){function o(D){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=D.life||1e3,this._delay=D.delay||0,this.loop=D.loop||!1,this.onframe=D.onframe||v.ZT,this.ondestroy=D.ondestroy||v.ZT,this.onrestart=D.onrestart||v.ZT,D.easing&&this.setEasing(D.easing)}return o.prototype.step=function(D,B){if(this._inited||(this._startTime=D+this._delay,this._inited=!0),this._paused){this._pausedTime+=B;return}var I=this._life,L=D-this._startTime-this._pausedTime,U=L/I;U<0&&(U=0),U=Math.min(U,1);var x=this.easingFunc,T=x?x(U):U;if(this.onframe(T),U===1)if(this.loop){var w=L%I;this._startTime=D-w,this._pausedTime=0,this.onrestart()}else return!0;return!1},o.prototype.pause=function(){this._paused=!0},o.prototype.resume=function(){this._paused=!1},o.prototype.setEasing=function(D){this.easing=D,this.easingFunc=(0,v.mf)(D)?D:k[D]||(0,F.H)(D)},o}(),W=z,K=P(21092),R=P(24839),b=Array.prototype.slice;function A(o,D,B){return(D-o)*B+o}function S(o,D,B,I){for(var L=D.length,U=0;U<L;U++)o[U]=A(D[U],B[U],I);return o}function m(o,D,B,I){for(var L=D.length,U=L&&D[0].length,x=0;x<L;x++){o[x]||(o[x]=[]);for(var T=0;T<U;T++)o[x][T]=A(D[x][T],B[x][T],I)}return o}function g(o,D,B,I){for(var L=D.length,U=0;U<L;U++)o[U]=D[U]+B[U]*I;return o}function y(o,D,B,I){for(var L=D.length,U=L&&D[0].length,x=0;x<L;x++){o[x]||(o[x]=[]);for(var T=0;T<U;T++)o[x][T]=D[x][T]+B[x][T]*I}return o}function E(o,D){for(var B=o.length,I=D.length,L=B>I?D:o,U=Math.min(B,I),x=L[U-1]||{color:[0,0,0,0],offset:0},T=U;T<Math.max(B,I);T++)L.push({offset:x.offset,color:x.color.slice()})}function h(o,D,B){var I=o,L=D;if(!(!I.push||!L.push)){var U=I.length,x=L.length;if(U!==x){var T=U>x;if(T)I.length=x;else for(var w=U;w<x;w++)I.push(B===1?L[w]:b.call(L[w]))}for(var V=I[0]&&I[0].length,w=0;w<I.length;w++)if(B===1)isNaN(I[w])&&(I[w]=L[w]);else for(var N=0;N<V;N++)isNaN(I[w][N])&&(I[w][N]=L[w][N])}}function c(o){if((0,v.zG)(o)){var D=o.length;if((0,v.zG)(o[0])){for(var B=[],I=0;I<D;I++)B.push(b.call(o[I]));return B}return b.call(o)}return o}function _(o){return o[0]=Math.floor(o[0])||0,o[1]=Math.floor(o[1])||0,o[2]=Math.floor(o[2])||0,o[3]=o[3]==null?1:o[3],"rgba("+o.join(",")+")"}function M(o){return(0,v.zG)(o&&o[0])?2:1}var l=0,O=1,d=2,f=3,u=4,p=5,n=6;function i(o){return o===u||o===p}function t(o){return o===O||o===d}var r=[0,0,0,0],e=function(){function o(D){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=D}return o.prototype.isFinished=function(){return this._finished},o.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},o.prototype.needsAnimate=function(){return this.keyframes.length>=1},o.prototype.getAdditiveTrack=function(){return this._additiveTrack},o.prototype.addKeyframe=function(D,B,I){this._needsSort=!0;var L=this.keyframes,U=L.length,x=!1,T=n,w=B;if((0,v.zG)(B)){var V=M(B);T=V,(V===1&&!(0,v.hj)(B[0])||V===2&&!(0,v.hj)(B[0][0]))&&(x=!0)}else if((0,v.hj)(B)&&!(0,v.Bu)(B))T=l;else if((0,v.HD)(B))if(!isNaN(+B))T=l;else{var N=K.Qc(B);N&&(w=N,T=f)}else if((0,v.Qq)(B)){var G=(0,v.l7)({},w);G.colorStops=(0,v.UI)(B.colorStops,function(H){return{offset:H.offset,color:K.Qc(H.color)}}),(0,R.I1)(B)?T=u:(0,R.gO)(B)&&(T=p),w=G}U===0?this.valType=T:(T!==this.valType||T===n)&&(x=!0),this.discrete=this.discrete||x;var Z={time:D,value:w,rawValue:B,percent:0};return I&&(Z.easing=I,Z.easingFunc=(0,v.mf)(I)?I:k[I]||(0,F.H)(I)),L.push(Z),Z},o.prototype.prepare=function(D,B){var I=this.keyframes;this._needsSort&&I.sort(function(ut,lt){return ut.time-lt.time});for(var L=this.valType,U=I.length,x=I[U-1],T=this.discrete,w=t(L),V=i(L),N=0;N<U;N++){var G=I[N],Z=G.value,H=x.value;G.percent=G.time/D,T||(w&&N!==U-1?h(Z,H,L):V&&E(Z.colorStops,H.colorStops))}if(!T&&L!==p&&B&&this.needsAnimate()&&B.needsAnimate()&&L===B.valType&&!B._finished){this._additiveTrack=B;for(var X=I[0].value,N=0;N<U;N++)L===l?I[N].additiveValue=I[N].value-X:L===f?I[N].additiveValue=g([],I[N].value,X,-1):t(L)&&(I[N].additiveValue=L===O?g([],I[N].value,X,-1):y([],I[N].value,X,-1))}},o.prototype.step=function(D,B){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var I=this._additiveTrack!=null,L=I?"additiveValue":"value",U=this.valType,x=this.keyframes,T=x.length,w=this.propName,V=U===f,N,G=this._lastFr,Z=Math.min,H,X;if(T===1)H=X=x[0];else{if(B<0)N=0;else if(B<this._lastFrP){var ut=Z(G+1,T-1);for(N=ut;N>=0&&!(x[N].percent<=B);N--);N=Z(N,T-2)}else{for(N=G;N<T&&!(x[N].percent>B);N++);N=Z(N-1,T-2)}X=x[N+1],H=x[N]}if(!!(H&&X)){this._lastFr=N,this._lastFrP=B;var lt=X.percent-H.percent,ot=lt===0?1:Z((B-H.percent)/lt,1);X.easingFunc&&(ot=X.easingFunc(ot));var q=I?this._additiveValue:V?r:D[w];if((t(U)||V)&&!q&&(q=this._additiveValue=[]),this.discrete)D[w]=ot<1?H.rawValue:X.rawValue;else if(t(U))U===O?S(q,H[L],X[L],ot):m(q,H[L],X[L],ot);else if(i(U)){var tt=H[L],j=X[L],et=U===u;D[w]={type:et?"linear":"radial",x:A(tt.x,j.x,ot),y:A(tt.y,j.y,ot),colorStops:(0,v.UI)(tt.colorStops,function(_t,St){var At=j.colorStops[St];return{offset:A(_t.offset,At.offset,ot),color:_(S([],_t.color,At.color,ot))}}),global:j.global},et?(D[w].x2=A(tt.x2,j.x2,ot),D[w].y2=A(tt.y2,j.y2,ot)):D[w].r=A(tt.r,j.r,ot)}else if(V)S(q,H[L],X[L],ot),I||(D[w]=_(q));else{var ct=A(H[L],X[L],ot);I?this._additiveValue=ct:D[w]=ct}I&&this._addToTarget(D)}}},o.prototype._addToTarget=function(D){var B=this.valType,I=this.propName,L=this._additiveValue;B===l?D[I]=D[I]+L:B===f?(K.Qc(D[I],r),g(r,r,L,1),D[I]=_(r)):B===O?g(D[I],D[I],L,1):B===d&&y(D[I],D[I],L,1)},o}(),a=function(){function o(D,B,I,L){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=D,this._loop=B,B&&L){(0,v.H)("Can' use additive animation on looped animation.");return}this._additiveAnimators=L,this._allowDiscrete=I}return o.prototype.getMaxTime=function(){return this._maxTime},o.prototype.getDelay=function(){return this._delay},o.prototype.getLoop=function(){return this._loop},o.prototype.getTarget=function(){return this._target},o.prototype.changeTarget=function(D){this._target=D},o.prototype.when=function(D,B,I){return this.whenWithKeys(D,B,(0,v.XP)(B),I)},o.prototype.whenWithKeys=function(D,B,I,L){for(var U=this._tracks,x=0;x<I.length;x++){var T=I[x],w=U[T];if(!w){w=U[T]=new e(T);var V=void 0,N=this._getAdditiveTrack(T);if(N){var G=N.keyframes,Z=G[G.length-1];V=Z&&Z.value,N.valType===f&&V&&(V=_(V))}else V=this._target[T];if(V==null)continue;D>0&&w.addKeyframe(0,c(V),L),this._trackKeys.push(T)}w.addKeyframe(D,c(B[T]),L)}return this._maxTime=Math.max(this._maxTime,D),this},o.prototype.pause=function(){this._clip.pause(),this._paused=!0},o.prototype.resume=function(){this._clip.resume(),this._paused=!1},o.prototype.isPaused=function(){return!!this._paused},o.prototype.duration=function(D){return this._maxTime=D,this._force=!0,this},o.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var D=this._doneCbs;if(D)for(var B=D.length,I=0;I<B;I++)D[I].call(this)},o.prototype._abortedCallback=function(){this._setTracksFinished();var D=this.animation,B=this._abortedCbs;if(D&&D.removeClip(this._clip),this._clip=null,B)for(var I=0;I<B.length;I++)B[I].call(this)},o.prototype._setTracksFinished=function(){for(var D=this._tracks,B=this._trackKeys,I=0;I<B.length;I++)D[B[I]].setFinished()},o.prototype._getAdditiveTrack=function(D){var B,I=this._additiveAnimators;if(I)for(var L=0;L<I.length;L++){var U=I[L].getTrack(D);U&&(B=U)}return B},o.prototype.start=function(D){if(!(this._started>0)){this._started=1;for(var B=this,I=[],L=this._maxTime||0,U=0;U<this._trackKeys.length;U++){var x=this._trackKeys[U],T=this._tracks[x],w=this._getAdditiveTrack(x),V=T.keyframes,N=V.length;if(T.prepare(L,w),T.needsAnimate())if(!this._allowDiscrete&&T.discrete){var G=V[N-1];G&&(B._target[T.propName]=G.rawValue),T.setFinished()}else I.push(T)}if(I.length||this._force){var Z=new W({life:L,loop:this._loop,delay:this._delay||0,onframe:function(H){B._started=2;var X=B._additiveAnimators;if(X){for(var ut=!1,lt=0;lt<X.length;lt++)if(X[lt]._clip){ut=!0;break}ut||(B._additiveAnimators=null)}for(var lt=0;lt<I.length;lt++)I[lt].step(B._target,H);var ot=B._onframeCbs;if(ot)for(var lt=0;lt<ot.length;lt++)ot[lt](B._target,H)},ondestroy:function(){B._doneCallback()}});this._clip=Z,this.animation&&this.animation.addClip(Z),D&&Z.setEasing(D)}else this._doneCallback();return this}},o.prototype.stop=function(D){if(!!this._clip){var B=this._clip;D&&B.onframe(1),this._abortedCallback()}},o.prototype.delay=function(D){return this._delay=D,this},o.prototype.during=function(D){return D&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(D)),this},o.prototype.done=function(D){return D&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(D)),this},o.prototype.aborted=function(D){return D&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(D)),this},o.prototype.getClip=function(){return this._clip},o.prototype.getTrack=function(D){return this._tracks[D]},o.prototype.getTracks=function(){var D=this;return(0,v.UI)(this._trackKeys,function(B){return D._tracks[B]})},o.prototype.stopTracks=function(D,B){if(!D.length||!this._clip)return!0;for(var I=this._tracks,L=this._trackKeys,U=0;U<D.length;U++){var x=I[D[U]];x&&!x.isFinished()&&(B?x.step(this._target,1):this._started===1&&x.step(this._target,0),x.setFinished())}for(var T=!0,U=0;U<L.length;U++)if(!I[L[U]].isFinished()){T=!1;break}return T&&this._abortedCallback(),T},o.prototype.saveTo=function(D,B,I){if(!!D){B=B||this._trackKeys;for(var L=0;L<B.length;L++){var U=B[L],x=this._tracks[U];if(!(!x||x.isFinished())){var T=x.keyframes,w=T[I?0:T.length-1];w&&(D[U]=c(w.rawValue))}}}},o.prototype.__changeFinalValue=function(D,B){B=B||(0,v.XP)(D);for(var I=0;I<B.length;I++){var L=B[I],U=this._tracks[L];if(!!U){var x=U.keyframes;if(x.length>1){var T=x.pop();U.addKeyframe(T.time,D[L]),U.prepare(this._maxTime,U.getAdditiveTrack())}}}},o}(),s=a},75188:function(Zt,pt,P){"use strict";P.d(pt,{H:function(){return F}});var C=P(18554),k=P(33051),v=/cubic-bezier\(([0-9,\.e ]+)\)/;function F(z){var W=z&&v.exec(z);if(W){var K=W[1].split(","),R=+(0,k.fy)(K[0]),b=+(0,k.fy)(K[1]),A=+(0,k.fy)(K[2]),S=+(0,k.fy)(K[3]);if(isNaN(R+b+A+S))return;var m=[];return function(g){return g<=0?0:g>=1?1:(0,C.kD)(0,R,A,1,g,m)&&(0,C.af)(0,b,S,1,m[0])}}}},22795:function(Zt,pt,P){"use strict";var C=P(66387),k;k=C.Z.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(v){return setTimeout(v,16)},pt.Z=k},50810:function(Zt,pt,P){"use strict";P.d(pt,{a:function(){return v}});var C=P(33051);function k(F,z){return!F||F==="solid"||!(z>0)?null:F==="dashed"?[4*z,2*z]:F==="dotted"?[z]:(0,C.hj)(F)?[F]:(0,C.kJ)(F)?F:null}function v(F){var z=F.style,W=z.lineDash&&z.lineWidth>0&&k(z.lineDash,z.lineWidth),K=z.lineDashOffset;if(W){var R=z.strokeNoScale&&F.getLineScale?F.getLineScale():1;R&&R!==1&&(W=(0,C.UI)(W,function(b){return b/R}),K/=R)}return[W,K]}},97772:function(Zt,pt,P){"use strict";P.d(pt,{RZ:function(){return _},RV:function(){return L},Dm:function(){return U}});var C=P(7719),k=P(14014),v=P(8007),F=P(5787),z=P(4665),W=P(44535),K=P(71505),R=P(33051),b=P(50810),A=P(14414),S=P(23132),m=new k.Z(!0);function g(T){var w=T.stroke;return!(w==null||w==="none"||!(T.lineWidth>0))}function y(T){return typeof T=="string"&&T!=="none"}function E(T){var w=T.fill;return w!=null&&w!=="none"}function h(T,w){if(w.fillOpacity!=null&&w.fillOpacity!==1){var V=T.globalAlpha;T.globalAlpha=w.fillOpacity*w.opacity,T.fill(),T.globalAlpha=V}else T.fill()}function c(T,w){if(w.strokeOpacity!=null&&w.strokeOpacity!==1){var V=T.globalAlpha;T.globalAlpha=w.strokeOpacity*w.opacity,T.stroke(),T.globalAlpha=V}else T.stroke()}function _(T,w,V){var N=(0,v.Gq)(w.image,w.__image,V);if((0,v.v5)(N)){var G=T.createPattern(N,w.repeat||"repeat");if(typeof DOMMatrix=="function"&&G&&G.setTransform){var Z=new DOMMatrix;Z.translateSelf(w.x||0,w.y||0),Z.rotateSelf(0,0,(w.rotation||0)*R.I3),Z.scaleSelf(w.scaleX||1,w.scaleY||1),G.setTransform(Z)}return G}}function M(T,w,V,N){var G,Z=g(V),H=E(V),X=V.strokePercent,ut=X<1,lt=!w.path;(!w.silent||ut)&&lt&&w.createPathProxy();var ot=w.path||m,q=w.__dirty;if(!N){var tt=V.fill,j=V.stroke,et=H&&!!tt.colorStops,ct=Z&&!!j.colorStops,_t=H&&!!tt.image,St=Z&&!!j.image,At=void 0,Ct=void 0,Et=void 0,Ot=void 0,Wt=void 0;(et||ct)&&(Wt=w.getBoundingRect()),et&&(At=q?(0,F.ZF)(T,tt,Wt):w.__canvasFillGradient,w.__canvasFillGradient=At),ct&&(Ct=q?(0,F.ZF)(T,j,Wt):w.__canvasStrokeGradient,w.__canvasStrokeGradient=Ct),_t&&(Et=q||!w.__canvasFillPattern?_(T,tt,w):w.__canvasFillPattern,w.__canvasFillPattern=Et),St&&(Ot=q||!w.__canvasStrokePattern?_(T,j,w):w.__canvasStrokePattern,w.__canvasStrokePattern=Et),et?T.fillStyle=At:_t&&(Et?T.fillStyle=Et:H=!1),ct?T.strokeStyle=Ct:St&&(Ot?T.strokeStyle=Ot:Z=!1)}var Ut=w.getGlobalScale();ot.setScale(Ut[0],Ut[1],w.segmentIgnoreThreshold);var Bt,Ht;T.setLineDash&&V.lineDash&&(G=(0,b.a)(w),Bt=G[0],Ht=G[1]);var kt=!0;(lt||q&A.RH)&&(ot.setDPR(T.dpr),ut?ot.setContext(null):(ot.setContext(T),kt=!1),ot.reset(),w.buildPath(ot,w.shape,N),ot.toStatic(),w.pathUpdated()),kt&&ot.rebuildPath(T,ut?X:1),Bt&&(T.setLineDash(Bt),T.lineDashOffset=Ht),N||(V.strokeFirst?(Z&&c(T,V),H&&h(T,V)):(H&&h(T,V),Z&&c(T,V))),Bt&&T.setLineDash([])}function l(T,w,V){var N=w.__image=(0,v.Gq)(V.image,w.__image,w,w.onload);if(!(!N||!(0,v.v5)(N))){var G=V.x||0,Z=V.y||0,H=w.getWidth(),X=w.getHeight(),ut=N.width/N.height;if(H==null&&X!=null?H=X*ut:X==null&&H!=null?X=H/ut:H==null&&X==null&&(H=N.width,X=N.height),V.sWidth&&V.sHeight){var lt=V.sx||0,ot=V.sy||0;T.drawImage(N,lt,ot,V.sWidth,V.sHeight,G,Z,H,X)}else if(V.sx&&V.sy){var lt=V.sx,ot=V.sy,q=H-lt,tt=X-ot;T.drawImage(N,lt,ot,q,tt,G,Z,H,X)}else T.drawImage(N,G,Z,H,X)}}function O(T,w,V){var N,G=V.text;if(G!=null&&(G+=""),G){T.font=V.font||S.Uo,T.textAlign=V.textAlign,T.textBaseline=V.textBaseline;var Z=void 0,H=void 0;T.setLineDash&&V.lineDash&&(N=(0,b.a)(w),Z=N[0],H=N[1]),Z&&(T.setLineDash(Z),T.lineDashOffset=H),V.strokeFirst?(g(V)&&T.strokeText(G,V.x,V.y),E(V)&&T.fillText(G,V.x,V.y)):(E(V)&&T.fillText(G,V.x,V.y),g(V)&&T.strokeText(G,V.x,V.y)),Z&&T.setLineDash([])}}var d=["shadowBlur","shadowOffsetX","shadowOffsetY"],f=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function u(T,w,V,N,G){var Z=!1;if(!N&&(V=V||{},w===V))return!1;if(N||w.opacity!==V.opacity){B(T,G),Z=!0;var H=Math.max(Math.min(w.opacity,1),0);T.globalAlpha=isNaN(H)?C.tj.opacity:H}(N||w.blend!==V.blend)&&(Z||(B(T,G),Z=!0),T.globalCompositeOperation=w.blend||C.tj.blend);for(var X=0;X<d.length;X++){var ut=d[X];(N||w[ut]!==V[ut])&&(Z||(B(T,G),Z=!0),T[ut]=T.dpr*(w[ut]||0))}return(N||w.shadowColor!==V.shadowColor)&&(Z||(B(T,G),Z=!0),T.shadowColor=w.shadowColor||C.tj.shadowColor),Z}function p(T,w,V,N,G){var Z=I(w,G.inHover),H=N?null:V&&I(V,G.inHover)||{};if(Z===H)return!1;var X=u(T,Z,H,N,G);if((N||Z.fill!==H.fill)&&(X||(B(T,G),X=!0),y(Z.fill)&&(T.fillStyle=Z.fill)),(N||Z.stroke!==H.stroke)&&(X||(B(T,G),X=!0),y(Z.stroke)&&(T.strokeStyle=Z.stroke)),(N||Z.opacity!==H.opacity)&&(X||(B(T,G),X=!0),T.globalAlpha=Z.opacity==null?1:Z.opacity),w.hasStroke()){var ut=Z.lineWidth,lt=ut/(Z.strokeNoScale&&w.getLineScale?w.getLineScale():1);T.lineWidth!==lt&&(X||(B(T,G),X=!0),T.lineWidth=lt)}for(var ot=0;ot<f.length;ot++){var q=f[ot],tt=q[0];(N||Z[tt]!==H[tt])&&(X||(B(T,G),X=!0),T[tt]=Z[tt]||q[1])}return X}function n(T,w,V,N,G){return u(T,I(w,G.inHover),V&&I(V,G.inHover),N,G)}function i(T,w){var V=w.transform,N=T.dpr||1;V?T.setTransform(N*V[0],N*V[1],N*V[2],N*V[3],N*V[4],N*V[5]):T.setTransform(N,0,0,N,0,0)}function t(T,w,V){for(var N=!1,G=0;G<T.length;G++){var Z=T[G];N=N||Z.isZeroArea(),i(w,Z),w.beginPath(),Z.buildPath(w,Z.shape),w.clip()}V.allClipped=N}function r(T,w){return T&&w?T[0]!==w[0]||T[1]!==w[1]||T[2]!==w[2]||T[3]!==w[3]||T[4]!==w[4]||T[5]!==w[5]:!(!T&&!w)}var e=1,a=2,s=3,o=4;function D(T){var w=E(T),V=g(T);return!(T.lineDash||!(+w^+V)||w&&typeof T.fill!="string"||V&&typeof T.stroke!="string"||T.strokePercent<1||T.strokeOpacity<1||T.fillOpacity<1)}function B(T,w){w.batchFill&&T.fill(),w.batchStroke&&T.stroke(),w.batchFill="",w.batchStroke=""}function I(T,w){return w&&T.__hoverStyle||T.style}function L(T,w){U(T,w,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function U(T,w,V,N){var G=w.transform;if(!w.shouldBePainted(V.viewWidth,V.viewHeight,!1,!1)){w.__dirty&=~A.YV,w.__isRendered=!1;return}var Z=w.__clipPaths,H=V.prevElClipPaths,X=!1,ut=!1;if((!H||(0,F.cF)(Z,H))&&(H&&H.length&&(B(T,V),T.restore(),ut=X=!0,V.prevElClipPaths=null,V.allClipped=!1,V.prevEl=null),Z&&Z.length&&(B(T,V),T.save(),t(Z,T,V),X=!0),V.prevElClipPaths=Z),V.allClipped){w.__isRendered=!1;return}w.beforeBrush&&w.beforeBrush(),w.innerBeforeBrush();var lt=V.prevEl;lt||(ut=X=!0);var ot=w instanceof z.ZP&&w.autoBatch&&D(w.style);X||r(G,lt.transform)?(B(T,V),i(T,w)):ot||B(T,V);var q=I(w,V.inHover);w instanceof z.ZP?(V.lastDrawType!==e&&(ut=!0,V.lastDrawType=e),p(T,w,lt,ut,V),(!ot||!V.batchFill&&!V.batchStroke)&&T.beginPath(),M(T,w,q,ot),ot&&(V.batchFill=q.fill||"",V.batchStroke=q.stroke||"")):w instanceof K.Z?(V.lastDrawType!==s&&(ut=!0,V.lastDrawType=s),p(T,w,lt,ut,V),O(T,w,q)):w instanceof W.ZP?(V.lastDrawType!==a&&(ut=!0,V.lastDrawType=a),n(T,w,lt,ut,V),l(T,w,q)):w.getTemporalDisplayables&&(V.lastDrawType!==o&&(ut=!0,V.lastDrawType=o),x(T,w,V)),ot&&N&&B(T,V),w.innerAfterBrush(),w.afterBrush&&w.afterBrush(),V.prevEl=w,w.__dirty=0,w.__isRendered=!0}function x(T,w,V){var N=w.getDisplayables(),G=w.getTemporalDisplayables();T.save();var Z={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:V.viewWidth,viewHeight:V.viewHeight,inHover:V.inHover},H,X;for(H=w.getCursor(),X=N.length;H<X;H++){var ut=N[H];ut.beforeBrush&&ut.beforeBrush(),ut.innerBeforeBrush(),U(T,ut,Z,H===X-1),ut.innerAfterBrush(),ut.afterBrush&&ut.afterBrush(),Z.prevEl=ut}for(var lt=0,ot=G.length;lt<ot;lt++){var ut=G[lt];ut.beforeBrush&&ut.beforeBrush(),ut.innerBeforeBrush(),U(T,ut,Z,lt===ot-1),ut.innerAfterBrush(),ut.afterBrush&&ut.afterBrush(),Z.prevEl=ut}w.clearTemporalDisplayables(),w.notClear=!0,T.restore()}},5787:function(Zt,pt,P){"use strict";P.d(pt,{ZF:function(){return F},cF:function(){return z},ap:function(){return K}});function C(R){return isFinite(R)}function k(R,b,A){var S=b.x==null?0:b.x,m=b.x2==null?1:b.x2,g=b.y==null?0:b.y,y=b.y2==null?0:b.y2;b.global||(S=S*A.width+A.x,m=m*A.width+A.x,g=g*A.height+A.y,y=y*A.height+A.y),S=C(S)?S:0,m=C(m)?m:1,g=C(g)?g:0,y=C(y)?y:0;var E=R.createLinearGradient(S,g,m,y);return E}function v(R,b,A){var S=A.width,m=A.height,g=Math.min(S,m),y=b.x==null?.5:b.x,E=b.y==null?.5:b.y,h=b.r==null?.5:b.r;b.global||(y=y*S+A.x,E=E*m+A.y,h=h*g),y=C(y)?y:.5,E=C(E)?E:.5,h=h>=0&&C(h)?h:.5;var c=R.createRadialGradient(y,E,0,y,E,h);return c}function F(R,b,A){for(var S=b.type==="radial"?v(R,b,A):k(R,b,A),m=b.colorStops,g=0;g<m.length;g++)S.addColorStop(m[g].offset,m[g].color);return S}function z(R,b){if(R===b||!R&&!b)return!1;if(!R||!b||R.length!==b.length)return!0;for(var A=0;A<R.length;A++)if(R[A]!==b[A])return!0;return!1}function W(R){return parseInt(R,10)}function K(R,b,A){var S=["width","height"][b],m=["clientWidth","clientHeight"][b],g=["paddingLeft","paddingTop"][b],y=["paddingRight","paddingBottom"][b];if(A[S]!=null&&A[S]!=="auto")return parseFloat(A[S]);var E=document.defaultView.getComputedStyle(R);return(R[m]||W(E[S])||W(R.style[S]))-(W(E[g])||0)-(W(E[y])||0)|0}},4990:function(Zt,pt,P){"use strict";P.d(pt,{KL:function(){return F},Ak:function(){return z},vU:function(){return W},GD:function(){return K},iv:function(){return R}});var C=P(66387),k=1;C.Z.hasGlobalWindow&&(k=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var v=0,F=k,z=.4,W="#333",K="#ccc",R="#eee"},80423:function(Zt,pt,P){"use strict";P.d(pt,{dz:function(){return z},lP:function(){return K},M3:function(){return R},mU:function(){return b},Dp:function(){return A},GM:function(){return m},wI:function(){return g}});var C=P(60479),k=P(92528),v=P(23132),F={};function z(y,E){E=E||v.Uo;var h=F[E];h||(h=F[E]=new k.ZP(500));var c=h.get(y);return c==null&&(c=v.qW.measureText(y,E).width,h.put(y,c)),c}function W(y,E,h,c){var _=z(y,E),M=A(E),l=R(0,_,h),O=b(0,M,c),d=new C.Z(l,O,_,M);return d}function K(y,E,h,c){var _=((y||"")+"").split(`
`),M=_.length;if(M===1)return W(_[0],E,h,c);for(var l=new C.Z(0,0,0,0),O=0;O<_.length;O++){var d=W(_[O],E,h,c);O===0?l.copy(d):l.union(d)}return l}function R(y,E,h){return h==="right"?y-=E:h==="center"&&(y-=E/2),y}function b(y,E,h){return h==="middle"?y-=E/2:h==="bottom"&&(y-=E),y}function A(y){return z("\u56FD",y)}function S(y,E){return platformApi.measureText(y,E)}function m(y,E){return typeof y=="string"?y.lastIndexOf("%")>=0?parseFloat(y)/100*E:parseFloat(y):y}function g(y,E,h){var c=E.position||"inside",_=E.distance!=null?E.distance:5,M=h.height,l=h.width,O=M/2,d=h.x,f=h.y,u="left",p="top";if(c instanceof Array)d+=m(c[0],h.width),f+=m(c[1],h.height),u=null,p=null;else switch(c){case"left":d-=_,f+=O,u="right",p="middle";break;case"right":d+=_+l,f+=O,p="middle";break;case"top":d+=l/2,f-=_,u="center",p="bottom";break;case"bottom":d+=l/2,f+=M+_,u="center";break;case"inside":d+=l/2,f+=O,u="center",p="middle";break;case"insideLeft":d+=_,f+=O,p="middle";break;case"insideRight":d+=l-_,f+=O,u="right",p="middle";break;case"insideTop":d+=l/2,f+=_,u="center";break;case"insideBottom":d+=l/2,f+=M-_,u="center",p="bottom";break;case"insideTopLeft":d+=_,f+=_;break;case"insideTopRight":d+=l-_,f+=_,u="right";break;case"insideBottomLeft":d+=_,f+=M-_,p="bottom";break;case"insideBottomRight":d+=l-_,f+=M-_,u="right",p="bottom";break}return y=y||{},y.x=d,y.y=f,y.align=u,y.verticalAlign=p,y}},60479:function(Zt,pt,P){"use strict";var C=P(32892),k=P(41610),v=Math.min,F=Math.max,z=new k.Z,W=new k.Z,K=new k.Z,R=new k.Z,b=new k.Z,A=new k.Z,S=function(){function m(g,y,E,h){E<0&&(g=g+E,E=-E),h<0&&(y=y+h,h=-h),this.x=g,this.y=y,this.width=E,this.height=h}return m.prototype.union=function(g){var y=v(g.x,this.x),E=v(g.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=F(g.x+g.width,this.x+this.width)-y:this.width=g.width,isFinite(this.y)&&isFinite(this.height)?this.height=F(g.y+g.height,this.y+this.height)-E:this.height=g.height,this.x=y,this.y=E},m.prototype.applyTransform=function(g){m.applyTransform(this,this,g)},m.prototype.calculateTransform=function(g){var y=this,E=g.width/y.width,h=g.height/y.height,c=C.Ue();return C.Iu(c,c,[-y.x,-y.y]),C.bA(c,c,[E,h]),C.Iu(c,c,[g.x,g.y]),c},m.prototype.intersect=function(g,y){if(!g)return!1;g instanceof m||(g=m.create(g));var E=this,h=E.x,c=E.x+E.width,_=E.y,M=E.y+E.height,l=g.x,O=g.x+g.width,d=g.y,f=g.y+g.height,u=!(c<l||O<h||M<d||f<_);if(y){var p=Infinity,n=0,i=Math.abs(c-l),t=Math.abs(O-h),r=Math.abs(M-d),e=Math.abs(f-_),a=Math.min(i,t),s=Math.min(r,e);c<l||O<h?a>n&&(n=a,i<t?k.Z.set(A,-i,0):k.Z.set(A,t,0)):a<p&&(p=a,i<t?k.Z.set(b,i,0):k.Z.set(b,-t,0)),M<d||f<_?s>n&&(n=s,r<e?k.Z.set(A,0,-r):k.Z.set(A,0,e)):a<p&&(p=a,r<e?k.Z.set(b,0,r):k.Z.set(b,0,-e))}return y&&k.Z.copy(y,u?b:A),u},m.prototype.contain=function(g,y){var E=this;return g>=E.x&&g<=E.x+E.width&&y>=E.y&&y<=E.y+E.height},m.prototype.clone=function(){return new m(this.x,this.y,this.width,this.height)},m.prototype.copy=function(g){m.copy(this,g)},m.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},m.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},m.prototype.isZero=function(){return this.width===0||this.height===0},m.create=function(g){return new m(g.x,g.y,g.width,g.height)},m.copy=function(g,y){g.x=y.x,g.y=y.y,g.width=y.width,g.height=y.height},m.applyTransform=function(g,y,E){if(!E){g!==y&&m.copy(g,y);return}if(E[1]<1e-5&&E[1]>-1e-5&&E[2]<1e-5&&E[2]>-1e-5){var h=E[0],c=E[3],_=E[4],M=E[5];g.x=y.x*h+_,g.y=y.y*c+M,g.width=y.width*h,g.height=y.height*c,g.width<0&&(g.x+=g.width,g.width=-g.width),g.height<0&&(g.y+=g.height,g.height=-g.height);return}z.x=K.x=y.x,z.y=R.y=y.y,W.x=R.x=y.x+y.width,W.y=K.y=y.y+y.height,z.transform(E),R.transform(E),W.transform(E),K.transform(E),g.x=v(z.x,W.x,K.x,R.x),g.y=v(z.y,W.y,K.y,R.y);var l=F(z.x,W.x,K.x,R.x),O=F(z.y,W.y,K.y,R.y);g.width=l-g.x,g.height=O-g.y},m}();pt.Z=S},23510:function(Zt,pt){"use strict";var P=function(){function C(k){k&&(this._$eventProcessor=k)}return C.prototype.on=function(k,v,F,z){this._$handlers||(this._$handlers={});var W=this._$handlers;if(typeof v=="function"&&(z=F,F=v,v=null),!F||!k)return this;var K=this._$eventProcessor;v!=null&&K&&K.normalizeQuery&&(v=K.normalizeQuery(v)),W[k]||(W[k]=[]);for(var R=0;R<W[k].length;R++)if(W[k][R].h===F)return this;var b={h:F,query:v,ctx:z||this,callAtLast:F.zrEventfulCallAtLast},A=W[k].length-1,S=W[k][A];return S&&S.callAtLast?W[k].splice(A,0,b):W[k].push(b),this},C.prototype.isSilent=function(k){var v=this._$handlers;return!v||!v[k]||!v[k].length},C.prototype.off=function(k,v){var F=this._$handlers;if(!F)return this;if(!k)return this._$handlers={},this;if(v){if(F[k]){for(var z=[],W=0,K=F[k].length;W<K;W++)F[k][W].h!==v&&z.push(F[k][W]);F[k]=z}F[k]&&F[k].length===0&&delete F[k]}else delete F[k];return this},C.prototype.trigger=function(k){for(var v=[],F=1;F<arguments.length;F++)v[F-1]=arguments[F];if(!this._$handlers)return this;var z=this._$handlers[k],W=this._$eventProcessor;if(z)for(var K=v.length,R=z.length,b=0;b<R;b++){var A=z[b];if(!(W&&W.filter&&A.query!=null&&!W.filter(k,A.query)))switch(K){case 0:A.h.call(A.ctx);break;case 1:A.h.call(A.ctx,v[0]);break;case 2:A.h.call(A.ctx,v[0],v[1]);break;default:A.h.apply(A.ctx,v);break}}return W&&W.afterTrigger&&W.afterTrigger(k),this},C.prototype.triggerWithContext=function(k){for(var v=[],F=1;F<arguments.length;F++)v[F-1]=arguments[F];if(!this._$handlers)return this;var z=this._$handlers[k],W=this._$eventProcessor;if(z)for(var K=v.length,R=v[K-1],b=z.length,A=0;A<b;A++){var S=z[A];if(!(W&&W.filter&&S.query!=null&&!W.filter(k,S.query)))switch(K){case 0:S.h.call(R);break;case 1:S.h.call(R,v[0]);break;case 2:S.h.call(R,v[0],v[1]);break;default:S.h.apply(R,v.slice(1,K-1));break}}return W&&W.afterTrigger&&W.afterTrigger(k),this},C}();pt.Z=P},92528:function(Zt,pt,P){"use strict";var C=function(){function F(z){this.value=z}return F}(),k=function(){function F(){this._len=0}return F.prototype.insert=function(z){var W=new C(z);return this.insertEntry(W),W},F.prototype.insertEntry=function(z){this.head?(this.tail.next=z,z.prev=this.tail,z.next=null,this.tail=z):this.head=this.tail=z,this._len++},F.prototype.remove=function(z){var W=z.prev,K=z.next;W?W.next=K:this.head=K,K?K.prev=W:this.tail=W,z.next=z.prev=null,this._len--},F.prototype.len=function(){return this._len},F.prototype.clear=function(){this.head=this.tail=null,this._len=0},F}(),v=function(){function F(z){this._list=new k,this._maxSize=10,this._map={},this._maxSize=z}return F.prototype.put=function(z,W){var K=this._list,R=this._map,b=null;if(R[z]==null){var A=K.len(),S=this._lastRemovedEntry;if(A>=this._maxSize&&A>0){var m=K.head;K.remove(m),delete R[m.key],b=m.value,this._lastRemovedEntry=m}S?S.value=W:S=new C(W),S.key=z,K.insertEntry(S),R[z]=S}return b},F.prototype.get=function(z){var W=this._map[z],K=this._list;if(W!=null)return W!==K.tail&&(K.remove(W),K.insertEntry(W)),W.value},F.prototype.clear=function(){this._list.clear(),this._map={}},F.prototype.len=function(){return this._list.len()},F}();pt.ZP=v},41587:function(Zt,pt,P){"use strict";var C=P(41610),k=[0,0],v=[0,0],F=new C.Z,z=new C.Z,W=function(){function K(R,b){this._corners=[],this._axes=[],this._origin=[0,0];for(var A=0;A<4;A++)this._corners[A]=new C.Z;for(var A=0;A<2;A++)this._axes[A]=new C.Z;R&&this.fromBoundingRect(R,b)}return K.prototype.fromBoundingRect=function(R,b){var A=this._corners,S=this._axes,m=R.x,g=R.y,y=m+R.width,E=g+R.height;if(A[0].set(m,g),A[1].set(y,g),A[2].set(y,E),A[3].set(m,E),b)for(var h=0;h<4;h++)A[h].transform(b);C.Z.sub(S[0],A[1],A[0]),C.Z.sub(S[1],A[3],A[0]),S[0].normalize(),S[1].normalize();for(var h=0;h<2;h++)this._origin[h]=S[h].dot(A[0])},K.prototype.intersect=function(R,b){var A=!0,S=!b;return F.set(Infinity,Infinity),z.set(0,0),!this._intersectCheckOneSide(this,R,F,z,S,1)&&(A=!1,S)||!this._intersectCheckOneSide(R,this,F,z,S,-1)&&(A=!1,S)||S||C.Z.copy(b,A?F:z),A},K.prototype._intersectCheckOneSide=function(R,b,A,S,m,g){for(var y=!0,E=0;E<2;E++){var h=this._axes[E];if(this._getProjMinMaxOnAxis(E,R._corners,k),this._getProjMinMaxOnAxis(E,b._corners,v),k[1]<v[0]||k[0]>v[1]){if(y=!1,m)return y;var c=Math.abs(v[0]-k[1]),_=Math.abs(k[0]-v[1]);Math.min(c,_)>S.len()&&(c<_?C.Z.scale(S,h,-c*g):C.Z.scale(S,h,_*g))}else if(A){var c=Math.abs(v[0]-k[1]),_=Math.abs(k[0]-v[1]);Math.min(c,_)<A.len()&&(c<_?C.Z.scale(A,h,c*g):C.Z.scale(A,h,-_*g))}}return y},K.prototype._getProjMinMaxOnAxis=function(R,b,A){for(var S=this._axes[R],m=this._origin,g=b[0].dot(S)+m[R],y=g,E=g,h=1;h<b.length;h++){var c=b[h].dot(S)+m[R];y=Math.min(c,y),E=Math.max(c,E)}A[0]=y,A[1]=E},K}();pt.Z=W},14014:function(Zt,pt,P){"use strict";P.d(pt,{L:function(){return f}});var C=P(45280),k=P(60479),v=P(4990),F=P(3726),z=P(18554),W={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},K=[],R=[],b=[],A=[],S=[],m=[],g=Math.min,y=Math.max,E=Math.cos,h=Math.sin,c=Math.abs,_=Math.PI,M=_*2,l=typeof Float32Array!="undefined",O=[];function d(p){var n=Math.round(p/_*1e8)/1e8;return n%2*_}function f(p,n){var i=d(p[0]);i<0&&(i+=M);var t=i-p[0],r=p[1];r+=t,!n&&r-i>=M?r=i+M:n&&i-r>=M?r=i-M:!n&&i>r?r=i+(M-d(i-r)):n&&i<r&&(r=i-(M-d(r-i))),p[0]=i,p[1]=r}var u=function(){function p(n){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,n&&(this._saveData=!1),this._saveData&&(this.data=[])}return p.prototype.increaseVersion=function(){this._version++},p.prototype.getVersion=function(){return this._version},p.prototype.setScale=function(n,i,t){t=t||0,t>0&&(this._ux=c(t/v.KL/n)||0,this._uy=c(t/v.KL/i)||0)},p.prototype.setDPR=function(n){this.dpr=n},p.prototype.setContext=function(n){this._ctx=n},p.prototype.getContext=function(){return this._ctx},p.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},p.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},p.prototype.moveTo=function(n,i){return this._drawPendingPt(),this.addData(W.M,n,i),this._ctx&&this._ctx.moveTo(n,i),this._x0=n,this._y0=i,this._xi=n,this._yi=i,this},p.prototype.lineTo=function(n,i){var t=c(n-this._xi),r=c(i-this._yi),e=t>this._ux||r>this._uy;if(this.addData(W.L,n,i),this._ctx&&e&&this._ctx.lineTo(n,i),e)this._xi=n,this._yi=i,this._pendingPtDist=0;else{var a=t*t+r*r;a>this._pendingPtDist&&(this._pendingPtX=n,this._pendingPtY=i,this._pendingPtDist=a)}return this},p.prototype.bezierCurveTo=function(n,i,t,r,e,a){return this._drawPendingPt(),this.addData(W.C,n,i,t,r,e,a),this._ctx&&this._ctx.bezierCurveTo(n,i,t,r,e,a),this._xi=e,this._yi=a,this},p.prototype.quadraticCurveTo=function(n,i,t,r){return this._drawPendingPt(),this.addData(W.Q,n,i,t,r),this._ctx&&this._ctx.quadraticCurveTo(n,i,t,r),this._xi=t,this._yi=r,this},p.prototype.arc=function(n,i,t,r,e,a){this._drawPendingPt(),O[0]=r,O[1]=e,f(O,a),r=O[0],e=O[1];var s=e-r;return this.addData(W.A,n,i,t,t,r,s,0,a?0:1),this._ctx&&this._ctx.arc(n,i,t,r,e,a),this._xi=E(e)*t+n,this._yi=h(e)*t+i,this},p.prototype.arcTo=function(n,i,t,r,e){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(n,i,t,r,e),this},p.prototype.rect=function(n,i,t,r){return this._drawPendingPt(),this._ctx&&this._ctx.rect(n,i,t,r),this.addData(W.R,n,i,t,r),this},p.prototype.closePath=function(){this._drawPendingPt(),this.addData(W.Z);var n=this._ctx,i=this._x0,t=this._y0;return n&&n.closePath(),this._xi=i,this._yi=t,this},p.prototype.fill=function(n){n&&n.fill(),this.toStatic()},p.prototype.stroke=function(n){n&&n.stroke(),this.toStatic()},p.prototype.len=function(){return this._len},p.prototype.setData=function(n){var i=n.length;!(this.data&&this.data.length===i)&&l&&(this.data=new Float32Array(i));for(var t=0;t<i;t++)this.data[t]=n[t];this._len=i},p.prototype.appendPath=function(n){n instanceof Array||(n=[n]);for(var i=n.length,t=0,r=this._len,e=0;e<i;e++)t+=n[e].len();l&&this.data instanceof Float32Array&&(this.data=new Float32Array(r+t));for(var e=0;e<i;e++)for(var a=n[e].data,s=0;s<a.length;s++)this.data[r++]=a[s];this._len=r},p.prototype.addData=function(n,i,t,r,e,a,s,o,D){if(!!this._saveData){var B=this.data;this._len+arguments.length>B.length&&(this._expandData(),B=this.data);for(var I=0;I<arguments.length;I++)B[this._len++]=arguments[I]}},p.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},p.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var n=[],i=0;i<this._len;i++)n[i]=this.data[i];this.data=n}},p.prototype.toStatic=function(){if(!!this._saveData){this._drawPendingPt();var n=this.data;n instanceof Array&&(n.length=this._len,l&&this._len>11&&(this.data=new Float32Array(n)))}},p.prototype.getBoundingRect=function(){b[0]=b[1]=S[0]=S[1]=Number.MAX_VALUE,A[0]=A[1]=m[0]=m[1]=-Number.MAX_VALUE;var n=this.data,i=0,t=0,r=0,e=0,a;for(a=0;a<this._len;){var s=n[a++],o=a===1;switch(o&&(i=n[a],t=n[a+1],r=i,e=t),s){case W.M:i=r=n[a++],t=e=n[a++],S[0]=r,S[1]=e,m[0]=r,m[1]=e;break;case W.L:(0,F.u4)(i,t,n[a],n[a+1],S,m),i=n[a++],t=n[a++];break;case W.C:(0,F.H9)(i,t,n[a++],n[a++],n[a++],n[a++],n[a],n[a+1],S,m),i=n[a++],t=n[a++];break;case W.Q:(0,F.mJ)(i,t,n[a++],n[a++],n[a],n[a+1],S,m),i=n[a++],t=n[a++];break;case W.A:var D=n[a++],B=n[a++],I=n[a++],L=n[a++],U=n[a++],x=n[a++]+U;a+=1;var T=!n[a++];o&&(r=E(U)*I+D,e=h(U)*L+B),(0,F.qL)(D,B,I,L,U,x,T,S,m),i=E(x)*I+D,t=h(x)*L+B;break;case W.R:r=i=n[a++],e=t=n[a++];var w=n[a++],V=n[a++];(0,F.u4)(r,e,r+w,e+V,S,m);break;case W.Z:i=r,t=e;break}C.VV(b,b,S),C.Fp(A,A,m)}return a===0&&(b[0]=b[1]=A[0]=A[1]=0),new k.Z(b[0],b[1],A[0]-b[0],A[1]-b[1])},p.prototype._calculateLength=function(){var n=this.data,i=this._len,t=this._ux,r=this._uy,e=0,a=0,s=0,o=0;this._pathSegLen||(this._pathSegLen=[]);for(var D=this._pathSegLen,B=0,I=0,L=0;L<i;){var U=n[L++],x=L===1;x&&(e=n[L],a=n[L+1],s=e,o=a);var T=-1;switch(U){case W.M:e=s=n[L++],a=o=n[L++];break;case W.L:{var w=n[L++],V=n[L++],N=w-e,G=V-a;(c(N)>t||c(G)>r||L===i-1)&&(T=Math.sqrt(N*N+G*G),e=w,a=V);break}case W.C:{var Z=n[L++],H=n[L++],w=n[L++],V=n[L++],X=n[L++],ut=n[L++];T=(0,z.Ci)(e,a,Z,H,w,V,X,ut,10),e=X,a=ut;break}case W.Q:{var Z=n[L++],H=n[L++],w=n[L++],V=n[L++];T=(0,z.wQ)(e,a,Z,H,w,V,10),e=w,a=V;break}case W.A:var lt=n[L++],ot=n[L++],q=n[L++],tt=n[L++],j=n[L++],et=n[L++],ct=et+j;L+=1,x&&(s=E(j)*q+lt,o=h(j)*tt+ot),T=y(q,tt)*g(M,Math.abs(et)),e=E(ct)*q+lt,a=h(ct)*tt+ot;break;case W.R:{s=e=n[L++],o=a=n[L++];var _t=n[L++],St=n[L++];T=_t*2+St*2;break}case W.Z:{var N=s-e,G=o-a;T=Math.sqrt(N*N+G*G),e=s,a=o;break}}T>=0&&(D[I++]=T,B+=T)}return this._pathLen=B,B},p.prototype.rebuildPath=function(n,i){var t=this.data,r=this._ux,e=this._uy,a=this._len,s,o,D,B,I,L,U=i<1,x,T,w=0,V=0,N,G=0,Z,H;if(U&&(this._pathSegLen||this._calculateLength(),x=this._pathSegLen,T=this._pathLen,N=i*T,!N))return;t:for(var X=0;X<a;){var ut=t[X++],lt=X===1;switch(lt&&(D=t[X],B=t[X+1],s=D,o=B),ut!==W.L&&G>0&&(n.lineTo(Z,H),G=0),ut){case W.M:s=D=t[X++],o=B=t[X++],n.moveTo(D,B);break;case W.L:{I=t[X++],L=t[X++];var ot=c(I-D),q=c(L-B);if(ot>r||q>e){if(U){var tt=x[V++];if(w+tt>N){var j=(N-w)/tt;n.lineTo(D*(1-j)+I*j,B*(1-j)+L*j);break t}w+=tt}n.lineTo(I,L),D=I,B=L,G=0}else{var et=ot*ot+q*q;et>G&&(Z=I,H=L,G=et)}break}case W.C:{var ct=t[X++],_t=t[X++],St=t[X++],At=t[X++],Ct=t[X++],Et=t[X++];if(U){var tt=x[V++];if(w+tt>N){var j=(N-w)/tt;(0,z.Vz)(D,ct,St,Ct,j,K),(0,z.Vz)(B,_t,At,Et,j,R),n.bezierCurveTo(K[1],R[1],K[2],R[2],K[3],R[3]);break t}w+=tt}n.bezierCurveTo(ct,_t,St,At,Ct,Et),D=Ct,B=Et;break}case W.Q:{var ct=t[X++],_t=t[X++],St=t[X++],At=t[X++];if(U){var tt=x[V++];if(w+tt>N){var j=(N-w)/tt;(0,z.Lx)(D,ct,St,j,K),(0,z.Lx)(B,_t,At,j,R),n.quadraticCurveTo(K[1],R[1],K[2],R[2]);break t}w+=tt}n.quadraticCurveTo(ct,_t,St,At),D=St,B=At;break}case W.A:var Ot=t[X++],Wt=t[X++],Ut=t[X++],Bt=t[X++],Ht=t[X++],kt=t[X++],Xt=t[X++],Vt=!t[X++],st=Ut>Bt?Ut:Bt,gt=c(Ut-Bt)>.001,xt=Ht+kt,Rt=!1;if(U){var tt=x[V++];w+tt>N&&(xt=Ht+kt*(N-w)/tt,Rt=!0),w+=tt}if(gt&&n.ellipse?n.ellipse(Ot,Wt,Ut,Bt,Xt,Ht,xt,Vt):n.arc(Ot,Wt,st,Ht,xt,Vt),Rt)break t;lt&&(s=E(Ht)*Ut+Ot,o=h(Ht)*Bt+Wt),D=E(xt)*Ut+Ot,B=h(xt)*Bt+Wt;break;case W.R:s=D=t[X],o=B=t[X+1],I=t[X++],L=t[X++];var dt=t[X++],Mt=t[X++];if(U){var tt=x[V++];if(w+tt>N){var vt=N-w;n.moveTo(I,L),n.lineTo(I+g(vt,dt),L),vt-=dt,vt>0&&n.lineTo(I+dt,L+g(vt,Mt)),vt-=Mt,vt>0&&n.lineTo(I+y(dt-vt,0),L+Mt),vt-=dt,vt>0&&n.lineTo(I,L+y(Mt-vt,0));break t}w+=tt}n.rect(I,L,dt,Mt);break;case W.Z:if(U){var tt=x[V++];if(w+tt>N){var j=(N-w)/tt;n.lineTo(D*(1-j)+s*j,B*(1-j)+o*j);break t}w+=tt}n.closePath(),D=s,B=o}}},p.prototype.clone=function(){var n=new p,i=this.data;return n.data=i.slice?i.slice():Array.prototype.slice.call(i),n._len=this._len,n},p.CMD=W,p.initDefaultProps=function(){var n=p.prototype;n._saveData=!0,n._ux=0,n._uy=0,n._pendingPtDist=0,n._version=0}(),p}();pt.Z=u},41610:function(Zt,pt){"use strict";var P=function(){function C(k,v){this.x=k||0,this.y=v||0}return C.prototype.copy=function(k){return this.x=k.x,this.y=k.y,this},C.prototype.clone=function(){return new C(this.x,this.y)},C.prototype.set=function(k,v){return this.x=k,this.y=v,this},C.prototype.equal=function(k){return k.x===this.x&&k.y===this.y},C.prototype.add=function(k){return this.x+=k.x,this.y+=k.y,this},C.prototype.scale=function(k){this.x*=k,this.y*=k},C.prototype.scaleAndAdd=function(k,v){this.x+=k.x*v,this.y+=k.y*v},C.prototype.sub=function(k){return this.x-=k.x,this.y-=k.y,this},C.prototype.dot=function(k){return this.x*k.x+this.y*k.y},C.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},C.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},C.prototype.normalize=function(){var k=this.len();return this.x/=k,this.y/=k,this},C.prototype.distance=function(k){var v=this.x-k.x,F=this.y-k.y;return Math.sqrt(v*v+F*F)},C.prototype.distanceSquare=function(k){var v=this.x-k.x,F=this.y-k.y;return v*v+F*F},C.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},C.prototype.transform=function(k){if(!!k){var v=this.x,F=this.y;return this.x=k[0]*v+k[2]*F+k[4],this.y=k[1]*v+k[3]*F+k[5],this}},C.prototype.toArray=function(k){return k[0]=this.x,k[1]=this.y,k},C.prototype.fromArray=function(k){this.x=k[0],this.y=k[1]},C.set=function(k,v,F){k.x=v,k.y=F},C.copy=function(k,v){k.x=v.x,k.y=v.y},C.len=function(k){return Math.sqrt(k.x*k.x+k.y*k.y)},C.lenSquare=function(k){return k.x*k.x+k.y*k.y},C.dot=function(k,v){return k.x*v.x+k.y*v.y},C.add=function(k,v,F){k.x=v.x+F.x,k.y=v.y+F.y},C.sub=function(k,v,F){k.x=v.x-F.x,k.y=v.y-F.y},C.scale=function(k,v,F){k.x=v.x*F,k.y=v.y*F},C.scaleAndAdd=function(k,v,F,z){k.x=v.x+F.x*z,k.y=v.y+F.y*z},C.lerp=function(k,v,F,z){var W=1-z;k.x=W*v.x+z*F.x,k.y=W*v.y+z*F.y},C}();pt.Z=P},87411:function(Zt,pt,P){"use strict";P.d(pt,{dN:function(){return S},kY:function(){return m}});var C=P(32892),k=P(45280),v=C.yR,F=5e-5;function z(g){return g>F||g<-F}var W=[],K=[],R=C.Ue(),b=Math.abs,A=function(){function g(){}return g.prototype.getLocalTransform=function(y){return g.getLocalTransform(this,y)},g.prototype.setPosition=function(y){this.x=y[0],this.y=y[1]},g.prototype.setScale=function(y){this.scaleX=y[0],this.scaleY=y[1]},g.prototype.setSkew=function(y){this.skewX=y[0],this.skewY=y[1]},g.prototype.setOrigin=function(y){this.originX=y[0],this.originY=y[1]},g.prototype.needLocalTransform=function(){return z(this.rotation)||z(this.x)||z(this.y)||z(this.scaleX-1)||z(this.scaleY-1)||z(this.skewX)||z(this.skewY)},g.prototype.updateTransform=function(){var y=this.parent&&this.parent.transform,E=this.needLocalTransform(),h=this.transform;if(!(E||y)){h&&(v(h),this.invTransform=null);return}h=h||C.Ue(),E?this.getLocalTransform(h):v(h),y&&(E?C.dC(h,y,h):C.JG(h,y)),this.transform=h,this._resolveGlobalScaleRatio(h)},g.prototype._resolveGlobalScaleRatio=function(y){var E=this.globalScaleRatio;if(E!=null&&E!==1){this.getGlobalScale(W);var h=W[0]<0?-1:1,c=W[1]<0?-1:1,_=((W[0]-h)*E+h)/W[0]||0,M=((W[1]-c)*E+c)/W[1]||0;y[0]*=_,y[1]*=_,y[2]*=M,y[3]*=M}this.invTransform=this.invTransform||C.Ue(),C.U_(this.invTransform,y)},g.prototype.getComputedTransform=function(){for(var y=this,E=[];y;)E.push(y),y=y.parent;for(;y=E.pop();)y.updateTransform();return this.transform},g.prototype.setLocalTransform=function(y){if(!!y){var E=y[0]*y[0]+y[1]*y[1],h=y[2]*y[2]+y[3]*y[3],c=Math.atan2(y[1],y[0]),_=Math.PI/2+c-Math.atan2(y[3],y[2]);h=Math.sqrt(h)*Math.cos(_),E=Math.sqrt(E),this.skewX=_,this.skewY=0,this.rotation=-c,this.x=+y[4],this.y=+y[5],this.scaleX=E,this.scaleY=h,this.originX=0,this.originY=0}},g.prototype.decomposeTransform=function(){if(!!this.transform){var y=this.parent,E=this.transform;y&&y.transform&&(y.invTransform=y.invTransform||C.Ue(),C.dC(K,y.invTransform,E),E=K);var h=this.originX,c=this.originY;(h||c)&&(R[4]=h,R[5]=c,C.dC(K,E,R),K[4]-=h,K[5]-=c,E=K),this.setLocalTransform(E)}},g.prototype.getGlobalScale=function(y){var E=this.transform;return y=y||[],E?(y[0]=Math.sqrt(E[0]*E[0]+E[1]*E[1]),y[1]=Math.sqrt(E[2]*E[2]+E[3]*E[3]),E[0]<0&&(y[0]=-y[0]),E[3]<0&&(y[1]=-y[1]),y):(y[0]=1,y[1]=1,y)},g.prototype.transformCoordToLocal=function(y,E){var h=[y,E],c=this.invTransform;return c&&k.Ne(h,h,c),h},g.prototype.transformCoordToGlobal=function(y,E){var h=[y,E],c=this.transform;return c&&k.Ne(h,h,c),h},g.prototype.getLineScale=function(){var y=this.transform;return y&&b(y[0]-1)>1e-10&&b(y[3]-1)>1e-10?Math.sqrt(b(y[0]*y[3]-y[2]*y[1])):1},g.prototype.copyTransform=function(y){m(this,y)},g.getLocalTransform=function(y,E){E=E||[];var h=y.originX||0,c=y.originY||0,_=y.scaleX,M=y.scaleY,l=y.anchorX,O=y.anchorY,d=y.rotation||0,f=y.x,u=y.y,p=y.skewX?Math.tan(y.skewX):0,n=y.skewY?Math.tan(-y.skewY):0;if(h||c||l||O){var i=h+l,t=c+O;E[4]=-i*_-p*t*M,E[5]=-t*M-n*i*_}else E[4]=E[5]=0;return E[0]=_,E[3]=M,E[1]=n*_,E[2]=p*M,d&&C.U1(E,E,d),E[4]+=h+f,E[5]+=c+u,E},g.initDefaultProps=function(){var y=g.prototype;y.scaleX=y.scaleY=y.globalScaleRatio=1,y.x=y.y=y.originX=y.originY=y.skewX=y.skewY=y.rotation=y.anchorX=y.anchorY=0}(),g}(),S=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function m(g,y){for(var E=0;E<S.length;E++){var h=S[E];g[h]=y[h]}}pt.ZP=A},3726:function(Zt,pt,P){"use strict";P.d(pt,{zk:function(){return S},u4:function(){return m},H9:function(){return E},mJ:function(){return h},qL:function(){return c}});var C=P(45280),k=P(18554),v=Math.min,F=Math.max,z=Math.sin,W=Math.cos,K=Math.PI*2,R=C.Ue(),b=C.Ue(),A=C.Ue();function S(_,M,l){if(_.length!==0){for(var O=_[0],d=O[0],f=O[0],u=O[1],p=O[1],n=1;n<_.length;n++)O=_[n],d=v(d,O[0]),f=F(f,O[0]),u=v(u,O[1]),p=F(p,O[1]);M[0]=d,M[1]=u,l[0]=f,l[1]=p}}function m(_,M,l,O,d,f){d[0]=v(_,l),d[1]=v(M,O),f[0]=F(_,l),f[1]=F(M,O)}var g=[],y=[];function E(_,M,l,O,d,f,u,p,n,i){var t=k.pP,r=k.af,e=t(_,l,d,u,g);n[0]=Infinity,n[1]=Infinity,i[0]=-Infinity,i[1]=-Infinity;for(var a=0;a<e;a++){var s=r(_,l,d,u,g[a]);n[0]=v(s,n[0]),i[0]=F(s,i[0])}e=t(M,O,f,p,y);for(var a=0;a<e;a++){var o=r(M,O,f,p,y[a]);n[1]=v(o,n[1]),i[1]=F(o,i[1])}n[0]=v(_,n[0]),i[0]=F(_,i[0]),n[0]=v(u,n[0]),i[0]=F(u,i[0]),n[1]=v(M,n[1]),i[1]=F(M,i[1]),n[1]=v(p,n[1]),i[1]=F(p,i[1])}function h(_,M,l,O,d,f,u,p){var n=k.QC,i=k.Zm,t=F(v(n(_,l,d),1),0),r=F(v(n(M,O,f),1),0),e=i(_,l,d,t),a=i(M,O,f,r);u[0]=v(_,d,e),u[1]=v(M,f,a),p[0]=F(_,d,e),p[1]=F(M,f,a)}function c(_,M,l,O,d,f,u,p,n){var i=C.VV,t=C.Fp,r=Math.abs(d-f);if(r%K<1e-4&&r>1e-4){p[0]=_-l,p[1]=M-O,n[0]=_+l,n[1]=M+O;return}if(R[0]=W(d)*l+_,R[1]=z(d)*O+M,b[0]=W(f)*l+_,b[1]=z(f)*O+M,i(p,R,b),t(n,R,b),d=d%K,d<0&&(d=d+K),f=f%K,f<0&&(f=f+K),d>f&&!u?f+=K:d<f&&u&&(d+=K),u){var e=f;f=d,d=e}for(var a=0;a<f;a+=Math.PI/2)a>d&&(A[0]=W(a)*l+_,A[1]=z(a)*O+M,i(p,A,p),t(n,A,n))}},18554:function(Zt,pt,P){"use strict";P.d(pt,{af:function(){return g},X_:function(){return y},kD:function(){return E},pP:function(){return h},Vz:function(){return c},t1:function(){return _},Ci:function(){return M},Zm:function(){return l},AZ:function(){return O},Jz:function(){return d},QC:function(){return f},Lx:function(){return u},Wr:function(){return p},wQ:function(){return n}});var C=P(45280),k=Math.pow,v=Math.sqrt,F=1e-8,z=1e-4,W=v(3),K=1/3,R=(0,C.Ue)(),b=(0,C.Ue)(),A=(0,C.Ue)();function S(i){return i>-F&&i<F}function m(i){return i>F||i<-F}function g(i,t,r,e,a){var s=1-a;return s*s*(s*i+3*a*t)+a*a*(a*e+3*s*r)}function y(i,t,r,e,a){var s=1-a;return 3*(((t-i)*s+2*(r-t)*a)*s+(e-r)*a*a)}function E(i,t,r,e,a,s){var o=e+3*(t-r)-i,D=3*(r-t*2+i),B=3*(t-i),I=i-a,L=D*D-3*o*B,U=D*B-9*o*I,x=B*B-3*D*I,T=0;if(S(L)&&S(U))if(S(D))s[0]=0;else{var w=-B/D;w>=0&&w<=1&&(s[T++]=w)}else{var V=U*U-4*L*x;if(S(V)){var N=U/L,w=-D/o+N,G=-N/2;w>=0&&w<=1&&(s[T++]=w),G>=0&&G<=1&&(s[T++]=G)}else if(V>0){var Z=v(V),H=L*D+1.5*o*(-U+Z),X=L*D+1.5*o*(-U-Z);H<0?H=-k(-H,K):H=k(H,K),X<0?X=-k(-X,K):X=k(X,K);var w=(-D-(H+X))/(3*o);w>=0&&w<=1&&(s[T++]=w)}else{var ut=(2*L*D-3*o*U)/(2*v(L*L*L)),lt=Math.acos(ut)/3,ot=v(L),q=Math.cos(lt),w=(-D-2*ot*q)/(3*o),G=(-D+ot*(q+W*Math.sin(lt)))/(3*o),tt=(-D+ot*(q-W*Math.sin(lt)))/(3*o);w>=0&&w<=1&&(s[T++]=w),G>=0&&G<=1&&(s[T++]=G),tt>=0&&tt<=1&&(s[T++]=tt)}}return T}function h(i,t,r,e,a){var s=6*r-12*t+6*i,o=9*t+3*e-3*i-9*r,D=3*t-3*i,B=0;if(S(o)){if(m(s)){var I=-D/s;I>=0&&I<=1&&(a[B++]=I)}}else{var L=s*s-4*o*D;if(S(L))a[0]=-s/(2*o);else if(L>0){var U=v(L),I=(-s+U)/(2*o),x=(-s-U)/(2*o);I>=0&&I<=1&&(a[B++]=I),x>=0&&x<=1&&(a[B++]=x)}}return B}function c(i,t,r,e,a,s){var o=(t-i)*a+i,D=(r-t)*a+t,B=(e-r)*a+r,I=(D-o)*a+o,L=(B-D)*a+D,U=(L-I)*a+I;s[0]=i,s[1]=o,s[2]=I,s[3]=U,s[4]=U,s[5]=L,s[6]=B,s[7]=e}function _(i,t,r,e,a,s,o,D,B,I,L){var U,x=.005,T=Infinity,w,V,N,G;R[0]=B,R[1]=I;for(var Z=0;Z<1;Z+=.05)b[0]=g(i,r,a,o,Z),b[1]=g(t,e,s,D,Z),N=(0,C.WU)(R,b),N<T&&(U=Z,T=N);T=Infinity;for(var H=0;H<32&&!(x<z);H++)w=U-x,V=U+x,b[0]=g(i,r,a,o,w),b[1]=g(t,e,s,D,w),N=(0,C.WU)(b,R),w>=0&&N<T?(U=w,T=N):(A[0]=g(i,r,a,o,V),A[1]=g(t,e,s,D,V),G=(0,C.WU)(A,R),V<=1&&G<T?(U=V,T=G):x*=.5);return L&&(L[0]=g(i,r,a,o,U),L[1]=g(t,e,s,D,U)),v(T)}function M(i,t,r,e,a,s,o,D,B){for(var I=i,L=t,U=0,x=1/B,T=1;T<=B;T++){var w=T*x,V=g(i,r,a,o,w),N=g(t,e,s,D,w),G=V-I,Z=N-L;U+=Math.sqrt(G*G+Z*Z),I=V,L=N}return U}function l(i,t,r,e){var a=1-e;return a*(a*i+2*e*t)+e*e*r}function O(i,t,r,e){return 2*((1-e)*(t-i)+e*(r-t))}function d(i,t,r,e,a){var s=i-2*t+r,o=2*(t-i),D=i-e,B=0;if(S(s)){if(m(o)){var I=-D/o;I>=0&&I<=1&&(a[B++]=I)}}else{var L=o*o-4*s*D;if(S(L)){var I=-o/(2*s);I>=0&&I<=1&&(a[B++]=I)}else if(L>0){var U=v(L),I=(-o+U)/(2*s),x=(-o-U)/(2*s);I>=0&&I<=1&&(a[B++]=I),x>=0&&x<=1&&(a[B++]=x)}}return B}function f(i,t,r){var e=i+r-2*t;return e===0?.5:(i-t)/e}function u(i,t,r,e,a){var s=(t-i)*e+i,o=(r-t)*e+t,D=(o-s)*e+s;a[0]=i,a[1]=s,a[2]=D,a[3]=D,a[4]=o,a[5]=r}function p(i,t,r,e,a,s,o,D,B){var I,L=.005,U=Infinity;R[0]=o,R[1]=D;for(var x=0;x<1;x+=.05){b[0]=l(i,r,a,x),b[1]=l(t,e,s,x);var T=(0,C.WU)(R,b);T<U&&(I=x,U=T)}U=Infinity;for(var w=0;w<32&&!(L<z);w++){var V=I-L,N=I+L;b[0]=l(i,r,a,V),b[1]=l(t,e,s,V);var T=(0,C.WU)(b,R);if(V>=0&&T<U)I=V,U=T;else{A[0]=l(i,r,a,N),A[1]=l(t,e,s,N);var G=(0,C.WU)(A,R);N<=1&&G<U?(I=N,U=G):L*=.5}}return B&&(B[0]=l(i,r,a,I),B[1]=l(t,e,s,I)),v(U)}function n(i,t,r,e,a,s,o){for(var D=i,B=t,I=0,L=1/o,U=1;U<=o;U++){var x=U*L,T=l(i,r,a,x),w=l(t,e,s,x),V=T-D,N=w-B;I+=Math.sqrt(V*V+N*N),D=T,B=w}return I}},54058:function(Zt,pt,P){"use strict";P.d(pt,{F1:function(){return y},UK:function(){return S},A4:function(){return R},YB:function(){return K}});var C=P(66387),k=Math.log(2);function v(E,h,c,_,M,l){var O=_+"-"+M,d=E.length;if(l.hasOwnProperty(O))return l[O];if(h===1){var f=Math.round(Math.log((1<<d)-1&~M)/k);return E[c][f]}for(var u=_|1<<c,p=c+1;_&1<<p;)p++;for(var n=0,i=0,t=0;i<d;i++){var r=1<<i;r&M||(n+=(t%2?-1:1)*E[c][i]*v(E,h-1,p,u,M|r,l),t++)}return l[O]=n,n}function F(E,h){var c=[[E[0],E[1],1,0,0,0,-h[0]*E[0],-h[0]*E[1]],[0,0,0,E[0],E[1],1,-h[1]*E[0],-h[1]*E[1]],[E[2],E[3],1,0,0,0,-h[2]*E[2],-h[2]*E[3]],[0,0,0,E[2],E[3],1,-h[3]*E[2],-h[3]*E[3]],[E[4],E[5],1,0,0,0,-h[4]*E[4],-h[4]*E[5]],[0,0,0,E[4],E[5],1,-h[5]*E[4],-h[5]*E[5]],[E[6],E[7],1,0,0,0,-h[6]*E[6],-h[6]*E[7]],[0,0,0,E[6],E[7],1,-h[7]*E[6],-h[7]*E[7]]],_={},M=v(c,8,0,0,0,_);if(M!==0){for(var l=[],O=0;O<8;O++)for(var d=0;d<8;d++)l[d]==null&&(l[d]=0),l[d]+=((O+d)%2?-1:1)*v(c,7,O===0?1:0,1<<O,1<<d,_)/M*h[O];return function(f,u,p){var n=u*l[6]+p*l[7]+1;f[0]=(u*l[0]+p*l[1]+l[2])/n,f[1]=(u*l[3]+p*l[4]+l[5])/n}}}var z="___zrEVENTSAVED",W=[];function K(E,h,c,_,M){return R(W,h,_,M,!0)&&R(E,c,W[0],W[1])}function R(E,h,c,_,M){if(h.getBoundingClientRect&&C.Z.domSupported&&!S(h)){var l=h[z]||(h[z]={}),O=b(h,l),d=A(O,l,M);if(d)return d(E,c,_),!0}return!1}function b(E,h){var c=h.markers;if(c)return c;c=h.markers=[];for(var _=["left","right"],M=["top","bottom"],l=0;l<4;l++){var O=document.createElement("div"),d=O.style,f=l%2,u=(l>>1)%2;d.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",_[f]+":0",M[u]+":0",_[1-f]+":auto",M[1-u]+":auto",""].join("!important;"),E.appendChild(O),c.push(O)}return c}function A(E,h,c){for(var _=c?"invTrans":"trans",M=h[_],l=h.srcCoords,O=[],d=[],f=!0,u=0;u<4;u++){var p=E[u].getBoundingClientRect(),n=2*u,i=p.left,t=p.top;O.push(i,t),f=f&&l&&i===l[n]&&t===l[n+1],d.push(E[u].offsetLeft,E[u].offsetTop)}return f&&M?M:(h.srcCoords=O,h[_]=c?F(d,O):F(O,d))}function S(E){return E.nodeName.toUpperCase()==="CANVAS"}var m=/([&<>"'])/g,g={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function y(E){return E==null?"":(E+"").replace(m,function(h,c){return g[c]})}},66387:function(Zt,pt){"use strict";var P=function(){function F(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return F}(),C=function(){function F(){this.browser=new P,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window!="undefined"}return F}(),k=new C;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(k.wxa=!0,k.touchEventsSupported=!0):typeof document=="undefined"&&typeof self!="undefined"?k.worker=!0:typeof navigator=="undefined"||navigator.userAgent.indexOf("Node.js")===0?(k.node=!0,k.svgSupported=!0):v(navigator.userAgent,k);function v(F,z){var W=z.browser,K=F.match(/Firefox\/([\d.]+)/),R=F.match(/MSIE\s([\d.]+)/)||F.match(/Trident\/.+?rv:(([\d.]+))/),b=F.match(/Edge?\/([\d.]+)/),A=/micromessenger/i.test(F);K&&(W.firefox=!0,W.version=K[1]),R&&(W.ie=!0,W.version=R[1]),b&&(W.edge=!0,W.version=b[1],W.newEdge=+b[1].split(".")[0]>18),A&&(W.weChat=!0),z.svgSupported=typeof SVGRect!="undefined",z.touchEventsSupported="ontouchstart"in window&&!W.ie&&!W.edge,z.pointerEventsSupported="onpointerdown"in window&&(W.edge||W.ie&&+W.version>=11),z.domSupported=typeof document!="undefined";var S=document.documentElement.style;z.transform3dSupported=(W.ie&&"transition"in S||W.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in S)&&!("OTransition"in S),z.transformSupported=z.transform3dSupported||W.ie&&+W.version>=9}pt.Z=k},61158:function(Zt,pt,P){"use strict";P.d(pt,{eV:function(){return W},iP:function(){return R},OD:function(){return b},Oo:function(){return S},xg:function(){return m},sT:function(){return g},x1:function(){return y}});var C=P(66387),k=P(54058),v=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,F=[],z=C.Z.browser.firefox&&+C.Z.browser.version.split(".")[0]<39;function W(E,h,c,_){return c=c||{},_?K(E,h,c):z&&h.layerX!=null&&h.layerX!==h.offsetX?(c.zrX=h.layerX,c.zrY=h.layerY):h.offsetX!=null?(c.zrX=h.offsetX,c.zrY=h.offsetY):K(E,h,c),c}function K(E,h,c){if(C.Z.domSupported&&E.getBoundingClientRect){var _=h.clientX,M=h.clientY;if((0,k.UK)(E)){var l=E.getBoundingClientRect();c.zrX=_-l.left,c.zrY=M-l.top;return}else if((0,k.A4)(F,E,_,M)){c.zrX=F[0],c.zrY=F[1];return}}c.zrX=c.zrY=0}function R(E){return E||window.event}function b(E,h,c){if(h=R(h),h.zrX!=null)return h;var _=h.type,M=_&&_.indexOf("touch")>=0;if(M){var O=_!=="touchend"?h.targetTouches[0]:h.changedTouches[0];O&&W(E,O,h,c)}else{W(E,h,h,c);var l=A(h);h.zrDelta=l?l/120:-(h.detail||0)/3}var d=h.button;return h.which==null&&d!==void 0&&v.test(h.type)&&(h.which=d&1?1:d&2?3:d&4?2:0),h}function A(E){var h=E.wheelDelta;if(h)return h;var c=E.deltaX,_=E.deltaY;if(c==null||_==null)return h;var M=Math.abs(_!==0?_:c),l=_>0?-1:_<0?1:c>0?-1:1;return 3*M*l}function S(E,h,c,_){E.addEventListener(h,c,_)}function m(E,h,c,_){E.removeEventListener(h,c,_)}var g=function(E){E.preventDefault(),E.stopPropagation(),E.cancelBubble=!0};function y(E){return E.which===2||E.which===3}},32892:function(Zt,pt,P){"use strict";P.d(pt,{Ue:function(){return C},yR:function(){return k},JG:function(){return v},dC:function(){return F},Iu:function(){return z},U1:function(){return W},bA:function(){return K},U_:function(){return R}});function C(){return[1,0,0,1,0,0]}function k(A){return A[0]=1,A[1]=0,A[2]=0,A[3]=1,A[4]=0,A[5]=0,A}function v(A,S){return A[0]=S[0],A[1]=S[1],A[2]=S[2],A[3]=S[3],A[4]=S[4],A[5]=S[5],A}function F(A,S,m){var g=S[0]*m[0]+S[2]*m[1],y=S[1]*m[0]+S[3]*m[1],E=S[0]*m[2]+S[2]*m[3],h=S[1]*m[2]+S[3]*m[3],c=S[0]*m[4]+S[2]*m[5]+S[4],_=S[1]*m[4]+S[3]*m[5]+S[5];return A[0]=g,A[1]=y,A[2]=E,A[3]=h,A[4]=c,A[5]=_,A}function z(A,S,m){return A[0]=S[0],A[1]=S[1],A[2]=S[2],A[3]=S[3],A[4]=S[4]+m[0],A[5]=S[5]+m[1],A}function W(A,S,m,g){g===void 0&&(g=[0,0]);var y=S[0],E=S[2],h=S[4],c=S[1],_=S[3],M=S[5],l=Math.sin(m),O=Math.cos(m);return A[0]=y*O+c*l,A[1]=-y*l+c*O,A[2]=E*O+_*l,A[3]=-E*l+O*_,A[4]=O*(h-g[0])+l*(M-g[1])+g[0],A[5]=O*(M-g[1])-l*(h-g[0])+g[1],A}function K(A,S,m){var g=m[0],y=m[1];return A[0]=S[0]*g,A[1]=S[1]*y,A[2]=S[2]*g,A[3]=S[3]*y,A[4]=S[4]*g,A[5]=S[5]*y,A}function R(A,S){var m=S[0],g=S[2],y=S[4],E=S[1],h=S[3],c=S[5],_=m*h-E*g;return _?(_=1/_,A[0]=h*_,A[1]=-E*_,A[2]=-g*_,A[3]=m*_,A[4]=(g*c-h*y)*_,A[5]=(E*y-m*c)*_,A):null}function b(A){var S=C();return v(S,A),S}},23132:function(Zt,pt,P){"use strict";P.d(pt,{n5:function(){return C},rk:function(){return k},Uo:function(){return v},qW:function(){return b}});var C=12,k="sans-serif",v=C+"px "+k,F=20,z=100,W="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function K(S){var m={};if(typeof JSON=="undefined")return m;for(var g=0;g<S.length;g++){var y=String.fromCharCode(g+32),E=(S.charCodeAt(g)-F)/z;m[y]=E}return m}var R=K(W),b={createCanvas:function(){return typeof document!="undefined"&&document.createElement("canvas")},measureText:function(){var S,m;return function(g,y){if(!S){var E=b.createCanvas();S=E&&E.getContext("2d")}if(S)return m!==y&&(m=S.font=y||v),S.measureText(g);g=g||"",y=y||v;var h=/(\d+)px/.exec(y),c=h&&+h[1]||C,_=0;if(y.indexOf("mono")>=0)_=c*g.length;else for(var M=0;M<g.length;M++){var l=R[g[M]];_+=l==null?c:l*c}return{width:_}}}(),loadImage:function(S,m,g){var y=new Image;return y.onload=m,y.onerror=g,y.src=S,y}};function A(S){for(var m in b)S[m]&&(b[m]=S[m])}},19455:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return A}});var C=32,k=7;function v(S){for(var m=0;S>=C;)m|=S&1,S>>=1;return S+m}function F(S,m,g,y){var E=m+1;if(E===g)return 1;if(y(S[E++],S[m])<0){for(;E<g&&y(S[E],S[E-1])<0;)E++;z(S,m,E)}else for(;E<g&&y(S[E],S[E-1])>=0;)E++;return E-m}function z(S,m,g){for(g--;m<g;){var y=S[m];S[m++]=S[g],S[g--]=y}}function W(S,m,g,y,E){for(y===m&&y++;y<g;y++){for(var h=S[y],c=m,_=y,M;c<_;)M=c+_>>>1,E(h,S[M])<0?_=M:c=M+1;var l=y-c;switch(l){case 3:S[c+3]=S[c+2];case 2:S[c+2]=S[c+1];case 1:S[c+1]=S[c];break;default:for(;l>0;)S[c+l]=S[c+l-1],l--}S[c]=h}}function K(S,m,g,y,E,h){var c=0,_=0,M=1;if(h(S,m[g+E])>0){for(_=y-E;M<_&&h(S,m[g+E+M])>0;)c=M,M=(M<<1)+1,M<=0&&(M=_);M>_&&(M=_),c+=E,M+=E}else{for(_=E+1;M<_&&h(S,m[g+E-M])<=0;)c=M,M=(M<<1)+1,M<=0&&(M=_);M>_&&(M=_);var l=c;c=E-M,M=E-l}for(c++;c<M;){var O=c+(M-c>>>1);h(S,m[g+O])>0?c=O+1:M=O}return M}function R(S,m,g,y,E,h){var c=0,_=0,M=1;if(h(S,m[g+E])<0){for(_=E+1;M<_&&h(S,m[g+E-M])<0;)c=M,M=(M<<1)+1,M<=0&&(M=_);M>_&&(M=_);var l=c;c=E-M,M=E-l}else{for(_=y-E;M<_&&h(S,m[g+E+M])>=0;)c=M,M=(M<<1)+1,M<=0&&(M=_);M>_&&(M=_),c+=E,M+=E}for(c++;c<M;){var O=c+(M-c>>>1);h(S,m[g+O])<0?M=O:c=O+1}return M}function b(S,m){var g=k,y,E,h=0,c=[];y=[],E=[];function _(u,p){y[h]=u,E[h]=p,h+=1}function M(){for(;h>1;){var u=h-2;if(u>=1&&E[u-1]<=E[u]+E[u+1]||u>=2&&E[u-2]<=E[u]+E[u-1])E[u-1]<E[u+1]&&u--;else if(E[u]>E[u+1])break;O(u)}}function l(){for(;h>1;){var u=h-2;u>0&&E[u-1]<E[u+1]&&u--,O(u)}}function O(u){var p=y[u],n=E[u],i=y[u+1],t=E[u+1];E[u]=n+t,u===h-3&&(y[u+1]=y[u+2],E[u+1]=E[u+2]),h--;var r=R(S[i],S,p,n,0,m);p+=r,n-=r,n!==0&&(t=K(S[p+n-1],S,i,t,t-1,m),t!==0&&(n<=t?d(p,n,i,t):f(p,n,i,t)))}function d(u,p,n,i){var t=0;for(t=0;t<p;t++)c[t]=S[u+t];var r=0,e=n,a=u;if(S[a++]=S[e++],--i==0){for(t=0;t<p;t++)S[a+t]=c[r+t];return}if(p===1){for(t=0;t<i;t++)S[a+t]=S[e+t];S[a+i]=c[r];return}for(var s=g,o,D,B;;){o=0,D=0,B=!1;do if(m(S[e],c[r])<0){if(S[a++]=S[e++],D++,o=0,--i==0){B=!0;break}}else if(S[a++]=c[r++],o++,D=0,--p==1){B=!0;break}while((o|D)<s);if(B)break;do{if(o=R(S[e],c,r,p,0,m),o!==0){for(t=0;t<o;t++)S[a+t]=c[r+t];if(a+=o,r+=o,p-=o,p<=1){B=!0;break}}if(S[a++]=S[e++],--i==0){B=!0;break}if(D=K(c[r],S,e,i,0,m),D!==0){for(t=0;t<D;t++)S[a+t]=S[e+t];if(a+=D,e+=D,i-=D,i===0){B=!0;break}}if(S[a++]=c[r++],--p==1){B=!0;break}s--}while(o>=k||D>=k);if(B)break;s<0&&(s=0),s+=2}if(g=s,g<1&&(g=1),p===1){for(t=0;t<i;t++)S[a+t]=S[e+t];S[a+i]=c[r]}else{if(p===0)throw new Error;for(t=0;t<p;t++)S[a+t]=c[r+t]}}function f(u,p,n,i){var t=0;for(t=0;t<i;t++)c[t]=S[n+t];var r=u+p-1,e=i-1,a=n+i-1,s=0,o=0;if(S[a--]=S[r--],--p==0){for(s=a-(i-1),t=0;t<i;t++)S[s+t]=c[t];return}if(i===1){for(a-=p,r-=p,o=a+1,s=r+1,t=p-1;t>=0;t--)S[o+t]=S[s+t];S[a]=c[e];return}for(var D=g;;){var B=0,I=0,L=!1;do if(m(c[e],S[r])<0){if(S[a--]=S[r--],B++,I=0,--p==0){L=!0;break}}else if(S[a--]=c[e--],I++,B=0,--i==1){L=!0;break}while((B|I)<D);if(L)break;do{if(B=p-R(c[e],S,u,p,p-1,m),B!==0){for(a-=B,r-=B,p-=B,o=a+1,s=r+1,t=B-1;t>=0;t--)S[o+t]=S[s+t];if(p===0){L=!0;break}}if(S[a--]=c[e--],--i==1){L=!0;break}if(I=i-K(S[r],c,0,i,i-1,m),I!==0){for(a-=I,e-=I,i-=I,o=a+1,s=e+1,t=0;t<I;t++)S[o+t]=c[s+t];if(i<=1){L=!0;break}}if(S[a--]=S[r--],--p==0){L=!0;break}D--}while(B>=k||I>=k);if(L)break;D<0&&(D=0),D+=2}if(g=D,g<1&&(g=1),i===1){for(a-=p,r-=p,o=a+1,s=r+1,t=p-1;t>=0;t--)S[o+t]=S[s+t];S[a]=c[e]}else{if(i===0)throw new Error;for(s=a-(i-1),t=0;t<i;t++)S[s+t]=c[t]}}return{mergeRuns:M,forceMergeRuns:l,pushRun:_}}function A(S,m,g,y){g||(g=0),y||(y=S.length);var E=y-g;if(!(E<2)){var h=0;if(E<C){h=F(S,g,y,m),W(S,g,y,g+h,m);return}var c=b(S,m),_=v(E);do{if(h=F(S,g,y,m),h<_){var M=E;M>_&&(M=_),W(S,g,g+M,g+h,m),h=M}c.pushRun(g,h),c.mergeRuns(),E-=h,g+=h}while(E!==0);c.forceMergeRuns()}}},33051:function(Zt,pt,P){"use strict";P.d(pt,{M8:function(){return y},H:function(){return E},d9:function(){return h},TS:function(){return c},l7:function(){return M},ce:function(){return l},cq:function(){return d},XW:function(){return f},jB:function(){return u},zG:function(){return p},S6:function(){return n},UI:function(){return i},u4:function(){return t},hX:function(){return r},sE:function(){return e},XP:function(){return a},ak:function(){return o},WA:function(){return D},kJ:function(){return B},mf:function(){return I},HD:function(){return L},cd:function(){return U},hj:function(){return x},Kn:function(){return T},fU:function(){return V},Mh:function(){return N},Qq:function(){return G},dL:function(){return Z},Bu:function(){return X},Jv:function(){return ut},pD:function(){return lt},R1:function(){return ot},tP:function(){return q},MY:function(){return tt},hu:function(){return j},fy:function(){return et},s7:function(){return _t},kW:function(){return Wt},WW:function(){return Ut},nW:function(){return Bt},$j:function(){return Ht},RI:function(){return kt},ZT:function(){return Xt},I3:function(){return Vt}});var C=P(23132),k=t(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(st,gt){return st["[object "+gt+"]"]=!0,st},{}),v=t(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(st,gt){return st["[object "+gt+"Array]"]=!0,st},{}),F=Object.prototype.toString,z=Array.prototype,W=z.forEach,K=z.filter,R=z.slice,b=z.map,A=function(){}.constructor,S=A?A.prototype:null,m="__proto__",g=2311;function y(){return g++}function E(){for(var st=[],gt=0;gt<arguments.length;gt++)st[gt]=arguments[gt];typeof console!="undefined"&&console.error.apply(console,st)}function h(st){if(st==null||typeof st!="object")return st;var gt=st,xt=F.call(st);if(xt==="[object Array]"){if(!St(st)){gt=[];for(var Rt=0,dt=st.length;Rt<dt;Rt++)gt[Rt]=h(st[Rt])}}else if(v[xt]){if(!St(st)){var Mt=st.constructor;if(Mt.from)gt=Mt.from(st);else{gt=new Mt(st.length);for(var Rt=0,dt=st.length;Rt<dt;Rt++)gt[Rt]=st[Rt]}}}else if(!k[xt]&&!St(st)&&!N(st)){gt={};for(var vt in st)st.hasOwnProperty(vt)&&vt!==m&&(gt[vt]=h(st[vt]))}return gt}function c(st,gt,xt){if(!T(gt)||!T(st))return xt?h(gt):st;for(var Rt in gt)if(gt.hasOwnProperty(Rt)&&Rt!==m){var dt=st[Rt],Mt=gt[Rt];T(Mt)&&T(dt)&&!B(Mt)&&!B(dt)&&!N(Mt)&&!N(dt)&&!w(Mt)&&!w(dt)&&!St(Mt)&&!St(dt)?c(dt,Mt,xt):(xt||!(Rt in st))&&(st[Rt]=h(gt[Rt]))}return st}function _(st,gt){for(var xt=st[0],Rt=1,dt=st.length;Rt<dt;Rt++)xt=c(xt,st[Rt],gt);return xt}function M(st,gt){if(Object.assign)Object.assign(st,gt);else for(var xt in gt)gt.hasOwnProperty(xt)&&xt!==m&&(st[xt]=gt[xt]);return st}function l(st,gt,xt){for(var Rt=a(gt),dt=0;dt<Rt.length;dt++){var Mt=Rt[dt];(xt?gt[Mt]!=null:st[Mt]==null)&&(st[Mt]=gt[Mt])}return st}var O=C.qW.createCanvas;function d(st,gt){if(st){if(st.indexOf)return st.indexOf(gt);for(var xt=0,Rt=st.length;xt<Rt;xt++)if(st[xt]===gt)return xt}return-1}function f(st,gt){var xt=st.prototype;function Rt(){}Rt.prototype=gt.prototype,st.prototype=new Rt;for(var dt in xt)xt.hasOwnProperty(dt)&&(st.prototype[dt]=xt[dt]);st.prototype.constructor=st,st.superClass=gt}function u(st,gt,xt){if(st="prototype"in st?st.prototype:st,gt="prototype"in gt?gt.prototype:gt,Object.getOwnPropertyNames)for(var Rt=Object.getOwnPropertyNames(gt),dt=0;dt<Rt.length;dt++){var Mt=Rt[dt];Mt!=="constructor"&&(xt?gt[Mt]!=null:st[Mt]==null)&&(st[Mt]=gt[Mt])}else l(st,gt,xt)}function p(st){return!st||typeof st=="string"?!1:typeof st.length=="number"}function n(st,gt,xt){if(!!(st&&gt))if(st.forEach&&st.forEach===W)st.forEach(gt,xt);else if(st.length===+st.length)for(var Rt=0,dt=st.length;Rt<dt;Rt++)gt.call(xt,st[Rt],Rt,st);else for(var Mt in st)st.hasOwnProperty(Mt)&&gt.call(xt,st[Mt],Mt,st)}function i(st,gt,xt){if(!st)return[];if(!gt)return q(st);if(st.map&&st.map===b)return st.map(gt,xt);for(var Rt=[],dt=0,Mt=st.length;dt<Mt;dt++)Rt.push(gt.call(xt,st[dt],dt,st));return Rt}function t(st,gt,xt,Rt){if(!!(st&&gt)){for(var dt=0,Mt=st.length;dt<Mt;dt++)xt=gt.call(Rt,xt,st[dt],dt,st);return xt}}function r(st,gt,xt){if(!st)return[];if(!gt)return q(st);if(st.filter&&st.filter===K)return st.filter(gt,xt);for(var Rt=[],dt=0,Mt=st.length;dt<Mt;dt++)gt.call(xt,st[dt],dt,st)&&Rt.push(st[dt]);return Rt}function e(st,gt,xt){if(!!(st&&gt)){for(var Rt=0,dt=st.length;Rt<dt;Rt++)if(gt.call(xt,st[Rt],Rt,st))return st[Rt]}}function a(st){if(!st)return[];if(Object.keys)return Object.keys(st);var gt=[];for(var xt in st)st.hasOwnProperty(xt)&&gt.push(xt);return gt}function s(st,gt){for(var xt=[],Rt=2;Rt<arguments.length;Rt++)xt[Rt-2]=arguments[Rt];return function(){return st.apply(gt,xt.concat(R.call(arguments)))}}var o=S&&I(S.bind)?S.call.bind(S.bind):s;function D(st){for(var gt=[],xt=1;xt<arguments.length;xt++)gt[xt-1]=arguments[xt];return function(){return st.apply(this,gt.concat(R.call(arguments)))}}function B(st){return Array.isArray?Array.isArray(st):F.call(st)==="[object Array]"}function I(st){return typeof st=="function"}function L(st){return typeof st=="string"}function U(st){return F.call(st)==="[object String]"}function x(st){return typeof st=="number"}function T(st){var gt=typeof st;return gt==="function"||!!st&&gt==="object"}function w(st){return!!k[F.call(st)]}function V(st){return!!v[F.call(st)]}function N(st){return typeof st=="object"&&typeof st.nodeType=="number"&&typeof st.ownerDocument=="object"}function G(st){return st.colorStops!=null}function Z(st){return st.image!=null}function H(st){return F.call(st)==="[object RegExp]"}function X(st){return st!==st}function ut(){for(var st=[],gt=0;gt<arguments.length;gt++)st[gt]=arguments[gt];for(var xt=0,Rt=st.length;xt<Rt;xt++)if(st[xt]!=null)return st[xt]}function lt(st,gt){return st!=null?st:gt}function ot(st,gt,xt){return st!=null?st:gt!=null?gt:xt}function q(st){for(var gt=[],xt=1;xt<arguments.length;xt++)gt[xt-1]=arguments[xt];return R.apply(st,gt)}function tt(st){if(typeof st=="number")return[st,st,st,st];var gt=st.length;return gt===2?[st[0],st[1],st[0],st[1]]:gt===3?[st[0],st[1],st[2],st[1]]:st}function j(st,gt){if(!st)throw new Error(gt)}function et(st){return st==null?null:typeof st.trim=="function"?st.trim():st.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var ct="__ec_primitive__";function _t(st){st[ct]=!0}function St(st){return st[ct]}var At=function(){function st(){this.data={}}return st.prototype.delete=function(gt){var xt=this.has(gt);return xt&&delete this.data[gt],xt},st.prototype.has=function(gt){return this.data.hasOwnProperty(gt)},st.prototype.get=function(gt){return this.data[gt]},st.prototype.set=function(gt,xt){return this.data[gt]=xt,this},st.prototype.keys=function(){return a(this.data)},st.prototype.forEach=function(gt){var xt=this.data;for(var Rt in xt)xt.hasOwnProperty(Rt)&&gt(xt[Rt],Rt)},st}(),Ct=typeof Map=="function";function Et(){return Ct?new Map:new At}var Ot=function(){function st(gt){var xt=B(gt);this.data=Et();var Rt=this;gt instanceof st?gt.each(dt):gt&&n(gt,dt);function dt(Mt,vt){xt?Rt.set(Mt,vt):Rt.set(vt,Mt)}}return st.prototype.hasKey=function(gt){return this.data.has(gt)},st.prototype.get=function(gt){return this.data.get(gt)},st.prototype.set=function(gt,xt){return this.data.set(gt,xt),xt},st.prototype.each=function(gt,xt){this.data.forEach(function(Rt,dt){gt.call(xt,Rt,dt)})},st.prototype.keys=function(){var gt=this.data.keys();return Ct?Array.from(gt):gt},st.prototype.removeKey=function(gt){this.data.delete(gt)},st}();function Wt(st){return new Ot(st)}function Ut(st,gt){for(var xt=new st.constructor(st.length+gt.length),Rt=0;Rt<st.length;Rt++)xt[Rt]=st[Rt];for(var dt=st.length,Rt=0;Rt<gt.length;Rt++)xt[Rt+dt]=gt[Rt];return xt}function Bt(st,gt){var xt;if(Object.create)xt=Object.create(st);else{var Rt=function(){};Rt.prototype=st,xt=new Rt}return gt&&M(xt,gt),xt}function Ht(st){var gt=st.style;gt.webkitUserSelect="none",gt.userSelect="none",gt.webkitTapHighlightColor="rgba(0,0,0,0)",gt["-webkit-touch-callout"]="none"}function kt(st,gt){return st.hasOwnProperty(gt)}function Xt(){}var Vt=180/Math.PI},45280:function(Zt,pt,P){"use strict";P.d(pt,{Ue:function(){return C},d9:function(){return v},IH:function(){return z},lu:function(){return K},bA:function(){return E},Fv:function(){return h},TE:function(){return c},TK:function(){return _},WU:function(){return l},t7:function(){return d},Ne:function(){return f},VV:function(){return u},Fp:function(){return p}});function C(n,i){return n==null&&(n=0),i==null&&(i=0),[n,i]}function k(n,i){return n[0]=i[0],n[1]=i[1],n}function v(n){return[n[0],n[1]]}function F(n,i,t){return n[0]=i,n[1]=t,n}function z(n,i,t){return n[0]=i[0]+t[0],n[1]=i[1]+t[1],n}function W(n,i,t,r){return n[0]=i[0]+t[0]*r,n[1]=i[1]+t[1]*r,n}function K(n,i,t){return n[0]=i[0]-t[0],n[1]=i[1]-t[1],n}function R(n){return Math.sqrt(A(n))}var b=null;function A(n){return n[0]*n[0]+n[1]*n[1]}var S=null;function m(n,i,t){return n[0]=i[0]*t[0],n[1]=i[1]*t[1],n}function g(n,i,t){return n[0]=i[0]/t[0],n[1]=i[1]/t[1],n}function y(n,i){return n[0]*i[0]+n[1]*i[1]}function E(n,i,t){return n[0]=i[0]*t,n[1]=i[1]*t,n}function h(n,i){var t=R(i);return t===0?(n[0]=0,n[1]=0):(n[0]=i[0]/t,n[1]=i[1]/t),n}function c(n,i){return Math.sqrt((n[0]-i[0])*(n[0]-i[0])+(n[1]-i[1])*(n[1]-i[1]))}var _=c;function M(n,i){return(n[0]-i[0])*(n[0]-i[0])+(n[1]-i[1])*(n[1]-i[1])}var l=M;function O(n,i){return n[0]=-i[0],n[1]=-i[1],n}function d(n,i,t,r){return n[0]=i[0]+r*(t[0]-i[0]),n[1]=i[1]+r*(t[1]-i[1]),n}function f(n,i,t){var r=i[0],e=i[1];return n[0]=t[0]*r+t[2]*e+t[4],n[1]=t[1]*r+t[3]*e+t[5],n}function u(n,i,t){return n[0]=Math.min(i[0],t[0]),n[1]=Math.min(i[1],t[1]),n}function p(n,i,t){return n[0]=Math.max(i[0],t[0]),n[1]=Math.max(i[1],t[1]),n}},52776:function(Zt,pt,P){"use strict";var C=P(4311),k=P(4665),v=function(F){(0,C.ZT)(z,F);function z(){var W=F!==null&&F.apply(this,arguments)||this;return W.type="compound",W}return z.prototype._updatePathDirty=function(){for(var W=this.shape.paths,K=this.shapeChanged(),R=0;R<W.length;R++)K=K||W[R].shapeChanged();K&&this.dirtyShape()},z.prototype.beforeBrush=function(){this._updatePathDirty();for(var W=this.shape.paths||[],K=this.getGlobalScale(),R=0;R<W.length;R++)W[R].path||W[R].createPathProxy(),W[R].path.setScale(K[0],K[1],W[R].segmentIgnoreThreshold)},z.prototype.buildPath=function(W,K){for(var R=K.paths||[],b=0;b<R.length;b++)R[b].buildPath(W,R[b].shape,!0)},z.prototype.afterBrush=function(){for(var W=this.shape.paths||[],K=0;K<W.length;K++)W[K].pathUpdated()},z.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),k.ZP.prototype.getBoundingRect.call(this)},z}(k.ZP);pt.Z=v},7719:function(Zt,pt,P){"use strict";P.d(pt,{tj:function(){return K},ik:function(){return R}});var C=P(4311),k=P(85823),v=P(60479),F=P(33051),z=P(14414),W="__zr_style_"+Math.round(Math.random()*10),K={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},R={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};K[W]=!0;var b=["z","z2","invisible"],A=["invisible"],S=function(E){(0,C.ZT)(h,E);function h(c){return E.call(this,c)||this}return h.prototype._init=function(c){for(var _=(0,F.XP)(c),M=0;M<_.length;M++){var l=_[M];l==="style"?this.useStyle(c[l]):E.prototype.attrKV.call(this,l,c[l])}this.style||this.useStyle({})},h.prototype.beforeBrush=function(){},h.prototype.afterBrush=function(){},h.prototype.innerBeforeBrush=function(){},h.prototype.innerAfterBrush=function(){},h.prototype.shouldBePainted=function(c,_,M,l){var O=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&y(this,c,_)||O&&!O[0]&&!O[3])return!1;if(M&&this.__clipPaths){for(var d=0;d<this.__clipPaths.length;++d)if(this.__clipPaths[d].isZeroArea())return!1}if(l&&this.parent)for(var f=this.parent;f;){if(f.ignore)return!1;f=f.parent}return!0},h.prototype.contain=function(c,_){return this.rectContain(c,_)},h.prototype.traverse=function(c,_){c.call(_,this)},h.prototype.rectContain=function(c,_){var M=this.transformCoordToLocal(c,_),l=this.getBoundingRect();return l.contain(M[0],M[1])},h.prototype.getPaintRect=function(){var c=this._paintRect;if(!this._paintRect||this.__dirty){var _=this.transform,M=this.getBoundingRect(),l=this.style,O=l.shadowBlur||0,d=l.shadowOffsetX||0,f=l.shadowOffsetY||0;c=this._paintRect||(this._paintRect=new v.Z(0,0,0,0)),_?v.Z.applyTransform(c,M,_):c.copy(M),(O||d||f)&&(c.width+=O*2+Math.abs(d),c.height+=O*2+Math.abs(f),c.x=Math.min(c.x,c.x+d-O),c.y=Math.min(c.y,c.y+f-O));var u=this.dirtyRectTolerance;c.isZero()||(c.x=Math.floor(c.x-u),c.y=Math.floor(c.y-u),c.width=Math.ceil(c.width+1+u*2),c.height=Math.ceil(c.height+1+u*2))}return c},h.prototype.setPrevPaintRect=function(c){c?(this._prevPaintRect=this._prevPaintRect||new v.Z(0,0,0,0),this._prevPaintRect.copy(c)):this._prevPaintRect=null},h.prototype.getPrevPaintRect=function(){return this._prevPaintRect},h.prototype.animateStyle=function(c){return this.animate("style",c)},h.prototype.updateDuringAnimation=function(c){c==="style"?this.dirtyStyle():this.markRedraw()},h.prototype.attrKV=function(c,_){c!=="style"?E.prototype.attrKV.call(this,c,_):this.style?this.setStyle(_):this.useStyle(_)},h.prototype.setStyle=function(c,_){return typeof c=="string"?this.style[c]=_:(0,F.l7)(this.style,c),this.dirtyStyle(),this},h.prototype.dirtyStyle=function(c){c||this.markRedraw(),this.__dirty|=z.SE,this._rect&&(this._rect=null)},h.prototype.dirty=function(){this.dirtyStyle()},h.prototype.styleChanged=function(){return!!(this.__dirty&z.SE)},h.prototype.styleUpdated=function(){this.__dirty&=~z.SE},h.prototype.createStyle=function(c){return(0,F.nW)(K,c)},h.prototype.useStyle=function(c){c[W]||(c=this.createStyle(c)),this.__inHover?this.__hoverStyle=c:this.style=c,this.dirtyStyle()},h.prototype.isStyleObject=function(c){return c[W]},h.prototype._innerSaveToNormal=function(c){E.prototype._innerSaveToNormal.call(this,c);var _=this._normalState;c.style&&!_.style&&(_.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(c,_,b)},h.prototype._applyStateObj=function(c,_,M,l,O,d){E.prototype._applyStateObj.call(this,c,_,M,l,O,d);var f=!(_&&l),u;if(_&&_.style?O?l?u=_.style:(u=this._mergeStyle(this.createStyle(),M.style),this._mergeStyle(u,_.style)):(u=this._mergeStyle(this.createStyle(),l?this.style:M.style),this._mergeStyle(u,_.style)):f&&(u=M.style),u)if(O){var p=this.style;if(this.style=this.createStyle(f?{}:p),f)for(var n=(0,F.XP)(p),i=0;i<n.length;i++){var t=n[i];t in u&&(u[t]=u[t],this.style[t]=p[t])}for(var r=(0,F.XP)(u),i=0;i<r.length;i++){var t=r[i];this.style[t]=this.style[t]}this._transitionState(c,{style:u},d,this.getAnimationStyleProps())}else this.useStyle(u);for(var e=this.__inHover?A:b,i=0;i<e.length;i++){var t=e[i];_&&_[t]!=null?this[t]=_[t]:f&&M[t]!=null&&(this[t]=M[t])}},h.prototype._mergeStates=function(c){for(var _=E.prototype._mergeStates.call(this,c),M,l=0;l<c.length;l++){var O=c[l];O.style&&(M=M||{},this._mergeStyle(M,O.style))}return M&&(_.style=M),_},h.prototype._mergeStyle=function(c,_){return(0,F.l7)(c,_),c},h.prototype.getAnimationStyleProps=function(){return R},h.initDefaultProps=function(){var c=h.prototype;c.type="displayable",c.invisible=!1,c.z=0,c.z2=0,c.zlevel=0,c.culling=!1,c.cursor="pointer",c.rectHover=!1,c.incremental=!1,c._rect=null,c.dirtyRectTolerance=0,c.__dirty=z.YV|z.SE}(),h}(k.Z),m=new v.Z(0,0,0,0),g=new v.Z(0,0,0,0);function y(E,h,c){return m.copy(E.getBoundingRect()),E.transform&&m.applyTransform(E.transform),g.width=h,g.height=c,!m.intersect(g)}pt.ZP=S},31797:function(Zt,pt){"use strict";var P=function(){function C(k){this.colorStops=k||[]}return C.prototype.addColorStop=function(k,v){this.colorStops.push({offset:k,color:v})},C}();pt.Z=P},38154:function(Zt,pt,P){"use strict";var C=P(4311),k=P(33051),v=P(85823),F=P(60479),z=function(W){(0,C.ZT)(K,W);function K(R){var b=W.call(this)||this;return b.isGroup=!0,b._children=[],b.attr(R),b}return K.prototype.childrenRef=function(){return this._children},K.prototype.children=function(){return this._children.slice()},K.prototype.childAt=function(R){return this._children[R]},K.prototype.childOfName=function(R){for(var b=this._children,A=0;A<b.length;A++)if(b[A].name===R)return b[A]},K.prototype.childCount=function(){return this._children.length},K.prototype.add=function(R){return R&&R!==this&&R.parent!==this&&(this._children.push(R),this._doAdd(R)),this},K.prototype.addBefore=function(R,b){if(R&&R!==this&&R.parent!==this&&b&&b.parent===this){var A=this._children,S=A.indexOf(b);S>=0&&(A.splice(S,0,R),this._doAdd(R))}return this},K.prototype.replace=function(R,b){var A=k.cq(this._children,R);return A>=0&&this.replaceAt(b,A),this},K.prototype.replaceAt=function(R,b){var A=this._children,S=A[b];if(R&&R!==this&&R.parent!==this&&R!==S){A[b]=R,S.parent=null;var m=this.__zr;m&&S.removeSelfFromZr(m),this._doAdd(R)}return this},K.prototype._doAdd=function(R){R.parent&&R.parent.remove(R),R.parent=this;var b=this.__zr;b&&b!==R.__zr&&R.addSelfToZr(b),b&&b.refresh()},K.prototype.remove=function(R){var b=this.__zr,A=this._children,S=k.cq(A,R);return S<0?this:(A.splice(S,1),R.parent=null,b&&R.removeSelfFromZr(b),b&&b.refresh(),this)},K.prototype.removeAll=function(){for(var R=this._children,b=this.__zr,A=0;A<R.length;A++){var S=R[A];b&&S.removeSelfFromZr(b),S.parent=null}return R.length=0,this},K.prototype.eachChild=function(R,b){for(var A=this._children,S=0;S<A.length;S++){var m=A[S];R.call(b,m,S)}return this},K.prototype.traverse=function(R,b){for(var A=0;A<this._children.length;A++){var S=this._children[A],m=R.call(b,S);S.isGroup&&!m&&S.traverse(R,b)}return this},K.prototype.addSelfToZr=function(R){W.prototype.addSelfToZr.call(this,R);for(var b=0;b<this._children.length;b++){var A=this._children[b];A.addSelfToZr(R)}},K.prototype.removeSelfFromZr=function(R){W.prototype.removeSelfFromZr.call(this,R);for(var b=0;b<this._children.length;b++){var A=this._children[b];A.removeSelfFromZr(R)}},K.prototype.getBoundingRect=function(R){for(var b=new F.Z(0,0,0,0),A=R||this._children,S=[],m=null,g=0;g<A.length;g++){var y=A[g];if(!(y.ignore||y.invisible)){var E=y.getBoundingRect(),h=y.getLocalTransform(S);h?(F.Z.applyTransform(b,E,h),m=m||b.clone(),m.union(b)):(m=m||E.clone(),m.union(E))}}return m||b},K}(v.Z);z.prototype.type="group",pt.Z=z},44535:function(Zt,pt,P){"use strict";var C=P(4311),k=P(7719),v=P(60479),F=P(33051),z=(0,F.ce)({x:0,y:0},k.tj),W={style:(0,F.ce)({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},k.ik.style)};function K(b){return!!(b&&typeof b!="string"&&b.width&&b.height)}var R=function(b){(0,C.ZT)(A,b);function A(){return b!==null&&b.apply(this,arguments)||this}return A.prototype.createStyle=function(S){return(0,F.nW)(z,S)},A.prototype._getSize=function(S){var m=this.style,g=m[S];if(g!=null)return g;var y=K(m.image)?m.image:this.__image;if(!y)return 0;var E=S==="width"?"height":"width",h=m[E];return h==null?y[S]:y[S]/y[E]*h},A.prototype.getWidth=function(){return this._getSize("width")},A.prototype.getHeight=function(){return this._getSize("height")},A.prototype.getAnimationStyleProps=function(){return W},A.prototype.getBoundingRect=function(){var S=this.style;return this._rect||(this._rect=new v.Z(S.x||0,S.y||0,this.getWidth(),this.getHeight())),this._rect},A}(k.ZP);R.prototype.type="image",pt.ZP=R},74438:function(Zt,pt,P){"use strict";var C=P(4311),k=P(31797),v=function(F){(0,C.ZT)(z,F);function z(W,K,R,b,A,S){var m=F.call(this,A)||this;return m.x=W==null?0:W,m.y=K==null?0:K,m.x2=R==null?1:R,m.y2=b==null?0:b,m.type="linear",m.global=S||!1,m}return z}(k.Z);pt.Z=v},4665:function(Zt,pt,P){"use strict";P.d(pt,{$t:function(){return a},ZP:function(){return B}});var C=P(4311),k=P(7719),v=P(14014);function F(I,L,U,x,T,w,V){if(T===0)return!1;var N=T,G=0,Z=I;if(V>L+N&&V>x+N||V<L-N&&V<x-N||w>I+N&&w>U+N||w<I-N&&w<U-N)return!1;if(I!==U)G=(L-x)/(I-U),Z=(I*x-U*L)/(I-U);else return Math.abs(w-I)<=N/2;var H=G*w-V+Z,X=H*H/(G*G+1);return X<=N/2*N/2}var z=P(18554);function W(I,L,U,x,T,w,V,N,G,Z,H){if(G===0)return!1;var X=G;if(H>L+X&&H>x+X&&H>w+X&&H>N+X||H<L-X&&H<x-X&&H<w-X&&H<N-X||Z>I+X&&Z>U+X&&Z>T+X&&Z>V+X||Z<I-X&&Z<U-X&&Z<T-X&&Z<V-X)return!1;var ut=z.t1(I,L,U,x,T,w,V,N,Z,H,null);return ut<=X/2}function K(I,L,U,x,T,w,V,N,G){if(V===0)return!1;var Z=V;if(G>L+Z&&G>x+Z&&G>w+Z||G<L-Z&&G<x-Z&&G<w-Z||N>I+Z&&N>U+Z&&N>T+Z||N<I-Z&&N<U-Z&&N<T-Z)return!1;var H=(0,z.Wr)(I,L,U,x,T,w,N,G,null);return H<=Z/2}var R=Math.PI*2;function b(I){return I%=R,I<0&&(I+=R),I}var A=Math.PI*2;function S(I,L,U,x,T,w,V,N,G){if(V===0)return!1;var Z=V;N-=I,G-=L;var H=Math.sqrt(N*N+G*G);if(H-Z>U||H+Z<U)return!1;if(Math.abs(x-T)%A<1e-4)return!0;if(w){var X=x;x=b(T),T=b(X)}else x=b(x),T=b(T);x>T&&(T+=A);var ut=Math.atan2(G,N);return ut<0&&(ut+=A),ut>=x&&ut<=T||ut+A>=x&&ut+A<=T}function m(I,L,U,x,T,w){if(w>L&&w>x||w<L&&w<x||x===L)return 0;var V=(w-L)/(x-L),N=x<L?1:-1;(V===1||V===0)&&(N=x<L?.5:-.5);var G=V*(U-I)+I;return G===T?Infinity:G>T?N:0}var g=v.Z.CMD,y=Math.PI*2,E=1e-4;function h(I,L){return Math.abs(I-L)<E}var c=[-1,-1,-1],_=[-1,-1];function M(){var I=_[0];_[0]=_[1],_[1]=I}function l(I,L,U,x,T,w,V,N,G,Z){if(Z>L&&Z>x&&Z>w&&Z>N||Z<L&&Z<x&&Z<w&&Z<N)return 0;var H=z.kD(L,x,w,N,Z,c);if(H===0)return 0;for(var X=0,ut=-1,lt=void 0,ot=void 0,q=0;q<H;q++){var tt=c[q],j=tt===0||tt===1?.5:1,et=z.af(I,U,T,V,tt);et<G||(ut<0&&(ut=z.pP(L,x,w,N,_),_[1]<_[0]&&ut>1&&M(),lt=z.af(L,x,w,N,_[0]),ut>1&&(ot=z.af(L,x,w,N,_[1]))),ut===2?tt<_[0]?X+=lt<L?j:-j:tt<_[1]?X+=ot<lt?j:-j:X+=N<ot?j:-j:tt<_[0]?X+=lt<L?j:-j:X+=N<lt?j:-j)}return X}function O(I,L,U,x,T,w,V,N){if(N>L&&N>x&&N>w||N<L&&N<x&&N<w)return 0;var G=z.Jz(L,x,w,N,c);if(G===0)return 0;var Z=z.QC(L,x,w);if(Z>=0&&Z<=1){for(var H=0,X=z.Zm(L,x,w,Z),ut=0;ut<G;ut++){var lt=c[ut]===0||c[ut]===1?.5:1,ot=z.Zm(I,U,T,c[ut]);ot<V||(c[ut]<Z?H+=X<L?lt:-lt:H+=w<X?lt:-lt)}return H}else{var lt=c[0]===0||c[0]===1?.5:1,ot=z.Zm(I,U,T,c[0]);return ot<V?0:w<L?lt:-lt}}function d(I,L,U,x,T,w,V,N){if(N-=L,N>U||N<-U)return 0;var G=Math.sqrt(U*U-N*N);c[0]=-G,c[1]=G;var Z=Math.abs(x-T);if(Z<1e-4)return 0;if(Z>=y-1e-4){x=0,T=y;var H=w?1:-1;return V>=c[0]+I&&V<=c[1]+I?H:0}if(x>T){var X=x;x=T,T=X}x<0&&(x+=y,T+=y);for(var ut=0,lt=0;lt<2;lt++){var ot=c[lt];if(ot+I>V){var q=Math.atan2(N,ot),H=w?1:-1;q<0&&(q=y+q),(q>=x&&q<=T||q+y>=x&&q+y<=T)&&(q>Math.PI/2&&q<Math.PI*1.5&&(H=-H),ut+=H)}}return ut}function f(I,L,U,x,T){for(var w=I.data,V=I.len(),N=0,G=0,Z=0,H=0,X=0,ut,lt,ot=0;ot<V;){var q=w[ot++],tt=ot===1;switch(q===g.M&&ot>1&&(U||(N+=m(G,Z,H,X,x,T))),tt&&(G=w[ot],Z=w[ot+1],H=G,X=Z),q){case g.M:H=w[ot++],X=w[ot++],G=H,Z=X;break;case g.L:if(U){if(F(G,Z,w[ot],w[ot+1],L,x,T))return!0}else N+=m(G,Z,w[ot],w[ot+1],x,T)||0;G=w[ot++],Z=w[ot++];break;case g.C:if(U){if(W(G,Z,w[ot++],w[ot++],w[ot++],w[ot++],w[ot],w[ot+1],L,x,T))return!0}else N+=l(G,Z,w[ot++],w[ot++],w[ot++],w[ot++],w[ot],w[ot+1],x,T)||0;G=w[ot++],Z=w[ot++];break;case g.Q:if(U){if(K(G,Z,w[ot++],w[ot++],w[ot],w[ot+1],L,x,T))return!0}else N+=O(G,Z,w[ot++],w[ot++],w[ot],w[ot+1],x,T)||0;G=w[ot++],Z=w[ot++];break;case g.A:var j=w[ot++],et=w[ot++],ct=w[ot++],_t=w[ot++],St=w[ot++],At=w[ot++];ot+=1;var Ct=!!(1-w[ot++]);ut=Math.cos(St)*ct+j,lt=Math.sin(St)*_t+et,tt?(H=ut,X=lt):N+=m(G,Z,ut,lt,x,T);var Et=(x-j)*_t/ct+j;if(U){if(S(j,et,_t,St,St+At,Ct,L,Et,T))return!0}else N+=d(j,et,_t,St,St+At,Ct,Et,T);G=Math.cos(St+At)*ct+j,Z=Math.sin(St+At)*_t+et;break;case g.R:H=G=w[ot++],X=Z=w[ot++];var Ot=w[ot++],Wt=w[ot++];if(ut=H+Ot,lt=X+Wt,U){if(F(H,X,ut,X,L,x,T)||F(ut,X,ut,lt,L,x,T)||F(ut,lt,H,lt,L,x,T)||F(H,lt,H,X,L,x,T))return!0}else N+=m(ut,X,ut,lt,x,T),N+=m(H,lt,H,X,x,T);break;case g.Z:if(U){if(F(G,Z,H,X,L,x,T))return!0}else N+=m(G,Z,H,X,x,T);G=H,Z=X;break}}return!U&&!h(Z,X)&&(N+=m(G,Z,H,X,x,T)||0),N!==0}function u(I,L,U){return f(I,0,!1,L,U)}function p(I,L,U,x){return f(I,L,!0,U,x)}var n=P(33051),i=P(21092),t=P(4990),r=P(14414),e=P(87411),a=(0,n.ce)({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},k.tj),s={style:(0,n.ce)({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},k.ik.style)},o=e.dN.concat(["invisible","culling","z","z2","zlevel","parent"]),D=function(I){(0,C.ZT)(L,I);function L(U){return I.call(this,U)||this}return L.prototype.update=function(){var U=this;I.prototype.update.call(this);var x=this.style;if(x.decal){var T=this._decalEl=this._decalEl||new L;T.buildPath===L.prototype.buildPath&&(T.buildPath=function(G){U.buildPath(G,U.shape)}),T.silent=!0;var w=T.style;for(var V in x)w[V]!==x[V]&&(w[V]=x[V]);w.fill=x.fill?x.decal:null,w.decal=null,w.shadowColor=null,x.strokeFirst&&(w.stroke=null);for(var N=0;N<o.length;++N)T[o[N]]=this[o[N]];T.__dirty|=r.YV}else this._decalEl&&(this._decalEl=null)},L.prototype.getDecalElement=function(){return this._decalEl},L.prototype._init=function(U){var x=(0,n.XP)(U);this.shape=this.getDefaultShape();var T=this.getDefaultStyle();T&&this.useStyle(T);for(var w=0;w<x.length;w++){var V=x[w],N=U[V];V==="style"?this.style?(0,n.l7)(this.style,N):this.useStyle(N):V==="shape"?(0,n.l7)(this.shape,N):I.prototype.attrKV.call(this,V,N)}this.style||this.useStyle({})},L.prototype.getDefaultStyle=function(){return null},L.prototype.getDefaultShape=function(){return{}},L.prototype.canBeInsideText=function(){return this.hasFill()},L.prototype.getInsideTextFill=function(){var U=this.style.fill;if(U!=="none"){if((0,n.HD)(U)){var x=(0,i.L0)(U,0);return x>.5?t.vU:x>.2?t.iv:t.GD}else if(U)return t.GD}return t.vU},L.prototype.getInsideTextStroke=function(U){var x=this.style.fill;if((0,n.HD)(x)){var T=this.__zr,w=!!(T&&T.isDarkMode()),V=(0,i.L0)(U,0)<t.Ak;if(w===V)return x}},L.prototype.buildPath=function(U,x,T){},L.prototype.pathUpdated=function(){this.__dirty&=~r.RH},L.prototype.getUpdatedPathProxy=function(U){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,U),this.path},L.prototype.createPathProxy=function(){this.path=new v.Z(!1)},L.prototype.hasStroke=function(){var U=this.style,x=U.stroke;return!(x==null||x==="none"||!(U.lineWidth>0))},L.prototype.hasFill=function(){var U=this.style,x=U.fill;return x!=null&&x!=="none"},L.prototype.getBoundingRect=function(){var U=this._rect,x=this.style,T=!U;if(T){var w=!1;this.path||(w=!0,this.createPathProxy());var V=this.path;(w||this.__dirty&r.RH)&&(V.beginPath(),this.buildPath(V,this.shape,!1),this.pathUpdated()),U=V.getBoundingRect()}if(this._rect=U,this.hasStroke()&&this.path&&this.path.len()>0){var N=this._rectStroke||(this._rectStroke=U.clone());if(this.__dirty||T){N.copy(U);var G=x.strokeNoScale?this.getLineScale():1,Z=x.lineWidth;if(!this.hasFill()){var H=this.strokeContainThreshold;Z=Math.max(Z,H==null?4:H)}G>1e-10&&(N.width+=Z/G,N.height+=Z/G,N.x-=Z/G/2,N.y-=Z/G/2)}return N}return U},L.prototype.contain=function(U,x){var T=this.transformCoordToLocal(U,x),w=this.getBoundingRect(),V=this.style;if(U=T[0],x=T[1],w.contain(U,x)){var N=this.path;if(this.hasStroke()){var G=V.lineWidth,Z=V.strokeNoScale?this.getLineScale():1;if(Z>1e-10&&(this.hasFill()||(G=Math.max(G,this.strokeContainThreshold)),p(N,G/Z,U,x)))return!0}if(this.hasFill())return u(N,U,x)}return!1},L.prototype.dirtyShape=function(){this.__dirty|=r.RH,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},L.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},L.prototype.animateShape=function(U){return this.animate("shape",U)},L.prototype.updateDuringAnimation=function(U){U==="style"?this.dirtyStyle():U==="shape"?this.dirtyShape():this.markRedraw()},L.prototype.attrKV=function(U,x){U==="shape"?this.setShape(x):I.prototype.attrKV.call(this,U,x)},L.prototype.setShape=function(U,x){var T=this.shape;return T||(T=this.shape={}),typeof U=="string"?T[U]=x:(0,n.l7)(T,U),this.dirtyShape(),this},L.prototype.shapeChanged=function(){return!!(this.__dirty&r.RH)},L.prototype.createStyle=function(U){return(0,n.nW)(a,U)},L.prototype._innerSaveToNormal=function(U){I.prototype._innerSaveToNormal.call(this,U);var x=this._normalState;U.shape&&!x.shape&&(x.shape=(0,n.l7)({},this.shape))},L.prototype._applyStateObj=function(U,x,T,w,V,N){I.prototype._applyStateObj.call(this,U,x,T,w,V,N);var G=!(x&&w),Z;if(x&&x.shape?V?w?Z=x.shape:(Z=(0,n.l7)({},T.shape),(0,n.l7)(Z,x.shape)):(Z=(0,n.l7)({},w?this.shape:T.shape),(0,n.l7)(Z,x.shape)):G&&(Z=T.shape),Z)if(V){this.shape=(0,n.l7)({},this.shape);for(var H={},X=(0,n.XP)(Z),ut=0;ut<X.length;ut++){var lt=X[ut];typeof Z[lt]=="object"?this.shape[lt]=Z[lt]:H[lt]=Z[lt]}this._transitionState(U,{shape:H},N)}else this.shape=Z,this.dirtyShape()},L.prototype._mergeStates=function(U){for(var x=I.prototype._mergeStates.call(this,U),T,w=0;w<U.length;w++){var V=U[w];V.shape&&(T=T||{},this._mergeStyle(T,V.shape))}return T&&(x.shape=T),x},L.prototype.getAnimationStyleProps=function(){return s},L.prototype.isZeroArea=function(){return!1},L.extend=function(U){var x=function(w){(0,C.ZT)(V,w);function V(N){var G=w.call(this,N)||this;return U.init&&U.init.call(G,N),G}return V.prototype.getDefaultStyle=function(){return(0,n.d9)(U.style)},V.prototype.getDefaultShape=function(){return(0,n.d9)(U.shape)},V}(L);for(var T in U)typeof U[T]=="function"&&(x.prototype[T]=U[T]);return x},L.initDefaultProps=function(){var U=L.prototype;U.type="path",U.strokeContainThreshold=5,U.segmentIgnoreThreshold=0,U.subPixelOptimize=!1,U.autoBatch=!1,U.__dirty=r.YV|r.SE|r.RH}(),L}(k.ZP),B=D},71505:function(Zt,pt,P){"use strict";var C=P(4311),k=P(7719),v=P(80423),F=P(4665),z=P(33051),W=P(23132),K=(0,z.ce)({strokeFirst:!0,font:W.Uo,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},F.$t),R=function(b){(0,C.ZT)(A,b);function A(){return b!==null&&b.apply(this,arguments)||this}return A.prototype.hasStroke=function(){var S=this.style,m=S.stroke;return m!=null&&m!=="none"&&S.lineWidth>0},A.prototype.hasFill=function(){var S=this.style,m=S.fill;return m!=null&&m!=="none"},A.prototype.createStyle=function(S){return(0,z.nW)(K,S)},A.prototype.setBoundingRect=function(S){this._rect=S},A.prototype.getBoundingRect=function(){var S=this.style;if(!this._rect){var m=S.text;m!=null?m+="":m="";var g=(0,v.lP)(m,S.font,S.textAlign,S.textBaseline);if(g.x+=S.x||0,g.y+=S.y||0,this.hasStroke()){var y=S.lineWidth;g.x-=y/2,g.y-=y/2,g.width+=y,g.height+=y}this._rect=g}return this._rect},A.initDefaultProps=function(){var S=A.prototype;S.dirtyRectTolerance=10}(),A}(k.ZP);R.prototype.type="tspan",pt.Z=R},9074:function(Zt,pt,P){"use strict";P.d(pt,{ZP:function(){return N},Y1:function(){return B},VG:function(){return o}});var C=P(4311),k=P(8007),v=P(33051),F=P(80423),z=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function W(G,Z,H,X,ut){if(!Z)return"";var lt=(G+"").split(`
`);ut=K(Z,H,X,ut);for(var ot=0,q=lt.length;ot<q;ot++)lt[ot]=R(lt[ot],ut);return lt.join(`
`)}function K(G,Z,H,X){X=X||{};var ut=(0,v.l7)({},X);ut.font=Z,H=(0,v.pD)(H,"..."),ut.maxIterations=(0,v.pD)(X.maxIterations,2);var lt=ut.minChar=(0,v.pD)(X.minChar,0);ut.cnCharWidth=(0,F.dz)("\u56FD",Z);var ot=ut.ascCharWidth=(0,F.dz)("a",Z);ut.placeholder=(0,v.pD)(X.placeholder,"");for(var q=G=Math.max(0,G-1),tt=0;tt<lt&&q>=ot;tt++)q-=ot;var j=(0,F.dz)(H,Z);return j>q&&(H="",j=0),q=G-j,ut.ellipsis=H,ut.ellipsisWidth=j,ut.contentWidth=q,ut.containerWidth=G,ut}function R(G,Z){var H=Z.containerWidth,X=Z.font,ut=Z.contentWidth;if(!H)return"";var lt=(0,F.dz)(G,X);if(lt<=H)return G;for(var ot=0;;ot++){if(lt<=ut||ot>=Z.maxIterations){G+=Z.ellipsis;break}var q=ot===0?b(G,ut,Z.ascCharWidth,Z.cnCharWidth):lt>0?Math.floor(G.length*ut/lt):0;G=G.substr(0,q),lt=(0,F.dz)(G,X)}return G===""&&(G=Z.placeholder),G}function b(G,Z,H,X){for(var ut=0,lt=0,ot=G.length;lt<ot&&ut<Z;lt++){var q=G.charCodeAt(lt);ut+=0<=q&&q<=127?H:X}return lt}function A(G,Z){G!=null&&(G+="");var H=Z.overflow,X=Z.padding,ut=Z.font,lt=H==="truncate",ot=(0,F.Dp)(ut),q=(0,v.pD)(Z.lineHeight,ot),tt=!!Z.backgroundColor,j=Z.lineOverflow==="truncate",et=Z.width,ct;et!=null&&(H==="break"||H==="breakAll")?ct=G?M(G,Z.font,et,H==="breakAll",0).lines:[]:ct=G?G.split(`
`):[];var _t=ct.length*q,St=(0,v.pD)(Z.height,_t);if(_t>St&&j){var At=Math.floor(St/q);ct=ct.slice(0,At)}if(G&&lt&&et!=null)for(var Ct=K(et,ut,Z.ellipsis,{minChar:Z.truncateMinChar,placeholder:Z.placeholder}),Et=0;Et<ct.length;Et++)ct[Et]=R(ct[Et],Ct);for(var Ot=St,Wt=0,Et=0;Et<ct.length;Et++)Wt=Math.max((0,F.dz)(ct[Et],ut),Wt);et==null&&(et=Wt);var Ut=Wt;return X&&(Ot+=X[0]+X[2],Ut+=X[1]+X[3],et+=X[1]+X[3]),tt&&(Ut=et),{lines:ct,height:St,outerWidth:Ut,outerHeight:Ot,lineHeight:q,calculatedLineHeight:ot,contentWidth:Wt,contentHeight:_t,width:et}}var S=function(){function G(){}return G}(),m=function(){function G(Z){this.tokens=[],Z&&(this.tokens=Z)}return G}(),g=function(){function G(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]}return G}();function y(G,Z){var H=new g;if(G!=null&&(G+=""),!G)return H;for(var X=Z.width,ut=Z.height,lt=Z.overflow,ot=(lt==="break"||lt==="breakAll")&&X!=null?{width:X,accumWidth:0,breakAll:lt==="breakAll"}:null,q=z.lastIndex=0,tt;(tt=z.exec(G))!=null;){var j=tt.index;j>q&&E(H,G.substring(q,j),Z,ot),E(H,tt[2],Z,ot,tt[1]),q=z.lastIndex}q<G.length&&E(H,G.substring(q,G.length),Z,ot);var et=[],ct=0,_t=0,St=Z.padding,At=lt==="truncate",Ct=Z.lineOverflow==="truncate";function Et(Q,ft,Tt){Q.width=ft,Q.lineHeight=Tt,ct+=Tt,_t=Math.max(_t,ft)}t:for(var Ot=0;Ot<H.lines.length;Ot++){for(var Wt=H.lines[Ot],Ut=0,Bt=0,Ht=0;Ht<Wt.tokens.length;Ht++){var kt=Wt.tokens[Ht],Xt=kt.styleName&&Z.rich[kt.styleName]||{},Vt=kt.textPadding=Xt.padding,st=Vt?Vt[1]+Vt[3]:0,gt=kt.font=Xt.font||Z.font;kt.contentHeight=(0,F.Dp)(gt);var xt=(0,v.pD)(Xt.height,kt.contentHeight);if(kt.innerHeight=xt,Vt&&(xt+=Vt[0]+Vt[2]),kt.height=xt,kt.lineHeight=(0,v.R1)(Xt.lineHeight,Z.lineHeight,xt),kt.align=Xt&&Xt.align||Z.align,kt.verticalAlign=Xt&&Xt.verticalAlign||"middle",Ct&&ut!=null&&ct+kt.lineHeight>ut){Ht>0?(Wt.tokens=Wt.tokens.slice(0,Ht),Et(Wt,Bt,Ut),H.lines=H.lines.slice(0,Ot+1)):H.lines=H.lines.slice(0,Ot);break t}var Rt=Xt.width,dt=Rt==null||Rt==="auto";if(typeof Rt=="string"&&Rt.charAt(Rt.length-1)==="%")kt.percentWidth=Rt,et.push(kt),kt.contentWidth=(0,F.dz)(kt.text,gt);else{if(dt){var Mt=Xt.backgroundColor,vt=Mt&&Mt.image;vt&&(vt=k.ko(vt),k.v5(vt)&&(kt.width=Math.max(kt.width,vt.width*xt/vt.height)))}var Y=At&&X!=null?X-Bt:null;Y!=null&&Y<kt.width?!dt||Y<st?(kt.text="",kt.width=kt.contentWidth=0):(kt.text=W(kt.text,Y-st,gt,Z.ellipsis,{minChar:Z.truncateMinChar}),kt.width=kt.contentWidth=(0,F.dz)(kt.text,gt)):kt.contentWidth=(0,F.dz)(kt.text,gt)}kt.width+=st,Bt+=kt.width,Xt&&(Ut=Math.max(Ut,kt.lineHeight))}Et(Wt,Bt,Ut)}H.outerWidth=H.width=(0,v.pD)(X,_t),H.outerHeight=H.height=(0,v.pD)(ut,ct),H.contentHeight=ct,H.contentWidth=_t,St&&(H.outerWidth+=St[1]+St[3],H.outerHeight+=St[0]+St[2]);for(var Ot=0;Ot<et.length;Ot++){var kt=et[Ot],$=kt.percentWidth;kt.width=parseInt($,10)/100*H.width}return H}function E(G,Z,H,X,ut){var lt=Z==="",ot=ut&&H.rich[ut]||{},q=G.lines,tt=ot.font||H.font,j=!1,et,ct;if(X){var _t=ot.padding,St=_t?_t[1]+_t[3]:0;if(ot.width!=null&&ot.width!=="auto"){var At=(0,F.GM)(ot.width,X.width)+St;q.length>0&&At+X.accumWidth>X.width&&(et=Z.split(`
`),j=!0),X.accumWidth=At}else{var Ct=M(Z,tt,X.width,X.breakAll,X.accumWidth);X.accumWidth=Ct.accumWidth+St,ct=Ct.linesWidths,et=Ct.lines}}else et=Z.split(`
`);for(var Et=0;Et<et.length;Et++){var Ot=et[Et],Wt=new S;if(Wt.styleName=ut,Wt.text=Ot,Wt.isLineHolder=!Ot&&!lt,typeof ot.width=="number"?Wt.width=ot.width:Wt.width=ct?ct[Et]:(0,F.dz)(Ot,tt),!Et&&!j){var Ut=(q[q.length-1]||(q[0]=new m)).tokens,Bt=Ut.length;Bt===1&&Ut[0].isLineHolder?Ut[0]=Wt:(Ot||!Bt||lt)&&Ut.push(Wt)}else q.push(new m([Wt]))}}function h(G){var Z=G.charCodeAt(0);return Z>=32&&Z<=591||Z>=880&&Z<=4351||Z>=4608&&Z<=5119||Z>=7680&&Z<=8303}var c=(0,v.u4)(",&?/;] ".split(""),function(G,Z){return G[Z]=!0,G},{});function _(G){return h(G)?!!c[G]:!0}function M(G,Z,H,X,ut){for(var lt=[],ot=[],q="",tt="",j=0,et=0,ct=0;ct<G.length;ct++){var _t=G.charAt(ct);if(_t===`
`){tt&&(q+=tt,et+=j),lt.push(q),ot.push(et),q="",tt="",j=0,et=0;continue}var St=(0,F.dz)(_t,Z),At=X?!1:!_(_t);if(lt.length?et+St>H:ut+et+St>H){et?(q||tt)&&(At?(q||(q=tt,tt="",j=0,et=j),lt.push(q),ot.push(et-j),tt+=_t,j+=St,q="",et=j):(tt&&(q+=tt,tt="",j=0),lt.push(q),ot.push(et),q=_t,et=St)):At?(lt.push(tt),ot.push(j),tt=_t,j=St):(lt.push(_t),ot.push(St));continue}et+=St,At?(tt+=_t,j+=St):(tt&&(q+=tt,tt="",j=0),q+=_t)}return!lt.length&&!q&&(q=G,tt="",j=0),tt&&(q+=tt),q&&(lt.push(q),ot.push(et)),lt.length===1&&(et+=ut),{accumWidth:et,lines:lt,linesWidths:ot}}var l=P(71505),O=P(44535),d=P(35151),f=P(60479),u=P(7719),p=P(23132),n={fill:"#000"},i=2,t={style:(0,v.ce)({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},u.ik.style)},r=function(G){(0,C.ZT)(Z,G);function Z(H){var X=G.call(this)||this;return X.type="text",X._children=[],X._defaultStyle=n,X.attr(H),X}return Z.prototype.childrenRef=function(){return this._children},Z.prototype.update=function(){G.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var H=0;H<this._children.length;H++){var X=this._children[H];X.zlevel=this.zlevel,X.z=this.z,X.z2=this.z2,X.culling=this.culling,X.cursor=this.cursor,X.invisible=this.invisible}},Z.prototype.updateTransform=function(){var H=this.innerTransformable;H?(H.updateTransform(),H.transform&&(this.transform=H.transform)):G.prototype.updateTransform.call(this)},Z.prototype.getLocalTransform=function(H){var X=this.innerTransformable;return X?X.getLocalTransform(H):G.prototype.getLocalTransform.call(this,H)},Z.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),G.prototype.getComputedTransform.call(this)},Z.prototype._updateSubTexts=function(){this._childCursor=0,I(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Z.prototype.addSelfToZr=function(H){G.prototype.addSelfToZr.call(this,H);for(var X=0;X<this._children.length;X++)this._children[X].__zr=H},Z.prototype.removeSelfFromZr=function(H){G.prototype.removeSelfFromZr.call(this,H);for(var X=0;X<this._children.length;X++)this._children[X].__zr=null},Z.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var H=new f.Z(0,0,0,0),X=this._children,ut=[],lt=null,ot=0;ot<X.length;ot++){var q=X[ot],tt=q.getBoundingRect(),j=q.getLocalTransform(ut);j?(H.copy(tt),H.applyTransform(j),lt=lt||H.clone(),lt.union(H)):(lt=lt||tt.clone(),lt.union(tt))}this._rect=lt||H}return this._rect},Z.prototype.setDefaultTextStyle=function(H){this._defaultStyle=H||n},Z.prototype.setTextContent=function(H){},Z.prototype._mergeStyle=function(H,X){if(!X)return H;var ut=X.rich,lt=H.rich||ut&&{};return(0,v.l7)(H,X),ut&&lt?(this._mergeRich(lt,ut),H.rich=lt):lt&&(H.rich=lt),H},Z.prototype._mergeRich=function(H,X){for(var ut=(0,v.XP)(X),lt=0;lt<ut.length;lt++){var ot=ut[lt];H[ot]=H[ot]||{},(0,v.l7)(H[ot],X[ot])}},Z.prototype.getAnimationStyleProps=function(){return t},Z.prototype._getOrCreateChild=function(H){var X=this._children[this._childCursor];return(!X||!(X instanceof H))&&(X=new H),this._children[this._childCursor++]=X,X.__zr=this.__zr,X.parent=this,X},Z.prototype._updatePlainTexts=function(){var H=this.style,X=H.font||p.Uo,ut=H.padding,lt=w(H),ot=A(lt,H),q=V(H),tt=!!H.backgroundColor,j=ot.outerHeight,et=ot.outerWidth,ct=ot.contentWidth,_t=ot.lines,St=ot.lineHeight,At=this._defaultStyle,Ct=H.x||0,Et=H.y||0,Ot=H.align||At.align||"left",Wt=H.verticalAlign||At.verticalAlign||"top",Ut=Ct,Bt=(0,F.mU)(Et,ot.contentHeight,Wt);if(q||ut){var Ht=(0,F.M3)(Ct,et,Ot),kt=(0,F.mU)(Et,j,Wt);q&&this._renderBackground(H,H,Ht,kt,et,j)}Bt+=St/2,ut&&(Ut=T(Ct,Ot,ut),Wt==="top"?Bt+=ut[0]:Wt==="bottom"&&(Bt-=ut[2]));for(var Xt=0,Vt=!1,st=x("fill"in H?H.fill:(Vt=!0,At.fill)),gt=U("stroke"in H?H.stroke:!tt&&(!At.autoStroke||Vt)?(Xt=i,At.stroke):null),xt=H.textShadowBlur>0,Rt=H.width!=null&&(H.overflow==="truncate"||H.overflow==="break"||H.overflow==="breakAll"),dt=ot.calculatedLineHeight,Mt=0;Mt<_t.length;Mt++){var vt=this._getOrCreateChild(l.Z),Y=vt.createStyle();vt.useStyle(Y),Y.text=_t[Mt],Y.x=Ut,Y.y=Bt,Ot&&(Y.textAlign=Ot),Y.textBaseline="middle",Y.opacity=H.opacity,Y.strokeFirst=!0,xt&&(Y.shadowBlur=H.textShadowBlur||0,Y.shadowColor=H.textShadowColor||"transparent",Y.shadowOffsetX=H.textShadowOffsetX||0,Y.shadowOffsetY=H.textShadowOffsetY||0),Y.stroke=gt,Y.fill=st,gt&&(Y.lineWidth=H.lineWidth||Xt,Y.lineDash=H.lineDash,Y.lineDashOffset=H.lineDashOffset||0),Y.font=X,D(Y,H),Bt+=St,Rt&&vt.setBoundingRect(new f.Z((0,F.M3)(Y.x,H.width,Y.textAlign),(0,F.mU)(Y.y,dt,Y.textBaseline),ct,dt))}},Z.prototype._updateRichTexts=function(){var H=this.style,X=w(H),ut=y(X,H),lt=ut.width,ot=ut.outerWidth,q=ut.outerHeight,tt=H.padding,j=H.x||0,et=H.y||0,ct=this._defaultStyle,_t=H.align||ct.align,St=H.verticalAlign||ct.verticalAlign,At=(0,F.M3)(j,ot,_t),Ct=(0,F.mU)(et,q,St),Et=At,Ot=Ct;tt&&(Et+=tt[3],Ot+=tt[0]);var Wt=Et+lt;V(H)&&this._renderBackground(H,H,At,Ct,ot,q);for(var Ut=!!H.backgroundColor,Bt=0;Bt<ut.lines.length;Bt++){for(var Ht=ut.lines[Bt],kt=Ht.tokens,Xt=kt.length,Vt=Ht.lineHeight,st=Ht.width,gt=0,xt=Et,Rt=Wt,dt=Xt-1,Mt=void 0;gt<Xt&&(Mt=kt[gt],!Mt.align||Mt.align==="left");)this._placeToken(Mt,H,Vt,Ot,xt,"left",Ut),st-=Mt.width,xt+=Mt.width,gt++;for(;dt>=0&&(Mt=kt[dt],Mt.align==="right");)this._placeToken(Mt,H,Vt,Ot,Rt,"right",Ut),st-=Mt.width,Rt-=Mt.width,dt--;for(xt+=(lt-(xt-Et)-(Wt-Rt)-st)/2;gt<=dt;)Mt=kt[gt],this._placeToken(Mt,H,Vt,Ot,xt+Mt.width/2,"center",Ut),xt+=Mt.width,gt++;Ot+=Vt}},Z.prototype._placeToken=function(H,X,ut,lt,ot,q,tt){var j=X.rich[H.styleName]||{};j.text=H.text;var et=H.verticalAlign,ct=lt+ut/2;et==="top"?ct=lt+H.height/2:et==="bottom"&&(ct=lt+ut-H.height/2);var _t=!H.isLineHolder&&V(j);_t&&this._renderBackground(j,X,q==="right"?ot-H.width:q==="center"?ot-H.width/2:ot,ct-H.height/2,H.width,H.height);var St=!!j.backgroundColor,At=H.textPadding;At&&(ot=T(ot,q,At),ct-=H.height/2-At[0]-H.innerHeight/2);var Ct=this._getOrCreateChild(l.Z),Et=Ct.createStyle();Ct.useStyle(Et);var Ot=this._defaultStyle,Wt=!1,Ut=0,Bt=x("fill"in j?j.fill:"fill"in X?X.fill:(Wt=!0,Ot.fill)),Ht=U("stroke"in j?j.stroke:"stroke"in X?X.stroke:!St&&!tt&&(!Ot.autoStroke||Wt)?(Ut=i,Ot.stroke):null),kt=j.textShadowBlur>0||X.textShadowBlur>0;Et.text=H.text,Et.x=ot,Et.y=ct,kt&&(Et.shadowBlur=j.textShadowBlur||X.textShadowBlur||0,Et.shadowColor=j.textShadowColor||X.textShadowColor||"transparent",Et.shadowOffsetX=j.textShadowOffsetX||X.textShadowOffsetX||0,Et.shadowOffsetY=j.textShadowOffsetY||X.textShadowOffsetY||0),Et.textAlign=q,Et.textBaseline="middle",Et.font=H.font||p.Uo,Et.opacity=(0,v.R1)(j.opacity,X.opacity,1),D(Et,j),Ht&&(Et.lineWidth=(0,v.R1)(j.lineWidth,X.lineWidth,Ut),Et.lineDash=(0,v.pD)(j.lineDash,X.lineDash),Et.lineDashOffset=X.lineDashOffset||0,Et.stroke=Ht),Bt&&(Et.fill=Bt);var Xt=H.contentWidth,Vt=H.contentHeight;Ct.setBoundingRect(new f.Z((0,F.M3)(Et.x,Xt,Et.textAlign),(0,F.mU)(Et.y,Vt,Et.textBaseline),Xt,Vt))},Z.prototype._renderBackground=function(H,X,ut,lt,ot,q){var tt=H.backgroundColor,j=H.borderWidth,et=H.borderColor,ct=tt&&tt.image,_t=tt&&!ct,St=H.borderRadius,At=this,Ct,Et;if(_t||H.lineHeight||j&&et){Ct=this._getOrCreateChild(d.Z),Ct.useStyle(Ct.createStyle()),Ct.style.fill=null;var Ot=Ct.shape;Ot.x=ut,Ot.y=lt,Ot.width=ot,Ot.height=q,Ot.r=St,Ct.dirtyShape()}if(_t){var Wt=Ct.style;Wt.fill=tt||null,Wt.fillOpacity=(0,v.pD)(H.fillOpacity,1)}else if(ct){Et=this._getOrCreateChild(O.ZP),Et.onload=function(){At.dirtyStyle()};var Ut=Et.style;Ut.image=tt.image,Ut.x=ut,Ut.y=lt,Ut.width=ot,Ut.height=q}if(j&&et){var Wt=Ct.style;Wt.lineWidth=j,Wt.stroke=et,Wt.strokeOpacity=(0,v.pD)(H.strokeOpacity,1),Wt.lineDash=H.borderDash,Wt.lineDashOffset=H.borderDashOffset||0,Ct.strokeContainThreshold=0,Ct.hasFill()&&Ct.hasStroke()&&(Wt.strokeFirst=!0,Wt.lineWidth*=2)}var Bt=(Ct||Et).style;Bt.shadowBlur=H.shadowBlur||0,Bt.shadowColor=H.shadowColor||"transparent",Bt.shadowOffsetX=H.shadowOffsetX||0,Bt.shadowOffsetY=H.shadowOffsetY||0,Bt.opacity=(0,v.R1)(H.opacity,X.opacity,1)},Z.makeFont=function(H){var X="";return B(H)&&(X=[H.fontStyle,H.fontWeight,o(H.fontSize),H.fontFamily||"sans-serif"].join(" ")),X&&(0,v.fy)(X)||H.textFont||H.font},Z}(u.ZP),e={left:!0,right:1,center:1},a={top:1,bottom:1,middle:1},s=["fontStyle","fontWeight","fontSize","fontFamily"];function o(G){return typeof G=="string"&&(G.indexOf("px")!==-1||G.indexOf("rem")!==-1||G.indexOf("em")!==-1)?G:isNaN(+G)?p.n5+"px":G+"px"}function D(G,Z){for(var H=0;H<s.length;H++){var X=s[H],ut=Z[X];ut!=null&&(G[X]=ut)}}function B(G){return G.fontSize!=null||G.fontFamily||G.fontWeight}function I(G){return L(G),(0,v.S6)(G.rich,L),G}function L(G){if(G){G.font=r.makeFont(G);var Z=G.align;Z==="middle"&&(Z="center"),G.align=Z==null||e[Z]?Z:"left";var H=G.verticalAlign;H==="center"&&(H="middle"),G.verticalAlign=H==null||a[H]?H:"top";var X=G.padding;X&&(G.padding=(0,v.MY)(G.padding))}}function U(G,Z){return G==null||Z<=0||G==="transparent"||G==="none"?null:G.image||G.colorStops?"#000":G}function x(G){return G==null||G==="none"?null:G.image||G.colorStops?"#000":G}function T(G,Z,H){return Z==="right"?G-H[1]:Z==="center"?G+H[3]/2-H[1]/2:G+H[3]}function w(G){var Z=G.text;return Z!=null&&(Z+=""),Z}function V(G){return!!(G.backgroundColor||G.lineHeight||G.borderWidth&&G.borderColor)}var N=r},14414:function(Zt,pt,P){"use strict";P.d(pt,{YV:function(){return C},SE:function(){return k},RH:function(){return v}});var C=1,k=2,v=4},8007:function(Zt,pt,P){"use strict";P.d(pt,{ko:function(){return F},Gq:function(){return z},v5:function(){return K}});var C=P(92528),k=P(23132),v=new C.ZP(50);function F(R){if(typeof R=="string"){var b=v.get(R);return b&&b.image}else return R}function z(R,b,A,S,m){if(R)if(typeof R=="string"){if(b&&b.__zrImageSrc===R||!A)return b;var g=v.get(R),y={hostEl:A,cb:S,cbPayload:m};return g?(b=g.image,!K(b)&&g.pending.push(y)):(b=k.qW.loadImage(R,W,W),b.__zrImageSrc=R,v.put(R,b.__cachedImgObj={image:b,pending:[y]})),b}else return R;else return b}function W(){var R=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var b=0;b<R.pending.length;b++){var A=R.pending[b],S=A.cb;S&&S(this,A.cbPayload),A.hostEl.dirty()}R.pending.length=0}function K(R){return R&&R.width&&R.height}},40924:function(Zt,pt,P){"use strict";P.d(pt,{L:function(){return v}});var C=P(45280);function k(F,z,W,K){var R=[],b=[],A=[],S=[],m,g,y,E;if(K){y=[Infinity,Infinity],E=[-Infinity,-Infinity];for(var h=0,c=F.length;h<c;h++)(0,C.VV)(y,y,F[h]),(0,C.Fp)(E,E,F[h]);(0,C.VV)(y,y,K[0]),(0,C.Fp)(E,E,K[1])}for(var h=0,c=F.length;h<c;h++){var _=F[h];if(W)m=F[h?h-1:c-1],g=F[(h+1)%c];else if(h===0||h===c-1){R.push((0,C.d9)(F[h]));continue}else m=F[h-1],g=F[h+1];(0,C.lu)(b,g,m),(0,C.bA)(b,b,z);var M=(0,C.TE)(_,m),l=(0,C.TE)(_,g),O=M+l;O!==0&&(M/=O,l/=O),(0,C.bA)(A,b,-M),(0,C.bA)(S,b,l);var d=(0,C.IH)([],_,A),f=(0,C.IH)([],_,S);K&&((0,C.Fp)(d,d,y),(0,C.VV)(d,d,E),(0,C.Fp)(f,f,y),(0,C.VV)(f,f,E)),R.push(d),R.push(f)}return W&&R.push(R.shift()),R}function v(F,z,W){var K=z.smooth,R=z.points;if(R&&R.length>=2){if(K){var b=k(R,K,W,z.smoothConstraint);F.moveTo(R[0][0],R[0][1]);for(var A=R.length,S=0;S<(W?A:A-1);S++){var m=b[S*2],g=b[S*2+1],y=R[(S+1)%A];F.bezierCurveTo(m[0],m[1],g[0],g[1],y[0],y[1])}}else{F.moveTo(R[0][0],R[0][1]);for(var S=1,E=R.length;S<E;S++)F.lineTo(R[S][0],R[S][1])}W&&F.closePath()}}},24111:function(Zt,pt,P){"use strict";P.d(pt,{_3:function(){return k},Pw:function(){return v},vu:function(){return F}});var C=Math.round;function k(z,W,K){if(!!W){var R=W.x1,b=W.x2,A=W.y1,S=W.y2;z.x1=R,z.x2=b,z.y1=A,z.y2=S;var m=K&&K.lineWidth;return m&&(C(R*2)===C(b*2)&&(z.x1=z.x2=F(R,m,!0)),C(A*2)===C(S*2)&&(z.y1=z.y2=F(A,m,!0))),z}}function v(z,W,K){if(!!W){var R=W.x,b=W.y,A=W.width,S=W.height;z.x=R,z.y=b,z.width=A,z.height=S;var m=K&&K.lineWidth;return m&&(z.x=F(R,m,!0),z.y=F(b,m,!0),z.width=Math.max(F(R+A,m,!1)-z.x,A===0?0:1),z.height=Math.max(F(b+S,m,!1)-z.y,S===0?0:1)),z}}function F(z,W,K){if(!W)return z;var R=C(z*2);return(R+C(W))%2==0?R/2:(R+(K?1:-1))/2}},14826:function(Zt,pt,P){"use strict";var C=P(4311),k=P(4665),v=function(){function z(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return z}(),F=function(z){(0,C.ZT)(W,z);function W(K){return z.call(this,K)||this}return W.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},W.prototype.getDefaultShape=function(){return new v},W.prototype.buildPath=function(K,R){var b=R.cx,A=R.cy,S=Math.max(R.r,0),m=R.startAngle,g=R.endAngle,y=R.clockwise,E=Math.cos(m),h=Math.sin(m);K.moveTo(E*S+b,h*S+A),K.arc(b,A,S,m,g,!y)},W}(k.ZP);F.prototype.type="arc",pt.Z=F},69538:function(Zt,pt,P){"use strict";var C=P(4311),k=P(4665),v=function(){function z(){this.cx=0,this.cy=0,this.r=0}return z}(),F=function(z){(0,C.ZT)(W,z);function W(K){return z.call(this,K)||this}return W.prototype.getDefaultShape=function(){return new v},W.prototype.buildPath=function(K,R){K.moveTo(R.cx+R.r,R.cy),K.arc(R.cx,R.cy,R.r,0,Math.PI*2)},W}(k.ZP);F.prototype.type="circle",pt.Z=F},22095:function(Zt,pt,P){"use strict";var C=P(4311),k=P(4665),v=P(24111),F={},z=function(){function K(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return K}(),W=function(K){(0,C.ZT)(R,K);function R(b){return K.call(this,b)||this}return R.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},R.prototype.getDefaultShape=function(){return new z},R.prototype.buildPath=function(b,A){var S,m,g,y;if(this.subPixelOptimize){var E=(0,v._3)(F,A,this.style);S=E.x1,m=E.y1,g=E.x2,y=E.y2}else S=A.x1,m=A.y1,g=A.x2,y=A.y2;var h=A.percent;h!==0&&(b.moveTo(S,m),h<1&&(g=S*(1-h)+g*h,y=m*(1-h)+y*h),b.lineTo(g,y))},R.prototype.pointAt=function(b){var A=this.shape;return[A.x1*(1-b)+A.x2*b,A.y1*(1-b)+A.y2*b]},R}(k.ZP);W.prototype.type="line",pt.Z=W},95094:function(Zt,pt,P){"use strict";var C=P(4311),k=P(4665),v=P(40924),F=function(){function W(){this.points=null,this.smooth=0,this.smoothConstraint=null}return W}(),z=function(W){(0,C.ZT)(K,W);function K(R){return W.call(this,R)||this}return K.prototype.getDefaultShape=function(){return new F},K.prototype.buildPath=function(R,b){v.L(R,b,!0)},K}(k.ZP);z.prototype.type="polygon",pt.Z=z},62514:function(Zt,pt,P){"use strict";var C=P(4311),k=P(4665),v=P(40924),F=function(){function W(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return W}(),z=function(W){(0,C.ZT)(K,W);function K(R){return W.call(this,R)||this}return K.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},K.prototype.getDefaultShape=function(){return new F},K.prototype.buildPath=function(R,b){v.L(R,b,!1)},K}(k.ZP);z.prototype.type="polyline",pt.Z=z},35151:function(Zt,pt,P){"use strict";P.d(pt,{Z:function(){return R}});var C=P(4311),k=P(4665);function v(b,A){var S=A.x,m=A.y,g=A.width,y=A.height,E=A.r,h,c,_,M;g<0&&(S=S+g,g=-g),y<0&&(m=m+y,y=-y),typeof E=="number"?h=c=_=M=E:E instanceof Array?E.length===1?h=c=_=M=E[0]:E.length===2?(h=_=E[0],c=M=E[1]):E.length===3?(h=E[0],c=M=E[1],_=E[2]):(h=E[0],c=E[1],_=E[2],M=E[3]):h=c=_=M=0;var l;h+c>g&&(l=h+c,h*=g/l,c*=g/l),_+M>g&&(l=_+M,_*=g/l,M*=g/l),c+_>y&&(l=c+_,c*=y/l,_*=y/l),h+M>y&&(l=h+M,h*=y/l,M*=y/l),b.moveTo(S+h,m),b.lineTo(S+g-c,m),c!==0&&b.arc(S+g-c,m+c,c,-Math.PI/2,0),b.lineTo(S+g,m+y-_),_!==0&&b.arc(S+g-_,m+y-_,_,0,Math.PI/2),b.lineTo(S+M,m+y),M!==0&&b.arc(S+M,m+y-M,M,Math.PI/2,Math.PI),b.lineTo(S,m+h),h!==0&&b.arc(S+h,m+h,h,Math.PI,Math.PI*1.5)}var F=P(24111),z=function(){function b(){this.x=0,this.y=0,this.width=0,this.height=0}return b}(),W={},K=function(b){(0,C.ZT)(A,b);function A(S){return b.call(this,S)||this}return A.prototype.getDefaultShape=function(){return new z},A.prototype.buildPath=function(S,m){var g,y,E,h;if(this.subPixelOptimize){var c=(0,F.Pw)(W,m,this.style);g=c.x,y=c.y,E=c.width,h=c.height,c.r=m.r,m=c}else g=m.x,y=m.y,E=m.width,h=m.height;m.r?v(S,m):S.rect(g,y,E,h)},A.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},A}(k.ZP);K.prototype.type="rect";var R=K},85795:function(Zt,pt,P){"use strict";var C=P(4311),k=P(4665),v=function(){function z(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return z}(),F=function(z){(0,C.ZT)(W,z);function W(K){return z.call(this,K)||this}return W.prototype.getDefaultShape=function(){return new v},W.prototype.buildPath=function(K,R){var b=R.cx,A=R.cy,S=Math.PI*2;K.moveTo(b+R.r,A),K.arc(b,A,R.r,0,S,!1),K.moveTo(b+R.r0,A),K.arc(b,A,R.r0,0,S,!0)},W}(k.ZP);F.prototype.type="ring",pt.Z=F},27214:function(Zt,pt,P){"use strict";P.d(pt,{C:function(){return O}});var C=P(4311),k=P(4665),v=P(33051),F=Math.PI,z=F*2,W=Math.sin,K=Math.cos,R=Math.acos,b=Math.atan2,A=Math.abs,S=Math.sqrt,m=Math.max,g=Math.min,y=1e-4;function E(d,f,u,p,n,i,t,r){var e=u-d,a=p-f,s=t-n,o=r-i,D=o*e-s*a;if(!(D*D<y))return D=(s*(f-i)-o*(d-n))/D,[d+D*e,f+D*a]}function h(d,f,u,p,n,i,t){var r=d-u,e=f-p,a=(t?i:-i)/S(r*r+e*e),s=a*e,o=-a*r,D=d+s,B=f+o,I=u+s,L=p+o,U=(D+I)/2,x=(B+L)/2,T=I-D,w=L-B,V=T*T+w*w,N=n-i,G=D*L-I*B,Z=(w<0?-1:1)*S(m(0,N*N*V-G*G)),H=(G*w-T*Z)/V,X=(-G*T-w*Z)/V,ut=(G*w+T*Z)/V,lt=(-G*T+w*Z)/V,ot=H-U,q=X-x,tt=ut-U,j=lt-x;return ot*ot+q*q>tt*tt+j*j&&(H=ut,X=lt),{cx:H,cy:X,x0:-s,y0:-o,x1:H*(n/N-1),y1:X*(n/N-1)}}function c(d){var f;if((0,v.kJ)(d)){var u=d.length;if(!u)return d;u===1?f=[d[0],d[0],0,0]:u===2?f=[d[0],d[0],d[1],d[1]]:u===3?f=d.concat(d[2]):f=d}else f=[d,d,d,d];return f}function _(d,f){var u,p=m(f.r,0),n=m(f.r0||0,0),i=p>0,t=n>0;if(!(!i&&!t)){if(i||(p=n,n=0),n>p){var r=p;p=n,n=r}var e=f.startAngle,a=f.endAngle;if(!(isNaN(e)||isNaN(a))){var s=f.cx,o=f.cy,D=!!f.clockwise,B=A(a-e),I=B>z&&B%z;if(I>y&&(B=I),!(p>y))d.moveTo(s,o);else if(B>z-y)d.moveTo(s+p*K(e),o+p*W(e)),d.arc(s,o,p,e,a,!D),n>y&&(d.moveTo(s+n*K(a),o+n*W(a)),d.arc(s,o,n,a,e,D));else{var L=void 0,U=void 0,x=void 0,T=void 0,w=void 0,V=void 0,N=void 0,G=void 0,Z=void 0,H=void 0,X=void 0,ut=void 0,lt=void 0,ot=void 0,q=void 0,tt=void 0,j=p*K(e),et=p*W(e),ct=n*K(a),_t=n*W(a),St=B>y;if(St){var At=f.cornerRadius;At&&(u=c(At),L=u[0],U=u[1],x=u[2],T=u[3]);var Ct=A(p-n)/2;if(w=g(Ct,x),V=g(Ct,T),N=g(Ct,L),G=g(Ct,U),X=Z=m(w,V),ut=H=m(N,G),(Z>y||H>y)&&(lt=p*K(a),ot=p*W(a),q=n*K(e),tt=n*W(e),B<F)){var Et=E(j,et,q,tt,lt,ot,ct,_t);if(Et){var Ot=j-Et[0],Wt=et-Et[1],Ut=lt-Et[0],Bt=ot-Et[1],Ht=1/W(R((Ot*Ut+Wt*Bt)/(S(Ot*Ot+Wt*Wt)*S(Ut*Ut+Bt*Bt)))/2),kt=S(Et[0]*Et[0]+Et[1]*Et[1]);X=g(Z,(p-kt)/(Ht+1)),ut=g(H,(n-kt)/(Ht-1))}}}if(!St)d.moveTo(s+j,o+et);else if(X>y){var Xt=g(x,X),Vt=g(T,X),st=h(q,tt,j,et,p,Xt,D),gt=h(lt,ot,ct,_t,p,Vt,D);d.moveTo(s+st.cx+st.x0,o+st.cy+st.y0),X<Z&&Xt===Vt?d.arc(s+st.cx,o+st.cy,X,b(st.y0,st.x0),b(gt.y0,gt.x0),!D):(Xt>0&&d.arc(s+st.cx,o+st.cy,Xt,b(st.y0,st.x0),b(st.y1,st.x1),!D),d.arc(s,o,p,b(st.cy+st.y1,st.cx+st.x1),b(gt.cy+gt.y1,gt.cx+gt.x1),!D),Vt>0&&d.arc(s+gt.cx,o+gt.cy,Vt,b(gt.y1,gt.x1),b(gt.y0,gt.x0),!D))}else d.moveTo(s+j,o+et),d.arc(s,o,p,e,a,!D);if(!(n>y)||!St)d.lineTo(s+ct,o+_t);else if(ut>y){var Xt=g(L,ut),Vt=g(U,ut),st=h(ct,_t,lt,ot,n,-Vt,D),gt=h(j,et,q,tt,n,-Xt,D);d.lineTo(s+st.cx+st.x0,o+st.cy+st.y0),ut<H&&Xt===Vt?d.arc(s+st.cx,o+st.cy,ut,b(st.y0,st.x0),b(gt.y0,gt.x0),!D):(Vt>0&&d.arc(s+st.cx,o+st.cy,Vt,b(st.y0,st.x0),b(st.y1,st.x1),!D),d.arc(s,o,n,b(st.cy+st.y1,st.cx+st.x1),b(gt.cy+gt.y1,gt.cx+gt.x1),D),Xt>0&&d.arc(s+gt.cx,o+gt.cy,Xt,b(gt.y1,gt.x1),b(gt.y0,gt.x0),!D))}else d.lineTo(s+ct,o+_t),d.arc(s,o,n,a,e,D)}d.closePath()}}}var M=function(){function d(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return d}(),l=function(d){(0,C.ZT)(f,d);function f(u){return d.call(this,u)||this}return f.prototype.getDefaultShape=function(){return new M},f.prototype.buildPath=function(u,p){_(u,p)},f.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},f}(k.ZP);l.prototype.type="sector";var O=l},24839:function(Zt,pt,P){"use strict";P.d(pt,{ut:function(){return W},zT:function(){return R},Pn:function(){return A},qV:function(){return m},jY:function(){return g},mU:function(){return y},i2:function(){return E},n1:function(){return h},Cv:function(){return _},R:function(){return l},I1:function(){return O},gO:function(){return d},H3:function(){return f},m1:function(){return u},Gk:function(){return p},gA:function(){return n},oF:function(){return i}});var C=P(33051),k=P(21092),v=P(66387),F=P(48764).Buffer,z=Math.round;function W(t){var r;if(!t||t==="transparent")t="none";else if(typeof t=="string"&&t.indexOf("rgba")>-1){var e=(0,k.Qc)(t);e&&(t="rgb("+e[0]+","+e[1]+","+e[2]+")",r=e[3])}return{color:t,opacity:r==null?1:r}}var K=1e-4;function R(t){return t<K&&t>-K}function b(t){return z(t*1e3)/1e3}function A(t){return z(t*1e4)/1e4}function S(t){return z(t*10)/10}function m(t){return"matrix("+b(t[0])+","+b(t[1])+","+b(t[2])+","+b(t[3])+","+A(t[4])+","+A(t[5])+")"}var g={left:"start",right:"end",center:"middle",middle:"middle"};function y(t,r,e){return e==="top"?t+=r/2:e==="bottom"&&(t-=r/2),t}function E(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}function h(t){var r=t.style,e=t.getGlobalScale();return[r.shadowColor,(r.shadowBlur||0).toFixed(2),(r.shadowOffsetX||0).toFixed(2),(r.shadowOffsetY||0).toFixed(2),e[0],e[1]].join(",")}function c(t){var r=[];if(t)for(var e=0;e<t.length;e++){var a=t[e];r.push(a.id)}return r.join(",")}function _(t){return t&&!!t.image}function M(t){return t&&!!t.svgElement}function l(t){return _(t)||M(t)}function O(t){return t.type==="linear"}function d(t){return t.type==="radial"}function f(t){return t&&(t.type==="linear"||t.type==="radial")}function u(t){return"url(#"+t+")"}function p(t){var r=t.getGlobalScale(),e=Math.max(r[0],r[1]);return Math.max(Math.ceil(Math.log(e)/Math.log(10)),1)}function n(t){var r=t.x||0,e=t.y||0,a=(t.rotation||0)*C.I3,s=(0,C.pD)(t.scaleX,1),o=(0,C.pD)(t.scaleY,1),D=t.skewX||0,B=t.skewY||0,I=[];return(r||e)&&I.push("translate("+r+"px,"+e+"px)"),a&&I.push("rotate("+a+")"),(s!==1||o!==1)&&I.push("scale("+s+","+o+")"),(D||B)&&I.push("skew("+z(D*C.I3)+"deg, "+z(B*C.I3)+"deg)"),I.join(" ")}var i=function(){return v.Z.hasGlobalWindow&&(0,C.mf)(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:typeof F!="undefined"?function(t){return F.from(t).toString("base64")}:function(t){return null}}()},21092:function(Zt,pt,P){"use strict";P.d(pt,{Qc:function(){return h},Uu:function(){return O},t7:function(){return f},ox:function(){return p},m8:function(){return n},Pz:function(){return i},L0:function(){return t},fD:function(){return a}});var C=P(92528),k=P(33051),v={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function F(s){return s=Math.round(s),s<0?0:s>255?255:s}function z(s){return s=Math.round(s),s<0?0:s>360?360:s}function W(s){return s<0?0:s>1?1:s}function K(s){var o=s;return o.length&&o.charAt(o.length-1)==="%"?F(parseFloat(o)/100*255):F(parseInt(o,10))}function R(s){var o=s;return o.length&&o.charAt(o.length-1)==="%"?W(parseFloat(o)/100):W(parseFloat(o))}function b(s,o,D){return D<0?D+=1:D>1&&(D-=1),D*6<1?s+(o-s)*D*6:D*2<1?o:D*3<2?s+(o-s)*(2/3-D)*6:s}function A(s,o,D){return s+(o-s)*D}function S(s,o,D,B,I){return s[0]=o,s[1]=D,s[2]=B,s[3]=I,s}function m(s,o){return s[0]=o[0],s[1]=o[1],s[2]=o[2],s[3]=o[3],s}var g=new C.ZP(20),y=null;function E(s,o){y&&m(y,o),y=g.put(s,y||o.slice())}function h(s,o){if(!!s){o=o||[];var D=g.get(s);if(D)return m(o,D);s=s+"";var B=s.replace(/ /g,"").toLowerCase();if(B in v)return m(o,v[B]),E(s,o),o;var I=B.length;if(B.charAt(0)==="#"){if(I===4||I===5){var L=parseInt(B.slice(1,4),16);if(!(L>=0&&L<=4095)){S(o,0,0,0,1);return}return S(o,(L&3840)>>4|(L&3840)>>8,L&240|(L&240)>>4,L&15|(L&15)<<4,I===5?parseInt(B.slice(4),16)/15:1),E(s,o),o}else if(I===7||I===9){var L=parseInt(B.slice(1,7),16);if(!(L>=0&&L<=16777215)){S(o,0,0,0,1);return}return S(o,(L&16711680)>>16,(L&65280)>>8,L&255,I===9?parseInt(B.slice(7),16)/255:1),E(s,o),o}return}var U=B.indexOf("("),x=B.indexOf(")");if(U!==-1&&x+1===I){var T=B.substr(0,U),w=B.substr(U+1,x-(U+1)).split(","),V=1;switch(T){case"rgba":if(w.length!==4)return w.length===3?S(o,+w[0],+w[1],+w[2],1):S(o,0,0,0,1);V=R(w.pop());case"rgb":if(w.length>=3)return S(o,K(w[0]),K(w[1]),K(w[2]),w.length===3?V:R(w[3])),E(s,o),o;S(o,0,0,0,1);return;case"hsla":if(w.length!==4){S(o,0,0,0,1);return}return w[3]=R(w[3]),c(w,o),E(s,o),o;case"hsl":if(w.length!==3){S(o,0,0,0,1);return}return c(w,o),E(s,o),o;default:return}}S(o,0,0,0,1)}}function c(s,o){var D=(parseFloat(s[0])%360+360)%360/360,B=R(s[1]),I=R(s[2]),L=I<=.5?I*(B+1):I+B-I*B,U=I*2-L;return o=o||[],S(o,F(b(U,L,D+1/3)*255),F(b(U,L,D)*255),F(b(U,L,D-1/3)*255),1),s.length===4&&(o[3]=s[3]),o}function _(s){if(!!s){var o=s[0]/255,D=s[1]/255,B=s[2]/255,I=Math.min(o,D,B),L=Math.max(o,D,B),U=L-I,x=(L+I)/2,T,w;if(U===0)T=0,w=0;else{x<.5?w=U/(L+I):w=U/(2-L-I);var V=((L-o)/6+U/2)/U,N=((L-D)/6+U/2)/U,G=((L-B)/6+U/2)/U;o===L?T=G-N:D===L?T=1/3+V-G:B===L&&(T=2/3+N-V),T<0&&(T+=1),T>1&&(T-=1)}var Z=[T*360,w,x];return s[3]!=null&&Z.push(s[3]),Z}}function M(s,o){var D=h(s);if(D){for(var B=0;B<3;B++)o<0?D[B]=D[B]*(1-o)|0:D[B]=(255-D[B])*o+D[B]|0,D[B]>255?D[B]=255:D[B]<0&&(D[B]=0);return i(D,D.length===4?"rgba":"rgb")}}function l(s){var o=h(s);if(o)return((1<<24)+(o[0]<<16)+(o[1]<<8)+ +o[2]).toString(16).slice(1)}function O(s,o,D){if(!(!(o&&o.length)||!(s>=0&&s<=1))){D=D||[];var B=s*(o.length-1),I=Math.floor(B),L=Math.ceil(B),U=o[I],x=o[L],T=B-I;return D[0]=F(A(U[0],x[0],T)),D[1]=F(A(U[1],x[1],T)),D[2]=F(A(U[2],x[2],T)),D[3]=W(A(U[3],x[3],T)),D}}var d=null;function f(s,o,D){if(!(!(o&&o.length)||!(s>=0&&s<=1))){var B=s*(o.length-1),I=Math.floor(B),L=Math.ceil(B),U=h(o[I]),x=h(o[L]),T=B-I,w=i([F(A(U[0],x[0],T)),F(A(U[1],x[1],T)),F(A(U[2],x[2],T)),W(A(U[3],x[3],T))],"rgba");return D?{color:w,leftIndex:I,rightIndex:L,value:B}:w}}var u=null;function p(s,o,D,B){var I=h(s);if(s)return I=_(I),o!=null&&(I[0]=z(o)),D!=null&&(I[1]=R(D)),B!=null&&(I[2]=R(B)),i(c(I),"rgba")}function n(s,o){var D=h(s);if(D&&o!=null)return D[3]=W(o),i(D,"rgba")}function i(s,o){if(!(!s||!s.length)){var D=s[0]+","+s[1]+","+s[2];return(o==="rgba"||o==="hsva"||o==="hsla")&&(D+=","+s[3]),o+"("+D+")"}}function t(s,o){var D=h(s);return D?(.299*D[0]+.587*D[1]+.114*D[2])*D[3]/255+(1-D[3])*o:0}function r(){return i([Math.round(Math.random()*255),Math.round(Math.random()*255),Math.round(Math.random()*255)],"rgb")}var e=new C.ZP(100);function a(s){if((0,k.HD)(s)){var o=e.get(s);return o||(o=M(s,-.1),e.put(s,o)),o}else if((0,k.Qq)(s)){var D=(0,k.l7)({},s);return D.colorStops=(0,k.UI)(s.colorStops,function(B){return{offset:B.offset,color:M(B.color,-.1)}}),D}return s}},7494:function(Zt,pt,P){"use strict";P.d(pt,{U5:function(){return t},iR:function(){return p},Pc:function(){return n},AA:function(){return i}});var C=P(4311),k=P(4665),v=P(14014),F=P(45280),z=v.Z.CMD,W=[[],[],[]],K=Math.sqrt,R=Math.atan2;function b(r,e){if(!!e){var a=r.data,s=r.len(),o,D,B,I,L,U,x=z.M,T=z.C,w=z.L,V=z.R,N=z.A,G=z.Q;for(B=0,I=0;B<s;){switch(o=a[B++],I=B,D=0,o){case x:D=1;break;case w:D=1;break;case T:D=3;break;case G:D=2;break;case N:var Z=e[4],H=e[5],X=K(e[0]*e[0]+e[1]*e[1]),ut=K(e[2]*e[2]+e[3]*e[3]),lt=R(-e[1]/ut,e[0]/X);a[B]*=X,a[B++]+=Z,a[B]*=ut,a[B++]+=H,a[B++]*=X,a[B++]*=ut,a[B++]+=lt,a[B++]+=lt,B+=2,I=B;break;case V:U[0]=a[B++],U[1]=a[B++],(0,F.Ne)(U,U,e),a[I++]=U[0],a[I++]=U[1],U[0]+=a[B++],U[1]+=a[B++],(0,F.Ne)(U,U,e),a[I++]=U[0],a[I++]=U[1]}for(L=0;L<D;L++){var ot=W[L];ot[0]=a[B++],ot[1]=a[B++],(0,F.Ne)(ot,ot,e),a[I++]=ot[0],a[I++]=ot[1]}}r.increaseVersion()}}var A=P(33051),S=Math.sqrt,m=Math.sin,g=Math.cos,y=Math.PI;function E(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function h(r,e){return(r[0]*e[0]+r[1]*e[1])/(E(r)*E(e))}function c(r,e){return(r[0]*e[1]<r[1]*e[0]?-1:1)*Math.acos(h(r,e))}function _(r,e,a,s,o,D,B,I,L,U,x){var T=L*(y/180),w=g(T)*(r-a)/2+m(T)*(e-s)/2,V=-1*m(T)*(r-a)/2+g(T)*(e-s)/2,N=w*w/(B*B)+V*V/(I*I);N>1&&(B*=S(N),I*=S(N));var G=(o===D?-1:1)*S((B*B*(I*I)-B*B*(V*V)-I*I*(w*w))/(B*B*(V*V)+I*I*(w*w)))||0,Z=G*B*V/I,H=G*-I*w/B,X=(r+a)/2+g(T)*Z-m(T)*H,ut=(e+s)/2+m(T)*Z+g(T)*H,lt=c([1,0],[(w-Z)/B,(V-H)/I]),ot=[(w-Z)/B,(V-H)/I],q=[(-1*w-Z)/B,(-1*V-H)/I],tt=c(ot,q);if(h(ot,q)<=-1&&(tt=y),h(ot,q)>=1&&(tt=0),tt<0){var j=Math.round(tt/y*1e6)/1e6;tt=y*2+j%2*y}x.addData(U,X,ut,B,I,lt,tt,T,D)}var M=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,l=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function O(r){var e=new v.Z;if(!r)return e;var a=0,s=0,o=a,D=s,B,I=v.Z.CMD,L=r.match(M);if(!L)return e;for(var U=0;U<L.length;U++){for(var x=L[U],T=x.charAt(0),w=void 0,V=x.match(l)||[],N=V.length,G=0;G<N;G++)V[G]=parseFloat(V[G]);for(var Z=0;Z<N;){var H=void 0,X=void 0,ut=void 0,lt=void 0,ot=void 0,q=void 0,tt=void 0,j=a,et=s,ct=void 0,_t=void 0;switch(T){case"l":a+=V[Z++],s+=V[Z++],w=I.L,e.addData(w,a,s);break;case"L":a=V[Z++],s=V[Z++],w=I.L,e.addData(w,a,s);break;case"m":a+=V[Z++],s+=V[Z++],w=I.M,e.addData(w,a,s),o=a,D=s,T="l";break;case"M":a=V[Z++],s=V[Z++],w=I.M,e.addData(w,a,s),o=a,D=s,T="L";break;case"h":a+=V[Z++],w=I.L,e.addData(w,a,s);break;case"H":a=V[Z++],w=I.L,e.addData(w,a,s);break;case"v":s+=V[Z++],w=I.L,e.addData(w,a,s);break;case"V":s=V[Z++],w=I.L,e.addData(w,a,s);break;case"C":w=I.C,e.addData(w,V[Z++],V[Z++],V[Z++],V[Z++],V[Z++],V[Z++]),a=V[Z-2],s=V[Z-1];break;case"c":w=I.C,e.addData(w,V[Z++]+a,V[Z++]+s,V[Z++]+a,V[Z++]+s,V[Z++]+a,V[Z++]+s),a+=V[Z-2],s+=V[Z-1];break;case"S":H=a,X=s,ct=e.len(),_t=e.data,B===I.C&&(H+=a-_t[ct-4],X+=s-_t[ct-3]),w=I.C,j=V[Z++],et=V[Z++],a=V[Z++],s=V[Z++],e.addData(w,H,X,j,et,a,s);break;case"s":H=a,X=s,ct=e.len(),_t=e.data,B===I.C&&(H+=a-_t[ct-4],X+=s-_t[ct-3]),w=I.C,j=a+V[Z++],et=s+V[Z++],a+=V[Z++],s+=V[Z++],e.addData(w,H,X,j,et,a,s);break;case"Q":j=V[Z++],et=V[Z++],a=V[Z++],s=V[Z++],w=I.Q,e.addData(w,j,et,a,s);break;case"q":j=V[Z++]+a,et=V[Z++]+s,a+=V[Z++],s+=V[Z++],w=I.Q,e.addData(w,j,et,a,s);break;case"T":H=a,X=s,ct=e.len(),_t=e.data,B===I.Q&&(H+=a-_t[ct-4],X+=s-_t[ct-3]),a=V[Z++],s=V[Z++],w=I.Q,e.addData(w,H,X,a,s);break;case"t":H=a,X=s,ct=e.len(),_t=e.data,B===I.Q&&(H+=a-_t[ct-4],X+=s-_t[ct-3]),a+=V[Z++],s+=V[Z++],w=I.Q,e.addData(w,H,X,a,s);break;case"A":ut=V[Z++],lt=V[Z++],ot=V[Z++],q=V[Z++],tt=V[Z++],j=a,et=s,a=V[Z++],s=V[Z++],w=I.A,_(j,et,a,s,q,tt,ut,lt,ot,w,e);break;case"a":ut=V[Z++],lt=V[Z++],ot=V[Z++],q=V[Z++],tt=V[Z++],j=a,et=s,a+=V[Z++],s+=V[Z++],w=I.A,_(j,et,a,s,q,tt,ut,lt,ot,w,e);break}}(T==="z"||T==="Z")&&(w=I.Z,e.addData(w),a=o,s=D),B=w}return e.toStatic(),e}var d=function(r){(0,C.ZT)(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.applyTransform=function(a){},e}(k.ZP);function f(r){return r.setData!=null}function u(r,e){var a=O(r),s=(0,A.l7)({},e);return s.buildPath=function(o){if(f(o)){o.setData(a.data);var D=o.getContext();D&&o.rebuildPath(D,1)}else{var D=o;a.rebuildPath(D,1)}},s.applyTransform=function(o){b(a,o),this.dirtyShape()},s}function p(r,e){return new d(u(r,e))}function n(r,e){var a=u(r,e),s=function(o){(0,C.ZT)(D,o);function D(B){var I=o.call(this,B)||this;return I.applyTransform=a.applyTransform,I.buildPath=a.buildPath,I}return D}(d);return s}function i(r,e){for(var a=[],s=r.length,o=0;o<s;o++){var D=r[o];a.push(D.getUpdatedPathProxy(!0))}var B=new k.ZP(e);return B.createPathProxy(),B.buildPath=function(I){if(f(I)){I.appendPath(a);var L=I.getContext();L&&I.rebuildPath(L,1)}},B}function t(r,e){e=e||{};var a=new k.ZP;return r.shape&&a.setShape(r.shape),a.setStyle(r.style),e.bakeTransform?b(a.path,r.getComputedTransform()):e.toLocal?a.setLocalTransform(r.getComputedTransform()):a.copyTransform(r),a.buildPath=r.buildPath,a.applyTransform=a.applyTransform,a.z=r.z,a.z2=r.z2,a.zlevel=r.zlevel,a}},99448:function(Zt,pt,P){"use strict";P.d(pt,{EJ:function(){return dt},S1:function(){return Xt},wm:function(){return xt},Qq:function(){return Mt}});var C=P(66387),k=P(33051),v=P(4311),F=P(45280),z=function(){function Y($,Q){this.target=$,this.topTarget=Q&&Q.topTarget}return Y}(),W=function(){function Y($){this.handler=$,$.on("mousedown",this._dragStart,this),$.on("mousemove",this._drag,this),$.on("mouseup",this._dragEnd,this)}return Y.prototype._dragStart=function($){for(var Q=$.target;Q&&!Q.draggable;)Q=Q.parent||Q.__hostTarget;Q&&(this._draggingTarget=Q,Q.dragging=!0,this._x=$.offsetX,this._y=$.offsetY,this.handler.dispatchToElement(new z(Q,$),"dragstart",$.event))},Y.prototype._drag=function($){var Q=this._draggingTarget;if(Q){var ft=$.offsetX,Tt=$.offsetY,wt=ft-this._x,bt=Tt-this._y;this._x=ft,this._y=Tt,Q.drift(wt,bt,$),this.handler.dispatchToElement(new z(Q,$),"drag",$.event);var zt=this.handler.findHover(ft,Tt,Q).target,Qt=this._dropTarget;this._dropTarget=zt,Q!==zt&&(Qt&&zt!==Qt&&this.handler.dispatchToElement(new z(Qt,$),"dragleave",$.event),zt&&zt!==Qt&&this.handler.dispatchToElement(new z(zt,$),"dragenter",$.event))}},Y.prototype._dragEnd=function($){var Q=this._draggingTarget;Q&&(Q.dragging=!1),this.handler.dispatchToElement(new z(Q,$),"dragend",$.event),this._dropTarget&&this.handler.dispatchToElement(new z(this._dropTarget,$),"drop",$.event),this._draggingTarget=null,this._dropTarget=null},Y}(),K=W,R=P(23510),b=P(61158),A=function(){function Y(){this._track=[]}return Y.prototype.recognize=function($,Q,ft){return this._doTrack($,Q,ft),this._recognize($)},Y.prototype.clear=function(){return this._track.length=0,this},Y.prototype._doTrack=function($,Q,ft){var Tt=$.touches;if(!!Tt){for(var wt={points:[],touches:[],target:Q,event:$},bt=0,zt=Tt.length;bt<zt;bt++){var Qt=Tt[bt],qt=b.eV(ft,Qt,{});wt.points.push([qt.zrX,qt.zrY]),wt.touches.push(Qt)}this._track.push(wt)}},Y.prototype._recognize=function($){for(var Q in g)if(g.hasOwnProperty(Q)){var ft=g[Q](this._track,$);if(ft)return ft}},Y}();function S(Y){var $=Y[1][0]-Y[0][0],Q=Y[1][1]-Y[0][1];return Math.sqrt($*$+Q*Q)}function m(Y){return[(Y[0][0]+Y[1][0])/2,(Y[0][1]+Y[1][1])/2]}var g={pinch:function(Y,$){var Q=Y.length;if(!!Q){var ft=(Y[Q-1]||{}).points,Tt=(Y[Q-2]||{}).points||ft;if(Tt&&Tt.length>1&&ft&&ft.length>1){var wt=S(ft)/S(Tt);!isFinite(wt)&&(wt=1),$.pinchScale=wt;var bt=m(ft);return $.pinchX=bt[0],$.pinchY=bt[1],{type:"pinch",target:Y[0].target,event:$}}}}},y=P(60479),E="silent";function h(Y,$,Q){return{type:Y,event:Q,target:$.target,topTarget:$.topTarget,cancelBubble:!1,offsetX:Q.zrX,offsetY:Q.zrY,gestureEvent:Q.gestureEvent,pinchX:Q.pinchX,pinchY:Q.pinchY,pinchScale:Q.pinchScale,wheelDelta:Q.zrDelta,zrByTouch:Q.zrByTouch,which:Q.which,stop:c}}function c(){b.sT(this.event)}var _=function(Y){(0,v.ZT)($,Y);function $(){var Q=Y!==null&&Y.apply(this,arguments)||this;return Q.handler=null,Q}return $.prototype.dispose=function(){},$.prototype.setCursor=function(){},$}(R.Z),M=function(){function Y($,Q){this.x=$,this.y=Q}return Y}(),l=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],O=new y.Z(0,0,0,0),d=function(Y){(0,v.ZT)($,Y);function $(Q,ft,Tt,wt,bt){var zt=Y.call(this)||this;return zt._hovered=new M(0,0),zt.storage=Q,zt.painter=ft,zt.painterRoot=wt,zt._pointerSize=bt,Tt=Tt||new _,zt.proxy=null,zt.setHandlerProxy(Tt),zt._draggingMgr=new K(zt),zt}return $.prototype.setHandlerProxy=function(Q){this.proxy&&this.proxy.dispose(),Q&&(k.S6(l,function(ft){Q.on&&Q.on(ft,this[ft],this)},this),Q.handler=this),this.proxy=Q},$.prototype.mousemove=function(Q){var ft=Q.zrX,Tt=Q.zrY,wt=p(this,ft,Tt),bt=this._hovered,zt=bt.target;zt&&!zt.__zr&&(bt=this.findHover(bt.x,bt.y),zt=bt.target);var Qt=this._hovered=wt?new M(ft,Tt):this.findHover(ft,Tt),qt=Qt.target,re=this.proxy;re.setCursor&&re.setCursor(qt?qt.cursor:"default"),zt&&qt!==zt&&this.dispatchToElement(bt,"mouseout",Q),this.dispatchToElement(Qt,"mousemove",Q),qt&&qt!==zt&&this.dispatchToElement(Qt,"mouseover",Q)},$.prototype.mouseout=function(Q){var ft=Q.zrEventControl;ft!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",Q),ft!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:Q})},$.prototype.resize=function(){this._hovered=new M(0,0)},$.prototype.dispatch=function(Q,ft){var Tt=this[Q];Tt&&Tt.call(this,ft)},$.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},$.prototype.setCursorStyle=function(Q){var ft=this.proxy;ft.setCursor&&ft.setCursor(Q)},$.prototype.dispatchToElement=function(Q,ft,Tt){Q=Q||{};var wt=Q.target;if(!(wt&&wt.silent)){for(var bt="on"+ft,zt=h(ft,Q,Tt);wt&&(wt[bt]&&(zt.cancelBubble=!!wt[bt].call(wt,zt)),wt.trigger(ft,zt),wt=wt.__hostTarget?wt.__hostTarget:wt.parent,!zt.cancelBubble););zt.cancelBubble||(this.trigger(ft,zt),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(Qt){typeof Qt[bt]=="function"&&Qt[bt].call(Qt,zt),Qt.trigger&&Qt.trigger(ft,zt)}))}},$.prototype.findHover=function(Q,ft,Tt){var wt=this.storage.getDisplayList(),bt=new M(Q,ft);if(u(wt,bt,Q,ft,Tt),this._pointerSize&&!bt.target){for(var zt=[],Qt=this._pointerSize,qt=Qt/2,re=new y.Z(Q-qt,ft-qt,Qt,Qt),ie=wt.length-1;ie>=0;ie--){var oe=wt[ie];oe!==Tt&&!oe.ignore&&!oe.ignoreCoarsePointer&&(!oe.parent||!oe.parent.ignoreCoarsePointer)&&(O.copy(oe.getBoundingRect()),oe.transform&&O.applyTransform(oe.transform),O.intersect(re)&&zt.push(oe))}if(zt.length)for(var ue=4,Me=Math.PI/12,Te=Math.PI*2,Ee=0;Ee<qt;Ee+=ue)for(var Pe=0;Pe<Te;Pe+=Me){var Ge=Q+Ee*Math.cos(Pe),Ce=ft+Ee*Math.sin(Pe);if(u(zt,bt,Ge,Ce,Tt),bt.target)return bt}}return bt},$.prototype.processGesture=function(Q,ft){this._gestureMgr||(this._gestureMgr=new A);var Tt=this._gestureMgr;ft==="start"&&Tt.clear();var wt=Tt.recognize(Q,this.findHover(Q.zrX,Q.zrY,null).target,this.proxy.dom);if(ft==="end"&&Tt.clear(),wt){var bt=wt.type;Q.gestureEvent=bt;var zt=new M;zt.target=wt.target,this.dispatchToElement(zt,bt,wt.event)}},$}(R.Z);k.S6(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(Y){d.prototype[Y]=function($){var Q=$.zrX,ft=$.zrY,Tt=p(this,Q,ft),wt,bt;if((Y!=="mouseup"||!Tt)&&(wt=this.findHover(Q,ft),bt=wt.target),Y==="mousedown")this._downEl=bt,this._downPoint=[$.zrX,$.zrY],this._upEl=bt;else if(Y==="mouseup")this._upEl=bt;else if(Y==="click"){if(this._downEl!==this._upEl||!this._downPoint||F.TK(this._downPoint,[$.zrX,$.zrY])>4)return;this._downPoint=null}this.dispatchToElement(wt,Y,$)}});function f(Y,$,Q){if(Y[Y.rectHover?"rectContain":"contain"]($,Q)){for(var ft=Y,Tt=void 0,wt=!1;ft;){if(ft.ignoreClip&&(wt=!0),!wt){var bt=ft.getClipPath();if(bt&&!bt.contain($,Q))return!1}ft.silent&&(Tt=!0);var zt=ft.__hostTarget;ft=zt||ft.parent}return Tt?E:!0}return!1}function u(Y,$,Q,ft,Tt){for(var wt=Y.length-1;wt>=0;wt--){var bt=Y[wt],zt=void 0;if(bt!==Tt&&!bt.ignore&&(zt=f(bt,Q,ft))&&(!$.topTarget&&($.topTarget=bt),zt!==E)){$.target=bt;break}}}function p(Y,$,Q){var ft=Y.painter;return $<0||$>ft.getWidth()||Q<0||Q>ft.getHeight()}var n=d,i=P(19455),t=P(14414),r=!1;function e(){r||(r=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function a(Y,$){return Y.zlevel===$.zlevel?Y.z===$.z?Y.z2-$.z2:Y.z-$.z:Y.zlevel-$.zlevel}var s=function(){function Y(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=a}return Y.prototype.traverse=function($,Q){for(var ft=0;ft<this._roots.length;ft++)this._roots[ft].traverse($,Q)},Y.prototype.getDisplayList=function($,Q){Q=Q||!1;var ft=this._displayList;return($||!ft.length)&&this.updateDisplayList(Q),ft},Y.prototype.updateDisplayList=function($){this._displayListLen=0;for(var Q=this._roots,ft=this._displayList,Tt=0,wt=Q.length;Tt<wt;Tt++)this._updateAndAddDisplayable(Q[Tt],null,$);ft.length=this._displayListLen,(0,i.Z)(ft,a)},Y.prototype._updateAndAddDisplayable=function($,Q,ft){if(!($.ignore&&!ft)){$.beforeUpdate(),$.update(),$.afterUpdate();var Tt=$.getClipPath();if($.ignoreClip)Q=null;else if(Tt){Q?Q=Q.slice():Q=[];for(var wt=Tt,bt=$;wt;)wt.parent=bt,wt.updateTransform(),Q.push(wt),bt=wt,wt=wt.getClipPath()}if($.childrenRef){for(var zt=$.childrenRef(),Qt=0;Qt<zt.length;Qt++){var qt=zt[Qt];$.__dirty&&(qt.__dirty|=t.YV),this._updateAndAddDisplayable(qt,Q,ft)}$.__dirty=0}else{var re=$;Q&&Q.length?re.__clipPaths=Q:re.__clipPaths&&re.__clipPaths.length>0&&(re.__clipPaths=[]),isNaN(re.z)&&(e(),re.z=0),isNaN(re.z2)&&(e(),re.z2=0),isNaN(re.zlevel)&&(e(),re.zlevel=0),this._displayList[this._displayListLen++]=re}var ie=$.getDecalElement&&$.getDecalElement();ie&&this._updateAndAddDisplayable(ie,Q,ft);var oe=$.getTextGuideLine();oe&&this._updateAndAddDisplayable(oe,Q,ft);var ue=$.getTextContent();ue&&this._updateAndAddDisplayable(ue,Q,ft)}},Y.prototype.addRoot=function($){$.__zr&&$.__zr.storage===this||this._roots.push($)},Y.prototype.delRoot=function($){if($ instanceof Array){for(var Q=0,ft=$.length;Q<ft;Q++)this.delRoot($[Q]);return}var Tt=k.cq(this._roots,$);Tt>=0&&this._roots.splice(Tt,1)},Y.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},Y.prototype.getRoots=function(){return this._roots},Y.prototype.dispose=function(){this._displayList=null,this._roots=null},Y}(),o=s,D=P(22795),B=P(95622);function I(){return new Date().getTime()}var L=function(Y){(0,v.ZT)($,Y);function $(Q){var ft=Y.call(this)||this;return ft._running=!1,ft._time=0,ft._pausedTime=0,ft._pauseStart=0,ft._paused=!1,Q=Q||{},ft.stage=Q.stage||{},ft}return $.prototype.addClip=function(Q){Q.animation&&this.removeClip(Q),this._head?(this._tail.next=Q,Q.prev=this._tail,Q.next=null,this._tail=Q):this._head=this._tail=Q,Q.animation=this},$.prototype.addAnimator=function(Q){Q.animation=this;var ft=Q.getClip();ft&&this.addClip(ft)},$.prototype.removeClip=function(Q){if(!!Q.animation){var ft=Q.prev,Tt=Q.next;ft?ft.next=Tt:this._head=Tt,Tt?Tt.prev=ft:this._tail=ft,Q.next=Q.prev=Q.animation=null}},$.prototype.removeAnimator=function(Q){var ft=Q.getClip();ft&&this.removeClip(ft),Q.animation=null},$.prototype.update=function(Q){for(var ft=I()-this._pausedTime,Tt=ft-this._time,wt=this._head;wt;){var bt=wt.next,zt=wt.step(ft,Tt);zt&&(wt.ondestroy(),this.removeClip(wt)),wt=bt}this._time=ft,Q||(this.trigger("frame",Tt),this.stage.update&&this.stage.update())},$.prototype._startLoop=function(){var Q=this;this._running=!0;function ft(){Q._running&&((0,D.Z)(ft),!Q._paused&&Q.update())}(0,D.Z)(ft)},$.prototype.start=function(){this._running||(this._time=I(),this._pausedTime=0,this._startLoop())},$.prototype.stop=function(){this._running=!1},$.prototype.pause=function(){this._paused||(this._pauseStart=I(),this._paused=!0)},$.prototype.resume=function(){this._paused&&(this._pausedTime+=I()-this._pauseStart,this._paused=!1)},$.prototype.clear=function(){for(var Q=this._head;Q;){var ft=Q.next;Q.prev=Q.next=Q.animation=null,Q=ft}this._head=this._tail=null},$.prototype.isFinished=function(){return this._head==null},$.prototype.animate=function(Q,ft){ft=ft||{},this.start();var Tt=new B.Z(Q,ft.loop);return this.addAnimator(Tt),Tt},$}(R.Z),U=L,x=300,T=C.Z.domSupported,w=function(){var Y=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],$=["touchstart","touchend","touchmove"],Q={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},ft=k.UI(Y,function(Tt){var wt=Tt.replace("mouse","pointer");return Q.hasOwnProperty(wt)?wt:Tt});return{mouse:Y,touch:$,pointer:ft}}(),V={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},N=!1;function G(Y){var $=Y.pointerType;return $==="pen"||$==="touch"}function Z(Y){Y.touching=!0,Y.touchTimer!=null&&(clearTimeout(Y.touchTimer),Y.touchTimer=null),Y.touchTimer=setTimeout(function(){Y.touching=!1,Y.touchTimer=null},700)}function H(Y){Y&&(Y.zrByTouch=!0)}function X(Y,$){return(0,b.OD)(Y.dom,new lt(Y,$),!0)}function ut(Y,$){for(var Q=$,ft=!1;Q&&Q.nodeType!==9&&!(ft=Q.domBelongToZr||Q!==$&&Q===Y.painterRoot);)Q=Q.parentNode;return ft}var lt=function(){function Y($,Q){this.stopPropagation=k.ZT,this.stopImmediatePropagation=k.ZT,this.preventDefault=k.ZT,this.type=Q.type,this.target=this.currentTarget=$.dom,this.pointerType=Q.pointerType,this.clientX=Q.clientX,this.clientY=Q.clientY}return Y}(),ot={mousedown:function(Y){Y=(0,b.OD)(this.dom,Y),this.__mayPointerCapture=[Y.zrX,Y.zrY],this.trigger("mousedown",Y)},mousemove:function(Y){Y=(0,b.OD)(this.dom,Y);var $=this.__mayPointerCapture;$&&(Y.zrX!==$[0]||Y.zrY!==$[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",Y)},mouseup:function(Y){Y=(0,b.OD)(this.dom,Y),this.__togglePointerCapture(!1),this.trigger("mouseup",Y)},mouseout:function(Y){Y=(0,b.OD)(this.dom,Y);var $=Y.toElement||Y.relatedTarget;ut(this,$)||(this.__pointerCapturing&&(Y.zrEventControl="no_globalout"),this.trigger("mouseout",Y))},wheel:function(Y){N=!0,Y=(0,b.OD)(this.dom,Y),this.trigger("mousewheel",Y)},mousewheel:function(Y){N||(Y=(0,b.OD)(this.dom,Y),this.trigger("mousewheel",Y))},touchstart:function(Y){Y=(0,b.OD)(this.dom,Y),H(Y),this.__lastTouchMoment=new Date,this.handler.processGesture(Y,"start"),ot.mousemove.call(this,Y),ot.mousedown.call(this,Y)},touchmove:function(Y){Y=(0,b.OD)(this.dom,Y),H(Y),this.handler.processGesture(Y,"change"),ot.mousemove.call(this,Y)},touchend:function(Y){Y=(0,b.OD)(this.dom,Y),H(Y),this.handler.processGesture(Y,"end"),ot.mouseup.call(this,Y),+new Date-+this.__lastTouchMoment<x&&ot.click.call(this,Y)},pointerdown:function(Y){ot.mousedown.call(this,Y)},pointermove:function(Y){G(Y)||ot.mousemove.call(this,Y)},pointerup:function(Y){ot.mouseup.call(this,Y)},pointerout:function(Y){G(Y)||ot.mouseout.call(this,Y)}};k.S6(["click","dblclick","contextmenu"],function(Y){ot[Y]=function($){$=(0,b.OD)(this.dom,$),this.trigger(Y,$)}});var q={pointermove:function(Y){G(Y)||q.mousemove.call(this,Y)},pointerup:function(Y){q.mouseup.call(this,Y)},mousemove:function(Y){this.trigger("mousemove",Y)},mouseup:function(Y){var $=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",Y),$&&(Y.zrEventControl="only_globalout",this.trigger("mouseout",Y))}};function tt(Y,$){var Q=$.domHandlers;C.Z.pointerEventsSupported?k.S6(w.pointer,function(ft){et($,ft,function(Tt){Q[ft].call(Y,Tt)})}):(C.Z.touchEventsSupported&&k.S6(w.touch,function(ft){et($,ft,function(Tt){Q[ft].call(Y,Tt),Z($)})}),k.S6(w.mouse,function(ft){et($,ft,function(Tt){Tt=(0,b.iP)(Tt),$.touching||Q[ft].call(Y,Tt)})}))}function j(Y,$){C.Z.pointerEventsSupported?k.S6(V.pointer,Q):C.Z.touchEventsSupported||k.S6(V.mouse,Q);function Q(ft){function Tt(wt){wt=(0,b.iP)(wt),ut(Y,wt.target)||(wt=X(Y,wt),$.domHandlers[ft].call(Y,wt))}et($,ft,Tt,{capture:!0})}}function et(Y,$,Q,ft){Y.mounted[$]=Q,Y.listenerOpts[$]=ft,(0,b.Oo)(Y.domTarget,$,Q,ft)}function ct(Y){var $=Y.mounted;for(var Q in $)$.hasOwnProperty(Q)&&(0,b.xg)(Y.domTarget,Q,$[Q],Y.listenerOpts[Q]);Y.mounted={}}var _t=function(){function Y($,Q){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=$,this.domHandlers=Q}return Y}(),St=function(Y){(0,v.ZT)($,Y);function $(Q,ft){var Tt=Y.call(this)||this;return Tt.__pointerCapturing=!1,Tt.dom=Q,Tt.painterRoot=ft,Tt._localHandlerScope=new _t(Q,ot),T&&(Tt._globalHandlerScope=new _t(document,q)),tt(Tt,Tt._localHandlerScope),Tt}return $.prototype.dispose=function(){ct(this._localHandlerScope),T&&ct(this._globalHandlerScope)},$.prototype.setCursor=function(Q){this.dom.style&&(this.dom.style.cursor=Q||"default")},$.prototype.__togglePointerCapture=function(Q){if(this.__mayPointerCapture=null,T&&+this.__pointerCapturing^+Q){this.__pointerCapturing=Q;var ft=this._globalHandlerScope;Q?j(this,ft):ct(ft)}},$}(R.Z),At=St,Ct=P(21092),Et=P(4990),Ot=P(38154);/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Wt={},Ut={};function Bt(Y){delete Ut[Y]}function Ht(Y){if(!Y)return!1;if(typeof Y=="string")return(0,Ct.L0)(Y,1)<Et.Ak;if(Y.colorStops){for(var $=Y.colorStops,Q=0,ft=$.length,Tt=0;Tt<ft;Tt++)Q+=(0,Ct.L0)($[Tt].color,1);return Q/=ft,Q<Et.Ak}return!1}var kt=function(){function Y($,Q,ft){var Tt=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,ft=ft||{},this.dom=Q,this.id=$;var wt=new o,bt=ft.renderer||"canvas";Wt[bt]||(bt=k.XP(Wt)[0]),ft.useDirtyRect=ft.useDirtyRect==null?!1:ft.useDirtyRect;var zt=new Wt[bt](Q,wt,ft,$),Qt=ft.ssr||zt.ssrOnly;this.storage=wt,this.painter=zt;var qt=!C.Z.node&&!C.Z.worker&&!Qt?new At(zt.getViewportRoot(),zt.root):null,re=ft.useCoarsePointer,ie=re==null||re==="auto"?C.Z.touchEventsSupported:!!re,oe=44,ue;ie&&(ue=k.pD(ft.pointerSize,oe)),this.handler=new n(wt,zt,qt,zt.root,ue),this.animation=new U({stage:{update:Qt?null:function(){return Tt._flush(!0)}}}),Qt||this.animation.start()}return Y.prototype.add=function($){this._disposed||!$||(this.storage.addRoot($),$.addSelfToZr(this),this.refresh())},Y.prototype.remove=function($){this._disposed||!$||(this.storage.delRoot($),$.removeSelfFromZr(this),this.refresh())},Y.prototype.configLayer=function($,Q){this._disposed||(this.painter.configLayer&&this.painter.configLayer($,Q),this.refresh())},Y.prototype.setBackgroundColor=function($){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor($),this.refresh(),this._backgroundColor=$,this._darkMode=Ht($))},Y.prototype.getBackgroundColor=function(){return this._backgroundColor},Y.prototype.setDarkMode=function($){this._darkMode=$},Y.prototype.isDarkMode=function(){return this._darkMode},Y.prototype.refreshImmediately=function($){this._disposed||($||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},Y.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},Y.prototype.flush=function(){this._disposed||this._flush(!1)},Y.prototype._flush=function($){var Q,ft=I();this._needsRefresh&&(Q=!0,this.refreshImmediately($)),this._needsRefreshHover&&(Q=!0,this.refreshHoverImmediately());var Tt=I();Q?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:Tt-ft})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},Y.prototype.setSleepAfterStill=function($){this._sleepAfterStill=$},Y.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},Y.prototype.refreshHover=function(){this._needsRefreshHover=!0},Y.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},Y.prototype.resize=function($){this._disposed||($=$||{},this.painter.resize($.width,$.height),this.handler.resize())},Y.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},Y.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},Y.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},Y.prototype.setCursorStyle=function($){this._disposed||this.handler.setCursorStyle($)},Y.prototype.findHover=function($,Q){if(!this._disposed)return this.handler.findHover($,Q)},Y.prototype.on=function($,Q,ft){return this._disposed||this.handler.on($,Q,ft),this},Y.prototype.off=function($,Q){this._disposed||this.handler.off($,Q)},Y.prototype.trigger=function($,Q){this._disposed||this.handler.trigger($,Q)},Y.prototype.clear=function(){if(!this._disposed){for(var $=this.storage.getRoots(),Q=0;Q<$.length;Q++)$[Q]instanceof Ot.Z&&$[Q].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},Y.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,Bt(this.id))},Y}();function Xt(Y,$){var Q=new kt(k.M8(),Y,$);return Ut[Q.id]=Q,Q}function Vt(Y){Y.dispose()}function st(){for(var Y in Ut)Ut.hasOwnProperty(Y)&&Ut[Y].dispose();Ut={}}function gt(Y){return Ut[Y]}function xt(Y,$){Wt[Y]=$}var Rt;function dt(Y){if(typeof Rt=="function")return Rt(Y)}function Mt(Y){Rt=Y}var vt="5.5.0"},4311:function(Zt,pt,P){"use strict";P.d(pt,{ZT:function(){return k}});/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var C=function(i,t){return C=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a])},C(i,t)};function k(i,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");C(i,t);function r(){this.constructor=i}i.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var v=function(){return v=Object.assign||function(t){for(var r,e=1,a=arguments.length;e<a;e++){r=arguments[e];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(t[s]=r[s])}return t},v.apply(this,arguments)};function F(i,t){var r={};for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&t.indexOf(e)<0&&(r[e]=i[e]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(i);a<e.length;a++)t.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(i,e[a])&&(r[e[a]]=i[e[a]]);return r}function z(i,t,r,e){var a=arguments.length,s=a<3?t:e===null?e=Object.getOwnPropertyDescriptor(t,r):e,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(i,t,r,e);else for(var D=i.length-1;D>=0;D--)(o=i[D])&&(s=(a<3?o(s):a>3?o(t,r,s):o(t,r))||s);return a>3&&s&&Object.defineProperty(t,r,s),s}function W(i,t){return function(r,e){t(r,e,i)}}function K(i,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(i,t)}function R(i,t,r,e){function a(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function D(L){try{I(e.next(L))}catch(U){o(U)}}function B(L){try{I(e.throw(L))}catch(U){o(U)}}function I(L){L.done?s(L.value):a(L.value).then(D,B)}I((e=e.apply(i,t||[])).next())})}function b(i,t){var r={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},e,a,s,o;return o={next:D(0),throw:D(1),return:D(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function D(I){return function(L){return B([I,L])}}function B(I){if(e)throw new TypeError("Generator is already executing.");for(;r;)try{if(e=1,a&&(s=I[0]&2?a.return:I[0]?a.throw||((s=a.return)&&s.call(a),0):a.next)&&!(s=s.call(a,I[1])).done)return s;switch(a=0,s&&(I=[I[0]&2,s.value]),I[0]){case 0:case 1:s=I;break;case 4:return r.label++,{value:I[1],done:!1};case 5:r.label++,a=I[1],I=[0];continue;case 7:I=r.ops.pop(),r.trys.pop();continue;default:if(s=r.trys,!(s=s.length>0&&s[s.length-1])&&(I[0]===6||I[0]===2)){r=0;continue}if(I[0]===3&&(!s||I[1]>s[0]&&I[1]<s[3])){r.label=I[1];break}if(I[0]===6&&r.label<s[1]){r.label=s[1],s=I;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(I);break}s[2]&&r.ops.pop(),r.trys.pop();continue}I=t.call(i,r)}catch(L){I=[6,L],a=0}finally{e=s=0}if(I[0]&5)throw I[1];return{value:I[0]?I[1]:void 0,done:!0}}}var A=Object.create?function(i,t,r,e){e===void 0&&(e=r),Object.defineProperty(i,e,{enumerable:!0,get:function(){return t[r]}})}:function(i,t,r,e){e===void 0&&(e=r),i[e]=t[r]};function S(i,t){for(var r in i)r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r)&&A(t,i,r)}function m(i){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&i[t],e=0;if(r)return r.call(i);if(i&&typeof i.length=="number")return{next:function(){return i&&e>=i.length&&(i=void 0),{value:i&&i[e++],done:!i}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function g(i,t){var r=typeof Symbol=="function"&&i[Symbol.iterator];if(!r)return i;var e=r.call(i),a,s=[],o;try{for(;(t===void 0||t-- >0)&&!(a=e.next()).done;)s.push(a.value)}catch(D){o={error:D}}finally{try{a&&!a.done&&(r=e.return)&&r.call(e)}finally{if(o)throw o.error}}return s}function y(){for(var i=[],t=0;t<arguments.length;t++)i=i.concat(g(arguments[t]));return i}function E(){for(var i=0,t=0,r=arguments.length;t<r;t++)i+=arguments[t].length;for(var e=Array(i),a=0,t=0;t<r;t++)for(var s=arguments[t],o=0,D=s.length;o<D;o++,a++)e[a]=s[o];return e}function h(i,t,r){if(r||arguments.length===2)for(var e=0,a=t.length,s;e<a;e++)(s||!(e in t))&&(s||(s=Array.prototype.slice.call(t,0,e)),s[e]=t[e]);return i.concat(s||t)}function c(i){return this instanceof c?(this.v=i,this):new c(i)}function _(i,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=r.apply(i,t||[]),a,s=[];return a={},o("next"),o("throw"),o("return"),a[Symbol.asyncIterator]=function(){return this},a;function o(x){e[x]&&(a[x]=function(T){return new Promise(function(w,V){s.push([x,T,w,V])>1||D(x,T)})})}function D(x,T){try{B(e[x](T))}catch(w){U(s[0][3],w)}}function B(x){x.value instanceof c?Promise.resolve(x.value.v).then(I,L):U(s[0][2],x)}function I(x){D("next",x)}function L(x){D("throw",x)}function U(x,T){x(T),s.shift(),s.length&&D(s[0][0],s[0][1])}}function M(i){var t,r;return t={},e("next"),e("throw",function(a){throw a}),e("return"),t[Symbol.iterator]=function(){return this},t;function e(a,s){t[a]=i[a]?function(o){return(r=!r)?{value:c(i[a](o)),done:a==="return"}:s?s(o):o}:s}}function l(i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=i[Symbol.asyncIterator],r;return t?t.call(i):(i=typeof m=="function"?m(i):i[Symbol.iterator](),r={},e("next"),e("throw"),e("return"),r[Symbol.asyncIterator]=function(){return this},r);function e(s){r[s]=i[s]&&function(o){return new Promise(function(D,B){o=i[s](o),a(D,B,o.done,o.value)})}}function a(s,o,D,B){Promise.resolve(B).then(function(I){s({value:I,done:D})},o)}}function O(i,t){return Object.defineProperty?Object.defineProperty(i,"raw",{value:t}):i.raw=t,i}var d=Object.create?function(i,t){Object.defineProperty(i,"default",{enumerable:!0,value:t})}:function(i,t){i.default=t};function f(i){if(i&&i.__esModule)return i;var t={};if(i!=null)for(var r in i)r!=="default"&&Object.prototype.hasOwnProperty.call(i,r)&&A(t,i,r);return d(t,i),t}function u(i){return i&&i.__esModule?i:{default:i}}function p(i,t,r,e){if(r==="a"&&!e)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?i!==t||!e:!t.has(i))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?e:r==="a"?e.call(i):e?e.value:t.get(i)}function n(i,t,r,e,a){if(e==="m")throw new TypeError("Private method is not writable");if(e==="a"&&!a)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?i!==t||!a:!t.has(i))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e==="a"?a.call(i,r):a?a.value=r:t.set(i,r),r}}}]);
