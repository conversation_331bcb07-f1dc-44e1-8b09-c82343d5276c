<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>survey-server</artifactId>
        <groupId>cn.surveyking</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>flow</artifactId>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <version>6.7.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>flowable-spring-boot-starter-dmn</artifactId>
                    <groupId>org.flowable</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flowable-dmn-spring-configurator</artifactId>
                    <groupId>org.flowable</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flowable-spring-boot-starter-cmmn</artifactId>
                    <groupId>org.flowable</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flowable-form-spring-configurator</artifactId>
                    <groupId>org.flowable</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flowable-idm-spring-configurator</artifactId>
                    <groupId>org.flowable</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>flowable-cmmn-spring-configurator</artifactId>
                    <groupId>org.flowable</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.26</version>
        </dependency>
        
        <dependency>
            <groupId>cn.surveyking</groupId>
            <artifactId>shared</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

</project>