(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[4371],{83173:function(g,t,n){"use strict";n.r(t);var l=n(11849),d=n(92725),v=n(27400),M=n(9761),o=n(67294),s=n(85893),P=o.lazy(function(){return Promise.all([n.e(2225),n.e(9223),n.e(4968),n.e(7098),n.e(7115),n.e(7422),n.e(9278),n.e(5246),n.e(5785),n.e(701),n.e(3305),n.e(5362),n.e(9228),n.e(4839),n.e(1818),n.e(3844),n.e(5555),n.e(398),n.e(2622),n.e(3524),n.e(2750),n.e(1476),n.e(6894),n.e(1022),n.e(270)]).then(n.bind(n,60438))}),h=o.lazy(function(){return n.e(6853).then(n.bind(n,56853))}),m=(0,M.Pi)(function(E){var D=(0,v.a)(),O=D.isMobile;return(0,s.jsx)(o.Suspense,{fallback:(0,s.jsx)(d.Z,{}),children:O?(0,s.jsx)(h,(0,l.Z)({},E)):(0,s.jsx)(P,(0,l.Z)({},E))})});t.default=m}}]);
