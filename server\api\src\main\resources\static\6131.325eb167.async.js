(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[6131],{50675:function(lt,et,x){"use strict";var S=x(28991),p=x(67294),q=x(72961),nt=x(27029),l=function(a,i){return p.createElement(nt.Z,(0,S.Z)((0,S.Z)({},a),{},{ref:i,icon:q.Z}))};l.displayName="CheckCircleFilled",et.Z=p.forwardRef(l)},46533:function(lt,et,x){"use strict";x.d(et,{Z:function(){return a}});var S=x(28991),p=x(67294),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM695.5 365.7l-210.6 292a31.8 31.8 0 01-51.7 0L308.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H689c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-square",theme:"filled"},nt=q,l=x(27029),N=function(g,E){return p.createElement(l.Z,(0,S.Z)((0,S.Z)({},g),{},{ref:E,icon:nt}))};N.displayName="CheckSquareFilled";var a=p.forwardRef(N)},30071:function(lt,et,x){"use strict";var S=x(28991),p=x(67294),q=x(99011),nt=x(27029),l=function(a,i){return p.createElement(nt.Z,(0,S.Z)((0,S.Z)({},a),{},{ref:i,icon:q.Z}))};l.displayName="ClockCircleOutlined",et.Z=p.forwardRef(l)},8913:function(lt,et,x){"use strict";var S=x(28991),p=x(67294),q=x(1085),nt=x(27029),l=function(a,i){return p.createElement(nt.Z,(0,S.Z)((0,S.Z)({},a),{},{ref:i,icon:q.Z}))};l.displayName="CloseCircleFilled",et.Z=p.forwardRef(l)},36862:function(lt,et,x){"use strict";x.d(et,{Z:function(){return a}});var S=x(28991),p=x(67294),q={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112c17.7 0 32 14.3 32 32v736c0 17.7-14.3 32-32 32H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32zM639.98 338.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-square",theme:"filled"},nt=q,l=x(27029),N=function(g,E){return p.createElement(l.Z,(0,S.Z)((0,S.Z)({},g),{},{ref:E,icon:nt}))};N.displayName="CloseSquareFilled";var a=p.forwardRef(N)},41687:function(lt,et,x){"use strict";x.d(et,{Z:function(){return a}});var S=x(28991),p=x(67294),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM512 533c-85.5 0-155.6 67.3-160 151.6a8 8 0 008 8.4h48.1c4.2 0 7.8-3.2 8.1-7.4C420 636.1 461.5 597 512 597s92.1 39.1 95.8 88.6c.3 4.2 3.9 7.4 8.1 7.4H664a8 8 0 008-8.4C667.6 600.3 597.5 533 512 533z"}}]},name:"frown",theme:"outlined"},nt=q,l=x(27029),N=function(g,E){return p.createElement(l.Z,(0,S.Z)((0,S.Z)({},g),{},{ref:E,icon:nt}))};N.displayName="FrownOutlined";var a=p.forwardRef(N)},57206:function(lt,et,x){"use strict";x.d(et,{Z:function(){return a}});var S=x(28991),p=x(67294),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M923 283.6a260.04 260.04 0 00-56.9-82.8 264.4 264.4 0 00-84-55.5A265.34 265.34 0 00679.7 125c-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5a258.44 258.44 0 00-56.9 82.8c-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3.1-35.3-7-69.6-20.9-101.9zM512 814.8S156 586.7 156 385.5C156 283.6 240.3 201 344.3 201c73.1 0 136.5 40.8 167.7 100.4C543.2 241.8 606.6 201 679.7 201c104 0 188.3 82.6 188.3 184.5 0 201.2-356 429.3-356 429.3z"}}]},name:"heart",theme:"outlined"},nt=q,l=x(27029),N=function(g,E){return p.createElement(l.Z,(0,S.Z)((0,S.Z)({},g),{},{ref:E,icon:nt}))};N.displayName="HeartOutlined";var a=p.forwardRef(N)},46870:function(lt,et,x){"use strict";x.d(et,{Z:function(){return a}});var S=x(28991),p=x(67294),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 565H360c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}}]},name:"meh",theme:"outlined"},nt=q,l=x(27029),N=function(g,E){return p.createElement(l.Z,(0,S.Z)((0,S.Z)({},g),{},{ref:E,icon:nt}))};N.displayName="MehOutlined";var a=p.forwardRef(N)},81473:function(lt,et,x){"use strict";x.d(et,{Z:function(){return a}});var S=x(28991),p=x(67294),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M136 384h56c4.4 0 8-3.6 8-8V200h176c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-37.6 0-68 30.4-68 68v180c0 4.4 3.6 8 8 8zm512-184h176v176c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V196c0-37.6-30.4-68-68-68H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zM376 824H200V648c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v180c0 37.6 30.4 68 68 68h180c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm512-184h-56c-4.4 0-8 3.6-8 8v176H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h180c37.6 0 68-30.4 68-68V648c0-4.4-3.6-8-8-8zm16-164H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"scan",theme:"outlined"},nt=q,l=x(27029),N=function(g,E){return p.createElement(l.Z,(0,S.Z)((0,S.Z)({},g),{},{ref:E,icon:nt}))};N.displayName="ScanOutlined";var a=p.forwardRef(N)},18613:function(lt,et,x){"use strict";x.d(et,{Z:function(){return a}});var S=x(28991),p=x(67294),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"}}]},name:"smile",theme:"outlined"},nt=q,l=x(27029),N=function(g,E){return p.createElement(l.Z,(0,S.Z)((0,S.Z)({},g),{},{ref:E,icon:nt}))};N.displayName="SmileOutlined";var a=p.forwardRef(N)},89366:function(lt,et,x){"use strict";x.d(et,{Z:function(){return a}});var S=x(28991),p=x(67294),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"},nt=q,l=x(27029),N=function(g,E){return p.createElement(l.Z,(0,S.Z)((0,S.Z)({},g),{},{ref:E,icon:nt}))};N.displayName="UserOutlined";var a=p.forwardRef(N)},58136:function(){},65011:function(lt,et,x){"use strict";var S=x(38663),p=x.n(S),q=x(58136),nt=x.n(q),l=x(43358)},40217:function(lt,et,x){(function(S,p){p(et,x(67294))})(this,function(S,p){"use strict";function q(t,e,n,o,s,r,u){try{var c=t[r](u),d=c.value}catch(f){return void n(f)}c.done?e(d):Promise.resolve(d).then(o,s)}function nt(t){return function(){var e=this,n=arguments;return new Promise(function(o,s){var r=t.apply(e,n);function u(d){q(r,o,s,u,c,"next",d)}function c(d){q(r,o,s,u,c,"throw",d)}u(void 0)})}}function l(){return(l=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function N(t,e){if(t==null)return{};var n,o,s={},r=Object.keys(t);for(o=0;o<r.length;o++)n=r[o],e.indexOf(n)>=0||(s[n]=t[n]);return s}function a(t){var e=function(n,o){if(typeof n!="object"||n===null)return n;var s=n[Symbol.toPrimitive];if(s!==void 0){var r=s.call(n,o||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(o==="string"?String:Number)(n)}(t,"string");return typeof e=="symbol"?e:String(e)}p=p&&Object.prototype.hasOwnProperty.call(p,"default")?p.default:p;var i={init:"init"},g=function(t){var e=t.value;return e===void 0?"":e},E=function(){return p.createElement(p.Fragment,null,"\xA0")},A={Cell:g,width:150,minWidth:0,maxWidth:Number.MAX_SAFE_INTEGER};function J(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.reduce(function(o,s){var r=s.style,u=s.className;return o=l({},o,{},N(s,["style","className"])),r&&(o.style=o.style?l({},o.style||{},{},r||{}):r),u&&(o.className=o.className?o.className+" "+u:u),o.className===""&&delete o.className,o},{})}var F=function(t,e){return e===void 0&&(e={}),function(n){return n===void 0&&(n={}),[].concat(t,[n]).reduce(function(o,s){return function r(u,c,d){return typeof c=="function"?r({},c(u,d)):Array.isArray(c)?J.apply(void 0,[u].concat(c)):J(u,c)}(o,s,l({},e,{userProps:n}))},{})}},H=function(t,e,n,o){return n===void 0&&(n={}),t.reduce(function(s,r){return r(s,n)},e)},$=function(t,e,n){return n===void 0&&(n={}),t.forEach(function(o){o(e,n)})};function j(t,e,n,o){t.findIndex(function(s){return s.pluginName===n}),e.forEach(function(s){t.findIndex(function(r){return r.pluginName===s})})}function Y(t,e){return typeof t=="function"?t(e):t}function V(t){var e=p.useRef();return e.current=t,p.useCallback(function(){return e.current},[])}var Z=typeof document!="undefined"?p.useLayoutEffect:p.useEffect;function Q(t,e){var n=p.useRef(!1);Z(function(){n.current&&t(),n.current=!0},e)}function rt(t,e,n){return n===void 0&&(n={}),function(o,s){s===void 0&&(s={});var r=typeof o=="string"?e[o]:o;if(r===void 0)throw console.info(e),new Error("Renderer Error \u261D\uFE0F");return pt(r,l({},t,{column:e},n,{},s))}}function pt(t,e){return function(o){return typeof o=="function"&&(s=Object.getPrototypeOf(o)).prototype&&s.prototype.isReactComponent;var s}(n=t)||typeof n=="function"||function(o){return typeof o=="object"&&typeof o.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(o.$$typeof.description)}(n)?p.createElement(t,e):t;var n}function yt(t,e,n){return n===void 0&&(n=0),t.map(function(o){return St(o=l({},o,{parent:e,depth:n})),o.columns&&(o.columns=yt(o.columns,o,n+1)),o})}function vt(t){return Vt(t,"columns")}function St(t){var e=t.id,n=t.accessor,o=t.Header;if(typeof n=="string"){e=e||n;var s=n.split(".");n=function(r){return function(u,c,d){if(!c)return u;var f,w=typeof c=="function"?c:JSON.stringify(c),v=Nt.get(w)||function(){var m=function(h){return function y(C,b){if(b===void 0&&(b=[]),Array.isArray(C))for(var B=0;B<C.length;B+=1)y(C[B],b);else b.push(C);return b}(h).map(function(y){return String(y).replace(".","_")}).join(".").replace(Te,".").replace(Ge,"").split(".")}(c);return Nt.set(w,m),m}();try{f=v.reduce(function(m,h){return m[h]},u)}catch(m){}return f!==void 0?f:d}(r,s)}}if(!e&&typeof o=="string"&&o&&(e=o),!e&&t.columns)throw console.error(t),new Error('A column ID (or unique "Header" value) is required!');if(!e)throw console.error(t),new Error("A column ID (or string accessor) is required!");return Object.assign(t,{id:e,accessor:n}),t}function Lt(t,e){if(!e)throw new Error;return Object.assign(t,l({Header:E,Footer:E},A,{},e,{},t)),Object.assign(t,{originalWidth:t.width}),t}function ke(t,e,n){n===void 0&&(n=function(){return{}});for(var o=[],s=t,r=0,u=function(){return r++},c=function(){var d={headers:[]},f=[],w=s.some(function(v){return v.parent});s.forEach(function(v){var m,h=[].concat(f).reverse()[0];w&&(v.parent?m=l({},v.parent,{originalId:v.parent.id,id:v.parent.id+"_"+u(),headers:[v]},n(v)):m=Lt(l({originalId:v.id+"_placeholder",id:v.id+"_placeholder_"+u(),placeholderOf:v,headers:[v]},n(v)),e),h&&h.originalId===m.originalId?h.headers.push(v):f.push(m)),d.headers.push(v)}),o.push(d),s=f};s.length;)c();return o.reverse()}var Nt=new Map;function ht(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];for(var o=0;o<e.length;o+=1)if(e[o]!==void 0)return e[o]}function jt(t){if(typeof t=="function")return t}function Vt(t,e){var n=[];return function o(s){s.forEach(function(r){r[e]?o(r[e]):n.push(r)})}(t),n}function Zt(t,e){var n=e.manualExpandedKey,o=e.expanded,s=e.expandSubRows,r=s===void 0||s,u=[];return t.forEach(function(c){return function d(f,w){w===void 0&&(w=!0),f.isExpanded=f.original&&f.original[n]||o[f.id],f.canExpand=f.subRows&&!!f.subRows.length,w&&u.push(f),f.subRows&&f.subRows.length&&f.isExpanded&&f.subRows.forEach(function(v){return d(v,r)})}(c)}),u}function wt(t,e,n){return jt(t)||e[t]||n[t]||n.text}function Ft(t,e,n){return t?t(e,n):e===void 0}function bt(){throw new Error("React-Table: You have not called prepareRow(row) one or more rows you are attempting to render.")}var Ot=null,Te=/\[/g,Ge=/\]/g,ze=function(t){return l({role:"table"},t)},He=function(t){return l({role:"rowgroup"},t)},De=function(t,e){var n=e.column;return l({key:"header_"+n.id,colSpan:n.totalVisibleHeaderCount,role:"columnheader"},t)},Le=function(t,e){var n=e.column;return l({key:"footer_"+n.id,colSpan:n.totalVisibleHeaderCount},t)},Ne=function(t,e){return l({key:"headerGroup_"+e.index,role:"row"},t)},je=function(t,e){return l({key:"footerGroup_"+e.index},t)},Ve=function(t,e){return l({key:"row_"+e.row.id,role:"row"},t)},Ze=function(t,e){var n=e.cell;return l({key:"cell_"+n.row.id+"_"+n.column.id,role:"cell"},t)};function Ue(){return{useOptions:[],stateReducers:[],useControlledState:[],columns:[],columnsDeps:[],allColumns:[],allColumnsDeps:[],accessValue:[],materializedColumns:[],materializedColumnsDeps:[],useInstanceAfterData:[],visibleColumns:[],visibleColumnsDeps:[],headerGroups:[],headerGroupsDeps:[],useInstanceBeforeDimensions:[],useInstance:[],prepareRow:[],getTableProps:[ze],getTableBodyProps:[He],getHeaderGroupProps:[Ne],getFooterGroupProps:[je],getHeaderProps:[De],getFooterProps:[Le],getRowProps:[Ve],getCellProps:[Ze],useFinalInstance:[]}}i.resetHiddenColumns="resetHiddenColumns",i.toggleHideColumn="toggleHideColumn",i.setHiddenColumns="setHiddenColumns",i.toggleHideAllColumns="toggleHideAllColumns";var Ut=function(t){t.getToggleHiddenProps=[Ke],t.getToggleHideAllColumnsProps=[$e],t.stateReducers.push(Xe),t.useInstanceBeforeDimensions.push(Ye),t.headerGroupsDeps.push(function(e,n){var o=n.instance;return[].concat(e,[o.state.hiddenColumns])}),t.useInstance.push(Je)};Ut.pluginName="useColumnVisibility";var Ke=function(t,e){var n=e.column;return[t,{onChange:function(o){n.toggleHidden(!o.target.checked)},style:{cursor:"pointer"},checked:n.isVisible,title:"Toggle Column Visible"}]},$e=function(t,e){var n=e.instance;return[t,{onChange:function(o){n.toggleHideAllColumns(!o.target.checked)},style:{cursor:"pointer"},checked:!n.allColumnsHidden&&!n.state.hiddenColumns.length,title:"Toggle All Columns Hidden",indeterminate:!n.allColumnsHidden&&n.state.hiddenColumns.length}]};function Xe(t,e,n,o){if(e.type===i.init)return l({hiddenColumns:[]},t);if(e.type===i.resetHiddenColumns)return l({},t,{hiddenColumns:o.initialState.hiddenColumns||[]});if(e.type===i.toggleHideColumn){var s=(e.value!==void 0?e.value:!t.hiddenColumns.includes(e.columnId))?[].concat(t.hiddenColumns,[e.columnId]):t.hiddenColumns.filter(function(r){return r!==e.columnId});return l({},t,{hiddenColumns:s})}return e.type===i.setHiddenColumns?l({},t,{hiddenColumns:Y(e.value,t.hiddenColumns)}):e.type===i.toggleHideAllColumns?l({},t,{hiddenColumns:(e.value!==void 0?e.value:!t.hiddenColumns.length)?o.allColumns.map(function(r){return r.id}):[]}):void 0}function Ye(t){var e=t.headers,n=t.state.hiddenColumns;p.useRef(!1).current;var o=0;e.forEach(function(s){return o+=function r(u,c){u.isVisible=c&&!n.includes(u.id);var d=0;return u.headers&&u.headers.length?u.headers.forEach(function(f){return d+=r(f,u.isVisible)}):d=u.isVisible?1:0,u.totalVisibleHeaderCount=d,d}(s,!0)})}function Je(t){var e=t.columns,n=t.flatHeaders,o=t.dispatch,s=t.allColumns,r=t.getHooks,u=t.state.hiddenColumns,c=t.autoResetHiddenColumns,d=c===void 0||c,f=V(t),w=s.length===u.length,v=p.useCallback(function(b,B){return o({type:i.toggleHideColumn,columnId:b,value:B})},[o]),m=p.useCallback(function(b){return o({type:i.setHiddenColumns,value:b})},[o]),h=p.useCallback(function(b){return o({type:i.toggleHideAllColumns,value:b})},[o]),y=F(r().getToggleHideAllColumnsProps,{instance:f()});n.forEach(function(b){b.toggleHidden=function(B){o({type:i.toggleHideColumn,columnId:b.id,value:B})},b.getToggleHiddenProps=F(r().getToggleHiddenProps,{instance:f(),column:b})});var C=V(d);Q(function(){C()&&o({type:i.resetHiddenColumns})},[o,e]),Object.assign(t,{allColumnsHidden:w,toggleHideColumn:v,setHiddenColumns:m,toggleHideAllColumns:h,getToggleHideAllColumnsProps:y})}var Qe={},qe={},tn=function(t,e,n){return t},en=function(t,e){return t.subRows||[]},nn=function(t,e,n){return""+(n?[n.id,e].join("."):e)},on=function(t){return t};function Kt(t){var e=t.initialState,n=e===void 0?Qe:e,o=t.defaultColumn,s=o===void 0?qe:o,r=t.getSubRows,u=r===void 0?en:r,c=t.getRowId,d=c===void 0?nn:c,f=t.stateReducer,w=f===void 0?tn:f,v=t.useControlledState,m=v===void 0?on:v;return l({},N(t,["initialState","defaultColumn","getSubRows","getRowId","stateReducer","useControlledState"]),{initialState:n,defaultColumn:s,getSubRows:u,getRowId:d,stateReducer:w,useControlledState:m})}function $t(t,e){e===void 0&&(e=0);var n=0,o=0,s=0,r=0;return t.forEach(function(u){var c=u.headers;if(u.totalLeft=e,c&&c.length){var d=$t(c,e),f=d[0],w=d[1],v=d[2],m=d[3];u.totalMinWidth=f,u.totalWidth=w,u.totalMaxWidth=v,u.totalFlexWidth=m}else u.totalMinWidth=u.minWidth,u.totalWidth=Math.min(Math.max(u.minWidth,u.width),u.maxWidth),u.totalMaxWidth=u.maxWidth,u.totalFlexWidth=u.canResize?u.totalWidth:0;u.isVisible&&(e+=u.totalWidth,n+=u.totalMinWidth,o+=u.totalWidth,s+=u.totalMaxWidth,r+=u.totalFlexWidth)}),[n,o,s,r]}function rn(t){var e=t.data,n=t.rows,o=t.flatRows,s=t.rowsById,r=t.column,u=t.getRowId,c=t.getSubRows,d=t.accessValueHooks,f=t.getInstance;e.forEach(function(w,v){return function m(h,y,C,b,B){C===void 0&&(C=0);var z=h,_=u(h,y,b),R=s[_];if(R)R.subRows&&R.originalSubRows.forEach(function(P,k){return m(P,k,C+1,R)});else if((R={id:_,original:z,index:y,depth:C,cells:[{}]}).cells.map=bt,R.cells.filter=bt,R.cells.forEach=bt,R.cells[0].getCellProps=bt,R.values={},B.push(R),o.push(R),s[_]=R,R.originalSubRows=c(h,y),R.originalSubRows){var G=[];R.originalSubRows.forEach(function(P,k){return m(P,k,C+1,R,G)}),R.subRows=G}r.accessor&&(R.values[r.id]=r.accessor(h,y,R,B,e)),R.values[r.id]=H(d,R.values[r.id],{row:R,column:r,instance:f()})}(w,v,0,void 0,n)})}i.resetExpanded="resetExpanded",i.toggleRowExpanded="toggleRowExpanded",i.toggleAllRowsExpanded="toggleAllRowsExpanded";var Xt=function(t){t.getToggleAllRowsExpandedProps=[sn],t.getToggleRowExpandedProps=[un],t.stateReducers.push(an),t.useInstance.push(ln),t.prepareRow.push(cn)};Xt.pluginName="useExpanded";var sn=function(t,e){var n=e.instance;return[t,{onClick:function(o){n.toggleAllRowsExpanded()},style:{cursor:"pointer"},title:"Toggle All Rows Expanded"}]},un=function(t,e){var n=e.row;return[t,{onClick:function(){n.toggleRowExpanded()},style:{cursor:"pointer"},title:"Toggle Row Expanded"}]};function an(t,e,n,o){if(e.type===i.init)return l({expanded:{}},t);if(e.type===i.resetExpanded)return l({},t,{expanded:o.initialState.expanded||{}});if(e.type===i.toggleAllRowsExpanded){var s=e.value,r=o.rowsById,u=Object.keys(r).length===Object.keys(t.expanded).length;if(s!==void 0?s:!u){var c={};return Object.keys(r).forEach(function(y){c[y]=!0}),l({},t,{expanded:c})}return l({},t,{expanded:{}})}if(e.type===i.toggleRowExpanded){var d,f=e.id,w=e.value,v=t.expanded[f],m=w!==void 0?w:!v;if(!v&&m)return l({},t,{expanded:l({},t.expanded,(d={},d[f]=!0,d))});if(v&&!m){var h=t.expanded;return h[f],l({},t,{expanded:N(h,[f].map(a))})}return t}}function ln(t){var e=t.data,n=t.rows,o=t.rowsById,s=t.manualExpandedKey,r=s===void 0?"expanded":s,u=t.paginateExpandedRows,c=u===void 0||u,d=t.expandSubRows,f=d===void 0||d,w=t.autoResetExpanded,v=w===void 0||w,m=t.getHooks,h=t.plugins,y=t.state.expanded,C=t.dispatch;j(h,["useSortBy","useGroupBy","usePivotColumns","useGlobalFilter"],"useExpanded");var b=V(v),B=Boolean(Object.keys(o).length&&Object.keys(y).length);B&&Object.keys(o).some(function(D){return!y[D]})&&(B=!1),Q(function(){b()&&C({type:i.resetExpanded})},[C,e]);var z=p.useCallback(function(D,O){C({type:i.toggleRowExpanded,id:D,value:O})},[C]),_=p.useCallback(function(D){return C({type:i.toggleAllRowsExpanded,value:D})},[C]),R=p.useMemo(function(){return c?Zt(n,{manualExpandedKey:r,expanded:y,expandSubRows:f}):n},[c,n,r,y,f]),G=p.useMemo(function(){return function(D){var O=0;return Object.keys(D).forEach(function(W){var X=W.split(".");O=Math.max(O,X.length)}),O}(y)},[y]),P=V(t),k=F(m().getToggleAllRowsExpandedProps,{instance:P()});Object.assign(t,{preExpandedRows:n,expandedRows:R,rows:R,expandedDepth:G,isAllRowsExpanded:B,toggleRowExpanded:z,toggleAllRowsExpanded:_,getToggleAllRowsExpandedProps:k})}function cn(t,e){var n=e.instance.getHooks,o=e.instance;t.toggleRowExpanded=function(s){return o.toggleRowExpanded(t.id,s)},t.getToggleRowExpandedProps=F(n().getToggleRowExpandedProps,{instance:o,row:t})}var Yt=function(t,e,n){return t=t.filter(function(o){return e.some(function(s){var r=o.values[s];return String(r).toLowerCase().includes(String(n).toLowerCase())})})};Yt.autoRemove=function(t){return!t};var Jt=function(t,e,n){return t.filter(function(o){return e.some(function(s){var r=o.values[s];return r===void 0||String(r).toLowerCase()===String(n).toLowerCase()})})};Jt.autoRemove=function(t){return!t};var Qt=function(t,e,n){return t.filter(function(o){return e.some(function(s){var r=o.values[s];return r===void 0||String(r)===String(n)})})};Qt.autoRemove=function(t){return!t};var qt=function(t,e,n){return t.filter(function(o){return e.some(function(s){return o.values[s].includes(n)})})};qt.autoRemove=function(t){return!t||!t.length};var te=function(t,e,n){return t.filter(function(o){return e.some(function(s){var r=o.values[s];return r&&r.length&&n.every(function(u){return r.includes(u)})})})};te.autoRemove=function(t){return!t||!t.length};var ee=function(t,e,n){return t.filter(function(o){return e.some(function(s){var r=o.values[s];return r&&r.length&&n.some(function(u){return r.includes(u)})})})};ee.autoRemove=function(t){return!t||!t.length};var ne=function(t,e,n){return t.filter(function(o){return e.some(function(s){var r=o.values[s];return n.includes(r)})})};ne.autoRemove=function(t){return!t||!t.length};var oe=function(t,e,n){return t.filter(function(o){return e.some(function(s){return o.values[s]===n})})};oe.autoRemove=function(t){return t===void 0};var re=function(t,e,n){return t.filter(function(o){return e.some(function(s){return o.values[s]==n})})};re.autoRemove=function(t){return t==null};var ie=function(t,e,n){var o=n||[],s=o[0],r=o[1];if((s=typeof s=="number"?s:-1/0)>(r=typeof r=="number"?r:1/0)){var u=s;s=r,r=u}return t.filter(function(c){return e.some(function(d){var f=c.values[d];return f>=s&&f<=r})})};ie.autoRemove=function(t){return!t||typeof t[0]!="number"&&typeof t[1]!="number"};var Rt=Object.freeze({__proto__:null,text:Yt,exactText:Jt,exactTextCase:Qt,includes:qt,includesAll:te,includesSome:ee,includesValue:ne,exact:oe,equals:re,between:ie});i.resetFilters="resetFilters",i.setFilter="setFilter",i.setAllFilters="setAllFilters";var se=function(t){t.stateReducers.push(dn),t.useInstance.push(fn)};function dn(t,e,n,o){if(e.type===i.init)return l({filters:[]},t);if(e.type===i.resetFilters)return l({},t,{filters:o.initialState.filters||[]});if(e.type===i.setFilter){var s=e.columnId,r=e.filterValue,u=o.allColumns,c=o.filterTypes,d=u.find(function(C){return C.id===s});if(!d)throw new Error("React-Table: Could not find a column with id: "+s);var f=wt(d.filter,c||{},Rt),w=t.filters.find(function(C){return C.id===s}),v=Y(r,w&&w.value);return Ft(f.autoRemove,v,d)?l({},t,{filters:t.filters.filter(function(C){return C.id!==s})}):l({},t,w?{filters:t.filters.map(function(C){return C.id===s?{id:s,value:v}:C})}:{filters:[].concat(t.filters,[{id:s,value:v}])})}if(e.type===i.setAllFilters){var m=e.filters,h=o.allColumns,y=o.filterTypes;return l({},t,{filters:Y(m,t.filters).filter(function(C){var b=h.find(function(B){return B.id===C.id});return!Ft(wt(b.filter,y||{},Rt).autoRemove,C.value,b)})})}}function fn(t){var e=t.data,n=t.rows,o=t.flatRows,s=t.rowsById,r=t.allColumns,u=t.filterTypes,c=t.manualFilters,d=t.defaultCanFilter,f=d!==void 0&&d,w=t.disableFilters,v=t.state.filters,m=t.dispatch,h=t.autoResetFilters,y=h===void 0||h,C=p.useCallback(function(P,k){m({type:i.setFilter,columnId:P,filterValue:k})},[m]),b=p.useCallback(function(P){m({type:i.setAllFilters,filters:P})},[m]);r.forEach(function(P){var k=P.id,D=P.accessor,O=P.defaultCanFilter,W=P.disableFilters;P.canFilter=D?ht(W!==!0&&void 0,w!==!0&&void 0,!0):ht(O,f,!1),P.setFilter=function(T){return C(P.id,T)};var X=v.find(function(T){return T.id===k});P.filterValue=X&&X.value});var B=p.useMemo(function(){if(c||!v.length)return[n,o,s];var P=[],k={};return[function D(O,W){W===void 0&&(W=0);var X=O;return(X=v.reduce(function(T,K){var U=K.id,tt=K.value,I=r.find(function(it){return it.id===U});if(!I)return T;W===0&&(I.preFilteredRows=T);var L=wt(I.filter,u||{},Rt);return L?(I.filteredRows=L(T,[U],tt),I.filteredRows):(console.warn("Could not find a valid 'column.filter' for column with the ID: "+I.id+"."),T)},O)).forEach(function(T){P.push(T),k[T.id]=T,T.subRows&&(T.subRows=T.subRows&&T.subRows.length>0?D(T.subRows,W+1):T.subRows)}),X}(n),P,k]},[c,v,n,o,s,r,u]),z=B[0],_=B[1],R=B[2];p.useMemo(function(){r.filter(function(P){return!v.find(function(k){return k.id===P.id})}).forEach(function(P){P.preFilteredRows=z,P.filteredRows=z})},[z,v,r]);var G=V(y);Q(function(){G()&&m({type:i.resetFilters})},[m,c?null:e]),Object.assign(t,{preFilteredRows:n,preFilteredFlatRows:o,preFilteredRowsById:s,filteredRows:z,filteredFlatRows:_,filteredRowsById:R,rows:z,flatRows:_,rowsById:R,setFilter:C,setAllFilters:b})}se.pluginName="useFilters",i.resetGlobalFilter="resetGlobalFilter",i.setGlobalFilter="setGlobalFilter";var ue=function(t){t.stateReducers.push(hn),t.useInstance.push(pn)};function hn(t,e,n,o){if(e.type===i.resetGlobalFilter)return l({},t,{globalFilter:o.initialState.globalFilter||void 0});if(e.type===i.setGlobalFilter){var s=e.filterValue,r=o.userFilterTypes,u=wt(o.globalFilter,r||{},Rt),c=Y(s,t.globalFilter);return Ft(u.autoRemove,c)?(t.globalFilter,N(t,["globalFilter"])):l({},t,{globalFilter:c})}}function pn(t){var e=t.data,n=t.rows,o=t.flatRows,s=t.rowsById,r=t.allColumns,u=t.filterTypes,c=t.globalFilter,d=t.manualGlobalFilter,f=t.state.globalFilter,w=t.dispatch,v=t.autoResetGlobalFilter,m=v===void 0||v,h=t.disableGlobalFilter,y=p.useCallback(function(R){w({type:i.setGlobalFilter,filterValue:R})},[w]),C=p.useMemo(function(){if(d||f===void 0)return[n,o,s];var R=[],G={},P=wt(c,u||{},Rt);if(!P)return console.warn("Could not find a valid 'globalFilter' option."),n;r.forEach(function(D){var O=D.disableGlobalFilter;D.canFilter=ht(O!==!0&&void 0,h!==!0&&void 0,!0)});var k=r.filter(function(D){return D.canFilter===!0});return[function D(O){return(O=P(O,k.map(function(W){return W.id}),f)).forEach(function(W){R.push(W),G[W.id]=W,W.subRows=W.subRows&&W.subRows.length?D(W.subRows):W.subRows}),O}(n),R,G]},[d,f,c,u,r,n,o,s,h]),b=C[0],B=C[1],z=C[2],_=V(m);Q(function(){_()&&w({type:i.resetGlobalFilter})},[w,d?null:e]),Object.assign(t,{preGlobalFilteredRows:n,preGlobalFilteredFlatRows:o,preGlobalFilteredRowsById:s,globalFilteredRows:b,globalFilteredFlatRows:B,globalFilteredRowsById:z,rows:b,flatRows:B,rowsById:z,setGlobalFilter:y,disableGlobalFilter:h})}function ae(t,e){return e.reduce(function(n,o){return n+(typeof o=="number"?o:0)},0)}ue.pluginName="useGlobalFilter";var le=Object.freeze({__proto__:null,sum:ae,min:function(t){var e=t[0]||0;return t.forEach(function(n){typeof n=="number"&&(e=Math.min(e,n))}),e},max:function(t){var e=t[0]||0;return t.forEach(function(n){typeof n=="number"&&(e=Math.max(e,n))}),e},minMax:function(t){var e=t[0]||0,n=t[0]||0;return t.forEach(function(o){typeof o=="number"&&(e=Math.min(e,o),n=Math.max(n,o))}),e+".."+n},average:function(t){return ae(0,t)/t.length},median:function(t){if(!t.length)return null;var e=Math.floor(t.length/2),n=[].concat(t).sort(function(o,s){return o-s});return t.length%2!=0?n[e]:(n[e-1]+n[e])/2},unique:function(t){return Array.from(new Set(t).values())},uniqueCount:function(t){return new Set(t).size},count:function(t){return t.length}}),gn=[],vn={};i.resetGroupBy="resetGroupBy",i.setGroupBy="setGroupBy",i.toggleGroupBy="toggleGroupBy";var ce=function(t){t.getGroupByToggleProps=[mn],t.stateReducers.push(yn),t.visibleColumnsDeps.push(function(e,n){var o=n.instance;return[].concat(e,[o.state.groupBy])}),t.visibleColumns.push(wn),t.useInstance.push(Cn),t.prepareRow.push(Sn)};ce.pluginName="useGroupBy";var mn=function(t,e){var n=e.header;return[t,{onClick:n.canGroupBy?function(o){o.persist(),n.toggleGroupBy()}:void 0,style:{cursor:n.canGroupBy?"pointer":void 0},title:"Toggle GroupBy"}]};function yn(t,e,n,o){if(e.type===i.init)return l({groupBy:[]},t);if(e.type===i.resetGroupBy)return l({},t,{groupBy:o.initialState.groupBy||[]});if(e.type===i.setGroupBy)return l({},t,{groupBy:e.value});if(e.type===i.toggleGroupBy){var s=e.columnId,r=e.value,u=r!==void 0?r:!t.groupBy.includes(s);return l({},t,u?{groupBy:[].concat(t.groupBy,[s])}:{groupBy:t.groupBy.filter(function(c){return c!==s})})}}function wn(t,e){var n=e.instance.state.groupBy,o=n.map(function(r){return t.find(function(u){return u.id===r})}).filter(Boolean),s=t.filter(function(r){return!n.includes(r.id)});return(t=[].concat(o,s)).forEach(function(r){r.isGrouped=n.includes(r.id),r.groupedIndex=n.indexOf(r.id)}),t}var Rn={};function Cn(t){var e=t.data,n=t.rows,o=t.flatRows,s=t.rowsById,r=t.allColumns,u=t.flatHeaders,c=t.groupByFn,d=c===void 0?de:c,f=t.manualGroupBy,w=t.aggregations,v=w===void 0?Rn:w,m=t.plugins,h=t.state.groupBy,y=t.dispatch,C=t.autoResetGroupBy,b=C===void 0||C,B=t.disableGroupBy,z=t.defaultCanGroupBy,_=t.getHooks;j(m,["useColumnOrder","useFilters"],"useGroupBy");var R=V(t);r.forEach(function(I){var L=I.accessor,it=I.defaultGroupBy,ct=I.disableGroupBy;I.canGroupBy=L?ht(I.canGroupBy,ct!==!0&&void 0,B!==!0&&void 0,!0):ht(I.canGroupBy,it,z,!1),I.canGroupBy&&(I.toggleGroupBy=function(){return t.toggleGroupBy(I.id)}),I.Aggregated=I.Aggregated||I.Cell});var G=p.useCallback(function(I,L){y({type:i.toggleGroupBy,columnId:I,value:L})},[y]),P=p.useCallback(function(I){y({type:i.setGroupBy,value:I})},[y]);u.forEach(function(I){I.getGroupByToggleProps=F(_().getGroupByToggleProps,{instance:R(),header:I})});var k=p.useMemo(function(){if(f||!h.length)return[n,o,s,gn,vn,o,s];var I=h.filter(function(at){return r.find(function(mt){return mt.id===at})}),L=[],it={},ct=[],M={},ot=[],st={},dt=function at(mt,gt,Ie){if(gt===void 0&&(gt=0),gt===I.length)return mt.map(function(Pt){return l({},Pt,{depth:gt})});var zt=I[gt],co=d(mt,zt);return Object.entries(co).map(function(Pt,fo){var Fe=Pt[0],Et=Pt[1],_t=zt+":"+Fe,Oe=at(Et,gt+1,_t=Ie?Ie+">"+_t:_t),Me=gt?Vt(Et,"leafRows"):Et,ho=function(ft,Ht,go){var Bt={};return r.forEach(function(ut){if(I.includes(ut.id))Bt[ut.id]=Ht[0]?Ht[0].values[ut.id]:null;else{var Ae=typeof ut.aggregate=="function"?ut.aggregate:v[ut.aggregate]||le[ut.aggregate];if(Ae){var vo=Ht.map(function(It){return It.values[ut.id]}),mo=ft.map(function(It){var Dt=It.values[ut.id];if(!go&&ut.aggregateValue){var We=typeof ut.aggregateValue=="function"?ut.aggregateValue:v[ut.aggregateValue]||le[ut.aggregateValue];if(!We)throw console.info({column:ut}),new Error("React Table: Invalid column.aggregateValue option for column listed above");Dt=We(Dt,It,ut)}return Dt});Bt[ut.id]=Ae(mo,vo)}else{if(ut.aggregate)throw console.info({column:ut}),new Error("React Table: Invalid column.aggregate option for column listed above");Bt[ut.id]=null}}}),Bt}(Me,Et,gt),po={id:_t,isGrouped:!0,groupByID:zt,groupByVal:Fe,values:ho,subRows:Oe,leafRows:Me,depth:gt,index:fo};return Oe.forEach(function(ft){L.push(ft),it[ft.id]=ft,ft.isGrouped?(ct.push(ft),M[ft.id]=ft):(ot.push(ft),st[ft.id]=ft)}),po})}(n);return dt.forEach(function(at){L.push(at),it[at.id]=at,at.isGrouped?(ct.push(at),M[at.id]=at):(ot.push(at),st[at.id]=at)}),[dt,L,it,ct,M,ot,st]},[f,h,n,o,s,r,v,d]),D=k[0],O=k[1],W=k[2],X=k[3],T=k[4],K=k[5],U=k[6],tt=V(b);Q(function(){tt()&&y({type:i.resetGroupBy})},[y,f?null:e]),Object.assign(t,{preGroupedRows:n,preGroupedFlatRow:o,preGroupedRowsById:s,groupedRows:D,groupedFlatRows:O,groupedRowsById:W,onlyGroupedFlatRows:X,onlyGroupedRowsById:T,nonGroupedFlatRows:K,nonGroupedRowsById:U,rows:D,flatRows:O,rowsById:W,toggleGroupBy:G,setGroupBy:P})}function Sn(t){t.allCells.forEach(function(e){var n;e.isGrouped=e.column.isGrouped&&e.column.id===t.groupByID,e.isPlaceholder=!e.isGrouped&&e.column.isGrouped,e.isAggregated=!e.isGrouped&&!e.isPlaceholder&&((n=t.subRows)==null?void 0:n.length)})}function de(t,e){return t.reduce(function(n,o,s){var r=""+o.values[e];return n[r]=Array.isArray(n[r])?n[r]:[],n[r].push(o),n},{})}var fe=/([0-9]+)/gm;function Mt(t,e){return t===e?0:t>e?1:-1}function Ct(t,e,n){return[t.values[n],e.values[n]]}function he(t){return typeof t=="number"?isNaN(t)||t===1/0||t===-1/0?"":String(t):typeof t=="string"?t:""}var bn=Object.freeze({__proto__:null,alphanumeric:function(t,e,n){var o=Ct(t,e,n),s=o[0],r=o[1];for(s=he(s),r=he(r),s=s.split(fe).filter(Boolean),r=r.split(fe).filter(Boolean);s.length&&r.length;){var u=s.shift(),c=r.shift(),d=parseInt(u,10),f=parseInt(c,10),w=[d,f].sort();if(isNaN(w[0])){if(u>c)return 1;if(c>u)return-1}else{if(isNaN(w[1]))return isNaN(d)?-1:1;if(d>f)return 1;if(f>d)return-1}}return s.length-r.length},datetime:function(t,e,n){var o=Ct(t,e,n),s=o[0],r=o[1];return Mt(s=s.getTime(),r=r.getTime())},basic:function(t,e,n){var o=Ct(t,e,n);return Mt(o[0],o[1])},string:function(t,e,n){var o=Ct(t,e,n),s=o[0],r=o[1];for(s=s.split("").filter(Boolean),r=r.split("").filter(Boolean);s.length&&r.length;){var u=s.shift(),c=r.shift(),d=u.toLowerCase(),f=c.toLowerCase();if(d>f)return 1;if(f>d)return-1;if(u>c)return 1;if(c>u)return-1}return s.length-r.length},number:function(t,e,n){var o=Ct(t,e,n),s=o[0],r=o[1],u=/[^0-9.]/gi;return Mt(s=Number(String(s).replace(u,"")),r=Number(String(r).replace(u,"")))}});i.resetSortBy="resetSortBy",i.setSortBy="setSortBy",i.toggleSortBy="toggleSortBy",i.clearSortBy="clearSortBy",A.sortType="alphanumeric",A.sortDescFirst=!1;var pe=function(t){t.getSortByToggleProps=[xn],t.stateReducers.push(Pn),t.useInstance.push(En)};pe.pluginName="useSortBy";var xn=function(t,e){var n=e.instance,o=e.column,s=n.isMultiSortEvent,r=s===void 0?function(u){return u.shiftKey}:s;return[t,{onClick:o.canSort?function(u){u.persist(),o.toggleSortBy(void 0,!n.disableMultiSort&&r(u))}:void 0,style:{cursor:o.canSort?"pointer":void 0},title:o.canSort?"Toggle SortBy":void 0}]};function Pn(t,e,n,o){if(e.type===i.init)return l({sortBy:[]},t);if(e.type===i.resetSortBy)return l({},t,{sortBy:o.initialState.sortBy||[]});if(e.type===i.clearSortBy)return l({},t,{sortBy:t.sortBy.filter(function(R){return R.id!==e.columnId})});if(e.type===i.setSortBy)return l({},t,{sortBy:e.sortBy});if(e.type===i.toggleSortBy){var s,r=e.columnId,u=e.desc,c=e.multi,d=o.allColumns,f=o.disableMultiSort,w=o.disableSortRemove,v=o.disableMultiRemove,m=o.maxMultiSortColCount,h=m===void 0?Number.MAX_SAFE_INTEGER:m,y=t.sortBy,C=d.find(function(R){return R.id===r}).sortDescFirst,b=y.find(function(R){return R.id===r}),B=y.findIndex(function(R){return R.id===r}),z=u!=null,_=[];return(s=!f&&c?b?"toggle":"add":B!==y.length-1||y.length!==1?"replace":b?"toggle":"replace")!=="toggle"||w||z||c&&v||!(b&&b.desc&&!C||!b.desc&&C)||(s="remove"),s==="replace"?_=[{id:r,desc:z?u:C}]:s==="add"?(_=[].concat(y,[{id:r,desc:z?u:C}])).splice(0,_.length-h):s==="toggle"?_=y.map(function(R){return R.id===r?l({},R,{desc:z?u:!b.desc}):R}):s==="remove"&&(_=y.filter(function(R){return R.id!==r})),l({},t,{sortBy:_})}}function En(t){var e=t.data,n=t.rows,o=t.flatRows,s=t.allColumns,r=t.orderByFn,u=r===void 0?ge:r,c=t.sortTypes,d=t.manualSortBy,f=t.defaultCanSort,w=t.disableSortBy,v=t.flatHeaders,m=t.state.sortBy,h=t.dispatch,y=t.plugins,C=t.getHooks,b=t.autoResetSortBy,B=b===void 0||b;j(y,["useFilters","useGlobalFilter","useGroupBy","usePivotColumns"],"useSortBy");var z=p.useCallback(function(O){h({type:i.setSortBy,sortBy:O})},[h]),_=p.useCallback(function(O,W,X){h({type:i.toggleSortBy,columnId:O,desc:W,multi:X})},[h]),R=V(t);v.forEach(function(O){var W=O.accessor,X=O.canSort,T=O.disableSortBy,K=O.id,U=W?ht(T!==!0&&void 0,w!==!0&&void 0,!0):ht(f,X,!1);O.canSort=U,O.canSort&&(O.toggleSortBy=function(I,L){return _(O.id,I,L)},O.clearSortBy=function(){h({type:i.clearSortBy,columnId:O.id})}),O.getSortByToggleProps=F(C().getSortByToggleProps,{instance:R(),column:O});var tt=m.find(function(I){return I.id===K});O.isSorted=!!tt,O.sortedIndex=m.findIndex(function(I){return I.id===K}),O.isSortedDesc=O.isSorted?tt.desc:void 0});var G=p.useMemo(function(){if(d||!m.length)return[n,o];var O=[],W=m.filter(function(X){return s.find(function(T){return T.id===X.id})});return[function X(T){var K=u(T,W.map(function(U){var tt=s.find(function(it){return it.id===U.id});if(!tt)throw new Error("React-Table: Could not find a column with id: "+U.id+" while sorting");var I=tt.sortType,L=jt(I)||(c||{})[I]||bn[I];if(!L)throw new Error("React-Table: Could not find a valid sortType of '"+I+"' for column '"+U.id+"'.");return function(it,ct){return L(it,ct,U.id,U.desc)}}),W.map(function(U){var tt=s.find(function(I){return I.id===U.id});return tt&&tt.sortInverted?U.desc:!U.desc}));return K.forEach(function(U){O.push(U),U.subRows&&U.subRows.length!==0&&(U.subRows=X(U.subRows))}),K}(n),O]},[d,m,n,o,s,u,c]),P=G[0],k=G[1],D=V(B);Q(function(){D()&&h({type:i.resetSortBy})},[d?null:e]),Object.assign(t,{preSortedRows:n,preSortedFlatRows:o,sortedRows:P,sortedFlatRows:k,rows:P,flatRows:k,setSortBy:z,toggleSortBy:_})}function ge(t,e,n){return[].concat(t).sort(function(o,s){for(var r=0;r<e.length;r+=1){var u=e[r],c=n[r]===!1||n[r]==="desc",d=u(o,s);if(d!==0)return c?-d:d}return n[0]?o.index-s.index:s.index-o.index})}i.resetPage="resetPage",i.gotoPage="gotoPage",i.setPageSize="setPageSize";var ve=function(t){t.stateReducers.push(_n),t.useInstance.push(Bn)};function _n(t,e,n,o){if(e.type===i.init)return l({pageSize:10,pageIndex:0},t);if(e.type===i.resetPage)return l({},t,{pageIndex:o.initialState.pageIndex||0});if(e.type===i.gotoPage){var s=o.pageCount,r=o.page,u=Y(e.pageIndex,t.pageIndex),c=!1;return u>t.pageIndex?c=s===-1?r.length>=t.pageSize:u<s:u<t.pageIndex&&(c=u>-1),c?l({},t,{pageIndex:u}):t}if(e.type===i.setPageSize){var d=e.pageSize,f=t.pageSize*t.pageIndex;return l({},t,{pageIndex:Math.floor(f/d),pageSize:d})}}function Bn(t){var e=t.rows,n=t.autoResetPage,o=n===void 0||n,s=t.manualExpandedKey,r=s===void 0?"expanded":s,u=t.plugins,c=t.pageCount,d=t.paginateExpandedRows,f=d===void 0||d,w=t.expandSubRows,v=w===void 0||w,m=t.state,h=m.pageSize,y=m.pageIndex,C=m.expanded,b=m.globalFilter,B=m.filters,z=m.groupBy,_=m.sortBy,R=t.dispatch,G=t.data,P=t.manualPagination;j(u,["useGlobalFilter","useFilters","useGroupBy","useSortBy","useExpanded"],"usePagination");var k=V(o);Q(function(){k()&&R({type:i.resetPage})},[R,P?null:G,b,B,z,_]);var D=P?c:Math.ceil(e.length/h),O=p.useMemo(function(){return D>0?[].concat(new Array(D)).fill(null).map(function(L,it){return it}):[]},[D]),W=p.useMemo(function(){var L;if(P)L=e;else{var it=h*y,ct=it+h;L=e.slice(it,ct)}return f?L:Zt(L,{manualExpandedKey:r,expanded:C,expandSubRows:v})},[v,C,r,P,y,h,f,e]),X=y>0,T=D===-1?W.length>=h:y<D-1,K=p.useCallback(function(L){R({type:i.gotoPage,pageIndex:L})},[R]),U=p.useCallback(function(){return K(function(L){return L-1})},[K]),tt=p.useCallback(function(){return K(function(L){return L+1})},[K]),I=p.useCallback(function(L){R({type:i.setPageSize,pageSize:L})},[R]);Object.assign(t,{pageOptions:O,pageCount:D,page:W,canPreviousPage:X,canNextPage:T,gotoPage:K,previousPage:U,nextPage:tt,setPageSize:I})}ve.pluginName="usePagination",i.resetPivot="resetPivot",i.togglePivot="togglePivot";var me=function(t){t.getPivotToggleProps=[In],t.stateReducers.push(Fn),t.useInstanceAfterData.push(On),t.allColumns.push(Mn),t.accessValue.push(An),t.materializedColumns.push(Wn),t.materializedColumnsDeps.push(kn),t.visibleColumns.push(Tn),t.visibleColumnsDeps.push(Gn),t.useInstance.push(zn),t.prepareRow.push(Hn)};me.pluginName="usePivotColumns";var ye=[],In=function(t,e){var n=e.header;return[t,{onClick:n.canPivot?function(o){o.persist(),n.togglePivot()}:void 0,style:{cursor:n.canPivot?"pointer":void 0},title:"Toggle Pivot"}]};function Fn(t,e,n,o){if(e.type===i.init)return l({pivotColumns:ye},t);if(e.type===i.resetPivot)return l({},t,{pivotColumns:o.initialState.pivotColumns||ye});if(e.type===i.togglePivot){var s=e.columnId,r=e.value,u=r!==void 0?r:!t.pivotColumns.includes(s);return l({},t,u?{pivotColumns:[].concat(t.pivotColumns,[s])}:{pivotColumns:t.pivotColumns.filter(function(c){return c!==s})})}}function On(t){t.allColumns.forEach(function(e){e.isPivotSource=t.state.pivotColumns.includes(e.id)})}function Mn(t,e){var n=e.instance;return t.forEach(function(o){o.isPivotSource=n.state.pivotColumns.includes(o.id),o.uniqueValues=new Set}),t}function An(t,e){var n=e.column;return n.uniqueValues&&t!==void 0&&n.uniqueValues.add(t),t}function Wn(t,e){var n=e.instance,o=n.allColumns,s=n.state;if(!s.pivotColumns.length||!s.groupBy||!s.groupBy.length)return t;var r=s.pivotColumns.map(function(d){return o.find(function(f){return f.id===d})}).filter(Boolean),u=o.filter(function(d){return!d.isPivotSource&&!s.groupBy.includes(d.id)&&!s.pivotColumns.includes(d.id)}),c=vt(function d(f,w,v){f===void 0&&(f=0),v===void 0&&(v=[]);var m=r[f];return m?Array.from(m.uniqueValues).sort().map(function(h){var y=l({},m,{Header:m.PivotHeader||typeof m.header=="string"?m.Header+": "+h:h,isPivotGroup:!0,parent:w,depth:f,id:w?w.id+"."+m.id+"."+h:m.id+"."+h,pivotValue:h});return y.columns=d(f+1,y,[].concat(v,[function(C){return C.values[m.id]===h}])),y}):u.map(function(h){return l({},h,{canPivot:!1,isPivoted:!0,parent:w,depth:f,id:""+(w?w.id+"."+h.id:h.id),accessor:function(y,C,b){if(v.every(function(B){return B(b)}))return b.values[h.id]}})})}());return[].concat(t,c)}function kn(t,e){var n=e.instance.state,o=n.pivotColumns,s=n.groupBy;return[].concat(t,[o,s])}function Tn(t,e){var n=e.instance.state;return t=t.filter(function(o){return!o.isPivotSource}),n.pivotColumns.length&&n.groupBy&&n.groupBy.length&&(t=t.filter(function(o){return o.isGrouped||o.isPivoted})),t}function Gn(t,e){var n=e.instance;return[].concat(t,[n.state.pivotColumns,n.state.groupBy])}function zn(t){var e=t.columns,n=t.allColumns,o=t.flatHeaders,s=t.getHooks,r=t.plugins,u=t.dispatch,c=t.autoResetPivot,d=c===void 0||c,f=t.manaulPivot,w=t.disablePivot,v=t.defaultCanPivot;j(r,["useGroupBy"],"usePivotColumns");var m=V(t);n.forEach(function(y){var C=y.accessor,b=y.defaultPivot,B=y.disablePivot;y.canPivot=C?ht(y.canPivot,B!==!0&&void 0,w!==!0&&void 0,!0):ht(y.canPivot,b,v,!1),y.canPivot&&(y.togglePivot=function(){return t.togglePivot(y.id)}),y.Aggregated=y.Aggregated||y.Cell}),o.forEach(function(y){y.getPivotToggleProps=F(s().getPivotToggleProps,{instance:m(),header:y})});var h=V(d);Q(function(){h()&&u({type:i.resetPivot})},[u,f?null:e]),Object.assign(t,{togglePivot:function(y,C){u({type:i.togglePivot,columnId:y,value:C})}})}function Hn(t){t.allCells.forEach(function(e){e.isPivoted=e.column.isPivoted})}i.resetSelectedRows="resetSelectedRows",i.toggleAllRowsSelected="toggleAllRowsSelected",i.toggleRowSelected="toggleRowSelected",i.toggleAllPageRowsSelected="toggleAllPageRowsSelected";var we=function(t){t.getToggleRowSelectedProps=[Dn],t.getToggleAllRowsSelectedProps=[Ln],t.getToggleAllPageRowsSelectedProps=[Nn],t.stateReducers.push(jn),t.useInstance.push(Vn),t.prepareRow.push(Zn)};we.pluginName="useRowSelect";var Dn=function(t,e){var n=e.instance,o=e.row,s=n.manualRowSelectedKey,r=s===void 0?"isSelected":s;return[t,{onChange:function(u){o.toggleRowSelected(u.target.checked)},style:{cursor:"pointer"},checked:!(!o.original||!o.original[r])||o.isSelected,title:"Toggle Row Selected",indeterminate:o.isSomeSelected}]},Ln=function(t,e){var n=e.instance;return[t,{onChange:function(o){n.toggleAllRowsSelected(o.target.checked)},style:{cursor:"pointer"},checked:n.isAllRowsSelected,title:"Toggle All Rows Selected",indeterminate:Boolean(!n.isAllRowsSelected&&Object.keys(n.state.selectedRowIds).length)}]},Nn=function(t,e){var n=e.instance;return[t,{onChange:function(o){n.toggleAllPageRowsSelected(o.target.checked)},style:{cursor:"pointer"},checked:n.isAllPageRowsSelected,title:"Toggle All Current Page Rows Selected",indeterminate:Boolean(!n.isAllPageRowsSelected&&n.page.some(function(o){var s=o.id;return n.state.selectedRowIds[s]}))}]};function jn(t,e,n,o){if(e.type===i.init)return l({selectedRowIds:{}},t);if(e.type===i.resetSelectedRows)return l({},t,{selectedRowIds:o.initialState.selectedRowIds||{}});if(e.type===i.toggleAllRowsSelected){var s=e.value,r=o.isAllRowsSelected,u=o.rowsById,c=o.nonGroupedRowsById,d=c===void 0?u:c,f=s!==void 0?s:!r,w=Object.assign({},t.selectedRowIds);return f?Object.keys(d).forEach(function(K){w[K]=!0}):Object.keys(d).forEach(function(K){delete w[K]}),l({},t,{selectedRowIds:w})}if(e.type===i.toggleRowSelected){var v=e.id,m=e.value,h=o.rowsById,y=o.selectSubRows,C=y===void 0||y,b=o.getSubRows,B=t.selectedRowIds[v],z=m!==void 0?m:!B;if(B===z)return t;var _=l({},t.selectedRowIds);return function K(U){var tt=h[U];if(tt&&(tt.isGrouped||(z?_[U]=!0:delete _[U]),C&&b(tt)))return b(tt).forEach(function(I){return K(I.id)})}(v),l({},t,{selectedRowIds:_})}if(e.type===i.toggleAllPageRowsSelected){var R=e.value,G=o.page,P=o.rowsById,k=o.selectSubRows,D=k===void 0||k,O=o.isAllPageRowsSelected,W=o.getSubRows,X=R!==void 0?R:!O,T=l({},t.selectedRowIds);return G.forEach(function(K){return function U(tt){var I=P[tt];if(I.isGrouped||(X?T[tt]=!0:delete T[tt]),D&&W(I))return W(I).forEach(function(L){return U(L.id)})}(K.id)}),l({},t,{selectedRowIds:T})}return t}function Vn(t){var e=t.data,n=t.rows,o=t.getHooks,s=t.plugins,r=t.rowsById,u=t.nonGroupedRowsById,c=u===void 0?r:u,d=t.autoResetSelectedRows,f=d===void 0||d,w=t.state.selectedRowIds,v=t.selectSubRows,m=v===void 0||v,h=t.dispatch,y=t.page,C=t.getSubRows;j(s,["useFilters","useGroupBy","useSortBy","useExpanded","usePagination"],"useRowSelect");var b=p.useMemo(function(){var W=[];return n.forEach(function(X){var T=m?function K(U,tt,I){if(tt[U.id])return!0;var L=I(U);if(L&&L.length){var it=!0,ct=!1;return L.forEach(function(M){ct&&!it||(K(M,tt,I)?ct=!0:it=!1)}),!!it||!!ct&&null}return!1}(X,w,C):!!w[X.id];X.isSelected=!!T,X.isSomeSelected=T===null,T&&W.push(X)}),W},[n,m,w,C]),B=Boolean(Object.keys(c).length&&Object.keys(w).length),z=B;B&&Object.keys(c).some(function(W){return!w[W]})&&(B=!1),B||y&&y.length&&y.some(function(W){var X=W.id;return!w[X]})&&(z=!1);var _=V(f);Q(function(){_()&&h({type:i.resetSelectedRows})},[h,e]);var R=p.useCallback(function(W){return h({type:i.toggleAllRowsSelected,value:W})},[h]),G=p.useCallback(function(W){return h({type:i.toggleAllPageRowsSelected,value:W})},[h]),P=p.useCallback(function(W,X){return h({type:i.toggleRowSelected,id:W,value:X})},[h]),k=V(t),D=F(o().getToggleAllRowsSelectedProps,{instance:k()}),O=F(o().getToggleAllPageRowsSelectedProps,{instance:k()});Object.assign(t,{selectedFlatRows:b,isAllRowsSelected:B,isAllPageRowsSelected:z,toggleRowSelected:P,toggleAllRowsSelected:R,getToggleAllRowsSelectedProps:D,getToggleAllPageRowsSelectedProps:O,toggleAllPageRowsSelected:G})}function Zn(t,e){var n=e.instance;t.toggleRowSelected=function(o){return n.toggleRowSelected(t.id,o)},t.getToggleRowSelectedProps=F(n.getHooks().getToggleRowSelectedProps,{instance:n,row:t})}var Re=function(t){return{}},Ce=function(t){return{}};i.setRowState="setRowState",i.setCellState="setCellState",i.resetRowState="resetRowState";var Se=function(t){t.stateReducers.push(Un),t.useInstance.push(Kn),t.prepareRow.push($n)};function Un(t,e,n,o){var s=o.initialRowStateAccessor,r=s===void 0?Re:s,u=o.initialCellStateAccessor,c=u===void 0?Ce:u,d=o.rowsById;if(e.type===i.init)return l({rowState:{}},t);if(e.type===i.resetRowState)return l({},t,{rowState:o.initialState.rowState||{}});if(e.type===i.setRowState){var f,w=e.rowId,v=e.value,m=t.rowState[w]!==void 0?t.rowState[w]:r(d[w]);return l({},t,{rowState:l({},t.rowState,(f={},f[w]=Y(v,m),f))})}if(e.type===i.setCellState){var h,y,C,b,B,z=e.rowId,_=e.columnId,R=e.value,G=t.rowState[z]!==void 0?t.rowState[z]:r(d[z]),P=(G==null||(h=G.cellState)==null?void 0:h[_])!==void 0?G.cellState[_]:c((y=d[z])==null||(C=y.cells)==null?void 0:C.find(function(k){return k.column.id===_}));return l({},t,{rowState:l({},t.rowState,(B={},B[z]=l({},G,{cellState:l({},G.cellState||{},(b={},b[_]=Y(R,P),b))}),B))})}}function Kn(t){var e=t.autoResetRowState,n=e===void 0||e,o=t.data,s=t.dispatch,r=p.useCallback(function(d,f){return s({type:i.setRowState,rowId:d,value:f})},[s]),u=p.useCallback(function(d,f,w){return s({type:i.setCellState,rowId:d,columnId:f,value:w})},[s]),c=V(n);Q(function(){c()&&s({type:i.resetRowState})},[o]),Object.assign(t,{setRowState:r,setCellState:u})}function $n(t,e){var n=e.instance,o=n.initialRowStateAccessor,s=o===void 0?Re:o,r=n.initialCellStateAccessor,u=r===void 0?Ce:r,c=n.state.rowState;t&&(t.state=c[t.id]!==void 0?c[t.id]:s(t),t.setState=function(d){return n.setRowState(t.id,d)},t.cells.forEach(function(d){t.state.cellState||(t.state.cellState={}),d.state=t.state.cellState[d.column.id]!==void 0?t.state.cellState[d.column.id]:u(d),d.setState=function(f){return n.setCellState(t.id,d.column.id,f)}}))}Se.pluginName="useRowState",i.resetColumnOrder="resetColumnOrder",i.setColumnOrder="setColumnOrder";var be=function(t){t.stateReducers.push(Xn),t.visibleColumnsDeps.push(function(e,n){var o=n.instance;return[].concat(e,[o.state.columnOrder])}),t.visibleColumns.push(Yn),t.useInstance.push(Jn)};function Xn(t,e,n,o){return e.type===i.init?l({columnOrder:[]},t):e.type===i.resetColumnOrder?l({},t,{columnOrder:o.initialState.columnOrder||[]}):e.type===i.setColumnOrder?l({},t,{columnOrder:Y(e.columnOrder,t.columnOrder)}):void 0}function Yn(t,e){var n=e.instance.state.columnOrder;if(!n||!n.length)return t;for(var o=[].concat(n),s=[].concat(t),r=[],u=function(){var c=o.shift(),d=s.findIndex(function(f){return f.id===c});d>-1&&r.push(s.splice(d,1)[0])};s.length&&o.length;)u();return[].concat(r,s)}function Jn(t){var e=t.dispatch;t.setColumnOrder=p.useCallback(function(n){return e({type:i.setColumnOrder,columnOrder:n})},[e])}be.pluginName="useColumnOrder",A.canResize=!0,i.columnStartResizing="columnStartResizing",i.columnResizing="columnResizing",i.columnDoneResizing="columnDoneResizing",i.resetResize="resetResize";var xe=function(t){t.getResizerProps=[Qn],t.getHeaderProps.push({style:{position:"relative"}}),t.stateReducers.push(qn),t.useInstance.push(eo),t.useInstanceBeforeDimensions.push(to)},Qn=function(t,e){var n=e.instance,o=e.header,s=n.dispatch,r=function(u,c){var d=!1;if(u.type==="touchstart"){if(u.touches&&u.touches.length>1)return;d=!0}var f,w,v=function(_){var R=[];return function G(P){P.columns&&P.columns.length&&P.columns.map(G),R.push(P)}(_),R}(c).map(function(_){return[_.id,_.totalWidth]}),m=d?Math.round(u.touches[0].clientX):u.clientX,h=function(){window.cancelAnimationFrame(f),f=null,s({type:i.columnDoneResizing})},y=function(){window.cancelAnimationFrame(f),f=null,s({type:i.columnResizing,clientX:w})},C=function(_){w=_,f||(f=window.requestAnimationFrame(y))},b={mouse:{moveEvent:"mousemove",moveHandler:function(_){return C(_.clientX)},upEvent:"mouseup",upHandler:function(_){document.removeEventListener("mousemove",b.mouse.moveHandler),document.removeEventListener("mouseup",b.mouse.upHandler),h()}},touch:{moveEvent:"touchmove",moveHandler:function(_){return _.cancelable&&(_.preventDefault(),_.stopPropagation()),C(_.touches[0].clientX),!1},upEvent:"touchend",upHandler:function(_){document.removeEventListener(b.touch.moveEvent,b.touch.moveHandler),document.removeEventListener(b.touch.upEvent,b.touch.moveHandler),h()}}},B=d?b.touch:b.mouse,z=!!function(){if(typeof Ot=="boolean")return Ot;var _=!1;try{var R={get passive(){return _=!0,!1}};window.addEventListener("test",null,R),window.removeEventListener("test",null,R)}catch(G){_=!1}return Ot=_}()&&{passive:!1};document.addEventListener(B.moveEvent,B.moveHandler,z),document.addEventListener(B.upEvent,B.upHandler,z),s({type:i.columnStartResizing,columnId:c.id,columnWidth:c.totalWidth,headerIdWidths:v,clientX:m})};return[t,{onMouseDown:function(u){return u.persist()||r(u,o)},onTouchStart:function(u){return u.persist()||r(u,o)},style:{cursor:"col-resize"},draggable:!1,role:"separator"}]};function qn(t,e){if(e.type===i.init)return l({columnResizing:{columnWidths:{}}},t);if(e.type===i.resetResize)return l({},t,{columnResizing:{columnWidths:{}}});if(e.type===i.columnStartResizing){var n=e.clientX,o=e.columnId,s=e.columnWidth,r=e.headerIdWidths;return l({},t,{columnResizing:l({},t.columnResizing,{startX:n,headerIdWidths:r,columnWidth:s,isResizingColumn:o})})}if(e.type===i.columnResizing){var u=e.clientX,c=t.columnResizing,d=c.startX,f=c.columnWidth,w=c.headerIdWidths,v=(u-d)/f,m={};return(w===void 0?[]:w).forEach(function(h){var y=h[0],C=h[1];m[y]=Math.max(C+C*v,0)}),l({},t,{columnResizing:l({},t.columnResizing,{columnWidths:l({},t.columnResizing.columnWidths,{},m)})})}return e.type===i.columnDoneResizing?l({},t,{columnResizing:l({},t.columnResizing,{startX:null,isResizingColumn:null})}):void 0}xe.pluginName="useResizeColumns";var to=function(t){var e=t.flatHeaders,n=t.disableResizing,o=t.getHooks,s=t.state.columnResizing,r=V(t);e.forEach(function(u){var c=ht(u.disableResizing!==!0&&void 0,n!==!0&&void 0,!0);u.canResize=c,u.width=s.columnWidths[u.id]||u.originalWidth||u.width,u.isResizing=s.isResizingColumn===u.id,c&&(u.getResizerProps=F(o().getResizerProps,{instance:r(),header:u}))})};function eo(t){var e=t.plugins,n=t.dispatch,o=t.autoResetResize,s=o===void 0||o,r=t.columns;j(e,["useAbsoluteLayout"],"useResizeColumns");var u=V(s);Q(function(){u()&&n({type:i.resetResize})},[r]);var c=p.useCallback(function(){return n({type:i.resetResize})},[n]);Object.assign(t,{resetResizing:c})}var At={position:"absolute",top:0},Pe=function(t){t.getTableBodyProps.push(xt),t.getRowProps.push(xt),t.getHeaderGroupProps.push(xt),t.getFooterGroupProps.push(xt),t.getHeaderProps.push(function(e,n){var o=n.column;return[e,{style:l({},At,{left:o.totalLeft+"px",width:o.totalWidth+"px"})}]}),t.getCellProps.push(function(e,n){var o=n.cell;return[e,{style:l({},At,{left:o.column.totalLeft+"px",width:o.column.totalWidth+"px"})}]}),t.getFooterProps.push(function(e,n){var o=n.column;return[e,{style:l({},At,{left:o.totalLeft+"px",width:o.totalWidth+"px"})}]})};Pe.pluginName="useAbsoluteLayout";var xt=function(t,e){return[t,{style:{position:"relative",width:e.instance.totalColumnsWidth+"px"}}]},Wt={display:"inline-block",boxSizing:"border-box"},kt=function(t,e){return[t,{style:{display:"flex",width:e.instance.totalColumnsWidth+"px"}}]},Ee=function(t){t.getRowProps.push(kt),t.getHeaderGroupProps.push(kt),t.getFooterGroupProps.push(kt),t.getHeaderProps.push(function(e,n){var o=n.column;return[e,{style:l({},Wt,{width:o.totalWidth+"px"})}]}),t.getCellProps.push(function(e,n){var o=n.cell;return[e,{style:l({},Wt,{width:o.column.totalWidth+"px"})}]}),t.getFooterProps.push(function(e,n){var o=n.column;return[e,{style:l({},Wt,{width:o.totalWidth+"px"})}]})};function _e(t){t.getTableProps.push(no),t.getRowProps.push(Tt),t.getHeaderGroupProps.push(Tt),t.getFooterGroupProps.push(Tt),t.getHeaderProps.push(oo),t.getCellProps.push(ro),t.getFooterProps.push(io)}Ee.pluginName="useBlockLayout",_e.pluginName="useFlexLayout";var no=function(t,e){return[t,{style:{minWidth:e.instance.totalColumnsMinWidth+"px"}}]},Tt=function(t,e){return[t,{style:{display:"flex",flex:"1 0 auto",minWidth:e.instance.totalColumnsMinWidth+"px"}}]},oo=function(t,e){var n=e.column;return[t,{style:{boxSizing:"border-box",flex:n.totalFlexWidth?n.totalFlexWidth+" 0 auto":void 0,minWidth:n.totalMinWidth+"px",width:n.totalWidth+"px"}}]},ro=function(t,e){var n=e.cell;return[t,{style:{boxSizing:"border-box",flex:n.column.totalFlexWidth+" 0 auto",minWidth:n.column.totalMinWidth+"px",width:n.column.totalWidth+"px"}}]},io=function(t,e){var n=e.column;return[t,{style:{boxSizing:"border-box",flex:n.totalFlexWidth?n.totalFlexWidth+" 0 auto":void 0,minWidth:n.totalMinWidth+"px",width:n.totalWidth+"px"}}]};function Be(t){t.stateReducers.push(lo),t.getTableProps.push(so),t.getHeaderProps.push(uo),t.getRowProps.push(ao)}i.columnStartResizing="columnStartResizing",i.columnResizing="columnResizing",i.columnDoneResizing="columnDoneResizing",i.resetResize="resetResize",Be.pluginName="useGridLayout";var so=function(t,e){var n=e.instance;return[t,{style:{display:"grid",gridTemplateColumns:n.visibleColumns.map(function(o){var s;return n.state.gridLayout.columnWidths[o.id]?n.state.gridLayout.columnWidths[o.id]+"px":((s=n.state.columnResizing)==null?void 0:s.isResizingColumn)?n.state.gridLayout.startWidths[o.id]+"px":typeof o.width=="number"?o.width+"px":o.width}).join(" ")}}]},uo=function(t,e){var n=e.column;return[t,{id:"header-cell-"+n.id,style:{position:"sticky",gridColumn:"span "+n.totalVisibleHeaderCount}}]},ao=function(t,e){var n=e.row;return n.isExpanded?[t,{style:{gridColumn:"1 / "+(n.cells.length+1)}}]:[t,{}]};function lo(t,e,n,o){if(e.type===i.init)return l({gridLayout:{columnWidths:{}}},t);if(e.type===i.resetResize)return l({},t,{gridLayout:{columnWidths:{}}});if(e.type===i.columnStartResizing){var s=e.columnId,r=e.headerIdWidths,u=Gt(s);if(u!==void 0){var c=o.visibleColumns.reduce(function(R,G){var P;return l({},R,((P={})[G.id]=Gt(G.id),P))},{}),d=o.visibleColumns.reduce(function(R,G){var P;return l({},R,((P={})[G.id]=G.minWidth,P))},{}),f=o.visibleColumns.reduce(function(R,G){var P;return l({},R,((P={})[G.id]=G.maxWidth,P))},{}),w=r.map(function(R){var G=R[0];return[G,Gt(G)]});return l({},t,{gridLayout:l({},t.gridLayout,{startWidths:c,minWidths:d,maxWidths:f,headerIdGridWidths:w,columnWidth:u})})}return t}if(e.type===i.columnResizing){var v=e.clientX,m=t.columnResizing.startX,h=t.gridLayout,y=h.columnWidth,C=h.minWidths,b=h.maxWidths,B=h.headerIdGridWidths,z=(v-m)/y,_={};return(B===void 0?[]:B).forEach(function(R){var G=R[0],P=R[1];_[G]=Math.min(Math.max(C[G],P+P*z),b[G])}),l({},t,{gridLayout:l({},t.gridLayout,{columnWidths:l({},t.gridLayout.columnWidths,{},_)})})}return e.type===i.columnDoneResizing?l({},t,{gridLayout:l({},t.gridLayout,{startWidths:{},minWidths:{},maxWidths:{}})}):void 0}function Gt(t){var e,n=(e=document.getElementById("header-cell-"+t))==null?void 0:e.offsetWidth;if(n!==void 0)return n}S._UNSTABLE_usePivotColumns=me,S.actions=i,S.defaultColumn=A,S.defaultGroupByFn=de,S.defaultOrderByFn=ge,S.defaultRenderer=g,S.emptyRenderer=E,S.ensurePluginOrder=j,S.flexRender=pt,S.functionalUpdate=Y,S.loopHooks=$,S.makePropGetter=F,S.makeRenderer=rt,S.reduceHooks=H,S.safeUseLayoutEffect=Z,S.useAbsoluteLayout=Pe,S.useAsyncDebounce=function(t,e){e===void 0&&(e=0);var n=p.useRef({}),o=V(t),s=V(e);return p.useCallback(function(){var r=nt(regeneratorRuntime.mark(function u(){var c,d,f,w=arguments;return regeneratorRuntime.wrap(function(v){for(;;)switch(v.prev=v.next){case 0:for(c=w.length,d=new Array(c),f=0;f<c;f++)d[f]=w[f];return n.current.promise||(n.current.promise=new Promise(function(m,h){n.current.resolve=m,n.current.reject=h})),n.current.timeout&&clearTimeout(n.current.timeout),n.current.timeout=setTimeout(nt(regeneratorRuntime.mark(function m(){return regeneratorRuntime.wrap(function(h){for(;;)switch(h.prev=h.next){case 0:return delete n.current.timeout,h.prev=1,h.t0=n.current,h.next=5,o().apply(void 0,d);case 5:h.t1=h.sent,h.t0.resolve.call(h.t0,h.t1),h.next=12;break;case 9:h.prev=9,h.t2=h.catch(1),n.current.reject(h.t2);case 12:return h.prev=12,delete n.current.promise,h.finish(12);case 15:case"end":return h.stop()}},m,null,[[1,9,12,15]])})),s()),v.abrupt("return",n.current.promise);case 5:case"end":return v.stop()}},u)}));return function(){return r.apply(this,arguments)}}(),[o,s])},S.useBlockLayout=Ee,S.useColumnOrder=be,S.useExpanded=Xt,S.useFilters=se,S.useFlexLayout=_e,S.useGetLatest=V,S.useGlobalFilter=ue,S.useGridLayout=Be,S.useGroupBy=ce,S.useMountedLayoutEffect=Q,S.usePagination=ve,S.useResizeColumns=xe,S.useRowSelect=we,S.useRowState=Se,S.useSortBy=pe,S.useTable=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),o=1;o<e;o++)n[o-1]=arguments[o];t=Kt(t),n=[Ut].concat(n);var s=p.useRef({}),r=V(s.current);Object.assign(r(),l({},t,{plugins:n,hooks:Ue()})),n.filter(Boolean).forEach(function(M){M(r().hooks)});var u=V(r().hooks);r().getHooks=u,delete r().hooks,Object.assign(r(),H(u().useOptions,Kt(t)));var c=r(),d=c.data,f=c.columns,w=c.initialState,v=c.defaultColumn,m=c.getSubRows,h=c.getRowId,y=c.stateReducer,C=c.useControlledState,b=V(y),B=p.useCallback(function(M,ot){if(!ot.type)throw console.info({action:ot}),new Error("Unknown Action \u{1F446}");return[].concat(u().stateReducers,Array.isArray(b())?b():[b()]).reduce(function(st,dt){return dt(st,ot,M,r())||st},M)},[u,b,r]),z=p.useReducer(B,void 0,function(){return B(w,{type:i.init})}),_=z[0],R=z[1],G=H([].concat(u().useControlledState,[C]),_,{instance:r()});Object.assign(r(),{state:G,dispatch:R});var P=p.useMemo(function(){return yt(H(u().columns,f,{instance:r()}))},[u,r,f].concat(H(u().columnsDeps,[],{instance:r()})));r().columns=P;var k=p.useMemo(function(){return H(u().allColumns,vt(P),{instance:r()}).map(St)},[P,u,r].concat(H(u().allColumnsDeps,[],{instance:r()})));r().allColumns=k;var D=p.useMemo(function(){for(var M=[],ot=[],st={},dt=[].concat(k);dt.length;){var at=dt.shift();rn({data:d,rows:M,flatRows:ot,rowsById:st,column:at,getRowId:h,getSubRows:m,accessValueHooks:u().accessValue,getInstance:r})}return[M,ot,st]},[k,d,h,m,u,r]),O=D[0],W=D[1],X=D[2];Object.assign(r(),{rows:O,initialRows:[].concat(O),flatRows:W,rowsById:X}),$(u().useInstanceAfterData,r());var T=p.useMemo(function(){return H(u().visibleColumns,k,{instance:r()}).map(function(M){return Lt(M,v)})},[u,k,r,v].concat(H(u().visibleColumnsDeps,[],{instance:r()})));k=p.useMemo(function(){var M=[].concat(T);return k.forEach(function(ot){M.find(function(st){return st.id===ot.id})||M.push(ot)}),M},[k,T]),r().allColumns=k;var K=p.useMemo(function(){return H(u().headerGroups,ke(T,v),r())},[u,T,v,r].concat(H(u().headerGroupsDeps,[],{instance:r()})));r().headerGroups=K;var U=p.useMemo(function(){return K.length?K[0].headers:[]},[K]);r().headers=U,r().flatHeaders=K.reduce(function(M,ot){return[].concat(M,ot.headers)},[]),$(u().useInstanceBeforeDimensions,r());var tt=T.filter(function(M){return M.isVisible}).map(function(M){return M.id}).sort().join("_");T=p.useMemo(function(){return T.filter(function(M){return M.isVisible})},[T,tt]),r().visibleColumns=T;var I=$t(U),L=I[0],it=I[1],ct=I[2];return r().totalColumnsMinWidth=L,r().totalColumnsWidth=it,r().totalColumnsMaxWidth=ct,$(u().useInstance,r()),[].concat(r().flatHeaders,r().allColumns).forEach(function(M){M.render=rt(r(),M),M.getHeaderProps=F(u().getHeaderProps,{instance:r(),column:M}),M.getFooterProps=F(u().getFooterProps,{instance:r(),column:M})}),r().headerGroups=p.useMemo(function(){return K.filter(function(M,ot){return M.headers=M.headers.filter(function(st){return st.headers?function dt(at){return at.filter(function(mt){return mt.headers?dt(mt.headers):mt.isVisible}).length}(st.headers):st.isVisible}),!!M.headers.length&&(M.getHeaderGroupProps=F(u().getHeaderGroupProps,{instance:r(),headerGroup:M,index:ot}),M.getFooterGroupProps=F(u().getFooterGroupProps,{instance:r(),headerGroup:M,index:ot}),!0)})},[K,r,u]),r().footerGroups=[].concat(r().headerGroups).reverse(),r().prepareRow=p.useCallback(function(M){M.getRowProps=F(u().getRowProps,{instance:r(),row:M}),M.allCells=k.map(function(ot){var st=M.values[ot.id],dt={column:ot,row:M,value:st};return dt.getCellProps=F(u().getCellProps,{instance:r(),cell:dt}),dt.render=rt(r(),ot,{row:M,cell:dt,value:st}),dt}),M.cells=T.map(function(ot){return M.allCells.find(function(st){return st.column.id===ot.id})}),$(u().prepareRow,M,{instance:r()})},[u,r,k,T]),r().getTableProps=F(u().getTableProps,{instance:r()}),r().getTableBodyProps=F(u().getTableBodyProps,{instance:r()}),$(u().useFinalInstance,r()),r()},Object.defineProperty(S,"__esModule",{value:!0})})},79521:function(lt,et,x){lt.exports=x(40217)},17672:function(lt,et,x){"use strict";x.d(et,{Z:function(){return l}});/*!
 * Signature Pad v4.0.4 | https://github.com/szimek/signature_pad
 * (c) 2022 Szymon Nowak | Released under the MIT license
 */class S{constructor(a,i,g,E){if(isNaN(a)||isNaN(i))throw new Error(`Point is invalid: (${a}, ${i})`);this.x=+a,this.y=+i,this.pressure=g||0,this.time=E||Date.now()}distanceTo(a){return Math.sqrt(Math.pow(this.x-a.x,2)+Math.pow(this.y-a.y,2))}equals(a){return this.x===a.x&&this.y===a.y&&this.pressure===a.pressure&&this.time===a.time}velocityFrom(a){return this.time!==a.time?this.distanceTo(a)/(this.time-a.time):0}}class p{constructor(a,i,g,E,A,J){this.startPoint=a,this.control2=i,this.control1=g,this.endPoint=E,this.startWidth=A,this.endWidth=J}static fromPoints(a,i){const g=this.calculateControlPoints(a[0],a[1],a[2]).c2,E=this.calculateControlPoints(a[1],a[2],a[3]).c1;return new p(a[1],g,E,a[2],i.start,i.end)}static calculateControlPoints(a,i,g){const E=a.x-i.x,A=a.y-i.y,J=i.x-g.x,F=i.y-g.y,H={x:(a.x+i.x)/2,y:(a.y+i.y)/2},$={x:(i.x+g.x)/2,y:(i.y+g.y)/2},j=Math.sqrt(E*E+A*A),Y=Math.sqrt(J*J+F*F),V=H.x-$.x,Z=H.y-$.y,Q=Y/(j+Y),rt={x:$.x+V*Q,y:$.y+Z*Q},pt=i.x-rt.x,yt=i.y-rt.y;return{c1:new S(H.x+pt,H.y+yt),c2:new S($.x+pt,$.y+yt)}}length(){const a=10;let i=0,g,E;for(let A=0;A<=a;A+=1){const J=A/a,F=this.point(J,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),H=this.point(J,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(A>0){const $=F-g,j=H-E;i+=Math.sqrt($*$+j*j)}g=F,E=H}return i}point(a,i,g,E,A){return i*(1-a)*(1-a)*(1-a)+3*g*(1-a)*(1-a)*a+3*E*(1-a)*a*a+A*a*a*a}}class q{constructor(){try{this._et=new EventTarget}catch(a){this._et=document}}addEventListener(a,i,g){this._et.addEventListener(a,i,g)}dispatchEvent(a){return this._et.dispatchEvent(a)}removeEventListener(a,i,g){this._et.removeEventListener(a,i,g)}}function nt(N,a=250){let i=0,g=null,E,A,J;const F=()=>{i=Date.now(),g=null,E=N.apply(A,J),g||(A=null,J=[])};return function(...$){const j=Date.now(),Y=a-(j-i);return A=this,J=$,Y<=0||Y>a?(g&&(clearTimeout(g),g=null),i=j,E=N.apply(A,J),g||(A=null,J=[])):g||(g=window.setTimeout(F,Y)),E}}class l extends q{constructor(a,i={}){super();this.canvas=a,this._handleMouseDown=g=>{g.buttons===1&&(this._drawningStroke=!0,this._strokeBegin(g))},this._handleMouseMove=g=>{this._drawningStroke&&this._strokeMoveUpdate(g)},this._handleMouseUp=g=>{g.buttons===1&&this._drawningStroke&&(this._drawningStroke=!1,this._strokeEnd(g))},this._handleTouchStart=g=>{if(g.preventDefault(),g.targetTouches.length===1){const E=g.changedTouches[0];this._strokeBegin(E)}},this._handleTouchMove=g=>{g.preventDefault();const E=g.targetTouches[0];this._strokeMoveUpdate(E)},this._handleTouchEnd=g=>{if(g.target===this.canvas){g.preventDefault();const A=g.changedTouches[0];this._strokeEnd(A)}},this._handlePointerStart=g=>{this._drawningStroke=!0,g.preventDefault(),this._strokeBegin(g)},this._handlePointerMove=g=>{this._drawningStroke&&(g.preventDefault(),this._strokeMoveUpdate(g))},this._handlePointerEnd=g=>{this._drawningStroke&&(g.preventDefault(),this._drawningStroke=!1,this._strokeEnd(g))},this.velocityFilterWeight=i.velocityFilterWeight||.7,this.minWidth=i.minWidth||.5,this.maxWidth=i.maxWidth||2.5,this.throttle="throttle"in i?i.throttle:16,this.minDistance="minDistance"in i?i.minDistance:5,this.dotSize=i.dotSize||0,this.penColor=i.penColor||"black",this.backgroundColor=i.backgroundColor||"rgba(0,0,0,0)",this._strokeMoveUpdate=this.throttle?nt(l.prototype._strokeUpdate,this.throttle):l.prototype._strokeUpdate,this._ctx=a.getContext("2d"),this.clear(),this.on()}clear(){const{_ctx:a,canvas:i}=this;a.fillStyle=this.backgroundColor,a.clearRect(0,0,i.width,i.height),a.fillRect(0,0,i.width,i.height),this._data=[],this._reset(),this._isEmpty=!0}fromDataURL(a,i={}){return new Promise((g,E)=>{const A=new Image,J=i.ratio||window.devicePixelRatio||1,F=i.width||this.canvas.width/J,H=i.height||this.canvas.height/J,$=i.xOffset||0,j=i.yOffset||0;this._reset(),A.onload=()=>{this._ctx.drawImage(A,$,j,F,H),g()},A.onerror=Y=>{E(Y)},A.crossOrigin="anonymous",A.src=a,this._isEmpty=!1})}toDataURL(a="image/png",i){switch(a){case"image/svg+xml":return this._toSVG();default:return this.canvas.toDataURL(a,i)}}on(){this.canvas.style.touchAction="none",this.canvas.style.msTouchAction="none",this.canvas.style.userSelect="none";const a=/Macintosh/.test(navigator.userAgent)&&"ontouchstart"in document;window.PointerEvent&&!a?this._handlePointerEvents():(this._handleMouseEvents(),"ontouchstart"in window&&this._handleTouchEvents())}off(){this.canvas.style.touchAction="auto",this.canvas.style.msTouchAction="auto",this.canvas.style.userSelect="auto",this.canvas.removeEventListener("pointerdown",this._handlePointerStart),this.canvas.removeEventListener("pointermove",this._handlePointerMove),document.removeEventListener("pointerup",this._handlePointerEnd),this.canvas.removeEventListener("mousedown",this._handleMouseDown),this.canvas.removeEventListener("mousemove",this._handleMouseMove),document.removeEventListener("mouseup",this._handleMouseUp),this.canvas.removeEventListener("touchstart",this._handleTouchStart),this.canvas.removeEventListener("touchmove",this._handleTouchMove),this.canvas.removeEventListener("touchend",this._handleTouchEnd)}isEmpty(){return this._isEmpty}fromData(a,{clear:i=!0}={}){i&&this.clear(),this._fromData(a,this._drawCurve.bind(this),this._drawDot.bind(this)),this._data=this._data.concat(a)}toData(){return this._data}_strokeBegin(a){this.dispatchEvent(new CustomEvent("beginStroke",{detail:a}));const i={dotSize:this.dotSize,minWidth:this.minWidth,maxWidth:this.maxWidth,penColor:this.penColor,points:[]};this._data.push(i),this._reset(),this._strokeUpdate(a)}_strokeUpdate(a){if(this._data.length===0){this._strokeBegin(a);return}this.dispatchEvent(new CustomEvent("beforeUpdateStroke",{detail:a}));const i=a.clientX,g=a.clientY,E=a.pressure!==void 0?a.pressure:a.force!==void 0?a.force:0,A=this._createPoint(i,g,E),J=this._data[this._data.length-1],F=J.points,H=F.length>0&&F[F.length-1],$=H?A.distanceTo(H)<=this.minDistance:!1,{penColor:j,dotSize:Y,minWidth:V,maxWidth:Z}=J;if(!H||!(H&&$)){const Q=this._addPoint(A);H?Q&&this._drawCurve(Q,{penColor:j,dotSize:Y,minWidth:V,maxWidth:Z}):this._drawDot(A,{penColor:j,dotSize:Y,minWidth:V,maxWidth:Z}),F.push({time:A.time,x:A.x,y:A.y,pressure:A.pressure})}this.dispatchEvent(new CustomEvent("afterUpdateStroke",{detail:a}))}_strokeEnd(a){this._strokeUpdate(a),this.dispatchEvent(new CustomEvent("endStroke",{detail:a}))}_handlePointerEvents(){this._drawningStroke=!1,this.canvas.addEventListener("pointerdown",this._handlePointerStart),this.canvas.addEventListener("pointermove",this._handlePointerMove),document.addEventListener("pointerup",this._handlePointerEnd)}_handleMouseEvents(){this._drawningStroke=!1,this.canvas.addEventListener("mousedown",this._handleMouseDown),this.canvas.addEventListener("mousemove",this._handleMouseMove),document.addEventListener("mouseup",this._handleMouseUp)}_handleTouchEvents(){this.canvas.addEventListener("touchstart",this._handleTouchStart),this.canvas.addEventListener("touchmove",this._handleTouchMove),this.canvas.addEventListener("touchend",this._handleTouchEnd)}_reset(){this._lastPoints=[],this._lastVelocity=0,this._lastWidth=(this.minWidth+this.maxWidth)/2,this._ctx.fillStyle=this.penColor}_createPoint(a,i,g){const E=this.canvas.getBoundingClientRect();return new S(a-E.left,i-E.top,g,new Date().getTime())}_addPoint(a){const{_lastPoints:i}=this;if(i.push(a),i.length>2){i.length===3&&i.unshift(i[0]);const g=this._calculateCurveWidths(i[1],i[2]),E=p.fromPoints(i,g);return i.shift(),E}return null}_calculateCurveWidths(a,i){const g=this.velocityFilterWeight*i.velocityFrom(a)+(1-this.velocityFilterWeight)*this._lastVelocity,E=this._strokeWidth(g),A={end:E,start:this._lastWidth};return this._lastVelocity=g,this._lastWidth=E,A}_strokeWidth(a){return Math.max(this.maxWidth/(a+1),this.minWidth)}_drawCurveSegment(a,i,g){const E=this._ctx;E.moveTo(a,i),E.arc(a,i,g,0,2*Math.PI,!1),this._isEmpty=!1}_drawCurve(a,i){const g=this._ctx,E=a.endWidth-a.startWidth,A=Math.ceil(a.length())*2;g.beginPath(),g.fillStyle=i.penColor;for(let J=0;J<A;J+=1){const F=J/A,H=F*F,$=H*F,j=1-F,Y=j*j,V=Y*j;let Z=V*a.startPoint.x;Z+=3*Y*F*a.control1.x,Z+=3*j*H*a.control2.x,Z+=$*a.endPoint.x;let Q=V*a.startPoint.y;Q+=3*Y*F*a.control1.y,Q+=3*j*H*a.control2.y,Q+=$*a.endPoint.y;const rt=Math.min(a.startWidth+$*E,i.maxWidth);this._drawCurveSegment(Z,Q,rt)}g.closePath(),g.fill()}_drawDot(a,i){const g=this._ctx,E=i.dotSize>0?i.dotSize:(i.minWidth+i.maxWidth)/2;g.beginPath(),this._drawCurveSegment(a.x,a.y,E),g.closePath(),g.fillStyle=i.penColor,g.fill()}_fromData(a,i,g){for(const E of a){const{penColor:A,dotSize:J,minWidth:F,maxWidth:H,points:$}=E;if($.length>1)for(let j=0;j<$.length;j+=1){const Y=$[j],V=new S(Y.x,Y.y,Y.pressure,Y.time);this.penColor=A,j===0&&this._reset();const Z=this._addPoint(V);Z&&i(Z,{penColor:A,dotSize:J,minWidth:F,maxWidth:H})}else this._reset(),g($[0],{penColor:A,dotSize:J,minWidth:F,maxWidth:H})}}_toSVG(){const a=this._data,i=Math.max(window.devicePixelRatio||1,1),g=0,E=0,A=this.canvas.width/i,J=this.canvas.height/i,F=document.createElementNS("http://www.w3.org/2000/svg","svg");F.setAttribute("width",this.canvas.width.toString()),F.setAttribute("height",this.canvas.height.toString()),this._fromData(a,(Z,{penColor:Q})=>{const rt=document.createElement("path");if(!isNaN(Z.control1.x)&&!isNaN(Z.control1.y)&&!isNaN(Z.control2.x)&&!isNaN(Z.control2.y)){const pt=`M ${Z.startPoint.x.toFixed(3)},${Z.startPoint.y.toFixed(3)} C ${Z.control1.x.toFixed(3)},${Z.control1.y.toFixed(3)} ${Z.control2.x.toFixed(3)},${Z.control2.y.toFixed(3)} ${Z.endPoint.x.toFixed(3)},${Z.endPoint.y.toFixed(3)}`;rt.setAttribute("d",pt),rt.setAttribute("stroke-width",(Z.endWidth*2.25).toFixed(3)),rt.setAttribute("stroke",Q),rt.setAttribute("fill","none"),rt.setAttribute("stroke-linecap","round"),F.appendChild(rt)}},(Z,{penColor:Q,dotSize:rt,minWidth:pt,maxWidth:yt})=>{const vt=document.createElement("circle"),St=rt>0?rt:(pt+yt)/2;vt.setAttribute("r",St.toString()),vt.setAttribute("cx",Z.x.toString()),vt.setAttribute("cy",Z.y.toString()),vt.setAttribute("fill",Q),F.appendChild(vt)});const H="data:image/svg+xml;base64,",$=`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="${g} ${E} ${this.canvas.width} ${this.canvas.height}" width="${A}" height="${J}">`;let j=F.innerHTML;if(j===void 0){const Z=document.createElement("dummy"),Q=F.childNodes;Z.innerHTML="";for(let rt=0;rt<Q.length;rt+=1)Z.appendChild(Q[rt].cloneNode(!0));j=Z.innerHTML}const Y="</svg>",V=$+j+Y;return H+btoa(V)}}}}]);
