(self.webpackChunksurvey_king=self.webpackChunksurvey_king||[]).push([[6802],{88770:function(g,E,n){"use strict";n.r(E);var l=n(11849),d=n(92725),v=n(27400),M=n(9761),t=n(67294),s=n(85893),P=t.lazy(function(){return Promise.all([n.e(2225),n.e(9223),n.e(4968),n.e(7098),n.e(7115),n.e(7422),n.e(9278),n.e(5246),n.e(5785),n.e(701),n.e(3305),n.e(5362),n.e(9228),n.e(4839),n.e(1818),n.e(3844),n.e(5555),n.e(398),n.e(9454),n.e(2622),n.e(3524),n.e(4512),n.e(2750),n.e(1476),n.e(1307),n.e(2592),n.e(9613),n.e(1022),n.e(1350),n.e(7408)]).then(n.bind(n,23751))}),h=t.lazy(function(){return n.e(5482).then(n.bind(n,25482))}),D=(0,M.Pi)(function(o){var O=(0,v.a)(),m=O.isMobile;return(0,s.jsx)(t.Suspense,{fallback:(0,s.jsx)(d.Z,{}),children:m?(0,s.jsx)(h,(0,l.Z)({},o)):(0,s.jsx)(P,(0,l.Z)({},o))})});E.default=D}}]);
